const ControlDefinition = require('../models/ControlDefinition'); // Assuming you have this model
const mongoose = require('mongoose');

// @desc    Get all control definitions
// @route   GET /api/controls/definitions
// @access  Private (requires authentication)
exports.getAllControlDefinitions = async (req, res) => {
    try {
        // Fetch all documents from the ControlDefinition collection
        // Add any necessary sorting or filtering if needed, e.g., .sort({ category: 1, name: 1 })
        const controlDefinitions = await ControlDefinition.find({});

        if (!controlDefinitions) {
            // This case is unlikely if the collection exists but might occur
            return res.status(404).json({ success: false, message: 'No control definitions found' });
        }

        res.status(200).json({ success: true, data: controlDefinitions });

    } catch (error) {
        console.error('Error fetching all control definitions:', error);
        res.status(500).json({ success: false, message: 'Server Error fetching control definitions' });
    }
};

// Add other CRUD operations for control definitions if needed (create, update, delete)
// e.g., exports.createControlDefinition = async (req, res) => { ... }; 