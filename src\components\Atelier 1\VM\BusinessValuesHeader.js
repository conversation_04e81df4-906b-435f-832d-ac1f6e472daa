// BusinessValuesHeader.js
import React from 'react';
import { motion } from 'framer-motion';
import { useTranslation } from 'react-i18next';
import { Save, PlusCircle, List, BarChart2, HelpCircle, Home, Info, Gem } from 'lucide-react';
import { useAnalysis } from '../../../context/AnalysisContext';

const BusinessValuesHeader = ({
  viewMode,
  setViewMode,
  handleSaveData,
  onAddClick,
  onToggleHelp,
  isHelpVisible,
  onOpenGuide
}) => {
  const { t } = useTranslation();
  const { currentAnalysis } = useAnalysis();
  return (
    <>
      {/* === MERGED HEADER === */}
      <div className="bg-white rounded-xl shadow-sm border border-slate-100 p-4 mb-6">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          {/* Left Side: Breadcrumb & Title */}
          <div>
            <div className="flex items-center text-sm text-slate-500 mb-2">
              <span className="hover:text-blue-600 transition-colors">{t('businessValues.breadcrumb.workshop1')}</span>
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mx-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
              <span className="text-blue-600 font-medium">{t('businessValues.breadcrumb.businessValues')}</span>
            </div>
            <h1 className="text-2xl sm:text-3xl font-bold text-slate-800 flex items-center">
              <Gem size={28} className="mr-3 text-blue-600" />
              {t('businessValues.title')}
            </h1>
          </div>

          {/* Right Side: Action Buttons */}
          <div className="flex items-center space-x-3">
            {/* View Mode Toggle */}
            <div className="bg-white rounded-md border shadow-sm relative overflow-hidden">
              <div
                className="absolute inset-0 bg-blue-500 transition-all duration-300 ease-in-out"
                style={{
                  transform: viewMode === 'list' ? 'translateX(0%)' : 'translateX(100%)',
                  width: '50%',
                  zIndex: 0
                }}
              />
              <motion.button
                className={`px-4 py-2 rounded-l-md relative z-10 transition-colors duration-300 text-sm font-medium ${viewMode === 'list' ? 'text-white' : 'text-gray-700'}`}
                onClick={() => setViewMode('list')}
                whileTap={{ scale: 0.95 }}
              >
                <List size={16} className="inline-block mr-2" />
                {t('businessValues.buttons.list')}
              </motion.button>
              <motion.button
                className={`px-4 py-2 rounded-r-md relative z-10 transition-colors duration-300 text-sm font-medium ${viewMode === 'chart' ? 'text-white' : 'text-gray-700'}`}
                onClick={() => setViewMode('chart')}
                whileTap={{ scale: 0.95 }}
              >
                <BarChart2 size={16} className="inline-block mr-2" />
                {t('businessValues.buttons.visualize')}
              </motion.button>
            </div>

            {/* Add Value Button */}
            <button
              className="text-sm font-medium bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 flex items-center shadow-sm transition duration-200"
              onClick={onAddClick}
            >
              <PlusCircle size={16} className="mr-2" />
              {t('businessValues.buttons.add')}
            </button>

            {/* Save Button */}
            <button
              className="text-sm font-medium bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center shadow-sm transition duration-200"
              onClick={handleSaveData}
            >
              <Save size={16} className="mr-2" />
              {t('businessValues.buttons.save')}
            </button>

            {/* Help Button */}
            <button
              className="text-sm font-medium bg-slate-100 text-slate-700 px-4 py-2 rounded-lg hover:bg-slate-200 flex items-center shadow-sm transition duration-200"
              onClick={onOpenGuide}
            >
              <Info size={16} className="mr-2" />
              {t('businessValues.buttons.help')}
            </button>
          </div>
        </div>
      </div>
      {/* === END MERGED HEADER === */}

    </>
  );
};

export default BusinessValuesHeader;