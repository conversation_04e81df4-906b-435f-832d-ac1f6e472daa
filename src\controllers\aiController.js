// src/controllers/aiController.js
const axios = require('axios');

// AI model configuration
const AI_API_URL = process.env.AI_API_URL || 'https://api.openai.com/v1/chat/completions';
const AI_API_KEY = process.env.AI_API_KEY;
const AI_MODEL = process.env.AI_MODEL || 'gpt-4';

// Evaluate SR/OV couples using AI
exports.evaluateCouples = async (req, res) => {
  try {
    const { analysisId, analysisName, couples } = req.body;

    if (!couples || !Array.isArray(couples) || couples.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Aucun couple SR/OV fourni pour l\'évaluation'
      });
    }

    // Prepare the prompt for the AI model
    const prompt = `
      En tant qu'expert en cybersécurité et analyse de risques, évaluez les couples Source de Risque (SR) / Objectif Visé (OV) suivants
      pour l'analyse "${analysisName || 'EBIOS RM'}".

      Pour chaque couple, déterminez s'il doit être retenu (true) ou non (false) en fonction de son niveau de risque,
      et fournissez une justification concise.

      Voici les couples à évaluer:
      ${couples.map(couple => `
        ID: ${couple.id}
        Source de risque: ${couple.sourceName}
        Catégorie: ${couple.sourceCategory}
        Objectif visé: ${couple.objectifVise}
        Catégorie OV: ${couple.objectifViseCategory || 'Non spécifiée'}
        Motivation: ${couple.motivation}
        Activité: ${couple.activite}
        Ressources: ${couple.ressources}
        Niveau calculé: ${couple.niveau}
      `).join('\n')}

      Répondez au format JSON avec la structure suivante:
      {
        "recommendations": [
          {
            "id": "id_du_couple",
            "recommended": true/false,
            "justification": "Justification de la décision"
          },
          ...
        ]
      }

      Critères de décision:
      - Les couples avec un niveau élevé (3) devraient généralement être retenus
      - Les couples avec un niveau faible (1) peuvent être ignorés sauf s'ils présentent un intérêt stratégique particulier
      - Les couples avec un niveau moyen (2) nécessitent une analyse plus approfondie basée sur le contexte
      - Tenez compte de la catégorie de la source de risque et de l'objectif visé dans votre évaluation
    `;

    // If no API key is available, return mock data for development
    if (!AI_API_KEY) {
      console.warn('AI_API_KEY not set, returning mock data');

      // Generate mock recommendations
      const mockRecommendations = couples.map(couple => {
        // Simple logic: retain couples with niveau >= 2
        const recommended = couple.niveau >= 2;

        return {
          id: couple.id,
          recommended,
          justification: recommended
            ? `Ce couple présente un niveau de risque ${couple.niveau === 3 ? 'élevé' : 'significatif'} qui justifie son inclusion dans l'analyse.`
            : `Ce couple présente un niveau de risque faible et peut être exclu de l'analyse prioritaire.`
        };
      });

      return res.json({
        success: true,
        recommendations: mockRecommendations
      });
    }

    // Call the AI API
    const response = await axios.post(
      AI_API_URL,
      {
        model: AI_MODEL,
        messages: [
          { role: 'system', content: 'Vous êtes un expert en cybersécurité et analyse de risques EBIOS RM.' },
          { role: 'user', content: prompt }
        ],
        temperature: 0.2,
        max_tokens: 2000
      },
      {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${AI_API_KEY}`
        }
      }
    );

    // Extract the AI response
    const aiResponse = response.data.choices[0].message.content;

    // Parse the JSON response
    let parsedResponse;
    try {
      // Extract JSON from the response (in case the AI includes additional text)
      const jsonMatch = aiResponse.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        parsedResponse = JSON.parse(jsonMatch[0]);
      } else {
        throw new Error('No valid JSON found in response');
      }
    } catch (error) {
      console.error('Error parsing AI response:', error);
      console.log('AI response:', aiResponse);

      return res.status(500).json({
        success: false,
        message: 'Erreur lors de l\'analyse de la réponse IA'
      });
    }

    return res.json({
      success: true,
      recommendations: parsedResponse.recommendations
    });
  } catch (error) {
    console.error('AI evaluation error:', error);

    return res.status(500).json({
      success: false,
      message: error.message || 'Erreur lors de l\'évaluation IA'
    });
  }
};

// Generate operational scenarios using AI
exports.generateOperationalScenarios = async (req, res) => {
  try {
    const { attackPaths, options = {} } = req.body;

    if (!attackPaths || !Array.isArray(attackPaths) || attackPaths.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Aucun chemin d\'attaque fourni pour la génération'
      });
    }

    const {
      scenariosPerPath = 2,
      detailLevel = 'medium',
      includeIndicators = true,
      includeTools = true
    } = options;

    // Create detailed prompt for operational scenarios generation
    const prompt = `En tant qu'expert en cybersécurité et analyse de risques EBIOS RM, générez des scénarios opérationnels détaillés pour les chemins d'attaque suivants.

Chemins d'attaque à analyser:
${attackPaths.map((path, index) => `
${index + 1}. ${path.name || `Chemin ${index + 1}`}
   - Source de risque: ${path.sourceRisque || 'Non spécifié'}
   - Parties prenantes: ${path.partiesPrenantes?.join(', ') || 'Non spécifié'}
   - Objectif visé: ${path.objectifVise || 'Non spécifié'}
   - Description: ${path.description || 'Non spécifié'}
`).join('')}

Pour chaque chemin d'attaque, générez ${scenariosPerPath} scénarios opérationnels distincts suivant le modèle EBIOS RM en 4 phases:

1. CONNAITRE (Reconnaissance et découverte)
2. RENTRER (Activités d'intrusion)
3. TROUVER (Reconnaissance interne et mouvement latéral)
4. EXPLOITER (Exploitation des actifs trouvés)

Chaque scénario doit inclure:
- Nom du scénario (court et descriptif)
- Description générale
- Niveau de gravité (Très faible, Faible, Moyen, Élevé, Très élevé)
- Vraisemblance (Très faible, Faible, Moyen, Élevé, Très élevé)
- Compétences requises (Débutant, Intermédiaire, Avancé, Expert)
- Délai d'exécution estimé
- Difficulté de détection (Très faible, Faible, Moyen, Élevé, Très élevé)
- Étapes opérationnelles détaillées pour chaque phase

${includeIndicators ? '- Indicateurs de compromission pour chaque étape' : ''}
${includeTools ? '- Outils potentiellement utilisés' : ''}

Répondez UNIQUEMENT avec un JSON valide suivant cette structure:
{
  "scenarios": [
    {
      "attackPathId": "id_du_chemin",
      "name": "Nom du scénario",
      "description": "Description détaillée",
      "severity": "Niveau de gravité",
      "likelihood": "Vraisemblance",
      "requiredSkills": "Compétences requises",
      "timeline": "Délai d'exécution",
      "detectionDifficulty": "Difficulté de détection",
      "operationalSteps": [
        {
          "phase": "CONNAITRE|RENTRER|TROUVER|EXPLOITER",
          "stepNumber": 1,
          "technique": "Nom de la technique",
          "description": "Description de l'étape",
          "indicators": ["indicateur1", "indicateur2"],
          "tools": ["outil1", "outil2"],
          "duration": "Durée estimée",
          "difficulty": "Difficulté"
        }
      ],
      "attackPathDetails": {
        "sourceRisque": "Source de risque",
        "partiesPrenantes": ["partie1", "partie2"],
        "objectifVise": "Objectif visé"
      }
    }
  ]
}`;

    // If no API key is available, return mock data for development
    if (!AI_API_KEY) {
      console.warn('AI_API_KEY not set, returning mock operational scenarios');

      const mockScenarios = attackPaths.flatMap(path =>
        Array.from({ length: scenariosPerPath }, (_, i) => ({
          attackPathId: path.id,
          name: `Scénario opérationnel ${i + 1} - ${path.name || 'Chemin d\'attaque'}`,
          description: `Scénario détaillé d'attaque exploitant ${path.sourceRisque || 'la source de risque'} pour atteindre ${path.objectifVise || 'l\'objectif visé'}.`,
          severity: ['Moyen', 'Élevé', 'Très élevé'][Math.floor(Math.random() * 3)],
          likelihood: ['Faible', 'Moyen', 'Élevé'][Math.floor(Math.random() * 3)],
          requiredSkills: ['Intermédiaire', 'Avancé', 'Expert'][Math.floor(Math.random() * 3)],
          timeline: ['1-3 jours', '1-2 semaines', '2-4 semaines'][Math.floor(Math.random() * 3)],
          detectionDifficulty: ['Moyen', 'Élevé', 'Très élevé'][Math.floor(Math.random() * 3)],
          operationalSteps: [
            {
              phase: 'CONNAITRE',
              stepNumber: 1,
              technique: 'Reconnaissance passive',
              description: 'Collecte d\'informations publiques sur la cible',
              indicators: ['Requêtes DNS anormales', 'Scan de ports'],
              tools: ['Nmap', 'Shodan', 'Google Dorking'],
              duration: '2-4 heures',
              difficulty: 'Faible'
            },
            {
              phase: 'RENTRER',
              stepNumber: 2,
              technique: 'Exploitation de vulnérabilité',
              description: 'Exploitation d\'une faille pour obtenir un accès initial',
              indicators: ['Tentatives d\'exploitation', 'Connexions suspectes'],
              tools: ['Metasploit', 'Exploit personnalisé'],
              duration: '1-2 jours',
              difficulty: 'Moyen'
            },
            {
              phase: 'TROUVER',
              stepNumber: 3,
              technique: 'Mouvement latéral',
              description: 'Exploration du réseau interne et élévation de privilèges',
              indicators: ['Authentifications multiples', 'Accès à des ressources sensibles'],
              tools: ['PowerShell', 'Mimikatz', 'BloodHound'],
              duration: '3-5 jours',
              difficulty: 'Élevé'
            },
            {
              phase: 'EXPLOITER',
              stepNumber: 4,
              technique: 'Exfiltration de données',
              description: 'Extraction des données ciblées',
              indicators: ['Transferts de données volumineux', 'Connexions externes'],
              tools: ['WinSCP', 'Tunneling', 'Chiffrement'],
              duration: '1-2 jours',
              difficulty: 'Moyen'
            }
          ],
          attackPathDetails: {
            sourceRisque: path.sourceRisque || 'Source non spécifiée',
            partiesPrenantes: path.partiesPrenantes || [],
            objectifVise: path.objectifVise || 'Objectif non spécifié'
          }
        }))
      );

      return res.json({
        success: true,
        scenarios: mockScenarios,
        metadata: {
          generatedByAI: true,
          model: 'mock-model',
          generationDate: new Date().toISOString(),
          attackPathsCount: attackPaths.length,
          totalScenarios: mockScenarios.length
        }
      });
    }

    // Make API call to AI service
    const response = await axios.post(AI_API_URL, {
      model: AI_MODEL,
      messages: [
        {
          role: 'system',
          content: 'Vous êtes un expert en cybersécurité spécialisé dans la méthode EBIOS RM. Vous générez des scénarios opérationnels détaillés et réalistes.'
        },
        {
          role: 'user',
          content: prompt
        }
      ],
      temperature: 0.7,
      max_tokens: 4000
    }, {
      headers: {
        'Authorization': `Bearer ${AI_API_KEY}`,
        'Content-Type': 'application/json'
      }
    });

    const aiResponse = response.data.choices[0].message.content;

    try {
      const parsedResponse = JSON.parse(aiResponse);

      return res.json({
        success: true,
        scenarios: parsedResponse.scenarios || [],
        metadata: {
          generatedByAI: true,
          model: AI_MODEL,
          generationDate: new Date().toISOString(),
          attackPathsCount: attackPaths.length,
          totalScenarios: parsedResponse.scenarios?.length || 0
        }
      });
    } catch (parseError) {
      console.error('Error parsing AI response:', parseError);
      return res.status(500).json({
        success: false,
        message: 'Erreur lors de l\'analyse de la réponse IA',
        error: parseError.message
      });
    }

  } catch (error) {
    console.error('Error generating operational scenarios:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la génération des scénarios opérationnels',
      error: error.message
    });
  }
};
