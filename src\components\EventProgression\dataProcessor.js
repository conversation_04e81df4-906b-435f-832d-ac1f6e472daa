/**
 * Process data to calculate event progression metrics between Atelier 1 and Atelier 3
 * @param {Array} dreadedEvents - Dreaded events from Atelier 1
 * @param {Object} controlPlan - Risk treatment plan from Atelier 1
 * @param {Array} ecosystemMeasures - Ecosystem measures from Atelier 3
 * @param {Array} attackPaths - Attack paths from Atelier 3
 * @returns {Object} Processed data with progression metrics
 */
export const processEventProgressionData = (
  dreadedEvents = [],
  controlPlan = {},
  ecosystemMeasures = [],
  attackPaths = [],
  businessValuesData = null
) => {
  console.log('processEventProgressionData called with:', {
    dreadedEventsCount: dreadedEvents.length,
    controlPlanKeys: Object.keys(controlPlan).length,
    ecosystemMeasuresCount: ecosystemMeasures.length,
    attackPathsCount: attackPaths.length,
    hasBusinessValuesData: !!businessValuesData
  });

  // Helper function to get business value details
  const getBusinessValueDetails = (businessValueId) => {
    if (!businessValuesData) {
      return { name: 'Non définie', supportAssets: [] };
    }

    // Handle different data structures - API returns { businessValues: [...] } or { data: { businessValues: [...] } }
    let businessValues = [];
    if (businessValuesData.businessValues) {
      businessValues = businessValuesData.businessValues;
    } else if (businessValuesData.data && businessValuesData.data.businessValues) {
      businessValues = businessValuesData.data.businessValues;
    } else {
      return { name: 'Non définie', supportAssets: [] };
    }

    // Try multiple comparison strategies for ID matching
    const businessValue = businessValues.find(bv => {
      return bv.id === businessValueId ||
             bv.id === parseInt(businessValueId) ||
             String(bv.id) === String(businessValueId) ||
             bv.id === Number(businessValueId);
    });

    if (!businessValue) {
      return { name: 'Non définie', supportAssets: [] };
    }

    return {
      id: businessValue.id,
      name: businessValue.name || 'Non définie',
      shortId: businessValue.shortId || '',
      supportAssets: businessValue.supportAssets || [],
      securityPillars: businessValue.securityPillars || []
    };
  };

  // Helper function to format support assets for display
  const formatSupportAssets = (supportAssets) => {
    if (!supportAssets || supportAssets.length === 0) {
      return 'Aucun';
    }

    return supportAssets.slice(0, 2).map(asset =>
      `${asset.name}${asset.type ? ` (${asset.type})` : ''}`
    ).join(', ') + (supportAssets.length > 2 ? '...' : '');
  };

  // Only use demo data if ALL data is missing (including control plan from Atelier 1)
  if (
    !Object.keys(controlPlan).length &&
    !ecosystemMeasures.length &&
    !attackPaths.length &&
    !dreadedEvents.length
  ) {
    console.log('Using demo data because ALL data is missing');

    // Use real dreaded events if available, otherwise use demo events
    if (!dreadedEvents.length) {
      dreadedEvents = [
        { id: 'de1', name: 'Divulgation de données sensibles', securityPillar: 'Confidentialité', gravity: 'Critique', businessValue: 'bv1' },
        { id: 'de2', name: 'Indisponibilité du service principal', securityPillar: 'Disponibilité', gravity: 'Catastrophique', businessValue: 'bv2' },
        { id: 'de3', name: 'Modification frauduleuse des données', securityPillar: 'Intégrité', gravity: 'Critique', businessValue: 'bv1' },
        { id: 'de4', name: 'Espionnage industriel', securityPillar: 'Confidentialité', gravity: 'Majeur', businessValue: 'bv3' }
      ];
    }

    // Add demo business values if none are available
    if (!businessValuesData || (!businessValuesData.businessValues && !businessValuesData.data)) {
      businessValuesData = {
        businessValues: [
          {
            id: 'bv1',
            name: 'Données clients',
            shortId: 'VM1',
            supportAssets: [
              { id: 'bs1', name: 'Base de données CRM', type: 'Logiciel' },
              { id: 'bs2', name: 'Serveur de données', type: 'Matériel' }
            ]
          },
          {
            id: 'bv2',
            name: 'Services en ligne',
            shortId: 'VM2',
            supportAssets: [
              { id: 'bs3', name: 'Serveur web principal', type: 'Matériel' },
              { id: 'bs4', name: 'Application web', type: 'Logiciel' }
            ]
          },
          {
            id: 'bv3',
            name: 'Propriété intellectuelle',
            shortId: 'VM3',
            supportAssets: [
              { id: 'bs5', name: 'Serveur de fichiers R&D', type: 'Matériel' },
              { id: 'bs6', name: 'Documents techniques', type: 'Information' }
            ]
          }
        ]
      };
    }

    // Create demo measures based on the first few real or demo events
    const firstEventId = dreadedEvents[0]?.id || 'de1';
    const secondEventId = dreadedEvents[1]?.id || 'de2';
    const thirdEventId = dreadedEvents[2]?.id || 'de3';

    controlPlan = {
      planData: {
        plan: {
          [`rule1_${firstEventId}`]: {
            ruleId: 'rule1',
            dreadedEventId: firstEventId,
            controls: [
              { controlId: 'ctrl1', controlName: 'Chiffrement des données', strategy: 'Réduire le risque' },
              { controlId: 'ctrl2', controlName: 'Contrôle d\'accès strict', strategy: 'Réduire le risque' }
            ]
          },
          [`rule2_${secondEventId}`]: {
            ruleId: 'rule2',
            dreadedEventId: secondEventId,
            controls: [
              { controlId: 'ctrl3', controlName: 'Plan de continuité d\'activité', strategy: 'Réduire le risque' },
              { controlId: 'ctrl4', controlName: 'Sauvegardes régulières', strategy: 'Réduire le risque' }
            ]
          },
          [`rule3_${thirdEventId}`]: {
            ruleId: 'rule3',
            dreadedEventId: thirdEventId,
            controls: [
              { controlId: 'ctrl5', controlName: 'Contrôles d\'intégrité', strategy: 'Réduire le risque' },
              { controlId: 'ctrl6', controlName: 'Audit trails', strategy: 'Réduire le risque' }
            ]
          }
        }
      }
    };

    attackPaths = [
      {
        id: 'path1',
        sourceRiskName: 'Attaquant externe',
        dreadedEventId: firstEventId,
        dreadedEventName: dreadedEvents[0]?.name || 'Divulgation de données sensibles',
        objectifVise: 'Données clients',
        referenceCode: 'CA01'
      },
      {
        id: 'path2',
        sourceRiskName: 'Malware',
        dreadedEventId: secondEventId,
        dreadedEventName: dreadedEvents[1]?.name || 'Indisponibilité du service',
        objectifVise: 'Serveur web',
        referenceCode: 'CA02'
      }
    ];

    ecosystemMeasures = [
      {
        id: 'em1',
        title: 'Chiffrement TLS/AES 256',
        description: 'Mise en place du chiffrement TLS pour les communications et AES 256 pour le stockage',
        category: 'protection',
        priority: 'high',
        implementation: 'applied',
        attackPaths: ['path1'],
        sourceRisks: ['Attaquant externe'],
        objectifsVises: ['Données clients']
      },
      {
        id: 'em2',
        title: 'Authentification multi-facteurs (MFA)',
        description: 'Déploiement de l\'authentification à deux facteurs pour tous les accès critiques',
        category: 'protection',
        priority: 'high',
        implementation: 'in-progress',
        attackPaths: ['path1'],
        sourceRisks: ['Attaquant externe'],
        objectifsVises: ['Données clients']
      },
      {
        id: 'em3',
        title: 'Architecture redondante multi-sites',
        description: 'Mise en place d\'une architecture redondante sur plusieurs sites géographiques',
        category: 'resilience',
        priority: 'high',
        implementation: 'planned',
        attackPaths: ['path2'],
        sourceRisks: ['Malware'],
        objectifsVises: ['Serveur web']
      },
      {
        id: 'em4',
        title: 'Monitoring et surveillance 24/7',
        description: 'Centre de surveillance opérationnel 24h/24 et 7j/7',
        category: 'defense',
        priority: 'medium',
        implementation: 'applied',
        attackPaths: ['path2'],
        sourceRisks: ['Malware'],
        objectifsVises: ['Serveur web']
      }
    ];
  } else {
    console.log('Using real data');
  }

  // Extract plan data from control plan
  console.log('controlPlan structure:', controlPlan);
  const planData = controlPlan.planData || {};

  // Handle different planData structures
  let actualPlanData = planData;
  if (planData.plan && Object.keys(planData.plan).length > 0) {
    actualPlanData = planData.plan;
    console.log('Using planData.plan as actualPlanData');
  } else if (Object.keys(planData).length === 0 && Object.keys(controlPlan).length > 0) {
    actualPlanData = controlPlan;
    console.log('Using controlPlan directly as planData');
  }

  console.log('Final data to process:', {
    dreadedEventsCount: dreadedEvents.length,
    planDataKeys: Object.keys(actualPlanData).length,
    ecosystemMeasuresCount: ecosystemMeasures.length,
    attackPathsCount: attackPaths.length
  });

  // Group attack paths by dreaded event
  const attackPathsByEvent = attackPaths.reduce((acc, path) => {
    const eventId = path.dreadedEventId;
    if (!acc[eventId]) {
      acc[eventId] = [];
    }
    acc[eventId].push(path);
    return acc;
  }, {});

  // Group ecosystem measures by attack path and by event
  const measuresByAttackPath = {};
  const measuresByEvent = {};

  ecosystemMeasures.forEach(measure => {
    // Try different ways to associate measures with attack paths
    let associatedPaths = [];

    if (measure.attackPaths && measure.attackPaths.length > 0) {
      associatedPaths = measure.attackPaths;
    } else if (measure.sourceRisks || measure.objectifsVises) {
      attackPaths.forEach(path => {
        const matchesSource = measure.sourceRisks && measure.sourceRisks.some(risk =>
          path.sourceRiskName && path.sourceRiskName.includes(risk.substring(0, 20))
        );
        const matchesObjectif = measure.objectifsVises && measure.objectifsVises.some(obj =>
          path.objectifVise && path.objectifVise.includes(obj.substring(0, 20))
        );

        if (matchesSource || matchesObjectif) {
          associatedPaths.push(path.id);
        }
      });
    }

    // Associate measure with found paths
    associatedPaths.forEach(pathId => {
      if (!measuresByAttackPath[pathId]) {
        measuresByAttackPath[pathId] = [];
      }
      measuresByAttackPath[pathId].push(measure);

      // Also group by event
      const path = attackPaths.find(p => p.id === pathId);
      if (path) {
        const eventId = path.dreadedEventId;
        if (!measuresByEvent[eventId]) {
          measuresByEvent[eventId] = [];
        }
        if (!measuresByEvent[eventId].find(m => m.id === measure.id)) {
          measuresByEvent[eventId].push(measure);
        }
      }
    });
  });

  // Group A1 measures (controls) by dreaded event
  const a1MeasuresByEvent = Object.entries(actualPlanData).reduce((acc, [key, entry]) => {
    let eventId = entry.dreadedEventId || entry.eventId || entry.dreadedEvent;

    if (!eventId && key.includes('_')) {
      const parts = key.split('_');
      eventId = parts[parts.length - 1];
    }

    if (!eventId) {
      return acc;
    }

    if (!acc[eventId]) {
      acc[eventId] = [];
    }

    const controls = entry.controls || entry.measures || [];
    controls.forEach(control => {
      if (!acc[eventId].find(c => (c.controlId || c.id) === (control.controlId || control.id))) {
        // Debug: log control structure
        console.log('A1 Control structure for event', eventId, ':', control);
        acc[eventId].push(control);
      }
    });

    return acc;
  }, {});

  // Calculate progression for each dreaded event
  const eventProgression = dreadedEvents.map(event => {
    const eventId = event.id;
    const a1Measures = a1MeasuresByEvent[eventId] || [];
    const a3Measures = measuresByEvent[eventId] || [];
    const eventAttackPaths = attackPathsByEvent[eventId] || [];

    // Determine PTR progression status - nouvelle logique
    let status = 'not-treated';
    let statusLabel = 'Non traité';
    let statusColor = 'red';

    const hasA1Measures = a1Measures.length > 0;
    const hasA3Measures = a3Measures.length > 0;

    if (hasA1Measures && hasA3Measures) {
      // Traité par les deux ateliers = Enrichi
      status = 'enriched';
      statusLabel = 'Enrichi (A1 + A3)';
      statusColor = 'green';
    } else if (hasA1Measures || hasA3Measures) {
      // Traité par un seul atelier = Partiellement traité
      status = 'partially-treated';
      statusLabel = hasA1Measures ? 'Partiellement traité (A1 seulement)' : 'Partiellement traité (A3 seulement)';
      statusColor = 'yellow';
    } else {
      // Traité par aucun atelier = Non traité
      status = 'not-treated';
      statusLabel = 'Non traité';
      statusColor = 'red';
    }

    // Get business value details for this event
    const businessValueDetails = getBusinessValueDetails(event.businessValue);

    // Debug log for the first event only
    if (eventId === dreadedEvents[0]?.id) {
      console.log('Debug - First event business value lookup:', {
        eventBusinessValue: event.businessValue,
        businessValueDetails,
        hasBusinessValuesData: !!businessValuesData
      });
    }

    return {
      id: eventId,
      name: event.name,
      gravity: event.gravity || event.severity || 'Non définie',
      securityPillar: event.securityPillar || 'Non défini',
      businessValue: event.businessValue,
      businessValueDetails,
      supportAssetsFormatted: formatSupportAssets(businessValueDetails.supportAssets),
      a1Measures,
      a3Measures,
      attackPaths: eventAttackPaths,
      status,
      statusLabel,
      statusColor,
      progressionRate: hasA1Measures && hasA3Measures ? 100 :
                      (hasA1Measures || hasA3Measures) ? 50 : 0,
      treatmentDetails: {
        hasA1: hasA1Measures,
        hasA3: hasA3Measures,
        a1Count: a1Measures.length,
        a3Count: a3Measures.length
      }
    };
  });

  // Calculate global statistics with new PTR logic
  const totalEvents = eventProgression.length;
  const enrichedEvents = eventProgression.filter(e => e.status === 'enriched').length; // A1 + A3
  const partiallyTreatedEvents = eventProgression.filter(e => e.status === 'partially-treated').length; // A1 OU A3
  const notTreatedEvents = eventProgression.filter(e => e.status === 'not-treated').length; // Aucun

  // Calculate statistics by security pillar
  const pillarStats = {};
  const pillars = ['Confidentialité', 'Intégrité', 'Disponibilité', 'Traçabilité', 'Auditabilité'];

  pillars.forEach(pillar => {
    const pillarEvents = eventProgression.filter(e => e.securityPillar === pillar);
    const pillarEnriched = pillarEvents.filter(e => e.status === 'enriched').length;
    const pillarPartial = pillarEvents.filter(e => e.status === 'partially-treated').length;
    const pillarNotTreated = pillarEvents.filter(e => e.status === 'not-treated').length;

    pillarStats[pillar] = {
      total: pillarEvents.length,
      enriched: pillarEnriched,
      partial: pillarPartial,
      notTreated: pillarNotTreated,
      enrichmentRate: pillarEvents.length > 0 ? Math.round((pillarEnriched / pillarEvents.length) * 100) : 0,
      treatmentRate: pillarEvents.length > 0 ? Math.round(((pillarEnriched + pillarPartial) / pillarEvents.length) * 100) : 0
    };
  });

  // Calculate statistics by gravity level
  const gravityStats = {};
  const gravityLevels = ['Catastrophique', 'Critique', 'Majeur', 'Mineur', 'Non définie'];

  gravityLevels.forEach(gravity => {
    const gravityEvents = eventProgression.filter(e => e.gravity === gravity);
    const gravityEnriched = gravityEvents.filter(e => e.status === 'enriched').length;
    const gravityPartial = gravityEvents.filter(e => e.status === 'partially-treated').length;
    const gravityNotTreated = gravityEvents.filter(e => e.status === 'not-treated').length;

    gravityStats[gravity] = {
      total: gravityEvents.length,
      enriched: gravityEnriched,
      partial: gravityPartial,
      notTreated: gravityNotTreated,
      enrichmentRate: gravityEvents.length > 0 ? Math.round((gravityEnriched / gravityEvents.length) * 100) : 0,
      treatmentRate: gravityEvents.length > 0 ? Math.round(((gravityEnriched + gravityPartial) / gravityEvents.length) * 100) : 0
    };
  });

  const globalStats = {
    totalEvents,
    enrichedEvents,
    partiallyTreatedEvents,
    notTreatedEvents,
    enrichmentRate: totalEvents > 0 ? Math.round((enrichedEvents / totalEvents) * 100) : 0,
    treatmentRate: totalEvents > 0 ? Math.round(((enrichedEvents + partiallyTreatedEvents) / totalEvents) * 100) : 0,
    pillarStats,
    gravityStats,
    // Additional useful metrics
    totalA1Measures: eventProgression.reduce((sum, e) => sum + e.a1Measures.length, 0),
    totalA3Measures: eventProgression.reduce((sum, e) => sum + e.a3Measures.length, 0),
    averageProgressionRate: totalEvents > 0 ? Math.round(eventProgression.reduce((sum, e) => sum + e.progressionRate, 0) / totalEvents) : 0
  };

  // Generate recommendations - DISABLED
  const recommendations = [];

  // Generate synthesis report
  const generateSynthesisReport = () => {
    const currentDate = new Date().toLocaleDateString('fr-FR', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });

    const report = `
# RAPPORT DE SYNTHÈSE - PROGRESSION DES PLANS DE TRAITEMENT DU RISQUE (PTR)

**Date du rapport :** ${currentDate}
**Analyse :** ${dreadedEvents.length} événements redoutés analysés

## RÉSUMÉ EXÉCUTIF

Cette analyse présente l'état d'avancement des Plans de Traitement du Risque (PTR) en comparant les mesures définies dans l'Atelier 1 (mesures initiales) avec celles développées dans l'Atelier 3 (mesures écosystémiques détaillées).

### STATISTIQUES GLOBALES

- **Total des événements redoutés :** ${globalStats.totalEvents}
- **Événements enrichis (A1 + A3) :** ${globalStats.enrichedEvents} (${globalStats.enrichmentRate}%)
- **Événements partiellement traités :** ${globalStats.partiallyTreatedEvents} (${Math.round((globalStats.partiallyTreatedEvents / globalStats.totalEvents) * 100)}%)
- **Événements non traités :** ${globalStats.notTreatedEvents} (${Math.round((globalStats.notTreatedEvents / globalStats.totalEvents) * 100)}%)

### ANALYSE DÉTAILLÉE

#### 🟢 ÉVÉNEMENTS ENRICHIS (${globalStats.enrichedEvents})
Les événements suivants bénéficient d'un traitement complet avec des mesures définies dans les deux ateliers :

${eventProgression.filter(e => e.status === 'enriched').map(event =>
  `- **${event.name}** (${event.gravity}) - ${event.treatmentDetails.a1Count} mesure(s) A1, ${event.treatmentDetails.a3Count} mesure(s) A3`
).join('\n')}

#### 🟡 ÉVÉNEMENTS PARTIELLEMENT TRAITÉS (${globalStats.partiallyTreatedEvents})
Ces événements nécessitent une attention particulière car ils ne sont traités que par un seul atelier :

${eventProgression.filter(e => e.status === 'partially-treated').map(event =>
  `- **${event.name}** (${event.gravity}) - ${event.treatmentDetails.hasA1 ? `${event.treatmentDetails.a1Count} mesure(s) A1 uniquement` : `${event.treatmentDetails.a3Count} mesure(s) A3 uniquement`}`
).join('\n')}

#### 🔴 ÉVÉNEMENTS NON TRAITÉS (${globalStats.notTreatedEvents})
Ces événements critiques nécessitent une action immédiate :

${eventProgression.filter(e => e.status === 'not-treated').map(event =>
  `- **${event.name}** (${event.gravity}) - Aucune mesure définie`
).join('\n')}

## RECOMMANDATIONS PRIORITAIRES

### Actions Immédiates
${recommendations.filter(r => r.type === 'critical').map(rec => `- ${rec.message}`).join('\n')}

### Actions d'Amélioration
${recommendations.filter(r => r.type === 'improvement').map(rec => `- ${rec.message}`).join('\n')}

### Recommandations Stratégiques

1. **Prioriser les événements critiques non traités** : Focus sur les événements de gravité "Catastrophique" et "Critique"
2. **Compléter les traitements partiels** : Développer les mesures manquantes pour les événements partiellement traités
3. **Maintenir la cohérence** : S'assurer que les mesures A1 et A3 sont alignées et complémentaires
4. **Suivi régulier** : Mettre en place un processus de révision périodique des PTR

## CONCLUSION

Le taux global de traitement des événements redoutés est de **${globalStats.treatmentRate}%**, avec **${globalStats.enrichmentRate}%** d'événements bénéficiant d'un traitement complet (A1 + A3).

${globalStats.enrichmentRate >= 80 ?
  "✅ **Excellent niveau de maturité** : La majorité des événements redoutés bénéficient d'un traitement complet." :
  globalStats.enrichmentRate >= 60 ?
  "⚠️ **Niveau de maturité satisfaisant** : Des améliorations sont possibles pour optimiser la couverture." :
  "🚨 **Niveau de maturité insuffisant** : Des actions urgentes sont nécessaires pour améliorer la couverture des risques."
}

---
*Rapport généré automatiquement par l'outil EBIOS RM - ${currentDate}*
    `.trim();

    return report;
  };

  return {
    eventProgression,
    globalStats,
    recommendations,
    dreadedEvents,
    generateSynthesisReport
  };
};
