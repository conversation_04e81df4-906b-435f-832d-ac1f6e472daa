// Utility functions to get translated constants
import i18n from '../i18n';

// Get translated severity options
export const getTranslatedSeverityOptions = () => {
  return [
    { value: "minor", label: i18n.t('severity.minor') },
    { value: "moderate", label: i18n.t('severity.moderate') },
    { value: "major", label: i18n.t('severity.major') },
    { value: "critical", label: i18n.t('severity.critical') },
    { value: "catastrophic", label: i18n.t('severity.catastrophic') }
  ];
};

// Get translated dreaded event impacts
export const getTranslatedDreadedEventImpacts = () => {
  return [
    { value: 'missions_services', label: i18n.t('impacts.missions_services') },
    { value: 'humain_materiel_environnemental', label: i18n.t('impacts.humain_materiel_environnemental') },
    { value: 'gouvernance', label: i18n.t('impacts.gouvernance') },
    { value: 'financier', label: i18n.t('impacts.financier') },
    { value: 'juridique', label: i18n.t('impacts.juridique') },
    { value: 'image_confiance', label: i18n.t('impacts.image_confiance') }
  ];
};

// Get translated security pillars
export const getTranslatedSecurityPillars = () => {
  return [
    { id: 'confidentialite', name: i18n.t('securityPillars.confidentialite'), color: '#4299E1' },
    { id: 'integrite', name: i18n.t('securityPillars.integrite'), color: '#48BB78' },
    { id: 'disponibilite', name: i18n.t('securityPillars.disponibilite'), color: '#ECC94B' },
    { id: 'tracabilite', name: i18n.t('securityPillars.tracabilite'), color: '#ED8936' },
    { id: 'preuve', name: i18n.t('securityPillars.preuve'), color: '#8F11EA' },
    { id: 'Auditabilite', name: i18n.t('securityPillars.auditabilite'), color: '#BF1515' }
  ];
};

// Get translated severity levels (with colors)
export const getTranslatedSeverityLevels = () => {
  return [
    { value: 'minor', label: i18n.t('severity.minor'), color: '#10B981' },
    { value: 'moderate', label: i18n.t('severity.moderate'), color: '#F59E0B' },
    { value: 'major', label: i18n.t('severity.major'), color: '#F97316' },
    { value: 'critical', label: i18n.t('severity.critical'), color: '#EF4444' },
    { value: 'catastrophic', label: i18n.t('severity.catastrophic'), color: '#1F2937' }
  ];
};

// Helper function to get translated impact labels
export const getTranslatedImpactLabels = (impacts) => {
  const formatImpacts = (impacts) => {
    if (!impacts) return [];
    if (Array.isArray(impacts)) return impacts;
    if (typeof impacts === 'string') {
      try {
        return JSON.parse(impacts);
      } catch {
        return [impacts];
      }
    }
    return [];
  };

  const impactArray = formatImpacts(impacts);
  return impactArray.map(impactValue => {
    const translatedImpacts = getTranslatedDreadedEventImpacts();
    const impact = translatedImpacts.find(imp => imp.value === impactValue);
    return impact ? impact.label : impactValue;
  });
};

// Helper function to get translated severity label
export const getTranslatedSeverityLabel = (severityValue) => {
  const translatedSeverities = getTranslatedSeverityOptions();
  const severity = translatedSeverities.find(sev => sev.value === severityValue);
  return severity ? severity.label : severityValue;
};

// Helper function to get translated security pillar name
export const getTranslatedSecurityPillarName = (pillarId) => {
  const translatedPillars = getTranslatedSecurityPillars();
  const pillar = translatedPillars.find(p => p.id === pillarId);
  return pillar ? pillar.name : pillarId;
};

// Create translated pillar labels object for backward compatibility
export const getTranslatedPillarLabels = () => {
  const pillars = getTranslatedSecurityPillars();
  const labels = {};
  pillars.forEach(pillar => {
    labels[pillar.id] = pillar.name;
  });
  return labels;
};
