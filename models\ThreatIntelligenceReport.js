// models/ThreatIntelligenceReport.js
const mongoose = require('mongoose');

const VulnerabilitySchema = new mongoose.Schema({
  id: { type: String, required: true }, // CVE-2024-12345
  description: { type: String, required: true },
  cvss: { type: Number, required: true },
  cvssVersion: { type: String, default: '3.1' },
  severity: { type: String, required: true }, // CRITICAL, HIGH, MEDIUM, LOW
  vector: { type: String }, // CVSS vector string
  published: { type: Date },
  modified: { type: Date },
  source: { type: String, default: 'NIST NVD' },
  references: [{ type: String }], // URLs
  cpe: [{ type: String }], // CPE configurations
  exploitability: { type: Number },
  impact: { type: Number },
  sourceIdentifier: { type: String },
  vulnStatus: { type: String },
  relevanceScore: { type: Number }, // Asset-specific relevance
  businessImpact: {
    level: { type: String }, // Critical, High, Medium, Low
    score: { type: Number }, // 0-10
    description: { type: String }
  },
  assetSpecificRisk: {
    mitigationPriority: { type: String }, // Immediate, High, Medium, Low
    exploitProbability: { type: Number }, // 0-1
    businessContext: { type: String }
  },
  selected: { type: Boolean, default: true }, // Whether included in analysis
  notes: { type: String } // User notes
});

const AttackTechniqueSchema = new mongoose.Schema({
  id: { type: String, required: true }, // T1190
  name: { type: String, required: true },
  tactic: { type: String, required: true }, // Initial Access, Execution, etc.
  description: { type: String, required: true },
  framework: { type: String, default: 'MITRE ATT&CK' }, // MITRE ATT&CK, MITRE ATLAS
  applicability: { type: Number }, // 0-1 score
  businessRelevance: { type: Number }, // 0-1 score
  assetSpecificScenario: { type: String }, // Generated scenario
  mitigationSuggestions: [{ type: String }], // Suggested mitigations
  selected: { type: Boolean, default: true }, // Whether included in analysis
  notes: { type: String } // User notes
});

const ThreatIntelligenceReportSchema = new mongoose.Schema({
  // Report metadata
  analysisId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Analysis',
    required: true
  },
  assetId: {
    type: String, // Business asset identifier
    required: true
  },
  assetName: {
    type: String,
    required: true
  },
  assetType: {
    type: String, // Infrastructure, Application, Data, etc.
    required: true
  },
  
  // Asset context for threat intelligence
  assetContext: {
    vendor: { type: String },
    product: { type: String },
    version: { type: String },
    cpe: { type: String }, // Common Platform Enumeration
    businessValueName: { type: String },
    dreadedEventName: { type: String },
    organizationalRole: { type: String },
    attackSurface: [{ type: String }],
    hasSpecificTechnology: { type: Boolean, default: false }
  },

  // Threat intelligence data
  vulnerabilities: {
    total: { type: Number, default: 0 },
    critical: [VulnerabilitySchema],
    high: [VulnerabilitySchema],
    medium: [VulnerabilitySchema],
    low: [VulnerabilitySchema],
    topCritical: [VulnerabilitySchema], // Top 5 most critical
    topRecent: [VulnerabilitySchema] // Top 5 most recent
  },

  attackTechniques: [AttackTechniqueSchema],

  // Risk assessment
  riskAssessment: {
    overallRiskScore: { type: Number }, // 0-10
    vulnerabilityRisk: { type: Number }, // 0-10
    attackTechniqueRisk: { type: Number }, // 0-10
    businessImpactScore: { type: Number }, // 0-10
    riskLevel: { type: String }, // Critical, High, Medium, Low
    recommendations: [{ 
      title: { type: String },
      description: { type: String },
      priority: { type: String }, // Critical, High, Medium, Low
      category: { type: String } // Vulnerability, Technique, General
    }]
  },

  // Report generation metadata
  generatedAt: { type: Date, default: Date.now },
  generatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  generatedByName: { type: String, required: true },
  
  // Data sources and quality
  dataSources: {
    vulnerabilitySource: { type: String, default: 'NIST NVD' },
    attackTechniqueSource: { type: String, default: 'MITRE ATT&CK' },
    lastUpdated: { type: Date, default: Date.now },
    dataQuality: { type: String, default: 'High' } // High, Medium, Low
  },

  // Report status and lifecycle
  status: {
    type: String,
    enum: ['draft', 'generated', 'reviewed', 'approved', 'archived'],
    default: 'generated'
  },
  
  // User selections and customizations
  userSelections: {
    selectedVulnerabilities: [{ type: String }], // Array of vulnerability IDs
    selectedAttackTechniques: [{ type: String }], // Array of technique IDs
    excludedVulnerabilities: [{ type: String }],
    excludedAttackTechniques: [{ type: String }],
    customNotes: { type: String },
    reviewComments: { type: String }
  },

  // Timestamps
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now },
  lastReviewedAt: { type: Date },
  lastReviewedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  lastReviewedByName: { type: String }
}, {
  timestamps: true
});

// Indexes for performance
ThreatIntelligenceReportSchema.index({ analysisId: 1, assetId: 1 });
ThreatIntelligenceReportSchema.index({ generatedAt: -1 });
ThreatIntelligenceReportSchema.index({ status: 1 });
ThreatIntelligenceReportSchema.index({ 'riskAssessment.overallRiskScore': -1 });

// Virtual for getting total selected items
ThreatIntelligenceReportSchema.virtual('selectedItemsCount').get(function() {
  return {
    vulnerabilities: this.userSelections.selectedVulnerabilities.length,
    attackTechniques: this.userSelections.selectedAttackTechniques.length,
    total: this.userSelections.selectedVulnerabilities.length + this.userSelections.selectedAttackTechniques.length
  };
});

// Method to update user selections
ThreatIntelligenceReportSchema.methods.updateSelections = function(selectedVulns, selectedTechniques, userId, userName) {
  this.userSelections.selectedVulnerabilities = selectedVulns;
  this.userSelections.selectedAttackTechniques = selectedTechniques;
  this.updatedAt = new Date();
  this.lastReviewedAt = new Date();
  this.lastReviewedBy = userId;
  this.lastReviewedByName = userName;
  return this.save();
};

module.exports = mongoose.model('ThreatIntelligenceReport', ThreatIntelligenceReportSchema);
