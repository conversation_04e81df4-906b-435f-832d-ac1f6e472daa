// Test script to verify EUVD API is working
// Run this with: node test-euvd-api.js

const axios = require('axios');

const EUVD_BASE_URL = 'https://euvdservices.enisa.europa.eu/api';

async function testEUVDAPI() {
    console.log('🧪 Testing EUVD API with known examples...\n');

    // Test 1: Latest vulnerabilities (should always have results)
    console.log('📋 Test 1: Latest Vulnerabilities');
    console.log('='.repeat(50));
    try {
        const response1 = await axios.get(`${EUVD_BASE_URL}/lastvulnerabilities`, {
            headers: {
                'Accept': 'application/json',
                'User-Agent': 'EBIOS-RM-CTI-Test/1.0'
            },
            timeout: 10000
        });

        console.log(`✅ Status: ${response1.status}`);
        console.log(`📊 Results: ${Array.isArray(response1.data) ? response1.data.length : 'Unknown format'}`);
        
        if (Array.isArray(response1.data) && response1.data.length > 0) {
            const firstVuln = response1.data[0];
            console.log(`🔍 First vulnerability:`, {
                id: firstVuln.cveId || firstVuln.id || 'No ID',
                description: (firstVuln.description || firstVuln.summary || 'No description').substring(0, 100) + '...',
                score: firstVuln.cvssScore || firstVuln.score || 'No score',
                exploited: firstVuln.exploited || false
            });
        }
    } catch (error) {
        console.error(`❌ Error: ${error.message}`);
        if (error.response) {
            console.error(`   Status: ${error.response.status} ${error.response.statusText}`);
        }
    }

    console.log('\n');

    // Test 2: Critical vulnerabilities
    console.log('🚨 Test 2: Critical Vulnerabilities');
    console.log('='.repeat(50));
    try {
        const response2 = await axios.get(`${EUVD_BASE_URL}/criticalvulnerabilities`, {
            headers: {
                'Accept': 'application/json',
                'User-Agent': 'EBIOS-RM-CTI-Test/1.0'
            },
            timeout: 10000
        });

        console.log(`✅ Status: ${response2.status}`);
        console.log(`📊 Results: ${Array.isArray(response2.data) ? response2.data.length : 'Unknown format'}`);
        
        if (Array.isArray(response2.data) && response2.data.length > 0) {
            const firstVuln = response2.data[0];
            console.log(`🔍 First critical vulnerability:`, {
                id: firstVuln.cveId || firstVuln.id || 'No ID',
                description: (firstVuln.description || firstVuln.summary || 'No description').substring(0, 100) + '...',
                score: firstVuln.cvssScore || firstVuln.score || 'No score'
            });
        }
    } catch (error) {
        console.error(`❌ Error: ${error.message}`);
        if (error.response) {
            console.error(`   Status: ${error.response.status} ${error.response.statusText}`);
        }
    }

    console.log('\n');

    // Test 3: Search with popular vendor (Microsoft - should have many results)
    console.log('🔍 Test 3: Search for Microsoft vulnerabilities');
    console.log('='.repeat(50));
    try {
        const response3 = await axios.get(`${EUVD_BASE_URL}/search`, {
            params: {
                vendor: 'Microsoft',
                fromScore: '7.0',
                size: '5'
            },
            headers: {
                'Accept': 'application/json',
                'User-Agent': 'EBIOS-RM-CTI-Test/1.0'
            },
            timeout: 10000
        });

        console.log(`✅ Status: ${response3.status}`);
        console.log(`📊 Results: ${response3.data?.totalElements || response3.data?.length || 'Unknown format'}`);
        
        const vulnerabilities = response3.data?.content || response3.data || [];
        if (Array.isArray(vulnerabilities) && vulnerabilities.length > 0) {
            console.log(`🔍 First Microsoft vulnerability:`, {
                id: vulnerabilities[0].cveId || vulnerabilities[0].id || 'No ID',
                description: (vulnerabilities[0].description || vulnerabilities[0].summary || 'No description').substring(0, 100) + '...',
                score: vulnerabilities[0].cvssScore || vulnerabilities[0].score || 'No score'
            });
        }
    } catch (error) {
        console.error(`❌ Error: ${error.message}`);
        if (error.response) {
            console.error(`   Status: ${error.response.status} ${error.response.statusText}`);
        }
    }

    console.log('\n');

    // Test 4: Search with text keyword (should have results)
    console.log('🔍 Test 4: Search with text keyword "Windows"');
    console.log('='.repeat(50));
    try {
        const response4 = await axios.get(`${EUVD_BASE_URL}/search`, {
            params: {
                text: 'Windows',
                fromScore: '6.0',
                size: '3'
            },
            headers: {
                'Accept': 'application/json',
                'User-Agent': 'EBIOS-RM-CTI-Test/1.0'
            },
            timeout: 10000
        });

        console.log(`✅ Status: ${response4.status}`);
        console.log(`📊 Results: ${response4.data?.totalElements || response4.data?.length || 'Unknown format'}`);
        
        const vulnerabilities = response4.data?.content || response4.data || [];
        if (Array.isArray(vulnerabilities) && vulnerabilities.length > 0) {
            console.log(`🔍 First Windows vulnerability:`, {
                id: vulnerabilities[0].cveId || vulnerabilities[0].id || 'No ID',
                description: (vulnerabilities[0].description || vulnerabilities[0].summary || 'No description').substring(0, 100) + '...',
                score: vulnerabilities[0].cvssScore || vulnerabilities[0].score || 'No score'
            });
        }
    } catch (error) {
        console.error(`❌ Error: ${error.message}`);
        if (error.response) {
            console.error(`   Status: ${error.response.status} ${error.response.statusText}`);
        }
    }

    console.log('\n');

    // Test 5: Your specific Oracle MySQL search
    console.log('🔍 Test 5: Your Oracle MySQL search');
    console.log('='.repeat(50));
    try {
        const response5 = await axios.get(`${EUVD_BASE_URL}/search`, {
            params: {
                vendor: 'Oracle',
                product: 'MySQL',
                text: 'Oracle MySQL',
                fromScore: '4.0',
                size: '15'
            },
            headers: {
                'Accept': 'application/json',
                'User-Agent': 'EBIOS-RM-CTI-Test/1.0'
            },
            timeout: 10000
        });

        console.log(`✅ Status: ${response5.status}`);
        console.log(`📊 Results: ${response5.data?.totalElements || response5.data?.length || 'Unknown format'}`);
        console.log(`📋 Full response structure:`, Object.keys(response5.data || {}));
        
        const vulnerabilities = response5.data?.content || response5.data || [];
        if (Array.isArray(vulnerabilities) && vulnerabilities.length > 0) {
            console.log(`🔍 First Oracle MySQL vulnerability:`, {
                id: vulnerabilities[0].cveId || vulnerabilities[0].id || 'No ID',
                description: (vulnerabilities[0].description || vulnerabilities[0].summary || 'No description').substring(0, 100) + '...',
                score: vulnerabilities[0].cvssScore || vulnerabilities[0].score || 'No score'
            });
        } else {
            console.log(`⚠️  No Oracle MySQL vulnerabilities found. This might explain your 0 results.`);
            console.log(`💡 Try different search terms like just "MySQL" or "Oracle"`);
        }
    } catch (error) {
        console.error(`❌ Error: ${error.message}`);
        if (error.response) {
            console.error(`   Status: ${error.response.status} ${error.response.statusText}`);
        }
    }

    console.log('\n🎯 Test Summary:');
    console.log('- If Tests 1-4 work but Test 5 fails, the issue is with Oracle/MySQL search terms');
    console.log('- If all tests fail, there might be a network/API issue');
    console.log('- If some tests work, the EUVD API is functional');
}

// Run the tests
testEUVDAPI().catch(console.error);
