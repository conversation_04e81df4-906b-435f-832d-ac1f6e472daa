const asyncHandler = require('express-async-handler');
const EventSuggestion = require('../models/EventSuggestion');

/**
 * @desc    Get all event suggestions
 * @route   GET /api/event-suggestions
 * @access  Private
 */
const getAllEventSuggestions = asyncHandler(async (req, res) => {
  try {
    // Get query parameters
    const { securityPillar } = req.query;
    
    // Build query
    const query = {};
    if (securityPillar) {
      query.securityPillar = securityPillar;
    }
    
    // Get suggestions from database
    const suggestions = await EventSuggestion.find(query).sort({ name: 1 });
    
    res.status(200).json({
      success: true,
      count: suggestions.length,
      data: suggestions
    });
  } catch (error) {
    console.error('Error getting event suggestions:', error);
    res.status(500).json({
      success: false,
      message: 'Error retrieving event suggestions',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

/**
 * @desc    Create a new event suggestion
 * @route   POST /api/event-suggestions
 * @access  Private
 */
const createEventSuggestion = asyncHandler(async (req, res) => {
  try {
    const { name, description, securityPillar, severity, isDefault } = req.body;
    
    // Validate required fields
    if (!name || !securityPillar) {
      res.status(400);
      throw new Error('Please provide name and securityPillar');
    }
    
    // Create suggestion
    const suggestion = await EventSuggestion.create({
      name,
      description,
      securityPillar,
      severity: severity || 'moderate',
      isDefault: isDefault || false,
      createdBy: req.user.id
    });
    
    res.status(201).json({
      success: true,
      data: suggestion
    });
  } catch (error) {
    console.error('Error creating event suggestion:', error);
    const statusCode = res.statusCode === 200 ? 500 : res.statusCode;
    res.status(statusCode).json({
      success: false,
      message: 'Error creating event suggestion',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

/**
 * @desc    Create multiple event suggestions (bulk import)
 * @route   POST /api/event-suggestions/bulk
 * @access  Private
 */
const createBulkEventSuggestions = asyncHandler(async (req, res) => {
  try {
    const { suggestions } = req.body;
    
    if (!suggestions || !Array.isArray(suggestions) || suggestions.length === 0) {
      res.status(400);
      throw new Error('Please provide an array of suggestions');
    }
    
    // Prepare suggestions with user ID
    const suggestionsToCreate = suggestions.map(suggestion => ({
      ...suggestion,
      createdBy: req.user.id
    }));
    
    // Create suggestions in bulk
    const createdSuggestions = await EventSuggestion.insertMany(suggestionsToCreate);
    
    res.status(201).json({
      success: true,
      count: createdSuggestions.length,
      data: createdSuggestions
    });
  } catch (error) {
    console.error('Error creating bulk event suggestions:', error);
    const statusCode = res.statusCode === 200 ? 500 : res.statusCode;
    res.status(statusCode).json({
      success: false,
      message: 'Error creating bulk event suggestions',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

/**
 * @desc    Update an event suggestion
 * @route   PUT /api/event-suggestions/:id
 * @access  Private
 */
const updateEventSuggestion = asyncHandler(async (req, res) => {
  try {
    const { id } = req.params;
    const { name, description, securityPillar, severity, isDefault } = req.body;
    
    // Find suggestion
    const suggestion = await EventSuggestion.findById(id);
    
    if (!suggestion) {
      res.status(404);
      throw new Error('Event suggestion not found');
    }
    
    // Update suggestion
    suggestion.name = name || suggestion.name;
    suggestion.description = description !== undefined ? description : suggestion.description;
    suggestion.securityPillar = securityPillar || suggestion.securityPillar;
    suggestion.severity = severity || suggestion.severity;
    suggestion.isDefault = isDefault !== undefined ? isDefault : suggestion.isDefault;
    suggestion.updatedAt = Date.now();
    
    const updatedSuggestion = await suggestion.save();
    
    res.status(200).json({
      success: true,
      data: updatedSuggestion
    });
  } catch (error) {
    console.error('Error updating event suggestion:', error);
    const statusCode = res.statusCode === 200 ? 500 : res.statusCode;
    res.status(statusCode).json({
      success: false,
      message: 'Error updating event suggestion',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

/**
 * @desc    Delete an event suggestion
 * @route   DELETE /api/event-suggestions/:id
 * @access  Private
 */
const deleteEventSuggestion = asyncHandler(async (req, res) => {
  try {
    const { id } = req.params;
    
    // Find suggestion
    const suggestion = await EventSuggestion.findById(id);
    
    if (!suggestion) {
      res.status(404);
      throw new Error('Event suggestion not found');
    }
    
    // Delete suggestion
    await suggestion.remove();
    
    res.status(200).json({
      success: true,
      data: {}
    });
  } catch (error) {
    console.error('Error deleting event suggestion:', error);
    const statusCode = res.statusCode === 200 ? 500 : res.statusCode;
    res.status(statusCode).json({
      success: false,
      message: 'Error deleting event suggestion',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

module.exports = {
  getAllEventSuggestions,
  createEventSuggestion,
  createBulkEventSuggestions,
  updateEventSuggestion,
  deleteEventSuggestion
};
