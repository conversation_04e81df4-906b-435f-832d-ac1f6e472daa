// controllers/threatIntelligenceController.js
const ThreatIntelligenceReport = require('../models/ThreatIntelligenceReport');
const Analysis = require('../models/Analysis');
const asyncHandler = require('../middleware/asyncHandler');

// @desc    Create or update threat intelligence report
// @route   POST /api/threat-intelligence/reports
// @access  Private
const createOrUpdateReport = asyncHandler(async (req, res) => {
  const {
    analysisId,
    assetId,
    assetName,
    assetType,
    assetContext,
    vulnerabilities,
    attackTechniques,
    riskAssessment,
    userSelections
  } = req.body;

  // Validate required fields
  if (!analysisId || !assetId || !assetName) {
    return res.status(400).json({
      success: false,
      message: 'Analysis ID, Asset ID, and Asset Name are required'
    });
  }

  // Verify analysis exists and user has access
  const analysis = await Analysis.findById(analysisId);
  if (!analysis) {
    return res.status(404).json({
      success: false,
      message: 'Analysis not found'
    });
  }

  try {
    // Check if report already exists for this asset
    let report = await ThreatIntelligenceReport.findOne({
      analysisId,
      assetId
    });

    if (report) {
      // Update existing report
      report.assetName = assetName;
      report.assetType = assetType;
      report.assetContext = assetContext;
      report.vulnerabilities = vulnerabilities;
      report.attackTechniques = attackTechniques;
      report.riskAssessment = riskAssessment;
      report.userSelections = userSelections;
      report.updatedAt = new Date();
      report.lastReviewedAt = new Date();
      report.lastReviewedBy = req.user.id;
      report.lastReviewedByName = req.user.name;

      await report.save();

      console.log(`[ThreatIntelligence] Updated report for asset ${assetId} in analysis ${analysisId}`);
    } else {
      // Create new report
      report = await ThreatIntelligenceReport.create({
        analysisId,
        assetId,
        assetName,
        assetType,
        assetContext,
        vulnerabilities,
        attackTechniques,
        riskAssessment,
        userSelections,
        generatedBy: req.user.id,
        generatedByName: req.user.name,
        dataSources: {
          vulnerabilitySource: 'NIST NVD',
          attackTechniqueSource: 'MITRE ATT&CK',
          lastUpdated: new Date(),
          dataQuality: 'High'
        }
      });

      console.log(`[ThreatIntelligence] Created new report for asset ${assetId} in analysis ${analysisId}`);
    }

    res.status(200).json({
      success: true,
      message: report.isNew ? 'Threat intelligence report created successfully' : 'Threat intelligence report updated successfully',
      data: report
    });

  } catch (error) {
    console.error('[ThreatIntelligence] Error creating/updating report:', error);
    res.status(500).json({
      success: false,
      message: 'Error saving threat intelligence report',
      error: error.message
    });
  }
});

// @desc    Get threat intelligence reports for an analysis
// @route   GET /api/threat-intelligence/reports/:analysisId
// @access  Private
const getReportsByAnalysis = asyncHandler(async (req, res) => {
  const { analysisId } = req.params;

  // Verify analysis exists
  const analysis = await Analysis.findById(analysisId);
  if (!analysis) {
    return res.status(404).json({
      success: false,
      message: 'Analysis not found'
    });
  }

  const reports = await ThreatIntelligenceReport.find({ analysisId })
    .populate('generatedBy', 'name email')
    .populate('lastReviewedBy', 'name email')
    .sort({ updatedAt: -1 });

  res.status(200).json({
    success: true,
    count: reports.length,
    data: reports
  });
});

// @desc    Get specific threat intelligence report
// @route   GET /api/threat-intelligence/reports/:analysisId/:assetId
// @access  Private
const getReportByAsset = asyncHandler(async (req, res) => {
  const { analysisId, assetId } = req.params;

  const report = await ThreatIntelligenceReport.findOne({
    analysisId,
    assetId
  })
    .populate('generatedBy', 'name email')
    .populate('lastReviewedBy', 'name email');

  if (!report) {
    return res.status(404).json({
      success: false,
      message: 'Threat intelligence report not found for this asset'
    });
  }

  res.status(200).json({
    success: true,
    data: report
  });
});

// @desc    Update user selections for vulnerabilities and techniques
// @route   PUT /api/threat-intelligence/reports/:analysisId/:assetId/selections
// @access  Private
const updateSelections = asyncHandler(async (req, res) => {
  const { analysisId, assetId } = req.params;
  const { selectedVulnerabilities, selectedAttackTechniques, customNotes } = req.body;

  const report = await ThreatIntelligenceReport.findOne({
    analysisId,
    assetId
  });

  if (!report) {
    return res.status(404).json({
      success: false,
      message: 'Threat intelligence report not found'
    });
  }

  // Update selections
  report.userSelections.selectedVulnerabilities = selectedVulnerabilities || [];
  report.userSelections.selectedAttackTechniques = selectedAttackTechniques || [];
  if (customNotes !== undefined) {
    report.userSelections.customNotes = customNotes;
  }

  await report.updateSelections(
    selectedVulnerabilities || [],
    selectedAttackTechniques || [],
    req.user.id,
    req.user.name
  );

  console.log(`[ThreatIntelligence] Updated selections for asset ${assetId}: ${selectedVulnerabilities?.length || 0} vulnerabilities, ${selectedAttackTechniques?.length || 0} techniques`);

  res.status(200).json({
    success: true,
    message: 'Selections updated successfully',
    data: {
      selectedVulnerabilities: report.userSelections.selectedVulnerabilities,
      selectedAttackTechniques: report.userSelections.selectedAttackTechniques,
      totalSelected: report.selectedItemsCount
    }
  });
});

// @desc    Delete threat intelligence report
// @route   DELETE /api/threat-intelligence/reports/:analysisId/:assetId
// @access  Private
const deleteReport = asyncHandler(async (req, res) => {
  const { analysisId, assetId } = req.params;

  const report = await ThreatIntelligenceReport.findOne({
    analysisId,
    assetId
  });

  if (!report) {
    return res.status(404).json({
      success: false,
      message: 'Threat intelligence report not found'
    });
  }

  await report.deleteOne();

  console.log(`[ThreatIntelligence] Deleted report for asset ${assetId} in analysis ${analysisId}`);

  res.status(200).json({
    success: true,
    message: 'Threat intelligence report deleted successfully'
  });
});

// @desc    Get threat intelligence statistics for an analysis
// @route   GET /api/threat-intelligence/stats/:analysisId
// @access  Private
const getAnalysisStats = asyncHandler(async (req, res) => {
  const { analysisId } = req.params;

  const reports = await ThreatIntelligenceReport.find({ analysisId });

  const stats = {
    totalReports: reports.length,
    totalVulnerabilities: 0,
    totalAttackTechniques: 0,
    riskDistribution: {
      critical: 0,
      high: 0,
      medium: 0,
      low: 0
    },
    averageRiskScore: 0,
    lastUpdated: null
  };

  if (reports.length > 0) {
    let totalRiskScore = 0;
    let latestUpdate = new Date(0);

    reports.forEach(report => {
      stats.totalVulnerabilities += report.vulnerabilities.total || 0;
      stats.totalAttackTechniques += report.attackTechniques.length || 0;

      if (report.riskAssessment?.overallRiskScore) {
        totalRiskScore += report.riskAssessment.overallRiskScore;
      }

      // Count risk levels
      const riskLevel = report.riskAssessment?.riskLevel?.toLowerCase();
      if (riskLevel && stats.riskDistribution[riskLevel] !== undefined) {
        stats.riskDistribution[riskLevel]++;
      }

      // Track latest update
      if (report.updatedAt > latestUpdate) {
        latestUpdate = report.updatedAt;
      }
    });

    stats.averageRiskScore = totalRiskScore / reports.length;
    stats.lastUpdated = latestUpdate;
  }

  res.status(200).json({
    success: true,
    data: stats
  });
});

// @desc    Save CTI analysis results
// @route   POST /api/cti/save-results
// @access  Private
const saveCTIResults = asyncHandler(async (req, res) => {
  const {
    results,
    vulnActions,
    techniqueActions,
    timestamp,
    attackPath,
    selectedAssets
  } = req.body;

  // Validate required fields
  if (!results || !attackPath) {
    return res.status(400).json({
      success: false,
      message: 'Results and attack path are required'
    });
  }

  try {
    // Create or update CTI results document
    const ctiData = {
      userId: req.user?.id || 'anonymous',
      attackPath: attackPath,
      selectedAssets: selectedAssets || [],
      results: results,
      vulnActions: vulnActions || {},
      techniqueActions: techniqueActions || {},
      timestamp: timestamp || new Date().toISOString(),
      lastModified: new Date()
    };

    // For now, we'll store in ThreatIntelligenceReport with a special type
    const report = new ThreatIntelligenceReport({
      analysisId: 'cti-results', // Special identifier for CTI results
      assetId: 'cti-combined',
      assetName: 'CTI Analysis Results',
      assetType: 'cti-results',
      assetContext: JSON.stringify(ctiData),
      vulnerabilities: results.assets?.flatMap(asset => asset.vulnerabilities || []) || [],
      attackTechniques: results.assets?.flatMap(asset => asset.techniques || []) || [],
      riskAssessment: {
        totalVulnerabilities: results.totalVulnerabilities || 0,
        totalAttackTechniques: results.totalAttackTechniques || 0,
        totalAssets: results.assets?.length || 0
      },
      userSelections: {
        vulnActions: vulnActions,
        techniqueActions: techniqueActions
      },
      createdAt: new Date(),
      updatedAt: new Date()
    });

    await report.save();

    console.log('[CTI] Results saved successfully:', {
      attackPath,
      assetsCount: selectedAssets?.length || 0,
      vulnCount: results.totalVulnerabilities || 0,
      techniqueCount: results.totalAttackTechniques || 0
    });

    res.status(200).json({
      success: true,
      message: 'CTI results saved successfully',
      data: {
        id: report._id,
        timestamp: report.createdAt
      }
    });
  } catch (error) {
    console.error('[CTI] Error saving results:', error);
    res.status(500).json({
      success: false,
      message: 'Error saving CTI results',
      error: error.message
    });
  }
});

// @desc    Load CTI analysis results
// @route   GET /api/cti/load-results
// @access  Private
const loadCTIResults = asyncHandler(async (req, res) => {
  try {
    const { analysisId } = req.query;

    let query = {};
    if (analysisId) {
      // Load CTI results for specific analysis
      query = { analysisId: analysisId };
    } else {
      // Load general CTI results (backward compatibility)
      query = {
        analysisId: 'cti-results',
        assetId: 'cti-combined'
      };
    }

    const report = await ThreatIntelligenceReport.findOne(query).sort({ createdAt: -1 });

    if (!report) {
      return res.status(404).json({
        success: false,
        message: 'No saved CTI results found'
      });
    }

    // Parse the stored data
    const ctiData = JSON.parse(report.assetContext);

    console.log('[CTI] Results loaded successfully:', {
      analysisId: analysisId || 'general',
      attackPath: ctiData.attackPath,
      assetsCount: ctiData.selectedAssets?.length || 0,
      timestamp: ctiData.timestamp
    });

    res.status(200).json({
      success: true,
      data: {
        results: ctiData.results,
        vulnActions: ctiData.vulnActions || {},
        techniqueActions: ctiData.techniqueActions || {},
        attackPath: ctiData.attackPath,
        selectedAssets: ctiData.selectedAssets || [],
        timestamp: ctiData.timestamp,
        lastModified: report.updatedAt,
        reportId: report._id
      }
    });
  } catch (error) {
    console.error('[CTI] Error loading results:', error);
    res.status(500).json({
      success: false,
      message: 'Error loading CTI results',
      error: error.message
    });
  }
});

// @desc    Delete CTI analysis results
// @route   DELETE /api/cti/delete-results
// @access  Private
const deleteCTIResults = asyncHandler(async (req, res) => {
  try {
    const { analysisId, reportId } = req.query;

    let query = {};
    let deleteCount = 0;

    if (reportId) {
      // Delete specific CTI report by ID
      const report = await ThreatIntelligenceReport.findById(reportId);
      if (!report) {
        return res.status(404).json({
          success: false,
          message: 'CTI report not found'
        });
      }

      await report.deleteOne();
      deleteCount = 1;

      console.log(`[CTI] Deleted specific CTI report: ${reportId}`);
    } else if (analysisId) {
      // Delete all CTI results for specific analysis
      const result = await ThreatIntelligenceReport.deleteMany({ analysisId: analysisId });
      deleteCount = result.deletedCount;

      console.log(`[CTI] Deleted ${deleteCount} CTI reports for analysis: ${analysisId}`);
    } else {
      // Delete general CTI results (backward compatibility)
      const result = await ThreatIntelligenceReport.deleteMany({
        analysisId: 'cti-results',
        assetId: 'cti-combined'
      });
      deleteCount = result.deletedCount;

      console.log(`[CTI] Deleted ${deleteCount} general CTI reports`);
    }

    if (deleteCount === 0) {
      return res.status(404).json({
        success: false,
        message: 'No CTI results found to delete'
      });
    }

    res.status(200).json({
      success: true,
      message: `Successfully deleted ${deleteCount} CTI result${deleteCount > 1 ? 's' : ''}`,
      data: {
        deletedCount: deleteCount,
        analysisId: analysisId || 'general',
        reportId: reportId || null
      }
    });
  } catch (error) {
    console.error('[CTI] Error deleting results:', error);
    res.status(500).json({
      success: false,
      message: 'Error deleting CTI results',
      error: error.message
    });
  }
});

// @desc    Get all CTI analysis results for an analysis
// @route   GET /api/cti/list-results
// @access  Private
const listCTIResults = asyncHandler(async (req, res) => {
  try {
    const { analysisId } = req.query;

    let query = {};
    if (analysisId) {
      query = { analysisId: analysisId };
    } else {
      // List general CTI results
      query = {
        analysisId: 'cti-results',
        assetId: 'cti-combined'
      };
    }

    const reports = await ThreatIntelligenceReport.find(query)
      .select('_id createdAt updatedAt assetContext')
      .sort({ createdAt: -1 });

    const results = reports.map(report => {
      try {
        const ctiData = JSON.parse(report.assetContext);
        return {
          id: report._id,
          attackPath: ctiData.attackPath,
          selectedAssets: ctiData.selectedAssets || [],
          timestamp: ctiData.timestamp,
          createdAt: report.createdAt,
          updatedAt: report.updatedAt,
          totalVulnerabilities: ctiData.results?.totalVulnerabilities || 0,
          totalAttackTechniques: ctiData.results?.totalAttackTechniques || 0,
          totalAssets: ctiData.results?.assets?.length || 0
        };
      } catch (parseError) {
        console.error('[CTI] Error parsing report data:', parseError);
        return {
          id: report._id,
          error: 'Failed to parse report data',
          createdAt: report.createdAt,
          updatedAt: report.updatedAt
        };
      }
    });

    console.log(`[CTI] Listed ${results.length} CTI results for analysis: ${analysisId || 'general'}`);

    res.status(200).json({
      success: true,
      count: results.length,
      data: results
    });
  } catch (error) {
    console.error('[CTI] Error listing results:', error);
    res.status(500).json({
      success: false,
      message: 'Error listing CTI results',
      error: error.message
    });
  }
});

module.exports = {
  createOrUpdateReport,
  getReportsByAnalysis,
  getReportByAsset,
  updateSelections,
  deleteReport,
  getAnalysisStats,
  saveCTIResults,
  loadCTIResults,
  deleteCTIResults,
  listCTIResults
};
