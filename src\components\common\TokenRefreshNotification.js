import React, { useState, useEffect, useCallback } from 'react';
import { RefreshCw, CheckCircle, AlertTriangle, LogIn } from 'lucide-react';
import { useNavigate } from 'react-router-dom';

/**
 * Component to show token refresh status notifications
 * This is a non-intrusive way to inform users about authentication status
 */
const TokenRefreshNotification = () => {
  const [notification, setNotification] = useState(null);
  const [showLoginButton, setShowLoginButton] = useState(false);
  const [redirectPath, setRedirectPath] = useState('');
  const navigate = useNavigate();

  // Handle redirect to login
  const handleRedirectToLogin = useCallback(() => {
    // Save current path for redirect after login
    if (redirectPath) {
      sessionStorage.setItem('redirectAfterLogin', redirectPath);
    }
    navigate('/login');
  }, [navigate, redirectPath]);

  useEffect(() => {
    // Listen for token refresh events
    const handleTokenRefreshing = () => {
      setNotification({
        type: 'refreshing',
        message: 'Rafraîchissement de votre session...',
        icon: <RefreshCw className="animate-spin h-4 w-4 mr-2" />,
        bgColor: 'bg-blue-50',
        textColor: 'text-blue-700',
        borderColor: 'border-blue-200'
      });
      setShowLoginButton(false);
    };

    const handleTokenRefreshed = () => {
      setNotification({
        type: 'success',
        message: 'Session rafraîchie avec succès',
        icon: <CheckCircle className="h-4 w-4 mr-2" />,
        bgColor: 'bg-green-50',
        textColor: 'text-green-700',
        borderColor: 'border-green-200'
      });
      setShowLoginButton(false);

      // Clear success notification after 3 seconds
      setTimeout(() => {
        setNotification(null);
      }, 3000);
    };

    const handleTokenError = (event) => {
      setNotification({
        type: 'error',
        message: event.detail?.message || 'Erreur de rafraîchissement de session',
        icon: <AlertTriangle className="h-4 w-4 mr-2" />,
        bgColor: 'bg-red-50',
        textColor: 'text-red-700',
        borderColor: 'border-red-200'
      });
      // Don't show login button for every error
      setShowLoginButton(false);
    };

    const handleSessionExpired = (event) => {
      console.log('Session expired event received', event.detail);
      setNotification({
        type: 'expired',
        message: event.detail?.message || 'Votre session a expiré',
        icon: <AlertTriangle className="h-4 w-4 mr-2" />,
        bgColor: 'bg-amber-50',
        textColor: 'text-amber-700',
        borderColor: 'border-amber-200'
      });

      // Show login button and store redirect path
      setShowLoginButton(true);
      if (event.detail?.currentPath) {
        setRedirectPath(event.detail.currentPath);
      }
    };

    // Handle max retries notification
    const handleMaxRetries = (event) => {
      console.log('Max retries event received', event.detail);
      setNotification({
        type: 'warning',
        message: event.detail?.message || 'Problème de connexion détecté',
        icon: <AlertTriangle className="h-4 w-4 mr-2" />,
        bgColor: 'bg-yellow-50',
        textColor: 'text-yellow-700',
        borderColor: 'border-yellow-200'
      });

      // Auto-dismiss after 10 seconds
      setTimeout(() => {
        if (notification?.type === 'warning') {
          setNotification(null);
        }
      }, 10000);
    };

    // Handle silent session expiry
    const handleSilentSessionExpired = (event) => {
      console.log('Silent session expired event received', event.detail);
      setNotification({
        type: 'expired',
        message: event.detail?.message || 'Votre session a expiré',
        icon: <AlertTriangle className="h-4 w-4 mr-2" />,
        bgColor: 'bg-amber-50',
        textColor: 'text-amber-700',
        borderColor: 'border-amber-200'
      });

      // Show login button and store redirect path
      setShowLoginButton(true);
      if (event.detail?.currentPath) {
        setRedirectPath(event.detail.currentPath);
      }
    };

    // Add event listeners
    window.addEventListener('auth:tokenRefreshing', handleTokenRefreshing);
    window.addEventListener('auth:tokenRefreshed', handleTokenRefreshed);
    window.addEventListener('auth:tokenError', handleTokenError);
    window.addEventListener('auth:tokenExpired', handleTokenError);
    window.addEventListener('auth:sessionExpired', handleSessionExpired);
    window.addEventListener('auth:silentSessionExpired', handleSilentSessionExpired);
    window.addEventListener('auth:tokenRefreshMaxRetries', handleMaxRetries);

    // Clean up
    return () => {
      window.removeEventListener('auth:tokenRefreshing', handleTokenRefreshing);
      window.removeEventListener('auth:tokenRefreshed', handleTokenRefreshed);
      window.removeEventListener('auth:tokenError', handleTokenError);
      window.removeEventListener('auth:tokenExpired', handleTokenError);
      window.removeEventListener('auth:sessionExpired', handleSessionExpired);
      window.removeEventListener('auth:silentSessionExpired', handleSilentSessionExpired);
      window.removeEventListener('auth:tokenRefreshMaxRetries', handleMaxRetries);
    };
  }, []);

  // Don't render anything if there's no notification
  if (!notification) {
    return null;
  }

  return (
    <div className={`fixed bottom-4 right-4 z-50 max-w-xs p-3 rounded-md shadow-md border ${notification.borderColor} ${notification.bgColor} transition-all duration-300 ease-in-out`}>
      <div className="flex flex-col">
        <div className="flex items-center">
          {notification.icon}
          <span className={`text-sm font-medium ${notification.textColor}`}>
            {notification.message}
          </span>
        </div>

        {showLoginButton && (
          <button
            onClick={handleRedirectToLogin}
            className="mt-2 flex items-center justify-center w-full px-3 py-1.5 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 transition-colors"
          >
            <LogIn size={14} className="mr-1.5" />
            Se reconnecter
          </button>
        )}
      </div>
    </div>
  );
};

export default TokenRefreshNotification;
