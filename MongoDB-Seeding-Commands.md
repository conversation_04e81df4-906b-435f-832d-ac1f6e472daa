# MongoDB Seeding Commands

This document provides the commands to manually seed the MongoDB database with all the JSON files from the `mongo-init/data` directory.

## Prerequisites

- Docker and Docker Compose installed
- EBIOS RM containers running (or ready to be started)
- Located in the directory containing the `docker-compose.yml` file

## Commands

Run these commands one by one in your terminal:

```bash
# Start the containers if they're not already running
docker-compose up -d

# Wait for MongoDB to start (about 10 seconds)
sleep 10

# Import users collection
docker cp mongo-init/data/ebiosrm.users.json ebiosrm-mongodb:/tmp/
docker exec ebiosrm-mongodb mongoimport --db ebiosrm --collection users --file /tmp/ebiosrm.users.json --jsonArray

# Import companies collection
docker cp mongo-init/data/ebiosrm.companies.json ebiosrm-mongodb:/tmp/
docker exec ebiosrm-mongodb mongoimport --db ebiosrm --collection companies --file /tmp/ebiosrm.companies.json --jsonArray

# Import analyses collection
docker cp mongo-init/data/ebiosrm.analyses.json ebiosrm-mongodb:/tmp/
docker exec ebiosrm-mongodb mongoimport --db ebiosrm --collection analyses --file /tmp/ebiosrm.analyses.json --jsonArray

# Import analysiscomponents collection
docker cp mongo-init/data/ebiosrm.analysiscomponents.json ebiosrm-mongodb:/tmp/
docker exec ebiosrm-mongodb mongoimport --db ebiosrm --collection analysiscomponents --file /tmp/ebiosrm.analysiscomponents.json --jsonArray

# Import analysiscontrols collection
docker cp mongo-init/data/ebiosrm.analysiscontrols.json ebiosrm-mongodb:/tmp/
docker exec ebiosrm-mongodb mongoimport --db ebiosrm --collection analysiscontrols --file /tmp/ebiosrm.analysiscontrols.json --jsonArray

# Import activitylogs collection
docker cp mongo-init/data/ebiosrm.activitylogs.json ebiosrm-mongodb:/tmp/
docker exec ebiosrm-mongodb mongoimport --db ebiosrm --collection activitylogs --file /tmp/ebiosrm.activitylogs.json --jsonArray

# Import eventsuggestions collection
docker cp mongo-init/data/ebiosrm.eventsuggestions.json ebiosrm-mongodb:/tmp/
docker exec ebiosrm-mongodb mongoimport --db ebiosrm --collection eventsuggestions --file /tmp/ebiosrm.eventsuggestions.json --jsonArray

# Import frameworkdefinitions collection
docker cp mongo-init/data/ebiosrm.frameworkdefinitions.json ebiosrm-mongodb:/tmp/
docker exec ebiosrm-mongodb mongoimport --db ebiosrm --collection frameworkdefinitions --file /tmp/ebiosrm.frameworkdefinitions.json --jsonArray

# Import security_controls collection
docker cp mongo-init/data/ebiosrm.security_controls.json ebiosrm-mongodb:/tmp/
docker exec ebiosrm-mongodb mongoimport --db ebiosrm --collection security_controls --file /tmp/ebiosrm.security_controls.json --jsonArray
```

## Verification

To verify that the data was imported successfully:

```bash
# Connect to the MongoDB container
docker exec -it ebiosrm-mongodb bash

# Start the MongoDB shell
mongosh

# Switch to the ebiosrm database
use ebiosrm

# List all collections
show collections

# Count documents in a collection (example for users)
db.users.countDocuments()

# Exit the MongoDB shell
exit

# Exit the container shell
exit
```

## Troubleshooting

If you encounter any issues:

1. Make sure the MongoDB container is running:
   ```bash
   docker ps | grep ebiosrm-mongodb
   ```

2. Check the MongoDB logs:
   ```bash
   docker logs ebiosrm-mongodb
   ```

3. If you see path-related errors, make sure you're using the correct path format for your operating system:
   - On Windows with Git Bash, the commands should work as written
   - On Windows with PowerShell or Command Prompt, the commands should also work as written
   - On Linux/Mac, the commands should work as written

4. If you need to start over, you can remove the MongoDB volume and containers:
   ```bash
   docker-compose down -v
   docker-compose up -d
   ```
   Then run the import commands again.
