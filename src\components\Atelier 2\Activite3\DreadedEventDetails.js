// src/components/Atelier 2/Activite3/DreadedEventDetails.js
import React from 'react';
import { X, Shield, AlertTriangle, Check, Info, BarChart2, Target } from 'lucide-react';

const DreadedEventDetails = ({ 
  event, 
  onClose, 
  businessValues = [],
  getPillarColor,
  getSeverityColor
}) => {
  if (!event) return null;

  // Format business value
  const formatBusinessValue = (value) => {
    if (typeof value === 'number') {
      return value.toString();
    }
    return value || 'N/A';
  };

  // Get business value for a specific type
  const getBusinessValue = (type) => {
    const bv = businessValues.find(bv => bv.type === type);
    return bv ? formatBusinessValue(bv.value) : 'N/A';
  };

  // Get color for business value
  const getBusinessValueColor = (type) => {
    const bv = businessValues.find(bv => bv.type === type);
    if (!bv || !bv.value) return 'text-gray-400';
    
    const value = typeof bv.value === 'number' ? bv.value : parseInt(bv.value, 10);
    if (isNaN(value)) return 'text-gray-400';
    
    if (value >= 8) return 'text-red-600';
    if (value >= 5) return 'text-yellow-600';
    return 'text-green-600';
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
      <div className="bg-white rounded-xl shadow-xl w-full max-w-3xl max-h-[90vh] overflow-hidden flex flex-col">
        {/* Header */}
        <div className={`p-4 ${getPillarColor(event.securityPillar)} flex justify-between items-center`}>
          <div className="flex items-center">
            <Shield className="mr-2" size={20} />
            <h2 className="font-bold text-lg">{event.name}</h2>
          </div>
          <button 
            onClick={onClose}
            className="p-1 rounded-full hover:bg-black hover:bg-opacity-10 transition-colors"
          >
            <X size={20} />
          </button>
        </div>
        
        {/* Content */}
        <div className="p-6 overflow-y-auto">
          {/* Description */}
          <div className="mb-6">
            <h3 className="text-sm font-medium text-gray-500 uppercase tracking-wide mb-2">Description</h3>
            <p className="text-gray-700">{event.description || 'Aucune description disponible'}</p>
          </div>
          
          {/* Details grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            {/* Left column */}
            <div>
              {/* Security Pillar */}
              <div className="mb-4">
                <h3 className="text-sm font-medium text-gray-500 uppercase tracking-wide mb-2">Pilier de Sécurité</h3>
                <div className="flex items-center">
                  <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm ${getPillarColor(event.securityPillar)}`}>
                    <Shield size={14} className="mr-1" />
                    {event.securityPillar.charAt(0).toUpperCase() + event.securityPillar.slice(1)}
                  </span>
                </div>
              </div>
              
              {/* Severity */}
              <div className="mb-4">
                <h3 className="text-sm font-medium text-gray-500 uppercase tracking-wide mb-2">Gravité</h3>
                <div className="flex items-center">
                  <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm ${getSeverityColor(event.severity)}`}>
                    <AlertTriangle size={14} className="mr-1" />
                    {event.severity === 'minor' ? 'Mineure' :
                     event.severity === 'moderate' ? 'Modérée' :
                     event.severity === 'major' ? 'Majeure' :
                     event.severity === 'critical' ? 'Critique' :
                     event.severity === 'catastrophic' ? 'Catastrophique' :
                     event.severity}
                  </span>
                </div>
              </div>
              
              {/* Status */}
              {event.status && (
                <div className="mb-4">
                  <h3 className="text-sm font-medium text-gray-500 uppercase tracking-wide mb-2">Statut</h3>
                  <div className="flex items-center">
                    <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm ${
                      event.status === 'active' ? 'bg-green-100 text-green-800' :
                      event.status === 'inactive' ? 'bg-gray-100 text-gray-800' :
                      'bg-blue-100 text-blue-800'
                    }`}>
                      {event.status === 'active' ? <Check size={14} className="mr-1" /> : <Info size={14} className="mr-1" />}
                      {event.status === 'active' ? 'Actif' :
                       event.status === 'inactive' ? 'Inactif' :
                       event.status}
                    </span>
                  </div>
                </div>
              )}
            </div>
            
            {/* Right column - Business Values */}
            <div>
              <h3 className="text-sm font-medium text-gray-500 uppercase tracking-wide mb-2">Valeurs Métier</h3>
              <div className="bg-gray-50 rounded-lg p-4 border border-gray-200">
                <div className="grid grid-cols-2 gap-3">
                  {/* Missions/Services */}
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">Missions/Services:</span>
                    <span className={`font-bold ${getBusinessValueColor('missions')}`}>
                      {getBusinessValue('missions')}
                    </span>
                  </div>
                  
                  {/* Human/Material */}
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">Humain/Matériel:</span>
                    <span className={`font-bold ${getBusinessValueColor('human')}`}>
                      {getBusinessValue('human')}
                    </span>
                  </div>
                  
                  {/* Governance */}
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">Gouvernance:</span>
                    <span className={`font-bold ${getBusinessValueColor('governance')}`}>
                      {getBusinessValue('governance')}
                    </span>
                  </div>
                  
                  {/* Financial */}
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">Financier:</span>
                    <span className={`font-bold ${getBusinessValueColor('financial')}`}>
                      {getBusinessValue('financial')}
                    </span>
                  </div>
                  
                  {/* Legal */}
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">Juridique:</span>
                    <span className={`font-bold ${getBusinessValueColor('legal')}`}>
                      {getBusinessValue('legal')}
                    </span>
                  </div>
                  
                  {/* Image/Trust */}
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">Image/Confiance:</span>
                    <span className={`font-bold ${getBusinessValueColor('image')}`}>
                      {getBusinessValue('image')}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          {/* Impact visualization */}
          <div className="mb-6">
            <h3 className="text-sm font-medium text-gray-500 uppercase tracking-wide mb-2">Visualisation des Impacts</h3>
            <div className="bg-gray-50 rounded-lg p-4 border border-gray-200 flex justify-center">
              <div className="w-full max-w-md">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-xs text-gray-500">Faible</span>
                  <span className="text-xs text-gray-500">Moyen</span>
                  <span className="text-xs text-gray-500">Élevé</span>
                </div>
                
                {/* Impact bars */}
                {['missions', 'human', 'governance', 'financial', 'legal', 'image'].map((type, index) => {
                  const bv = businessValues.find(bv => bv.type === type);
                  const value = bv ? (typeof bv.value === 'number' ? bv.value : parseInt(bv.value, 10)) : 0;
                  const percentage = isNaN(value) ? 0 : Math.min(100, (value / 10) * 100);
                  
                  const getLabel = (type) => {
                    switch(type) {
                      case 'missions': return 'Missions/Services';
                      case 'human': return 'Humain/Matériel';
                      case 'governance': return 'Gouvernance';
                      case 'financial': return 'Financier';
                      case 'legal': return 'Juridique';
                      case 'image': return 'Image/Confiance';
                      default: return type;
                    }
                  };
                  
                  return (
                    <div key={type} className="mb-2">
                      <div className="flex items-center mb-1">
                        <span className="text-xs text-gray-600 w-32">{getLabel(type)}:</span>
                        <div className="flex-1 bg-gray-200 rounded-full h-2">
                          <div 
                            className={`h-2 rounded-full ${
                              value >= 8 ? 'bg-red-500' : 
                              value >= 5 ? 'bg-yellow-500' : 
                              'bg-green-500'
                            }`}
                            style={{ width: `${percentage}%` }}
                          ></div>
                        </div>
                        <span className="ml-2 text-xs font-medium">{isNaN(value) ? 'N/A' : value}</span>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          </div>
        </div>
        
        {/* Footer */}
        <div className="p-4 bg-gray-50 border-t border-gray-200 flex justify-end">
          <button 
            onClick={onClose}
            className="px-4 py-2 bg-gray-200 text-gray-800 rounded-lg hover:bg-gray-300 transition-colors"
          >
            Fermer
          </button>
        </div>
      </div>
    </div>
  );
};

export default DreadedEventDetails;
