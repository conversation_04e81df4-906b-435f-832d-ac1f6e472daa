{"common": {"print": "Print Report", "loading": "Loading...", "error": "Error", "save": "Save", "cancel": "Cancel", "delete": "Delete", "edit": "Edit", "add": "Add", "search": "Search", "filter": "Filter", "export": "Export", "import": "Import", "logout": "Logout", "previous": "Previous", "next": "Next", "startAnalysis": "Start Analysis", "description": "Description", "noDescription": "No description", "help": "Help", "close": "Close"}, "navigation": {"workshop1": "Workshop 1: Scoping", "context": "Context & Stakeholders", "businessValues": "Business Values", "dreadedEvents": "Dreaded Events", "securityFoundation": "Security Foundation", "securityControls": "Security Controls", "riskTreatment": "Treatment Plan"}, "riskTreatment": {"title": "Risk Treatment Plan - Summary", "executiveSummaryTitle": "Executive Summary", "generalInfo": "General Information", "analysisName": "Analysis name:", "creationDate": "Creation date:", "organization": "Organization:", "scope": "Scope:", "missions": "Missions:", "participants": "Participants", "businessContext": {"supportAssets": "Support Assets ({{count}}):", "type": "Type", "location": "Location", "description": "Desc", "noSupportAssets": "No associated support assets.", "noBusinessValues": "No business values defined."}, "initialDistribution": {"description": "Distribution of {{count}} dreaded events by initial severity level:"}, "riskIdentification": {"totalEvents": "Total number of dreaded events identified: {{count}}"}, "securityFramework": "Security Framework (Selected Rules)", "treatmentStrategies": "Adopted Treatment Strategies", "detailedPlan": "Detailed Treatment Plan", "acceptedRisks": "Accepted Risks", "residualDistribution": "Residual Risk Distribution (After Treatment)", "executiveSummary": {"intro": "This summary presents the results of Workshop 1 EBIOS RM risk analysis for the analysis \"{{analysisName}}\", covering the scope \"{{scope}}\".", "businessValues": "In total, {{businessValuesCount}} essential business values have been identified, leading to {{dreadedEventsCount}} potential dreaded events. Among these, {{criticalEventsCount}} events present an initial Critical (G4) or Catastrophic (G5) severity.", "securityRules": "To address these risks, {{rulesCount}} security rules have been selected and evaluated. The detailed treatment plan outlines the decisions made: {{reduceCount}} Reductions, {{acceptCount}} Acceptances, {{transferCount}} Transfers, {{avoidCount}} Avoidances.", "undefined": "{{count}} Undefined", "objective": "The objective is to bring the residual risk level to an acceptable threshold for the organization."}, "table": {"headers": {"severityLevel": "Severity Level", "eventCount": "Number of Events", "eventName": "Event Name", "associatedBusinessValue": "Associated Business Value", "primaryImpact": "Primary Impact", "initialSeverity": "Initial Severity"}}, "noMissions": "No missions defined"}, "businessValues": {"title": "Business Values", "breadcrumb": {"workshop1": "Workshop 1", "businessValues": "Business Values"}, "buttons": {"list": "List", "visualize": "Visualize", "add": "Add", "save": "Save", "help": "Help", "edit": "Edit", "delete": "Delete", "cancel": "Cancel", "addAsset": "Add <PERSON>set", "manage": "Manage", "close": "Close", "start": "Start", "addValue": "Add", "remove": "Remove", "expand": "Expand", "collapse": "Collapse"}, "fields": {"name": "Name", "description": "Description", "businessValueName": "Business value name", "businessValueDescription": "Business value description (optional)", "supportAssetName": "Support asset name", "supportAssetDescription": "Support asset description (optional)", "owner": "Owner (optional)", "location": "Location (optional)", "type": "Type"}, "placeholders": {"businessValueName": "Business value name", "businessValueDescription": "Business value description (optional)", "supportAssetName": "Support asset name *", "supportAssetDescription": "Support asset description (optional)", "owner": "Owner (optional)", "location": "Location (optional)"}, "sections": {"identifiedValues": "Identified business values", "securityPillars": "Related security pillars", "supportAssets": "Associated support assets", "addSupportAsset": "Add a support asset", "legend": "Legend"}, "status": {"saving": "Saving business values...", "saved": "Business values saved successfully!", "saveError": "Error saving", "valueAdded": "Business value \"{name}\" added successfully!", "valueRemoved": "Business value \"{name}\" removed successfully!", "emptyNameError": "Business value name cannot be empty.", "selected": "selected", "assets": "assets"}, "confirmations": {"deleteValue": "Are you sure you want to delete the business value \"{name}\"?", "deleteAsset": "Delete support asset \"{name}\"?"}, "empty": {"noValues": "No business values have been added", "noAssets": "No support assets have been associated", "noAssetsChart": "No associated support assets", "addValuesForChart": "Add business values to visualize their mapping"}, "modal": {"addTitle": "Add a new business value"}, "chart": {"selectValue": "Select a business value", "useShortIds": "Use short identifiers (BV1, BV2...)", "securityPillars": "Security Pillars", "supportAssets": "Support Assets", "globalMapping": "Global Mapping", "zoomIn": "Zoom in", "zoomOut": "Zoom out", "resetView": "Reset view", "studyPerimeter": "Study Perimeter", "supportAssetCount": "support asset(s)", "pillarCount": "pillar(s)", "assets": "assets"}, "legend": {"informationSystem": "Information System", "businessValue": "Business Value", "supportAsset": "Support Asset", "securityPillar": "Security Pillar", "studyPerimeter": "Study Perimeter"}, "help": {"title": "Help on business values", "description": "Business values represent the essential resources of your organization. Use the list view to manage each value individually or switch to the chart view to visualize the relationships between your business values and their support assets."}, "guide": {"title": "User Guide", "steps": {"identify": {"title": "Identify business values", "description": "Identify the essential processes, services or missions of your organization that must be protected."}, "evaluate": {"title": "Assess importance", "description": "For each business value, assess its level of importance to the organization (low, medium, high)."}, "assets": {"title": "Identify support assets", "description": "For each business value, identify the support assets (hardware, software, networks, personnel) that support it."}, "links": {"title": "Define links", "description": "Establish links between business values and support assets to understand dependencies."}}, "advice": {"title": "Advice", "description": "Focus on the most critical business values for your organization. Too broad an analysis can dilute the effectiveness of your security approach."}}, "assetTypes": {"organisation": "Organization", "locaux": "Premises", "equipements": "Equipment", "reseaux": "Networks", "logiciels": "Software and Applications"}}, "footer": {"copyright": "EBIOS RM - Risk Analysis Application © 2025"}, "dashboard": {"title": "Dashboard", "subtitle": "Security risk analysis and monitoring", "version": "v2.0", "analysisSelector": {"title": "Analysis Selection", "info": "Choose an analysis to view its data"}, "status": {"loading": "Loading data...", "error": "Error loading data", "errorDetails": "Please try again or contact support if the problem persists.", "retry": "Retry"}, "tabs": {"overview": "Overview", "participants": "Participants", "events": "Dreaded Events", "security": "Security Measures", "analysis": "Analysis"}, "cards": {"participants": "Participants", "businessValues": "Business Values", "dreadedEvents": "Dreaded Events", "securityMeasures": "Security Measures", "differentRoles": "different roles", "pillarsCovered": "pillars covered", "critical": "critical", "implemented": "implemented", "toDefine": "To be defined"}, "charts": {"securityDistribution": "Security Measures Distribution", "eventsByPillar": "Dreaded Events by <PERSON><PERSON>", "impactTypes": "Most Important Impact Types", "implementationTimeline": "Implementation Timeline", "globalSecurityStatus": "Global Security Status", "numberOfMeasures": "Number of measures", "numberOfEvents": "Number of events", "security": "Security", "eventsBySeverity": "Events by Severity", "businessValuePillars": "Business Value Pillars"}, "statusLabels": {"implemented": "Implemented", "partial": "Partial", "planned": "Planned", "toApply": "To Apply", "toAbandon": "To Abandon", "implementedMeasures": "Implemented measures", "partialMeasures": "Partial measures", "plannedMeasures": "Planned measures"}, "empty": {"noSecurityMeasures": "No security measures defined", "defineSecurityMeasures": "Define security measures to visualize the timeline"}, "footer": {"organization": "Organization", "analysisDate": "Analysis Date", "lastModified": "Last Modified"}}, "analysisSelector": {"title": "Analysis Selection", "newAnalysis": "New Analysis", "cancel": "Cancel", "availableAnalyses": "Available Analyses", "loading": "Loading", "loadingAnalyses": "Loading analyses...", "selected": "Selected", "select": "Select", "modifiedOn": "Modified on", "createdOn": "Created on", "by": "By", "unknownUser": "Unknown user", "noDescription": "No description", "viewDetails": "View details", "edit": "Edit", "delete": "Delete", "confirm": "Confirm", "deleting": "Deleting...", "creating": "Creating...", "createAnalysis": "Create Analysis", "analysisName": "Analysis name", "analysisNamePlaceholder": "Ex: Risk Analysis - Project X", "description": "Description", "optionalDescription": "Description (optional)", "descriptionPlaceholder": "Analysis scope description...", "noAnalysesAvailable": "No analyses available", "createFirstAnalysis": "Create your first analysis by clicking \"New Analysis\"", "selectedAnalysis": "Selected Analysis", "showDetails": "Show details", "hideDetails": "Hide details", "company": "Company", "status": {"inProgress": "In Progress", "completed": "Completed"}, "alerts": {"mustBeConnected": "You must be connected to a company to create an analysis.", "editFeatureNotImplemented": "Edit feature to be implemented"}}, "context": {"title": "Study Framework & RACI Matrix", "breadcrumb": {"workshop1": "Workshop 1", "framing": "Framing"}, "buttons": {"export": "Export", "save": "Save", "help": "Help", "addParticipant": "Add Participant", "addMission": "Add"}, "sections": {"generalInfo": "General Information", "participants": "Participants & RACI Matrix", "visualization": "RACI Matrix Visualization"}, "steps": {"step1": "Step 1/3", "step2": "Step 2/3", "step3": "Step 3/3"}, "fields": {"organizationName": "Organization Name", "organizationNamePlaceholder": "Enter your organization name", "organizationNameHelp": "Name of the entity concerned by the analysis", "analysisDate": "Analysis Date", "analysisDateHelp": "Start date of the risk analysis", "missions": "Missions", "missionPlaceholder": "Add a mission...", "scope": "Objective/Scope", "scopePlaceholder": "Describe the objective and scope of the analysis...", "scopeHelp": "Clear definition of objectives and analysis scope", "participantName": "Participant Name", "participantNamePlaceholder": "Full name", "position": "Position/Role", "customPosition": "Custom Position", "customPositionPlaceholder": "Specify the position...", "selectRole": "Select"}, "status": {"loading": "Loading context data...", "saving": "Saving context data...", "saved": "Context data saved successfully!", "exported": "Data exported successfully!", "participantAdded": "New participant added"}, "errors": {"loadingError": "Context loading error", "retry": "Retry", "saveError": "Error saving data", "exportError": "Error exporting data", "invalidParticipants": "Some participants have no name or are invalid and will not be saved. Continue?", "serverError": "Error communicating with server", "fileGenerationError": "Error generating file"}, "tooltips": {"export": "Export data in JSON format", "save": "Save context data"}, "required": "*", "advice": {"title": "Tip:", "missionScope": "Clearly define the mission and scope of your analysis to guide risk identification and role definition. A well-defined mission and scope facilitate the assignment of RACI responsibilities."}, "table": {"participant": "Participant", "position": "Position / Role", "workshop1": "Workshop 1", "workshop2": "Workshop 2", "workshop3": "Workshop 3", "workshop4": "Workshop 4", "workshop5": "Workshop 5", "noMissions": "No missions added", "noParticipants": "No participants to display.", "addParticipantHint": "Click \"Add Participant\" to get started.", "cannotDisplayVisualization": "Cannot display visualization", "addParticipantsHint": "Add participants and workshops, or check test data to enable RACI visualization."}, "raci": {"legend": "RACI Legend", "responsible": "Responsible", "responsibleDesc": "Executes the task", "accountable": "Accountable", "accountableDesc": "Validates the result", "consulted": "Consulted", "consultedDesc": "Provides input", "informed": "Informed", "informedDesc": "<PERSON><PERSON> informed", "notInvolved": "Not Involved", "notInvolvedDesc": "Does not participate"}, "visualization": {"matrixSynthesis": "Matrix Summary", "workshopFocus": "Workshop Focus", "participantFocus": "Participant Focus", "filterByRole": "Filter by Role", "allRoles": "All roles", "filterByWorkshop": "Filter by Workshop", "allWorkshops": "All workshops", "activeFilters": "Active filters:", "role": "Role:", "workshop": "Workshop:", "removeRoleFilter": "Remove role filter", "removeWorkshopFilter": "Remove workshop filter", "function": "Function", "distribution": "Distribution", "globalDistribution": "Global Distribution of Assigned Roles", "noAssignments": "No role assignments found with current filters.", "noParticipant": "No participant", "noResults": "No results to display", "adjustFilters": "The selected filters return no data. Try adjusting the filters or check the entered RACI data.", "noAssignmentForParticipant": "No assignment for this participant with current filters.", "none": "None"}}, "dreadedEvents": {"title": "Dreaded Events", "breadcrumb": {"workshop1": "Workshop 1", "dreadedEvents": "Dreaded Events"}, "buttons": {"save": "Save", "saving": "Saving...", "help": "Help", "add": "Add", "edit": "Edit", "delete": "Delete", "cancel": "Cancel", "validate": "Validate", "export": "Export", "reset": "Reset", "resetFilters": "Reset filters", "close": "Close", "start": "Start", "loadMore": "More suggestions", "loading": "Loading...", "addSelection": "Add selection", "getAiSuggestions": "Get AI Suggestions", "searchingAi": "AI Search...", "updateEvent": "Update event", "addCustomEvent": "Add custom event", "cancelModification": "Cancel modification"}, "sections": {"definition": "Definition of dreaded events", "criteriaSelection": "Criteria selection", "suggestions": "Suggestions", "customEvent": "Custom event", "identifiedEvents": "Identified dreaded events", "selectCriteria": "Select criteria", "proposalsFor": "Proposals for"}, "fields": {"businessValue": "Related business value", "securityPillar": "Impacted security pillar", "eventName": "Event name", "description": "Description", "severity": "Severity scale", "impacts": "Impacts (multiple selection)", "gravity": "Severity"}, "placeholders": {"selectBusinessValue": "Select a business value", "eventName": "Ex: Customer data leak", "eventDescription": "Description of the dreaded event and its consequences", "searchSuggestion": "Search for a suggestion...", "searchEvent": "Search for an event..."}, "status": {"saving": "Saving dreaded events...", "saved": "Dreaded events saved successfully!", "saveError": "Error saving", "eventAdded": "Dreaded event \"{name}\" added successfully!", "eventRemoved": "Dreaded event \"{name}\" removed successfully!", "eventUpdated": "Dreaded event \"{name}\" updated successfully!", "suggestionsAdded": "{count} suggestion{plural} added successfully!", "aiSuggestionsGenerated": "{count} AI suggestion{plural} generated successfully!", "newAiSuggestionsGenerated": "{count} new AI suggestion{plural} generated!", "noNewAiSuggestions": "No new AI suggestions could be generated.", "noAiSuggestions": "No AI suggestions could be generated.", "fillRequiredFields": "Please fill in all required fields.", "selectAtLeastOne": "Please select at least one suggestion.", "selectBusinessValueAndPillar": "Please first select a Business Value and a Security Pillar.", "selectAnalysisValuePillar": "Please select an analysis, a business value and a security pillar.", "selected": "selected", "events": "event(s)", "eventsDisplayed": "event{plural} displayed out of", "bulkDeleteConfirm": "Are you sure you want to delete {count} event{plural}?", "bulkDeleteSuccess": "{count} event{plural} deleted successfully", "notSpecified": "Not specified", "undefined": "Undefined"}, "empty": {"noEvents": "No dreaded events defined", "noEventsDescription": "Select a business value and a security pillar, then add dreaded events to see them appear here.", "noSuggestions": "No suggestions found.", "noSuggestionsForPillar": "No suggestions available for this pillar. Select another security pillar.", "noSearchResults": "No suggestions match your search", "noResults": "No results found", "noResultsDescription": "No events match your search criteria. Try modifying your filters.", "selectCriteriaDescription": "To define dreaded events, please first select a business value and an impacted security pillar in the left column."}, "info": {"definition": "For each business value, identify the dreaded events that could impact each of the associated security pillars and assess their severity.", "businessValuesWithEvents": "Business values with existing dreaded events"}, "table": {"headers": {"businessValue": "Business value", "impactedPillar": "Impacted pillar", "dreadedEvent": "Dreaded event", "impacts": "Impacts", "severity": "Severity", "actions": "Actions"}}, "modal": {"aiSuggestions": "AI suggestions for \"{businessValue}\" ({pillar})", "editEvent": "Edit event", "createCustomEvent": "Create custom event", "error": "Error"}, "export": {"confirmCsv": "Do you want to export dreaded events to CSV format?", "toImplement": "Export functionality to be implemented"}, "guide": {"title": "User Guide", "steps": {"identify": {"title": "Identify dreaded events", "description": "Identify dreaded events that could affect your business values, such as disclosure of confidential information or unavailability of a critical service."}, "associate": {"title": "Associate with business values", "description": "For each dreaded event, associate it with one or more business values that would be impacted."}, "evaluate": {"title": "Assess impacts", "description": "Assess the impact of each dreaded event on different dimensions: missions/services, human/material/environmental, governance, financial, legal, and image/trust."}, "pillars": {"title": "Identify security pillars", "description": "Associate each dreaded event with a security pillar (confidentiality, integrity, availability, auditability, proof, traceability)."}}, "advice": {"title": "Advice", "description": "Use the AI suggestions button to generate relevant dreaded events based on your business values and your organization's context."}}}, "severity": {"minor": "Minor", "moderate": "Moderate", "major": "Major", "critical": "Critical", "catastrophic": "Catastrophic"}, "impacts": {"missions_services": "Impacts on organizational missions and services", "humain_materiel_environnemental": "Human, material or environmental impacts", "gouvernance": "Governance impacts", "financier": "Financial impacts", "juridique": "Legal impacts", "image_confiance": "Image and trust impacts"}, "securityPillars": {"confidentialite": "Confidentiality", "integrite": "Integrity", "disponibilite": "Availability", "tracabilite": "Traceability", "preuve": "Proof", "auditabilite": "Auditability"}, "workshop2": {"title": "Workshop 2 - Risk Sources and Target Objectives", "navigationTitle": "Workshop 2: RS & TO", "currentAnalysis": "Analysis: {{name}}", "noAnalysisSelected": "No analysis selected", "activities": {"identifySROV": "Identify RS/TO", "evaluateSROV": "Evaluate RS/TO", "selectSROV": "Select RS/TO"}, "navigation": {"identifySROV": "Identify RS and TO", "evaluateSROV": "Evaluate RS/TO couples", "associateSRER": "Associate RS to DE"}, "activity1": {"breadcrumb": "Activity 1", "title": "Identify risk sources and target objectives", "riskSourcesByCategory": "Risk sources by category", "errors": {"noAnalysisSelected": "No analysis selected", "invalidApiResponse": "Invalid API response", "aiGenerationError": "Error generating AI suggestions. Please try again.", "noSuggestionSelected": "No suggestion selected"}, "success": {"sourcesAdded": "{{count}} risk source(s) added", "sourcesAddedBeforeSave": "{{count}} risk source(s) added before save"}, "ai": {"suggestions": "AI Suggestions", "generating": "Generating AI...", "modalTitle": "AI Risk Source Suggestions", "generatingInProgress": "Generating AI suggestions...", "analyzingContext": "Analyzing your business context and threat landscape", "noSuggestions": "No AI suggestions available", "createManually": "You can create risk sources manually using the form above", "createManuallyButton": "Create Manual Source", "suggestionsTitle": "AI Generated Risk Sources", "selectSuggestions": "Select the risk sources you want to add to your analysis:", "selectedCount": "{{count}} selected", "uncategorized": "Uncategorized", "motivation": "Motivation", "activity": "Activity", "resources": "Resources", "moreSuggestions": "More Suggestions", "addButton": "Add Selected Sources"}, "levels": {"low": "Low", "medium": "Medium", "high": "High"}, "threatCategories": {"title": "Threat Categories", "selectedCount": "{{count}} categories selected out of {{total}}", "selectButton": "Select", "selectedButton": "Selected", "deselectButton": "Deselect"}, "addSource": {"title": "Add a risk source", "subtitle": "Select a category and add a new source", "selectedCategory": "Selected category", "noCategory": "No category selected", "unknownCategory": "Unknown category", "name": "Name", "namePlaceholder": "Source name", "targetObjectiveCategory": "Target Objective Category", "selectObjectiveCategory": "Select an objective category", "targetObjectiveDetail": "Target Objective Detail", "targetObjectiveDetailRequired": "Target Objective Detail *", "targetObjectivePlaceholder": "Specify the objective targeted by the source", "motivation": "Motivation", "activity": "Activity", "resources": "Resources", "description": "Description", "descriptionPlaceholder": "Description (optional)", "addButton": "Add source"}, "sourcesList": {"title": "Threat categories", "noSources": "No risk source for this category", "noSourcesHint": "Select this category and use the form above to add a source", "editingTitle": "Editing risk source", "saveButton": "Save", "cancelButton": "Cancel", "editButton": "Edit", "deleteButton": "Delete", "nameLabel": "Risk source name", "descriptionLabel": "Description", "targetObjectiveLabel": "Target objective detail", "motivationLabel": "Motivation", "activityLabel": "Activity", "resourcesLabel": "Resources"}, "table": {"headers": {"riskSource": "Risk source", "targetObjectiveDetail": "Target Objective Detail", "objectiveCategory": "TO Category", "motivation": "Motivation", "activity": "Activity", "resources": "Resources"}}, "messages": {"selectCategory": "Please select a category", "nameRequired": "Risk source name is required", "targetObjectiveRequired": "Target objective detail is required", "sourceAdded": "Risk source added", "sourceUpdated": "Risk source updated", "sourceDeleted": "Risk source deleted", "sourcesSaved": "Risk sources saved successfully", "saveError": "Error saving risk sources", "noAnalysisSelected": "No analysis selected", "noCategorySelected": "No threat category selected", "selectCategoriesFirst": "Please first select threat categories in the \"Threat Categories\" tab."}, "objectiveCategories": {"confidentialite": "CONFIDENTIALITY", "integrite": "INTEGRITY", "disponibilite": "AVAILABILITY", "tracabilite": "TRACEABILITY", "preuve": "PROOF", "defi": "CHALLENGE, ENTERTAINMENT"}, "levelLabels": {"faible": "Low", "moyen": "Medium", "eleve": "High", "faibles": "Low", "moyennes": "Medium", "importantes": "High"}, "categories": {"etatique": {"name": "STATE", "description": "States, intelligence agencies.", "details": "Professional attacks, stable resources, long-term operational capacity. Can purchase or discover zero-day vulnerabilities (0-Day) and infiltrate isolated networks."}, "criminel": {"name": "ORGANIZED CRIME", "description": "Cybercriminal organizations (mafias, gangs, agencies).", "details": "Online scams, ransomware, botnet exploitation. Sophisticated operations for profit. Some can purchase zero-day vulnerabilities."}, "terroriste": {"name": "TERRORIST", "description": "Cyberterrorists, cybermilitias.", "details": "Unsophisticated but determined attacks aimed at destabilization and destruction: denial of service, shutdowns of critical systems."}, "activiste": {"name": "IDEOLOGICAL ACTIVIST", "description": "Cyber-hacktivists, interest groups, sects.", "details": "Operating methods similar to cyberterrorists but with less destructive intentions. Convey an ideology or message."}, "officine": {"name": "SPECIALIZED AGENCY", "description": "Cybermercenaries with high technical capabilities.", "details": "Create and sell attack tools and kits. Primarily motivated by financial gain."}, "amateur": {"name": "AMATEUR", "description": "Script-kiddie hackers or those with good computer knowledge.", "details": "Motivated by social recognition, entertainment or challenge. Use attack kits accessible online."}, "vengeur": {"name": "AVENGER", "description": "Individuals driven by a spirit of revenge or sense of injustice.", "details": "Fired ex-employees, disgruntled contractors. Formidable due to their determination and internal knowledge of systems."}, "malveillant": {"name": "PATHOLOGICAL MALICIOUS", "description": "Individuals with pathological or opportunistic motivations.", "details": "Unfair competitors, dishonest customers, scammers. May exploit attack kits or subcontract to specialized agencies."}}}, "activity2": {"breadcrumb": "Activity 2", "title": "Evaluate RS/TO couples", "evaluationTitle": "RS/TO couples evaluation", "saving": "Saving...", "views": {"table": "Table", "visualization": "Visualization"}, "errors": {"sourceWithoutName": "Source without name", "unknownCategory": "Unknown category", "noRiskSources": "No risk sources found. Please add some in Activity 1.", "loadingError": "Error loading data: {{message}}", "unknownError": "Unknown error", "saveError": "Error saving: {{message}}", "errorWithMessage": "Error: {{message}}", "saveRiskSourcesError": "Error saving risk sources"}, "success": {"riskSourcesSaved": "Risk sources saved successfully"}, "print": {"preparing": "Preparing for printing...", "preparingDocument": "Preparing document for printing...", "visualizationTitle": "RS/TO couples visualization", "viewByObjective": "View by target objective", "viewBySource": "View by risk source", "title": "RS/TO couples evaluation", "analysis": "Analysis", "date": "Date", "noJustification": "No justification provided", "notSelected": "Not selected", "page1": "Page 1", "page2": "Page 2", "generatedOn": "Document generated on", "documentReady": "Document ready for printing"}, "table": {"headers": {"riskSource": "Risk source", "category": "Category", "targetObjective": "Target objective", "motivation": "Motivation", "activity": "Activity", "resources": "Resources", "level": "Level", "selected": "Selected", "justification": "Justification"}, "search": "Search...", "allCategories": "All categories", "total": "Total", "retained": "Retained", "highLevel": "High level", "noResults": "No RS/TO couples match your search", "resetFilters": "Reset filters", "noJustification": "No justification provided", "notRetained": "Not retained", "clickToEdit": "Click to edit", "cancel": "Cancel", "save": "Save", "edit": "Edit", "confirm": "Confirm", "enterJustification": "Enter a justification..."}, "visualization": {"visionByObjective": "Vision by target objective", "visionBySource": "Vision by risk source", "pointsRepresentSources": "Points represent risk sources (RS)", "pointsRepresentObjectives": "Points represent target objectives (TO)", "interpretationGuide": "Interpretation guide", "dragToModifyLevel": "Drag a point to modify its level (1-3). Closer to center = higher level.", "dragToModifyImpact": "Drag a point to modify its impact (1-3). Closer to center = higher impact.", "criticalZone": "Critical zone", "moderateZone": "Moderate zone", "lowZone": "Low zone", "level": "Level", "priorityAttention": "Priority attention required", "vigilanceRecommended": "Vigilance recommended", "limitedRisk": "Limited risk", "interpretation": "Interpretation", "interpretationSources": "This visualization shows how risk sources are distributed according to target objectives and their level of criticality.", "interpretationObjectives": "This visualization shows how target objectives are distributed according to risk sources and their level of criticality.", "closerToCenter": "Elements closer to the center represent a higher level of risk and require special attention.", "retained": "Retained", "notRetained": "Not retained", "dataTable": {"id": "ID", "name": "Name", "status": "Status", "level": "Level", "sector": "Sector", "activity": "Activity", "motivation": "Motivation", "resources": "Resources", "objective": "Objective", "category": "Category", "actions": "Actions", "critical": "Critical", "moderate": "Moderate", "low": "Low", "remove": "Remove", "retain": "<PERSON><PERSON>", "riskSources": "Risk sources", "targetObjectives": "Target objectives"}}}, "activity3": {"breadcrumb": "Activity 3", "title": "RS/DE Association", "subtitle": "Association of Retained Risk Sources to Dreaded Events", "showTable": "Show table", "hideTable": "Hide table", "saving": "Saving risk source / dreaded event associations...", "errors": {"loadingError": "Error loading data", "saveError": "Error saving associations"}, "success": {"associationsSaved": "Associations saved successfully"}, "cards": {"retainedRiskSources": "Retained Risk Sources", "outOfIdentified": "out of {{total}} identified sources", "dreadedEvents": "Dreaded Events", "eventsAvailable": "events available", "associations": "Associations", "associationsCreated": "associations created"}, "filter": {"title": "Filter risk sources", "search": "Search", "searchPlaceholder": "Search for a risk source...", "filterByCategory": "Filter by category", "allCategories": "All categories", "activeFilters": "Active filters", "category": "Category", "reset": "Reset", "resetFilters": "Reset filters"}, "loading": {"title": "Loading data", "message": "Please wait while we retrieve the information..."}, "empty": {"noRetainedSources": "No retained risk sources found", "noSourcesRetained": "No risk sources have been retained in the previous activity.", "selectSourcesFirst": "Please first select risk sources to retain in the previous activity before continuing.", "noMatchingCriteria": "No risk sources match your search criteria."}, "sourceCard": {"objective": "Objective", "targetObjective": "Target Objective", "notSpecified": "Not specified", "noDreadedEvents": "No dreaded events available", "eventsSelected": "{{count}} event(s) selected", "selectEvents": "Select dreaded events..."}, "guide": {"title": "User Guide", "selectRiskSources": "Select risk sources", "selectRiskSourcesDesc": "Browse the retained risk sources and click on a card to see details and associate dreaded events.", "associateDreadedEvents": "Associate with dreaded events", "associateDreadedEventsDesc": "For each risk source, select one or more relevant dreaded events from the list.", "understandSecurityPillars": "Understand security pillars", "understandSecurityPillarsDesc": "Each dreaded event is linked to a security pillar (confidentiality, integrity, availability, etc.) that will be affected.", "evaluateGravity": "Evaluate gravity", "evaluateGravityDesc": "The gravity of each association is determined by the impact of the dreaded event and the capabilities of the risk source.", "consultSummaryTable": "Consult summary table", "consultSummaryTableDesc": "Use the 'Show table' button to see all associations in a complete tabular format.", "tip": "Tip", "tipMessage": "Don't forget to save your associations before leaving the page to avoid losing your work.", "start": "Start"}}}, "workshop3": {"title": "Workshop 3: Strategic Scenarios", "navigationTitle": "Workshop 3: <PERSON><PERSON><PERSON><PERSON>", "subtitle": "Map the ecosystem and develop strategic scenarios for risk analysis.", "activities": {"mapEcosystem": "Map ecosystem", "strategicScenarios": "Strategic scenarios", "attackPaths": "Attack paths", "securityMeasures": "Security measures", "eventProgression": "Event progression"}, "navigation": {"mapEcosystem": "Map ecosystem", "strategicScenarios": "Strategic scenarios", "attackPaths": "Attack paths", "securityMeasures": "Security measures", "eventProgression": "Event progression"}, "activity1": {"breadcrumb": "Map ecosystem", "title": "Map ecosystem", "stakeholder": "Stakeholder", "stakeholders": "Stakeholders", "addStakeholder": "Add stakeholder", "editStakeholder": "Edit stakeholder", "filters": "Filters", "retained": "retained", "notRetained": "not retained", "saving": "Saving stakeholders...", "updatingThresholds": "Updating thresholds...", "radarVisualization": "Radar visualization", "modifyThresholds": "Modify thresholds", "filter": {"allTypes": "All types", "internal": "Internal", "external": "External", "allCategories": "All categories"}, "categories": {"client": "Client", "partner": "Partner", "provider": "Provider", "technical": "Technical service", "business": "Business service", "subsidiary": "Subsidiary"}, "legend": {"title": "Legend", "zones": "ZONES", "danger": "Danger", "control": "Control", "watch": "Watch", "types": "TYPES", "categories": "CATEGORIES", "cyberReliability": "Cyber Reliability", "exposure": "Exposure", "weak": "Weak", "average": "Average", "good": "Good", "excellent": "Excellent", "minimal": "Minimal", "moderate": "Moderate", "high": "High", "critical": "Critical"}, "table": {"sortBy": "Sort by", "name": "Name", "category": "Category", "type": "Type", "threat": "Threat", "dependency": "Dep", "penetration": "Pen", "cyberMaturity": "Mat", "trust": "Trust", "retained": "Retained", "zone": "Zone", "actions": "Actions", "rank": "Rank"}, "errors": {"loadingError": "Error loading data", "saveError": "Error: {{message}}", "unknownError": "Unknown error", "saveStakeholdersError": "Error saving stakeholders", "updateThresholdsError": "Error updating thresholds"}, "success": {"stakeholdersSaved": "Stakeholders saved successfully", "thresholdsUpdated": "Thresholds updated successfully", "stakeholderUpdated": "Stakeholder updated successfully", "stakeholderAdded": "Stakeholder added successfully", "stakeholderDeleted": "{{name}} deleted successfully", "stakeholderStatusChanged": "Stakeholder {{name}} marked as {{status}}"}, "stakeholderForm": {"name": "Name", "namePlaceholder": "Stakeholder name", "category": "Category", "type": "Type", "rank": "Rank", "rank1": "Rank 1 (direct interaction)", "rank2": "Rank 2 (indirect interaction)", "rank3": "Rank 3 (distant interaction)", "description": "Description", "descriptionPlaceholder": "Stakeholder description", "criteriaEvaluation": "Criteria evaluation", "dependency": "Dependency", "penetration": "Penetration", "cyberMaturity": "Cyber Maturity", "trust": "Trust", "dependency1": "Non-essential relationship to strategic functions", "dependency2": "Useful relationship to strategic functions", "dependency3": "Essential but non-exclusive relationship", "dependency4": "Essential and unique relationship (no short-term substitution possible)", "penetration1": "No access or user-level access to user terminals", "penetration2": "Administrator access to user terminals or physical access to sites", "penetration3": "Administrator access to business servers", "penetration4": "Administrator access to infrastructure equipment or physical access to server rooms", "cyberMaturity1": "IT hygiene rules are applied occasionally and not formalized", "cyberMaturity2": "Hygiene rules and regulations are taken into account, without integration into a global policy", "cyberMaturity3": "A global policy is applied in terms of digital security", "cyberMaturity4": "The stakeholder implements a risk management policy", "trust1": "The stakeholder's intentions cannot be assessed", "trust2": "The stakeholder's intentions are considered neutral", "trust3": "The stakeholder's intentions are known and probably positive", "trust4": "The stakeholder's intentions are perfectly known and fully compatible", "calculatedThreatLevel": "Calculated threat level", "threatLevelFormula": "(Dependency × Penetration) / (Cyber Maturity × Trust)", "notes": "Notes", "notesPlaceholder": "Additional notes", "retainedDescription": "Retained stakeholders will be included in the risk analysis", "update": "Update", "add": "Add"}, "guide": {"title": "Guide: Map the Ecosystem", "description": "This activity allows you to map the stakeholder ecosystem and assess the threat level they represent for the study object.", "stepsToFollow": "Steps to follow", "step1Title": "Identify stakeholders", "step1Description": "Identify internal and external stakeholders who interact with the study object. Classify them by category (client, partner, provider, etc.) and by type (internal or external).", "step2Title": "Evaluate exposure criteria", "step2Description": "For each stakeholder, evaluate the level of dependency (vital relationship for the activity) and penetration (access to internal resources).", "step3Title": "Evaluate cyber reliability criteria", "step3Description": "Evaluate the cyber maturity (security capabilities) and trust (intentions and interests) of each stakeholder.", "step4Title": "Calculate threat level", "step4Description": "The threat level is calculated according to the formula: (Dependency × Penetration) / (Cyber Maturity × Trust). The higher the level, the more the stakeholder represents a threat."}, "loading": "Loading stakeholders...", "empty": {"noStakeholders": "No stakeholders", "addStakeholders": "Add stakeholders to start mapping the ecosystem."}}, "activity4": {"breadcrumb": "Security Measures", "title": "Security Measures", "generateWithAI": "Generate with AI", "description": "Define specific security measures for the ecosystem, aimed at reducing the danger level induced by critical stakeholders and acting on identified strategic scenarios.", "saving": "Saving measures...", "success": {"measuresSaved": "Security measures saved successfully"}, "errors": {"saveError": "Error during save", "error": "Error", "saveProblem": "Save problem"}}, "activity5": {"breadcrumb": "PTR Progression", "title": "Risk Treatment Plan Progression", "loading": "Loading data...", "description": "Track the progression of Risk Treatment Plans (PTR) by analyzing the coverage of dreaded events by Workshop 1 and Workshop 3 measures. Identify untreated, partially treated or enriched events to optimize your security strategy.", "demoMode": "Demo Mode", "demoModeDescription": "The displayed data are examples. To see your real data, define dreaded events in Workshop 1 and security measures in Workshop 3.", "printTitle": "Risk Treatment Plan Progression", "defaultAnalysisName": "EBIOS RM Analysis", "print": {"generalInfo": "General Information", "analysis": "Analysis", "generationDate": "Generation date", "eventCount": "Number of events", "globalStats": "Global Statistics", "totalEvents": "Total events", "enrichedEvents": "Enriched events (A1 + A3)", "partialEvents": "Partial events (A1 or A3)", "notTreatedEvents": "Untreated events", "globalCoverageRate": "Global coverage rate", "progressCharts": "Progress Charts", "eventDistribution": "Event Distribution", "enriched": "Enriched", "partial": "Partial", "notTreated": "Not treated", "progressionRates": "Progression Rates", "enrichment": "Enrichment", "globalCoverage": "Global Coverage", "totalMeasures": "Total measures", "workshop1": "Workshop 1", "workshop3": "Workshop 3", "averageProgression": "Average progression", "eventDetails": "Dreaded Event Details", "dreadedEvent": "Dreaded Event", "businessValue": "Business Value", "supportAssets": "Support Assets", "progression": "Progression", "status": "Status", "notDefined": "Not defined", "none": "None", "measure": "measure", "measures": "measures", "noDataAvailable": "No data available for printing.", "toolName": "EBIOS RM - Risk Management Tool", "reportGenerated": "Report automatically generated on", "methodology": "EBIOS Risk Manager Methodology - ANSSI"}, "guide": {"title": "PTR Progression Guide", "objective": "Objective of this activity", "objectiveDescription": "This activity allows you to track the progression of Risk Treatment Plans (PTR) by analyzing the coverage of dreaded events by Workshop 1 and Workshop 3 measures.", "progressionLogic": "New progression logic", "enriched": "Enriched", "enrichedDescription": "Event treated by both workshops (A1 + A3)", "partiallyTreated": "Partially treated", "partiallyTreatedDescription": "Event treated by one workshop only (A1 OR A3)", "notTreated": "Not treated", "notTreatedDescription": "Event not treated by any workshop", "generateReport": "Generate a synthesis report for complete analysis", "tableUsage": "Table usage", "tableUsageDescription": "Use filters to analyze event subsets. Click on arrows to see measure details for each workshop. The statistics at the top give you an overview of global progression."}}, "activity2": {"breadcrumb": "Strategic Scenarios", "title": "Strategic Scenarios", "subtitle": "Define strategic scenarios for your risk analysis by associating RS/TO couples, dreaded events, business values and stakeholders.", "loading": "Loading data...", "loadingMessage": "Loading strategic scenarios data...", "saveAttackPaths": "Save attack paths", "searchPlaceholder": "Search...", "allCategories": "All categories", "controlStatus": {"implemented": "Implemented", "planned": "Planned", "notDefined": "Not defined"}, "table": {"scenarios": "Scenarios", "riskSource": "Risk Source", "targetObjective": "Target Objective", "dreadedEvent": "Dreaded Event", "businessValueAssets": "Business Value & Support Assets", "controlMeasures": "Control Measures", "concernedStakeholders": "Concerned Stakeholders", "category": "Category", "supportAssets": "Support Assets", "responsible": "Responsible", "noSupportAssets": "No associated support assets", "noControlMeasures": "No control measures defined", "noRetainedStakeholders": "No retained stakeholders"}, "empty": {"noScenariosFound": "No strategic scenarios found", "modifySearchCriteria": "Try modifying your search criteria"}, "errors": {"loadingError": "Error loading data. Please try again.", "loadingDataError": "Error loading data", "loadingErrorTitle": "Loading error", "noAttackPathSelected": "No attack path selected. Please select at least one stakeholder for a scenario.", "saveAttackPathsError": "Error saving attack paths"}, "success": {"attackPathsSaved": "Attack paths saved successfully"}, "guide": {"title": "Strategic Scenarios Guide", "context": {"title": "Strategic scenarios context", "description": "Strategic scenarios allow you to identify potential attack paths by associating risk sources, target objectives, dreaded events, business values and concerned stakeholders."}, "understanding": {"title": "Understanding the table", "scenarios": "Strategic Scenarios", "scenariosDesc": "Unique identifier for each scenario (S01, S02, etc.)", "riskSource": "Risk Source", "riskSourceDesc": "The identified risk source with its category", "targetObjective": "Target Objective", "targetObjectiveDesc": "The objective targeted by the risk source", "dreadedEvent": "Dreaded Event", "dreadedEventDesc": "The dreaded events associated with this risk source", "businessValue": "Business Value & Support Assets", "businessValueDesc": "The impacted business values and their support assets", "controlMeasures": "Control Measures", "controlMeasuresDesc": "The control measures identified to treat these risks", "stakeholders": "Concerned Stakeholders", "stakeholdersDesc": "The stakeholders concerned by this scenario"}, "actions": {"title": "Possible actions", "search": "Use the search bar to filter scenarios", "filter": "Filter by category using the dropdown menu", "sort": "Click on column headers to sort data", "selectStakeholders": "Select concerned stakeholders for each scenario"}}}, "activity3": {"breadcrumb": "Attack Paths", "title": "Attack Paths", "subtitle": "Visualize the attack paths identified for your risk analysis.", "description": "Visualize and analyze the attack paths identified for your risk analysis. Each path connects a risk source to a target objective via a dreaded event.", "loading": "Loading attack paths...", "loadingMessage": "Loading attack paths...", "searchPlaceholder": "Search...", "visualizeSelection": "Visualize selection", "visualize": "Visualize", "visualizeAttackPath": "Visualize attack path", "collapse": "Collapse", "details": "Details", "closeVisualization": "Close visualization", "export": {"button": "Export", "csv": "Export CSV", "filename": "attack-paths"}, "table": {"selection": "Selection", "reference": "Reference", "riskSource": "Risk Source", "targetObjective": "Target Objective", "dreadedEvent": "Dreaded Event", "businessValue": "Business Value", "stakeholders": "Stakeholders", "actions": "Actions", "none": "None"}, "empty": {"noAttackPaths": "No attack paths found", "noAttackPathsMessage": "No attack paths have been defined for this analysis. Please return to Activity 2 to define attack paths."}, "errors": {"loadingError": "Error loading data. Please try again.", "loadingAttackPathsError": "Error loading attack paths", "loadingErrorTitle": "Loading error", "selectAttackPath": "Please select an attack path"}, "detailsModal": {"attackPathDetails": "Attack path details", "concernedStakeholder": "Concerned stakeholder", "noAssociatedStakeholder": "No associated stakeholder"}, "visualization": {"title": "Attack Path Visualization: {{sourceRisk}} → {{dreadedEvent}}"}}}, "session": {"warning10Min": "Your session will expire in 10 minutes. Please save your work.", "warning5Min": "Your session will expire in 5 minutes. Please save your work immediately.", "expired": "Session expired. Redirecting to login page..."}, "securityFoundation": {"title": "Security Foundation", "breadcrumb": {"workshop1": "Workshop 1", "securityFoundation": "Security Foundation"}, "buttons": {"save": "Save", "saving": "Saving...", "import": "Import CSV", "print": "Print", "cancel": "Cancel", "add": "Add", "modify": "Modify", "delete": "Delete", "close": "Close", "selectAll": "Select All", "deselectAll": "Deselect All", "visible": "(Visible)", "remove": "Remove", "link": "Link...", "confirm": "Confirm", "modifyRule": "Modify this rule", "deleteRule": "Delete this rule"}, "sections": {"frameworks": "Frameworks", "availableRules": "Available rules", "selectedRules": "Selected rules", "summary": "Summary", "status": "Status", "justification": "Justification", "linkedDreadedEvents": "Linked Dreaded Events", "rule": "Rule"}, "placeholders": {"searchRule": "Search for a rule...", "frameworkName": "Ex: Project Y Requirements", "configure": "Configure...", "optional": "Optional..."}, "status": {"available": "available", "selected": "selected", "rulesFound": "rule(s) found in CSV file", "noFrameworkLoaded": "No framework loaded.", "noRuleFound": "No rule found for this term.", "noRuleDefined": "No rule defined for this framework.", "noRuleSelected": "No rule selected for this framework.", "addRulesFromList": "Add rules from the list above.", "selectFramework": "Select a framework from the left list to start.", "loadingFrameworks": "Loading frameworks...", "noRuleSelectedSummary": "No rules selected to display summary.", "specifyFrameworkName": "Please specify a name for the framework.", "importedOn": "Imported on", "frameworkImported": "Framework \"{{name}}\" imported successfully! ({{count}} rules)", "printAreaNotFound": "Error: Unable to find print area.", "tableNotFound": "Error: Table not found.", "printingInProgress": "Printing security foundation summary...", "loadingData": "Loading security foundation data...", "loadingError": "Loading error:", "saveError": "Save Error:", "noFrameworkDefinitions": "No framework definitions found for this analysis.", "ruleNotFoundForModification": "Error: Rule not found for modification.", "ruleNotFound": "Error: Rule not found.", "confirmDeleteRule": "Are you sure you want to delete the rule \"{{name}}\"? This action is irreversible.", "noAnalysisSelected": "Error: No analysis selected for saving.", "savingSecurityFoundation": "Saving security foundation...", "rulesSelected": "{{count}} rule{{count, plural, one{} other{s}}} selected", "applied": "Applied", "partially": "Partially", "notApplied": "Not Applied", "unknown": "Unknown", "linkedCount": "{{count}} linked - Modify", "noDreadedEventsAvailable": "No dreaded events available.", "noDescriptionAvailable": "No description available.", "undefined": "Undefined", "none": "None", "noLinkedBusinessValue": "No linked business value found.", "noDreadedEventsLinked": "No dreaded events linked."}, "import": {"title": "Import rules from CSV", "frameworkName": "New framework name", "importButton": "Import", "importTooltip": "Import rules from a CSV file"}, "modal": {"ruleDescription": "Rule description", "linkDreadedEvents": "<PERSON> Dreaded Events", "rule": "Rule:"}, "table": {"headers": {"framework": "Framework", "rule": "Rule", "status": "Status", "justification": "Justification", "eventsValuesAssets": "Dreaded Events / Business Values / Support Assets", "actions": "Actions"}}}, "securityControls": {"title": "Security Controls", "breadcrumb": {"workshop1": "Workshop 1", "securityControls": "Security controls"}, "buttons": {"save": "Save", "saving": "Saving...", "print": "Print", "addControls": "Add Controls", "generateAI": "Generate Suggestions", "cancel": "Cancel", "add": "Add", "addSelection": "Add Selection", "close": "Close", "modify": "Modify", "delete": "Delete", "addControlsFromDatabase": "Add controls from database", "suggestControlsWithAI": "Suggest controls with AI"}, "sections": {"riskTreatmentPlan": "Risk Treatment Plan", "linkedRules": "Rules linked to dreaded events", "controls": "Controls", "treatment": "Treatment", "residualSeverity": "Residual severity", "responsible": "Responsible", "decision": "Decision", "date": "Date", "securityRules": "Security Rules", "dreadedEvents": "Dreaded Events", "identifiedControls": "Identified Controls", "control": "Control", "value": "Value"}, "table": {"headers": {"rule": "Rule", "dreadedEvent": "Dreaded event", "severity": "Severity", "controls": "Security controls", "treatment": "Risk treatment", "residualSeverity": "Residual severity", "responsible": "Responsible", "decision": "Implementation decision", "actions": "Actions"}}, "treatment": {"reduce": "Reduce risk", "transfer": "Transfer risk", "avoid": "Avoid risk", "accept": "Accept risk"}, "decision": {"implemented": "Implemented", "partial": "Partial", "planned": "Planned", "apply_before": "To apply before", "abandoned": "To abandon"}, "severity": {"catastrophic": "Catastrophic", "critical": "Critical", "major": "Major", "moderate": "Moderate", "minor": "Minor", "undefined": "Undefined"}, "placeholders": {"selectTreatment": "Select a treatment", "selectSeverity": "Select a severity", "selectDecision": "Select a decision", "responsibleName": "Responsible person name", "selectDate": "Select a date", "who": "Who?"}, "status": {"noLinkedRules": "No rules linked to dreaded events found.", "linkRulesFirst": "Please first link rules to dreaded events in the Security Foundation section.", "loadingControls": "Loading controls...", "noControlsFound": "No controls found in database.", "controlsFiltered": "Controls filtered by:", "pillarOfEvent": "Pillar of dreaded event:", "treatmentStrategy": "Treatment strategy:", "generating": "Generating...", "noSuggestions": "No suggestions generated.", "suggestionsGenerated": "Generated suggestions:", "planSaved": "Treatment plan saved successfully!", "saveError": "Error saving treatment plan.", "rulesDefined": "rules defined.", "eventsIdentified": "events identified.", "controlsIdentified": "controls identified.", "unknownRule": "Unknown rule", "noControlsSelected": "No controls selected/suggested."}, "modal": {"addControls": "Add Security Controls", "aiSuggestions": "Suggest Controls (AI)", "forRule": "For rule:", "linkedToEvent": "Linked to event:", "generateSuggestions": "Generate Suggestions", "error": "Error:"}, "info": {"riskTreatmentPlan": "Configure security controls for each rule linked to identified dreaded events."}}}