// src/components/Atelier 2/Activite3/SelectionCouples.js
import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Save, RefreshCw, AlertCircle, Target, Search, Filter, Link, Check, X, Info, BarChart2, Plus } from 'lucide-react';
import { useAnalysis } from '../../../context/AnalysisContext';
import { showSuccessToast, showErrorToast, showLoadingToast, updateToast } from '../../../utils/toastUtils';

const SelectionCouples = () => {
  // State for couples SR/OV
  const [couples, setCouples] = useState([]);
  const [selectedCouples, setSelectedCouples] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterVraisemblance, setFilterVraisemblance] = useState('');
  const [isSaving, setIsSaving] = useState(false);
  const [viewMode, setViewMode] = useState('list'); // 'list' or 'matrix'

  // Get analysis context
  const {
    currentAnalysis,
    loadCouplesSROV,
    saveSelectedCouplesSROV,
    getSelectedCouplesSROV,
    getSourcesDeRisque,
    loadObjectifsVises,
  } = useAnalysis();

  // State for sources and objectifs
  const [sourcesRisque, setSourcesRisque] = useState([]);
  const [objectifsVises, setObjectifsVises] = useState([]);

  // Load data on component mount
  useEffect(() => {
    const fetchData = async () => {
      if (currentAnalysis?.id) {
        try {
          // Load all couples
          const couplesData = await loadCouplesSROV(currentAnalysis.id);
          if (couplesData) {
            setCouples(couplesData);
          }

          // Load selected couples
          const selectedData = await getSelectedCouplesSROV(currentAnalysis.id);
          if (selectedData) {
            // Check if we're dealing with the new format (mappings with sourceId and dreadedEventId)
            if (selectedData.length > 0 && typeof selectedData[0] === 'object' && selectedData[0].sourceId) {
              // Extract couple IDs from the mappings by matching sourceId with couple sourceId
              const coupleIds = [];

              // For each mapping, find the corresponding couple
              selectedData.forEach(mapping => {
                const matchingCouple = couplesData.find(
                  couple => couple.sourceId === mapping.sourceId
                );

                if (matchingCouple) {
                  coupleIds.push(matchingCouple.id);
                }
              });

              setSelectedCouples(coupleIds);
              console.log('Converted mappings to couple IDs:', coupleIds);
            } else {
              // Old format - direct array of IDs
              setSelectedCouples(selectedData);
            }
          }

          // Load sources de risque
          const sourcesData = await getSourcesDeRisque(currentAnalysis.id);
          if (sourcesData) {
            setSourcesRisque(sourcesData);
          }

          // Load objectifs visés
          const objectifsData = await loadObjectifsVises(currentAnalysis.id);
          if (objectifsData) {
            setObjectifsVises(objectifsData);
          }
        } catch (error) {
          console.error('Error loading data:', error);
          showErrorToast('Erreur lors du chargement des données');
        }
      }
    };

    fetchData();
  }, [currentAnalysis?.id, loadCouplesSROV, getSelectedCouplesSROV, getSourcesDeRisque, loadObjectifsVises]);

  // Handle toggling selection of a couple
  const handleToggleSelection = (coupleId) => {
    const isSelected = selectedCouples.includes(coupleId);

    if (isSelected) {
      // Remove from selection
      setSelectedCouples(selectedCouples.filter(id => id !== coupleId));
    } else {
      // Add to selection
      setSelectedCouples([...selectedCouples, coupleId]);
    }
  };

  // Handle saving selected couples
  const handleSaveSelection = async () => {
    if (!currentAnalysis?.id) {
      showErrorToast('Aucune analyse sélectionnée');
      return;
    }

    setIsSaving(true);
    const toastId = showLoadingToast('Sauvegarde des couples SR/OV sélectionnés...');

    try {
      // Convert selected couple IDs to the new mapping format for compatibility with SourceRiskDreadedEventMapping
      const selectedCouplesData = selectedCouples.map(coupleId => {
        const couple = couples.find(c => c.id === coupleId);
        if (!couple) return null;

        return {
          id: `${couple.sourceId}-${Date.now()}`, // Create a unique ID
          sourceId: couple.sourceId,
          dreadedEventId: null, // This will be filled in later in Activite3
          createdAt: new Date().toISOString()
        };
      }).filter(Boolean); // Remove any null entries

      // Save in both formats for backward compatibility
      await saveSelectedCouplesSROV(currentAnalysis.id, {
        selectedCouples, // Old format
        mappings: selectedCouplesData // New format
      });

      updateToast(toastId, 'Couples SR/OV sélectionnés sauvegardés avec succès', 'success');
    } catch (error) {
      console.error('Error saving selected couples SR/OV:', error);
      updateToast(toastId, 'Erreur lors de la sauvegarde des couples SR/OV sélectionnés', 'error');
    } finally {
      setIsSaving(false);
    }
  };

  // Filter couples based on search term and vraisemblance filter
  const filteredCouples = couples.filter(couple => {
    const source = sourcesRisque.find(s => s.id === couple.sourceId);
    const objectif = objectifsVises.find(o => o.id === couple.objectifId);

    const sourceName = source ? source.name : '';
    const objectifName = objectif ? objectif.name : '';

    const matchesSearch =
      sourceName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      objectifName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      couple.justification.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesVraisemblance = filterVraisemblance ? couple.vraisemblance === filterVraisemblance : true;

    return matchesSearch && matchesVraisemblance;
  });

  // Get color for vraisemblance
  const getVraisemblanceColor = (level) => {
    switch (level) {
      case 'faible': return 'bg-green-100 text-green-800';
      case 'moyenne': return 'bg-yellow-100 text-yellow-800';
      case 'forte': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  // Get source name by ID
  const getSourceName = (id) => {
    const source = sourcesRisque.find(s => s.id === id);
    return source ? source.name : `ID: ${id}`;
  };

  // Get objectif name by ID
  const getObjectifName = (id) => {
    const objectif = objectifsVises.find(o => o.id === id);
    return objectif ? objectif.name : `ID: ${id}`;
  };

  // Get source type by ID
  const getSourceType = (id) => {
    const source = sourcesRisque.find(s => s.id === id);
    return source ? source.type : '';
  };

  // Get objectif type by ID
  const getObjectifType = (id) => {
    const objectif = objectifsVises.find(o => o.id === id);
    return objectif ? objectif.type : '';
  };

  // Get type badge color for source
  const getSourceTypeBadgeColor = (id) => {
    const type = getSourceType(id);
    switch (type) {
      case 'humain': return 'bg-blue-100 text-blue-800';
      case 'technique': return 'bg-purple-100 text-purple-800';
      case 'physique': return 'bg-orange-100 text-orange-800';
      case 'organisationnel': return 'bg-indigo-100 text-indigo-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  // Get type badge color for objectif
  const getObjectifTypeBadgeColor = (id) => {
    const type = getObjectifType(id);
    switch (type) {
      case 'confidentialite': return 'bg-blue-100 text-blue-800';
      case 'integrite': return 'bg-purple-100 text-purple-800';
      case 'disponibilite': return 'bg-orange-100 text-orange-800';
      case 'auditabilite': return 'bg-red-100 text-red-800';
      case 'preuve': return 'bg-indigo-100 text-indigo-800';
      case 'controle': return 'bg-teal-100 text-teal-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  // Prepare data for matrix view
  const matrixData = {
    sources: [...new Set(couples.map(couple => couple.sourceId))].map(id => ({
      id,
      name: getSourceName(id),
      type: getSourceType(id)
    })),
    objectifs: [...new Set(couples.map(couple => couple.objectifId))].map(id => ({
      id,
      name: getObjectifName(id),
      type: getObjectifType(id)
    }))
  };

  // Get couple by source and objectif IDs
  const getCoupleByIds = (sourceId, objectifId) => {
    return couples.find(couple =>
      couple.sourceId === sourceId && couple.objectifId === objectifId
    );
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="bg-white p-6 rounded-lg shadow-sm border border-gray-200"
    >
      {/* Header */}
      <div className="flex justify-between items-center mb-6">
        <div>
          <h2 className="text-2xl font-bold text-gray-800 flex items-center">
            <Link size={24} className="mr-2 text-blue-600" />
            Sélection des Couples SR/OV
          </h2>
          <p className="text-sm text-gray-500 mt-1">
            Sélectionnez les couples SR/OV pertinents pour votre analyse
          </p>
        </div>
        <div className="flex items-center space-x-4">
          {/* View mode toggle */}
          <div className="bg-gray-100 rounded-md p-1 flex">
            <button
              className={`px-3 py-1.5 rounded-md ${viewMode === 'list' ? 'bg-white shadow-sm' : 'text-gray-600'}`}
              onClick={() => setViewMode('list')}
            >
              <div className="flex items-center">
                <AlertCircle size={16} className="mr-1.5" />
                Liste
              </div>
            </button>
            <button
              className={`px-3 py-1.5 rounded-md ${viewMode === 'matrix' ? 'bg-white shadow-sm' : 'text-gray-600'}`}
              onClick={() => setViewMode('matrix')}
            >
              <div className="flex items-center">
                <BarChart2 size={16} className="mr-1.5" />
                Matrice
              </div>
            </button>
          </div>

          {/* Save button */}
          <button
            onClick={handleSaveSelection}
            disabled={isSaving}
            className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 flex items-center disabled:opacity-50"
          >
            {isSaving ? (
              <RefreshCw size={16} className="mr-2 animate-spin" />
            ) : (
              <Save size={16} className="mr-2" />
            )}
            Enregistrer
          </button>
        </div>
      </div>

      {/* Info cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        <div className="bg-blue-50 p-4 rounded-lg border border-blue-100 shadow-sm">
          <div className="flex items-center text-blue-700">
            <AlertCircle size={18} className="mr-2" />
            <h3 className="font-medium">Sources de Risque</h3>
          </div>
          <p className="text-blue-600 text-sm mt-1">
            {sourcesRisque.length} sources de risque identifiées
          </p>
        </div>
        <div className="bg-purple-50 p-4 rounded-lg border border-purple-100 shadow-sm">
          <div className="flex items-center text-purple-700">
            <Target size={18} className="mr-2" />
            <h3 className="font-medium">Objectifs Visés</h3>
          </div>
          <p className="text-purple-600 text-sm mt-1">
            {objectifsVises.length} objectifs visés identifiés
          </p>
        </div>
        <div className="bg-green-50 p-4 rounded-lg border border-green-100 shadow-sm">
          <div className="flex items-center text-green-700">
            <Info size={18} className="mr-2" />
            <h3 className="font-medium">Couples Sélectionnés</h3>
          </div>
          <p className="text-green-600 text-sm mt-1">
            {selectedCouples.length} couples SR/OV sélectionnés sur {couples.length}
          </p>
        </div>
      </div>

      {/* Search and filter - only for list view */}
      {viewMode === 'list' && (
        <div className="flex flex-col md:flex-row gap-4 mb-6">
          <div className="flex-1">
            <div className="relative">
              <input
                type="text"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full p-2 pl-10 border border-gray-300 rounded-md"
                placeholder="Rechercher un couple SR/OV..."
              />
              <Search size={18} className="absolute left-3 top-2.5 text-gray-400" />
            </div>
          </div>
          <div className="w-full md:w-64">
            <select
              value={filterVraisemblance}
              onChange={(e) => setFilterVraisemblance(e.target.value)}
              className="w-full p-2 border border-gray-300 rounded-md"
            >
              <option value="">Toutes les vraisemblances</option>
              <option value="faible">Faible</option>
              <option value="moyenne">Moyenne</option>
              <option value="forte">Forte</option>
            </select>
          </div>
        </div>
      )}

      {/* List view */}
      {viewMode === 'list' && (
        <>
          {filteredCouples.length === 0 ? (
            <div className="text-center py-8 bg-gray-50 rounded-lg border border-gray-200">
              <Link size={48} className="mx-auto text-gray-400 mb-4" />
              <p className="text-gray-600 font-medium">Aucun couple SR/OV trouvé</p>
              <p className="text-gray-500 text-sm mt-1">
                {couples.length > 0
                  ? 'Essayez de modifier vos critères de recherche ou de filtre'
                  : 'Aucun couple SR/OV n\'a été défini dans l\'activité précédente'}
              </p>
            </div>
          ) : (
            <div className="grid grid-cols-1 gap-4">
              {filteredCouples.map((couple) => (
                <div
                  key={couple.id}
                  className={`p-4 rounded-lg border shadow-sm hover:shadow-md transition-shadow ${
                    selectedCouples.includes(couple.id)
                      ? 'bg-blue-50 border-blue-200'
                      : 'bg-white border-gray-200'
                  }`}
                >
                  <div className="flex justify-between items-start">
                    <div className="flex-1">
                      <div className="flex flex-col md:flex-row md:items-center md:space-x-4">
                        <div className="flex-1">
                          <div className="flex items-center">
                            <AlertCircle size={16} className="text-blue-600 mr-2" />
                            <h3 className="text-lg font-semibold text-gray-800">{getSourceName(couple.sourceId)}</h3>
                            <span className={`ml-2 px-2 py-0.5 rounded-full text-xs ${getSourceTypeBadgeColor(couple.sourceId)}`}>
                              {getSourceType(couple.sourceId).charAt(0).toUpperCase() + getSourceType(couple.sourceId).slice(1)}
                            </span>
                          </div>
                        </div>
                        <div className="flex items-center my-2 md:my-0">
                          <div className="w-6 h-0.5 bg-gray-300"></div>
                          <div className="mx-2 p-1 bg-blue-100 rounded-full">
                            <Link size={14} className="text-blue-600" />
                          </div>
                          <div className="w-6 h-0.5 bg-gray-300"></div>
                        </div>
                        <div className="flex-1">
                          <div className="flex items-center">
                            <Target size={16} className="text-blue-600 mr-2" />
                            <h3 className="text-lg font-semibold text-gray-800">{getObjectifName(couple.objectifId)}</h3>
                            <span className={`ml-2 px-2 py-0.5 rounded-full text-xs ${getObjectifTypeBadgeColor(couple.objectifId)}`}>
                              {getObjectifType(couple.objectifId).charAt(0).toUpperCase() + getObjectifType(couple.objectifId).slice(1)}
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div className="ml-4">
                      <button
                        onClick={() => handleToggleSelection(couple.id)}
                        className={`p-2 rounded-full ${
                          selectedCouples.includes(couple.id)
                            ? 'bg-blue-100 text-blue-600 hover:bg-blue-200'
                            : 'bg-gray-100 text-gray-500 hover:bg-gray-200'
                        }`}
                        title={selectedCouples.includes(couple.id) ? 'Désélectionner' : 'Sélectionner'}
                      >
                        {selectedCouples.includes(couple.id) ? (
                          <Check size={20} />
                        ) : (
                          <Plus size={20} />
                        )}
                      </button>
                    </div>
                  </div>
                  <div className="mt-4 grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <p className="text-xs text-gray-500">Vraisemblance</p>
                      <span className={`inline-block px-2 py-0.5 rounded-full text-xs ${getVraisemblanceColor(couple.vraisemblance)}`}>
                        {couple.vraisemblance.charAt(0).toUpperCase() + couple.vraisemblance.slice(1)}
                      </span>
                    </div>
                    <div>
                      <p className="text-xs text-gray-500">Justification</p>
                      <p className="text-sm font-medium">{couple.justification || 'Non spécifiée'}</p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </>
      )}

      {/* Matrix view */}
      {viewMode === 'matrix' && (
        <div className="overflow-x-auto">
          {matrixData.sources.length === 0 || matrixData.objectifs.length === 0 ? (
            <div className="text-center py-8 bg-gray-50 rounded-lg border border-gray-200">
              <BarChart2 size={48} className="mx-auto text-gray-400 mb-4" />
              <p className="text-gray-600 font-medium">Impossible d'afficher la matrice</p>
              <p className="text-gray-500 text-sm mt-1">
                Aucune source de risque ou objectif visé n'a été défini
              </p>
            </div>
          ) : (
            <table className="min-w-full border-collapse">
              <thead>
                <tr>
                  <th className="p-3 border bg-gray-50"></th>
                  {matrixData.objectifs.map(objectif => (
                    <th key={objectif.id} className="p-3 border bg-gray-50 min-w-[150px]">
                      <div className="flex flex-col items-center">
                        <span className={`px-2 py-0.5 rounded-full text-xs mb-1 ${getObjectifTypeBadgeColor(objectif.id)}`}>
                          {objectif.type.charAt(0).toUpperCase() + objectif.type.slice(1)}
                        </span>
                        <span className="font-medium text-sm">{objectif.name}</span>
                      </div>
                    </th>
                  ))}
                </tr>
              </thead>
              <tbody>
                {matrixData.sources.map(source => (
                  <tr key={source.id}>
                    <td className="p-3 border bg-gray-50 min-w-[150px]">
                      <div className="flex flex-col">
                        <span className={`px-2 py-0.5 rounded-full text-xs mb-1 ${getSourceTypeBadgeColor(source.id)}`}>
                          {source.type.charAt(0).toUpperCase() + source.type.slice(1)}
                        </span>
                        <span className="font-medium text-sm">{source.name}</span>
                      </div>
                    </td>
                    {matrixData.objectifs.map(objectif => {
                      const couple = getCoupleByIds(source.id, objectif.id);
                      return (
                        <td key={`${source.id}-${objectif.id}`} className="p-2 border text-center">
                          {couple ? (
                            <button
                              onClick={() => handleToggleSelection(couple.id)}
                              className={`p-3 w-full h-full flex flex-col items-center justify-center rounded ${
                                selectedCouples.includes(couple.id)
                                  ? 'bg-blue-50 hover:bg-blue-100'
                                  : 'bg-gray-50 hover:bg-gray-100'
                              }`}
                            >
                              <span className={`px-2 py-0.5 rounded-full text-xs mb-1 ${getVraisemblanceColor(couple.vraisemblance)}`}>
                                {couple.vraisemblance.charAt(0).toUpperCase() + couple.vraisemblance.slice(1)}
                              </span>
                              {selectedCouples.includes(couple.id) ? (
                                <Check size={18} className="text-blue-600" />
                              ) : (
                                <Plus size={18} className="text-gray-400" />
                              )}
                            </button>
                          ) : (
                            <span className="text-gray-300">-</span>
                          )}
                        </td>
                      );
                    })}
                  </tr>
                ))}
              </tbody>
            </table>
          )}
        </div>
      )}
    </motion.div>
  );
};

export default SelectionCouples;
