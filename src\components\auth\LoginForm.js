// src/components/auth/LoginForm.js - Updated for role-based redirects
import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '../../context/AuthContext';

const LoginForm = () => {
  // Form state
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');

  // Error and loading states
  const [formError, setFormError] = useState(''); // General form error from context or validation
  const [inputErrors, setInputErrors] = useState({}); // Specific input field errors
  const [showPassword, setShowPassword] = useState(false);

  // Auth context values
  const { login, isLoading, error: authError, isAuthenticated, user } = useAuth();

  // Navigation hooks
  const navigate = useNavigate();
  const location = useLocation();

  // Effect to redirect if already authenticated
  useEffect(() => {
    if (isAuthenticated && user) {
      // Redirect to dashboard or the intended page before login
      const from = location.state?.from?.pathname || '/dashboard';
      navigate(from, { replace: true });
    }
  }, [isAuthenticated, user, navigate, location]);

  // Effect to update formError when authError changes
  useEffect(() => {
    if (authError) {
      setFormError(authError); // Display error from auth context
    } else {
      setFormError(''); // Clear error if auth context has no error
    }
  }, [authError]);

  // Function to validate form inputs
  const validateForm = () => {
    const errors = {};
    let hasErrors = false;

    if (!email.trim()) {
      errors.email = "L'email est requis";
      hasErrors = true;
    } else if (!/\S+@\S+\.\S+/.test(email)) {
      errors.email = "L'adresse email est invalide";
      hasErrors = true;
    }

    if (!password) {
      errors.password = 'Le mot de passe est requis';
      hasErrors = true;
    }

    setInputErrors(errors);
    return !hasErrors; // Return true if valid, false otherwise
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();
    setFormError(''); // Reset general form error on new submission attempt
    setInputErrors({}); // Reset input errors

    if (!validateForm()) {
      setFormError("Veuillez corriger les erreurs dans le formulaire.");
      return; // Stop submission if validation fails
    }

    try {
      await login(email, password);
      // Successful login is handled by the useEffect hook for redirection
    } catch (err) {
      // Error is caught and set in the AuthContext,
      // which updates `authError` and triggers the useEffect above.
      // We might set a generic message here if needed, but usually context handles it.
      console.error('Login attempt failed:', err); // Log for debugging
      // setFormError('La connexion a échoué. Veuillez réessayer.'); // Optional generic message
    }
  };

  // Toggle password visibility
  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-100 via-purple-100 to-pink-100 p-4">
      <div className="w-full max-w-md bg-white rounded-xl shadow-2xl overflow-hidden">
        {/* Header with Logos */}
        <div className="bg-gradient-to-r from-blue-600 to-purple-600 p-6">
          <div className="flex justify-between items-center mb-6">
            {/* ACG Logo - Left */}
            <div className="overflow-hidden rounded-xl shadow-md bg-white p-1.5 transition-transform duration-300 hover:scale-105">
              <img
                src="/acg-logo.png"
                alt="ACG Logo"
                className="h-14 object-contain"
              />
            </div>

            {/* EBIOS RM Logo - Middle */}
            <div className="overflow-hidden rounded-xl shadow-md bg-white p-1.5 transition-transform duration-300 hover:scale-105 mx-auto">
              <img
                src="/ebios-rm-logo.png"
                alt="EBIOS Risk Manager Logo"
                className="h-14 object-contain"
              />
            </div>

            {/* ANSSI Logo - Right */}
            <div className="overflow-hidden rounded-xl shadow-md bg-white p-1.5 transition-transform duration-300 hover:scale-105">
              <img
                src="/anssi-logo.png"
                alt="ANSSI Logo"
                className="h-14 object-contain"
              />
            </div>
          </div>

          <h2 className="text-2xl font-bold text-center text-white mb-1">
            EBIOS RM - Analyse des risques
          </h2>
          <p className="text-center text-blue-100 text-sm">
            Plateforme de gestion des risques selon la méthode EBIOS
          </p>
        </div>

        {/* Login Form */}
        <div className="p-8">
          <h3 className="text-xl font-semibold text-gray-800 mb-6 text-center">
            Connexion à votre compte
          </h3>

          {/* Display General Form Error */}
          {(formError) && (
            <div className="p-3 mb-6 bg-red-50 border border-red-200 text-red-700 rounded-md text-sm">
              {formError}
            </div>
          )}

          <form onSubmit={handleSubmit} noValidate>
            <div className="space-y-4">
              {/* Email Input */}
              <div>
                <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
                  Adresse email
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor">
                      <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z" />
                      <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z" />
                    </svg>
                  </div>
                  <input
                    type="email"
                    id="email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    disabled={isLoading}
                    autoComplete="username"
                    className={`appearance-none block w-full pl-10 pr-3 py-2 border ${inputErrors.email ? 'border-red-300' : 'border-gray-300'} rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm`}
                    placeholder="<EMAIL>"
                    aria-invalid={!!inputErrors.email}
                    aria-describedby={inputErrors.email ? "email-error" : undefined}
                  />
                </div>
                {inputErrors.email && (
                  <p id="email-error" className="mt-1 text-sm text-red-600">
                    {inputErrors.email}
                  </p>
                )}
              </div>

              {/* Password Input */}
              <div>
                <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-1">
                  Mot de passe
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <input
                    type={showPassword ? "text" : "password"}
                    id="password"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    disabled={isLoading}
                    autoComplete="current-password"
                    className={`appearance-none block w-full pl-10 pr-10 py-2 border ${inputErrors.password ? 'border-red-300' : 'border-gray-300'} rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm`}
                    placeholder="••••••••"
                    aria-invalid={!!inputErrors.password}
                    aria-describedby={inputErrors.password ? "password-error" : undefined}
                  />
                  <button
                    type="button"
                    className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-600 hover:text-gray-800"
                    onClick={togglePasswordVisibility}
                    aria-label={showPassword ? "Hide password" : "Show password"}
                  >
                    {showPassword ? (
                      <svg className="h-5 w-5" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M3 3L21 21M10.584 10.587C10.2087 10.962 9.99778 11.4716 9.99778 12.0013C9.99778 12.531 10.2087 13.0406 10.584 13.4156C10.959 13.7906 11.4686 14.0015 11.9983 14.0015C12.528 14.0015 13.0376 13.7906 13.4126 13.4156M9.00002 4.99998C10.0358 4.37778 11.2014 4.0004 12.395 3.99998C16.983 3.99998 20.895 7.04998 23 12C22.275 13.373 21.334 14.603 20.217 15.621M14.572 18.485C13.8786 18.8222 13.1344 19.0006 12.38 19C7.79202 19 3.88002 15.95 1.77502 11C2.77502 8.87 4.24602 7.12 6.00002 5.84998L14.572 18.485Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                      </svg>
                    ) : (
                      <svg className="h-5 w-5" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M14.122 9.87999C15.293 11.051 15.293 12.952 14.122 14.125C12.951 15.296 11.05 15.296 9.877 14.125C8.706 12.954 8.706 11.053 9.877 9.87999C11.048 8.70899 12.95 8.70899 14.122 9.87999Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                        <path d="M12 5C17.108 5 21.247 8.33301 23 12C21.247 15.667 17.108 19 12 19C6.892 19 2.753 15.667 1 12C2.753 8.33301 6.892 5 12 5Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                      </svg>
                    )}
                  </button>
                </div>
                {inputErrors.password && (
                  <p id="password-error" className="mt-1 text-sm text-red-600">
                    {inputErrors.password}
                  </p>
                )}
              </div>
            </div>

            {/* Submit Button */}
            <div className="mt-6">
              <button
                type="submit"
                disabled={isLoading}
                className={`w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white transition duration-150 ease-in-out ${isLoading ? 'bg-blue-400 cursor-not-allowed' : 'bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500'}`}
              >
                {isLoading ? (
                  <>
                    <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Connexion en cours...
                  </>
                ) : 'Se connecter'}
              </button>
            </div>
          </form>

          {/* Footer */}
          <div className="mt-6 text-center">
            <p className="text-xs text-gray-500">
              © {new Date().getFullYear()} EBIOS RM - Tous droits réservés
            </p>
            <p className="text-xs text-gray-400 mt-1">
              EBIOS Risk Manager - Système de gestion des risques
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LoginForm;
