// src/components/Atelier4/AttackGraphs.js
import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Route, Info, BookOpen, Eye, Edit, Plus, Search, DoorOpen, Target, Zap } from 'lucide-react';
import AttackGraphVisualization from './Activite1/AttackGraphVisualization';
import AttackPathTable from './Activite1/AttackPathTable';
import AttackSequenceForm from './Activite1/AttackSequenceForm';
import { useAnalysis } from '../../context/AnalysisContext';

const AttackGraphs = () => {
  const [attackPaths, setAttackPaths] = useState([]);
  const [selectedPath, setSelectedPath] = useState(null);
  const [showForm, setShowForm] = useState(false);
  const [loading, setLoading] = useState(false);
  const [viewMode, setViewMode] = useState('table'); // 'table' or 'graph'
  const { currentAnalysis } = useAnalysis();

  // Attack phases for EBIOS RM
  const attackPhases = [
    {
      id: 'connaitre',
      name: 'CONNAITRE',
      description: 'Reconnaissance et découverte externes',
      color: 'bg-blue-500',
      icon: Search,
      examples: ['OSINT', 'Reconnaissance DNS', 'Social Engineering']
    },
    {
      id: 'rentrer',
      name: 'RENTRER',
      description: 'Intrusion dans le système',
      color: 'bg-orange-500',
      icon: DoorOpen,
      examples: ['Phishing', 'Exploitation vulnérabilités', 'Accès physique']
    },
    {
      id: 'trouver',
      name: 'TROUVER',
      description: 'Reconnaissance interne et latéralisation',
      color: 'bg-purple-500',
      icon: Target,
      examples: ['Scan réseau interne', 'Élévation privilèges', 'Mouvement latéral']
    },
    {
      id: 'exploiter',
      name: 'EXPLOITER',
      description: 'Exploitation des données et biens supports',
      color: 'bg-red-500',
      icon: Zap,
      examples: ['Exfiltration données', 'Chiffrement ransomware', 'Destruction']
    }
  ];

  // Load attack paths data
  useEffect(() => {
    const loadAttackPaths = async () => {
      if (currentAnalysis?.id) {
        setLoading(true);
        try {
          console.log('[AttackGraphs] Loading attack paths for analysis:', currentAnalysis.id);

          // Mock attack paths with detailed steps
          const mockAttackPaths = [
            {
              id: 'path1',
              title: 'Vol de données personnelles',
              referenceCode: 'CA01',
              sourceRiskName: 'Concurrent malveillant',
              targetObjective: 'Données clients',
              dreadedEventName: 'Vol de données personnelles',
              description: 'Attaque ciblée pour voler les données clients',
              steps: [
                {
                  id: 'step1',
                  phase: 'connaitre',
                  name: 'Reconnaissance OSINT',
                  description: 'Collecte d\'informations via sources ouvertes',
                  techniques: ['T1589', 'T1590'],
                  duration: '2-5 jours'
                },
                {
                  id: 'step2',
                  phase: 'rentrer',
                  name: 'Phishing ciblé',
                  description: 'Envoi d\'emails de phishing personnalisés',
                  techniques: ['T1566.001'],
                  duration: '1-2 jours'
                },
                {
                  id: 'step3',
                  phase: 'trouver',
                  name: 'Élévation privilèges',
                  description: 'Exploitation de vulnérabilités locales',
                  techniques: ['T1068', 'T1055'],
                  duration: '1-3 jours'
                },
                {
                  id: 'step4',
                  phase: 'exploiter',
                  name: 'Exfiltration données',
                  description: 'Vol et exfiltration des données clients',
                  techniques: ['T1041', 'T1020'],
                  duration: '1 jour'
                }
              ],
              likelihood: 'Moyenne',
              impact: 'Élevé',
              riskLevel: 'Élevé'
            }
          ];

          setAttackPaths(mockAttackPaths);
        } catch (error) {
          console.error('[AttackGraphs] Error loading attack paths:', error);
          setAttackPaths([]);
        } finally {
          setLoading(false);
        }
      }
    };

    loadAttackPaths();
  }, [currentAnalysis?.id]);

  // Handle path selection
  const handlePathSelect = (path) => {
    setSelectedPath(path);
    setViewMode('graph');
  };

  // Handle path editing
  const handleEditPath = (path) => {
    setSelectedPath(path);
    setShowForm(true);
  };

  // Handle path deletion
  const handleDeletePath = (pathId) => {
    setAttackPaths(prev => prev.filter(p => p.id !== pathId));
    if (selectedPath?.id === pathId) {
      setSelectedPath(null);
    }
  };

  // Handle form submission
  const handleFormSubmit = (pathData) => {
    if (selectedPath) {
      // Update existing path
      setAttackPaths(prev => prev.map(p =>
        p.id === selectedPath.id ? { ...p, ...pathData } : p
      ));
    } else {
      // Add new path
      const newPath = {
        id: `path_${Date.now()}`,
        ...pathData
      };
      setAttackPaths(prev => [...prev, newPath]);
    }
    setShowForm(false);
    setSelectedPath(null);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-100">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-800 mb-2 flex items-center">
              <Route size={28} className="mr-3 text-purple-600" />
              Phase 3: Graphes d'attaque
            </h1>
            <p className="text-gray-600">
              Visualisation et édition interactive des chemins d'attaque opérationnels.
            </p>
          </div>
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2 text-sm text-gray-500">
              <Info size={16} />
              <span>Édition interactive</span>
            </div>
            <button
              onClick={() => setShowForm(true)}
              className="flex items-center px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
            >
              <Plus size={16} className="mr-2" />
              Nouveau chemin
            </button>
          </div>
        </div>

        {/* View Mode Toggle */}
        <div className="flex items-center space-x-2 mb-4">
          <button
            onClick={() => setViewMode('table')}
            className={`flex items-center px-3 py-2 rounded-lg text-sm transition-colors ${
              viewMode === 'table'
                ? 'bg-purple-100 text-purple-700 font-medium'
                : 'text-gray-600 hover:bg-gray-100'
            }`}
          >
            <Eye size={16} className="mr-2" />
            Vue tableau
          </button>
          <button
            onClick={() => setViewMode('graph')}
            className={`flex items-center px-3 py-2 rounded-lg text-sm transition-colors ${
              viewMode === 'graph'
                ? 'bg-purple-100 text-purple-700 font-medium'
                : 'text-gray-600 hover:bg-gray-100'
            }`}
            disabled={!selectedPath}
          >
            <Route size={16} className="mr-2" />
            Vue graphique
          </button>
        </div>

        {/* Attack Phases Overview */}
        <div className="bg-purple-50 p-4 rounded-lg border border-purple-200">
          <h3 className="font-medium text-purple-800 mb-3 flex items-center">
            <BookOpen size={16} className="mr-2" />
            Phases d'attaque EBIOS RM
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {attackPhases.map((phase, index) => (
              <div key={phase.id} className="bg-white p-3 rounded border border-purple-100">
                <div className="flex items-center mb-2">
                  <div className={`${phase.color} w-3 h-3 rounded-full mr-2`}></div>
                  <div className="font-medium text-gray-700">{phase.name}</div>
                </div>
                <div className="text-xs text-gray-600 mb-2">{phase.description}</div>
                <div className="space-y-1">
                  {phase.examples.map((example, idx) => (
                    <div key={idx} className="text-xs text-gray-500 bg-gray-50 px-2 py-1 rounded">
                      {example}
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Content based on view mode */}
      {viewMode === 'table' ? (
        <AttackPathTable
          attackPaths={attackPaths}
          onSelect={handlePathSelect}
          onEdit={handleEditPath}
          onDelete={handleDeletePath}
          loading={loading}
        />
      ) : (
        selectedPath && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
          >
            <AttackGraphVisualization
              attackPath={selectedPath}
              phases={attackPhases}
            />
          </motion.div>
        )
      )}

      {/* Attack Sequence Form Modal */}
      {showForm && (
        <AttackSequenceForm
          path={selectedPath}
          phases={attackPhases}
          onSave={handleFormSubmit}
          onCancel={() => {
            setShowForm(false);
            setSelectedPath(null);
          }}
        />
      )}

      {/* Usage Guide */}
      <div className="bg-gray-50 p-6 rounded-xl border border-gray-200">
        <h3 className="font-medium text-gray-800 mb-3">Guide d'utilisation</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-600">
          <div className="space-y-2">
            <div className="font-medium text-gray-700">Vue tableau</div>
            <div>• Visualisez tous les chemins d'attaque</div>
            <div>• Éditez les propriétés des chemins</div>
            <div>• Gérez les chemins existants</div>
          </div>
          <div className="space-y-2">
            <div className="font-medium text-gray-700">Vue graphique</div>
            <div>• Visualisation interactive des étapes</div>
            <div>• Édition des connexions</div>
            <div>• Analyse des flux d'attaque</div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AttackGraphs;
