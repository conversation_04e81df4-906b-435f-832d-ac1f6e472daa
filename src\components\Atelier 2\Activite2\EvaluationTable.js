// src/components/Atelier 2/Activite2/EvaluationTable.js
import React, { useState, useEffect } from 'react';
import { Edit, Check, X, Filter, Search, ArrowUp, ArrowDown, AlertTriangle } from 'lucide-react';
import { useTranslation } from 'react-i18next';

const EvaluationTable = ({ couples, updateCouple, toggleSelection, hasUnsavedChanges, onSave }) => {
  const { t } = useTranslation();

  // Add sorting functionality
  const [sortField, setSortField] = useState('sourceName');
  const [sortDirection, setSortDirection] = useState('asc');
  const [highlightedRows, setHighlightedRows] = useState([]);
  const [editingJustificationId, setEditingJustificationId] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterCategory, setFilterCategory] = useState('');
  const [editingCoupleId, setEditingCoupleId] = useState(null);

  // Get unique categories for filtering
  const categories = [...new Set(couples.map(couple => couple.sourceCategory))];

  // Highlight high-risk couples
  useEffect(() => {
    const highRiskCouples = couples
      .filter(couple => couple.niveau >= 3)
      .map(couple => couple.id);

    setHighlightedRows(highRiskCouples);
  }, [couples]);

  // Handle sorting
  const handleSort = (field) => {
    if (sortField === field) {
      // Toggle direction if same field
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      // New field, default to ascending
      setSortField(field);
      setSortDirection('asc');
    }
  };

  // Filter and sort couples
  const filteredAndSortedCouples = couples
    .filter(couple => {
      const matchesSearch =
        (couple.sourceName?.toLowerCase() || '').includes(searchTerm.toLowerCase()) ||
        (couple.objectifVise?.toLowerCase() || '').includes(searchTerm.toLowerCase());

      const matchesCategory = filterCategory ? couple.sourceCategory === filterCategory : true;

      return matchesSearch && matchesCategory;
    })
    .sort((a, b) => {
      // Handle null or undefined values
      const aValue = a[sortField] || '';
      const bValue = b[sortField] || '';

      // Compare based on direction
      if (sortDirection === 'asc') {
        return aValue.toString().localeCompare(bValue.toString());
      } else {
        return bValue.toString().localeCompare(aValue.toString());
      }
    });

  // Handle starting edit mode for a couple
  const handleStartEdit = (coupleId) => {
    setEditingCoupleId(coupleId);
    setEditingJustificationId(null); // Close justification editing if open
  };

  // Handle canceling edit mode
  const handleCancelEdit = () => {
    setEditingCoupleId(null);
  };

  // Handle starting justification edit mode (double-click)
  const handleStartJustificationEdit = (coupleId) => {
    setEditingJustificationId(coupleId);
    setEditingCoupleId(null); // Close couple editing if open
  };

  // Handle double-click on justification
  const handleJustificationDoubleClick = (e, coupleId) => {
    e.preventDefault();
    e.stopPropagation();
    handleStartJustificationEdit(coupleId);
  };

  // Handle saving justification
  const handleSaveJustification = (coupleId, justification) => {
    updateCouple(coupleId, 'justification', justification);
    setEditingJustificationId(null);
  };

  // Get display value for motivation/activite/ressources
  const getDisplayValue = (value) => {
    if (!value) return '-';

    const normalizedValue = String(value).toLowerCase();

    // Motivation et activité
    if (normalizedValue.startsWith('faible')) return 'Faible';
    if (normalizedValue.startsWith('moyen')) return 'Moyen';
    if (normalizedValue.startsWith('elev')) return 'Élevé';

    // Ressources
    if (normalizedValue.startsWith('import')) return 'Importantes';
    if (normalizedValue === 'moyennes') return 'Moyennes';
    if (normalizedValue === 'faibles') return 'Faibles';

    return value;
  };

  // Get display value for niveau (1-4)
  const getNiveauDisplay = (niveau) => {
    return niveau;
  };

  // Get color class for niveau
  const getNiveauColorClass = (niveau) => {
    if (niveau === 1) return 'bg-green-100 text-green-800';
    if (niveau === 2) return 'bg-yellow-100 text-yellow-800';
    if (niveau === 3) return 'bg-orange-100 text-orange-800';
    if (niveau === 4) return 'bg-red-100 text-red-800';
    return 'bg-gray-100 text-gray-800';
  };

  // Render sort indicator
  const renderSortIndicator = (field) => {
    if (sortField !== field) return null;

    return sortDirection === 'asc'
      ? <ArrowUp size={14} className="ml-1 inline" />
      : <ArrowDown size={14} className="ml-1 inline" />;
  };

  return (
    <div className="space-y-4">
      {/* Filters and Actions */}
      <div className="flex flex-col sm:flex-row justify-between gap-4 mb-4">
        <div className="flex flex-col sm:flex-row gap-2 sm:items-center">
          {/* Search */}
          <div className="relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <Search size={16} className="text-gray-400" />
            </div>
            <input
              type="text"
              placeholder={t('workshop2.activity2.table.search')}
              className="pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500 block w-full sm:w-64 transition-shadow duration-200 shadow-sm hover:shadow"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>

          {/* Category Filter */}
          <div className="relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <Filter size={16} className="text-gray-400" />
            </div>
            <select
              className="pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500 block w-full sm:w-48 transition-shadow duration-200 shadow-sm hover:shadow appearance-none bg-white"
              value={filterCategory}
              onChange={(e) => setFilterCategory(e.target.value)}
            >
              <option value="">{t('workshop2.activity2.table.allCategories')}</option>
              {categories.map((category) => (
                <option key={category} value={category}>
                  {category}
                </option>
              ))}
            </select>
          </div>
        </div>

        {/* Stats Summary */}
        <div className="flex items-center gap-3 text-sm">
          <div className="px-3 py-1 bg-blue-50 text-blue-700 rounded-md">
            {t('workshop2.activity2.table.total')}: {couples.length}
          </div>
          <div className="px-3 py-1 bg-red-50 text-red-700 rounded-md">
            {t('workshop2.activity2.table.retained')}: {couples.filter(c => c.selected).length}
          </div>
          {highlightedRows.length > 0 && (
            <div className="px-3 py-1 bg-red-50 text-red-700 rounded-md flex items-center">
              <AlertTriangle size={14} className="mr-1" />
              {t('workshop2.activity2.table.highLevel')}: {highlightedRows.length}
            </div>
          )}
        </div>
      </div>

      {/* Table */}
      <div className="overflow-x-auto rounded-lg border border-gray-200 shadow-sm">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gradient-to-r from-gray-50 to-gray-100">
            <tr>
              <th
                scope="col"
                className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-200 transition-colors"
                onClick={() => handleSort('sourceName')}
              >
                {t('workshop2.activity2.table.headers.riskSource')} {renderSortIndicator('sourceName')}
              </th>
              <th
                scope="col"
                className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-200 transition-colors"
                onClick={() => handleSort('sourceCategory')}
              >
                {t('workshop2.activity2.table.headers.category')} {renderSortIndicator('sourceCategory')}
              </th>
              <th
                scope="col"
                className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-200 transition-colors"
                onClick={() => handleSort('objectifVise')}
              >
                {t('workshop2.activity2.table.headers.targetObjective')} {renderSortIndicator('objectifVise')}
              </th>
              <th
                scope="col"
                className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-200 transition-colors"
                onClick={() => handleSort('ressources')}
              >
                {t('workshop2.activity2.table.headers.resources')} {renderSortIndicator('ressources')}
              </th>
              <th
                scope="col"
                className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-200 transition-colors"
                onClick={() => handleSort('activite')}
              >
                {t('workshop2.activity2.table.headers.activity')} {renderSortIndicator('activite')}
              </th>
              <th
                scope="col"
                className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-200 transition-colors"
                onClick={() => handleSort('motivation')}
              >
                {t('workshop2.activity2.table.headers.motivation')} {renderSortIndicator('motivation')}
              </th>
              <th
                scope="col"
                className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-200 transition-colors"
                onClick={() => handleSort('niveau')}
              >
                {t('workshop2.activity2.table.headers.level')} {renderSortIndicator('niveau')}
              </th>
              <th
                scope="col"
                className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-200 transition-colors"
                onClick={() => handleSort('selected')}
              >
                {t('workshop2.activity2.table.headers.selected')} {renderSortIndicator('selected')}
              </th>
              <th
                scope="col"
                className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
              >
                {t('workshop2.activity2.table.headers.justification')}
              </th>
              <th scope="col" className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-100">
            {filteredAndSortedCouples.map((couple) => {
              const isHighRisk = highlightedRows.includes(couple.id);
              const rowClass = couple.selected
                ? 'bg-red-50 hover:bg-red-100'
                : isHighRisk
                  ? 'bg-orange-50 hover:bg-orange-100'
                  : 'hover:bg-gray-50';

              return (
                <tr key={couple.id} className={`transition-colors ${rowClass}`}>
                  {/* Source de risque */}
                  <td className="px-4 py-3 whitespace-normal">
                    <div className="text-sm font-medium text-gray-900">{couple.sourceName}</div>
                  </td>

                  {/* Catégorie */}
                  <td className="px-4 py-3 whitespace-nowrap">
                    <span className="px-2 py-1 inline-flex text-xs leading-5 font-medium rounded-full bg-gray-100 text-gray-800">
                      {couple.sourceCategory}
                    </span>
                  </td>

                  {/* Objectif visé */}
                  <td className="px-4 py-3 whitespace-normal">
                    <div className="text-sm text-gray-900">{couple.objectifVise}</div>
                    {couple.objectifViseCategory && (
                      <div className="text-xs text-gray-500 mt-1 px-2 py-0.5 bg-gray-100 rounded-full inline-block">
                        {couple.objectifViseCategory}
                      </div>
                    )}
                  </td>

                  {/* Ressources */}
                  <td className="px-4 py-3 whitespace-nowrap">
                    {editingCoupleId === couple.id ? (
                      <select
                        className="block w-full py-1 px-2 text-sm border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                        value={couple.ressources}
                        onChange={(e) => updateCouple(couple.id, 'ressources', e.target.value)}
                      >
                        <option value="faibles">{t('workshop2.activity1.levelLabels.faibles')}</option>
                        <option value="moyennes">{t('workshop2.activity1.levelLabels.moyennes')}</option>
                        <option value="importantes">{t('workshop2.activity1.levelLabels.importantes')}</option>
                      </select>
                    ) : (
                      <div className={`text-sm px-2 py-1 rounded ${
                        couple.ressources === 'importantes'
                          ? 'bg-red-100 text-red-800'
                          : couple.ressources === 'moyennes'
                            ? 'bg-yellow-100 text-yellow-800'
                            : 'bg-green-100 text-green-800'
                      }`}>
                        {getDisplayValue(couple.ressources)}
                      </div>
                    )}
                  </td>

                  {/* Activité */}
                  <td className="px-4 py-3 whitespace-nowrap">
                    {editingCoupleId === couple.id ? (
                      <select
                        className="block w-full py-1 px-2 text-sm border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                        value={couple.activite}
                        onChange={(e) => updateCouple(couple.id, 'activite', e.target.value)}
                      >
                        <option value="faible">{t('workshop2.activity1.levelLabels.faible')}</option>
                        <option value="moyen">{t('workshop2.activity1.levelLabels.moyen')}</option>
                        <option value="eleve">{t('workshop2.activity1.levelLabels.eleve')}</option>
                      </select>
                    ) : (
                      <div className={`text-sm px-2 py-1 rounded ${
                        couple.activite === 'eleve'
                          ? 'bg-red-100 text-red-800'
                          : couple.activite === 'moyen'
                            ? 'bg-yellow-100 text-yellow-800'
                            : 'bg-green-100 text-green-800'
                      }`}>
                        {getDisplayValue(couple.activite)}
                      </div>
                    )}
                  </td>

                  {/* Motivation */}
                  <td className="px-4 py-3 whitespace-nowrap">
                    {editingCoupleId === couple.id ? (
                      <select
                        className="block w-full py-1 px-2 text-sm border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                        value={couple.motivation}
                        onChange={(e) => updateCouple(couple.id, 'motivation', e.target.value)}
                      >
                        <option value="faible">{t('workshop2.activity1.levelLabels.faible')}</option>
                        <option value="moyen">{t('workshop2.activity1.levelLabels.moyen')}</option>
                        <option value="eleve">{t('workshop2.activity1.levelLabels.eleve')}</option>
                      </select>
                    ) : (
                      <div className={`text-sm px-2 py-1 rounded ${
                        couple.motivation === 'eleve'
                          ? 'bg-red-100 text-red-800'
                          : couple.motivation === 'moyen'
                            ? 'bg-yellow-100 text-yellow-800'
                            : 'bg-green-100 text-green-800'
                      }`}>
                        {getDisplayValue(couple.motivation)}
                      </div>
                    )}
                  </td>

                  {/* Niveau */}
                  <td className="px-4 py-3 whitespace-nowrap text-center">
                    <span className={`px-3 py-1.5 inline-flex text-sm leading-5 font-bold rounded-full shadow-sm ${getNiveauColorClass(couple.niveau)}`}>
                      {getNiveauDisplay(couple.niveau)}
                    </span>
                  </td>

                  {/* Retenu */}
                  <td className="px-4 py-3 whitespace-nowrap text-center">
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        checked={couple.selected}
                        onChange={() => toggleSelection(couple.id)}
                        className="sr-only peer"
                      />
                      <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-red-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-red-600"></div>
                    </label>
                  </td>

                  {/* Justification */}
                  <td className="px-4 py-3 whitespace-normal">
                    {editingJustificationId === couple.id ? (
                      <div className="flex flex-col space-y-2">
                        <textarea
                          className="w-full p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500 text-sm"
                          rows="3"
                          defaultValue={couple.justification || ''}
                          placeholder={t('workshop2.activity2.table.enterJustification')}
                          id={`justification-${couple.id}`}
                        />
                        <div className="flex justify-end space-x-2">
                          <button
                            onClick={() => setEditingJustificationId(null)}
                            className="text-gray-600 hover:text-gray-900 p-1 rounded-full hover:bg-gray-100 transition-colors"
                            title={t('workshop2.activity2.table.cancel')}
                          >
                            <X size={16} />
                          </button>
                          <button
                            onClick={() => handleSaveJustification(
                              couple.id,
                              document.getElementById(`justification-${couple.id}`).value
                            )}
                            className="text-green-600 hover:text-green-900 p-1 rounded-full hover:bg-green-100 transition-colors"
                            title={t('workshop2.activity2.table.save')}
                          >
                            <Check size={16} />
                          </button>
                        </div>
                      </div>
                    ) : (
                      <div
                        className="text-sm text-gray-700 cursor-pointer hover:bg-gray-50 p-2 rounded"
                        onClick={() => handleStartJustificationEdit(couple.id)}
                        title={t('workshop2.activity2.table.clickToEdit')}
                      >
                        {couple.justification ? (
                          <div>{couple.justification}</div>
                        ) : (
                          couple.selected
                            ? <span className="text-gray-400 italic">{t('workshop2.activity2.table.noJustification')}</span>
                            : <span className="text-gray-400 italic">{t('workshop2.activity2.table.notRetained')}</span>
                        )}
                      </div>
                    )}
                  </td>

                  {/* Actions */}
                  <td className="px-4 py-3 whitespace-nowrap text-right text-sm font-medium">
                    {editingCoupleId === couple.id ? (
                      <div className="flex space-x-2 justify-end">
                        <button
                          onClick={handleCancelEdit}
                          className="text-gray-600 hover:text-gray-900 p-1.5 rounded-full hover:bg-gray-100 transition-colors"
                          title={t('workshop2.activity2.table.cancel')}
                        >
                          <X size={18} />
                        </button>
                        <button
                          onClick={() => setEditingCoupleId(null)}
                          className="text-green-600 hover:text-green-900 p-1.5 rounded-full hover:bg-green-100 transition-colors"
                          title={t('workshop2.activity2.table.confirm')}
                        >
                          <Check size={18} />
                        </button>
                      </div>
                    ) : (
                      <button
                        onClick={() => handleStartEdit(couple.id)}
                        className="text-indigo-600 hover:text-indigo-900 p-1.5 rounded-full hover:bg-indigo-100 transition-colors"
                        title={t('workshop2.activity2.table.edit')}
                      >
                        <Edit size={18} />
                      </button>
                    )}
                  </td>
                </tr>
              );
            })}
          </tbody>
        </table>
      </div>

      {/* No results message */}
      {filteredAndSortedCouples.length === 0 && (
        <div className="text-center py-8 bg-gray-50 rounded-lg border border-gray-200">
          <div className="text-gray-500 mb-2">{t('workshop2.activity2.table.noResults')}</div>
          <button
            className="text-blue-600 hover:text-blue-800 text-sm font-medium"
            onClick={() => {
              setSearchTerm('');
              setFilterCategory('');
            }}
          >
            {t('workshop2.activity2.table.resetFilters')}
          </button>
        </div>
      )}
    </div>
  );
};

export default EvaluationTable;
