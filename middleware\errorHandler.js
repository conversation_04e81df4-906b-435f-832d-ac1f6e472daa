// backend/middleware/errorHandler.js

/**
 * Custom error handler middleware
 */
const errorHandler = (err, req, res, next) => {
    console.error('Error:', err);
    
    // Default error response
    let statusCode = res.statusCode === 200 ? 500 : res.statusCode;
    let message = err.message || 'Erreur serveur';
    let data = null;
    
    // Handle Mongoose validation error
    if (err.name === 'ValidationError') {
      statusCode = 400;
      
      const errors = {};
      for (const field in err.errors) {
        errors[field] = err.errors[field].message;
      }
      
      message = 'Erreur de validation';
      data = { errors };
    }
    
    // Handle Mongoose cast error (invalid ID)
    if (err.name === 'CastError') {
      statusCode = 400;
      message = `Invalid ${err.path}: ${err.value}`;
    }
    
    // Handle Mongoose duplicate key error
    if (err.code === 11000) {
      statusCode = 400;
      const field = Object.keys(err.keyValue)[0];
      message = `Duplicate value for ${field}: ${err.keyValue[field]}`;
    }
    
    // Handle JWT errors
    if (err.name === 'JsonWebTokenError') {
      statusCode = 401;
      message = 'Token invalide';
    }
    
    if (err.name === 'TokenExpiredError') {
      statusCode = 401;
      message = 'Token expiré';
    }
    
    // Send error response
    res.status(statusCode).json({
      success: false,
      message,
      data,
      stack: process.env.NODE_ENV === 'development' ? err.stack : undefined
    });
  };
  
  module.exports = errorHandler;