// routes/nistRoutes.js
const express = require('express');
const axios = require('axios');
const router = express.Router();

// NIST NVD API base URL
const NIST_BASE_URL = 'https://services.nvd.nist.gov/rest/json/cves/2.0';

// NOTE: These routes are intentionally PUBLIC (no authentication required)
// They serve as proxy routes for external CTI APIs and should be accessible
// without authentication to allow CTI analysis functionality

// Middleware to explicitly bypass any global authentication
router.use((req, res, next) => {
  // Remove any authorization headers that might cause issues
  delete req.headers.authorization;
  console.log(`[NIST Proxy] ${req.method} ${req.path} - Authentication bypassed`);
  next();
});

// @route   GET /api/nist/test
// @desc    Test endpoint to verify NIST routes are working
// @access  Public (no auth required)
router.get('/test', (req, res) => {
  console.log('[NIST Proxy] Test endpoint called - routes are working!');
  res.json({
    success: true,
    message: 'NIST proxy routes are working',
    timestamp: new Date().toISOString(),
    baseUrl: NIST_BASE_URL
  });
});

// Rate limiting variables
let lastRequestTime = 0;
const RATE_LIMIT_DELAY = 6000; // 6 seconds between requests as recommended by NIST

// Helper function to enforce rate limiting
const enforceRateLimit = () => {
  const now = Date.now();
  const timeSinceLastRequest = now - lastRequestTime;

  if (timeSinceLastRequest < RATE_LIMIT_DELAY) {
    const waitTime = RATE_LIMIT_DELAY - timeSinceLastRequest;
    console.log(`[NIST Proxy] Rate limiting: waiting ${waitTime}ms`);
    return new Promise(resolve => setTimeout(resolve, waitTime));
  }

  lastRequestTime = now;
  return Promise.resolve();
};

// @route   GET /api/nist/search
// @desc    Simple NIST NVD keyword search proxy
// @access  Public
router.get('/search', async (req, res) => {
  try {
    console.log('[NIST Proxy] Keyword search request:', req.query);

    // Enforce rate limiting
    await enforceRateLimit();

    // Extract and validate keyword search parameter
    const keywordSearch = req.query.keywordSearch;

    if (!keywordSearch || typeof keywordSearch !== 'string') {
      return res.status(400).json({
        error: 'Missing keyword search',
        message: 'keywordSearch parameter is required',
        status: 400,
        example: '/api/nist/search?keywordSearch=Microsoft Windows'
      });
    }

    // Clean keyword search - keep alphanumeric, spaces, and basic punctuation
    const cleanKeyword = keywordSearch.replace(/[^\w\s\-\.]/g, '').trim();

    if (cleanKeyword.length === 0) {
      return res.status(400).json({
        error: 'Invalid keyword search',
        message: 'Keyword search must contain valid characters',
        status: 400
      });
    }

    // Prepare parameters for NIST API
    const nistParams = {
      keywordSearch: cleanKeyword
    };

    // Add optional pagination parameters
    if (req.query.resultsPerPage) {
      nistParams.resultsPerPage = Math.min(parseInt(req.query.resultsPerPage) || 20, 2000);
    }

    if (req.query.startIndex) {
      nistParams.startIndex = parseInt(req.query.startIndex) || 0;
    }

    console.log('[NIST Proxy] Searching NIST API with keywords:', cleanKeyword);

    // Make request to NIST NVD API
    const response = await axios.get(NIST_BASE_URL, {
      params: nistParams,
      headers: {
        'Accept': 'application/json',
        'User-Agent': 'EBIOS-RM-CTI-Service/1.0'
      },
      timeout: 30000 // 30 second timeout
    });

    console.log('[NIST Proxy] Search response:', {
      status: response.status,
      totalResults: response.data?.totalResults || 0,
      vulnerabilitiesCount: response.data?.vulnerabilities?.length || 0,
      keywords: cleanKeyword
    });

    // Return the NIST API response directly
    res.json(response.data);

  } catch (error) {
    console.error('[NIST Proxy] Keyword search error:', {
      message: error.message,
      status: error.response?.status,
      statusText: error.response?.statusText,
      keywords: req.query.keywordSearch
    });

    // Handle rate limiting
    if (error.response?.status === 403) {
      return res.status(429).json({
        error: 'NIST API rate limit exceeded',
        message: 'Please wait before making another request',
        status: 429,
        retryAfter: 30
      });
    }

    // Handle bad requests
    if (error.response?.status === 400) {
      return res.status(400).json({
        error: 'Invalid search parameters',
        message: error.response?.data?.message || 'Bad request to NIST API',
        status: 400
      });
    }

    // For network errors, provide graceful fallback
    if (error.code === 'ECONNABORTED' || error.code === 'ENOTFOUND') {
      console.warn('[NIST Proxy] NIST API unavailable, providing empty response');
      return res.json({
        resultsPerPage: nistParams.resultsPerPage || 20,
        startIndex: nistParams.startIndex || 0,
        totalResults: 0,
        format: "NVD_CVE",
        version: "2.0",
        timestamp: new Date().toISOString(),
        vulnerabilities: [],
        message: "NIST API temporarily unavailable"
      });
    }

    // For other errors, provide empty response
    console.warn('[NIST Proxy] Providing empty response due to error:', error.message);
    res.json({
      resultsPerPage: nistParams.resultsPerPage || 20,
      startIndex: nistParams.startIndex || 0,
      totalResults: 0,
      format: "NVD_CVE",
      version: "2.0",
      timestamp: new Date().toISOString(),
      vulnerabilities: [],
      message: `Search error: ${error.message}`
    });
  }
});

// @route   GET /api/nist/cpe-search
// @desc    Proxy NIST CPE search API
// @access  Public
router.get('/cpe-search', async (req, res) => {
  try {
    console.log('[NIST Proxy] CPE search request:', req.query);

    const response = await axios.get(NIST_BASE_URL, {
      params: req.query,
      headers: {
        'Accept': 'application/json',
        'User-Agent': 'EBIOS-RM-CTI-Service/1.0'
      },
      timeout: 30000
    });

    console.log('[NIST Proxy] CPE search response:', {
      status: response.status,
      totalResults: response.data?.totalResults || 0,
      vulnerabilitiesCount: response.data?.vulnerabilities?.length || 0
    });

    res.json(response.data);
  } catch (error) {
    console.error('[NIST Proxy] CPE search error:', {
      message: error.message,
      status: error.response?.status,
      statusText: error.response?.statusText
    });

    res.status(error.response?.status || 500).json({
      error: 'NIST CPE search failed',
      message: error.message,
      status: error.response?.status || 500
    });
  }
});

// @route   GET /api/nist/keyword-search
// @desc    Proxy NIST keyword search API
// @access  Public
router.get('/keyword-search', async (req, res) => {
  try {
    console.log('[NIST Proxy] Keyword search request:', req.query);

    const response = await axios.get(NIST_BASE_URL, {
      params: req.query,
      headers: {
        'Accept': 'application/json',
        'User-Agent': 'EBIOS-RM-CTI-Service/1.0'
      },
      timeout: 30000
    });

    console.log('[NIST Proxy] Keyword search response:', {
      status: response.status,
      totalResults: response.data?.totalResults || 0,
      vulnerabilitiesCount: response.data?.vulnerabilities?.length || 0
    });

    res.json(response.data);
  } catch (error) {
    console.error('[NIST Proxy] Keyword search error:', {
      message: error.message,
      status: error.response?.status,
      statusText: error.response?.statusText
    });

    res.status(error.response?.status || 500).json({
      error: 'NIST keyword search failed',
      message: error.message,
      status: error.response?.status || 500
    });
  }
});

module.exports = router;
