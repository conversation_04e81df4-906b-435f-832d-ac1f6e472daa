// src/components/Atelier3/Activite3/NodeContextMenu.js
import React, { useEffect, useRef, useState } from 'react';
import { Copy, Trash2, Edit2, ZoomIn, ArrowUpRight } from 'lucide-react';

const NodeContextMenu = ({
  x,
  y,
  node,
  onClose,
  onEdit,
  onDelete,
  onDuplicate,
  onFocus
}) => {
  const menuRef = useRef(null);

  // Close the menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (menuRef.current && !menuRef.current.contains(event.target)) {
        onClose();
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [onClose]);

  // Use useEffect to calculate position after the component has mounted
  const [position, setPosition] = useState({ x, y });

  useEffect(() => {
    if (menuRef.current) {
      // Get the parent container dimensions
      const parentElement = menuRef.current.parentElement;
      const parentWidth = parentElement?.clientWidth || window.innerWidth;
      const parentHeight = parentElement?.clientHeight || window.innerHeight;

      // Get the actual menu dimensions
      const menuWidth = menuRef.current.offsetWidth;
      const menuHeight = menuRef.current.offsetHeight;

      // Calculate adjusted position
      let adjustedX = Math.min(x, parentWidth - menuWidth);
      let adjustedY = Math.min(y, parentHeight - menuHeight);

      // Make sure the menu doesn't go off the left or top edge
      adjustedX = Math.max(0, adjustedX);
      adjustedY = Math.max(0, adjustedY);

      setPosition({ x: adjustedX, y: adjustedY });
    }
  }, [x, y, menuRef]);

  return (
    <div
      ref={menuRef}
      className="absolute bg-white rounded-lg shadow-lg border border-slate-200 z-50 w-48"
      style={{
        left: position.x,
        top: position.y
      }}
    >
      <ul className="py-1">
        <li>
          <button
            className="w-full text-left px-4 py-2 text-sm text-slate-700 hover:bg-slate-100 flex items-center"
            onClick={() => {
              onEdit(node);
              onClose();
            }}
          >
            <Edit2 size={16} className="mr-2 text-blue-600" />
            Modifier
          </button>
        </li>
        <li>
          <button
            className="w-full text-left px-4 py-2 text-sm text-slate-700 hover:bg-slate-100 flex items-center"
            onClick={() => {
              onDuplicate(node);
              onClose();
            }}
          >
            <Copy size={16} className="mr-2 text-green-600" />
            Dupliquer
          </button>
        </li>
        <li>
          <button
            className="w-full text-left px-4 py-2 text-sm text-slate-700 hover:bg-slate-100 flex items-center"
            onClick={() => {
              onFocus(node);
              onClose();
            }}
          >
            <ZoomIn size={16} className="mr-2 text-purple-600" />
            Zoomer
          </button>
        </li>
        <li className="border-t border-slate-200">
          <button
            className="w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-red-50 flex items-center"
            onClick={() => {
              onDelete(node.id);
              onClose();
            }}
          >
            <Trash2 size={16} className="mr-2" />
            Supprimer
          </button>
        </li>
      </ul>
    </div>
  );
};

export default NodeContextMenu;
