// src/components/Atelier4/ScenarioVisualization.js
import React, { useState, useCallback, useEffect, useRef } from 'react';
import ReactFlow, {
  MiniMap,
  Controls,
  Background,
  addEdge,
  applyNodeChanges,
  applyEdgeChanges,
  Panel,
  useReactFlow,
  Handle,
  Position,
} from 'reactflow';
import 'reactflow/dist/style.css';
import { X, ZoomIn, ZoomOut, Maximize2, RefreshCw, Download, Save, FileText, Eye, Printer } from 'lucide-react';
import { toPng } from 'html-to-image';
import { jsPDF } from 'jspdf';

// Custom node types for scenario visualization
const ScenarioStepNode = ({ data, selected }) => {
  const getPhaseColor = (phase) => {
    switch (phase) {
      case 'CONNAITRE': return '#3B82F6'; // Blue
      case 'RENTRER': return '#EF4444'; // Red
      case 'TROUVER': return '#F59E0B'; // Amber
      case 'EXPLOITER': return '#10B981'; // Green
      default: return '#6B7280'; // Gray
    }
  };

  const getPhaseIcon = (phase) => {
    switch (phase) {
      case 'CONNAITRE': return '1';
      case 'RENTRER': return '2';
      case 'TROUVER': return '3';
      case 'EXPLOITER': return '4';
      default: return '•';
    }
  };

  const phaseColor = getPhaseColor(data.phase);
  const phaseIcon = getPhaseIcon(data.phase);

  return (
    <div
      className={`relative border-2 rounded-xl shadow-lg w-96 bg-white transition-all duration-200 hover:shadow-xl ${
        selected ? 'border-blue-500 ring-2 ring-blue-200' : 'border-gray-300'
      }`}
      style={{ borderColor: selected ? '#3B82F6' : phaseColor }}
    >
      {/* Phase header with icon and name */}
      <div
        className="flex items-center text-white font-bold text-sm py-3 px-4 rounded-t-xl"
        style={{ backgroundColor: phaseColor }}
      >
        <div
          className="w-8 h-8 rounded-full bg-white flex items-center justify-center mr-3 text-sm font-bold shadow-sm"
          style={{ color: phaseColor }}
        >
          {phaseIcon}
        </div>
        <div className="flex-1">
          <div className="font-bold text-base">{data.phase || 'PHASE'}</div>
          <div className="text-xs opacity-90 font-normal">{data.name}</div>
        </div>
      </div>

      {/* Content */}
      <div className="p-4">
        {/* Description */}
        <div className="mb-4">
          <p className="text-gray-700 text-sm leading-relaxed font-medium">{data.description}</p>
        </div>

        {/* Metadata row */}
        <div className="flex flex-wrap gap-2 mb-4">
          {data.duration && (
            <div className="bg-gray-100 px-3 py-1 rounded-full text-xs text-gray-700">
              <strong>Durée:</strong> {data.duration}
            </div>
          )}
          {data.difficulty && (
            <div className="bg-gray-100 px-3 py-1 rounded-full text-xs text-gray-700">
              <strong>Difficulté:</strong> {data.difficulty}
            </div>
          )}
          {data.tools && data.tools.length > 0 && (
            <div className="bg-gray-100 px-3 py-1 rounded-full text-xs text-gray-700">
              <strong>Outils:</strong> {data.tools.length}
            </div>
          )}
        </div>

        {/* Techniques Section */}
        {data.techniques && data.techniques.length > 0 && (
          <div className="mb-4">
            <h5 className="font-semibold text-gray-800 mb-2 text-sm flex items-center">
              <div className="w-2 h-2 rounded-full mr-2" style={{ backgroundColor: phaseColor }}></div>
              Techniques:
            </h5>
            <div className="space-y-2">
              {data.techniques.slice(0, 4).map((technique, index) => (
                <div key={index} className="bg-purple-50 border border-purple-200 px-3 py-2 rounded-lg text-xs">
                  <div className="font-medium text-purple-800">
                    {technique.name || technique.technique || technique}
                  </div>
                  {technique.description && (
                    <div className="text-purple-600 mt-1 text-xs">
                      {technique.description}
                    </div>
                  )}
                  {technique.id && (
                    <div className="text-purple-500 mt-1 text-xs font-mono">
                      ID: {technique.id}
                    </div>
                  )}
                </div>
              ))}
              {data.techniques.length > 4 && (
                <div className="text-gray-500 italic text-xs text-center py-1">
                  +{data.techniques.length - 4} techniques supplémentaires
                </div>
              )}
            </div>
          </div>
        )}

        {/* Indicators Section */}
        {data.indicators && data.indicators.length > 0 && (
          <div className="mb-4">
            <h5 className="font-semibold text-gray-800 mb-2 text-sm flex items-center">
              <div className="w-2 h-2 rounded-full mr-2" style={{ backgroundColor: phaseColor }}></div>
              Indicateurs:
            </h5>
            <div className="space-y-2">
              {data.indicators.slice(0, 4).map((indicator, index) => (
                <div key={index} className="bg-yellow-50 border border-yellow-200 px-3 py-2 rounded-lg text-xs">
                  <div className="font-medium text-yellow-800">
                    {indicator.name || indicator.indicator || indicator}
                  </div>
                  {indicator.description && (
                    <div className="text-yellow-600 mt-1 text-xs">
                      {indicator.description}
                    </div>
                  )}
                  {indicator.type && (
                    <div className="text-yellow-500 mt-1 text-xs">
                      Type: {indicator.type}
                    </div>
                  )}
                </div>
              ))}
              {data.indicators.length > 4 && (
                <div className="text-gray-500 italic text-xs text-center py-1">
                  +{data.indicators.length - 4} indicateurs supplémentaires
                </div>
              )}
            </div>
          </div>
        )}

        {/* Tools Section */}
        {data.tools && data.tools.length > 0 && (
          <div className="mb-2">
            <h5 className="font-semibold text-gray-800 mb-2 text-sm flex items-center">
              <div className="w-2 h-2 rounded-full mr-2" style={{ backgroundColor: phaseColor }}></div>
              Outils:
            </h5>
            <div className="flex flex-wrap gap-1">
              {data.tools.slice(0, 6).map((tool, index) => (
                <span key={index} className="bg-green-100 text-green-800 px-2 py-1 rounded text-xs font-medium border border-green-200">
                  {tool.name || tool}
                </span>
              ))}
              {data.tools.length > 6 && (
                <span className="text-gray-500 italic text-xs px-2 py-1">
                  +{data.tools.length - 6} more
                </span>
              )}
            </div>
          </div>
        )}
      </div>

      {/* Handles */}
      <Handle
        type="target"
        position={Position.Left}
        style={{
          background: phaseColor,
          border: '3px solid white',
          width: '12px',
          height: '12px',
          boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
        }}
      />
      <Handle
        type="source"
        position={Position.Right}
        style={{
          background: phaseColor,
          border: '3px solid white',
          width: '12px',
          height: '12px',
          boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
        }}
      />
    </div>
  );
};

const ScenarioHeaderNode = ({ data, selected }) => {
  return (
    <div
      className={`relative p-4 border-2 rounded-xl shadow-xl w-[500px] bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 transition-all duration-200 hover:shadow-2xl ${
        selected ? 'border-blue-500 ring-2 ring-blue-200' : 'border-blue-300'
      }`}
    >
      {/* Header with icon */}
      <div className="flex items-center mb-3">
        <div className="w-8 h-8 rounded-full bg-blue-500 flex items-center justify-center mr-3 shadow-md">
          <Eye className="text-white" size={16} />
        </div>
        <div className="flex-1">
          <div className="font-bold text-blue-900 text-base mb-1">{data.name}</div>
          <div className="text-xs text-blue-700 opacity-80">Scénario Opérationnel IA</div>
        </div>
      </div>

      {/* Description */}
      <div className="mb-3 p-2 bg-white bg-opacity-60 rounded-lg border border-blue-200">
        <div className="text-xs text-blue-800 leading-relaxed font-medium">{data.description}</div>
      </div>

      {/* Compact metadata grid */}
      <div className="grid grid-cols-3 gap-2 mb-3">
        {data.severity && (
          <div className="bg-white bg-opacity-70 rounded-lg p-2 text-center border border-red-200">
            <div className="text-red-600 font-semibold text-xs mb-1">Sévérité</div>
            <div className="text-red-800 font-bold text-xs">{data.severity}</div>
          </div>
        )}
        {data.likelihood && (
          <div className="bg-white bg-opacity-70 rounded-lg p-2 text-center border border-orange-200">
            <div className="text-orange-600 font-semibold text-xs mb-1">Probabilité</div>
            <div className="text-orange-800 font-bold text-xs">{data.likelihood}</div>
          </div>
        )}
        {data.timeline && (
          <div className="bg-white bg-opacity-70 rounded-lg p-2 text-center border border-green-200">
            <div className="text-green-600 font-semibold text-xs mb-1">Durée</div>
            <div className="text-green-800 font-bold text-xs">{data.timeline}</div>
          </div>
        )}
      </div>

      {/* Attack phases preview - more compact */}
      <div className="flex justify-center space-x-1 text-xs">
        <div className="flex items-center space-x-1">
          <div className="w-2 h-2 rounded-full bg-blue-500"></div>
          <span className="text-blue-700 font-medium">1</span>
        </div>
        <div className="text-gray-400">→</div>
        <div className="flex items-center space-x-1">
          <div className="w-2 h-2 rounded-full bg-red-500"></div>
          <span className="text-red-700 font-medium">2</span>
        </div>
        <div className="text-gray-400">→</div>
        <div className="flex items-center space-x-1">
          <div className="w-2 h-2 rounded-full bg-amber-500"></div>
          <span className="text-amber-700 font-medium">3</span>
        </div>
        <div className="text-gray-400">→</div>
        <div className="flex items-center space-x-1">
          <div className="w-2 h-2 rounded-full bg-green-500"></div>
          <span className="text-green-700 font-medium">4</span>
        </div>
      </div>

      {/* Handles */}
      <Handle
        type="source"
        position={Position.Right}
        style={{
          background: '#3B82F6',
          border: '3px solid white',
          width: '12px',
          height: '12px',
          boxShadow: '0 2px 6px rgba(0,0,0,0.15)'
        }}
      />
    </div>
  );
};

const nodeTypes = {
  scenarioHeader: ScenarioHeaderNode,
  scenarioStep: ScenarioStepNode,
};

const ScenarioVisualization = ({
  scenario,
  title = "Visualisation du Scénario Opérationnel",
  onClose,
  isInline = false,
  nodes: externalNodes,
  edges: externalEdges,
  onNodesChange: onExternalNodesChange,
  onEdgesChange: onExternalEdgesChange
}) => {
  // Use external nodes/edges if provided, otherwise use internal state
  const [internalNodes, setInternalNodes] = useState([]);
  const [internalEdges, setInternalEdges] = useState([]);
  const [isFullscreen, setIsFullscreen] = useState(false);

  const nodes = externalNodes || internalNodes;
  const edges = externalEdges || internalEdges;
  const setNodes = onExternalNodesChange || setInternalNodes;
  const setEdges = onExternalEdgesChange || setInternalEdges;

  const reactFlowWrapper = useRef(null);
  const reactFlowInstance = useReactFlow();

  console.log('[ScenarioVisualization] Component rendering with:', { scenario, title, isInline });

  // Create nodes and edges based on the scenario
  useEffect(() => {
    if (!scenario) return;

    console.log('[ScenarioVisualization] Received scenario:', scenario);
    console.log('[ScenarioVisualization] Scenario steps:', scenario.steps);

    const createScenarioVisualization = () => {
      const newNodes = [];
      const newEdges = [];

      // Calculate positions to prevent intersections
      const headerWidth = 500;
      const stepWidth = 400;
      const horizontalSpacing = 50; // Space between cards

      // Create header node at the start of the line
      const headerNode = {
        id: 'scenario-header',
        type: 'scenarioHeader',
        position: { x: 50, y: 50 },
        data: {
          name: scenario.name || scenario.title || 'Scénario Opérationnel',
          description: scenario.description || '',
          severity: scenario.severity,
          likelihood: scenario.likelihood,
          timeline: scenario.timeline,
          detectionDifficulty: scenario.detectionDifficulty,
        },
      };
      newNodes.push(headerNode);

      // Create step nodes if available
      if (scenario.steps && scenario.steps.length > 0) {
        scenario.steps.forEach((step, index) => {
          const stepNode = {
            id: `step-${index}`,
            type: 'scenarioStep',
            position: {
              x: 50 + headerWidth + horizontalSpacing + (index * (stepWidth + horizontalSpacing)), // Start after header
              y: 50 // Same level as header for horizontal flow
            },
            data: {
              stepNumber: step.stepNumber || index + 1,
              phase: step.phase,
              name: step.name,
              technique: step.technique,
              description: step.description,
              duration: step.duration,
              difficulty: step.difficulty,
              techniques: step.techniques,
              indicators: step.indicators,
              tools: step.tools,
            },
          };
          newNodes.push(stepNode);

          // Create edge from header to first step, or from previous step
          const sourceId = index === 0 ? 'scenario-header' : `step-${index - 1}`;
          const edge = {
            id: `edge-${index}`,
            source: sourceId,
            target: `step-${index}`,
            type: 'straight', // Use straight edges for horizontal flow
            animated: true,
            style: { stroke: '#6366F1', strokeWidth: 3 },
            label: index === 0 ? 'Début' : `Phase ${index + 1}`,
            labelStyle: {
              fill: '#6366F1',
              fontWeight: 600,
              fontSize: 11,
              background: 'white',
              padding: '4px 8px',
              borderRadius: '6px',
              border: '2px solid #6366F1',
              boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
            },
            labelBgPadding: [8, 4],
            labelBgBorderRadius: 6,
            labelBgStyle: { fill: 'white', fillOpacity: 0.9 },
          };
          newEdges.push(edge);
        });
      } else {
        // Create a simple node if no detailed steps
        const simpleNode = {
          id: 'simple-scenario',
          type: 'scenarioStep',
          position: { x: 200, y: 300 },
          data: {
            stepNumber: 1,
            name: 'Scénario Simplifié',
            description: scenario.description || 'Aucun détail d\'étapes disponible',
            techniques: [],
            indicators: [],
          },
        };
        newNodes.push(simpleNode);

        const edge = {
          id: 'edge-simple',
          source: 'scenario-header',
          target: 'simple-scenario',
          type: 'smoothstep',
          animated: true,
          style: { stroke: '#6366F1', strokeWidth: 2 },
          label: 'Scénario',
          labelStyle: { fill: '#6366F1', fontWeight: 500 },
        };
        newEdges.push(edge);
      }

      setNodes(newNodes);
      setEdges(newEdges);
    };

    createScenarioVisualization();
  }, [scenario]);

  const onNodesChange = useCallback(
    (changes) => setNodes((nds) => applyNodeChanges(changes, nds)),
    [setNodes]
  );

  const onEdgesChange = useCallback(
    (changes) => setEdges((eds) => applyEdgeChanges(changes, eds)),
    [setEdges]
  );

  const onConnect = useCallback(
    (connection) => setEdges((eds) => addEdge(connection, eds)),
    [setEdges]
  );

  // Function to reset the view
  const resetView = useCallback(() => {
    if (reactFlowInstance) {
      reactFlowInstance.fitView({ padding: 0.2 });
    }
  }, [reactFlowInstance]);

  // Function to export as PNG
  const exportAsPng = useCallback(() => {
    if (reactFlowWrapper.current === null) return;

    toPng(reactFlowWrapper.current, {
      backgroundColor: '#fff',
      width: reactFlowWrapper.current.offsetWidth,
      height: reactFlowWrapper.current.offsetHeight,
    })
      .then((dataUrl) => {
        const link = document.createElement('a');
        link.download = `scenario-${new Date().toISOString().split('T')[0]}.png`;
        link.href = dataUrl;
        link.click();
      });
  }, []);

  return (
    <div className={`scenario-visualization ${isFullscreen ? 'fullscreen' : ''}`}>
      {/* Header */}
      <div className={`bg-white border-b border-slate-200 ${isInline ? 'p-3' : 'p-4'}`}>
        <div className="flex items-center justify-between">
          <div>
            <h2 className={`font-bold text-slate-800 flex items-center ${isInline ? 'text-lg' : 'text-xl'}`}>
              <Eye size={isInline ? 20 : 24} className="mr-3 text-indigo-600" />
              {title}
            </h2>
            {!isInline && (
              <p className="text-slate-600 mt-1">
                Visualisation interactive du scénario opérationnel
              </p>
            )}
          </div>
          
          <div className="flex items-center space-x-2">
            <button
              onClick={resetView}
              className="flex items-center px-3 py-2 bg-slate-100 text-slate-700 rounded-lg hover:bg-slate-200 transition duration-200 text-sm"
              title="Réinitialiser la vue"
            >
              <RefreshCw size={16} className="mr-1" />
              Centrer
            </button>
            
            <button
              onClick={exportAsPng}
              className="flex items-center px-3 py-2 bg-blue-100 text-blue-700 rounded-lg hover:bg-blue-200 transition duration-200 text-sm"
              title="Exporter en PNG"
            >
              <Download size={16} className="mr-1" />
              PNG
            </button>
            
            <button
              onClick={() => setIsFullscreen(!isFullscreen)}
              className="flex items-center px-3 py-2 bg-slate-100 text-slate-700 rounded-lg hover:bg-slate-200 transition duration-200 text-sm"
              title={isFullscreen ? "Quitter le plein écran" : "Plein écran"}
            >
              <Maximize2 size={16} />
            </button>
            
            <button
              onClick={onClose}
              className="flex items-center px-3 py-2 bg-red-100 text-red-700 rounded-lg hover:bg-red-200 transition duration-200 text-sm"
              title="Fermer"
            >
              <X size={16} />
            </button>
          </div>
        </div>
      </div>

      {/* Flow diagram */}
      <div className="flex" style={{ height: isFullscreen ? 'calc(100vh - 80px)' : (isInline ? '500px' : '600px') }}>
        <div
          ref={reactFlowWrapper}
          className="flex-grow relative"
        >
          <ReactFlow
            nodes={nodes}
            edges={edges}
            onNodesChange={onNodesChange}
            onEdgesChange={onEdgesChange}
            onConnect={onConnect}
            nodeTypes={nodeTypes}
            fitView
            attributionPosition="bottom-left"
          >
            <Controls />
            <MiniMap
              nodeColor={(n) => {
                if (n.type === 'scenarioHeader') return '#E0E7FF';
                if (n.type === 'scenarioStep') return '#F3F4F6';
                return '#E2E8F0';
              }}
              nodeStrokeWidth={3}
              pannable
              zoomable
            />
            <Background color="#ccc" variant="dots" gap={16} size={1} />
          </ReactFlow>
        </div>
      </div>

      {/* Footer */}
      {!isInline && (
        <div className="p-4 border-t border-slate-200 text-sm text-slate-500 bg-slate-50">
          <p>
            <strong>Visualisation interactive :</strong> Déplacez et explorez les étapes du scénario •
            <strong> Zoom :</strong> Utilisez la molette de la souris ou les contrôles •
            <strong> Export :</strong> Sauvegardez la visualisation en PNG
          </p>
        </div>
      )}
    </div>
  );
};

export default ScenarioVisualization;

// CSS Styles (add to your global CSS or component styles)
const styles = `
.scenario-visualization {
  position: relative;
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.scenario-visualization.fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  border-radius: 0;
}

.react-flow__node-scenarioHeader {
  background: transparent !important;
  border: none !important;
}

.react-flow__node-scenarioStep {
  background: transparent !important;
  border: none !important;
}

.react-flow__edge-path {
  stroke-width: 2;
}

.react-flow__edge-text {
  font-size: 12px;
  font-weight: 500;
}

.react-flow__controls {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.react-flow__controls button {
  background: white;
  border: none;
  border-bottom: 1px solid #e2e8f0;
  color: #64748b;
  transition: all 0.2s;
}

.react-flow__controls button:hover {
  background: #f8fafc;
  color: #334155;
}

.react-flow__controls button:last-child {
  border-bottom: none;
}

.react-flow__minimap {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}
`;

// Inject styles if not already present
if (typeof document !== 'undefined' && !document.getElementById('scenario-visualization-styles')) {
  const styleSheet = document.createElement('style');
  styleSheet.id = 'scenario-visualization-styles';
  styleSheet.textContent = styles;
  document.head.appendChild(styleSheet);
}
