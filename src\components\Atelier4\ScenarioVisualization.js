// src/components/Atelier4/ScenarioVisualization.js
import React, { useState, useCallback, useEffect, useRef } from 'react';
import ReactFlow, {
  MiniMap,
  Controls,
  Background,
  addEdge,
  applyNodeChanges,
  applyEdgeChanges,
  Panel,
  useReactFlow,
} from 'reactflow';
import 'reactflow/dist/style.css';
import { X, ZoomIn, ZoomOut, Maximize2, RefreshCw, Download, Save, FileText, Eye, Printer } from 'lucide-react';
import { toPng } from 'html-to-image';
import { jsPDF } from 'jspdf';

// Custom node types for scenario visualization
const ScenarioStepNode = ({ data, selected }) => {
  const getPhaseColor = (phase) => {
    switch (phase) {
      case 'CONNAITRE': return '#3B82F6'; // Blue
      case 'RENTRER': return '#EF4444'; // Red
      case 'TROUVER': return '#F59E0B'; // Amber
      case 'EXPLOITER': return '#10B981'; // Green
      default: return '#6B7280'; // Gray
    }
  };

  const phaseColor = getPhaseColor(data.phase);

  return (
    <div 
      className={`relative p-4 border-2 rounded-lg shadow-md w-80 bg-white ${
        selected ? 'border-blue-500' : 'border-gray-300'
      }`}
      style={{ borderColor: selected ? '#3B82F6' : phaseColor }}
    >
      <div className="flex items-start mb-2">
        <div 
          className="w-6 h-6 rounded-full flex items-center justify-center text-white text-xs font-bold mr-3 flex-shrink-0"
          style={{ backgroundColor: phaseColor }}
        >
          {data.stepNumber}
        </div>
        <div className="flex-1">
          <div className="flex items-center mb-1">
            {data.phase && (
              <span 
                className="px-2 py-1 rounded text-xs font-semibold text-white mr-2"
                style={{ backgroundColor: phaseColor }}
              >
                {data.phase}
              </span>
            )}
            <div className="font-bold text-gray-800 text-sm">{data.name || data.technique}</div>
          </div>
          <div className="text-xs text-gray-600 mb-2">{data.description}</div>
          
          {data.duration && (
            <div className="text-xs text-gray-500 mb-1">
              <strong>Durée:</strong> {data.duration}
            </div>
          )}
          
          {data.difficulty && (
            <div className="text-xs text-gray-500">
              <strong>Difficulté:</strong> {data.difficulty}
            </div>
          )}
        </div>
      </div>

      {/* Techniques */}
      {data.techniques && data.techniques.length > 0 && (
        <div className="mb-2">
          <div className="text-xs font-semibold text-gray-600 mb-1">Techniques:</div>
          <div className="flex flex-wrap gap-1">
            {data.techniques.slice(0, 3).map((technique, index) => (
              <span key={index} className="inline-flex items-center px-1 py-0.5 rounded text-xs bg-purple-100 text-purple-800">
                {technique}
              </span>
            ))}
            {data.techniques.length > 3 && (
              <span className="text-xs text-gray-500">+{data.techniques.length - 3} more</span>
            )}
          </div>
        </div>
      )}

      {/* Indicators */}
      {data.indicators && data.indicators.length > 0 && (
        <div className="mb-2">
          <div className="text-xs font-semibold text-gray-600 mb-1">Indicateurs:</div>
          <div className="flex flex-wrap gap-1">
            {data.indicators.slice(0, 2).map((indicator, index) => (
              <span key={index} className="inline-flex items-center px-1 py-0.5 rounded text-xs bg-yellow-100 text-yellow-800">
                {indicator}
              </span>
            ))}
            {data.indicators.length > 2 && (
              <span className="text-xs text-gray-500">+{data.indicators.length - 2} more</span>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

const ScenarioHeaderNode = ({ data, selected }) => {
  return (
    <div 
      className={`relative p-4 border-2 rounded-lg shadow-md w-96 bg-gradient-to-r from-indigo-50 to-blue-50 ${
        selected ? 'border-blue-500' : 'border-indigo-300'
      }`}
    >
      <div className="flex items-center mb-2">
        <Eye className="text-indigo-600 mr-2 flex-shrink-0" size={20} />
        <div className="font-bold text-indigo-800 text-lg">{data.name}</div>
      </div>
      
      <div className="text-sm text-indigo-700 mb-3">{data.description}</div>
      
      <div className="grid grid-cols-3 gap-2 text-xs">
        {data.severity && (
          <div className="text-center">
            <div className="text-indigo-600 font-semibold">Sévérité</div>
            <div className="text-indigo-800">{data.severity}</div>
          </div>
        )}
        {data.likelihood && (
          <div className="text-center">
            <div className="text-indigo-600 font-semibold">Probabilité</div>
            <div className="text-indigo-800">{data.likelihood}</div>
          </div>
        )}
        {data.timeline && (
          <div className="text-center">
            <div className="text-indigo-600 font-semibold">Durée</div>
            <div className="text-indigo-800">{data.timeline}</div>
          </div>
        )}
      </div>
    </div>
  );
};

const nodeTypes = {
  scenarioHeader: ScenarioHeaderNode,
  scenarioStep: ScenarioStepNode,
};

const ScenarioVisualization = ({ scenario, title = "Visualisation du Scénario Opérationnel", onClose, isInline = false }) => {
  const [nodes, setNodes] = useState([]);
  const [edges, setEdges] = useState([]);
  const [isFullscreen, setIsFullscreen] = useState(false);

  const reactFlowWrapper = useRef(null);
  const reactFlowInstance = useReactFlow();

  console.log('[ScenarioVisualization] Component rendering with:', { scenario, title, isInline });

  // Create nodes and edges based on the scenario
  useEffect(() => {
    if (!scenario) return;

    console.log('[ScenarioVisualization] Received scenario:', scenario);
    console.log('[ScenarioVisualization] Scenario steps:', scenario.steps);

    const createScenarioVisualization = () => {
      const newNodes = [];
      const newEdges = [];

      // Create header node
      const headerNode = {
        id: 'scenario-header',
        type: 'scenarioHeader',
        position: { x: 200, y: 50 },
        data: {
          name: scenario.name || scenario.title || 'Scénario Opérationnel',
          description: scenario.description || '',
          severity: scenario.severity,
          likelihood: scenario.likelihood,
          timeline: scenario.timeline,
          detectionDifficulty: scenario.detectionDifficulty,
        },
      };
      newNodes.push(headerNode);

      // Create step nodes if available
      if (scenario.steps && scenario.steps.length > 0) {
        scenario.steps.forEach((step, index) => {
          const stepNode = {
            id: `step-${index}`,
            type: 'scenarioStep',
            position: { 
              x: 50 + (index % 3) * 400, 
              y: 250 + Math.floor(index / 3) * 200 
            },
            data: {
              stepNumber: step.stepNumber || index + 1,
              phase: step.phase,
              name: step.name,
              technique: step.technique,
              description: step.description,
              duration: step.duration,
              difficulty: step.difficulty,
              techniques: step.techniques,
              indicators: step.indicators,
              tools: step.tools,
            },
          };
          newNodes.push(stepNode);

          // Create edge from header to first step, or from previous step
          const sourceId = index === 0 ? 'scenario-header' : `step-${index - 1}`;
          const edge = {
            id: `edge-${index}`,
            source: sourceId,
            target: `step-${index}`,
            type: 'smoothstep',
            animated: true,
            style: { stroke: '#6366F1', strokeWidth: 2 },
            label: index === 0 ? 'Début' : `Étape ${index + 1}`,
            labelStyle: { fill: '#6366F1', fontWeight: 500 },
          };
          newEdges.push(edge);
        });
      } else {
        // Create a simple node if no detailed steps
        const simpleNode = {
          id: 'simple-scenario',
          type: 'scenarioStep',
          position: { x: 200, y: 300 },
          data: {
            stepNumber: 1,
            name: 'Scénario Simplifié',
            description: scenario.description || 'Aucun détail d\'étapes disponible',
            techniques: [],
            indicators: [],
          },
        };
        newNodes.push(simpleNode);

        const edge = {
          id: 'edge-simple',
          source: 'scenario-header',
          target: 'simple-scenario',
          type: 'smoothstep',
          animated: true,
          style: { stroke: '#6366F1', strokeWidth: 2 },
          label: 'Scénario',
          labelStyle: { fill: '#6366F1', fontWeight: 500 },
        };
        newEdges.push(edge);
      }

      setNodes(newNodes);
      setEdges(newEdges);
    };

    createScenarioVisualization();
  }, [scenario]);

  const onNodesChange = useCallback(
    (changes) => setNodes((nds) => applyNodeChanges(changes, nds)),
    [setNodes]
  );

  const onEdgesChange = useCallback(
    (changes) => setEdges((eds) => applyEdgeChanges(changes, eds)),
    [setEdges]
  );

  const onConnect = useCallback(
    (connection) => setEdges((eds) => addEdge(connection, eds)),
    [setEdges]
  );

  // Function to reset the view
  const resetView = useCallback(() => {
    if (reactFlowInstance) {
      reactFlowInstance.fitView({ padding: 0.2 });
    }
  }, [reactFlowInstance]);

  // Function to export as PNG
  const exportAsPng = useCallback(() => {
    if (reactFlowWrapper.current === null) return;

    toPng(reactFlowWrapper.current, {
      backgroundColor: '#fff',
      width: reactFlowWrapper.current.offsetWidth,
      height: reactFlowWrapper.current.offsetHeight,
    })
      .then((dataUrl) => {
        const link = document.createElement('a');
        link.download = `scenario-${new Date().toISOString().split('T')[0]}.png`;
        link.href = dataUrl;
        link.click();
      });
  }, []);

  return (
    <div className={`scenario-visualization ${isFullscreen ? 'fullscreen' : ''}`}>
      {/* Debug indicator */}
      <div className="p-2 bg-green-100 border border-green-300 rounded mb-2">
        <p className="text-xs text-green-800">
          ✅ ScenarioVisualization component is rendering | Nodes: {nodes.length} | Edges: {edges.length}
        </p>
      </div>

      {/* Header */}
      <div className={`bg-white border-b border-slate-200 ${isInline ? 'p-3' : 'p-4'}`}>
        <div className="flex items-center justify-between">
          <div>
            <h2 className={`font-bold text-slate-800 flex items-center ${isInline ? 'text-lg' : 'text-xl'}`}>
              <Eye size={isInline ? 20 : 24} className="mr-3 text-indigo-600" />
              {title}
            </h2>
            {!isInline && (
              <p className="text-slate-600 mt-1">
                Visualisation interactive du scénario opérationnel
              </p>
            )}
          </div>
          
          <div className="flex items-center space-x-2">
            <button
              onClick={resetView}
              className="flex items-center px-3 py-2 bg-slate-100 text-slate-700 rounded-lg hover:bg-slate-200 transition duration-200 text-sm"
              title="Réinitialiser la vue"
            >
              <RefreshCw size={16} className="mr-1" />
              Centrer
            </button>
            
            <button
              onClick={exportAsPng}
              className="flex items-center px-3 py-2 bg-blue-100 text-blue-700 rounded-lg hover:bg-blue-200 transition duration-200 text-sm"
              title="Exporter en PNG"
            >
              <Download size={16} className="mr-1" />
              PNG
            </button>
            
            <button
              onClick={() => setIsFullscreen(!isFullscreen)}
              className="flex items-center px-3 py-2 bg-slate-100 text-slate-700 rounded-lg hover:bg-slate-200 transition duration-200 text-sm"
              title={isFullscreen ? "Quitter le plein écran" : "Plein écran"}
            >
              <Maximize2 size={16} />
            </button>
            
            <button
              onClick={onClose}
              className="flex items-center px-3 py-2 bg-red-100 text-red-700 rounded-lg hover:bg-red-200 transition duration-200 text-sm"
              title="Fermer"
            >
              <X size={16} />
            </button>
          </div>
        </div>
      </div>

      {/* Flow diagram */}
      <div className="flex" style={{ height: isFullscreen ? 'calc(100vh - 80px)' : (isInline ? '500px' : '600px') }}>
        <div
          ref={reactFlowWrapper}
          className="flex-grow relative"
        >
          <ReactFlow
            nodes={nodes}
            edges={edges}
            onNodesChange={onNodesChange}
            onEdgesChange={onEdgesChange}
            onConnect={onConnect}
            nodeTypes={nodeTypes}
            fitView
            attributionPosition="bottom-left"
          >
            <Controls />
            <MiniMap
              nodeColor={(n) => {
                if (n.type === 'scenarioHeader') return '#E0E7FF';
                if (n.type === 'scenarioStep') return '#F3F4F6';
                return '#E2E8F0';
              }}
              nodeStrokeWidth={3}
              pannable
              zoomable
            />
            <Background color="#ccc" variant="dots" gap={16} size={1} />
          </ReactFlow>
        </div>
      </div>

      {/* Footer */}
      {!isInline && (
        <div className="p-4 border-t border-slate-200 text-sm text-slate-500 bg-slate-50">
          <p>
            <strong>Visualisation interactive :</strong> Déplacez et explorez les étapes du scénario •
            <strong> Zoom :</strong> Utilisez la molette de la souris ou les contrôles •
            <strong> Export :</strong> Sauvegardez la visualisation en PNG
          </p>
        </div>
      )}
    </div>
  );
};

export default ScenarioVisualization;

// CSS Styles (add to your global CSS or component styles)
const styles = `
.scenario-visualization {
  position: relative;
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.scenario-visualization.fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  border-radius: 0;
}

.react-flow__node-scenarioHeader {
  background: transparent !important;
  border: none !important;
}

.react-flow__node-scenarioStep {
  background: transparent !important;
  border: none !important;
}

.react-flow__edge-path {
  stroke-width: 2;
}

.react-flow__edge-text {
  font-size: 12px;
  font-weight: 500;
}

.react-flow__controls {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.react-flow__controls button {
  background: white;
  border: none;
  border-bottom: 1px solid #e2e8f0;
  color: #64748b;
  transition: all 0.2s;
}

.react-flow__controls button:hover {
  background: #f8fafc;
  color: #334155;
}

.react-flow__controls button:last-child {
  border-bottom: none;
}

.react-flow__minimap {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}
`;

// Inject styles if not already present
if (typeof document !== 'undefined' && !document.getElementById('scenario-visualization-styles')) {
  const styleSheet = document.createElement('style');
  styleSheet.id = 'scenario-visualization-styles';
  styleSheet.textContent = styles;
  document.head.appendChild(styleSheet);
}
