// Translation Management Utility
// This can be extended to work with services like Crowdin, Lokalise, or Phrase

class TranslationManager {
  constructor() {
    this.translations = new Map();
    this.currentLanguage = 'fr';
    this.fallbackLanguage = 'fr';
    this.missingKeys = new Set();
  }

  // Load translations from external service or local files
  async loadTranslations(language) {
    try {
      // Option 1: Load from external API
      // const response = await fetch(`/api/translations/${language}`);
      // const translations = await response.json();
      
      // Option 2: Load from local files
      const translations = await import(`../i18n/locales/${language}.json`);
      
      this.translations.set(language, translations.default);
      return translations.default;
    } catch (error) {
      console.error(`Failed to load translations for ${language}:`, error);
      return {};
    }
  }

  // Get translation with fallback
  t(key, params = {}, language = this.currentLanguage) {
    const translations = this.translations.get(language) || {};
    let value = this.getNestedValue(translations, key);

    // Fallback to default language if not found
    if (!value && language !== this.fallbackLanguage) {
      const fallbackTranslations = this.translations.get(this.fallbackLanguage) || {};
      value = this.getNestedValue(fallbackTranslations, key);
    }

    // Track missing keys for development
    if (!value) {
      this.missingKeys.add(key);
      console.warn(`Missing translation key: ${key}`);
      return key; // Return key as fallback
    }

    // Replace parameters in translation
    return this.interpolate(value, params);
  }

  // Get nested object value using dot notation
  getNestedValue(obj, path) {
    return path.split('.').reduce((current, key) => current?.[key], obj);
  }

  // Replace parameters in translation string
  interpolate(template, params) {
    return template.replace(/\{\{(\w+)\}\}/g, (match, key) => {
      return params[key] !== undefined ? params[key] : match;
    });
  }

  // Change current language
  async setLanguage(language) {
    if (!this.translations.has(language)) {
      await this.loadTranslations(language);
    }
    this.currentLanguage = language;
    
    // Persist language preference
    localStorage.setItem('preferred-language', language);
    
    // Trigger language change event
    window.dispatchEvent(new CustomEvent('languageChanged', { 
      detail: { language } 
    }));
  }

  // Get current language
  getCurrentLanguage() {
    return this.currentLanguage;
  }

  // Get available languages
  getAvailableLanguages() {
    return Array.from(this.translations.keys());
  }

  // Export missing keys for translation
  exportMissingKeys() {
    return Array.from(this.missingKeys);
  }

  // Auto-translate missing keys (using external service)
  async autoTranslate(text, targetLanguage, sourceLanguage = 'fr') {
    try {
      // Example with Google Translate API
      // const response = await fetch('/api/translate', {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify({
      //     text,
      //     source: sourceLanguage,
      //     target: targetLanguage
      //   })
      // });
      // return await response.json();
      
      // Placeholder for auto-translation
      return `[AUTO] ${text}`;
    } catch (error) {
      console.error('Auto-translation failed:', error);
      return text;
    }
  }
}

// Create singleton instance
const translationManager = new TranslationManager();

// Initialize with saved language preference
const savedLanguage = localStorage.getItem('preferred-language') || 'fr';
translationManager.setLanguage(savedLanguage);

export default translationManager;
