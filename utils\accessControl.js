// utils/accessControl.js
const mongoose = require('mongoose');
const Analysis = require('../models/Analysis');

/**
 * Check if a user has access to an analysis
 * @param {string} analysisId - The ID of the analysis
 * @param {string} userId - The ID of the user
 * @param {string} userRole - The role of the user
 * @param {string} userCompanyId - The company ID of the user
 * @returns {Object} - Object with success flag and message
 */
exports.checkAnalysisAccess = async (analysisId, userId, userRole, userCompanyId) => {
  try {
    // Validate analysisId
    if (!mongoose.Types.ObjectId.isValid(analysisId)) {
      return {
        success: false,
        status: 400,
        message: 'Invalid analysis ID'
      };
    }

    // Get the analysis
    const analysis = await Analysis.findById(analysisId);

    // Check if analysis exists
    if (!analysis) {
      return {
        success: false,
        status: 404,
        message: 'Analysis not found'
      };
    }

    // Admin has access to all analyses
    if (userRole === 'admin') {
      return {
        success: true
      };
    }

    // Check if user is the owner of the analysis
    if (analysis.createdBy.toString() === userId) {
      return {
        success: true
      };
    }

    // Check if user is in the same company as the analysis
    if (analysis.companyId && userCompanyId && analysis.companyId.toString() === userCompanyId.toString()) {
      return {
        success: true
      };
    }

    // Check if user is in the sharedWith array
    if (analysis.sharedWith && analysis.sharedWith.includes(userId)) {
      return {
        success: true
      };
    }

    // User does not have access
    return {
      success: false,
      status: 403,
      message: 'You do not have permission to access this analysis'
    };
  } catch (error) {
    console.error('Error checking analysis access:', error);
    return {
      success: false,
      status: 500,
      message: 'Server error'
    };
  }
};

/**
 * Simplified version of checkAnalysisAccess that returns a boolean
 * Used by controllers that need a simple yes/no answer
 * @param {string} userId - The ID of the user
 * @param {string} analysisId - The ID of the analysis
 * @returns {Promise<boolean>} - True if access is allowed, false otherwise
 */
exports.userHasAnalysisAccess = async (userId, analysisId) => {
  try {
    // Validate analysisId
    if (!mongoose.Types.ObjectId.isValid(analysisId)) {
      return false;
    }

    // Get the analysis
    const analysis = await Analysis.findById(analysisId);

    // Check if analysis exists
    if (!analysis) {
      return false;
    }

    // Check if user is the owner of the analysis
    if (analysis.createdBy && analysis.createdBy.toString() === userId) {
      return true;
    }

    // Check if user is in the same company as the analysis
    if (analysis.companyId && analysis.companyId.toString() === userId) {
      return true;
    }

    // Check if user is in the sharedWith array
    if (analysis.sharedWith && analysis.sharedWith.includes(userId)) {
      return true;
    }

    return false;
  } catch (error) {
    console.error('Error checking analysis access:', error);
    return false;
  }
};
