import React, { useRef, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';

const EnhancedRadarVisualization = ({ couples, onCoupleSelectionChange }) => {
  const { t } = useTranslation();
  const sourcesChartRef = useRef(null);
  const objectifsChartRef = useRef(null);

  // Chart dimensions - simplify sizing
  const chartSize = 500;
  const centerX = chartSize / 2;
  const centerY = chartSize / 2;
  const maxRadius = chartSize * 0.4;

  // Simplified levels - always 3 levels
  const maxLevel = 3;

  // Invert radius for more intuitive visualization
  // Higher level/danger is closer to the center
  const invertRadius = true;

  // Risk source categories with emojis and distinct colors - using the same colors as in ThreatCategories.js
  const riskSectors = [
    { name: '🏛️ ÉTATIQUE', color: '#DC2626', startAngle: 0, endAngle: 45 },          // red-600
    { name: '🔪 CRIME ORGANISÉ', color: '#9333EA', startAngle: 45, endAngle: 90 },    // purple-600
    { name: '💣 TERRORISTE', color: '#F97316', startAngle: 90, endAngle: 135 },       // orange-600 (amber in ThreatCategories)
    { name: '✊ ACTIVISTE IDÉOLOGIQUE', color: '#16A34A', startAngle: 135, endAngle: 180 }, // green-600
    { name: '🕵️ OFFICINE SPÉCIALISÉE', color: '#2563EB', startAngle: 180, endAngle: 225 }, // blue-600
    { name: '🎮 AMATEUR', color: '#CA8A04', startAngle: 225, endAngle: 270 },         // yellow-600
    { name: '😡 VENGEUR', color: '#DB2777', startAngle: 270, endAngle: 315 },         // pink-600
    { name: '🦹 MALVEILLANT PATHOLOGIQUE', color: '#4F46E5', startAngle: 315, endAngle: 360 } // indigo-600
  ];

  const objectiveSectors = [
    { name: 'ESPIONNAGE', color: '#EC4899', startAngle: 0, endAngle: 60 },
    { name: 'PRÉPOSITIONNEMENT STRATÉGIQUE', color: '#8B5CF6', startAngle: 60, endAngle: 120 },
    { name: 'INFLUENCE', color: '#3B82F6', startAngle: 120, endAngle: 180 },
    { name: 'ENTRAVE AU FONCTIONNEMENT', color: '#10B981', startAngle: 180, endAngle: 240 },
    { name: 'LUCRATIF', color: '#F59E0B', startAngle: 240, endAngle: 300 },
    { name: 'DÉFI, AMUSEMENT', color: '#EF4444', startAngle: 300, endAngle: 360 }
  ];

  // Category mapping for risk sources
  const categoryToSector = (category) => {
    if (!category) return '🏛️ ÉTATIQUE';

    const lowerCategory = category.toLowerCase();

    // Map categories to sector names
    if (lowerCategory.includes('etatique')) return '🏛️ ÉTATIQUE';
    if (lowerCategory.includes('criminel') || lowerCategory.includes('crime') || lowerCategory.includes('organise')) return '🔪 CRIME ORGANISÉ';
    if (lowerCategory.includes('terroriste')) return '💣 TERRORISTE';
    if (lowerCategory.includes('activiste') || lowerCategory.includes('ideologique')) return '✊ ACTIVISTE IDÉOLOGIQUE';
    if (lowerCategory.includes('officine') || lowerCategory.includes('specialisee')) return '🕵️ OFFICINE SPÉCIALISÉE';
    if (lowerCategory.includes('amateur')) return '🎮 AMATEUR';
    if (lowerCategory.includes('vengeur')) return '😡 VENGEUR';
    if (lowerCategory.includes('malveillant') || lowerCategory.includes('pathologique')) return '🦹 MALVEILLANT PATHOLOGIQUE';

    // Default fallback
    return '🏛️ ÉTATIQUE';
  };

  const objectiveToSector = (category) => {
    if (!category) return 'ESPIONNAGE';

    const lowerCategory = category.toLowerCase();

    // Handle variations of category names
    if (lowerCategory.includes('espionnage')) return 'ESPIONNAGE';
    if (lowerCategory.includes('prepositionnement') || lowerCategory.includes('stratégique') || lowerCategory.includes('strategique')) return 'PRÉPOSITIONNEMENT STRATÉGIQUE';
    if (lowerCategory.includes('influence')) return 'INFLUENCE';
    if (lowerCategory.includes('entrave') || lowerCategory.includes('fonctionnement')) return 'ENTRAVE AU FONCTIONNEMENT';
    if (lowerCategory.includes('lucratif') || lowerCategory.includes('financier')) return 'LUCRATIF';
    if (lowerCategory.includes('défi') || lowerCategory.includes('defi') || lowerCategory.includes('amusement')) return 'DÉFI, AMUSEMENT';

    // Default fallback
    return 'ESPIONNAGE';
  };

  // Get sector angle - simplified
  const getSectorAngle = (sectorName, sectors) => {
    const sector = sectors.find(s => s.name === sectorName);
    return sector ? (sector.startAngle + sector.endAngle) / 2 : 0;
  };

  // Simplified level calculation from 1-3
  const calculateNiveau = (motivation, activite, ressources) => {
    // Normalize inputs to handle different grammatical forms
    const normalizedMotivation = String(motivation || '').toLowerCase();
    const normalizedActivite = String(activite || '').toLowerCase();
    const normalizedRessources = String(ressources || '').toLowerCase();

    // Convert string values to numeric scores
    const motivationScore = normalizedMotivation.startsWith('elev') ? 3 :
                           normalizedMotivation.startsWith('moyen') ? 2 : 1;

    const activiteScore = normalizedActivite.startsWith('elev') ? 3 :
                         normalizedActivite.startsWith('moyen') ? 2 : 1;

    const ressourcesScore = normalizedRessources.startsWith('import') ? 3 :
                           normalizedRessources.startsWith('moyen') ? 2 : 1;

    // Calculate average and round to nearest integer
    const avgScore = (motivationScore + activiteScore + ressourcesScore) / 3;
    return Math.round(avgScore);
  };

  // Process data for "Vision par objectif visé" chart - showing SR points
  const processRiskPoints = () => {
    const points = [];

    couples.forEach((item, index) => {
      // For the "Vision par objectif visé" chart, we want to show the source de risque points
      // So we use the sourceCategory to determine the sector
      const sourceSector = categoryToSector(item.sourceCategory);
      const angle = getSectorAngle(sourceSector, riskSectors);

      // Use niveau directly if available, otherwise calculate it
      const level = item.niveau || calculateNiveau(item.motivation, item.activite, item.ressources);

      points.push({
        id: item.id,
        sourceId: item.sourceId,
        sourceName: item.sourceName,
        sourceCategory: item.sourceCategory,
        name: item.sourceName || 'Source sans nom',
        displayId: `SR${index + 1}`,
        level: level,
        angle: angle,
        retained: item.selected,
        sector: sourceSector, // Use the source sector
        objectif: item.objectifVise,
        objectifCategory: item.objectifViseCategory,
        originalData: item
      });
    });

    return points;
  };

  // Process data for "Vision par source de risque" chart - showing OV points
  const processObjectivePoints = () => {
    const points = [];

    couples.forEach((item, index) => {
      // For the "Vision par source de risque" chart, we want to show the objectif visé points
      // So we use the objectifViseCategory to determine the sector
      const objectifSector = objectiveToSector(item.objectifViseCategory);
      const angle = getSectorAngle(objectifSector, objectiveSectors);

      // Use niveau directly if available, otherwise calculate it
      const level = item.niveau || calculateNiveau(item.motivation, item.activite, item.ressources);

      points.push({
        id: item.sourceId,
        objectifVise: item.objectifVise,
        objectifCategory: item.objectifViseCategory,
        name: item.objectifVise || 'Objectif sans nom',
        displayId: `OV${index + 1}`,
        level: level,
        angle: angle,
        retained: item.selected,
        sector: objectifSector, // Use the objectif sector
        sourceCategory: item.sourceCategory,
        originalData: item
      });
    });

    return points;
  };

  // Simplified position calculation with collision prevention
  const pointsToCartesian = (points) => {
    // Group points by sector and level
    const groupedPoints = {};

    points.forEach(point => {
      const key = `${point.sector}-${point.level}`;
      if (!groupedPoints[key]) {
        groupedPoints[key] = [];
      }
      groupedPoints[key].push(point);
    });

    // Process each group
    const processedPoints = [];

    Object.entries(groupedPoints).forEach(([key, group]) => {
      // Radius calculation - simplified and consistent
      const level = parseInt(key.split('-')[1]);

      // With inversion: level 3 (highest) is closer to center
      const radius = invertRadius
        ? ((maxLevel + 1 - level) / maxLevel) * maxRadius
        : (level / maxLevel) * maxRadius;

      if (group.length === 1) {
        // Single point - simple placement
        const point = group[0];
        const angleRad = (point.angle * Math.PI) / 180;

        processedPoints.push({
          ...point,
          x: centerX + radius * Math.cos(angleRad),
          y: centerY + radius * Math.sin(angleRad)
        });
      } else {
        // Multiple points - distribute around base angle
        const baseAngle = group[0].angle;
        const angleSpread = Math.min(10, 40 / group.length); // Adaptive spread
        const totalSpread = (group.length - 1) * angleSpread;
        const startAngle = baseAngle - totalSpread / 2;

        group.forEach((point, index) => {
          const adjustedAngle = startAngle + index * angleSpread;
          const angleRad = (adjustedAngle * Math.PI) / 180;

          processedPoints.push({
            ...point,
            x: centerX + radius * Math.cos(angleRad),
            y: centerY + radius * Math.sin(angleRad)
          });
        });
      }
    });

    return processedPoints;
  };

  // State for points and UI control
  const [riskPoints, setRiskPoints] = useState([]);
  const [objectivePoints, setObjectivePoints] = useState([]);
  const [draggedPoint, setDraggedPoint] = useState(null);
  const [activeDataTable, setActiveDataTable] = useState('sources'); // 'sources' or 'objectifs'

  // Update points when data changes
  useEffect(() => {
    setRiskPoints(processRiskPoints());
    setObjectivePoints(processObjectivePoints());
  }, [couples]);

  // Start dragging a point
  const startDrag = (pointId, pointIndex, isSourcesChart, e) => {
    e.preventDefault();
    setDraggedPoint({
      id: pointId,
      index: pointIndex,
      isRiskTab: isSourcesChart
    });
  };

  // Handle dragging with immediate table updates
  useEffect(() => {
    if (!draggedPoint) return;

    const handleMouseMove = (e) => {
      const svg = draggedPoint.isRiskTab ? sourcesChartRef.current : objectifsChartRef.current;
      if (!svg) return;

      const rect = svg.getBoundingClientRect();

      // Calculate mouse position relative to chart center
      const mouseX = e.clientX - rect.left - centerX;
      const mouseY = e.clientY - rect.top - centerY;

      // Calculate distance from center
      const distance = Math.sqrt(mouseX * mouseX + mouseY * mouseY);

      // Calculate level based on distance (1-3)
      let level;
      if (invertRadius) {
        // Closer to center = higher level
        if (distance < maxRadius / 3) level = 3;
        else if (distance < maxRadius * 2/3) level = 2;
        else level = 1;
      } else {
        // Further from center = higher level
        if (distance > maxRadius * 2/3) level = 3;
        else if (distance > maxRadius / 3) level = 2;
        else level = 1;
      }

      // Get the point and its originalData using the ID for more reliable identification
      const pointsArray = draggedPoint.isRiskTab ? riskPoints : objectivePoints;
      // Find the point by its ID instead of index
      const point = pointsArray.find(p => p.id === draggedPoint.id);

      if (point && point.originalData) {
        // Update the originalData directly to ensure table consistency
        point.originalData.niveau = level;

        // Also update activite, motivation, and ressources based on level
        if (level === 3) {
          point.originalData.activite = 'eleve';
          point.originalData.motivation = 'eleve';
          point.originalData.ressources = 'importantes';
        } else if (level === 2) {
          point.originalData.activite = 'moyen';
          point.originalData.motivation = 'moyen';
          point.originalData.ressources = 'moyennes';
        } else {
          point.originalData.activite = 'faible';
          point.originalData.motivation = 'faible';
          point.originalData.ressources = 'faibles';
        }
      }

      // Update local state with the new level using ID for reliable updates
      if (draggedPoint.isRiskTab) {
        setRiskPoints(current => {
          return current.map(p => {
            if (p.id === draggedPoint.id) {
              return { ...p, level: level };
            }
            return p;
          });
        });
      } else {
        setObjectivePoints(current => {
          return current.map(p => {
            if (p.id === draggedPoint.id) {
              return { ...p, level: level };
            }
            return p;
          });
        });
      }

      // Call parent callback to update the data model
      if (onCoupleSelectionChange && point?.id) {
        // Update both niveau and the related properties
        onCoupleSelectionChange(point.id, 'niveau', level);

        // Update dependent fields that appear in the table
        if (level === 3) {
          onCoupleSelectionChange(point.id, 'activite', 'eleve');
          onCoupleSelectionChange(point.id, 'motivation', 'eleve');
          onCoupleSelectionChange(point.id, 'ressources', 'importantes');
        } else if (level === 2) {
          onCoupleSelectionChange(point.id, 'activite', 'moyen');
          onCoupleSelectionChange(point.id, 'motivation', 'moyen');
          onCoupleSelectionChange(point.id, 'ressources', 'moyennes');
        } else {
          onCoupleSelectionChange(point.id, 'activite', 'faible');
          onCoupleSelectionChange(point.id, 'motivation', 'faible');
          onCoupleSelectionChange(point.id, 'ressources', 'limitees');
        }
      }
    };

    const handleMouseUp = () => {
      setDraggedPoint(null);
    };

    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);

    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };
  }, [draggedPoint, riskPoints, objectivePoints, onCoupleSelectionChange]);

  // Toggle point selection
  const toggleRetention = (pointIndex) => {
    const isSourcesTab = activeDataTable === 'sources';
    const points = isSourcesTab ? riskPoints : objectivePoints;
    const point = points[pointIndex];
    const newRetained = !point.retained;

    // Update local state
    if (isSourcesTab) {
      setRiskPoints(current => {
        const newPoints = [...current];
        newPoints[pointIndex].retained = newRetained;
        return newPoints;
      });
    } else {
      setObjectivePoints(current => {
        const newPoints = [...current];
        newPoints[pointIndex].retained = newRetained;
        return newPoints;
      });
    }

    // Update parent data
    if (onCoupleSelectionChange && point) {
      onCoupleSelectionChange(point.id, 'selected', newRetained);
    }
  };

  // Render single chart
  const renderChart = (isSourcesChart) => {
    // For the "Vision par objectif visé" chart, we want to show risk sectors
    // For the "Vision par source de risque" chart, we want to show objectif sectors
    const sectors = isSourcesChart ? riskSectors : objectiveSectors;
    const points = isSourcesChart ? riskPoints : objectivePoints;
    const chartRef = isSourcesChart ? sourcesChartRef : objectifsChartRef;

    // Use all sectors instead of just active ones
    const activeSectors = sectors;

    // Calculate point positions
    const cartesianPoints = pointsToCartesian(points);

    // Background color
    const bgColor = isSourcesChart ? 'bg-gray-100' : 'bg-blue-50';

    return (
      <div className={`chart-container ${bgColor} p-4 rounded-lg flex-1`}>
        <h2 className="text-xl font-bold text-center mb-2">
          {isSourcesChart ? t('workshop2.activity2.visualization.visionByObjective') : t('workshop2.activity2.visualization.visionBySource')}
        </h2>
        <div className="text-sm text-center mb-2">
          {isSourcesChart
            ? t('workshop2.activity2.visualization.pointsRepresentSources')
            : t('workshop2.activity2.visualization.pointsRepresentObjectives')}
        </div>

        {/* Sector legend - only showing categories present in the data */}
        <div className="flex flex-wrap justify-center gap-1 mb-3">
          {(() => {
            // Get unique sector names from the points
            const points = isSourcesChart ? riskPoints : objectivePoints;
            const uniqueSectors = [...new Set(points.map(p => p.sector))];

            // Filter sectors to only those that are present in the data
            const presentSectors = activeSectors.filter(s => uniqueSectors.includes(s.name));

            return presentSectors.map(sector => (
              <div key={sector.name} className="flex items-center mx-1">
                <div
                  className="w-3 h-3 rounded-sm mr-1"
                  style={{ backgroundColor: sector.color, opacity: 0.7 }}
                ></div>
                <span className="text-xs">{sector.name}</span>
              </div>
            ));
          })()}
        </div>

        <svg
          ref={chartRef}
          width={chartSize}
          height={chartSize}
          viewBox={`0 0 ${chartSize} ${chartSize}`}
          className="mx-auto"
        >
          {/* Render level circles with zone colors */}
          <circle
            cx={centerX}
            cy={centerY}
            r={maxRadius * 1/3}
            fill="#FFEBEE"
            stroke="#E53935"
            strokeWidth="1.5"
            opacity="0.6"
          />
          <circle
            cx={centerX}
            cy={centerY}
            r={maxRadius * 2/3}
            fill="#FFF8E1"
            stroke="#FFA000"
            strokeWidth="1.5"
            opacity="0.6"
          />
          <circle
            cx={centerX}
            cy={centerY}
            r={maxRadius}
            fill="#E8F5E9"
            stroke="#43A047"
            strokeWidth="1.5"
            opacity="0.6"
          />

          {/* Zone labels */}
          <text
            x={centerX - 15}
            y={centerY - (maxRadius * 1/6)}
            fontSize="11"
            fontWeight="bold"
            fill="#B71C1C"
            textAnchor="end"
          >
{t('workshop2.activity2.visualization.criticalZone')}
          </text>
          <text
            x={centerX - 15}
            y={centerY - (maxRadius * 1/2)}
            fontSize="11"
            fontWeight="bold"
            fill="#E65100"
            textAnchor="end"
          >
{t('workshop2.activity2.visualization.moderateZone')}
          </text>
          <text
            x={centerX - 15}
            y={centerY - (maxRadius * 5/6)}
            fontSize="11"
            fontWeight="bold"
            fill="#1B5E20"
            textAnchor="end"
          >
{t('workshop2.activity2.visualization.lowZone')}
          </text>

          {/* Level labels */}
          {[1, 2, 3].map((level, index) => {
            const displayLevel = invertRadius ? maxLevel + 1 - level : level;
            return (
              <text
                key={`level-${level}`}
                x={centerX + 10}
                y={centerY - (maxRadius * (index + 1) / maxLevel)}
                fontSize="12"
                fontWeight="bold"
                fill="#333"
              >
{t('workshop2.activity2.visualization.level')} {displayLevel}
              </text>
            );
          })}

          {/* Render sectors */}
          {activeSectors.map(sector => {
            // Calculate sector path
            const startAngle = (sector.startAngle * Math.PI) / 180;
            const endAngle = (sector.endAngle * Math.PI) / 180;

            const x1 = centerX + maxRadius * Math.cos(startAngle);
            const y1 = centerY + maxRadius * Math.sin(startAngle);
            const x2 = centerX + maxRadius * Math.cos(endAngle);
            const y2 = centerY + maxRadius * Math.sin(endAngle);

            const largeArcFlag = Math.abs(sector.endAngle - sector.startAngle) > 180 ? 1 : 0;

            const pathData = `
              M ${centerX} ${centerY}
              L ${x1} ${y1}
              A ${maxRadius} ${maxRadius} 0 ${largeArcFlag} 1 ${x2} ${y2}
              Z
            `;

            // Calculate label position
            const midAngle = ((sector.startAngle + sector.endAngle) / 2) * Math.PI / 180;
            const labelRadius = maxRadius * 1.1;
            const labelX = centerX + labelRadius * Math.cos(midAngle);
            const labelY = centerY + labelRadius * Math.sin(midAngle);

            return (
              <React.Fragment key={sector.name}>
                {/* Sector area */}
                <path
                  d={pathData}
                  fill={sector.color}
                  opacity="0.4"
                  stroke="#333"
                  strokeWidth="1.5"
                />

                {/* Boundary lines */}
                <line
                  x1={centerX}
                  y1={centerY}
                  x2={x1}
                  y2={y1}
                  stroke="#333"
                  strokeWidth="1.5"
                  strokeDasharray="3,3"
                />
                <line
                  x1={centerX}
                  y1={centerY}
                  x2={x2}
                  y2={y2}
                  stroke="#333"
                  strokeWidth="1.5"
                  strokeDasharray="3,3"
                />

                {/* Sector label */}
                <text
                  x={labelX}
                  y={labelY}
                  textAnchor="middle"
                  fontSize="12"
                  fontWeight="bold"
                  fill="#000000"
                  style={{ textShadow: '0px 0px 3px white' }}
                >
                  {sector.name}
                </text>
              </React.Fragment>
            );
          })}

          {/* Render points */}
          {cartesianPoints.map((point, index) => {
            // Red for retained, green for not retained (inverted as requested)
            const fillColor = point.retained ? '#ef4444' : '#22c55e';
            const borderColor = point.retained ? '#dc2626' : '#16a34a';

            const isBeingDragged = draggedPoint &&
                                draggedPoint.isRiskTab === isSourcesChart &&
                                draggedPoint.id === point.id;

            return (
              <g key={`point-${point.id}-${index}`}>
                {/* Highlight circle for dragging feedback */}
                {isBeingDragged && (
                  <circle
                    cx={point.x}
                    cy={point.y}
                    r={18}
                    fill="rgba(59, 130, 246, 0.3)"
                    stroke="#3b82f6"
                    strokeWidth={1}
                    strokeDasharray="3,3"
                  />
                )}

                {/* Simple circle */}
                <circle
                  cx={point.x}
                  cy={point.y}
                  r={12}
                  fill={fillColor}
                  stroke={borderColor}
                  strokeWidth={isBeingDragged ? 3 : 2}
                  style={{ cursor: isBeingDragged ? 'grabbing' : 'grab' }}
                  onMouseDown={(e) => startDrag(point.id, index, isSourcesChart, e)}
                />

                {/* Point ID text */}
                <text
                  x={point.x}
                  y={point.y + 4}
                  fontSize="10"
                  fontWeight="bold"
                  textAnchor="middle"
                  fill="white"
                  pointerEvents="none"
                >
                  {point.displayId}
                </text>
              </g>
            );
          })}

          {/* No relevance indicator as requested */}
        </svg>

        <div className="mt-4 p-3 bg-white rounded shadow-sm text-sm">
          <div className="font-bold mb-1 text-center">{t('workshop2.activity2.visualization.interpretationGuide')}</div>
          <div className="mb-2 text-center">
            {isSourcesChart
              ? t('workshop2.activity2.visualization.dragToModifyLevel')
              : t('workshop2.activity2.visualization.dragToModifyImpact')}
          </div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-2 mt-2">
            <div className="flex items-center">
              <div className="w-4 h-4 rounded-full bg-red-100 border border-red-500 mr-2 flex-shrink-0"></div>
              <span><strong>{t('workshop2.activity2.visualization.criticalZone')}</strong>: {t('workshop2.activity2.visualization.level')} 3 - {t('workshop2.activity2.visualization.priorityAttention')}</span>
            </div>
            <div className="flex items-center">
              <div className="w-4 h-4 rounded-full bg-amber-100 border border-amber-500 mr-2 flex-shrink-0"></div>
              <span><strong>{t('workshop2.activity2.visualization.moderateZone')}</strong>: {t('workshop2.activity2.visualization.level')} 2 - {t('workshop2.activity2.visualization.vigilanceRecommended')}</span>
            </div>
            <div className="flex items-center">
              <div className="w-4 h-4 rounded-full bg-green-100 border border-green-500 mr-2 flex-shrink-0"></div>
              <span><strong>{t('workshop2.activity2.visualization.lowZone')}</strong>: {t('workshop2.activity2.visualization.level')} 1 - {t('workshop2.activity2.visualization.limitedRisk')}</span>
            </div>
          </div>

          <div className="mt-3 text-xs text-gray-700 border-t pt-2">
            <p className="mb-1"><strong>{t('workshop2.activity2.visualization.interpretation')}:</strong> {isSourcesChart
              ? t('workshop2.activity2.visualization.interpretationSources')
              : t('workshop2.activity2.visualization.interpretationObjectives')
            }</p>
            <p>{t('workshop2.activity2.visualization.closerToCenter')}</p>
          </div>
          <div className="mt-2 text-center text-xs text-gray-600">
            <span className="inline-block px-2 py-1 bg-red-100 rounded-full mr-2">{t('workshop2.activity2.visualization.retained')}</span>
            <span className="inline-block px-2 py-1 bg-green-100 rounded-full">{t('workshop2.activity2.visualization.notRetained')}</span>
          </div>
        </div>
      </div>
    );
  };

  // Render data table
  const renderDataTable = () => {
    const isSourcesTab = activeDataTable === 'sources';
    const points = isSourcesTab ? riskPoints : objectivePoints;

    return (
      <div className="overflow-x-auto p-4">
        <table className="min-w-full table-auto border-collapse">
          <thead>
            <tr className="bg-gray-50 border-b border-gray-200">
              <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{t('workshop2.activity2.visualization.dataTable.id')}</th>
              <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{t('workshop2.activity2.visualization.dataTable.name')}</th>
              <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{t('workshop2.activity2.visualization.dataTable.status')}</th>
              <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{t('workshop2.activity2.visualization.dataTable.level')}</th>
              <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{t('workshop2.activity2.visualization.dataTable.sector')}</th>
              {activeDataTable === 'sources' ? (
                <>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{t('workshop2.activity2.visualization.dataTable.activity')}</th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{t('workshop2.activity2.visualization.dataTable.motivation')}</th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{t('workshop2.activity2.visualization.dataTable.resources')}</th>
                </>
              ) : (
                <>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{t('workshop2.activity2.visualization.dataTable.objective')}</th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{t('workshop2.activity2.visualization.dataTable.category')}</th>
                </>
              )}
              <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{t('workshop2.activity2.visualization.dataTable.actions')}</th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {points.map((point, index) => (
              <tr
                key={`row-${index}`}
                className={`transition-colors hover:bg-gray-50 ${point.retained ? 'bg-red-50' : 'bg-green-50'}`}
              >
                <td className="px-4 py-3 text-sm font-medium text-gray-900">{point.displayId}</td>
                <td className="px-4 py-3 text-sm text-gray-700">{point.name}</td>
                <td className="px-4 py-3 text-sm">
                  <span
                    className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                      point.retained
                        ? 'bg-red-100 text-red-800'
                        : 'bg-green-100 text-green-800'
                    }`}
                  >
{point.retained ? t('workshop2.activity2.visualization.retained') : t('workshop2.activity2.visualization.notRetained')}
                  </span>
                </td>
                <td className="px-4 py-3 text-sm font-bold text-center">
                  <div className="flex flex-col items-center">
                    <span className={`inline-flex items-center justify-center w-6 h-6 rounded-full ${
                      point.level === 3
                        ? 'bg-red-100 text-red-800'
                        : point.level === 2
                          ? 'bg-amber-100 text-amber-800'
                          : 'bg-green-100 text-green-800'
                    }`}>
                      {point.level}
                    </span>
                    <span className="text-xs text-gray-500 mt-1">
{point.level === 3 ? t('workshop2.activity2.visualization.dataTable.critical') : point.level === 2 ? t('workshop2.activity2.visualization.dataTable.moderate') : t('workshop2.activity2.visualization.dataTable.low')}
                    </span>
                  </div>
                </td>
                <td className="px-4 py-3 text-sm text-gray-700">{point.sector}</td>

                {activeDataTable === 'sources' && point.originalData ? (
                  <>
                    <td className="px-4 py-3 text-sm text-gray-700 capitalize">{point.originalData.activite}</td>
                    <td className="px-4 py-3 text-sm text-gray-700 capitalize">{point.originalData.motivation}</td>
                    <td className="px-4 py-3 text-sm text-gray-700 capitalize">{point.originalData.ressources}</td>
                  </>
                ) : (
                  <>
                    <td className="px-4 py-3 text-sm text-gray-700">{point.objectif || point.objectifVise}</td>
                    <td className="px-4 py-3 text-sm text-gray-700">{point.objectifCategory || point.objectifViseCategory}</td>
                  </>
                )}

                <td className="px-4 py-3 text-sm text-gray-500">
                  <div className="flex space-x-2">
                    <button
                      onClick={() => toggleRetention(index)}
                      className={`inline-flex items-center px-2 py-1 border rounded-md text-xs font-medium transition-colors ${
                        point.retained
                          ? 'border-green-300 text-green-700 bg-white hover:bg-green-50'
                          : 'border-red-300 text-red-700 bg-white hover:bg-red-50'
                      }`}
                    >
{point.retained ? t('workshop2.activity2.visualization.dataTable.remove') : t('workshop2.activity2.visualization.dataTable.retain')}
                    </button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    );
  };

  return (
    <div className="radar-visualization">
      {/* Both charts side by side */}
      <div className="flex flex-col lg:flex-row gap-4">
        {renderChart(true)}
        {renderChart(false)}
      </div>

      {/* Tab selector for data table */}
      <div className="flex border-b border-gray-200 mt-6">
        <button
          className={`py-2 px-4 font-medium text-sm ${
            activeDataTable === 'sources'
              ? 'border-b-2 border-blue-500 text-blue-600'
              : 'text-gray-500 hover:text-gray-700'
          }`}
          onClick={() => setActiveDataTable('sources')}
        >
{t('workshop2.activity2.visualization.dataTable.riskSources')}
        </button>
        <button
          className={`py-2 px-4 font-medium text-sm ${
            activeDataTable === 'objectifs'
              ? 'border-b-2 border-blue-500 text-blue-600'
              : 'text-gray-500 hover:text-gray-700'
          }`}
          onClick={() => setActiveDataTable('objectifs')}
        >
{t('workshop2.activity2.visualization.dataTable.targetObjectives')}
        </button>
      </div>

      {/* Data table below */}
      {renderDataTable()}
    </div>
  );
};

export default EnhancedRadarVisualization;