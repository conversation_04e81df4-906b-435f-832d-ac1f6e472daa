// test-simple-nist-search.js
// Test script to verify the simplified NIST keyword search

const axios = require('axios');

const BASE_URL = 'http://localhost:5000/api/nist/search';

async function testSimpleNISTSearch() {
  console.log('🧪 Testing Simplified NIST Keyword Search...\n');

  // Test cases based on NIST API documentation examples
  const testCases = [
    {
      name: 'Microsoft Products',
      keywords: 'Microsoft',
      description: 'Search for CVEs mentioning Microsoft'
    },
    {
      name: 'Multiple Keywords (AND logic)',
      keywords: 'Windows MacOs Linux',
      description: 'Search for CVEs mentioning Windows, MacOs, AND Linux'
    },
    {
      name: 'Oracle Database',
      keywords: 'Oracle MySQL',
      description: 'Search for CVEs mentioning Oracle MySQL'
    },
    {
      name: 'Cisco Network Equipment',
      keywords: 'Cisco Router',
      description: 'Search for CVEs mentioning Cisco Router'
    },
    {
      name: 'Apache Web Server',
      keywords: 'Apache HTTP',
      description: 'Search for CVEs mentioning Apache HTTP'
    },
    {
      name: 'VMware Virtualization',
      keywords: 'VMware vSphere',
      description: 'Search for CVEs mentioning VMware vSphere'
    }
  ];

  for (const testCase of testCases) {
    console.log(`\n📋 Testing: ${testCase.name}`);
    console.log(`   Keywords: "${testCase.keywords}"`);
    console.log(`   Description: ${testCase.description}`);

    try {
      const startTime = Date.now();
      
      const response = await axios.get(BASE_URL, {
        params: {
          keywordSearch: testCase.keywords,
          resultsPerPage: 10,
          startIndex: 0
        },
        timeout: 15000
      });

      const endTime = Date.now();
      const duration = endTime - startTime;

      if (response.status === 200) {
        const data = response.data;
        console.log(`✅ SUCCESS (${duration}ms)`);
        console.log(`   Total Results: ${data.totalResults || 0}`);
        console.log(`   Returned: ${data.vulnerabilities?.length || 0} vulnerabilities`);
        
        if (data.vulnerabilities && data.vulnerabilities.length > 0) {
          const firstCVE = data.vulnerabilities[0];
          console.log(`   First CVE: ${firstCVE.cve.id}`);
          console.log(`   Description: ${firstCVE.cve.descriptions[0]?.value.substring(0, 100)}...`);
        }
      } else {
        console.log(`⚠️ Unexpected status: ${response.status}`);
      }

    } catch (error) {
      if (error.response) {
        console.log(`❌ HTTP Error: ${error.response.status} - ${error.response.statusText}`);
        if (error.response.data && error.response.data.message) {
          console.log(`   Error Message: ${error.response.data.message}`);
        }
        if (error.response.data && error.response.data.example) {
          console.log(`   Example: ${error.response.data.example}`);
        }
      } else if (error.code === 'ECONNREFUSED') {
        console.log(`❌ Connection refused - backend server not running`);
        break; // No point testing other cases
      } else {
        console.log(`❌ Error: ${error.message}`);
      }
    }

    // Add delay between requests to respect rate limiting
    await new Promise(resolve => setTimeout(resolve, 2000));
  }

  // Test error cases
  console.log('\n🚫 Testing Error Cases...\n');

  // Test missing keyword parameter
  try {
    console.log('Testing missing keywordSearch parameter...');
    const response = await axios.get(BASE_URL, {
      params: {
        resultsPerPage: 5
      }
    });
    console.log('❌ Should have failed but didn\'t');
  } catch (error) {
    if (error.response && error.response.status === 400) {
      console.log('✅ Correctly rejected missing keywordSearch parameter');
      console.log(`   Message: ${error.response.data.message}`);
    } else {
      console.log(`❌ Unexpected error: ${error.message}`);
    }
  }

  // Test empty keyword parameter
  try {
    console.log('\nTesting empty keywordSearch parameter...');
    const response = await axios.get(BASE_URL, {
      params: {
        keywordSearch: '',
        resultsPerPage: 5
      }
    });
    console.log('❌ Should have failed but didn\'t');
  } catch (error) {
    if (error.response && error.response.status === 400) {
      console.log('✅ Correctly rejected empty keywordSearch parameter');
      console.log(`   Message: ${error.response.data.message}`);
    } else {
      console.log(`❌ Unexpected error: ${error.message}`);
    }
  }

  // Test special characters handling
  try {
    console.log('\nTesting special characters handling...');
    const response = await axios.get(BASE_URL, {
      params: {
        keywordSearch: 'Microsoft@#$%^&*()',
        resultsPerPage: 5
      }
    });
    
    if (response.status === 200) {
      console.log('✅ Special characters handled gracefully');
      console.log(`   Results: ${response.data.totalResults || 0}`);
    }
  } catch (error) {
    console.log(`⚠️ Special characters caused error: ${error.message}`);
  }

  console.log('\n🏁 Simple NIST search testing completed!');
  console.log('\n📊 Summary:');
  console.log('   - NIST API uses simple keywordSearch parameter');
  console.log('   - Multiple keywords work as AND logic');
  console.log('   - Wildcard matching (e.g., "circle" matches "circles")');
  console.log('   - Rate limiting: 6-second delays between requests');
  console.log('   - Graceful error handling for invalid parameters');
}

// Run the test
if (require.main === module) {
  testSimpleNISTSearch().catch(console.error);
}

module.exports = { testSimpleNISTSearch };
