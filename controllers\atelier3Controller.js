// controllers/atelier3Controller.js
const mongoose = require('mongoose');
const AnalysisComponent = require('../models/AnalysisComponent');
const { getIpAddress } = require('../utils/requestUtils');
const ActivityLog = require('../models/ActivityLog');

// Helper function to create activity log
const logActivity = async (req, actionType, resourceType, resourceId, details = {}) => {
  try {
    if (!req.user) return;

    await ActivityLog.create({
      userId: req.user.id,
      userName: req.user.name,
      companyId: req.user.companyId,
      companyName: req.user.companyName,
      actionType,
      resourceType,
      resourceId,
      ipAddress: getIpAddress(req),
      userAgent: req.headers['user-agent'] || '',
      details: {
        ...details,
        timestamp: new Date()
      }
    });
  } catch (error) {
    console.error('Activity log error:', error);
  }
};

// Helper function to check if user has access to analysis
const userHasAnalysisAccess = async (userId, analysisId) => {
  try {
    const analysis = await mongoose.model('Analysis').findById(analysisId);
    return analysis && (analysis.createdBy.toString() === userId || analysis.companyId.toString() === userId);
  } catch (error) {
    console.error('Error checking analysis access:', error);
    return false;
  }
};

/**
 * Get all stakeholders for an analysis
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getStakeholders = async (req, res) => {
  try {
    const { analysisId } = req.params;

    // Check if user has access to this analysis
    if (!await userHasAnalysisAccess(req.user.id, analysisId)) {
      return res.status(403).json({ success: false, message: 'Accès non autorisé à cette analyse' });
    }

    // Find the stakeholders component for this analysis
    const component = await AnalysisComponent.findOne({
      analysisId,
      componentType: 'stakeholders'
    });

    if (!component) {
      return res.status(200).json({ success: true, data: [] });
    }

    return res.status(200).json({
      success: true,
      data: component.data.stakeholders || []
    });
  } catch (error) {
    console.error('Error getting stakeholders:', error);
    return res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération des parties prenantes',
      error: error.message
    });
  }
};

/**
 * Save all stakeholders for an analysis
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.saveStakeholders = async (req, res) => {
  try {
    const { analysisId } = req.params;
    const { stakeholders } = req.body;

    // Check if user has access to this analysis
    if (!await userHasAnalysisAccess(req.user.id, analysisId)) {
      return res.status(403).json({ success: false, message: 'Accès non autorisé à cette analyse' });
    }

    // Find or create the stakeholders component
    let component = await AnalysisComponent.findOne({
      analysisId,
      componentType: 'stakeholders'
    });

    if (component) {
      // Update existing component
      component.data.stakeholders = stakeholders;
      component.updatedBy = req.user.id;
      component.markModified('data'); // Tell Mongoose that the 'data' field (Mixed type) has been changed
      await component.save();
    } else {
      // Create new component
      component = await AnalysisComponent.create({
        analysisId,
        componentType: 'stakeholders',
        data: { stakeholders },
        createdBy: req.user.id
      });
    }

    // Log the activity
    try {
      await logActivity(
        req,
        'COMPONENT_UPDATE', // Use standard action type
        'analysis_component', // Use standard resource type
        component._id,
        { analysisId, componentType: 'stakeholders', count: stakeholders.length }
      );
    } catch (logError) {
      console.error('Activity log error (non-critical):', logError);
      // Continue execution even if logging fails
    }

    return res.status(200).json({
      success: true,
      data: stakeholders,
      message: 'Parties prenantes enregistrées avec succès'
    });
  } catch (error) {
    console.error('Error saving stakeholders:', error);
    return res.status(500).json({
      success: false,
      message: 'Erreur lors de l\'enregistrement des parties prenantes',
      error: error.message
    });
  }
};

/**
 * Get a single stakeholder by ID
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getStakeholder = async (req, res) => {
  try {
    const { analysisId, stakeholderId } = req.params;

    // Check if user has access to this analysis
    if (!await userHasAnalysisAccess(req.user.id, analysisId)) {
      return res.status(403).json({ success: false, message: 'Accès non autorisé à cette analyse' });
    }

    // Find the stakeholders component
    const component = await AnalysisComponent.findOne({
      analysisId,
      componentType: 'stakeholders'
    });

    if (!component || !component.data.stakeholders) {
      return res.status(404).json({ success: false, message: 'Partie prenante non trouvée' });
    }

    // Find the specific stakeholder
    const stakeholder = component.data.stakeholders.find(s => s.id === stakeholderId);

    if (!stakeholder) {
      return res.status(404).json({ success: false, message: 'Partie prenante non trouvée' });
    }

    return res.status(200).json({
      success: true,
      data: stakeholder
    });
  } catch (error) {
    console.error('Error getting stakeholder:', error);
    return res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération de la partie prenante',
      error: error.message
    });
  }
};

/**
 * Create a new stakeholder
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.createStakeholder = async (req, res) => {
  try {
    const { analysisId } = req.params;
    const stakeholderData = req.body;

    // Check if user has access to this analysis
    if (!await userHasAnalysisAccess(req.user.id, analysisId)) {
      return res.status(403).json({ success: false, message: 'Accès non autorisé à cette analyse' });
    }

    // Find or create the stakeholders component
    let component = await AnalysisComponent.findOne({
      analysisId,
      componentType: 'stakeholders'
    });

    // Generate a unique ID for the new stakeholder
    const newStakeholder = {
      ...stakeholderData,
      id: mongoose.Types.ObjectId().toString(),
      createdAt: new Date(),
      updatedAt: new Date()
    };

    if (component) {
      // Add to existing stakeholders
      component.data.stakeholders = component.data.stakeholders || [];
      component.data.stakeholders.push(newStakeholder);
      component.updatedBy = req.user.id;
      component.markModified('data'); // Tell Mongoose that the 'data' field (Mixed type) has been changed
      await component.save();
    } else {
      // Create new component with this stakeholder
      component = await AnalysisComponent.create({
        analysisId,
        componentType: 'stakeholders',
        data: { stakeholders: [newStakeholder] },
        createdBy: req.user.id
      });
    }

    // Log the activity
    try {
      await logActivity(
        req,
        'COMPONENT_UPDATE', // Use standard action type
        'analysis_component', // Use standard resource type
        component._id,
        { analysisId, componentType: 'stakeholders', stakeholderName: newStakeholder.name, action: 'create_stakeholder' }
      );
    } catch (logError) {
      console.error('Activity log error (non-critical):', logError);
      // Continue execution even if logging fails
    }

    return res.status(201).json({
      success: true,
      data: newStakeholder,
      message: 'Partie prenante créée avec succès'
    });
  } catch (error) {
    console.error('Error creating stakeholder:', error);
    return res.status(500).json({
      success: false,
      message: 'Erreur lors de la création de la partie prenante',
      error: error.message
    });
  }
};

/**
 * Update an existing stakeholder
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.updateStakeholder = async (req, res) => {
  try {
    const { analysisId, stakeholderId } = req.params;
    const stakeholderData = req.body;

    // Check if user has access to this analysis
    if (!await userHasAnalysisAccess(req.user.id, analysisId)) {
      return res.status(403).json({ success: false, message: 'Accès non autorisé à cette analyse' });
    }

    // Find the stakeholders component
    const component = await AnalysisComponent.findOne({
      analysisId,
      componentType: 'stakeholders'
    });

    if (!component || !component.data.stakeholders) {
      return res.status(404).json({ success: false, message: 'Partie prenante non trouvée' });
    }

    // Find and update the specific stakeholder
    const stakeholderIndex = component.data.stakeholders.findIndex(s => s.id === stakeholderId);

    if (stakeholderIndex === -1) {
      return res.status(404).json({ success: false, message: 'Partie prenante non trouvée' });
    }

    // Update the stakeholder
    const updatedStakeholder = {
      ...component.data.stakeholders[stakeholderIndex],
      ...stakeholderData,
      id: stakeholderId, // Ensure ID doesn't change
      updatedAt: new Date()
    };

    component.data.stakeholders[stakeholderIndex] = updatedStakeholder;
    component.updatedBy = req.user.id;
    component.markModified('data'); // Tell Mongoose that the 'data' field (Mixed type) has been changed
    await component.save();

    // Log the activity
    try {
      await logActivity(
        req,
        'COMPONENT_UPDATE', // Use standard action type
        'analysis_component', // Use standard resource type
        component._id,
        { analysisId, componentType: 'stakeholders', stakeholderName: updatedStakeholder.name, stakeholderId, action: 'update_stakeholder' }
      );
    } catch (logError) {
      console.error('Activity log error (non-critical):', logError);
      // Continue execution even if logging fails
    }

    return res.status(200).json({
      success: true,
      data: updatedStakeholder,
      message: 'Partie prenante mise à jour avec succès'
    });
  } catch (error) {
    console.error('Error updating stakeholder:', error);
    return res.status(500).json({
      success: false,
      message: 'Erreur lors de la mise à jour de la partie prenante',
      error: error.message
    });
  }
};

/**
 * Delete a stakeholder
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.deleteStakeholder = async (req, res) => {
  try {
    const { analysisId, stakeholderId } = req.params;

    // Check if user has access to this analysis
    if (!await userHasAnalysisAccess(req.user.id, analysisId)) {
      return res.status(403).json({ success: false, message: 'Accès non autorisé à cette analyse' });
    }

    // Find the stakeholders component
    const component = await AnalysisComponent.findOne({
      analysisId,
      componentType: 'stakeholders'
    });

    if (!component || !component.data.stakeholders) {
      return res.status(404).json({ success: false, message: 'Partie prenante non trouvée' });
    }

    // Find the stakeholder to delete
    const stakeholderIndex = component.data.stakeholders.findIndex(s => s.id === stakeholderId);

    if (stakeholderIndex === -1) {
      return res.status(404).json({ success: false, message: 'Partie prenante non trouvée' });
    }

    // Store stakeholder name for logging
    const stakeholderName = component.data.stakeholders[stakeholderIndex].name;

    // Remove the stakeholder
    component.data.stakeholders.splice(stakeholderIndex, 1);
    component.updatedBy = req.user.id;
    component.markModified('data'); // Tell Mongoose that the 'data' field (Mixed type) has been changed
    await component.save();

    // Log the activity
    try {
      await logActivity(
        req,
        'COMPONENT_UPDATE', // Use standard action type
        'analysis_component', // Use standard resource type
        component._id,
        { analysisId, componentType: 'stakeholders', stakeholderName, stakeholderId, action: 'delete_stakeholder' }
      );
    } catch (logError) {
      console.error('Activity log error (non-critical):', logError);
      // Continue execution even if logging fails
    }

    return res.status(200).json({
      success: true,
      message: 'Partie prenante supprimée avec succès'
    });
  } catch (error) {
    console.error('Error deleting stakeholder:', error);
    return res.status(500).json({
      success: false,
      message: 'Erreur lors de la suppression de la partie prenante',
      error: error.message
    });
  }
};

/**
 * Update threshold values for stakeholder threat zones
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.updateThresholds = async (req, res) => {
  try {
    const { analysisId } = req.params;
    const thresholds = req.body;

    // Check if user has access to this analysis
    if (!await userHasAnalysisAccess(req.user.id, analysisId)) {
      return res.status(403).json({ success: false, message: 'Accès non autorisé à cette analyse' });
    }

    // Find or create the stakeholders component
    let component = await AnalysisComponent.findOne({
      analysisId,
      componentType: 'stakeholders'
    });

    if (component) {
      // Update existing component
      component.data.thresholds = thresholds;
      component.updatedBy = req.user.id;
      component.markModified('data'); // Tell Mongoose that the 'data' field (Mixed type) has been changed
      await component.save();
    } else {
      // Create new component with thresholds
      component = await AnalysisComponent.create({
        analysisId,
        componentType: 'stakeholders',
        data: { stakeholders: [], thresholds },
        createdBy: req.user.id
      });
    }

    // Log the activity
    try {
      await logActivity(
        req,
        'COMPONENT_UPDATE', // Use standard action type
        'analysis_component', // Use standard resource type
        component._id,
        { analysisId, componentType: 'stakeholders', thresholds, action: 'update_thresholds' }
      );
    } catch (logError) {
      console.error('Activity log error (non-critical):', logError);
      // Continue execution even if logging fails
    }

    return res.status(200).json({
      success: true,
      data: thresholds,
      message: 'Seuils mis à jour avec succès'
    });
  } catch (error) {
    console.error('Error updating thresholds:', error);
    return res.status(500).json({
      success: false,
      message: 'Erreur lors de la mise à jour des seuils',
      error: error.message
    });
  }
};

/**
 * Get threshold values for stakeholder threat zones
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getThresholds = async (req, res) => {
  try {
    const { analysisId } = req.params;

    // Check if user has access to this analysis
    if (!await userHasAnalysisAccess(req.user.id, analysisId)) {
      return res.status(403).json({ success: false, message: 'Accès non autorisé à cette analyse' });
    }

    // Find the stakeholders component
    const component = await AnalysisComponent.findOne({
      analysisId,
      componentType: 'stakeholders'
    });

    if (!component || !component.data.thresholds) {
      // Return default thresholds if none exist
      return res.status(200).json({
        success: true,
        data: {
          danger: 3,
          control: 1.5,
          watch: 0.5
        }
      });
    }

    return res.status(200).json({
      success: true,
      data: component.data.thresholds
    });
  } catch (error) {
    console.error('Error getting thresholds:', error);
    return res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération des seuils',
      error: error.message
    });
  }
};
