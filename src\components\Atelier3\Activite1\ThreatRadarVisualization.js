// src/components/Atelier3/Activite1/ThreatRadarVisualization.js
import React, { useState, useRef, useEffect } from 'react';
import { Users, Building, Shield, AlertCircle, Settings, Info, ZoomIn, ZoomOut, RefreshCw, Move } from 'lucide-react';

const ThreatRadarVisualization = ({
  stakeholders,
  thresholds,
  onUpdateThresholds,
  onSelectStakeholder,
  isLoading,
  isThresholdEditing,
  setIsThresholdEditing
}) => {
  const [selectedStakeholder, setSelectedStakeholder] = useState(null);
  const [editedThresholds, setEditedThresholds] = useState({ ...thresholds });
  const [hoveredStakeholder, setHoveredStakeholder] = useState(null);
  const svgRef = useRef(null);

  // SVG dimensions - larger for full-width layout
  const width = 800;
  const height = 800;
  const centerX = width / 2;
  const centerY = height / 2;
  const maxRadius = Math.min(width, height) / 2 - 60;

  // Zoom and pan state
  const [zoomLevel, setZoomLevel] = useState(1);
  const [panOffset, setPanOffset] = useState({ x: 0, y: 0 });
  const [isPanning, setIsPanning] = useState(false);
  const [startPanPoint, setStartPanPoint] = useState({ x: 0, y: 0 });

  // Handle zoom in
  const handleZoomIn = () => {
    setZoomLevel(prev => Math.min(3, prev + 0.2));
  };

  // Handle zoom out
  const handleZoomOut = () => {
    setZoomLevel(prev => Math.max(0.5, prev - 0.2));
  };

  // Reset zoom and pan
  const handleResetView = () => {
    setZoomLevel(1);
    setPanOffset({ x: 0, y: 0 });
  };

  // Start panning
  const handleMouseDown = (e) => {
    if (e.button === 0) { // Left mouse button
      setIsPanning(true);
      setStartPanPoint({ x: e.clientX, y: e.clientY });
    }
  };

  // Pan while mouse is moving
  const handleMouseMove = (e) => {
    if (isPanning) {
      const dx = (e.clientX - startPanPoint.x) / zoomLevel;
      const dy = (e.clientY - startPanPoint.y) / zoomLevel;
      setPanOffset(prev => ({ x: prev.x + dx, y: prev.y + dy }));
      setStartPanPoint({ x: e.clientX, y: e.clientY });
    }
  };

  // Stop panning
  const handleMouseUp = () => {
    setIsPanning(false);
  };

  // Stop panning if mouse leaves the SVG
  const handleMouseLeave = () => {
    setIsPanning(false);
  };

  // Calculate radius for threshold zones and stakeholders to ensure they're proportionally spaced
  const getThresholdRadius = (thresholdValue) => {
    // For threshold zones, we want them to be proportionally spaced
    // This creates more visually balanced zones
    const maxThreshold = thresholds.danger * 1.2; // Slightly higher than danger threshold for better visualization
    const proportion = 1 - (thresholdValue / maxThreshold);
    return maxRadius * proportion;
  };

  // Corrected stakeholder positioning to ensure points appear in the correct zones
  const getStakeholderPosition = (stakeholder) => {
    const threatLevel = stakeholder.threatLevel || 0;

    // CRITICAL FIX: Use the same threshold radius calculation as the zones
    // This ensures stakeholders appear in the correct zones based on their threat level
    let radius = getThresholdRadius(threatLevel);

    // Set a minimum distance from center to prevent stakeholders from crossing to the other side
    const minRadius = maxRadius * 0.15; // Minimum 15% of max radius from center

    // Ensure radius is at least the minimum value
    radius = Math.max(radius, minRadius);

    // Add a small variation to radius to prevent points from being on the same line
    // But keep it very small to ensure points stay in their correct zones
    const idNumber = parseInt(stakeholder.id) || 0;
    const radialVariation = (idNumber % 3) * (maxRadius * 0.02); // Very small variation (0%, 2%, or 4%)
    radius += radialVariation;

    // Get a deterministic but well-distributed angle based on stakeholder ID
    // Using prime numbers helps create a more scattered distribution
    const primeMultiplier = 83; // A prime number for better distribution
    const goldenRatio = 0.618033988749895; // Golden ratio for optimal spacing

    // Calculate angle using golden ratio method for better distribution
    // This creates a spiral-like distribution that avoids alignments
    const normalizedId = ((idNumber * primeMultiplier) % 1000) / 1000;
    const angle = (normalizedId + goldenRatio) * 2 * Math.PI;

    // Calculate position
    const x = centerX + radius * Math.cos(angle);
    const y = centerY + radius * Math.sin(angle);

    return {
      x,
      y,
      threatLevel,
      angle,
      radius
    };
  };

  // Calculate fiabilité cyber (maturité cyber × confiance)
  const calculateFiabiliteCyber = (stakeholder) => {
    return (stakeholder.cyberMaturity || 1) * (stakeholder.trust || 1);
  };

  // Calculate exposition (dépendance × pénétration)
  const calculateExposition = (stakeholder) => {
    return (stakeholder.dependency || 1) * (stakeholder.penetration || 1);
  };

  // Get color based on fiabilité cyber
  const getFiabiliteCyberColor = (stakeholder) => {
    const fiabiliteCyber = calculateFiabiliteCyber(stakeholder);

    if (fiabiliteCyber < 4) return '#EF4444'; // Rouge
    if (fiabiliteCyber >= 4 && fiabiliteCyber <= 5) return '#F97316'; // Orange
    if (fiabiliteCyber >= 6 && fiabiliteCyber <= 7) return '#3B82F6'; // Bleu
    return '#10B981'; // Vert (> 7)
  };

  // Get point size based on exposition (very large sizes for maximum visibility)
  const getExpositionSize = (stakeholder) => {
    const exposition = calculateExposition(stakeholder);

    if (exposition < 3) return 20; // Small
    if (exposition >= 3 && exposition <= 6) return 28; // Medium
    if (exposition >= 7 && exposition <= 9) return 36; // Large
    return 44; // Extra Large (> 9)
  };

  // Get color based on stakeholder category (kept for backward compatibility)
  const getCategoryColor = (category) => {
    switch(category) {
      case 'client': return '#3B82F6'; // blue
      case 'partner': return '#10B981'; // green
      case 'provider': return '#F59E0B'; // amber
      case 'technical': return '#8B5CF6'; // purple
      case 'business': return '#EC4899'; // pink
      case 'subsidiary': return '#6366F1'; // indigo
      default: return '#6B7280'; // gray
    }
  };

  // Get icon based on stakeholder category
  const getCategoryIcon = (category) => {
    switch(category) {
      case 'client': return <Users size={16} />;
      case 'partner': return <Building size={16} />;
      case 'provider': return <Shield size={16} />;
      case 'technical': return <AlertCircle size={16} />;
      case 'business': return <Building size={16} />;
      case 'subsidiary': return <Building size={16} />;
      default: return <Users size={16} />;
    }
  };

  // Get threat level color with optional opacity
  const getThreatLevelColor = (level, opacity = 1) => {
    let color;
    if (level >= thresholds.danger) color = "#EF4444"; // Red for danger
    else if (level >= thresholds.control) color = "#F59E0B"; // Amber for control
    else if (level >= thresholds.watch) color = "#10B981"; // Green for watch
    else color = "#64748B"; // Default slate

    // If opacity is provided, convert to rgba
    if (opacity !== 1) {
      const r = parseInt(color.slice(1, 3), 16);
      const g = parseInt(color.slice(3, 5), 16);
      const b = parseInt(color.slice(5, 7), 16);
      return `rgba(${r}, ${g}, ${b}, ${opacity})`;
    }

    return color;
  };

  // Handle threshold change
  const handleThresholdChange = (e) => {
    const { name, value } = e.target;
    setEditedThresholds(prev => ({
      ...prev,
      [name]: parseFloat(value)
    }));
  };

  // Save threshold changes
  const handleSaveThresholds = () => {
    onUpdateThresholds(editedThresholds);
    setIsThresholdEditing(false);
  };

  // Cancel threshold editing
  const handleCancelThresholds = () => {
    setEditedThresholds({ ...thresholds });
    setIsThresholdEditing(false);
  };

  // Handle stakeholder click
  const handleStakeholderClick = (stakeholder) => {
    setSelectedStakeholder(stakeholder);
    if (onSelectStakeholder) {
      onSelectStakeholder(stakeholder);
    }
  };

  // Render loading state
  if (isLoading) {
    return (
      <div className="p-8 text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto"></div>
        <p className="mt-4 text-gray-600">Chargement de la visualisation...</p>
      </div>
    );
  }

  // Render empty state
  if (stakeholders.length === 0) {
    return (
      <div className="p-8 text-center">
        <div className="bg-blue-50 p-4 rounded-full inline-flex items-center justify-center mb-4">
          <Users size={32} className="text-blue-500" />
        </div>
        <h3 className="text-lg font-medium text-gray-900 mb-2">Aucune partie prenante</h3>
        <p className="text-gray-600 mb-4">
          Ajoutez des parties prenantes pour visualiser la cartographie de l'écosystème.
        </p>
      </div>
    );
  }

  return (
    <div className="h-full"> {/* Main container */}
      {/* Chart Area with absolute positioned legend */}
      <div className="relative h-full bg-white rounded-lg shadow-sm border border-slate-200 p-2"> {/* Container for chart and its absolute elements */}
        {/* Legend removed - now in a separate column */}

        {/* Threshold settings panel (absolute to this container) */}
        {isThresholdEditing && (
          <div className="absolute top-12 right-2 bg-white p-4 rounded-lg shadow-md border border-gray-200 z-20 w-64">
            <h3 className="text-sm font-medium text-gray-700 mb-3">Modifier les seuils</h3>
            <div className="space-y-3">
              <div>
                <label className="block text-xs text-gray-500 mb-1">Seuil de danger</label>
                <input
                  type="number"
                  name="danger"
                  value={editedThresholds.danger}
                  onChange={handleThresholdChange}
                  step="0.1"
                  min="0"
                  className="w-full p-1.5 text-sm border border-gray-300 rounded"
                />
              </div>
              <div>
                <label className="block text-xs text-gray-500 mb-1">Seuil de contrôle</label>
                <input
                  type="number"
                  name="control"
                  value={editedThresholds.control}
                  onChange={handleThresholdChange}
                  step="0.1"
                  min="0"
                  className="w-full p-1.5 text-sm border border-gray-300 rounded"
                />
              </div>
              <div>
                <label className="block text-xs text-gray-500 mb-1">Seuil de veille</label>
                <input
                  type="number"
                  name="watch"
                  value={editedThresholds.watch}
                  onChange={handleThresholdChange}
                  step="0.1"
                  min="0"
                  className="w-full p-1.5 text-sm border border-gray-300 rounded"
                />
              </div>
            </div>
            <div className="flex justify-end mt-4 space-x-2">
              <button
                onClick={handleCancelThresholds}
                className="px-3 py-1.5 text-xs border border-gray-300 rounded hover:bg-gray-50"
              >
                Annuler
              </button>
              <button
                onClick={handleSaveThresholds}
                className="px-3 py-1.5 text-xs bg-blue-600 text-white rounded hover:bg-blue-700"
              >
                Appliquer
              </button>
            </div>
          </div>
        )}

        {/* Zoom controls */}
        <div className="absolute top-2 left-2 z-10 bg-white rounded-md shadow-sm border border-gray-200 p-1 flex items-center">
          <button
            onClick={handleZoomOut}
            className="p-1 text-gray-600 hover:bg-gray-100 rounded"
            disabled={zoomLevel <= 0.5}
            title="Zoom out"
          >
            <ZoomOut size={16} />
          </button>
          <span className="mx-1 text-xs font-medium">{Math.round(zoomLevel * 100)}%</span>
          <button
            onClick={handleZoomIn}
            className="p-1 text-gray-600 hover:bg-gray-100 rounded"
            disabled={zoomLevel >= 3}
            title="Zoom in"
          >
            <ZoomIn size={16} />
          </button>
          <button
            onClick={handleResetView}
            className="ml-1 p-1 text-gray-600 hover:bg-gray-100 rounded"
            disabled={zoomLevel === 1 && panOffset.x === 0 && panOffset.y === 0}
            title="Reset view"
          >
            <RefreshCw size={16} />
          </button>
          <div className="ml-1 p-1 text-gray-600 flex items-center" title="Drag to move">
            <Move size={16} />
            <span className="ml-1 text-xs">Drag</span>
          </div>
        </div>

        {/* Radar visualization SVG container - using full height */}
        <div className="w-full h-full overflow-hidden"> {/* Using full height now that legend is removed */}
          <svg
            ref={svgRef}
            width="100%"
            height="100%"
            viewBox={`0 0 ${width} ${height}`}
            className="mx-auto max-w-full max-h-full cursor-move"
            onMouseDown={handleMouseDown}
            onMouseMove={handleMouseMove}
            onMouseUp={handleMouseUp}
            onMouseLeave={handleMouseLeave}
            style={{
              transform: `scale(${zoomLevel}) translate(${panOffset.x}px, ${panOffset.y}px)`,
              transformOrigin: 'center',
              transition: 'transform 0.1s ease-out'
            }}
          >
            {/* Radial Grid Lines and Labels - using the same calculation method as zones */}
            {[2, 4, 6, 8].map(level => {
              // Use the same calculation method as the zones for consistent grid lines
              const radius = getThresholdRadius(level);
              return (
                <g key={`grid-${level}`}>
                  {/* Enhanced grid circle */}
                  <circle
                    cx={centerX}
                    cy={centerY}
                    r={radius}
                    fill="none"
                    stroke="rgba(100, 116, 139, 0.8)" // Even more visible grid lines with higher opacity
                    strokeWidth="1.5"
                    strokeDasharray="5,4" // More visible dash pattern
                  />

                  {/* Grid numbers removed */}
                </g>
              );
            })}

            {/* Enhanced background circles for DYNAMIC threat zones with proportional spacing */}
            {/* Using getThresholdRadius for more balanced zone visualization */}
            <circle
              cx={centerX}
              cy={centerY}
              r={getThresholdRadius(thresholds.watch)}
              fill="rgba(229, 247, 235, 0.4)"
              stroke="#10B981"
              strokeWidth="2"
            />
            <circle
              cx={centerX}
              cy={centerY}
              r={getThresholdRadius(thresholds.control)}
              fill="rgba(254, 243, 199, 0.4)"
              stroke="#F59E0B"
              strokeWidth="2"
            />
            <circle
              cx={centerX}
              cy={centerY}
              r={getThresholdRadius(thresholds.danger)}
              fill="rgba(254, 226, 226, 0.4)"
              stroke="#EF4444"
              strokeWidth="2"
            />

            {/* Center point representing the object of study */}
            <circle cx={centerX} cy={centerY} r={12} fill="#334155" stroke="#475569" strokeWidth="1.5" />
            <text x={centerX} y={centerY + 30} textAnchor="middle" fill="#334155" fontSize="11" fontWeight="medium">Objet de l'étude</text> {/* Adjusted position */}

            {/* Enhanced zone labels with backgrounds */}
            {/* Danger zone label */}
            <rect
              x={centerX - 50}
              y={centerY - getThresholdRadius(thresholds.danger) - 18}
              width={100}
              height={20}
              rx={10}
              fill="rgba(254, 226, 226, 0.8)"
              stroke="#EF4444"
              strokeWidth="1"
            />
            <text
              x={centerX}
              y={centerY - getThresholdRadius(thresholds.danger) - 5}
              textAnchor="middle"
              fill="#B91C1C"
              fontSize="12"
              fontWeight="bold"
            >
              Zone de Danger
            </text>

            {/* Control zone label */}
            <rect
              x={centerX - 55}
              y={centerY - getThresholdRadius(thresholds.control) - 18}
              width={110}
              height={20}
              rx={10}
              fill="rgba(254, 243, 199, 0.8)"
              stroke="#F59E0B"
              strokeWidth="1"
            />
            <text
              x={centerX}
              y={centerY - getThresholdRadius(thresholds.control) - 5}
              textAnchor="middle"
              fill="#B45309"
              fontSize="12"
              fontWeight="bold"
            >
              Zone de Contrôle
            </text>

            {/* Watch zone label */}
            <rect
              x={centerX - 50}
              y={centerY - getThresholdRadius(thresholds.watch) - 18}
              width={100}
              height={20}
              rx={10}
              fill="rgba(229, 247, 235, 0.8)"
              stroke="#10B981"
              strokeWidth="1"
            />
            <text
              x={centerX}
              y={centerY - getThresholdRadius(thresholds.watch) - 5}
              textAnchor="middle"
              fill="#047857"
              fontSize="12"
              fontWeight="bold"
            >
              Zone de Veille
            </text>

            {/* Legend removed - now in a separate column */}

            {/* Stakeholders (drawn on top of everything else) */}
            {stakeholders.map(stakeholder => {
              const { x, y } = getStakeholderPosition(stakeholder);
              const color = getFiabiliteCyberColor(stakeholder); // Nouvelle couleur basée sur fiabilité cyber
              const pointSize = getExpositionSize(stakeholder); // Nouvelle taille basée sur exposition
              const isInternal = stakeholder.type === 'internal';
              const isHovered = hoveredStakeholder?.id === stakeholder.id;
              const threatColor = getThreatLevelColor(stakeholder.threatLevel);

              return (
                <g key={stakeholder.id} onClick={() => handleStakeholderClick(stakeholder)} onMouseEnter={() => setHoveredStakeholder(stakeholder)} onMouseLeave={() => setHoveredStakeholder(null)} style={{ cursor: 'pointer' }}>
                  {/* Glow effect for hover */}
                  {isHovered && <circle cx={x} cy={y} r={pointSize + 6} fill="rgba(59, 130, 246, 0.3)" stroke="#3b82f6" strokeWidth={1} strokeDasharray="3,3" />}

                  {/* Threat level indicator ring - adjusted to point size */}
                  <circle cx={x} cy={y} r={pointSize + 2} fill="none" stroke={threatColor} strokeWidth={2.5} strokeOpacity={0.6} />

                  {/* Stakeholder shape with dynamic size */}
                  {isInternal ?
                    <rect
                      x={x - pointSize/2}
                      y={y - pointSize/2}
                      width={pointSize}
                      height={pointSize}
                      fill={color}
                      stroke={threatColor}
                      strokeWidth={1.5}
                      transform={`rotate(45, ${x}, ${y})`}
                    />
                    :
                    <circle cx={x} cy={y} r={pointSize/2} fill={color} stroke={threatColor} strokeWidth={1.5} />
                  }

                  {/* Name with background for better visibility */}
                  <rect
                    x={x - stakeholder.name.length * 2.5 - 4}
                    y={y + pointSize + 2}
                    width={stakeholder.name.length * 5 + 8}
                    height={16}
                    rx={8}
                    fill="rgba(255, 255, 255, 0.9)"
                    stroke={color}
                    strokeWidth={0.5}
                  />
                  <text x={x} y={y + pointSize + 14} textAnchor="middle" fill="#1E293B" fontSize="10" fontWeight="medium">{stakeholder.name}</text>
                </g>
              );
            })}
          </svg>
        </div>

        {/* Hover tooltip - fixed position in the top-right corner */}
        {hoveredStakeholder && (
          <div
            className="absolute top-2 right-2 bg-white p-3 rounded-lg shadow-lg border border-gray-200 z-30 w-72"
            style={{
              borderColor: getFiabiliteCyberColor(hoveredStakeholder)
            }}
          >
            <div className="flex items-center mb-2">
              <div
                className="p-1.5 rounded-full mr-2"
                style={{ backgroundColor: getFiabiliteCyberColor(hoveredStakeholder) }}
              >
                {getCategoryIcon(hoveredStakeholder.category)}
              </div>
              <div>
                <h3 className="font-medium text-gray-900">{hoveredStakeholder.name}</h3>
                <p className="text-xs text-gray-500">{hoveredStakeholder.type === 'internal' ? 'Interne' : 'Externe'}</p>
              </div>
            </div>

            {/* Critères de base */}
            <div className="grid grid-cols-2 gap-2 text-xs mb-2">
              <div><span className="text-gray-500">Dépendance:</span><span className="ml-1 font-medium">{hoveredStakeholder.dependency}</span></div>
              <div><span className="text-gray-500">Pénétration:</span><span className="ml-1 font-medium">{hoveredStakeholder.penetration}</span></div>
              <div><span className="text-gray-500">Maturité:</span><span className="ml-1 font-medium">{hoveredStakeholder.cyberMaturity}</span></div>
              <div><span className="text-gray-500">Confiance:</span><span className="ml-1 font-medium">{hoveredStakeholder.trust}</span></div>
            </div>

            {/* Nouveaux indicateurs calculés */}
            <div className="mt-2 pt-2 border-t border-gray-100 space-y-2">
              <div className="flex justify-between items-center">
                <span className="text-xs text-gray-500">Fiabilité Cyber:</span>
                <span
                  className="font-bold text-sm px-2 py-0.5 rounded-full text-white"
                  style={{
                    backgroundColor: getFiabiliteCyberColor(hoveredStakeholder)
                  }}
                >
                  {calculateFiabiliteCyber(hoveredStakeholder).toFixed(1)}
                </span>
              </div>

              <div className="flex justify-between items-center">
                <span className="text-xs text-gray-500">Exposition:</span>
                <span className="font-bold text-sm px-2 py-0.5 rounded-full bg-gray-100 text-gray-800">
                  {calculateExposition(hoveredStakeholder).toFixed(1)}
                </span>
              </div>

              <div className="flex justify-between items-center">
                <span className="text-xs text-gray-500">Niveau de menace:</span>
                <span
                  className="font-bold text-sm px-2 py-0.5 rounded-full"
                  style={{
                    backgroundColor: getThreatLevelColor(hoveredStakeholder.threatLevel, 0.2),
                    color: getThreatLevelColor(hoveredStakeholder.threatLevel)
                  }}
                >
                  {hoveredStakeholder.threatLevel?.toFixed(2) || '0.00'}
                </span>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ThreatRadarVisualization;
