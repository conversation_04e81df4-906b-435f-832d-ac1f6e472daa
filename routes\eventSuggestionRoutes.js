const express = require('express');
const {
  getAllEventSuggestions,
  createEventSuggestion,
  createBulkEventSuggestions,
  updateEventSuggestion,
  deleteEventSuggestion
} = require('../controllers/eventSuggestionController');

const { protect } = require('../middleware/authMiddleware');

const router = express.Router();

// Apply protect middleware to all routes
router.use(protect);

// Routes for event suggestions
router.route('/')
  .get(getAllEventSuggestions)
  .post(createEventSuggestion);

// Route for bulk creation
router.route('/bulk')
  .post(createBulkEventSuggestions);

// Routes for specific event suggestion
router.route('/:id')
  .put(updateEventSuggestion)
  .delete(deleteEventSuggestion);

module.exports = router;
