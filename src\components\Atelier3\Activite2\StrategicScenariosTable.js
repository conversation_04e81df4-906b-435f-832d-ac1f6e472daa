// src/components/Atelier3/Activite2/StrategicScenariosTable.js
import React, { useState, useMemo, useCallback, useEffect } from 'react';
import { Search, Info, Shield, AlertTriangle, Check, Target, Save, Loader } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { useAnalysis } from '../../../context/AnalysisContext';
import { showSuccessToast, showErrorToast } from '../../../utils/toastUtils';

import attackPathsService from '../../../services/attackPathsService';

const StrategicScenariosTable = ({
  mappings,
  sourcesRisque,
  dreadedEvents,
  controlsData,
  stakeholders,
  businessValues = [],
  initialSelectedStakeholders = {},
  onSelectedStakeholdersChange
}) => {
  const { t } = useTranslation();
  const { currentAnalysis } = useAnalysis();
  const [isGuideOpen, setIsGuideOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterCategory, setFilterCategory] = useState('');
  const [selectedStakeholders, setSelectedStakeholders] = useState(initialSelectedStakeholders);
  const [isSaving, setIsSaving] = useState(false);

  // Get all unique categories
  const categories = [...new Set(sourcesRisque.map(source => source.category))];

  // Update local state when initial selected stakeholders change
  useEffect(() => {
    setSelectedStakeholders(initialSelectedStakeholders);
  }, [initialSelectedStakeholders]);

  // Create a combined data structure with all the information
  const tableData = mappings.map(mapping => {
    const sourceRisk = sourcesRisque.find(source => source.id === mapping.sourceId);
    const dreadedEvent = dreadedEvents.find(event => event.id === mapping.dreadedEventId);

    // Skip if source or dreaded event not found
    if (!sourceRisk || !dreadedEvent) return null;

    // Temporarily comment out the selection check to show all sources
    // if (sourceRisk.selected !== true) return null;

    // Find the business value associated with this dreaded event
    const businessValue = businessValues.find(bv => String(bv.id) === String(dreadedEvent.businessValue));

    // Get controls for this dreaded event
    const eventControls = [];

    // Look through the planData to find controls for this dreaded event
    if (controlsData && controlsData.planData) {
      // The planData can have different structures based on the version
      // Check both direct keys and nested 'plan' structure
      const planDataToCheck = controlsData.planData.plan || controlsData.planData;

      Object.values(planDataToCheck || {}).forEach(entry => {
        if (entry.dreadedEventId === dreadedEvent.id) {
          // Add controls from this entry
          if (entry.controls && Array.isArray(entry.controls)) {
            eventControls.push(...entry.controls);
          }
        }
      });
    }

    return {
      id: mapping.id,
      sourceId: sourceRisk.id,
      dreadedEventId: dreadedEvent.id,
      sourceRisk: sourceRisk.name,
      sourceCategory: sourceRisk.category,
      objectifVise: sourceRisk.objectifVise,
      objectifViseCategory: sourceRisk.objectifViseCategory,
      dreadedEvent: dreadedEvent.name,
      dreadedEventDescription: dreadedEvent.description,
      controls: eventControls,
      // Include business value and support assets
      businessValue: businessValue ? businessValue.name : 'N/A',
      businessValueId: businessValue ? businessValue.id : null,
      supportAssets: businessValue ? businessValue.supportAssets || [] : [],
      // Include other relevant data
      sourceDescription: sourceRisk.description,
      fullSourceRisk: sourceRisk,
      fullDreadedEvent: dreadedEvent
    };
  }).filter(Boolean);

  // Filter data based on search term and category filter
  const filteredData = useMemo(() => {
    return tableData.filter(item => {
      const matchesSearch =
        item.sourceRisk.toLowerCase().includes(searchTerm.toLowerCase()) ||
        item.objectifVise.toLowerCase().includes(searchTerm.toLowerCase()) ||
        item.dreadedEvent.toLowerCase().includes(searchTerm.toLowerCase());

      const matchesCategory = filterCategory ? item.sourceCategory === filterCategory : true;

      return matchesSearch && matchesCategory;
    });
  }, [tableData, searchTerm, filterCategory]);

  // Sort data by dreaded events (fixed organization)
  const sortedData = useMemo(() => {
    return [...filteredData].sort((a, b) => {
      // Primary sort: by dreaded event name
      const dreadedEventComparison = a.dreadedEvent.localeCompare(b.dreadedEvent);
      if (dreadedEventComparison !== 0) return dreadedEventComparison;

      // Secondary sort: by source risk name
      return a.sourceRisk.localeCompare(b.sourceRisk);
    });
  }, [filteredData]);

  // Create a mapping for unique scenario numbers for each row
  const scenarioNumberMapping = useMemo(() => {
    const mapping = {};
    let scenarioCounter = 1;

    // Assign a unique scenario number to each unique source-dreaded event combination
    sortedData.forEach(item => {
      const key = `${item.sourceId}-${item.dreadedEventId}`;
      if (!mapping[key]) {
        mapping[key] = scenarioCounter++;
      }
    });

    return mapping;
  }, [sortedData]);

  // Process data for merged cells
  const processedData = useMemo(() => {
    if (sortedData.length === 0) return [];

    // Create a new array with rowspan information
    const result = [];

    // Track current dreaded event, source and objectif for rowspan calculation
    let currentDreadedEventId = null;
    let currentDreadedEventIndex = -1;
    let currentSourceId = null;
    let currentSourceIndex = -1;
    let currentObjectifValue = null;
    let currentObjectifIndex = -1;

    sortedData.forEach((item, index) => {
      // Create a new item with rowspan information
      const newItem = {
        ...item,
        dreadedEventRowspan: 1,
        sourceRowspan: 1,
        objectifRowspan: 1,
        // Calculate scenario number based on the unique source-dreaded event combination
        scenarioNumber: scenarioNumberMapping[`${item.sourceId}-${item.dreadedEventId}`],
        // Ensure all cells have values, even if empty
        sourceRisk: item.sourceRisk || '',
        sourceCategory: item.sourceCategory || '',
        objectifVise: item.objectifVise || '',
        objectifViseCategory: item.objectifViseCategory || '',
        dreadedEvent: item.dreadedEvent || '',
        controls: item.controls || []
      };

      // Check if this is the same dreaded event as the previous one
      if (item.dreadedEventId === currentDreadedEventId) {
        // Same dreaded event, increment the rowspan of the first occurrence
        result[currentDreadedEventIndex].dreadedEventRowspan++;
        // Mark this item to not display the scenario number
        newItem.hideDreadedEvent = true;
      } else {
        // New dreaded event
        currentDreadedEventId = item.dreadedEventId;
        currentDreadedEventIndex = result.length;
      }

      // Check if this is a new source or the same as the previous one
      if (item.sourceId === currentSourceId) {
        // Same source, increment the rowspan of the first occurrence
        result[currentSourceIndex].sourceRowspan++;
        // Mark this item to not display the source
        newItem.hideSource = true;

        // Check if objectif is the same as the current objectif
        if (item.objectifVise === currentObjectifValue) {
          // Same objectif, increment the rowspan of the first occurrence
          result[currentObjectifIndex].objectifRowspan++;
          // Mark this item to not display the objectif
          newItem.hideObjectif = true;
        } else {
          // New objectif within the same source
          currentObjectifValue = item.objectifVise;
          currentObjectifIndex = result.length;
        }
      } else {
        // New source
        currentSourceId = item.sourceId;
        currentSourceIndex = result.length;

        // Reset objectif tracking for the new source
        currentObjectifValue = item.objectifVise;
        currentObjectifIndex = result.length;
      }

      result.push(newItem);
    });

    return result;
  }, [sortedData, scenarioNumberMapping]);

  // Format control decision status
  const formatDecisionStatus = (decision) => {
    switch (decision) {
      case 'implemented':
        return (
          <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-green-50 text-green-800 border border-green-200">
            <Check size={12} className="mr-1" />
            {t('workshop3.activity2.controlStatus.implemented')}
          </span>
        );
      case 'planned':
        return (
          <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-blue-50 text-blue-800 border border-blue-200">
            <Shield size={12} className="mr-1" />
            {t('workshop3.activity2.controlStatus.planned')}
          </span>
        );
      default:
        return (
          <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-gray-50 text-gray-800 border border-gray-200">
            <Info size={12} className="mr-1" />
            {t('workshop3.activity2.controlStatus.notDefined')}
          </span>
        );
    }
  };

  // Handle stakeholder selection
  const handleStakeholderSelection = useCallback((sourceId, dreadedEventId, stakeholderId, isChecked) => {
    setSelectedStakeholders(prev => {
      // Create a deep copy of the previous state
      const newState = { ...prev };

      // Create the key for this source-dreaded event pair
      const key = `${sourceId}-${dreadedEventId}`;

      // Initialize the array if it doesn't exist
      if (!newState[key]) {
        newState[key] = [];
      }

      // Add or remove the stakeholder ID based on the checkbox state
      if (isChecked) {
        // Add the stakeholder ID if it's not already in the array
        if (!newState[key].includes(stakeholderId)) {
          newState[key] = [...newState[key], stakeholderId];
        }
      } else {
        // Remove the stakeholder ID if it's in the array
        newState[key] = newState[key].filter(id => id !== stakeholderId);
      }

      // Notify parent component of the change
      if (onSelectedStakeholdersChange) {
        onSelectedStakeholdersChange(newState);
      }

      return newState;
    });
  }, [onSelectedStakeholdersChange]);

  // Prepare attack paths data for saving
  const prepareAttackPathsData = useCallback(() => {
    const attackPaths = [];

    // Loop through all processed data items
    processedData.forEach(item => {
      // Get the key for this source-dreaded event pair
      const key = `${item.sourceId}-${item.dreadedEventId}`;

      // Get the selected stakeholder IDs for this item
      const selectedStakeholderIds = selectedStakeholders[key] || [];

      // Get the stakeholder objects for the selected IDs
      const selectedStakeholdersForItem = stakeholders
        .filter(s => s.retained && selectedStakeholderIds.includes(s.id))
        .map(s => ({ id: s.id, name: s.name }));

      // Create a separate attack path for each selected stakeholder
      selectedStakeholdersForItem.forEach(stakeholder => {
        attackPaths.push({
          id: `${item.sourceId}-${item.dreadedEventId}-${stakeholder.id}-${Date.now()}`,
          sourceRiskId: item.sourceId,
          sourceRiskName: item.sourceRisk,
          objectifVise: item.objectifVise,
          dreadedEventId: item.dreadedEventId,
          dreadedEventName: item.dreadedEvent,
          businessValueId: item.businessValueId,
          businessValueName: item.businessValue,
          stakeholders: [stakeholder], // Only one stakeholder per attack path
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        });
      });
    });

    return attackPaths;
  }, [processedData, selectedStakeholders, stakeholders]);

  // Handle saving attack paths
  const handleSaveAttackPaths = useCallback(async () => {
    if (!currentAnalysis || !currentAnalysis.id) {
      showErrorToast(t('workshop2.activity1.errors.noAnalysisSelected'));
      return;
    }

    const attackPaths = prepareAttackPathsData();

    if (attackPaths.length === 0) {
      showErrorToast(t('workshop3.activity2.errors.noAttackPathSelected'));
      return;
    }

    setIsSaving(true);

    try {
      // Call the attack paths service (using dedicated endpoint)
      const response = await attackPathsService.saveAttackPaths(currentAnalysis.id, attackPaths);
      console.log('Save response:', response);

      // Show success message
      showSuccessToast(t('workshop3.activity2.success.attackPathsSaved'));

      // Optional: Reset selected stakeholders after successful save
      // setSelectedStakeholders({});
    } catch (error) {
      console.error('Error saving attack paths:', error);
      showErrorToast(t('workshop3.activity2.errors.saveAttackPathsError'));
    } finally {
      setIsSaving(false);
    }
  }, [currentAnalysis, prepareAttackPathsData]);



  return (
    <div className="space-y-6">
      {/* Enhanced Header - Matching Context.js style */}
      <div className="bg-white rounded-xl shadow-sm border border-slate-100 p-4 mb-6">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          {/* Left Side: Breadcrumb & Title */}
          <div>
            <div className="flex items-center text-sm text-slate-500 mb-2">
              <span className="hover:text-blue-600 transition-colors">{t('workshop3.title')}</span>
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mx-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
              <span className="text-blue-600 font-medium">{t('workshop3.activity2.breadcrumb')}</span>
            </div>
            <h1 className="text-2xl sm:text-3xl font-bold text-slate-800 flex items-center">
              <Shield size={28} className="mr-3 text-blue-600" />
              {t('workshop3.activity2.title')}
            </h1>
          </div>

          {/* Right Side: Action Buttons */}
          <div className="flex items-center space-x-3">
            {/* Help Button */}
            <button
              className="text-sm font-medium bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 flex items-center shadow-sm transition duration-200"
              onClick={() => setIsGuideOpen(true)}
            >
              <Info size={16} className="mr-2" />
              {t('common.help')}
            </button>

            {/* Save Button */}
            <button
              className="text-sm font-medium bg-green-600 text-white px-6 py-2.5 rounded-lg hover:bg-green-700 flex items-center shadow-md transition duration-200 disabled:opacity-50 disabled:cursor-not-allowed border border-green-700"
              onClick={handleSaveAttackPaths}
              disabled={isSaving}
            >
              {isSaving ? (
                <Loader size={18} className="mr-2 animate-spin" />
              ) : (
                <Save size={18} className="mr-2" />
              )}
              <span className="font-bold">
                {t('workshop3.activity2.saveAttackPaths')}
              </span>
            </button>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="bg-white rounded-xl shadow-lg border border-slate-100 overflow-hidden">
        {/* Table Header with Search and Filters */}
        <div className="p-5 border-b border-slate-200 bg-gradient-to-r from-blue-50 to-white">
          <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
            <div>
              <h2 className="text-xl font-semibold text-slate-800 flex items-center">
                <Target size={20} className="mr-2 text-blue-600" />
                {t('workshop3.activity2.title')}
              </h2>
              <p className="text-sm text-slate-500 mt-1">{t('workshop3.activity2.subtitle')}</p>
            </div>
            <div className="flex items-center space-x-3">
              {/* Search input */}
              <div className="relative">
                <input
                  type="text"
                  placeholder={t('workshop3.activity2.searchPlaceholder')}
                  className="pl-9 pr-4 py-2 border border-slate-300 rounded-lg focus:ring-blue-500 focus:border-blue-500 w-full md:w-64 shadow-sm"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
                <Search size={16} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400" />
              </div>

              {/* Category filter */}
              <select
                className="border border-slate-300 rounded-lg px-3 py-2 focus:ring-blue-500 focus:border-blue-500 shadow-sm bg-white"
                value={filterCategory}
                onChange={(e) => setFilterCategory(e.target.value)}
              >
                <option value="">{t('workshop3.activity2.allCategories')}</option>
                {categories.map((category) => (
                  <option key={category} value={category}>
                    {category}
                  </option>
                ))}
              </select>
            </div>
          </div>
        </div>

        {/* Table with horizontal scrolling */}
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-slate-200 border-collapse">
            <thead className="bg-gradient-to-r from-slate-50 to-slate-100 sticky top-0 z-10">
              <tr className="border-b-2 border-slate-300">
                <th
                  scope="col"
                  className="px-6 py-4 text-center text-xs font-bold text-slate-800 uppercase tracking-wider bg-gradient-to-r from-blue-50 to-blue-100 border-r-2 border-slate-300 w-[8%] shadow-sm"
                >
                  <div className="flex items-center justify-center">
                    <span className="inline-flex items-center justify-center h-6 w-6 rounded-full bg-blue-600 text-white font-bold text-xs mr-2">
                      S
                    </span>
                    {t('workshop3.activity2.table.scenarios')}
                  </div>
                </th>
                <th
                  scope="col"
                  className="px-4 py-4 text-left text-xs font-semibold text-slate-700 uppercase tracking-wider border-r-2 border-slate-300 w-[16%]"
                >
                  <div className="flex items-center">
                    {t('workshop3.activity2.table.riskSource')}
                  </div>
                </th>
                <th
                  scope="col"
                  className="px-4 py-4 text-left text-xs font-semibold text-slate-700 uppercase tracking-wider border-r-2 border-slate-300 w-[16%]"
                >
                  <div className="flex items-center">
                    {t('workshop3.activity2.table.targetObjective')}
                  </div>
                </th>
                <th
                  scope="col"
                  className="px-4 py-4 text-left text-xs font-semibold text-slate-700 uppercase tracking-wider border-r-2 border-slate-300 w-[14%]"
                >
                  <div className="flex items-center">
                    {t('workshop3.activity2.table.dreadedEvent')}
                  </div>
                </th>
                <th
                  scope="col"
                  className="px-4 py-4 text-left text-xs font-semibold text-slate-700 uppercase tracking-wider border-r-2 border-slate-300 w-[15%]"
                >
                  <div className="flex items-center">
                    {t('workshop3.activity2.table.businessValueAssets')}
                  </div>
                </th>
                <th
                  scope="col"
                  className="px-4 py-4 text-left text-xs font-semibold text-slate-700 uppercase tracking-wider border-r-2 border-slate-300 w-[15%]"
                >
                  <div className="flex items-center">
                    {t('workshop3.activity2.table.controlMeasures')}
                  </div>
                </th>
                <th
                  scope="col"
                  className="px-4 py-4 text-left text-xs font-semibold text-slate-700 uppercase tracking-wider w-[16%]"
                >
                  <div className="flex items-center">
                    {t('workshop3.activity2.table.concernedStakeholders')}
                  </div>
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y-2 divide-slate-300">
              {filteredData.length === 0 ? (
                <tr>
                  <td colSpan="7" className="px-6 py-10 text-center">
                    <div className="w-16 h-16 mx-auto bg-slate-100 rounded-full flex items-center justify-center mb-4">
                      <Search size={24} className="text-slate-400" />
                    </div>
                    <h3 className="text-lg font-medium text-slate-800 mb-1">{t('workshop3.activity2.empty.noScenariosFound')}</h3>
                    <p className="text-slate-500">{t('workshop3.activity2.empty.modifySearchCriteria')}</p>
                  </td>
                </tr>
              ) : (
                processedData.map((item, index) => (
                  <tr key={`${item.sourceId}-${item.dreadedEventId}`} className="hover:bg-blue-50/40 even:bg-slate-50/70 odd:bg-white border-b-2 border-slate-200 transition-all duration-200 hover:shadow-sm">
                    {/* Scénario Stratégique - First column with enhanced design */}
                    <td className="px-6 py-4 text-sm font-medium text-slate-900 border-r-2 border-slate-300 text-center bg-gradient-to-r from-blue-50/80 to-blue-100/60">
                      <span className="inline-flex items-center justify-center h-10 w-10 rounded-full bg-gradient-to-r from-blue-600 to-blue-700 text-white font-bold text-sm shadow-lg hover:shadow-xl transition-shadow duration-200">
                        S{String(item.scenarioNumber).padStart(2, '0')}
                      </span>
                    </td>

                    {/* Source de Risque avec Catégorie - with rowspan */}
                    {!item.hideSource ? (
                      <td
                        rowSpan={item.sourceRowspan}
                        className="px-4 py-4 text-sm text-slate-900 border-r-2 border-slate-300 align-top hover:bg-blue-50/30 transition-colors duration-200"
                      >
                        <div className="font-semibold text-blue-800 mb-2">{item.sourceRisk}</div>
                        <div className="inline-block px-3 py-1 text-xs font-medium rounded-full bg-blue-100 text-blue-800 shadow-sm border border-blue-200">
                          {t('workshop3.activity2.table.category')}: {item.sourceCategory}
                        </div>
                        {item.sourceDescription && (
                          <div className="text-xs text-slate-600 mt-2 leading-relaxed">{item.sourceDescription}</div>
                        )}
                      </td>
                    ) : null}

                    {/* Objectif Visé - with rowspan */}
                    {!item.hideObjectif ? (
                      <td
                        rowSpan={item.objectifRowspan}
                        className="px-4 py-4 text-sm text-slate-900 border-r-2 border-slate-300 align-top hover:bg-indigo-50/30 transition-colors duration-200"
                      >
                        <div className="font-semibold text-indigo-800 mb-2">{item.objectifVise}</div>
                        <div className="inline-block px-3 py-1 text-xs font-medium rounded-full bg-indigo-100 text-indigo-800 shadow-sm border border-indigo-200">
                          {t('workshop3.activity2.table.category')}: {item.objectifViseCategory}
                        </div>
                      </td>
                    ) : null}

                    {/* Événement Redouté */}
                    <td className="px-4 py-4 text-sm text-slate-900 border-r-2 border-slate-300 hover:bg-amber-50/30 transition-colors duration-200">
                      <div className="font-semibold text-amber-800 mb-1">{item.dreadedEvent}</div>
                      {item.dreadedEventDescription && (
                        <div className="text-xs text-slate-600 mt-1 leading-relaxed">{item.dreadedEventDescription}</div>
                      )}
                    </td>

                    {/* Valeur Métier & Biens Support */}
                    <td className="px-4 py-4 text-sm text-slate-900 border-r-2 border-slate-300 hover:bg-green-50/30 transition-colors duration-200">
                      <div className="font-semibold text-green-800 mb-2">{item.businessValue}</div>
                      {item.supportAssets && item.supportAssets.length > 0 ? (
                        <div className="mt-3 space-y-2">
                          <div className="text-xs font-semibold text-slate-700 mb-2">{t('workshop3.activity2.table.supportAssets')}:</div>
                          {item.supportAssets.map((asset, index) => (
                            <div key={asset.id} className="flex items-center bg-green-50 p-2 rounded-md border border-green-200">
                              <span className="inline-block w-12 text-xs font-medium text-green-700 bg-green-100 px-2 py-1 rounded mr-2">{asset.shortId}</span>
                              <span className="text-xs text-slate-700 font-medium">{asset.name}</span>
                            </div>
                          ))}
                        </div>
                      ) : (
                        <div className="text-xs text-gray-500 mt-2 italic bg-gray-50 p-2 rounded border">{t('workshop3.activity2.table.noSupportAssets')}</div>
                      )}
                    </td>

                    {/* Mesures de Contrôle */}
                    <td className="px-4 py-4 text-sm text-slate-900 border-r-2 border-slate-300 hover:bg-purple-50/30 transition-colors duration-200">
                      {item.controls && item.controls.length > 0 ? (
                        <div className="space-y-3">
                          {item.controls.map((control, index) => (
                            <div key={index} className="bg-purple-50 p-3 rounded-lg border border-purple-200 shadow-sm">
                              <div className="font-semibold text-purple-800 mb-2">{control.name || control.controlName}</div>
                              <div className="flex flex-wrap items-center gap-2">
                                {control.responsiblePerson && (
                                  <span className="text-xs text-slate-600 bg-slate-100 px-2 py-1 rounded-full">
                                    {t('workshop3.activity2.table.responsible')}: {control.responsiblePerson}
                                  </span>
                                )}
                                {control.decision && (
                                  <span>
                                    {formatDecisionStatus(control.decision)}
                                  </span>
                                )}
                              </div>
                            </div>
                          ))}
                        </div>
                      ) : (
                        <span className="text-slate-400 italic bg-gray-50 p-3 rounded border block text-center">{t('workshop3.activity2.table.noControlMeasures')}</span>
                      )}
                    </td>

                    {/* Parties Prenantes Concernées */}
                    <td className="px-4 py-4 text-sm text-slate-900 hover:bg-teal-50/30 transition-colors duration-200">
                      {stakeholders && stakeholders.length > 0 ? (
                        <div className="space-y-2">
                          {stakeholders.filter(s => s.retained).map((stakeholder) => (
                            <div key={stakeholder.id} className="flex items-center bg-teal-50 p-2 rounded-lg border border-teal-200 shadow-sm hover:bg-teal-100 hover:shadow-md transition-all duration-200">
                              <input
                                type="checkbox"
                                id={`stakeholder-${stakeholder.id}-${item.sourceId}-${item.dreadedEventId}`}
                                className="h-4 w-4 text-teal-600 rounded border-slate-300 focus:ring-teal-500 focus:ring-2"
                                checked={(selectedStakeholders[`${item.sourceId}-${item.dreadedEventId}`] || []).includes(stakeholder.id)}
                                onChange={(e) => handleStakeholderSelection(item.sourceId, item.dreadedEventId, stakeholder.id, e.target.checked)}
                              />
                              <label
                                htmlFor={`stakeholder-${stakeholder.id}-${item.sourceId}-${item.dreadedEventId}`}
                                className="ml-3 text-sm text-teal-800 font-semibold truncate cursor-pointer"
                                title={stakeholder.name}
                              >
                                {stakeholder.name}
                              </label>
                            </div>
                          ))}
                        </div>
                      ) : (
                        <span className="text-slate-400 italic bg-gray-50 p-3 rounded border block text-center">{t('workshop3.activity2.table.noRetainedStakeholders')}</span>
                      )}
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
      </div>

      {/* Guide Modal */}
      {isGuideOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-xl shadow-xl p-6 max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl font-bold text-slate-800 flex items-center">
                <Info size={20} className="mr-2 text-blue-600" />
                {t('workshop3.activity2.guide.title')}
              </h2>
              <button
                onClick={() => setIsGuideOpen(false)}
                className="text-slate-500 hover:text-slate-700 focus:outline-none"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            <div className="space-y-4 text-slate-700">
              <div className="bg-slate-50 p-4 rounded-lg border border-slate-200 mb-4">
                <h3 className="font-semibold text-slate-800 mb-2">{t('workshop3.activity2.guide.context.title')}</h3>
                <p className="text-slate-600">
                  {t('workshop3.activity2.guide.context.description')}
                </p>
              </div>

              <div className="bg-blue-50 p-4 rounded-lg border border-blue-100">
                <h3 className="font-semibold text-blue-800 mb-2">{t('workshop3.activity2.guide.understanding.title')}</h3>
                <ul className="list-disc pl-5 space-y-2">
                  <li><strong>{t('workshop3.activity2.guide.understanding.scenarios')}</strong> - {t('workshop3.activity2.guide.understanding.scenariosDesc')}</li>
                  <li><strong>{t('workshop3.activity2.guide.understanding.riskSource')}</strong> - {t('workshop3.activity2.guide.understanding.riskSourceDesc')}</li>
                  <li><strong>{t('workshop3.activity2.guide.understanding.targetObjective')}</strong> - {t('workshop3.activity2.guide.understanding.targetObjectiveDesc')}</li>
                  <li><strong>{t('workshop3.activity2.guide.understanding.dreadedEvent')}</strong> - {t('workshop3.activity2.guide.understanding.dreadedEventDesc')}</li>
                  <li><strong>{t('workshop3.activity2.guide.understanding.businessValue')}</strong> - {t('workshop3.activity2.guide.understanding.businessValueDesc')}</li>
                  <li><strong>{t('workshop3.activity2.guide.understanding.controlMeasures')}</strong> - {t('workshop3.activity2.guide.understanding.controlMeasuresDesc')}</li>
                  <li><strong>{t('workshop3.activity2.guide.understanding.stakeholders')}</strong> - {t('workshop3.activity2.guide.understanding.stakeholdersDesc')}</li>
                </ul>
              </div>

              <div className="bg-amber-50 p-4 rounded-lg border border-amber-100">
                <h3 className="font-semibold text-amber-800 mb-2">{t('workshop3.activity2.guide.actions.title')}</h3>
                <ul className="list-disc pl-5 space-y-1">
                  <li>{t('workshop3.activity2.guide.actions.search')}</li>
                  <li>{t('workshop3.activity2.guide.actions.filter')}</li>
                  <li>{t('workshop3.activity2.guide.actions.sort')}</li>
                  <li>{t('workshop3.activity2.guide.actions.selectStakeholders')}</li>
                </ul>
              </div>
            </div>

            <div className="mt-6 flex justify-end">
              <button
                onClick={() => setIsGuideOpen(false)}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition duration-200"
              >
                {t('common.close')}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default StrategicScenariosTable;
