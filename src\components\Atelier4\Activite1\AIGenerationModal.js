// src/components/Atelier4/Activite2/AIGenerationModal.js
import React, { useState } from 'react';
import './AIGenerationModal.css';

const AIGenerationModal = ({ attackPaths, existingScenarios, onGenerate, onClose }) => {
  const [selectedPaths, setSelectedPaths] = useState([]);
  const [isGenerating, setIsGenerating] = useState(false);
  const [generationOptions, setGenerationOptions] = useState({
    scenariosPerPath: 2,
    includeSteps: true,
    detailLevel: 'detailed' // 'basic', 'detailed', 'comprehensive'
  });

  const handlePathSelection = (pathId) => {
    setSelectedPaths(prev => 
      prev.includes(pathId) 
        ? prev.filter(id => id !== pathId)
        : [...prev, pathId]
    );
  };

  const handleSelectAll = () => {
    if (selectedPaths.length === attackPaths.length) {
      setSelectedPaths([]);
    } else {
      setSelectedPaths(attackPaths.map(path => path.id));
    }
  };

  const handleGenerate = async () => {
    if (selectedPaths.length === 0) return;

    setIsGenerating(true);
    try {
      await onGenerate(selectedPaths, generationOptions);
    } catch (error) {
      console.error('Error generating scenarios:', error);
    } finally {
      setIsGenerating(false);
    }
  };

  const getEstimatedScenarios = () => {
    return selectedPaths.length * generationOptions.scenariosPerPath;
  };

  const getPathInfo = (path) => {
    const existingCount = existingScenarios.filter(s => s.attackPathId === path.id).length;
    return {
      hasExisting: existingCount > 0,
      existingCount
    };
  };

  return (
    <div className="ai-modal-overlay">
      <div className="ai-modal">
        <div className="modal-header">
          <div className="header-content">
            <h3>
              <i className="fas fa-robot"></i>
              Génération de scénarios opérationnels avec IA
            </h3>
            <p>Sélectionnez les chemins d'attaque pour générer des scénarios opérationnels détaillés</p>
          </div>
          <button className="close-btn" onClick={onClose}>
            <i className="fas fa-times"></i>
          </button>
        </div>

        <div className="modal-body">
          <div className="generation-options">
            <h4>Options de génération</h4>
            
            <div className="options-grid">
              <div className="option-group">
                <label>Scénarios par chemin d'attaque</label>
                <select
                  value={generationOptions.scenariosPerPath}
                  onChange={(e) => setGenerationOptions(prev => ({
                    ...prev,
                    scenariosPerPath: parseInt(e.target.value)
                  }))}
                >
                  <option value={1}>1 scénario</option>
                  <option value={2}>2 scénarios</option>
                  <option value={3}>3 scénarios</option>
                  <option value={4}>4 scénarios</option>
                </select>
              </div>

              <div className="option-group">
                <label>Niveau de détail</label>
                <select
                  value={generationOptions.detailLevel}
                  onChange={(e) => setGenerationOptions(prev => ({
                    ...prev,
                    detailLevel: e.target.value
                  }))}
                >
                  <option value="basic">Basique</option>
                  <option value="detailed">Détaillé</option>
                  <option value="comprehensive">Exhaustif</option>
                </select>
              </div>
            </div>

            <div className="option-checkbox">
              <label>
                <input
                  type="checkbox"
                  checked={generationOptions.includeSteps}
                  onChange={(e) => setGenerationOptions(prev => ({
                    ...prev,
                    includeSteps: e.target.checked
                  }))}
                />
                Inclure les étapes opérationnelles détaillées
              </label>
            </div>
          </div>

          <div className="path-selection">
            <div className="selection-header">
              <h4>Chemins d'attaque disponibles ({attackPaths.length})</h4>
              <button 
                className="select-all-btn"
                onClick={handleSelectAll}
              >
                {selectedPaths.length === attackPaths.length ? 'Désélectionner tout' : 'Sélectionner tout'}
              </button>
            </div>

            <div className="paths-list">
              {attackPaths.map(path => {
                const pathInfo = getPathInfo(path);
                return (
                  <div 
                    key={path.id} 
                    className={`path-item ${selectedPaths.includes(path.id) ? 'selected' : ''}`}
                  >
                    <label className="path-checkbox">
                      <input
                        type="checkbox"
                        checked={selectedPaths.includes(path.id)}
                        onChange={() => handlePathSelection(path.id)}
                      />
                      <div className="path-content">
                        <div className="path-header">
                          <span className="path-name">{path.name}</span>
                          {pathInfo.hasExisting && (
                            <span className="existing-badge">
                              {pathInfo.existingCount} existant{pathInfo.existingCount > 1 ? 's' : ''}
                            </span>
                          )}
                        </div>
                        
                        <div className="path-details">
                          <div className="path-info">
                            <span className="info-item">
                              <i className="fas fa-bullseye"></i>
                              {path.sourceRiskName || 'Source non spécifiée'}
                            </span>
                            <span className="info-item">
                              <i className="fas fa-exclamation-triangle"></i>
                              {path.dreadedEventName || 'Événement non spécifié'}
                            </span>
                          </div>
                          
                          {path.description && (
                            <p className="path-description">{path.description}</p>
                          )}
                        </div>
                      </div>
                    </label>
                  </div>
                );
              })}
            </div>
          </div>

          {selectedPaths.length > 0 && (
            <div className="generation-summary">
              <div className="summary-card">
                <h4>Résumé de la génération</h4>
                <div className="summary-stats">
                  <div className="stat">
                    <span className="stat-label">Chemins sélectionnés</span>
                    <span className="stat-value">{selectedPaths.length}</span>
                  </div>
                  <div className="stat">
                    <span className="stat-label">Scénarios à générer</span>
                    <span className="stat-value">{getEstimatedScenarios()}</span>
                  </div>
                  <div className="stat">
                    <span className="stat-label">Niveau de détail</span>
                    <span className="stat-value">
                      {generationOptions.detailLevel === 'basic' ? 'Basique' :
                       generationOptions.detailLevel === 'detailed' ? 'Détaillé' : 'Exhaustif'}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>

        <div className="modal-footer">
          <div className="footer-info">
            {selectedPaths.length === 0 ? (
              <span className="warning">
                <i className="fas fa-exclamation-triangle"></i>
                Sélectionnez au moins un chemin d'attaque
              </span>
            ) : (
              <span className="info">
                <i className="fas fa-info-circle"></i>
                La génération peut prendre quelques minutes
              </span>
            )}
          </div>
          
          <div className="footer-actions">
            <button 
              className="btn btn-secondary" 
              onClick={onClose}
              disabled={isGenerating}
            >
              Annuler
            </button>
            <button 
              className="btn btn-primary" 
              onClick={handleGenerate}
              disabled={selectedPaths.length === 0 || isGenerating}
            >
              {isGenerating ? (
                <>
                  <div className="spinner"></div>
                  Génération en cours...
                </>
              ) : (
                <>
                  <i className="fas fa-magic"></i>
                  Générer les scénarios
                </>
              )}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AIGenerationModal;