// src/components/Atelier 3/Activite1/Activite1.js
import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Save, Plus, Info, Users, BarChart2, Table, Filter } from 'lucide-react';
import { useAnalysis } from '../../../context/AnalysisContext';
import StakeholderForm from './StakeholderForm';
import StakeholderTable from './StakeholderTable';
import ThreatRadarVisualization from './ThreatRadarVisualization';
import GuideModal from './GuideModal';
import { showSuccessToast, showErrorToast, showLoadingToast, updateToast } from '../../../utils/toastUtils';

const Activite1 = () => {
  const [stakeholders, setStakeholders] = useState([]);
  const [selectedStakeholder, setSelectedStakeholder] = useState(null);
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [isGuideOpen, setIsGuideOpen] = useState(false);
  const [activeView, setActiveView] = useState('radar'); // 'radar', 'table', or 'form'
  const [isLoading, setIsLoading] = useState(false);
  const [thresholds, setThresholds] = useState({
    danger: 3,
    control: 1.5,
    watch: 0.5
  });
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [filterType, setFilterType] = useState('all'); // 'all', 'internal', 'external'
  const [filterCategory, setFilterCategory] = useState('all'); // 'all', 'client', 'partner', etc.

  // Get analysis context
  const {
    currentAnalysis,
    getStakeholders,
    saveStakeholders,
    getStakeholderThresholds,
    updateStakeholderThresholds
  } = useAnalysis();

  // Load data when component mounts
  useEffect(() => {
    const loadData = async () => {
      if (currentAnalysis?.id) {
        setIsLoading(true);
        try {
          // Load stakeholders
          const stakeholdersData = await getStakeholders(currentAnalysis.id);
          if (stakeholdersData) {
            setStakeholders(stakeholdersData);
          }

          // Load thresholds
          const thresholdsData = await getStakeholderThresholds(currentAnalysis.id);
          if (thresholdsData) {
            setThresholds(thresholdsData);
          }
        } catch (error) {
          console.error('Error loading data:', error);
          showErrorToast('Erreur lors du chargement des données');
        } finally {
          setIsLoading(false);
        }
      }
    };

    loadData();
  }, [currentAnalysis?.id, getStakeholders, getStakeholderThresholds]);

  // Handle save button click
  const handleSave = async () => {
    if (!currentAnalysis?.id) {
      showErrorToast('Aucune analyse sélectionnée');
      return;
    }

    const toastId = showLoadingToast('Enregistrement des parties prenantes...');
    
    try {
      const response = await saveStakeholders(currentAnalysis.id, stakeholders);
      
      if (response.success) {
        updateToast(toastId, 'Parties prenantes enregistrées avec succès', 'success');
        setHasUnsavedChanges(false);
      } else {
        updateToast(toastId, `Erreur: ${response.message}`, 'error');
      }
    } catch (error) {
      console.error('Error saving stakeholders:', error);
      updateToast(toastId, 'Erreur lors de l\'enregistrement', 'error');
    }
  };

  // Handle threshold update
  const handleUpdateThresholds = async (newThresholds) => {
    if (!currentAnalysis?.id) {
      showErrorToast('Aucune analyse sélectionnée');
      return;
    }

    try {
      const response = await updateStakeholderThresholds(currentAnalysis.id, newThresholds);
      
      if (response.success) {
        setThresholds(newThresholds);
        showSuccessToast('Seuils mis à jour avec succès');
      } else {
        showErrorToast(`Erreur: ${response.message}`);
      }
    } catch (error) {
      console.error('Error updating thresholds:', error);
      showErrorToast('Erreur lors de la mise à jour des seuils');
    }
  };

  // Handle stakeholder creation/update
  const handleStakeholderSubmit = (stakeholderData) => {
    if (selectedStakeholder) {
      // Update existing stakeholder
      const updatedStakeholders = stakeholders.map(s => 
        s.id === selectedStakeholder.id ? { ...s, ...stakeholderData, updatedAt: new Date() } : s
      );
      setStakeholders(updatedStakeholders);
      showSuccessToast('Partie prenante mise à jour avec succès');
    } else {
      // Create new stakeholder
      const newStakeholder = {
        ...stakeholderData,
        id: Date.now().toString(), // Simple ID generation
        createdAt: new Date(),
        updatedAt: new Date()
      };
      setStakeholders([...stakeholders, newStakeholder]);
      showSuccessToast('Partie prenante ajoutée avec succès');
    }
    
    setSelectedStakeholder(null);
    setIsFormOpen(false);
    setHasUnsavedChanges(true);
    setActiveView('radar');
  };

  // Handle stakeholder deletion
  const handleDeleteStakeholder = (id) => {
    setStakeholders(stakeholders.filter(s => s.id !== id));
    setHasUnsavedChanges(true);
    showSuccessToast('Partie prenante supprimée');
  };

  // Filter stakeholders based on current filters
  const filteredStakeholders = stakeholders.filter(stakeholder => {
    const typeMatch = filterType === 'all' || stakeholder.type === filterType;
    const categoryMatch = filterCategory === 'all' || stakeholder.category === filterCategory;
    return typeMatch && categoryMatch;
  });

  return (
    <div className="space-y-6">
      {/* Header with actions */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-xl font-bold text-gray-800">Cartographier l'écosystème</h2>
          <p className="text-gray-600 mt-1">
            Identifiez et évaluez les parties prenantes de l'écosystème
          </p>
        </div>
        <div className="flex space-x-2">
          <button
            onClick={() => setIsGuideOpen(true)}
            className="px-3 py-2 bg-blue-50 text-blue-600 rounded-md hover:bg-blue-100 transition-colors flex items-center"
          >
            <Info size={18} className="mr-1.5" />
            Guide
          </button>
          <button
            onClick={handleSave}
            disabled={!hasUnsavedChanges}
            className={`px-3 py-2 rounded-md flex items-center ${
              hasUnsavedChanges
                ? 'bg-blue-600 text-white hover:bg-blue-700'
                : 'bg-gray-100 text-gray-400 cursor-not-allowed'
            }`}
          >
            <Save size={18} className="mr-1.5" />
            Enregistrer
          </button>
        </div>
      </div>

      {/* View toggle and add button */}
      <div className="flex justify-between items-center">
        <div className="flex space-x-2 bg-gray-100 p-1 rounded-lg">
          <button
            onClick={() => setActiveView('radar')}
            className={`px-3 py-1.5 rounded-md flex items-center ${
              activeView === 'radar' ? 'bg-white shadow-sm text-blue-600' : 'text-gray-600'
            }`}
          >
            <BarChart2 size={18} className="mr-1.5" />
            Visualisation
          </button>
          <button
            onClick={() => setActiveView('table')}
            className={`px-3 py-1.5 rounded-md flex items-center ${
              activeView === 'table' ? 'bg-white shadow-sm text-blue-600' : 'text-gray-600'
            }`}
          >
            <Table size={18} className="mr-1.5" />
            Tableau
          </button>
        </div>
        <button
          onClick={() => {
            setSelectedStakeholder(null);
            setIsFormOpen(true);
            setActiveView('form');
          }}
          className="px-3 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors flex items-center"
        >
          <Plus size={18} className="mr-1.5" />
          Ajouter une partie prenante
        </button>
      </div>

      {/* Filter controls */}
      {(activeView === 'radar' || activeView === 'table') && (
        <div className="flex items-center space-x-4 bg-gray-50 p-3 rounded-lg">
          <div className="flex items-center">
            <Filter size={16} className="text-gray-500 mr-2" />
            <span className="text-sm text-gray-600 mr-2">Filtres:</span>
          </div>
          <select
            value={filterType}
            onChange={(e) => setFilterType(e.target.value)}
            className="text-sm border border-gray-300 rounded-md px-2 py-1"
          >
            <option value="all">Tous les types</option>
            <option value="internal">Interne</option>
            <option value="external">Externe</option>
          </select>
          <select
            value={filterCategory}
            onChange={(e) => setFilterCategory(e.target.value)}
            className="text-sm border border-gray-300 rounded-md px-2 py-1"
          >
            <option value="all">Toutes les catégories</option>
            <option value="client">Client</option>
            <option value="partner">Partenaire</option>
            <option value="provider">Prestataire</option>
            <option value="technical">Service technique</option>
            <option value="business">Service métier</option>
            <option value="subsidiary">Filiale</option>
          </select>
        </div>
      )}

      {/* Main content based on active view */}
      <div className="bg-white rounded-xl border border-gray-200 shadow-sm overflow-hidden">
        {activeView === 'radar' && (
          <ThreatRadarVisualization
            stakeholders={filteredStakeholders}
            thresholds={thresholds}
            onUpdateThresholds={handleUpdateThresholds}
            onSelectStakeholder={(stakeholder) => {
              setSelectedStakeholder(stakeholder);
              setIsFormOpen(true);
              setActiveView('form');
            }}
            isLoading={isLoading}
          />
        )}
        
        {activeView === 'table' && (
          <StakeholderTable
            stakeholders={filteredStakeholders}
            onEdit={(stakeholder) => {
              setSelectedStakeholder(stakeholder);
              setIsFormOpen(true);
              setActiveView('form');
            }}
            onDelete={handleDeleteStakeholder}
            isLoading={isLoading}
          />
        )}
        
        {activeView === 'form' && (
          <StakeholderForm
            stakeholder={selectedStakeholder}
            onSubmit={handleStakeholderSubmit}
            onCancel={() => {
              setSelectedStakeholder(null);
              setIsFormOpen(false);
              setActiveView('radar');
            }}
          />
        )}
      </div>

      {/* Guide Modal */}
      <GuideModal isOpen={isGuideOpen} onClose={() => setIsGuideOpen(false)} />
    </div>
  );
};

export default Activite1;
