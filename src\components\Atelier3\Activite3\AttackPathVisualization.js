// src/components/Atelier3/Activite3/AttackPathVisualization.js
import React, { useState, useCallback, useEffect, useRef } from 'react';
import ReactFlow, {
  MiniMap,
  Controls,
  Background,
  addEdge,
  applyNodeChanges,
  applyEdgeChanges,
  Panel,
  useReactFlow,
} from 'reactflow';
import 'reactflow/dist/style.css';
import { X, ZoomIn, ZoomOut, Maximize2, RefreshCw, Download, Save, FileText, Eye, Printer, Plus } from 'lucide-react';
import { nodeTypes } from './CustomNodes';
import { edgeTypes } from './CustomEdges';
import DiagramLegend from './DiagramLegend';
import NodeDetailsSidebar from './NodeDetailsSidebar';
import NodeContextMenu from './NodeContextMenu';
import { toPng } from 'html-to-image';
import { jsPDF } from 'jspdf';
import api from '../../../api/apiClient';
import { useAnalysis } from '../../../context/AnalysisContext';
import { showSuccessToast, showErrorToast, showLoadingToast, dismissToast } from '../../../utils/toastUtils';
import './FlowStyles.css';

const AttackPathVisualization = ({ attackPath, title = "Visualisation du Chemin d'Attaque", onSave }) => {
  const { currentAnalysis } = useAnalysis();
  const [isSaving, setIsSaving] = useState(false);
  const [nodes, setNodes] = useState([]);
  const [edges, setEdges] = useState([]);
  const [selectedNode, setSelectedNode] = useState(null);
  const [showSidebar, setShowSidebar] = useState(false);
  const [showComparisonView, setShowComparisonView] = useState(false);
  const [nodeIdCounter, setNodeIdCounter] = useState(0);
  const [edgeIdCounter, setEdgeIdCounter] = useState(0);
  const [isModified, setIsModified] = useState(false);
  const [contextMenu, setContextMenu] = useState(null);
  const [selectedEdge, setSelectedEdge] = useState(null);
  const [showEdgeEditModal, setShowEdgeEditModal] = useState(false);

  const reactFlowWrapper = useRef(null);
  const reactFlowInstance = useReactFlow();

  // Create nodes and edges based on the attack path
  useEffect(() => {
    if (!attackPath) return;

    const loadDiagramState = async () => {
      // Check if there's a saved diagram state for this attack path
      if (attackPath.id) {
        try {
          // Try to get from API first
          try {
            const response = await api.get(`/analyses/${currentAnalysis.id}/attack-paths/${attackPath.id}/diagram`);
            if (response.success && response.data && response.data.diagramState) {
              const diagramState = response.data.diagramState;

              // Validate the data structure
              if (diagramState &&
                  Array.isArray(diagramState.nodes) &&
                  Array.isArray(diagramState.edges)) {

                // Validate each node has required properties
                const validNodes = diagramState.nodes.filter(node =>
                  node && node.id && node.position && node.data);

                // Validate each edge has required properties
                const validEdges = diagramState.edges.filter(edge =>
                  edge && edge.id && edge.source && edge.target);

                if (validNodes.length > 0) {
                  setNodes(validNodes);
                  setEdges(validEdges);
                  setNodeIdCounter(validNodes.length);
                  setEdgeIdCounter(validEdges.length);
                  console.log('Loaded saved diagram state from API');
                  return true; // Indicate that we loaded a saved state
                }
              }
            }
          } catch (apiError) {
            console.warn('Could not get diagram state from API, trying local storage:', apiError);
          }

          // If API fails, try to get from local storage
          const storageKey = `diagram-state-${attackPath.id}`;
          const localData = localStorage.getItem(storageKey);

          if (localData) {
            try {
              const parsedData = JSON.parse(localData);

              // Validate the data structure
              if (parsedData &&
                  Array.isArray(parsedData.nodes) &&
                  Array.isArray(parsedData.edges)) {

                // Validate each node has required properties
                const validNodes = parsedData.nodes.filter(node =>
                  node && node.id && node.position && node.data);

                // Validate each edge has required properties
                const validEdges = parsedData.edges.filter(edge =>
                  edge && edge.id && edge.source && edge.target);

                if (validNodes.length > 0) {
                  setNodes(validNodes);
                  setEdges(validEdges);
                  setNodeIdCounter(validNodes.length);
                  setEdgeIdCounter(validEdges.length);
                  console.log('Loaded saved diagram state from local storage');
                  return true; // Indicate that we loaded a saved state
                }
              }
            } catch (parseError) {
              console.error('Error parsing saved diagram state:', parseError);
            }
          }
        } catch (error) {
          console.error('Error loading diagram state:', error);
          // Continue with default diagram creation
        }
      }
      return false; // Indicate that we didn't load a saved state
    };

    const createDefaultDiagram = () => {
      // Create nodes
      const newNodes = [
        // Source Risk Node
        {
          id: 'source-risk',
          type: 'sourceRisk',
          position: { x: 50, y: 150 },
          data: {
            label: attackPath.sourceRiskName,
            description: 'Source de risque identifiée dans l\'analyse',
            riskLevel: 'medium',
            sequence: 1
          },
        },
        // Objectif Visé Node
        {
          id: 'objectif-vise',
          type: 'businessValue',
          position: { x: 650, y: 150 },
          data: {
            label: attackPath.objectifVise,
            description: 'Objectif visé par la source de risque',
            dreadedEvent: attackPath.dreadedEventName,
            businessValue: attackPath.businessValueName,
            sequence: 3
          },
        },
      ];

      // Add only the selected stakeholder node
      let stakeholderNode = null;
      if (attackPath.selectedStakeholder) {
        // If a specific stakeholder is selected, use that one
        stakeholderNode = {
          id: `stakeholder-${attackPath.selectedStakeholder.id}`,
          type: 'stakeholder',
          position: { x: 350, y: 150 },
          data: {
            label: attackPath.selectedStakeholder.name,
            description: 'Partie prenante concernée par l\'événement redouté',
            sequence: 2
          },
        };
        newNodes.push(stakeholderNode);
      } else if (attackPath.stakeholders && attackPath.stakeholders.length > 0) {
        // If no specific stakeholder is selected but stakeholders exist, use the first one
        stakeholderNode = {
          id: `stakeholder-${attackPath.stakeholders[0].id}`,
          type: 'stakeholder',
          position: { x: 350, y: 150 },
          data: {
            label: attackPath.stakeholders[0].name,
            description: 'Partie prenante concernée par l\'événement redouté',
            sequence: 2
          },
        };
        newNodes.push(stakeholderNode);
      }

      // Create edges
      let newEdges = [];

      // Always add a direct attack line (dashed) from Source Risk to Objectif Visé
      newEdges.push({
        id: 'e-direct-attack',
        source: 'source-risk',
        target: 'objectif-vise',
        label: 'Attaque directe',
        labelStyle: { fill: '#ff4500', fontWeight: 600 },
        style: { stroke: '#ff4500', strokeWidth: 1.5 },
        zIndex: 5, // Put this edge above other elements
        // Add data to identify this as a direct attack edge
        data: { edgeType: 'direct-attack' },
        // Use our custom animated edge type
        type: 'animated',
      });

      // Add edges for the stakeholder if it exists
      if (stakeholderNode) {
        // Edge from Source Risk to Stakeholder
        newEdges.push({
          id: `e-source-${stakeholderNode.id}`,
          source: 'source-risk',
          target: stakeholderNode.id,
          type: 'animated', // Use our custom animated edge
          label: 'Cible',
          labelStyle: { fill: '#D8000C', fontWeight: 500 },
          style: { stroke: '#D8000C', strokeWidth: 2 },
          zIndex: 1, // Put this edge in the foreground
        });

        // Edge from Stakeholder to Objectif Visé
        newEdges.push({
          id: `e-${stakeholderNode.id}-objectif`,
          source: stakeholderNode.id,
          target: 'objectif-vise',
          type: 'animated', // Use our custom animated edge
          label: 'Vise',
          labelStyle: { fill: '#00529B', fontWeight: 500 },
          style: { stroke: '#00529B', strokeWidth: 2 },
          zIndex: 1, // Put this edge in the foreground
        });
      }

      setNodes(newNodes);
      setEdges(newEdges);
      setNodeIdCounter(newNodes.length);
      setEdgeIdCounter(newEdges.length);
    };

    // First try to load saved state, if not available create default diagram
    loadDiagramState().then(loadedSavedState => {
      if (!loadedSavedState) {
        createDefaultDiagram();
      }
    });
  }, [attackPath, currentAnalysis]);

  // Handlers for node and edge changes (drag, selection, removal)
  const onNodesChange = useCallback(
    (changes) => {
      setNodes((nds) => applyNodeChanges(changes, nds));
      setIsModified(true);
    },
    [setNodes]
  );

  const onEdgesChange = useCallback(
    (changes) => {
      setEdges((eds) => applyEdgeChanges(changes, eds));
      setIsModified(true);
    },
    [setEdges]
  );

  // Handler for connecting nodes with dynamic handle assignment
  const onConnect = useCallback(
    (connection) => {
      // Find existing connections to determine which handle to use
      const sourceConnections = edges.filter(edge => edge.source === connection.source);
      const targetConnections = edges.filter(edge => edge.target === connection.target);

      // Determine source handle
      let sourceHandle = connection.sourceHandle;
      if (!sourceHandle) {
        // Find the next available output handle
        const usedSourceHandles = sourceConnections.map(edge => edge.sourceHandle || 'output-0');
        let handleIndex = 0;
        while (usedSourceHandles.includes(`output-${handleIndex}`)) {
          handleIndex++;
        }
        sourceHandle = `output-${handleIndex}`;
      }

      // Determine target handle
      let targetHandle = connection.targetHandle;
      if (!targetHandle) {
        // Find the next available input handle
        const usedTargetHandles = targetConnections.map(edge => edge.targetHandle || 'input-0');
        let handleIndex = 0;
        while (usedTargetHandles.includes(`input-${handleIndex}`)) {
          handleIndex++;
        }
        targetHandle = `input-${handleIndex}`;
      }

      const newEdge = {
        ...connection,
        id: `edge-${edgeIdCounter + 1}`,
        animated: true,
        label: 'Connexion',
        style: { stroke: '#666', strokeWidth: 2 },
        labelStyle: { fill: '#666', fontWeight: 500 },
        type: 'animated', // Use our custom animated edge type
        sourceHandle,
        targetHandle,
      };
      setEdges((eds) => addEdge(newEdge, eds));
      setEdgeIdCounter(prev => prev + 1);
      setIsModified(true);
    },
    [setEdges, edgeIdCounter, edges]
  );

  // Handler for edge click
  const onEdgeClick = useCallback((event, edge) => {
    event.stopPropagation();
    setSelectedEdge(edge);
    setShowEdgeEditModal(true);
  }, []);

  // Handler for node selection (single click = select for dragging)
  const onNodeClick = useCallback((event, node) => {
    // Regular click - just select the node for dragging, don't open sidebar
    if (event.button === 0) { // Left click
      setSelectedNode(node);

      // Close context menu if open
      if (contextMenu) {
        setContextMenu(null);
      }
    }
  }, [contextMenu]);

  // Handler for node double-click (double click = edit)
  const onNodeDoubleClick = useCallback((event, node) => {
    event.stopPropagation();

    // Store the current nodes and edges before showing sidebar
    const currentNodes = [...nodes];
    const currentEdges = [...edges];

    setSelectedNode(node);
    setShowSidebar(true);

    // Close context menu if open
    if (contextMenu) {
      setContextMenu(null);
    }

    // Ensure nodes and edges are preserved when opening sidebar
    setTimeout(() => {
      if (currentNodes.length > 0 && nodes.length === 0) {
        setNodes(currentNodes);
        setEdges(currentEdges);
      }
    }, 50);
  }, [contextMenu, nodes, edges]);

  // Handler for node context menu
  const onNodeContextMenu = useCallback((event, node) => {
    // Prevent default context menu
    event.preventDefault();

    // Get the React Flow container's bounding rectangle
    const reactFlowBounds = reactFlowWrapper.current.getBoundingClientRect();

    // Calculate position relative to the React Flow container
    const x = event.clientX - reactFlowBounds.left;
    const y = event.clientY - reactFlowBounds.top;

    // Open our custom context menu
    setContextMenu({
      x: x,
      y: y,
      node: node
    });
  }, []);

  // Handler for updating a node
  const handleUpdateNode = useCallback((nodeId, newData) => {
    setNodes(nds =>
      nds.map(node => {
        if (node.id === nodeId) {
          return {
            ...node,
            data: {
              ...node.data,
              ...newData
            }
          };
        }
        return node;
      })
    );
    setIsModified(true);
  }, []);

  // Handler for updating an edge
  const handleUpdateEdge = useCallback((edgeId, newLabel) => {
    setEdges(eds =>
      eds.map(edge => {
        if (edge.id === edgeId) {
          return {
            ...edge,
            label: newLabel
          };
        }
        return edge;
      })
    );
    setIsModified(true);
    setShowEdgeEditModal(false);
    setSelectedEdge(null);
  }, []);

  // Handler for adding a new node
  const handleAddNode = useCallback((nodeType, nodeData) => {
    const newNodeId = `node-${nodeIdCounter + 1}`;
    const sourceNode = selectedNode;

    // Calculate position based on selected node
    let position = { x: 350, y: 350 };
    if (sourceNode) {
      position = {
        x: sourceNode.position.x + 200,
        y: sourceNode.position.y + 50
      };
    }

    // Create the new node
    const newNode = {
      id: newNodeId,
      type: nodeType,
      position,
      data: {
        ...nodeData,
        sequence: nodes.length + 1
      }
    };

    // Add the node
    setNodes(nds => [...nds, newNode]);
    setNodeIdCounter(prev => prev + 1);

    // If a node is selected, create an edge from it to the new node
    if (sourceNode) {
      const newEdgeId = `edge-${edgeIdCounter + 1}`;
      const newEdge = {
        id: newEdgeId,
        source: sourceNode.id,
        target: newNodeId,
        animated: true,
        label: 'Connexion',
        style: {
          stroke: nodeData.color || '#666',
          strokeWidth: 2
        },
        labelStyle: {
          fill: nodeData.color || '#666',
          fontWeight: 500
        },
        type: 'animated', // Use our custom animated edge type
      };

      setEdges(eds => [...eds, newEdge]);
      setEdgeIdCounter(prev => prev + 1);
    }

    setIsModified(true);
  }, [selectedNode, nodeIdCounter, edgeIdCounter, nodes.length]);

  // Handler for deleting a node
  const handleDeleteNode = useCallback((nodeId) => {
    // Don't allow deletion of source-risk or objectif-vise nodes
    if (nodeId === 'source-risk' || nodeId === 'objectif-vise') {
      return;
    }

    // Remove the node
    setNodes(nds => nds.filter(node => node.id !== nodeId));

    // Remove any edges connected to this node
    setEdges(eds => eds.filter(edge =>
      edge.source !== nodeId && edge.target !== nodeId
    ));

    setShowSidebar(false);
    setSelectedNode(null);
    setIsModified(true);
  }, []);

  // Handler for duplicating a node
  const handleDuplicateNode = useCallback((node) => {
    const newNodeId = `node-${nodeIdCounter + 1}`;

    // Create a copy of the node with a new position slightly offset
    const newNode = {
      ...node,
      id: newNodeId,
      position: {
        x: node.position.x + 50,
        y: node.position.y + 50
      }
    };

    // Add the duplicated node
    setNodes(nds => [...nds, newNode]);
    setNodeIdCounter(prev => prev + 1);
    setIsModified(true);
  }, [nodeIdCounter]);

  // Handler for focusing on a node
  const handleFocusNode = useCallback((node) => {
    if (reactFlowInstance) {
      reactFlowInstance.fitView({
        nodes: [node],
        padding: 0.5,
        duration: 800
      });
    }
  }, [reactFlowInstance]);

  // Function to reset the view
  const resetView = useCallback(() => {
    if (reactFlowInstance) {
      reactFlowInstance.fitView({ padding: 0.2 });
    }
  }, [reactFlowInstance]);

  // Function to export as PNG
  const exportAsPng = useCallback(() => {
    if (reactFlowWrapper.current === null) return;

    toPng(reactFlowWrapper.current, {
      filter: (node) => {
        // Don't include the sidebar in the image
        return !node.classList?.contains('sidebar');
      },
      backgroundColor: '#fff',
      width: reactFlowWrapper.current.offsetWidth,
      height: reactFlowWrapper.current.offsetHeight,
    })
      .then((dataUrl) => {
        const link = document.createElement('a');
        link.download = `chemin-attaque-${new Date().toISOString().split('T')[0]}.png`;
        link.href = dataUrl;
        link.click();
      });
  }, []);

  // Function to export as PDF
  const exportAsPdf = useCallback(() => {
    if (reactFlowWrapper.current === null) return;

    toPng(reactFlowWrapper.current, {
      filter: (node) => {
        // Don't include the sidebar in the image
        return !node.classList?.contains('sidebar');
      },
      backgroundColor: '#fff',
      width: reactFlowWrapper.current.offsetWidth,
      height: reactFlowWrapper.current.offsetHeight,
    })
      .then((dataUrl) => {
        const pdf = new jsPDF({
          orientation: 'landscape',
          unit: 'px',
          format: [reactFlowWrapper.current.offsetWidth, reactFlowWrapper.current.offsetHeight],
        });

        // Add title and metadata
        pdf.setFontSize(16);
        pdf.text(title, 20, 20);
        pdf.setFontSize(10);
        pdf.text(`Généré le ${new Date().toLocaleString()}`, 20, 40);

        // Add the image
        pdf.addImage(
          dataUrl,
          'PNG',
          0,
          50,
          reactFlowWrapper.current.offsetWidth,
          reactFlowWrapper.current.offsetHeight - 50
        );

        pdf.save(`chemin-attaque-${new Date().toISOString().split('T')[0]}.pdf`);
      });
  }, [title]);

  // Function to print the diagram
  const printDiagram = useCallback(() => {
    if (reactFlowWrapper.current === null) return;

    toPng(reactFlowWrapper.current, {
      filter: (node) => {
        // Don't include the sidebar in the image
        return !node.classList?.contains('sidebar');
      },
      backgroundColor: '#fff',
    })
      .then((dataUrl) => {
        const printWindow = window.open('', '_blank');
        printWindow.document.write(`
          <html>
            <head>
              <title>${title}</title>
              <style>
                body { margin: 0; padding: 20px; font-family: Arial, sans-serif; }
                .header { margin-bottom: 20px; }
                .title { font-size: 24px; font-weight: bold; margin-bottom: 10px; }
                .subtitle { font-size: 14px; color: #666; margin-bottom: 20px; }
                img { max-width: 100%; height: auto; }
              </style>
            </head>
            <body>
              <div class="header">
                <div class="title">${title}</div>
                <div class="subtitle">Généré le ${new Date().toLocaleString()}</div>
              </div>
              <img src="${dataUrl}" />
            </body>
          </html>
        `);
        printWindow.document.close();
        printWindow.focus();
        setTimeout(() => {
          printWindow.print();
          printWindow.close();
        }, 500);
      });
  }, [title]);

  // Function to save the diagram state
  const saveDiagramState = useCallback(async () => {
    if (!currentAnalysis?.id || !attackPath?.id) {
      showErrorToast('Impossible de sauvegarder le diagramme: informations manquantes');
      return;
    }

    setIsSaving(true);
    const toastId = showLoadingToast('Sauvegarde du diagramme en cours...');

    try {
      // Validate nodes and edges before saving
      const validNodes = nodes.filter(node => node && node.id && node.position && node.data);
      const validEdges = edges.filter(edge => edge && edge.id && edge.source && edge.target);

      // Get the current diagram state
      const diagramState = {
        nodes: validNodes,
        edges: validEdges
      };

      // Update the attack path with the diagram state
      const updatedAttackPath = {
        ...attackPath,
        diagramState
      };

      // Save the diagram state to local storage
      try {
        const storageKey = `diagram-state-${attackPath.id}`;
        localStorage.setItem(storageKey, JSON.stringify(diagramState));
        console.log('Diagram state saved to local storage');
      } catch (storageError) {
        console.error('Error saving to local storage:', storageError);
      }

      // Try to save to the API
      try {
        const response = await api.post(
          `/analyses/${currentAnalysis.id}/attack-paths/${attackPath.id}/diagram`,
          { diagramState }
        );
        console.log('Diagram state saved to API:', response);
      } catch (apiError) {
        console.warn('Could not save diagram state to API, saved to local storage only:', apiError);
      }

      dismissToast(toastId);
      showSuccessToast('Diagramme sauvegardé avec succès');
      setIsModified(false);

      // Call the onSave callback if provided
      if (onSave && typeof onSave === 'function') {
        onSave(updatedAttackPath);
      }
    } catch (error) {
      console.error('Error saving diagram state:', error);
      dismissToast(toastId);
      showErrorToast('Erreur lors de la sauvegarde du diagramme');
    } finally {
      setIsSaving(false);
    }
  }, [attackPath, currentAnalysis, nodes, edges, onSave]);

  if (!attackPath) return null;

  return (
    <div className="bg-white rounded-xl shadow-md mt-8 overflow-hidden">
      {/* Header */}
      <div className="p-4 border-b border-slate-200 flex justify-between items-center bg-gradient-to-r from-blue-50 to-white">
        <h2 className="text-xl font-semibold text-slate-800 flex items-center">
          <Eye size={20} className="mr-2 text-blue-600" />
          {title}
          {isModified && <span className="ml-2 text-xs text-orange-500">(Modifié)</span>}
        </h2>
        <div className="flex space-x-2">
          <button
            onClick={resetView}
            className="p-2 text-slate-500 hover:text-slate-700 hover:bg-slate-100 rounded-full transition-colors"
            title="Réinitialiser la vue"
          >
            <RefreshCw size={18} />
          </button>
          <button
            onClick={exportAsPdf}
            className="p-2 text-slate-500 hover:text-slate-700 hover:bg-slate-100 rounded-full transition-colors"
            title="Exporter en PDF"
          >
            <FileText size={18} />
          </button>
          <button
            onClick={printDiagram}
            className="p-2 text-slate-500 hover:text-slate-700 hover:bg-slate-100 rounded-full transition-colors"
            title="Imprimer"
          >
            <Printer size={18} />
          </button>
          <button
            onClick={saveDiagramState}
            disabled={isSaving || !isModified}
            className={`p-2 ${isModified ? 'text-blue-600 hover:text-blue-800' : 'text-slate-400'} hover:bg-slate-100 rounded-full transition-colors`}
            title="Sauvegarder le diagramme"
          >
            <Save size={18} />
          </button>
        </div>
      </div>

      {/* Flow diagram with sidebar */}
      <div className="flex" style={{ height: '600px' }}>
        <div
          ref={reactFlowWrapper}
          className="flex-grow relative"
        >
          <ReactFlow
            nodes={nodes}
            edges={edges}
            onNodesChange={onNodesChange}
            onEdgesChange={onEdgesChange}
            onConnect={onConnect}
            onNodeClick={onNodeClick}
            onNodeDoubleClick={onNodeDoubleClick}
            onNodeContextMenu={onNodeContextMenu}
            onEdgeClick={onEdgeClick}
            nodeTypes={nodeTypes}
            edgeTypes={edgeTypes}
            fitView
            attributionPosition="bottom-left"
          >
            <Controls />
            <MiniMap
              nodeColor={(n) => {
                if (n.type === 'sourceRisk') return '#FFCACA';
                if (n.type === 'dreadedEvent') return '#FFF3CD';
                if (n.type === 'businessValue') return '#DFF2BF';
                if (n.type === 'businessValueStandalone') return '#E9D5FF';
                if (n.type === 'stakeholder') return '#D9E8FF';
                return '#E2E8F0';
              }}
              nodeStrokeWidth={3}
              pannable
              zoomable
            />
            <Background color="#ccc" variant="dots" gap={16} size={1} />
            <DiagramLegend />

            {/* Context Menu */}
            {contextMenu && (
              <NodeContextMenu
                x={contextMenu.x}
                y={contextMenu.y}
                node={contextMenu.node}
                onClose={() => setContextMenu(null)}
                onEdit={(node) => {
                  setSelectedNode(node);
                  setShowSidebar(true);
                }}
                onDelete={handleDeleteNode}
                onDuplicate={handleDuplicateNode}
                onFocus={handleFocusNode}
              />
            )}

            <Panel position="top-right" className="bg-white p-2 rounded-md shadow-md border border-slate-200">
              <div className="flex space-x-2">
                <button
                  onClick={() => {
                    setSelectedNode(null);
                    setShowSidebar(true);
                    // No need to force the add form, it will show automatically when selectedNode is null
                  }}
                  className="px-3 py-1 bg-blue-600 text-white text-sm rounded hover:bg-blue-700 transition-colors flex items-center"
                >
                  <Plus size={14} className="mr-1" />
                  Ajouter un nœud
                </button>
              </div>
            </Panel>
          </ReactFlow>
        </div>

        {showSidebar && (
          <div className="sidebar">
            <NodeDetailsSidebar
              selectedNode={selectedNode}
              onClose={() => {
                setShowSidebar(false);
                setSelectedNode(null);
              }}
              onUpdateNode={handleUpdateNode}
              onAddNode={handleAddNode}
              onDeleteNode={handleDeleteNode}
              attackPath={attackPath}
            />
          </div>
        )}
      </div>

      {/* Footer */}
      <div className="p-4 border-t border-slate-200 text-sm text-slate-500 bg-slate-50">
        <p>
          <strong>Simple clic :</strong> Sélectionner et déplacer les nœuds •
          <strong> Double-clic :</strong> Modifier les propriétés des nœuds •
          <strong> Clic sur connexion :</strong> Modifier le titre de la connexion
        </p>
      </div>

      {/* Edge Edit Modal */}
      {showEdgeEditModal && selectedEdge && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-96 max-w-md mx-4">
            <h3 className="text-lg font-semibold text-slate-800 mb-4">Modifier la connexion</h3>
            <div className="mb-4">
              <label className="block text-sm font-medium text-slate-700 mb-2">
                Titre de la connexion
              </label>
              <input
                type="text"
                defaultValue={selectedEdge.label || 'Connexion'}
                className="w-full p-3 border border-slate-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                placeholder="Entrez le titre de la connexion"
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    handleUpdateEdge(selectedEdge.id, e.target.value);
                  } else if (e.key === 'Escape') {
                    setShowEdgeEditModal(false);
                    setSelectedEdge(null);
                  }
                }}
                autoFocus
              />
            </div>
            <div className="flex justify-end space-x-3">
              <button
                onClick={() => {
                  setShowEdgeEditModal(false);
                  setSelectedEdge(null);
                }}
                className="px-4 py-2 bg-slate-200 text-slate-800 rounded-lg hover:bg-slate-300 transition duration-200"
              >
                Annuler
              </button>
              <button
                onClick={(e) => {
                  const input = e.target.closest('.bg-white').querySelector('input');
                  handleUpdateEdge(selectedEdge.id, input.value);
                }}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition duration-200"
              >
                Enregistrer
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AttackPathVisualization;
