// src/hooks/useSessionManager.js
import { useState, useEffect, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { showWarningToast } from '../utils/toastUtils';

const SESSION_DURATION = 4 * 60 * 60 * 1000; // 4 hours in milliseconds
const WARNING_10_MIN = 10 * 60 * 1000; // 10 minutes in milliseconds
const WARNING_5_MIN = 5 * 60 * 1000; // 5 minutes in milliseconds

export const useSessionManager = () => {
  const { t } = useTranslation();
  const [sessionStartTime, setSessionStartTime] = useState(null);
  const [warningShown10Min, setWarningShown10Min] = useState(false);
  const [warningShown5Min, setWarningShown5Min] = useState(false);

  // Initialize session when user logs in
  const initializeSession = useCallback(() => {
    const now = Date.now();
    setSessionStartTime(now);
    setWarningShown10Min(false);
    setWarningShown5Min(false);

    // Store session start time in localStorage
    localStorage.setItem('sessionStartTime', now.toString());

    console.log('Session initialized for 4 hours');
  }, []);

  // Get remaining session time
  const getRemainingTime = useCallback(() => {
    if (!sessionStartTime) return 0;

    const now = Date.now();
    const elapsed = now - sessionStartTime;
    const remaining = SESSION_DURATION - elapsed;

    return Math.max(0, remaining);
  }, [sessionStartTime]);

  // Handle session expiry - simply refresh the page
  const handleSessionExpiry = useCallback(() => {
    console.log('Session expired - refreshing page');
    window.location.reload();
  }, []);

  // Check if session is still valid on app load
  const checkExistingSession = useCallback(() => {
    const token = localStorage.getItem('token');
    const storedSessionStart = localStorage.getItem('sessionStartTime');

    if (token && storedSessionStart) {
      const sessionStart = parseInt(storedSessionStart);
      const now = Date.now();
      const elapsed = now - sessionStart;

      if (elapsed < SESSION_DURATION) {
        setSessionStartTime(sessionStart);
        return true;
      } else {
        // Session expired, refresh page
        handleSessionExpiry();
        return false;
      }
    }

    return false;
  }, [handleSessionExpiry]);

  // Session monitoring effect
  useEffect(() => {
    if (!sessionStartTime) return;

    const checkSession = () => {
      const remaining = getRemainingTime();

      // Session expired - refresh page
      if (remaining <= 0) {
        handleSessionExpiry();
        return;
      }

      // Show 10-minute warning
      if (remaining <= WARNING_10_MIN && !warningShown10Min) {
        setWarningShown10Min(true);
        showWarningToast(t('session.warning10Min'), { duration: 10000 });
      }

      // Show 5-minute warning
      if (remaining <= WARNING_5_MIN && !warningShown5Min) {
        setWarningShown5Min(true);
        showWarningToast(t('session.warning5Min'), { duration: 15000 });
      }
    };

    // Check every minute
    const interval = setInterval(checkSession, 60000);

    // Initial check
    checkSession();

    return () => clearInterval(interval);
  }, [
    sessionStartTime,
    warningShown10Min,
    warningShown5Min,
    getRemainingTime,
    handleSessionExpiry,
    t
  ]);

  // Check existing session on mount
  useEffect(() => {
    checkExistingSession();
  }, [checkExistingSession]);

  return {
    initializeSession,
    getRemainingTime,
    sessionStartTime: sessionStartTime ? new Date(sessionStartTime) : null
  };
};

export default useSessionManager;
