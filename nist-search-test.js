// NIST API Search Test - Demonstrating different search strategies
// Run this in browser console to test NIST API search methods

const testNistSearch = async () => {
  const nistBaseUrl = 'https://services.nvd.nist.gov/rest/json/cves/2.0';
  
  // Test asset: Cisco ISR 4000
  const testAsset = {
    name: "Routeur principal",
    vendor: "Cisco",
    product: "ISR 4000", 
    version: "16.12.05",
    type: "reseaux"
  };

  console.log('🧪 Testing NIST API Search Strategies for:', testAsset);
  console.log('================================================');

  // Strategy 1: Keyword Search (most flexible)
  console.log('\n🔍 Strategy 1: Keyword Search');
  try {
    const keywords = `${testAsset.vendor} ${testAsset.product}`.trim();
    const params1 = new URLSearchParams({
      keywordSearch: keywords,
      resultsPerPage: '10',
      startIndex: '0'
    });
    
    console.log(`Searching for: "${keywords}"`);
    console.log(`URL: ${nistBaseUrl}?${params1}`);
    
    const response1 = await fetch(`${nistBaseUrl}?${params1}`);
    const data1 = await response1.json();
    
    console.log(`✅ Keyword search results:`, {
      totalResults: data1.totalResults,
      vulnerabilitiesFound: data1.vulnerabilities?.length || 0,
      firstCVE: data1.vulnerabilities?.[0]?.cve?.id || 'None'
    });
    
    if (data1.vulnerabilities?.length > 0) {
      console.log('📋 First few CVEs:', data1.vulnerabilities.slice(0, 3).map(v => v.cve.id));
    }
  } catch (error) {
    console.error('❌ Keyword search failed:', error);
  }

  // Strategy 2: Virtual CPE Match (partial CPE with wildcards)
  console.log('\n🔍 Strategy 2: Virtual CPE Match');
  try {
    // Create partial CPE with wildcards
    const virtualCPE = `cpe:2.3:*:cisco:*`;
    const params2 = new URLSearchParams({
      virtualMatchString: virtualCPE,
      resultsPerPage: '10',
      startIndex: '0'
    });
    
    console.log(`Virtual CPE: "${virtualCPE}"`);
    console.log(`URL: ${nistBaseUrl}?${params2}`);
    
    const response2 = await fetch(`${nistBaseUrl}?${params2}`);
    const data2 = await response2.json();
    
    console.log(`✅ Virtual CPE results:`, {
      totalResults: data2.totalResults,
      vulnerabilitiesFound: data2.vulnerabilities?.length || 0,
      firstCVE: data2.vulnerabilities?.[0]?.cve?.id || 'None'
    });
    
    if (data2.vulnerabilities?.length > 0) {
      console.log('📋 First few CVEs:', data2.vulnerabilities.slice(0, 3).map(v => v.cve.id));
    }
  } catch (error) {
    console.error('❌ Virtual CPE search failed:', error);
  }

  // Strategy 3: Exact CPE Match (most restrictive)
  console.log('\n🔍 Strategy 3: Exact CPE Match');
  try {
    // Generate exact CPE
    const exactCPE = `cpe:2.3:h:cisco:integrated_services_router:16.12.05:*:*:*:*:*:*:*`;
    const params3 = new URLSearchParams({
      cpeName: exactCPE,
      resultsPerPage: '10',
      startIndex: '0'
    });
    
    console.log(`Exact CPE: "${exactCPE}"`);
    console.log(`URL: ${nistBaseUrl}?${params3}`);
    
    const response3 = await fetch(`${nistBaseUrl}?${params3}`);
    const data3 = await response3.json();
    
    console.log(`✅ Exact CPE results:`, {
      totalResults: data3.totalResults,
      vulnerabilitiesFound: data3.vulnerabilities?.length || 0,
      firstCVE: data3.vulnerabilities?.[0]?.cve?.id || 'None'
    });
    
    if (data3.vulnerabilities?.length > 0) {
      console.log('📋 First few CVEs:', data3.vulnerabilities.slice(0, 3).map(v => v.cve.id));
    }
  } catch (error) {
    console.error('❌ Exact CPE search failed:', error);
  }

  // Strategy 4: Product-only search
  console.log('\n🔍 Strategy 4: Product-Only Search');
  try {
    const productOnly = testAsset.product;
    const params4 = new URLSearchParams({
      keywordSearch: productOnly,
      resultsPerPage: '5',
      startIndex: '0'
    });
    
    console.log(`Product search: "${productOnly}"`);
    console.log(`URL: ${nistBaseUrl}?${params4}`);
    
    const response4 = await fetch(`${nistBaseUrl}?${params4}`);
    const data4 = await response4.json();
    
    console.log(`✅ Product-only results:`, {
      totalResults: data4.totalResults,
      vulnerabilitiesFound: data4.vulnerabilities?.length || 0,
      firstCVE: data4.vulnerabilities?.[0]?.cve?.id || 'None'
    });
    
    if (data4.vulnerabilities?.length > 0) {
      console.log('📋 First few CVEs:', data4.vulnerabilities.slice(0, 3).map(v => v.cve.id));
    }
  } catch (error) {
    console.error('❌ Product-only search failed:', error);
  }

  console.log('\n🎯 SUMMARY:');
  console.log('- Keyword search is usually most effective');
  console.log('- Virtual CPE allows partial matching with wildcards');
  console.log('- Exact CPE is very restrictive but precise');
  console.log('- Product-only search provides broader results');
  console.log('\n💡 Recommendation: Use keyword search first, then fallback to others');
};

// Export for use
window.testNistSearch = testNistSearch;

console.log('🚀 NIST Search Test loaded!');
console.log('Run: testNistSearch() in console to test different search strategies');
