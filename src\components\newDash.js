
// src/components/dashboard/Dashboard.js
import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>hart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer, Cell } from 'recharts';
import {
    <PERSON><PERSON><PERSON> as <PERSON><PERSON>hartL<PERSON><PERSON>,
    <PERSON><PERSON><PERSON> as <PERSON><PERSON>hartLucide,
    Users,
    Target,
    Calendar,
    Shield,
    AlertTriangle,
    ChevronDown,
    ChevronRight,
    ChevronUp,
    Download,
    RefreshCw,
    Info,
    FileText,
    Settings,
    ExternalLink,
    TrendingUp,
    Filter,
    Clock,
    CheckCircle,
    XCircle,
    HelpCircle,
    Eye,
    LineChart,
    Zap,
    Award
} from 'lucide-react';
import AnalysisSelector from './AnalysisSelector'; // Assuming this component works independently or can handle no selection
import { useAnalysis } from '../context/AnalysisContext'; // We'll keep the hook but override its data

// --- Mock Data ---
const MOCK_DATA_ENABLED = false; // Set to true to use mock data

const mockCurrentAnalysis = {
    id: 'analysis-mock-123',
    name: '<PERSON><PERSON><PERSON> de Risques - Projet "Titan"',
    companyName: 'CyberSecure Inc.',
    updatedAt: new Date(Date.now() - 86400000 * 2).toISOString(), // 2 days ago
};

const mockContextData = {
    organizationName: '',
    missions: [],
    analysisDate: '',
    participants: [
        { id: 'p1', name: 'Alice Dubois', email: '<EMAIL>', position: 'RSSI', customPosition: null },
        { id: 'p2', name: 'Bob Martin', email: '<EMAIL>', position: 'Chef de Projet IT', customPosition: null },
        { id: 'p3', name: 'Charlie Dupont', email: '<EMAIL>', position: 'Architecte Sécurité', customPosition: null },
        { id: 'p4', name: 'Diana Lefevre', email: '<EMAIL>', position: 'Responsable Conformité', customPosition: null },
        { id: 'p5', name: 'Ethan Moreau', email: '<EMAIL>', position: 'Développeur Senior', customPosition: null },
        { id: 'p6', name: 'Fiona Petit', email: '<EMAIL>', position: 'OTHER', customPosition: 'Analyste Risques Externes' },
    ],
    matrix: { // Simple mock RACI matrix
        p1: { scope: 'A', risks: 'R', controls: 'A', report: 'R' },
        p2: { scope: 'R', risks: 'C', controls: 'I', report: 'I' },
        p3: { scope: 'C', risks: 'A', controls: 'R', report: 'C' },
        p4: { scope: 'C', risks: 'C', controls: 'C', report: 'A' },
        p5: { scope: 'I', risks: 'I', controls: 'I', report: 'I' },
        p6: { scope: 'I', risks: 'R', controls: 'C', report: 'C' },
    },
};

const mockBusinessValues = [
    { id: 'bv1', name: 'Réputation de la marque', description: 'Confiance des clients et image publique.', securityPillars: ['Confidentialité', 'Intégrité'] },
    { id: 'bv2', name: 'Continuité des opérations', description: 'Capacité à maintenir les services essentiels.', securityPillars: ['Disponibilité'] },
    { id: 'bv3', name: 'Propriété intellectuelle', description: 'Protection des secrets commerciaux et innovations.', securityPillars: ['Confidentialité', 'Intégrité'] },
    { id: 'bv4', name: 'Conformité réglementaire', description: 'Respect des lois (RGPD, etc.).', securityPillars: ['Confidentialité', 'Intégrité', 'Disponibilité'] },
    { id: 'bv5', name: 'Données clients sensibles', description: 'Informations personnelles et financières.', securityPillars: ['Confidentialité'] },
];

const mockDreadedEvents = [
    { id: 'de1', name: 'Fuite de données clients massive', description: 'Exfiltration de la base de données clients.', severity: 'critical', securityPillar: 'Confidentialité', businessValue: 'bv5' },
    { id: 'de2', name: 'Attaque Ransomware sur serveurs critiques', description: 'Chiffrement des serveurs de production.', severity: 'catastrophic', securityPillar: 'Disponibilité', businessValue: 'bv2' },
    { id: 'de3', name: 'Modification non autorisée du site web', description: 'Défacement du site public.', severity: 'major', securityPillar: 'Intégrité', businessValue: 'bv1' },
    { id: 'de4', name: 'Interruption prolongée du service cloud', description: 'Panne majeure du fournisseur cloud.', severity: 'critical', securityPillar: 'Disponibilité', businessValue: 'bv2' },
    { id: 'de5', name: 'Vol de code source propriétaire', description: 'Accès non autorisé au dépôt de code.', severity: 'major', securityPillar: 'Confidentialité', businessValue: 'bv3' },
    { id: 'de6', name: 'Non-conformité RGPD suite à un audit', description: 'Amende réglementaire potentielle.', severity: 'major', securityPillar: 'Confidentialité', businessValue: 'bv4' },
    { id: 'de7', name: 'Accès frauduleux à un compte admin', description: 'Compromission des identifiants administrateur.', severity: 'critical', securityPillar: 'Intégrité', businessValue: 'bv5' },
    { id: 'de8', name: 'Phishing réussi sur un employé clé', description: 'Compromission d\'un compte interne.', severity: 'moderate', securityPillar: 'Confidentialité', businessValue: 'bv1' },
    { id: 'de9', name: 'Attaque DDoS sur l\'infrastructure réseau', description: 'Indisponibilité temporaire des services web.', severity: 'major', securityPillar: 'Disponibilité', businessValue: 'bv2'},
    { id: 'de10', name: 'Erreur humaine de configuration sécurité', description: 'Exposition accidentelle de données.', severity: 'moderate', securityPillar: 'Confidentialité', businessValue: 'bv5'},
];

const mockSecurityFramework = { // Minimal mock data
    id: 'fwk-1',
    name: 'ISO 27001:2022',
    rules: [ { id: 'rule1', name: 'A.5.1 Policies for information security' } ],
};

const mockSecurityControls = [
    { id: 'sc1', name: 'Authentification Multi-Facteurs (MFA)', description: 'Mise en place de MFA pour tous les accès sensibles.', status: 'implemented', type: 'Préventif', priority: 'Haute' },
    { id: 'sc2', name: 'Chiffrement des données au repos', description: 'Chiffrement des bases de données et stockages.', status: 'implemented', type: 'Préventif', priority: 'Haute' },
    { id: 'sc3', name: 'Scans de vulnérabilités réguliers', description: 'Analyse hebdomadaire des systèmes exposés.', status: 'partial', type: 'Détectif', priority: 'Moyenne' },
    { id: 'sc4', name: 'Plan de Continuité d\'Activité (PCA)', description: 'Test annuel du plan de reprise après sinistre.', status: 'planned', type: 'Correctif', priority: 'Haute' },
    { id: 'sc5', name: 'Formation de sensibilisation à la sécurité', description: 'Formation annuelle obligatoire pour tous.', status: 'implemented', type: 'Préventif', priority: 'Moyenne' },
    { id: 'sc6', name: 'Système de Détection d\'Intrusion (IDS/IPS)', description: 'Surveillance du trafic réseau entrant/sortant.', status: 'implemented', type: 'Détectif', priority: 'Haute' },
    { id: 'sc7', name: 'Gestion des correctifs (Patch Management)', description: 'Application mensuelle des correctifs critiques.', status: 'partial', type: 'Préventif', priority: 'Moyenne' },
    { id: 'sc8', name: 'Sauvegardes externalisées et testées', description: 'Sauvegardes quotidiennes, restauration testée trimestriellement.', status: 'implemented', type: 'Correctif', priority: 'Haute' },
    { id: 'sc9', name: 'Contrôle d\'accès basé sur les rôles (RBAC)', description: 'Application du principe de moindre privilège.', status: 'implemented', type: 'Préventif', priority: 'Moyenne' },
    { id: 'sc10', name: 'Journalisation centralisée (SIEM)', description: 'Collecte et analyse des logs de sécurité.', status: 'planned', type: 'Détectif', priority: 'Haute' },
    { id: 'sc11', name: 'Politique de mot de passe robuste', description: 'Exigence de complexité et rotation régulière.', status: 'implemented', type: 'Préventif', priority: 'Basse'},
    { id: 'sc12', name: 'Filtrage des emails entrants', description: 'Protection anti-spam et anti-phishing.', status: 'implemented', type: 'Préventif', priority: 'Moyenne'},
];

const mockRiskTreatments = [ // Minimal mock data, not actively used in UI yet
    { id: 'rt1', eventId: 'de1', action: 'Mitigate', controlIds: ['sc1', 'sc2'], description: 'Renforcer le chiffrement et MFA.' },
    { id: 'rt2', eventId: 'de2', action: 'Mitigate', controlIds: ['sc4', 'sc8'], description: 'Améliorer PCA et sauvegardes.' },
];


// --- Enhanced Helper Components ---

// Modern Card with hover effects and optional gradient
const InfoCard = ({ title, message, icon = FileText, value = null, trend = null, color = 'blue' }) => {
    const colorMap = {
        blue: 'from-blue-50 to-blue-100 border-blue-200 shadow-blue-100/40',
        green: 'from-emerald-50 to-emerald-100 border-emerald-200 shadow-emerald-100/40',
        purple: 'from-violet-50 to-violet-100 border-violet-200 shadow-violet-100/40',
        amber: 'from-amber-50 to-amber-100 border-amber-200 shadow-amber-100/40',
        red: 'from-red-50 to-red-100 border-red-200 shadow-red-100/40',
    };

    const iconColorMap = {
        blue: 'text-blue-500',
        green: 'text-emerald-500',
        purple: 'text-violet-500',
        amber: 'text-amber-500',
        red: 'text-red-500',
    };

    return (
        <motion.div
            whileHover={{ y: -3, boxShadow: '0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 8px 10px -6px rgba(0, 0, 0, 0.05)' }}
            transition={{ duration: 0.2 }}
            className={`bg-gradient-to-br ${colorMap[color]} p-5 rounded-xl border shadow-lg relative overflow-hidden flex flex-col justify-between min-h-[130px]`}
        >
            <div className="absolute right-0 top-0 h-full w-1/3 opacity-10 pointer-events-none">
                {React.createElement(icon, { size: 80, className: "absolute -right-5 -top-5 transform rotate-12" })}
            </div>
            <div>
                <div className="flex items-center mb-1">
                    {React.createElement(icon, { size: 18, className: `mr-2 ${iconColorMap[color]}` })}
                    <h3 className="text-sm font-medium text-gray-600">{title}</h3>
                </div>
            </div>
            {value !== null ? (
                <div className="mt-3">
                    <span className="text-3xl font-bold text-gray-800">{value}</span>
                    {trend && (
                        <div className="flex items-center mt-1">
                            <span className={`text-xs ${trend > 0 ? 'text-green-600' : 'text-red-600'} flex items-center`}>
                                {trend > 0 ? '↑' : '↓'} {Math.abs(trend)}%
                                <span className="text-gray-500 ml-1">depuis le dernier mois</span>
                            </span>
                        </div>
                    )}
                </div>
            ) : (
                <div className="flex items-center justify-center text-center text-xs text-gray-500 mt-1 flex-grow bg-white/50 p-2 rounded-lg">
                    {React.createElement(icon, { size: 16, className: "mr-1 flex-shrink-0" })}
                    <span>{message || 'À définir'}</span>
                </div>
            )}
        </motion.div>
    );
};

// Enhanced Section with collapsible functionality
const InfoSection = ({ title, message, icon = FileText, children = null, collapsible = false }) => {
    const [isCollapsed, setIsCollapsed] = useState(collapsible); // Start collapsed if collapsible

    return (
        <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-white rounded-xl border border-gray-200 shadow-md overflow-hidden"
        >
            <div className="p-4 border-b border-gray-200 bg-gray-50 flex justify-between items-center">
                <h3 className="font-medium text-gray-700 flex items-center">
                    {React.createElement(icon, { size: 18, className: "mr-2 text-gray-500" })}
                    {title}
                </h3>
                {collapsible && (
                    <button
                        onClick={() => setIsCollapsed(!isCollapsed)}
                        className="p-1 rounded-full hover:bg-gray-200 transition-colors"
                        aria-label={isCollapsed ? "Développer la section" : "Réduire la section"}
                    >
                        {isCollapsed ? <ChevronRight size={18} /> : <ChevronUp size={18} />}
                    </button>
                )}
            </div>

            <AnimatePresence>
                {(!collapsible || !isCollapsed) && (
                    <motion.div
                        initial={{ height: 0, opacity: 0 }}
                        animate={{ height: "auto", opacity: 1 }}
                        exit={{ height: 0, opacity: 0 }}
                        transition={{ duration: 0.2 }}
                        style={{ overflow: 'hidden' }} // Important for height animation
                    >
                        {children || (
                            <div className="p-8 text-center">
                                {React.createElement(icon, { size: 48, className: `mx-auto mb-3 text-gray-300` })}
                                <p className={`text-gray-500`}>
                                    {message || 'Aucune donnée définie pour cette section.'}
                                </p>
                            </div>
                        )}
                    </motion.div>
                )}
            </AnimatePresence>
        </motion.div>
    );
};

// Enhanced Empty Chart
const EmptyChartPlaceholder = ({ title, icon = BarChartLucide }) => (
    <motion.div
        whileHover={{ boxShadow: '0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 8px 10px -6px rgba(0, 0, 0, 0.05)' }}
        className="bg-white p-5 rounded-xl border border-gray-200 shadow-md"
    >
        <div className="flex items-center mb-4">
            {React.createElement(icon, { size: 20, className: "mr-2 text-gray-400" })}
            <h3 className="font-medium text-gray-700">{title}</h3>
        </div>
        <div className="h-[300px] flex flex-col items-center justify-center text-center bg-gray-50 rounded-lg">
            {React.createElement(icon, { size: 48, className: `text-gray-300 mb-3 opacity-50` })}
            <p className="text-sm text-gray-400">Données insuffisantes pour afficher le graphique.</p>
        </div>
    </motion.div>
);

// Enhanced chart container with a modern look
const ChartContainer = ({ title, icon, children }) => (
    <motion.div
        // Variants can be added if needed within a parent container
        // variants={itemVariants}
        whileHover={{ boxShadow: '0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 8px 10px -6px rgba(0, 0, 0, 0.05)' }}
        transition={{ duration: 0.2 }}
        className="bg-white p-5 rounded-xl border border-gray-200 shadow-md"
    >
        <div className="flex items-center justify-between mb-4">
            <div className="flex items-center">
                {React.createElement(icon, { size: 20, className: "mr-2 text-gray-500" })}
                <h3 className="font-medium text-gray-700">{title}</h3>
            </div>
            <div className="flex space-x-1">
                <button className="p-1.5 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-full transition-colors" aria-label="Actualiser">
                    <RefreshCw size={16} />
                </button>
                <button className="p-1.5 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-full transition-colors" aria-label="Options">
                    <Settings size={16} />
                </button>
            </div>
        </div>
        {children}
    </motion.div>
);

// --- Main Dashboard Component ---
const Dashboard = ({ preferences = {}, setPreferences }) => {
    // --- Get Data SOLELY from Context (or Mocks) ---
    const contextData = useAnalysis();

    // --- OVERRIDE WITH MOCK DATA if enabled ---
    const {
        currentAnalysis,
        currentContextData,
        currentBusinessValues,
        currentDreadedEvents,
        currentSecurityFramework,
        currentSecurityControls,
        currentRiskTreatments,
        isWorkshopDataLoading,
        workshopDataError
    } = MOCK_DATA_ENABLED ? {
        currentAnalysis: mockCurrentAnalysis,
        currentContextData: mockContextData,
        currentBusinessValues: mockBusinessValues,
        currentDreadedEvents: mockDreadedEvents,
        currentSecurityFramework: mockSecurityFramework,
        currentSecurityControls: mockSecurityControls,
        currentRiskTreatments: mockRiskTreatments,
        isWorkshopDataLoading: false, // Force loading state off for mock data
        workshopDataError: null,      // Force error state off for mock data
    } : contextData;

    // --- Local UI State ---
    const [activeSection, setActiveSection] = useState(preferences?.activeSection || 'overview');
    const [securityFilter, setSecurityFilter] = useState('all');
    const [securityView, setSecurityView] = useState('grid');

    // Update activeSection in preferences
    useEffect(() => {
        if (setPreferences && activeSection !== preferences?.activeSection) {
            setPreferences({ ...preferences, activeSection });
        }
    // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [activeSection]); // Only trigger when activeSection changes

    // --- Use Context/Mock Data Directly ---
    // Assign defaults, assume data exists unless explicitly null/undefined
    const context = currentContextData || {};
    const bValues = currentBusinessValues || [];
    const events = currentDreadedEvents || [];
    // const frameworkData = currentSecurityFramework || null; // Contains rules, frameworks etc. (Not heavily used yet)
    const controlsData = currentSecurityControls || [];
    // const treatmentsData = currentRiskTreatments || []; // Not used yet, but available

    // --- Prepare data for charts (only if source data exists) ---
    const participantRolesData = context?.participants?.length > 0 ? (context.participants).reduce((acc, p) => {
        let role = p.position === "OTHER" && p.customPosition ? p.customPosition : (p.position || 'Undefined');
        // Group similar roles for better chart readability if needed
        if (role.toLowerCase().includes('responsable')) role = 'Responsable';
        if (role.toLowerCase().includes('chef')) role = 'Chef de Projet/Équipe';
        if (role.toLowerCase().includes('architect')) role = 'Architecte';
        if (role.toLowerCase().includes('développeur')) role = 'Développeur';

        const existing = acc.find(item => item.name === role);
        if (existing) existing.value += 1; else acc.push({ name: role, value: 1 });
        return acc;
    }, []) : [];

    const securityPillarEventsData = events?.length > 0 ? (events).reduce((acc, event) => {
        // Normalize security pillar to handle case inconsistencies (especially for auditabilite/Auditabilite)
        let pillar = event.securityPillar || 'Undefined';
        // Normalize auditabilite to always use the same capitalization (Auditabilite)
        if (pillar.toLowerCase() === 'auditabilite') {
            pillar = 'Auditabilite';
        }

        const severity = event.severity || 'Undefined'; // Use severity directly
        let pillarItem = acc.find(item => item.pillar === pillar);
        if (!pillarItem) {
             // Initialize all possible severities for consistent stacking
            pillarItem = { pillar, catastrophic: 0, critical: 0, major: 0, moderate: 0, minor: 0 }; // Changed 'low' to 'minor'
            acc.push(pillarItem);
        }
         // Increment the count for the specific severity
        if (severity && pillarItem.hasOwnProperty(severity)) {
            pillarItem[severity] += 1;
        }
        return acc;
    }, []) : [];


    const businessValuePillarsData = bValues?.length > 0 ? (bValues).reduce((acc, bv) => {
        if (Array.isArray(bv.securityPillars)) {
            bv.securityPillars.forEach(pillar => {
                const pillarName = pillar || 'Undefined';
                const existing = acc.find(item => item.name === pillarName);
                if (existing) existing.value += 1; else acc.push({ name: pillarName, value: 1 });
            });
        }
        return acc;
    }, []) : [];

    // Filter security controls based on selected filter
    const filteredControls = controlsData.filter(control => {
        if (securityFilter === 'all') return true;
        return control.status === securityFilter;
    });

    // Color palettes - Enhanced with more modern colors
    const ROLE_COLORS = ['#6366F1', '#10B981', '#8B5CF6', '#F43F5E', '#F59E0B', '#3B82F6', '#EC4899', '#14B8A6']; // Added more colors
    const SEVERITY_COLORS = { // Matched keys to severity values
        catastrophic: '#1E293B', // Slate 800
        critical: '#DC2626',     // Red 600
        major: '#F97316',        // Orange 600
        moderate: '#FACC15',     // Yellow 500 (more visible than amber)
        minor: '#10B981'         // Emerald 500
    };
    const PILLAR_COLORS = ['#6366F1', '#10B981', '#F43F5E', '#F97316', '#8B5CF6', '#EC4899', '#22D3EE']; // Added Cyan

    // Tab variants for animation
    const tabVariants = {
        active: {
            backgroundColor: '#EFF6FF', // blue-50
            color: '#1D4ED8',         // blue-700
            borderColor: '#93C5FD',    // blue-300
            boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)'
        },
        inactive: {
            backgroundColor: '#F9FAFB', // gray-50
            color: '#4B5563',         // gray-600
            borderColor: '#E5E7EB',    // gray-200
            boxShadow: 'none'
        }
    };

    // Animation for container transitions
    const containerVariants = {
        hidden: { opacity: 0, y: 10 },
        visible: {
            opacity: 1,
            y: 0,
            transition: {
                duration: 0.4,
                staggerChildren: 0.08 // Slightly faster stagger
            }
        }
    };

    const itemVariants = {
        hidden: { opacity: 0, y: 15 }, // Slightly more vertical movement
        visible: { opacity: 1, y: 0 }
    };

    // Button group variants
    const buttonGroupVariants = {
        active: {
            backgroundColor: '#4F46E5', // indigo-600
            color: 'white',
            borderColor: '#4338CA', // indigo-700
        },
        inactive: {
            backgroundColor: '#F9FAFB', // gray-50
            color: '#6B7280',         // gray-500
            borderColor: '#E5E7EB',    // gray-200
        }
    };

    // --- Main Render ---
    return (
        <motion.div
            initial={{ opacity: 0, y: 15 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="bg-gradient-to-b from-white to-gray-50 p-6 rounded-xl shadow-lg border border-gray-200 min-h-screen" // Ensure min height
        >
            {/* Enhanced Header */}
            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
                <div>
                    <h2 className="text-2xl font-bold text-gray-800 flex items-center">
                        <BarChartLucide size={24} className="mr-2 text-blue-500" />
                        Tableau de bord
                        <span className="ml-2 px-2 py-1 bg-blue-100 text-blue-800 text-xs font-medium rounded-full">v2.1 Mock</span>
                    </h2>
                    <p className="text-sm text-gray-500 mt-1 ml-9">Analyse et suivi des risques de sécurité</p>
                </div>
                <div className="flex flex-wrap gap-2">
                    <div className="bg-gray-100 rounded-md px-3 py-1.5 text-sm text-gray-700 flex items-center shadow-sm">
                        <Calendar size={16} className="mr-1.5 text-gray-500" />
                        {new Date().toLocaleDateString('fr-FR', {weekday: 'long', year: 'numeric', month: 'long', day: 'numeric'})}
                    </div>
                    <motion.button
                        whileHover={{ scale: 1.03 }}
                        whileTap={{ scale: 0.97 }}
                        className="bg-white text-gray-700 px-4 py-2 rounded-md hover:bg-gray-100 transition-colors duration-200 flex items-center shadow-sm border border-gray-200"
                        onClick={() => alert('Options du tableau de bord')}
                    >
                        <Settings size={18} className="mr-2" />
                        Options
                    </motion.button>
                    <motion.button
                        whileHover={{ scale: 1.03 }}
                        whileTap={{ scale: 0.97 }}
                        className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors duration-200 flex items-center shadow-sm disabled:opacity-50 disabled:cursor-not-allowed"
                        onClick={() => alert('Exportation des données de l\'analyse mockée')}
                        disabled={!currentAnalysis} // Should be enabled with mock data
                    >
                        <Download size={18} className="mr-2" />
                        Exporter
                    </motion.button>
                </div>
            </div>

            {/* Analysis Selector - Still useful even with mocks to show structure */}
            <div className="mb-6 bg-white p-4 rounded-xl shadow-md border border-gray-200">
                <div className="flex items-center mb-2">
                    <h3 className="text-sm font-medium text-gray-700">Sélection de l'analyse</h3>
                     <div className="ml-2 px-2 py-0.5 bg-yellow-50 rounded-full text-xs text-yellow-700 flex items-center">
                        <Info size={12} className="mr-1" />
                        {MOCK_DATA_ENABLED ? 'Données de démonstration actives' : 'Choisissez une analyse pour voir ses données'}
                    </div>
                </div>
                 {/* Assuming AnalysisSelector can handle a default or show selection */}
                <AnalysisSelector />
            </div>

            {/* Loading State */}
            {isWorkshopDataLoading && (
                <motion.div
                    initial={{ opacity: 0, y: 5 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="bg-blue-50 border border-blue-100 rounded-lg p-5 my-6 flex items-center justify-center"
                >
                    <RefreshCw size={22} className="animate-spin text-blue-500 mr-3" />
                    <p className="text-blue-700 font-medium">Chargement des données...</p>
                </motion.div>
            )}

            {/* Error state */}
            {workshopDataError && !isWorkshopDataLoading && (
                <motion.div
                    initial={{ opacity: 0, y: 5 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="bg-red-50 border border-red-100 rounded-lg p-5 my-6 flex items-center"
                >
                    <XCircle size={22} className="text-red-500 mr-3 flex-shrink-0" />
                    <div>
                        <p className="text-red-700 font-medium">Erreur lors du chargement des données</p>
                        <p className="text-red-600 text-sm mt-1">Veuillez réessayer ou contacter le support si le problème persiste.</p>
                    </div>
                    <button className="ml-auto bg-white border border-red-300 text-red-600 px-3 py-1 rounded-md text-sm hover:bg-red-50">
                        Réessayer
                    </button>
                </motion.div>
            )}

            {/* Content Area: Render if NOT loading AND an analysis IS selected (or mocked) */}
            {!isWorkshopDataLoading && currentAnalysis && (
                <AnimatePresence mode="wait">
                    <motion.div
                        key={activeSection} // Re-trigger animation on section change
                        initial="hidden"
                        animate="visible"
                        exit="hidden" // Add exit animation if needed
                        variants={containerVariants}
                        className="space-y-8" // Add spacing between sections managed by this container
                    >
                        {/* Enhanced Navigation Tabs */}
                        <div className="flex flex-wrap gap-3 mb-8">
                            {[
                                { id: 'overview', label: "Vue d'ensemble", icon: BarChartLucide },
                                { id: 'participants', label: "Participants", icon: Users },
                                { id: 'events', label: "Événements redoutés", icon: AlertTriangle },
                                { id: 'security', label: "Mesures de sécurité", icon: Shield },
                                { id: 'analysis', label: "Analyse & Actions", icon: LineChart }, // Renamed for clarity
                            ].map(section => (
                                <motion.button
                                    key={section.id}
                                    variants={tabVariants}
                                    animate={activeSection === section.id ? 'active' : 'inactive'}
                                    whileHover={{ scale: 1.02, y: -1 }}
                                    whileTap={{ scale: 0.98 }}
                                    transition={{ duration: 0.15 }}
                                    className="px-5 py-2.5 rounded-lg text-sm font-medium border flex items-center focus:outline-none focus-visible:ring-2 focus-visible:ring-blue-400 focus-visible:ring-offset-2"
                                    onClick={() => setActiveSection(section.id)}
                                >
                                    {React.createElement(section.icon, { size: 16, className: "mr-2" })}
                                    {section.label}
                                </motion.button>
                            ))}
                        </div>

                        {/* Main Content based on activeSection */}
                        {/* Wrap each section's content in a motion.div for staggering */}
                        <motion.div variants={itemVariants} className="space-y-8">
                            {/* Overview Section */}
                            {activeSection === 'overview' && (
                                <div className="space-y-8">
                                    {/* Analytics Overview Banner */}
                                    <motion.div
                                        variants={itemVariants} // Apply item variant here too
                                        className="bg-gradient-to-r from-blue-600 to-indigo-600 rounded-xl p-6 text-white shadow-lg" // Increased padding
                                    >
                                        <div className="flex flex-col md:flex-row justify-between items-start md:items-center">
                                            <div>
                                                <h3 className="text-xl font-semibold mb-1 flex items-center"> {/* Increased font size */}
                                                    <Shield size={22} className="mr-2 text-blue-200" /> {/* Slightly larger icon */}
                                                    Analyse: {currentAnalysis?.name}
                                                </h3>
                                                <p className="text-blue-100 text-sm max-w-xl">
                                                    Ce tableau de bord présente une vue d'ensemble de votre analyse de risques,
                                                    y compris les événements redoutés, les valeurs métier et les mesures de sécurité.
                                                </p>
                                            </div>
                                            <div className="flex gap-3 mt-4 md:mt-0 flex-shrink-0"> {/* Prevent shrinking */}
                                                <motion.button
                                                    whileHover={{ scale: 1.05, backgroundColor: '#DBEAFE' }} // Lighter blue bg on hover
                                                    whileTap={{ scale: 0.95 }}
                                                    className="bg-white text-blue-600 px-4 py-2 rounded-lg text-sm font-medium shadow-sm hover:bg-blue-50 transition-colors flex items-center"
                                                    onClick={() => alert('Actualisation des données mockées... (aucune action)')}
                                                >
                                                    <RefreshCw size={16} className="mr-2" />
                                                    Actualiser
                                                </motion.button>
                                                <motion.button
                                                    whileHover={{ scale: 1.05 }}
                                                    whileTap={{ scale: 0.95 }}
                                                    className="bg-blue-500 text-white px-4 py-2 rounded-lg text-sm font-medium shadow-sm hover:bg-blue-400 transition-colors border border-blue-400 flex items-center"
                                                    onClick={() => alert('Génération du rapport PDF mocké...')}
                                                >
                                                    <Download size={16} className="mr-2" />
                                                    Rapport PDF
                                                </motion.button>
                                            </div>
                                        </div>
                                    </motion.div>

                                    {/* Summary Cards - Enhanced with colors and mock trends */}
                                    <motion.div variants={itemVariants} className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-5">
                                        <InfoCard
                                            title="Participants"
                                            value={context?.participants?.length ?? 'N/A'}
                                            icon={Users}
                                            trend={12} // Mock trend
                                            color="blue"
                                        />
                                        <InfoCard
                                            title="Valeurs métier"
                                            value={bValues?.length ?? 'N/A'}
                                            icon={Target}
                                            trend={8} // Mock trend
                                            color="green"
                                        />
                                        <InfoCard
                                            title="Événements redoutés"
                                            value={events?.length ?? 'N/A'}
                                            icon={AlertTriangle}
                                            trend={-5} // Mock trend
                                            color="amber"
                                        />
                                        <InfoCard
                                            title="Mesures de sécurité"
                                            value={controlsData?.length ?? 'N/A'}
                                            icon={Shield}
                                            trend={15} // Mock trend
                                            color="purple"
                                        />
                                    </motion.div>

                                    {/* Charts - Enhanced with modern containers */}
                                    <motion.div variants={itemVariants} className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                                        {/* Participant Roles Chart */}
                                        {(participantRolesData.length > 0) ? (
                                            <ChartContainer title="Répartition des rôles" icon={Users}>
                                                <ResponsiveContainer width="100%" height={300}>
                                                    <PieChart>
                                                        <Pie
                                                            data={participantRolesData}
                                                            dataKey="value"
                                                            nameKey="name" // Ensure nameKey is set
                                                            label={({ name, percent }) => `${name} (${(percent * 100).toFixed(0)}%)`}
                                                            outerRadius={90}
                                                            innerRadius={40} // Make it a donut chart
                                                            fill="#8884d8"
                                                            labelLine={false}
                                                            animationDuration={800}
                                                            animationBegin={200}
                                                        >
                                                            {participantRolesData.map((entry, index) => (
                                                                <Cell key={`cell-roles-${index}`} fill={ROLE_COLORS[index % ROLE_COLORS.length]} />
                                                            ))}
                                                        </Pie>
                                                        <Tooltip contentStyle={{ borderRadius: '8px', boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)', border: 'none', backgroundColor: 'rgba(255, 255, 255, 0.9)' }} />
                                                        <Legend />
                                                    </PieChart>
                                                </ResponsiveContainer>
                                            </ChartContainer>
                                        ) : (
                                            <EmptyChartPlaceholder title="Répartition des rôles" icon={PieChartLucide} />
                                        )}

                                        {/* Dreaded Events Chart */}
                                        {(securityPillarEventsData.length > 0) ? (
                                            <ChartContainer title="Événements redoutés par pilier & sévérité" icon={AlertTriangle}>
                                                <ResponsiveContainer width="100%" height={300}>
                                                    <BarChart
                                                        data={securityPillarEventsData}
                                                        layout="vertical"
                                                        margin={{ top: 5, right: 30, left: 30, bottom: 5 }} // Adjusted left margin
                                                        barGap={4} // Increased gap slightly
                                                        barCategoryGap="20%" // Use percentage for category gap
                                                    >
                                                        <CartesianGrid strokeDasharray="3 3" stroke="#e5e7eb" /> {/* Lighter grid */}
                                                        <XAxis type="number" />
                                                        <YAxis dataKey="pillar" type="category" width={100} tick={{ fontSize: 11 }}/> {/* Adjusted width */}
                                                        <Tooltip
                                                             formatter={(value, name) => [value, name.charAt(0).toUpperCase() + name.slice(1)]} // Capitalize severity name in tooltip
                                                            contentStyle={{
                                                                borderRadius: '8px',
                                                                boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
                                                                border: 'none',
                                                                backgroundColor: 'rgba(255, 255, 255, 0.9)'
                                                            }}
                                                            cursor={{ fill: 'rgba(230, 230, 230, 0.3)' }} // Add hover cursor effect
                                                        />
                                                        <Legend wrapperStyle={{ fontSize: '12px', paddingTop: '10px' }}/>
                                                        {Object.entries(SEVERITY_COLORS).map(([severityKey, color]) => (
                                                            <Bar
                                                                key={severityKey}
                                                                dataKey={severityKey}
                                                                stackId="a" // Ensure stacking
                                                                fill={color}
                                                                name={severityKey.charAt(0).toUpperCase() + severityKey.slice(1)}
                                                                radius={[0, 4, 4, 0]} // Rounded corners on the right
                                                                animationDuration={800}
                                                                animationBegin={200}
                                                            />
                                                        ))}
                                                    </BarChart>
                                                </ResponsiveContainer>
                                            </ChartContainer>
                                        ) : (
                                            <EmptyChartPlaceholder title="Événements redoutés par pilier" icon={BarChartLucide}/>
                                        )}
                                    </motion.div>

                                    <motion.div variants={itemVariants} className="grid grid-cols-1 lg:grid-cols-2 gap-6 mt-6">
                                        {/* Business Value Pillars Chart */}
                                        {(businessValuePillarsData.length > 0) ? (
                                            <ChartContainer title="Piliers de sécurité liés aux valeurs métier" icon={Target}>
                                                <ResponsiveContainer width="100%" height={300}>
                                                    <PieChart>
                                                        <Pie
                                                            data={businessValuePillarsData}
                                                            dataKey="value"
                                                            nameKey="name"
                                                            label={({ name, percent }) => `${name} (${(percent * 100).toFixed(0)}%)`}
                                                            outerRadius={90}
                                                            innerRadius={40} // Donut chart
                                                            fill="#8884d8"
                                                            labelLine={false}
                                                            animationDuration={800}
                                                            animationBegin={200}
                                                        >
                                                            {businessValuePillarsData.map((entry, index) => (
                                                                <Cell key={`cell-bv-${index}`} fill={PILLAR_COLORS[index % PILLAR_COLORS.length]} />
                                                            ))}
                                                        </Pie>
                                                        <Tooltip contentStyle={{ borderRadius: '8px', boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)', border: 'none', backgroundColor: 'rgba(255, 255, 255, 0.9)' }} />
                                                        <Legend />
                                                    </PieChart>
                                                </ResponsiveContainer>
                                            </ChartContainer>
                                        ) : (
                                            <EmptyChartPlaceholder title="Piliers des valeurs métier" icon={PieChartLucide}/>
                                        )}

                                        {/* Risk Matrix Chart - Simplified for Mock Data */}
                                        <ChartContainer title="Matrice des risques (Exemple)" icon={TrendingUp}>
                                            {(events?.length > 0) ? (
                                                <div className="relative h-[300px] mt-8 mb-12"> {/* Added margin for labels */}
                                                    <div className="absolute inset-0">
                                                        {/* Background grid */}
                                                        <div className="grid grid-cols-5 grid-rows-5 h-full border border-gray-200 rounded-lg overflow-hidden">
                                                            {Array.from({ length: 25 }).map((_, index) => {
                                                                const row = Math.floor(index / 5); // Impact (0=Catastrophic, 4=Minor)
                                                                const col = index % 5;          // Likelihood (0=Rare, 4=Frequent)

                                                                // Determine color based on risk zone (example logic)
                                                                let bgColor = 'bg-green-100'; // Low
                                                                if ((row <= 1 && col >= 3) || (row <= 2 && col >= 4)) bgColor = 'bg-red-100'; // High
                                                                else if ((row <= 2 && col >= 2) || (row <= 3 && col >= 3)) bgColor = 'bg-orange-100'; // Medium-High
                                                                else if ((row <= 3 && col >= 1) || (row <=4 && col >= 2)) bgColor = 'bg-yellow-100'; // Medium-Low

                                                                return (
                                                                    <div
                                                                        key={`cell-${row}-${col}`}
                                                                        className={`border border-gray-100 ${bgColor}`}
                                                                    ></div>
                                                                );
                                                            })}
                                                        </div>

                                                        {/* Axis labels */}
                                                        <div className="absolute left-0 top-0 h-full flex flex-col justify-around py-2 -ml-[75px] text-right">
                                                            <span className="text-xs text-gray-500 font-medium">Catastrophique</span>
                                                            <span className="text-xs text-gray-500 font-medium">Critique</span>
                                                            <span className="text-xs text-gray-500 font-medium">Majeur</span>
                                                            <span className="text-xs text-gray-500 font-medium">Modéré</span>
                                                            <span className="text-xs text-gray-500 font-medium">Mineur</span>
                                                        </div>

                                                        <div className="absolute bottom-0 left-0 w-full flex justify-around px-2 -mb-6">
                                                            <span className="text-xs text-gray-500 font-medium">Rare</span>
                                                            <span className="text-xs text-gray-500 font-medium">Peu probable</span>
                                                            <span className="text-xs text-gray-500 font-medium">Possible</span>
                                                            <span className="text-xs text-gray-500 font-medium">Probable</span>
                                                            <span className="text-xs text-gray-500 font-medium">Fréquent</span>
                                                        </div>

                                                        {/* Event dots - Placed based on severity and pseudo-likelihood */}
                                                        {events.slice(0, 10).map((event, index) => {
                                                            const severityMap = { 'minor': 4, 'moderate': 3, 'major': 2, 'critical': 1, 'catastrophic': 0 };
                                                            const row = severityMap[event.severity] ?? 3; // Default to moderate if undefined
                                                            // Pseudo-likelihood based on index/severity for variety
                                                            const col = (index + row * 2) % 5;

                                                            const offsetX = (Math.random() * 0.4 + 0.3) * 100; // 30% to 70%
                                                            const offsetY = (Math.random() * 0.4 + 0.3) * 100; // 30% to 70%

                                                            const severityColor = SEVERITY_COLORS[event.severity] || '#9CA3AF'; // Default gray

                                                            return (
                                                                <motion.div
                                                                    key={event.id}
                                                                    initial={{ scale: 0, opacity: 0 }}
                                                                    animate={{ scale: 1, opacity: 1 }}
                                                                    transition={{ delay: index * 0.08, duration: 0.3 }}
                                                                    className="absolute w-5 h-5 -ml-2.5 -mt-2.5 flex items-center justify-center group"
                                                                    style={{
                                                                        left: `${(col * 20) + offsetX}%`,
                                                                        top: `${(row * 20) + offsetY}%`
                                                                    }}
                                                                    title={`${index + 1}. ${event.name} (${event.severity})`}
                                                                >
                                                                    <div className={`w-4 h-4 rounded-full flex items-center justify-center text-xs font-bold text-white shadow-md`}
                                                                         style={{ backgroundColor: severityColor }}>
                                                                        {index + 1}
                                                                    </div>
                                                                     {/* Tooltip on hover - basic */}
                                                                    {/* <span className="absolute bottom-full mb-2 w-max px-2 py-1 bg-gray-800 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none z-10">
                                                                        {index + 1}. {event.name}
                                                                    </span> */}
                                                                </motion.div>
                                                            );
                                                        })}

                                                        {/* Axis titles */}
                                                        <div className="absolute -left-16 top-1/2 -translate-y-1/2 -rotate-90 text-sm font-medium text-gray-700">
                                                            Impact
                                                        </div>
                                                        <div className="absolute bottom-0 left-1/2 -translate-x-1/2 -mb-10 text-sm font-medium text-gray-700">
                                                            Probabilité
                                                        </div>
                                                    </div>
                                                </div>
                                            ) : (
                                                <div className="h-[300px] flex flex-col items-center justify-center text-center bg-gray-50 rounded-lg">
                                                    <TrendingUp size={48} className="text-gray-300 mb-3" />
                                                    <p className="text-sm text-gray-500 font-medium">Matrice de risques indisponible</p>
                                                    <p className="text-xs text-gray-400 max-w-xs mt-1">
                                                        Ajoutez des événements redoutés pour générer votre matrice de risques.
                                                    </p>
                                                </div>
                                            )}
                                        </ChartContainer>
                                    </motion.div>

                                    <motion.div variants={itemVariants} className="grid grid-cols-1 lg:grid-cols-2 gap-6 mt-6">
                                        {/* Security Status Summary - Using Mock Values */}
                                        <ChartContainer title="Statut global de sécurité (Exemple)" icon={Shield}>
                                            <div className="p-4">
                                                <div className="mb-6 flex justify-center">
                                                    <motion.div
                                                        initial={{ opacity: 0, scale: 0.8 }}
                                                        animate={{ opacity: 1, scale: 1 }}
                                                        transition={{ delay: 0.3, duration: 0.5 }}
                                                        className="relative w-36 h-36" // Slightly larger
                                                    >
                                                        <svg className="w-full h-full" viewBox="0 0 100 100">
                                                            <circle cx="50" cy="50" r="45" fill="none" stroke="#e5e7eb" strokeWidth="10" />
                                                            <motion.circle
                                                                cx="50" cy="50" r="45"
                                                                fill="none"
                                                                stroke={controlsData?.length > 0 ? "#3B82F6" : "#D1D5DB"} // Blue or Gray
                                                                strokeWidth="10"
                                                                strokeLinecap="round"
                                                                strokeDasharray={2 * Math.PI * 45} // Circumference
                                                                initial={{ strokeDashoffset: 2 * Math.PI * 45 }}
                                                                animate={{ strokeDashoffset: (2 * Math.PI * 45) * (1 - (controlsData?.length ? 0.65 : 0)) }} // Mock 65%
                                                                transition={{ duration: 1, delay: 0.5, ease: "easeOut" }}
                                                                transform="rotate(-90 50 50)"
                                                            />
                                                        </svg>
                                                        <div className="absolute inset-0 flex items-center justify-center">
                                                            <div className="text-center">
                                                                <span className="text-3xl font-bold text-gray-800">{controlsData?.length ? '65%' : 'N/A'}</span>
                                                                <span className="block text-xs text-gray-500 mt-1">Complétude</span>
                                                            </div>
                                                        </div>
                                                    </motion.div>
                                                </div>

                                                <div className="space-y-4">
                                                    <div className="flex justify-between items-center">
                                                        <span className="text-sm text-gray-600 flex items-center"><CheckCircle size={14} className="mr-1 text-green-500"/>Mesures en place</span>
                                                        <div className="flex items-center">
                                                            <span className="text-sm font-medium text-gray-800">
                                                                {controlsData?.filter(c => c.status === 'implemented')?.length || 0}
                                                            </span>
                                                            <span className="text-xs text-gray-500 ml-1">/{controlsData?.length || 0}</span>
                                                        </div>
                                                    </div>
                                                    <div className="flex justify-between items-center">
                                                        <span className="text-sm text-gray-600 flex items-center"><Clock size={14} className="mr-1 text-amber-500"/>Mesures partielles</span>
                                                        <div className="flex items-center">
                                                            <span className="text-sm font-medium text-gray-800">
                                                                {controlsData?.filter(c => c.status === 'partial')?.length || 0}
                                                            </span>
                                                             <span className="text-xs text-gray-500 ml-1">/{controlsData?.length || 0}</span>
                                                        </div>
                                                    </div>
                                                    <div className="flex justify-between items-center">
                                                         <span className="text-sm text-gray-600 flex items-center"><Calendar size={14} className="mr-1 text-blue-500"/>Mesures planifiées</span>
                                                        <div className="flex items-center">
                                                            <span className="text-sm font-medium text-gray-800">
                                                                {controlsData?.filter(c => c.status === 'planned')?.length || 0}
                                                            </span>
                                                             <span className="text-xs text-gray-500 ml-1">/{controlsData?.length || 0}</span>
                                                        </div>
                                                    </div>

                                                    {/* Progress bars for mock compliance/mitigation */}
                                                    <div>
                                                        <div className="flex justify-between items-center mb-1">
                                                            <span className="text-sm text-gray-600">Risques critiques mitigés (Est.)</span>
                                                            <span className="text-sm font-medium text-green-600">78%</span>
                                                        </div>
                                                        <div className="h-2.5 bg-gray-100 rounded-full overflow-hidden">
                                                            <motion.div
                                                                className="h-full bg-gradient-to-r from-emerald-400 to-green-500 rounded-full"
                                                                initial={{ width: 0 }}
                                                                animate={{ width: '78%' }}
                                                                transition={{ duration: 0.8, delay: 0.8 }}
                                                            ></motion.div>
                                                        </div>
                                                    </div>
                                                    <div>
                                                        <div className="flex justify-between items-center mb-1">
                                                            <span className="text-sm text-gray-600">Conformité référentiel (Est.)</span>
                                                            <span className="text-sm font-medium text-amber-600">52%</span>
                                                        </div>
                                                        <div className="h-2.5 bg-gray-100 rounded-full overflow-hidden">
                                                            <motion.div
                                                                className="h-full bg-gradient-to-r from-yellow-400 to-amber-500 rounded-full"
                                                                initial={{ width: 0 }}
                                                                animate={{ width: '52%' }}
                                                                transition={{ duration: 0.8, delay: 1.0 }}
                                                            ></motion.div>
                                                        </div>
                                                    </div>
                                                </div>

                                                <div className="mt-6 pt-4 border-t border-gray-200">
                                                    <h4 className="text-sm font-medium text-gray-700 mb-2 flex items-center"><Zap size={14} className="mr-1.5 text-indigo-500"/>Prochaines étapes suggérées:</h4>
                                                    <ul className="text-xs text-gray-600 space-y-1.5">
                                                        <li className="flex items-start">
                                                            <span className="text-blue-500 mr-1.5 mt-0.5">•</span>
                                                            Finaliser l'implémentation des {controlsData?.filter(c => c.status === 'partial')?.length} mesures partielles.
                                                        </li>
                                                        <li className="flex items-start">
                                                            <span className="text-blue-500 mr-1.5 mt-0.5">•</span>
                                                            Planifier et exécuter les {controlsData?.filter(c => c.status === 'planned')?.length} mesures planifiées.
                                                        </li>
                                                         <li className="flex items-start">
                                                            <span className="text-blue-500 mr-1.5 mt-0.5">•</span>
                                                            Revoir les {events?.filter(e => ['critical', 'catastrophic'].includes(e.severity)).length} risques critiques/catastrophiques.
                                                        </li>
                                                    </ul>
                                                </div>
                                            </div>
                                        </ChartContainer>

                                        {/* Recent Activities - Using Mock Data */}
                                        <ChartContainer title="Activités récentes (Exemple)" icon={Clock}>
                                            <div className="p-2">
                                                <div className="space-y-3 max-h-[320px] overflow-y-auto pr-2 custom-scrollbar"> {/* Increased height */}
                                                    {[ // More detailed mock activities
                                                        { id: 1, type: 'control', action: 'add', user: 'Alice Dubois', time: '2h', item: 'MFA pour accès admin' },
                                                        { id: 2, type: 'event', action: 'update', user: 'Charlie Dupont', time: '4h', item: 'Risque Ransomware (Sévérité: Catastrophic)' },
                                                        { id: 3, type: 'participant', action: 'add', user: 'Alice Dubois', time: '1j', item: 'Fiona Petit (Analyste Externe)' },
                                                        { id: 4, type: 'value', action: 'update', user: 'Diana Lefevre', time: '1j', item: 'Conformité réglementaire (Piliers ajoutés)' },
                                                        { id: 5, type: 'control', action: 'status', user: 'Bob Martin', time: '2j', item: 'Scan Vulnérabilités (Passé: Partiel -> Implémenté)' },
                                                        { id: 6, type: 'event', action: 'add', user: 'Charlie Dupont', time: '3j', item: 'Attaque DDoS (Sévérité: Major)' },
                                                        { id: 7, type: 'treatment', action: 'assign', user: 'Alice Dubois', time: '3j', item: 'Traitement pour Fuite de données assigné à Bob' },
                                                        { id: 8, type: 'report', action: 'generate', user: 'Diana Lefevre', time: '4j', item: 'Rapport d\'avancement généré' },
                                                    ].map((activity, index) => (
                                                        <motion.div
                                                            key={activity.id}
                                                            initial={{ opacity: 0, x: -15 }}
                                                            animate={{ opacity: 1, x: 0 }}
                                                            transition={{ delay: index * 0.08, duration: 0.3 }}
                                                            className="flex items-start p-2.5 rounded-lg hover:bg-gray-50 transition-colors duration-150"
                                                        >
                                                            <div className={`p-2 rounded-full mr-3 mt-0.5 ${
                                                                activity.action === 'add' ? 'bg-green-100' :
                                                                activity.action === 'update' || activity.action === 'status' || activity.action === 'assign' ? 'bg-blue-100' :
                                                                activity.action === 'delete' ? 'bg-red-100' :
                                                                'bg-gray-100' // Default for generate etc.
                                                            }`}>
                                                                {activity.action === 'add' ? <CheckCircle size={16} className="text-green-600" /> :
                                                                 activity.action === 'update' || activity.action === 'status' ? <RefreshCw size={16} className="text-blue-600" /> :
                                                                 activity.action === 'delete' ? <XCircle size={16} className="text-red-600" /> :
                                                                 activity.action === 'assign' ? <Users size={16} className="text-purple-600" /> :
                                                                 <FileText size={16} className="text-gray-600" />}
                                                            </div>
                                                            <div className="flex-grow">
                                                                <div className="flex justify-between items-start">
                                                                    <p className="text-sm text-gray-700 leading-tight">
                                                                        <span className="font-medium">{activity.user}</span>
                                                                        <span className="text-gray-500"> a {
                                                                            activity.action === 'add' ? 'ajouté' :
                                                                            activity.action === 'update' ? 'modifié' :
                                                                            activity.action === 'delete' ? 'supprimé' :
                                                                            activity.action === 'status' ? 'mis à jour le statut de' :
                                                                            activity.action === 'assign' ? 'assigné' :
                                                                            activity.action === 'generate' ? 'généré' :
                                                                            'effectué une action sur'
                                                                        }
                                                                        {activity.type === 'control' ? ' la mesure' :
                                                                         activity.type === 'event' ? ' l\'événement' :
                                                                         activity.type === 'participant' ? ' le participant' :
                                                                         activity.type === 'value' ? ' la valeur métier' :
                                                                         activity.type === 'treatment' ? ' le traitement' :
                                                                         activity.type === 'report' ? ' le rapport' : ''
                                                                        }
                                                                        :</span>
                                                                    </p>
                                                                    <span className="text-xs text-gray-400 flex-shrink-0 ml-2">{activity.time}</span>
                                                                </div>
                                                                <p className="text-xs text-gray-600 mt-0.5">{activity.item}</p>
                                                            </div>
                                                        </motion.div>
                                                    ))}
                                                </div>
                                                <div className="pt-3 pb-1 text-center border-t border-gray-100 mt-2">
                                                    <button className="text-sm text-blue-600 hover:text-blue-800 hover:underline focus:outline-none">
                                                        Voir toutes les activités
                                                    </button>
                                                </div>
                                            </div>
                                        </ChartContainer>
                                    </motion.div>
                                </div>
                            )}

                            {/* Participants Section */}
                            {activeSection === 'participants' && (
                                <motion.div variants={itemVariants}>
                                    {(context?.participants?.length > 0) ? (
                                        <InfoSection title={`Liste des participants (${context.participants.length})`} icon={Users}>
                                            <div className="p-4">
                                                <div className="mb-4 flex flex-wrap gap-3 justify-between items-center">
                                                    {/* Search and Filter */}
                                                    <div className="flex items-center gap-2 flex-grow sm:flex-grow-0">
                                                        <div className="relative flex-grow sm:flex-grow-0">
                                                            <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                                                                <svg className="w-4 h-4 text-gray-500" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 20 20">
                                                                    <path stroke="currentColor" strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="m19 19-4-4m0-7A7 7 0 1 1 1 8a7 7 0 0 1 14 0Z"/>
                                                                </svg>
                                                            </div>
                                                            <input type="search" className="block w-full p-2.5 pl-10 text-sm border border-gray-300 rounded-lg bg-gray-50 focus:ring-blue-500 focus:border-blue-500" placeholder="Rechercher..." />
                                                        </div>
                                                        <button className="p-2.5 text-gray-500 bg-gray-100 hover:bg-gray-200 rounded-lg border border-gray-200" aria-label="Filtrer">
                                                            <Filter size={18} />
                                                        </button>
                                                    </div>
                                                    {/* Add Participant Button */}
                                                    <button className="px-4 py-2 text-sm text-white bg-blue-600 rounded-lg hover:bg-blue-700 transition-colors flex items-center shadow-sm">
                                                        <Users size={16} className="mr-2" /> Ajouter un participant
                                                    </button>
                                                </div>

                                                {/* Participant Table */}
                                                <div className="overflow-x-auto border border-gray-200 rounded-lg">
                                                    <table className="min-w-full divide-y divide-gray-200">
                                                        <thead className="bg-gray-50">
                                                            <tr>
                                                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Nom</th>
                                                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Fonction</th>
                                                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Rôles RACI (Simplifié)</th>
                                                                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                                                            </tr>
                                                        </thead>
                                                        <tbody className="bg-white divide-y divide-gray-200">
                                                            {context.participants.map((p) => (
                                                                <motion.tr
                                                                    key={p.id}
                                                                    initial={{ opacity: 0 }}
                                                                    animate={{ opacity: 1 }}
                                                                    transition={{ duration: 0.3 }}
                                                                    whileHover={{ backgroundColor: '#F9FAFB' }} // gray-50
                                                                >
                                                                    <td className="px-6 py-4 whitespace-nowrap">
                                                                        <div className="flex items-center">
                                                                            <div className="flex-shrink-0 h-9 w-9 rounded-full bg-blue-100 flex items-center justify-center text-blue-800 font-medium text-sm">
                                                                                {p.name?.split(' ').map(n => n[0]).join('').toUpperCase() || '??'}
                                                                            </div>
                                                                            <div className="ml-3">
                                                                                <div className="text-sm font-medium text-gray-900">{p.name || 'Sans nom'}</div>
                                                                                <div className="text-xs text-gray-500">{p.email || ''}</div>
                                                                            </div>
                                                                        </div>
                                                                    </td>
                                                                    <td className="px-6 py-4 whitespace-nowrap">
                                                                        <div className="text-sm text-gray-600">
                                                                            {(p.position === "OTHER" && p.customPosition) ?
                                                                                p.customPosition :
                                                                                (p.position || 'Non défini')}
                                                                        </div>
                                                                    </td>
                                                                    <td className="px-6 py-4 whitespace-nowrap">
                                                                        {context.matrix && context.matrix[p.id] ? (
                                                                            <div className="flex space-x-1.5">
                                                                                 {/* Simplified display showing just the roles */}
                                                                                {Object.values(context.matrix[p.id]).filter(Boolean).map((r, index) => (
                                                                                    <span
                                                                                        key={`${p.id}-role-${index}`}
                                                                                        className="inline-flex items-center justify-center px-2 h-5 rounded-full text-xs font-bold w-5" // Fixed width/height
                                                                                        style={{
                                                                                            backgroundColor: r === 'R' ? '#A7F3D0' : r === 'A' ? '#BFDBFE' : r === 'C' ? '#FEF9C3' : r === 'I' ? '#FEE2E2' : '#F3F4F6', // Lighter shades
                                                                                            color: r === 'R' ? '#047857' : r === 'A' ? '#1D4ED8' : r === 'C' ? '#B45309' : r === 'I' ? '#B91C1C' : '#4B5563' // Darker text
                                                                                        }}
                                                                                        title={r === 'R' ? 'Responsible' : r === 'A' ? 'Accountable' : r === 'C' ? 'Consulted' : 'Informed'}
                                                                                    >
                                                                                        {r}
                                                                                    </span>
                                                                                ))}
                                                                            </div>
                                                                        ) : (<span className="text-xs text-gray-400">Aucun rôle</span>)}
                                                                    </td>
                                                                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm">
                                                                        <button className="text-blue-600 hover:text-blue-800 px-2 py-1 rounded hover:bg-blue-50 transition-colors text-xs font-medium">
                                                                            Modifier
                                                                        </button>
                                                                         <button className="text-gray-500 hover:text-gray-700 px-2 py-1 rounded hover:bg-gray-100 transition-colors ml-2 text-xs">
                                                                            Détails
                                                                        </button>
                                                                    </td>
                                                                </motion.tr>
                                                            ))}
                                                        </tbody>
                                                    </table>
                                                </div>
                                                 {/* Footer/Pagination Placeholder */}
                                                <div className="flex justify-between items-center mt-4 text-xs text-gray-500">
                                                    <span>Affichage de {context.participants.length} participants</span>
                                                    {/* Placeholder for pagination */}
                                                     <button className="px-3 py-1 text-xs text-blue-600 bg-blue-50 rounded-md hover:bg-blue-100 transition-colors flex items-center">
                                                        <ExternalLink size={14} className="mr-1" /> Voir la gestion complète
                                                    </button>
                                                </div>
                                            </div>
                                        </InfoSection>
                                    ) : (
                                        <InfoSection title="Liste des participants" icon={Users}>
                                            <div className="p-8 text-center">
                                                <Users size={48} className="mx-auto mb-3 text-gray-300" />
                                                <h3 className="text-gray-600 font-medium mb-1">Aucun participant trouvé</h3>
                                                <p className="text-gray-500 text-sm mb-4">Ajoutez des participants pour commencer votre analyse</p>
                                                <button className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                                                    Ajouter des participants
                                                </button>
                                            </div>
                                        </InfoSection>
                                    )}
                                </motion.div>
                            )}

                            {/* Dreaded Events Section */}
                            {activeSection === 'events' && (
                                <motion.div variants={itemVariants}>
                                    {(events?.length > 0) ? (
                                        <InfoSection title={`Événements redoutés (${events.length})`} icon={AlertTriangle}>
                                            <div className="p-4">
                                                <div className="mb-4 flex flex-wrap gap-3 justify-between items-center">
                                                     {/* Search and Filters */}
                                                    <div className="flex items-center gap-2 flex-wrap">
                                                        <div className="relative">
                                                            <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                                                                <svg className="w-4 h-4 text-gray-500" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 20 20"><path stroke="currentColor" strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="m19 19-4-4m0-7A7 7 0 1 1 1 8a7 7 0 0 1 14 0Z"/></svg>
                                                            </div>
                                                            <input type="search" className="block w-full p-2.5 pl-10 text-sm border border-gray-300 rounded-lg bg-gray-50 focus:ring-amber-500 focus:border-amber-500" placeholder="Rechercher événement..." />
                                                        </div>
                                                        <select defaultValue="" className="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-amber-500 focus:border-amber-500 block p-2.5">
                                                            <option value="">Tous les piliers</option>
                                                            {/* Dynamically generate options based on used pillars */}
                                                            {[...new Set(events.map(e => e.securityPillar).filter(Boolean))].map(p => <option key={p} value={p.toLowerCase()}>{p}</option>)}
                                                        </select>
                                                        <select defaultValue="" className="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-amber-500 focus:border-amber-500 block p-2.5">
                                                            <option value="">Toutes les sévérités</option>
                                                            {Object.keys(SEVERITY_COLORS).map(s => <option key={s} value={s}>{s.charAt(0).toUpperCase() + s.slice(1)}</option>)}
                                                        </select>
                                                    </div>
                                                    {/* Add Event Button */}
                                                    <button className="px-4 py-2 text-sm text-white bg-amber-600 rounded-lg hover:bg-amber-700 transition-colors flex items-center shadow-sm">
                                                        <AlertTriangle size={16} className="mr-2" /> Ajouter un événement
                                                    </button>
                                                </div>

                                                 {/* Events List */}
                                                <div className="space-y-4">
                                                    {events.slice(0, 6).map((event) => ( // Limit displayed events initially
                                                        <motion.div
                                                            key={event.id}
                                                            className="border rounded-xl p-5 hover:shadow-md transition-shadow duration-200 bg-white"
                                                            whileHover={{ y: -2, boxShadow: '0 4px 10px -3px rgba(0, 0, 0, 0.07)' }}
                                                            initial={{ opacity: 0, y: 10 }}
                                                            animate={{ opacity: 1, y: 0 }}
                                                            transition={{ duration: 0.3 }}
                                                        >
                                                            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center">
                                                                <h4 className="font-semibold text-gray-800 flex items-center mb-2 sm:mb-0">
                                                                    <AlertTriangle size={18} className="mr-2 text-amber-500 flex-shrink-0" />
                                                                    {event.name}
                                                                </h4>
                                                                <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-bold border ${ // Bolder style
                                                                    {
                                                                        minor: 'bg-emerald-100 text-emerald-800 border-emerald-200',
                                                                        moderate: 'bg-yellow-100 text-yellow-800 border-yellow-200', // Switched to Yellow
                                                                        major: 'bg-orange-100 text-orange-800 border-orange-200',
                                                                        critical: 'bg-red-100 text-red-800 border-red-200',
                                                                        catastrophic: 'bg-slate-800 text-slate-100 border-slate-600' // Darker bg
                                                                    }[event.severity] || 'bg-gray-100 text-gray-800 border-gray-200'
                                                                }`}>
                                                                    {event.severity?.charAt(0).toUpperCase() + event.severity?.slice(1) || 'Non défini'}
                                                                </span>
                                                            </div>
                                                            <p className="text-sm text-gray-600 mt-3 bg-gray-50 p-3 rounded-lg border border-gray-100">
                                                                {event.description}
                                                            </p>
                                                            <div className="mt-4 flex flex-wrap gap-x-3 gap-y-2 items-center">
                                                                {event.securityPillar && (
                                                                    <span className="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                                                                        <Shield size={12} className="mr-1.5" />
                                                                        {event.securityPillar}
                                                                    </span>
                                                                )}
                                                                {event.businessValue && (
                                                                    <span className="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                                                        <Target size={12} className="mr-1.5" />
                                                                        {/* Find the name from mockBusinessValues */}
                                                                        {bValues.find(bv => bv.id === event.businessValue)?.name || event.businessValue}
                                                                    </span>
                                                                )}
                                                                <div className="flex-grow"></div> {/* Pushes buttons to the right */}
                                                                <div className="flex gap-2">
                                                                    <button className="text-xs text-blue-600 hover:text-blue-800 hover:underline focus:outline-none">Modifier</button>
                                                                    <button className="text-xs text-gray-500 hover:text-gray-700 hover:underline focus:outline-none">Détails</button>
                                                                </div>
                                                            </div>
                                                        </motion.div>
                                                    ))}
                                                </div>

                                                {/* Show More Button */}
                                                {events.length > 6 && (
                                                    <div className="mt-6 flex justify-center">
                                                        <button className="px-4 py-2 bg-white border border-gray-300 rounded-lg shadow-sm text-sm text-gray-700 hover:bg-gray-50 flex items-center focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-amber-400">
                                                            <ChevronDown size={16} className="mr-1" /> Voir {events.length - 6} événement(s) de plus
                                                        </button>
                                                    </div>
                                                )}
                                            </div>
                                        </InfoSection>
                                    ) : (
                                        <InfoSection title="Événements redoutés" icon={AlertTriangle}>
                                            <div className="p-8 text-center">
                                                <AlertTriangle size={48} className="mx-auto mb-3 text-gray-300" />
                                                <h3 className="text-gray-600 font-medium mb-1">Aucun événement redouté défini</h3>
                                                <p className="text-gray-500 text-sm mb-4">
                                                    Identifiez les événements redoutés pour continuer l'analyse des risques.
                                                </p>
                                                <button className="px-4 py-2 bg-amber-600 text-white rounded-lg hover:bg-amber-700 transition-colors">
                                                    Définir des événements
                                                </button>
                                            </div>
                                        </InfoSection>
                                    )}
                                </motion.div>
                            )}

                            {/* Security Measures Section */}
                            {activeSection === 'security' && (
                                <motion.div variants={itemVariants}>
                                    {(controlsData?.length > 0) ? (
                                        <InfoSection title={`Mesures de sécurité (${controlsData.length})`} icon={Shield}>
                                            <div className="p-4">
                                                <div className="mb-5 flex flex-col md:flex-row flex-wrap md:items-center gap-4 justify-between">
                                                    {/* Filters */}
                                                    <div className="flex items-center gap-2 flex-wrap">
                                                        {/* Search */}
                                                        <div className="relative">
                                                            <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                                                                <svg className="w-4 h-4 text-gray-500" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 20 20"><path stroke="currentColor" strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="m19 19-4-4m0-7A7 7 0 1 1 1 8a7 7 0 0 1 14 0Z"/></svg>
                                                            </div>
                                                            <input type="search" className="block w-full p-2.5 pl-10 text-sm border border-gray-300 rounded-lg bg-gray-50 focus:ring-purple-500 focus:border-purple-500" placeholder="Rechercher mesure..." />
                                                        </div>
                                                        {/* Status Filter Buttons */}
                                                        <div className="flex rounded-lg border border-gray-200 overflow-hidden shadow-sm">
                                                            {[
                                                                { value: 'all', label: 'Tous', icon: null },
                                                                { value: 'implemented', label: 'En place', icon: CheckCircle },
                                                                { value: 'partial', label: 'Partiel', icon: Clock },
                                                                { value: 'planned', label: 'Planifié', icon: Calendar }
                                                            ].map((filter, index, arr) => (
                                                                <motion.button
                                                                    key={filter.value}
                                                                    variants={buttonGroupVariants}
                                                                    animate={securityFilter === filter.value ? 'active' : 'inactive'}
                                                                    whileHover={{ backgroundColor: securityFilter === filter.value ? '#4338CA' : '#F3F4F6' }}
                                                                    onClick={() => setSecurityFilter(filter.value)}
                                                                    className={`px-3 py-2 text-xs font-medium flex items-center focus:outline-none focus:z-10 focus:ring-2 focus:ring-indigo-400 ${index < arr.length - 1 ? 'border-r border-gray-200' : ''}`}
                                                                >
                                                                    {filter.icon && React.createElement(filter.icon, { size: 14, className: "mr-1.5" })}
                                                                    {filter.label}
                                                                </motion.button>
                                                            ))}
                                                        </div>
                                                    </div>

                                                    {/* View Toggle and Add Button */}
                                                    <div className="flex items-center gap-3">
                                                        {/* View Toggle */}
                                                        <div className="flex rounded-lg border border-gray-200 overflow-hidden shadow-sm">
                                                            <motion.button
                                                                variants={buttonGroupVariants}
                                                                animate={securityView === 'grid' ? 'active' : 'inactive'}
                                                                whileHover={{ backgroundColor: securityView === 'grid' ? '#4338CA' : '#F3F4F6' }}
                                                                onClick={() => setSecurityView('grid')}
                                                                className="px-3 py-2 text-xs font-medium border-r border-gray-200 focus:outline-none focus:z-10 focus:ring-2 focus:ring-indigo-400"
                                                                aria-label="Vue Grille"
                                                            >
                                                                <svg xmlns="http://www.w3.org/2000/svg" className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zm10 0a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zm10 0a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z" /></svg>
                                                            </motion.button>
                                                            <motion.button
                                                                variants={buttonGroupVariants}
                                                                animate={securityView === 'list' ? 'active' : 'inactive'}
                                                                whileHover={{ backgroundColor: securityView === 'list' ? '#4338CA' : '#F3F4F6' }}
                                                                onClick={() => setSecurityView('list')}
                                                                className="px-3 py-2 text-xs font-medium focus:outline-none focus:z-10 focus:ring-2 focus:ring-indigo-400"
                                                                aria-label="Vue Liste"
                                                            >
                                                                <svg xmlns="http://www.w3.org/2000/svg" className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" /></svg>
                                                            </motion.button>
                                                        </div>
                                                        {/* Add Control Button */}
                                                        <button className="px-4 py-2 text-sm text-white bg-purple-600 rounded-lg hover:bg-purple-700 transition-colors flex items-center shadow-sm">
                                                            <Shield size={16} className="mr-2" /> Ajouter une mesure
                                                        </button>
                                                    </div>
                                                </div>

                                                {/* Filter Info Banner */}
                                                <div className="bg-blue-50 p-3 rounded-lg mb-4 flex items-center justify-between border border-blue-100">
                                                    <p className="text-blue-700 text-sm font-medium flex items-center">
                                                        <Info size={16} className="mr-2 flex-shrink-0"/>
                                                        {filteredControls.length} mesure(s) {securityFilter !== 'all' ? `avec statut "${securityFilter}" ` : ''} affichée(s) sur {controlsData.length} au total.
                                                    </p>
                                                    {securityFilter !== 'all' && (
                                                        <button onClick={() => setSecurityFilter('all')} className="text-xs text-blue-600 hover:text-blue-800 hover:underline">
                                                            Voir tout
                                                        </button>
                                                    )}
                                                </div>

                                                {/* Grid View */}
                                                {securityView === 'grid' ? (
                                                    <motion.div
                                                        layout // Animate layout changes
                                                        className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                                                        {filteredControls.map(control => (
                                                            <motion.div
                                                                layout
                                                                key={control.id}
                                                                initial={{ opacity: 0, scale: 0.95 }}
                                                                animate={{ opacity: 1, scale: 1 }}
                                                                exit={{ opacity: 0, scale: 0.95 }}
                                                                transition={{ duration: 0.2 }}
                                                                className="border rounded-lg p-4 bg-white hover:shadow-md transition-all flex flex-col justify-between min-h-[160px]" // Fixed min height
                                                                whileHover={{ y: -2, boxShadow: '0 4px 10px -3px rgba(0, 0, 0, 0.07)' }}
                                                            >
                                                                <div>
                                                                    <div className="flex justify-between items-start mb-2">
                                                                        <h4 className="font-semibold text-gray-800 text-sm leading-tight">{control.name}</h4>
                                                                        <span className={`px-2.5 py-0.5 rounded-full text-xs font-medium whitespace-nowrap ${
                                                                            control.status === 'implemented' ? 'bg-green-100 text-green-800' :
                                                                            control.status === 'partial' ? 'bg-yellow-100 text-yellow-800' : // Switched to Yellow
                                                                            control.status === 'planned' ? 'bg-blue-100 text-blue-800' :
                                                                            'bg-gray-100 text-gray-800'
                                                                        }`}>
                                                                            {control.status === 'implemented' ? 'En place' :
                                                                            control.status === 'partial' ? 'Partiel' :
                                                                            control.status === 'planned' ? 'Planifié' :
                                                                            control.status || 'N/D'}
                                                                        </span>
                                                                    </div>
                                                                    <p className="text-xs text-gray-500 line-clamp-3"> {/* Limit description lines */}
                                                                        {control.description || 'Aucune description disponible.'}
                                                                    </p>
                                                                </div>
                                                                <div className="flex justify-between items-center mt-3 pt-3 border-t border-gray-100">
                                                                    <div className="flex items-center gap-1.5">
                                                                        <span className="bg-purple-100 text-purple-800 text-xs px-2 py-0.5 rounded-full">
                                                                            {control.type || 'N/D'}
                                                                        </span>
                                                                        <span className={`text-xs px-2 py-0.5 rounded-full ${
                                                                            control.priority === 'Haute' ? 'bg-red-100 text-red-800' :
                                                                            control.priority === 'Moyenne' ? 'bg-orange-100 text-orange-800' :
                                                                            'bg-gray-100 text-gray-800'
                                                                        }`}>
                                                                            {control.priority || 'N/D'}
                                                                        </span>
                                                                    </div>
                                                                    <button className="text-xs text-blue-600 hover:text-blue-800 hover:underline focus:outline-none">Détails</button>
                                                                </div>
                                                            </motion.div>
                                                        ))}
                                                    </motion.div>
                                                ) : (
                                                /* List View */
                                                    <motion.div layout className="overflow-x-auto rounded-lg border border-gray-200">
                                                        <table className="min-w-full divide-y divide-gray-200">
                                                            <thead className="bg-gray-50">
                                                                <tr>
                                                                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Mesure</th>
                                                                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Statut</th>
                                                                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                                                                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Priorité</th>
                                                                    <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                                                                </tr>
                                                            </thead>
                                                            <tbody className="bg-white divide-y divide-gray-200">
                                                                <AnimatePresence>
                                                                    {filteredControls.map(control => (
                                                                        <motion.tr
                                                                            layout
                                                                            key={control.id}
                                                                            initial={{ opacity: 0 }}
                                                                            animate={{ opacity: 1 }}
                                                                            exit={{ opacity: 0 }}
                                                                            transition={{ duration: 0.2 }}
                                                                            className="hover:bg-gray-50"
                                                                        >
                                                                            <td className="px-6 py-4 whitespace-nowrap">
                                                                                <div className="text-sm font-medium text-gray-900">{control.name}</div>
                                                                                <div className="text-xs text-gray-500 line-clamp-1">{control.description}</div>
                                                                            </td>
                                                                            <td className="px-6 py-4 whitespace-nowrap">
                                                                                <span className={`px-2.5 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${
                                                                                    control.status === 'implemented' ? 'bg-green-100 text-green-800' :
                                                                                    control.status === 'partial' ? 'bg-yellow-100 text-yellow-800' : // Switched to Yellow
                                                                                    control.status === 'planned' ? 'bg-blue-100 text-blue-800' :
                                                                                    'bg-gray-100 text-gray-800'
                                                                                }`}>
                                                                                    {control.status === 'implemented' ? 'En place' :
                                                                                    control.status === 'partial' ? 'Partiel' :
                                                                                    control.status === 'planned' ? 'Planifié' :
                                                                                    control.status || 'N/D'}
                                                                                </span>
                                                                            </td>
                                                                            <td className="px-6 py-4 whitespace-nowrap">
                                                                                <div className="text-sm text-gray-500">{control.type || 'N/D'}</div>
                                                                            </td>
                                                                            <td className="px-6 py-4 whitespace-nowrap">
                                                                                 <span className={`text-xs font-medium px-2 py-0.5 rounded ${
                                                                                    control.priority === 'Haute' ? 'text-red-800 bg-red-100' :
                                                                                    control.priority === 'Moyenne' ? 'text-orange-800 bg-orange-100' :
                                                                                    'text-gray-800 bg-gray-100'
                                                                                }`}>
                                                                                    {control.priority || 'N/D'}
                                                                                </span>
                                                                            </td>
                                                                            <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                                                                <button className="text-blue-600 hover:text-blue-900 mr-3 text-xs">Modifier</button>
                                                                                <button className="text-gray-600 hover:text-gray-900 text-xs">Détails</button>
                                                                            </td>
                                                                        </motion.tr>
                                                                    ))}
                                                                </AnimatePresence>
                                                            </tbody>
                                                        </table>
                                                    </motion.div>
                                                )}

                                                 {/* Pagination/Load More Placeholder */}
                                                {filteredControls.length > 10 && securityView === 'grid' && ( // Example threshold
                                                    <div className="flex justify-center mt-6">
                                                        <button className="px-4 py-2 text-sm text-blue-600 bg-blue-50 hover:bg-blue-100 rounded-lg transition-colors flex items-center focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-400">
                                                            <ChevronDown size={16} className="mr-1" />
                                                            Afficher plus de mesures
                                                        </button>
                                                    </div>
                                                )}
                                            </div>
                                        </InfoSection>
                                    ) : (
                                        <InfoSection title="Mesures de sécurité" icon={Shield}>
                                            <div className="p-8 text-center">
                                                <Shield size={48} className="mx-auto mb-3 text-gray-300" />
                                                <h3 className="text-gray-600 font-medium mb-1">Aucune mesure de sécurité définie</h3>
                                                <p className="text-gray-500 text-sm mb-4">
                                                    Définissez des mesures de sécurité pour vous protéger contre les événements redoutés.
                                                </p>
                                                <button className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors">
                                                    Ajouter des mesures
                                                </button>
                                            </div>
                                        </InfoSection>
                                    )}
                                </motion.div>
                            )}

                             {/* Analysis & Actions Section - Populated with Mock Examples */}
                            {activeSection === 'analysis' && (
                                <motion.div variants={itemVariants} className="space-y-8">
                                    {/* Top Summary Cards */}
                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                        <InfoCard
                                            title="Score de risque global (Est.)"
                                            value="62.4" // Mock value
                                            icon={TrendingUp} // Changed icon
                                            trend={-8} // Mock trend
                                            color="red" // Represents risk level
                                            message="Calculé basé sur la sévérité et la probabilité estimée des événements."
                                        />
                                        <InfoCard
                                            title="Niveau de conformité (Est.)"
                                            value="71%" // Mock value
                                            icon={Award}
                                            trend={12} // Mock trend
                                            color="green" // Represents compliance
                                            message="Basé sur l'implémentation des contrôles par rapport au référentiel choisi (ex: ISO 27001)."
                                        />
                                    </div>

                                    {/* Risk Evolution Chart Placeholder */}
                                    <ChartContainer title="Évolution du niveau de risque (Exemple)" icon={LineChart}>
                                        <div className="p-4">
                                            <div className="h-[300px] flex flex-col items-center justify-center bg-gray-50 rounded-xl border border-gray-200">
                                                <LineChart size={48} className="text-indigo-300 mb-3" />
                                                <p className="text-gray-600 font-medium text-center">Graphique d'évolution du risque (Bientôt disponible)</p>
                                                <p className="text-xs text-gray-400 max-w-sm mt-1 mb-4 text-center">
                                                    Visualisez comment le niveau de risque change au fil du temps avec l'implémentation des mesures. Nécessite plusieurs snapshots de l'analyse.
                                                </p>
                                                <button className="px-4 py-2 bg-indigo-600 text-white rounded-lg text-sm shadow-sm hover:bg-indigo-700 transition-colors">
                                                    Comparer avec une analyse précédente
                                                </button>
                                            </div>
                                        </div>
                                    </ChartContainer>

                                    {/* Analysis Widgets */}
                                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                                        {/* Control Impact */}
                                        <ChartContainer title="Impact estimé des mesures" icon={Zap}>
                                            <div className="p-4 text-center flex flex-col items-center justify-center h-[280px]">
                                                <div className="relative inline-flex mb-4">
                                                    <motion.div
                                                        initial={{ pathLength: 0, opacity: 0 }}
                                                        animate={{ pathLength: 0.42, opacity: 1 }} // Mock 42%
                                                        transition={{ duration: 1, delay: 0.5, ease: "easeOut" }}
                                                        className="absolute inset-0"
                                                    >
                                                        <svg className="w-40 h-40" viewBox="0 0 100 100">
                                                            <circle cx="50" cy="50" r="40" fill="none" stroke="#e5e7eb" strokeWidth="10" />
                                                            <motion.circle
                                                                cx="50" cy="50" r="40"
                                                                fill="none"
                                                                stroke="#10B981" // Emerald 500
                                                                strokeWidth="10"
                                                                strokeLinecap="round"
                                                                strokeDasharray={2 * Math.PI * 40}
                                                                transform="rotate(-90 50 50)"
                                                            />
                                                        </svg>
                                                    </motion.div>
                                                    <div className="w-40 h-40 rounded-full bg-emerald-50 flex items-center justify-center">
                                                        <div className="text-center">
                                                            <div className="text-3xl font-bold text-emerald-700">42%</div>
                                                            <div className="text-xs text-emerald-600">Réduction Risque</div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <p className="text-sm text-gray-600 mt-2 px-4">Réduction globale du risque estimée grâce aux mesures <span className="font-semibold">en place</span>.</p>
                                            </div>
                                        </ChartContainer>

                                        {/* Risk Distribution */}
                                        <ChartContainer title="Répartition des risques résiduels" icon={PieChartLucide}>
                                            <div className="p-4 text-center flex flex-col items-center justify-center h-[280px]">
                                                <div className="grid grid-cols-3 gap-3 w-full max-w-xs mb-4">
                                                     {/* Mock counts based on severities */}
                                                    <div className="bg-red-100 rounded-lg p-3 text-center border border-red-200">
                                                        <div className="text-2xl font-bold text-red-700">{events?.filter(e => e.severity === 'critical' || e.severity === 'catastrophic').length}</div>
                                                        <div className="text-xs text-red-600 mt-1">Critique / Cat.</div>
                                                    </div>
                                                    <div className="bg-orange-100 rounded-lg p-3 text-center border border-orange-200">
                                                        <div className="text-2xl font-bold text-orange-700">{events?.filter(e => e.severity === 'major').length}</div>
                                                        <div className="text-xs text-orange-600 mt-1">Majeur</div>
                                                    </div>
                                                     <div className="bg-yellow-100 rounded-lg p-3 text-center border border-yellow-200">
                                                        <div className="text-2xl font-bold text-yellow-700">{events?.filter(e => e.severity === 'moderate' || e.severity === 'minor').length}</div>
                                                        <div className="text-xs text-yellow-600 mt-1">Modéré / Mineur</div>
                                                    </div>
                                                </div>
                                                <p className="text-sm text-gray-600 mt-2 px-4">Nombre d'événements redoutés par niveau de sévérité après prise en compte des mesures (estimation).</p>
                                            </div>
                                        </ChartContainer>

                                        {/* Required Actions */}
                                        <ChartContainer title="Actions prioritaires requises" icon={CheckCircle}>
                                             <div className="p-3">
                                                <div className="space-y-3 h-[250px] overflow-y-auto custom-scrollbar pr-1">
                                                    {/* Mock actions based on controls not implemented */}
                                                    {controlsData?.filter(c => c.status !== 'implemented' && c.priority === 'Haute').slice(0,3).map(control => (
                                                        <div key={`action-${control.id}`} className="bg-red-50 p-3 rounded-lg border-l-4 border-red-500 shadow-sm">
                                                            <div className="flex items-center">
                                                                <div className="font-medium text-red-800 text-sm flex-grow">{control.name} ({control.status === 'planned' ? 'Planifié' : 'Partiel'})</div>
                                                                <span className="ml-2 text-xs text-red-700 bg-red-100 px-2 py-0.5 rounded-full font-semibold">Haute</span>
                                                            </div>
                                                             <p className="text-xs text-red-700 mt-1">Priorité: {control.priority}</p>
                                                        </div>
                                                    ))}
                                                    {controlsData?.filter(c => c.status !== 'implemented' && c.priority === 'Moyenne').slice(0,2).map(control => (
                                                        <div key={`action-${control.id}`} className="bg-orange-50 p-3 rounded-lg border-l-4 border-orange-500 shadow-sm">
                                                            <div className="flex items-center">
                                                                <div className="font-medium text-orange-800 text-sm flex-grow">{control.name} ({control.status === 'planned' ? 'Planifié' : 'Partiel'})</div>
                                                                <span className="ml-2 text-xs text-orange-700 bg-orange-100 px-2 py-0.5 rounded-full font-semibold">Moyenne</span>
                                                            </div>
                                                            <p className="text-xs text-orange-700 mt-1">Priorité: {control.priority}</p>
                                                        </div>
                                                    ))}
                                                     {/* Add a placeholder if few actions */}
                                                    {controlsData?.filter(c => c.status !== 'implemented').length < 3 && (
                                                        <div className="bg-green-50 p-3 rounded-lg border-l-4 border-green-500 text-center">
                                                            <div className="font-medium text-green-800 text-sm">Bon travail ! Peu d'actions prioritaires restantes.</div>
                                                        </div>
                                                    )}
                                                </div>
                                            </div>
                                        </ChartContainer>
                                    </div>

                                    {/* Recommendations Section */}
                                    <InfoSection title="Recommandations d'amélioration (Exemple)" icon={HelpCircle} collapsible={true}>
                                        <div className="p-4 space-y-4">
                                            {/* Recommendation 1 */}
                                            <div className="bg-white rounded-lg border border-gray-200 p-4 shadow-sm hover:shadow-md transition-shadow">
                                                <div className="flex items-start">
                                                    <div className="bg-red-100 p-2.5 rounded-full mr-4 mt-1">
                                                        <AlertTriangle size={18} className="text-red-600" />
                                                    </div>
                                                    <div className="flex-grow">
                                                        <h4 className="font-semibold text-gray-800 mb-1">Risque élevé: Ransomware sur serveurs critiques</h4>
                                                        <p className="text-sm text-gray-600 mb-3">
                                                            L'événement #DE2 (Ransomware) a une sévérité 'Catastrophic'. Les contrôles associés (PCA, Sauvegardes) ne sont pas tous 'implemented'.
                                                        </p>
                                                        <div className="bg-gray-50 p-3 rounded-lg text-sm border border-gray-100">
                                                            <h5 className="font-medium text-gray-700 mb-1.5">Recommandations Urgentes:</h5>
                                                            <ul className="list-disc list-inside text-gray-600 space-y-1 text-xs">
                                                                <li>Finaliser et tester le PCA (Contrôle #SC4 - Statut: {controlsData.find(c=>c.id==='sc4')?.status || 'N/A'}).</li>
                                                                <li>Vérifier la fréquence et la testabilité des sauvegardes (Contrôle #SC8 - Statut: {controlsData.find(c=>c.id==='sc8')?.status || 'N/A'}).</li>
                                                                <li>Renforcer la segmentation réseau pour limiter la propagation.</li>
                                                                <li>Mettre en place une solution EDR (Endpoint Detection and Response).</li>
                                                            </ul>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            {/* Recommendation 2 */}
                                            <div className="bg-white rounded-lg border border-gray-200 p-4 shadow-sm hover:shadow-md transition-shadow">
                                                <div className="flex items-start">
                                                    <div className="bg-orange-100 p-2.5 rounded-full mr-4 mt-1">
                                                        <Shield size={18} className="text-orange-600" />
                                                    </div>
                                                    <div className="flex-grow">
                                                        <h4 className="font-semibold text-gray-800 mb-1">Risque moyen: Gestion des correctifs partielle</h4>
                                                        <p className="text-sm text-gray-600 mb-3">
                                                            Le contrôle #SC7 (Patch Management) est 'partial', augmentant la surface d'attaque pour divers événements.
                                                        </p>
                                                        <div className="bg-gray-50 p-3 rounded-lg text-sm border border-gray-100">
                                                            <h5 className="font-medium text-gray-700 mb-1.5">Recommandations:</h5>
                                                            <ul className="list-disc list-inside text-gray-600 space-y-1 text-xs">
                                                                <li>Automatiser le déploiement des correctifs critiques et de sécurité.</li>
                                                                <li>Établir une politique claire de gestion des vulnérabilités (SLA).</li>
                                                                <li>Intégrer les résultats des scans de vulnérabilités (Contrôle #SC3) au processus de patch.</li>
                                                            </ul>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                             {/* Recommendation 3 */}
                                            <div className="bg-white rounded-lg border border-gray-200 p-4 shadow-sm hover:shadow-md transition-shadow">
                                                <div className="flex items-start">
                                                    <div className="bg-blue-100 p-2.5 rounded-full mr-4 mt-1">
                                                        <Eye size={18} className="text-blue-600" />
                                                    </div>
                                                    <div className="flex-grow">
                                                        <h4 className="font-semibold text-gray-800 mb-1">Amélioration: Visibilité et détection</h4>
                                                        <p className="text-sm text-gray-600 mb-3">
                                                            Le contrôle #SC10 (SIEM) est 'planned'. Son implémentation améliorera significativement la détection précoce.
                                                        </p>
                                                        <div className="bg-gray-50 p-3 rounded-lg text-sm border border-gray-100">
                                                            <h5 className="font-medium text-gray-700 mb-1.5">Recommandations:</h5>
                                                            <ul className="list-disc list-inside text-gray-600 space-y-1 text-xs">
                                                                <li>Accélérer le projet d'implémentation du SIEM.</li>
                                                                <li>Définir des cas d'usage clairs et des alertes pertinentes.</li>
                                                                <li>Former l'équipe à l'utilisation de l'outil et à la réponse aux incidents.</li>
                                                            </ul>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div className="flex justify-end p-4 mt-2">
                                            <button className="px-4 py-2 bg-indigo-600 text-white rounded-lg text-sm flex items-center shadow-sm hover:bg-indigo-700 transition-colors">
                                                <Download size={16} className="mr-2" />
                                                Télécharger le rapport d'analyse détaillé
                                            </button>
                                        </div>
                                    </InfoSection>
                                </motion.div>
                            )}
                        </motion.div> {/* End of section content wrapper */}

                         {/* Enhanced Footer Information */}
                        <motion.div
                            initial={{ opacity: 0 }}
                            animate={{ opacity: 1 }}
                            transition={{ delay: 0.4 }} // Ensure footer fades in last
                            className="mt-10 text-xs text-gray-500 flex flex-col sm:flex-row items-center gap-4 border-t border-gray-200 pt-5"
                        >
                            <div className="flex items-center gap-x-4 gap-y-2 flex-wrap">
                                <span className="flex items-center bg-gray-100 px-3 py-1.5 rounded-full border border-gray-200">
                                    <Users size={14} className="mr-1.5 text-blue-500" />
                                    <span className="font-medium text-gray-600">Organisation:</span>
                                    <span className="ml-1.5">{context?.organizationName || currentAnalysis?.companyName || 'N/A'}</span>
                                </span>

                                <span className="flex items-center bg-gray-100 px-3 py-1.5 rounded-full border border-gray-200">
                                    <Calendar size={14} className="mr-1.5 text-green-500" />
                                    <span className="font-medium text-gray-600">Date Analyse:</span>
                                    <span className="ml-1.5">{context?.analysisDate || 'N/A'}</span>
                                </span>

                                <span className="flex items-center bg-gray-100 px-3 py-1.5 rounded-full border border-gray-200">
                                    <RefreshCw size={14} className="mr-1.5 text-purple-500" />
                                    <span className="font-medium text-gray-600">Dernière Modif.:</span>
                                    <span className="ml-1.5">{currentAnalysis?.updatedAt ? new Date(currentAnalysis.updatedAt).toLocaleString('fr-FR', { day: '2-digit', month: '2-digit', year: 'numeric', hour: '2-digit', minute: '2-digit' }) : 'N/A'}</span>
                                </span>
                            </div>
                            <div className="flex-grow"></div> {/* Pushes button to right on larger screens */}
                            <button className="text-blue-600 hover:text-blue-800 hover:underline text-xs flex items-center mt-2 sm:mt-0">
                                <Info size={14} className="mr-1.5" />
                                Afficher les détails de cette analyse
                            </button>
                        </motion.div>

                    </motion.div> {/* End of AnimatePresence content */}
                </AnimatePresence>
            )}

            {/* Enhanced Message when no analysis is selected (and no mock data) */}
            {!isWorkshopDataLoading && !currentAnalysis && !MOCK_DATA_ENABLED && (
                <motion.div
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="text-center py-16 bg-gradient-to-b from-blue-50 to-gray-50 rounded-xl border border-blue-100 mt-6 px-4"
                >
                    <motion.div
                        initial={{ scale: 0.9, opacity: 0 }}
                        animate={{ scale: 1, opacity: 1 }}
                        transition={{ delay: 0.2, type: "spring", stiffness: 100 }}
                        className="inline-block mb-6" // Removed white bg for better integration
                    >
                        {/* Use a more relevant icon */}
                        <svg className="w-16 h-16 mx-auto text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth="1.5" d="M4 7v10m16-10v10M4 17h16M4 7h16M9 4h6M9 20h6m-6-7h6m-3-3v3"></path></svg>
                    </motion.div>
                    <h3 className="text-xl text-gray-700 font-medium mb-2">Bienvenue sur le Tableau de Bord</h3>
                    <p className="text-gray-500 max-w-md mx-auto mb-6">
                        Pour commencer, veuillez sélectionner une analyse existante dans le menu déroulant ci-dessus ou créez une nouvelle analyse de risques.
                    </p>
                    <div className="flex flex-col sm:flex-row gap-3 justify-center">
                        <motion.button
                            whileHover={{ scale: 1.03, y: -2 }}
                            whileTap={{ scale: 0.97 }}
                            className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors shadow-md flex items-center justify-center focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                        >
                            <XCircle size={18} className="mr-2"/> {/* Assuming PlusCircle is imported */}
                            Créer une nouvelle analyse
                        </motion.button>
                        <motion.button
                            whileHover={{ scale: 1.03, y: -2 }}
                            whileTap={{ scale: 0.97 }}
                            className="px-6 py-3 bg-white text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors shadow-sm flex items-center justify-center focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-400"
                        >
                            <Eye size={18} className="mr-2"/>
                            Charger un exemple
                        </motion.button>
                    </div>
                </motion.div>
            )}
        </motion.div>
    );
};

// Custom hook for dashboard analytics (Keep as is)
const useDashboardAnalytics = () => {
    const [isInitialized, setIsInitialized] = useState(false);

    useEffect(() => {
        // Mock analytics initialization
        if (!isInitialized) {
            console.log("Dashboard analytics initialized");
            setIsInitialized(true);
        }

        // Cleanup
        return () => {
            console.log("Dashboard analytics cleanup");
        };
    }, [isInitialized]);

    return {
        trackEvent: (eventName, data) => {
            // Mock analytics tracking
            console.log(`Analytics event: ${eventName}`, data);
        }
    };
};

export default Dashboard;

// --- END OF FILE Dashboard.js ---
