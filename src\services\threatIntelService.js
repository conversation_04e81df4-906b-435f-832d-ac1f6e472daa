// src/services/threatIntelService.js
// Advanced Threat Intelligence Service with multiple data sources

class ThreatIntelService {
  constructor() {
    // Multiple threat intelligence sources
    this.sources = {
      mitre: {
        stix: 'https://raw.githubusercontent.com/mitre/cti/master/enterprise-attack/enterprise-attack.json',
        groups: 'https://raw.githubusercontent.com/mitre/cti/master/enterprise-attack/intrusion-sets',
        malware: 'https://raw.githubusercontent.com/mitre/cti/master/enterprise-attack/malware'
      },
      cisa: {
        kev: 'https://www.cisa.gov/sites/default/files/feeds/known_exploited_vulnerabilities.json'
      },
      nist: {
        nvd: 'https://services.nvd.nist.gov/rest/json/cves/2.0'
      },
      openCTI: {
        // Add your OpenCTI instance URL if available
        baseUrl: process.env.OPENCTI_URL || null
      }
    };

    // Cache settings
    this.cache = {
      mitreTechniques: { data: null, lastUpdated: null, expiry: 24 * 60 * 60 * 1000 }, // 24h
      cisaKEV: { data: null, lastUpdated: null, expiry: 6 * 60 * 60 * 1000 }, // 6h
      threatGroups: { data: null, lastUpdated: null, expiry: 24 * 60 * 60 * 1000 } // 24h
    };
  }

  // Fetch CISA Known Exploited Vulnerabilities
  async fetchCISAKnownExploitedVulns() {
    try {
      console.log('[ThreatIntel] Fetching CISA Known Exploited Vulnerabilities...');
      
      // Check cache
      const now = Date.now();
      if (this.cache.cisaKEV.data && 
          (now - this.cache.cisaKEV.lastUpdated) < this.cache.cisaKEV.expiry) {
        console.log('[ThreatIntel] Using cached CISA KEV data');
        return this.cache.cisaKEV.data;
      }

      const response = await fetch(this.sources.cisa.kev, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'User-Agent': 'EBIOS-RM-ThreatIntel/1.0'
        }
      });

      if (!response.ok) {
        throw new Error(`CISA KEV API error: ${response.status}`);
      }

      const kevData = await response.json();
      
      // Process KEV data
      const processedKEV = kevData.vulnerabilities?.map(vuln => ({
        cveId: vuln.cveID,
        vendorProject: vuln.vendorProject,
        product: vuln.product,
        vulnerabilityName: vuln.vulnerabilityName,
        dateAdded: vuln.dateAdded,
        shortDescription: vuln.shortDescription,
        requiredAction: vuln.requiredAction,
        dueDate: vuln.dueDate,
        knownRansomwareCampaignUse: vuln.knownRansomwareCampaignUse,
        priority: 'HIGH', // CISA KEV are high priority by definition
        source: 'CISA KEV'
      })) || [];

      // Update cache
      this.cache.cisaKEV.data = processedKEV;
      this.cache.cisaKEV.lastUpdated = now;

      console.log(`[ThreatIntel] Fetched ${processedKEV.length} CISA KEV entries`);
      return processedKEV;

    } catch (error) {
      console.error('[ThreatIntel] Error fetching CISA KEV:', error);
      return [];
    }
  }

  // Fetch threat actor groups from MITRE
  async fetchThreatGroups() {
    try {
      console.log('[ThreatIntel] Fetching MITRE threat groups...');
      
      // Check cache
      const now = Date.now();
      if (this.cache.threatGroups.data && 
          (now - this.cache.threatGroups.lastUpdated) < this.cache.threatGroups.expiry) {
        console.log('[ThreatIntel] Using cached threat groups data');
        return this.cache.threatGroups.data;
      }

      const response = await fetch(this.sources.mitre.stix, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'User-Agent': 'EBIOS-RM-ThreatIntel/1.0'
        }
      });

      if (!response.ok) {
        throw new Error(`MITRE groups API error: ${response.status}`);
      }

      const stixData = await response.json();
      
      // Extract threat groups (intrusion-sets)
      const threatGroups = stixData.objects?.filter(obj => 
        obj.type === 'intrusion-set' && !obj.revoked
      ).map(group => ({
        id: group.external_references?.find(ref => ref.source_name === 'mitre-attack')?.external_id || 'Unknown',
        name: group.name,
        aliases: group.aliases || [],
        description: group.description || 'No description available',
        techniques: [], // Will be populated by cross-referencing
        lastSeen: group.modified || group.created,
        sophistication: this.assessGroupSophistication(group),
        primaryTargets: this.extractTargetSectors(group.description || ''),
        source: 'MITRE ATT&CK'
      })) || [];

      // Update cache
      this.cache.threatGroups.data = threatGroups;
      this.cache.threatGroups.lastUpdated = now;

      console.log(`[ThreatIntel] Fetched ${threatGroups.length} threat groups`);
      return threatGroups;

    } catch (error) {
      console.error('[ThreatIntel] Error fetching threat groups:', error);
      return [];
    }
  }

  // Assess threat group sophistication level
  assessGroupSophistication(group) {
    const description = (group.description || '').toLowerCase();
    
    if (description.includes('apt') || description.includes('state-sponsored') || 
        description.includes('nation-state')) {
      return 'Advanced';
    } else if (description.includes('criminal') || description.includes('ransomware')) {
      return 'Intermediate';
    } else {
      return 'Basic';
    }
  }

  // Extract target sectors from group description
  extractTargetSectors(description) {
    const sectors = [];
    const sectorKeywords = {
      'Financial': ['bank', 'financial', 'finance', 'payment'],
      'Healthcare': ['healthcare', 'hospital', 'medical', 'pharma'],
      'Government': ['government', 'military', 'defense', 'embassy'],
      'Technology': ['technology', 'software', 'tech', 'IT'],
      'Energy': ['energy', 'oil', 'gas', 'utility', 'power'],
      'Manufacturing': ['manufacturing', 'industrial', 'factory'],
      'Education': ['university', 'education', 'academic', 'research']
    };

    const lowerDesc = description.toLowerCase();
    Object.entries(sectorKeywords).forEach(([sector, keywords]) => {
      if (keywords.some(keyword => lowerDesc.includes(keyword))) {
        sectors.push(sector);
      }
    });

    return sectors.length > 0 ? sectors : ['General'];
  }

  // Enhanced vulnerability prioritization using multiple sources
  async prioritizeVulnerabilities(vulnerabilities) {
    try {
      console.log('[ThreatIntel] Prioritizing vulnerabilities with threat intelligence...');
      
      // Fetch CISA KEV for high-priority vulns
      const cisaKEV = await this.fetchCISAKnownExploitedVulns();
      
      // Enhance vulnerabilities with threat intelligence
      const enhancedVulns = vulnerabilities.map(vuln => {
        const enhanced = { ...vuln };
        
        // Check if vulnerability is in CISA KEV
        const kevEntry = cisaKEV.find(kev => kev.cveId === vuln.id);
        if (kevEntry) {
          enhanced.cisaKEV = true;
          enhanced.priority = 'CRITICAL';
          enhanced.threatIntel = {
            knownExploited: true,
            ransomwareUse: kevEntry.knownRansomwareCampaignUse === 'Known',
            requiredAction: kevEntry.requiredAction,
            dueDate: kevEntry.dueDate
          };
        }
        
        // Add EPSS score if available (Exploit Prediction Scoring System)
        enhanced.exploitProbability = this.calculateExploitProbability(vuln);
        
        return enhanced;
      });

      console.log(`[ThreatIntel] Enhanced ${enhancedVulns.length} vulnerabilities with threat intelligence`);
      return enhancedVulns;

    } catch (error) {
      console.error('[ThreatIntel] Error prioritizing vulnerabilities:', error);
      return vulnerabilities; // Return original if enhancement fails
    }
  }

  // Calculate exploit probability based on various factors
  calculateExploitProbability(vuln) {
    let probability = 0.1; // Base probability
    
    // CVSS score factor
    const cvssScore = vuln.severity?.score || 0;
    probability += (cvssScore / 10) * 0.3;
    
    // Age factor (newer vulnerabilities are more likely to be exploited)
    const publishDate = new Date(vuln.publishedDate);
    const daysSincePublish = (Date.now() - publishDate.getTime()) / (1000 * 60 * 60 * 24);
    if (daysSincePublish < 30) probability += 0.2; // Recent vulns
    else if (daysSincePublish < 90) probability += 0.1;
    
    // Vulnerability type factor
    const description = (vuln.description || '').toLowerCase();
    if (description.includes('remote code execution')) probability += 0.3;
    else if (description.includes('sql injection')) probability += 0.2;
    else if (description.includes('authentication bypass')) probability += 0.25;
    
    return Math.min(probability, 1.0); // Cap at 100%
  }

  // Get contextual threat intelligence for asset types
  async getContextualThreatIntel(assetType, businessSector = 'General') {
    try {
      console.log(`[ThreatIntel] Getting contextual threat intel for ${assetType} in ${businessSector} sector`);
      
      const threatGroups = await this.fetchThreatGroups();
      
      // Filter threat groups by sector
      const relevantGroups = threatGroups.filter(group => 
        group.primaryTargets.includes(businessSector) || 
        group.primaryTargets.includes('General')
      );

      return {
        relevantThreatGroups: relevantGroups.slice(0, 5), // Top 5 relevant groups
        sectorThreats: this.getSectorSpecificThreats(businessSector),
        assetThreats: this.getAssetSpecificThreats(assetType),
        lastUpdated: new Date().toISOString()
      };

    } catch (error) {
      console.error('[ThreatIntel] Error getting contextual threat intel:', error);
      return {
        relevantThreatGroups: [],
        sectorThreats: [],
        assetThreats: [],
        lastUpdated: new Date().toISOString()
      };
    }
  }

  // Get sector-specific threats
  getSectorSpecificThreats(sector) {
    const sectorThreats = {
      'Financial': ['Ransomware', 'Banking Trojans', 'Payment Card Fraud', 'Insider Threats'],
      'Healthcare': ['Ransomware', 'Data Theft', 'Medical Device Attacks', 'HIPAA Violations'],
      'Government': ['APT Groups', 'Espionage', 'Data Exfiltration', 'Supply Chain Attacks'],
      'Technology': ['IP Theft', 'Supply Chain Attacks', 'Zero-day Exploits', 'Insider Threats'],
      'Energy': ['ICS/SCADA Attacks', 'Nation-state Actors', 'Physical Disruption', 'Ransomware'],
      'General': ['Ransomware', 'Phishing', 'Data Theft', 'Business Email Compromise']
    };

    return sectorThreats[sector] || sectorThreats['General'];
  }

  // Get asset-specific threats
  getAssetSpecificThreats(assetType) {
    const assetThreats = {
      'Serveur': ['Web Application Attacks', 'Server Exploitation', 'Privilege Escalation'],
      'Base de données': ['SQL Injection', 'Data Exfiltration', 'Credential Theft'],
      'Application': ['Code Injection', 'Client-side Attacks', 'API Abuse'],
      'Réseau': ['Network Sniffing', 'Man-in-the-Middle', 'Lateral Movement'],
      'Équipement': ['Endpoint Malware', 'Device Compromise', 'Physical Access']
    };

    return assetThreats[assetType] || ['General Cyber Threats'];
  }
}

export default new ThreatIntelService();
