import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { SECURITY_PILLARS, SEVERITY_OPTIONS, EVENT_SUGGESTIONS, pillarColors, pillarLabels, AI_SUGGESTIONS, DREADED_EVENT_IMPACTS } from '../../../constants'; // Added DREADED_EVENT_IMPACTS
import {
  getTranslatedSeverityOptions,
  getTranslatedDreadedEventImpacts,
  getTranslatedSecurityPillars,
  getTranslatedPillarLabels,
  getTranslatedSeverityLabel
} from '../../../utils/translatedConstants';
// Remove these imports
// import { loadDataFromAPI, handleSaveDataWithAPI, getCurrentAnalysisId } from '../../../utils/appStateManagerAPI';
import { useAnalysis } from '../../../context/AnalysisContext';
import { Save, Plus, RefreshCw, AlertCircle, Zap, X, Info } from 'lucide-react';
// Import the service
import dreadedEventService from '../../../services/dreadedEventsService'; // Import the service
import GuideModal from './GuideModal';
import { showSuccessToast, showErrorToast, showLoadingToast, updateToast, showInfoToast } from '../../../utils/toastUtils';

// Composant Badge stylisé pour les statuts de gravité
const SeverityBadge = ({ severity }) => {
  const { t } = useTranslation();
  const styles = {
    'minor': 'bg-emerald-50 text-emerald-700 border-emerald-200',
    'moderate': 'bg-amber-50 text-amber-700 border-amber-200',
    'major': 'bg-orange-50 text-orange-800 border-orange-200',
    'critical': 'bg-red-50 text-red-700 border-red-200',
    'catastrophic': 'bg-slate-900 text-slate-50 border-slate-700'
  };

  const label = getTranslatedSeverityLabel(severity) || t('dreadedEvents.status.undefined');

  return (
    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border ${styles[severity] || 'bg-gray-100 text-gray-800 border-gray-200'}`}>
      {severity === 'critical' && (
        <svg className="w-3 h-3 mr-1 text-red-500" fill="currentColor" viewBox="0 0 8 8">
          <circle cx="4" cy="4" r="3" />
        </svg>
      )}
      {label}
    </span>
  );
};

// Composant Card stylisée
const Card = ({ children, className = "" }) => {
  return (
    <div className={`bg-white rounded-lg border border-gray-200 shadow-sm ${className}`}>
      {children}
    </div>
  );
};

// Composant PillarButton pour afficher les piliers de sécurité
const PillarButton = ({ pillarValue, isSelected, onClick, pillarLabels, getPillarAcronym, pillarColors }) => {
  // Fix for auditabilite/Auditabilite case issues
  let normalizedPillarValue = pillarValue;

  // Handle different variations of auditabilite
  if (pillarValue.toLowerCase() === 'auditabilite') {
    normalizedPillarValue = 'Auditabilite';
  }

  // Find the pillar in SECURITY_PILLARS using id instead of value
  const pillar = SECURITY_PILLARS.find(p => p.id.toLowerCase() === normalizedPillarValue.toLowerCase());

  // Ensure we get the correct colors, especially for Auditabilite which should be red
  let colors;
  if (normalizedPillarValue === 'Auditabilite') {
    colors = pillarColors['auditabilite'] || { bg: 'bg-red-500', text: 'text-white', border: 'border-red-600', hover: 'hover:bg-red-600' };
  } else {
    colors = pillarColors[normalizedPillarValue] || pillarColors[pillarValue] || { bg: 'bg-gray-100', text: 'text-gray-700', border: 'border-gray-300', hover: 'hover:bg-gray-200' };
  }
  const acronym = getPillarAcronym(normalizedPillarValue);
  const pillarDisplay = pillarLabels[normalizedPillarValue] || pillarLabels[pillarValue] || pillar?.name || normalizedPillarValue;

  return (
    <button
      onClick={onClick}
      className={`
        flex items-center gap-2 p-3 rounded-lg border transition-all
        ${isSelected ? `${colors.bg} ${colors.text} ring-2 ring-offset-1 ring-blue-500` : 'bg-white text-gray-800'}
        ${colors.border} ${colors.hover}
        w-full
      `}
    >
      <div className={`
        w-10 h-10 rounded-full flex items-center justify-center font-bold text-lg
        ${colors.bg} ${colors.text} border ${colors.border}
      `}>
        {acronym}
      </div>
      <div className="text-left">
        <div className="font-medium">{pillarDisplay}</div>
      </div>
    </button>
  );
};

// NEW: Modal Component for AI Suggestions
const AiSuggestionsModal = ({
  isOpen,
  onClose,
  suggestions,
  isLoading,
  error,
  onAddSelected,
  businessValueName,
  pillarLabel,
  onFetchMore
}) => {
  const { t } = useTranslation();
  const [selectedAiSuggestions, setSelectedAiSuggestions] = useState([]);

  useEffect(() => {
    // Reset selection when suggestions change or modal opens/closes
    setSelectedAiSuggestions([]);
  }, [suggestions, isOpen]);

  const toggleSelection = (suggestion) => {
    setSelectedAiSuggestions(prev =>
      prev.some(s => s.name === suggestion.name)
        ? prev.filter(s => s.name !== suggestion.name)
        : [...prev, suggestion]
    );
  };

  const handleAddClick = () => {
    onAddSelected(selectedAiSuggestions);
    onClose(); // Close modal after adding
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex justify-center items-center p-4">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-2xl max-h-[90vh] flex flex-col">
        {/* Modal Header */}
        <div className="flex justify-between items-center p-4 border-b">
          <h3 className="text-lg font-medium text-gray-900 flex items-center">
            <Zap size={20} className="mr-2 text-yellow-500" />
            {t('dreadedEvents.modal.aiSuggestions', { businessValue: businessValueName, pillar: pillarLabel })}
          </h3>
          <button onClick={onClose} className="text-gray-400 hover:text-gray-600">
            <X size={24} />
          </button>
        </div>

        {/* Modal Body */}
        <div className="p-6 overflow-y-auto flex-grow">
          {/* Display loading indicator even if there are existing suggestions */}
          {isLoading && (
            <div className="text-center py-6">
              <RefreshCw size={24} className="mx-auto animate-spin text-blue-500 mb-3" />
              <p className="text-gray-600">{t('dreadedEvents.buttons.searchingAi')}</p>
            </div>
          )}
          {/* Display error if it occurs */}
          {error && (
            <div className="bg-red-50 text-red-700 p-4 rounded-md mb-4">
              <h4 className="font-bold mb-1">{t('dreadedEvents.modal.error')}</h4>
              <p>{error}</p>
            </div>
          )}
          {/* Display suggestions */}
          {!isLoading && suggestions && suggestions.length > 0 ? (
            <div className="space-y-3">
              {suggestions.map((suggestion, index) => (
                <div
                  key={`${suggestion.name}-${index}`} // Use index for potential duplicates
                  className={`border rounded-md p-3 transition-colors flex items-start gap-3 ${
                    selectedAiSuggestions.some(s => s.name === suggestion.name)
                      ? 'bg-blue-50 border-blue-300'
                      : 'bg-white hover:bg-gray-50 border-gray-200'
                  }`}
                >
                  <input
                    type="checkbox"
                    className="h-4 w-4 mt-1 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    checked={selectedAiSuggestions.some(s => s.name === suggestion.name)}
                    onChange={() => toggleSelection(suggestion)}
                    id={`ai-suggestion-${index}`}
                  />
                  <div className="flex-1">
                     <label htmlFor={`ai-suggestion-${index}`} className="font-medium text-gray-800 cursor-pointer block">
                        {suggestion.name}
                     </label>
                     {suggestion.description && <p className="text-sm text-gray-500 mt-1">{suggestion.description}</p>}
                     <div className="mt-1">
                        <SeverityBadge severity={suggestion.severity || 'moderate'} />
                     </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            // Only show 'No suggestions' if not loading and suggestions array is empty
            !isLoading && suggestions.length === 0 && (
                <div className="text-center py-10 text-gray-500">
                  {t('dreadedEvents.empty.noSuggestions')}
                </div>
            )
          )}
        </div>

        {/* Modal Footer */}
        <div className="flex justify-between items-center p-4 border-t bg-gray-50 rounded-b-lg">
          {/* Left side: Load More button */}
          <button
            onClick={onFetchMore}
            disabled={isLoading} // Disable while loading
            className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 flex items-center"
          >
             <RefreshCw size={16} className={`mr-2 ${isLoading ? 'animate-spin' : ''}`} />
             {isLoading ? t('dreadedEvents.buttons.loading') : t('dreadedEvents.buttons.loadMore')}
          </button>

          {/* Right side: Selection count and actions */}
          <div className="flex items-center">
            <span className="text-sm text-gray-600 mr-4">
              {selectedAiSuggestions.length} {t('dreadedEvents.status.selected')}
            </span>
            <button
              onClick={onClose}
              className="px-4 py-2 mr-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
            >
              {t('dreadedEvents.buttons.cancel')}
            </button>
            <button
              onClick={handleAddClick}
              disabled={selectedAiSuggestions.length === 0 || isLoading}
              className="px-4 py-2 bg-blue-600 border border-transparent rounded-md text-sm font-medium text-white hover:bg-blue-700 disabled:opacity-50"
            >
              {t('dreadedEvents.buttons.addSelection')}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

const DreadedEvents = () => {
  const { t } = useTranslation();

  // Get translated constants
  const translatedSeverityOptions = getTranslatedSeverityOptions();
  const translatedDreadedEventImpacts = getTranslatedDreadedEventImpacts();
  const translatedPillarLabels = getTranslatedPillarLabels();

  // Define local state for props that were previously passed in
  const [dreadedEvents, setDreadedEvents] = useState([]);
  const [newEvent, setNewEvent] = useState({
    name: '',
    description: '',
    securityPillar: '',
    severity: 'moderate',
    businessValue: '',
    impacts: [] // Changed to array for multiple impacts
  });
  const [selectedSuggestions, setSelectedSuggestions] = useState([]);
  const [customEventMode, setCustomEventMode] = useState(false);
  const [editingSuggestion, setEditingSuggestion] = useState(null);
  const [eventBeingEdited, setEventBeingEdited] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  // Removed filter states
  const [selectedEvents, setSelectedEvents] = useState([]);
  const [selectAll, setSelectAll] = useState(false);
  const [activeSectionTab, setActiveSectionTab] = useState('suggestions');
  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [loadedBusinessValues, setLoadedBusinessValues] = useState([]);

  // NEW state for AI suggestions
  const [aiSuggestions, setAiSuggestions] = useState([]);
  const [isFetchingAiSuggestions, setIsFetchingAiSuggestions] = useState(false);
  const [showAiSuggestionsModal, setShowAiSuggestionsModal] = useState(false);
  const [aiSuggestionsError, setAiSuggestionsError] = useState(null);

  // State for guide modal
  const [isGuideOpen, setIsGuideOpen] = useState(false);

  // State for database suggestions
  const [dbSuggestions, setDbSuggestions] = useState([]);
  const [isLoadingDbSuggestions, setIsLoadingDbSuggestions] = useState(false);

  // Get current analysis context
  const {
    currentAnalysis,
    currentBusinessValues,
    currentDreadedEvents,
    saveCurrentDreadedEvents,
    isWorkshopDataLoading,
    workshopDataError,
    // Need context data for AI
    currentContextData
  } = useAnalysis();

  useEffect(() => {
    if (currentDreadedEvents) {
      setDreadedEvents(currentDreadedEvents);
    }
  }, [currentDreadedEvents]);

  useEffect(() => {
    if (currentBusinessValues) {
      setLoadedBusinessValues(currentBusinessValues);
    }
  }, [currentBusinessValues]);

  // Load suggestions from database when security pillar changes
  useEffect(() => {
    if (newEvent.securityPillar) {
      loadSuggestionsFromDatabase(newEvent.securityPillar);
    }
  }, [newEvent.securityPillar]);

  const handleEnhancedSave = async () => {
    setIsSaving(true);

    // Show loading toast
    const toastId = showLoadingToast(t('dreadedEvents.status.saving'));

    try {
      const response = await saveCurrentDreadedEvents(dreadedEvents);
      if (response.success) {
        // Update loading toast to success
        updateToast(toastId, t('dreadedEvents.status.saved'), 'success');
      } else {
        // Update loading toast to error
        updateToast(toastId, `${t('dreadedEvents.status.saveError')}: ${response.message || "Erreur inconnue"}`, 'error');
      }
    } catch (error) {
      console.error("Error saving dreaded events:", error);
      // Update loading toast to error
      updateToast(toastId, `${t('dreadedEvents.status.saveError')}: ${error.message || 'Erreur inconnue'}`, 'error');
    } finally {
      setIsSaving(false);
    }
  };

  // Réinitialiser le pilier de sécurité quand la valeur métier change
  const handleBusinessValueChange = (value) => {
    setNewEvent({
      ...newEvent,
      businessValue: value,
      securityPillar: '' // Réinitialiser le pilier sélectionné
    });

    setSelectedSuggestions([]);
    setCustomEventMode(false);
    setEditingSuggestion(null);
  };

  // Quand le pilier de sécurité change, réinitialiser les suggestions sélectionnées
  useEffect(() => {
    setSelectedSuggestions([]);
    setCustomEventMode(false);
    setEditingSuggestion(null);
    setSearchTerm('');
  }, [newEvent.securityPillar]);

  const effectiveBusinessValues = loadedBusinessValues;

  const handleAddEvent = () => {
    if (newEvent.name.trim() && newEvent.securityPillar && newEvent.businessValue) {
      // Ajouter l'identifiant Date.now() pour assurer un ordre prévisible
      const newEventWithId = {
        id: Date.now(),
        ...newEvent // Includes name, description, securityPillar, severity, businessValue, impact
      };

      // Ajouter le nouvel événement à la liste existante
      setDreadedEvents([...dreadedEvents, newEventWithId]);

      // Réinitialiser le formulaire mais conserver le contexte
      setNewEvent({
        name: '',
        description: '',
        securityPillar: newEvent.securityPillar, // Conserver le pilier sélectionné
        severity: 'moderate',
        businessValue: newEvent.businessValue, // Conserver la valeur métier
        impacts: [] // Reset impacts for next potential custom event
      });
      setEventBeingEdited(null);

      // Show success toast
      showSuccessToast(t('dreadedEvents.status.eventAdded', { name: newEventWithId.name }));
    } else {
      // Show error toast if required fields are missing
      showErrorToast(t('dreadedEvents.status.fillRequiredFields'));
    }
  };

  // Fonction pour ajouter les événements suggérés sélectionnés
  const handleAddSelectedSuggestions = () => {
    if (selectedSuggestions.length === 0) {
      showErrorToast(t('dreadedEvents.status.selectAtLeastOne'));
      return;
    }

    const newEvents = selectedSuggestions.map(suggestion => ({
      id: Date.now() + Math.floor(Math.random() * 1000), // Identifiant unique
      name: suggestion.name,
      description: suggestion.description,
      securityPillar: newEvent.securityPillar,
      severity: suggestion.severity,
      businessValue: newEvent.businessValue,
      impacts: suggestion.impacts || (suggestion.impact ? [suggestion.impact] : []) // Convert single impact to array if needed
    }));

    setDreadedEvents([...dreadedEvents, ...newEvents]);
    setSelectedSuggestions([]);
    setEditingSuggestion(null);

    // Show success toast
    const plural = newEvents.length > 1 ? 's' : '';
    showSuccessToast(t('dreadedEvents.status.suggestionsAdded', { count: newEvents.length, plural }));
  };

  // NEW: Function to add selected AI suggestions
  const handleAddSelectedAiSuggestions = (selectedAiToAdd) => {
    if (!newEvent.businessValue || !newEvent.securityPillar) {
      showErrorToast(t('dreadedEvents.status.selectBusinessValueAndPillar'));
      return;
    }

    if (selectedAiToAdd.length === 0) {
      showErrorToast(t('dreadedEvents.status.selectAtLeastOne'));
      return;
    }

    const newEvents = selectedAiToAdd.map((suggestion, index) => {
      const newEventObject = {
        id: Date.now() + index, // Simple unique ID generation
        name: suggestion.name,
        description: suggestion.description || '', // Use description if provided
        securityPillar: newEvent.securityPillar, // Use pillar selected in the main form
        businessValue: newEvent.businessValue, // Use BV selected in the main form
        severity: suggestion.severity || 'moderate', // Default to 'moderate' if missing
        impacts: suggestion.impacts || (suggestion.impact ? [suggestion.impact] : []), // Convert single impact to array if needed
        isSuggestion: false, // Mark as a concrete event
        isAiGenerated: true, // Mark as AI generated
      };
      return newEventObject;
    });

    setDreadedEvents(prevEvents => [...prevEvents, ...newEvents]);

    // Show success toast
    const plural = newEvents.length > 1 ? 's' : '';
    showSuccessToast(t('dreadedEvents.status.aiSuggestionsGenerated', { count: newEvents.length, plural }));

    // Optional: Clear selection in modal state if needed (though modal closes anyway)
    // setSelectedAiSuggestions([]); // If managing selection state outside modal
  };

  const handleRemoveEvent = (id) => {
    // Find the event to be removed for the toast message
    const eventToRemove = dreadedEvents.find(event => event.id === id);

    setDreadedEvents(dreadedEvents.filter(event => event.id !== id));

    // Show success toast
    if (eventToRemove) {
      showSuccessToast(t('dreadedEvents.status.eventRemoved', { name: eventToRemove.name }));
    } else {
      showSuccessToast(t('dreadedEvents.status.eventRemoved', { name: '' }));
    }
  };

  // Fonction pour modifier un événement existant
  const handleEditEvent = (event) => {
    setEventBeingEdited(event);
    // Copier l'événement dans le formulaire de création
    setNewEvent({
      name: event.name,
      description: event.description,
      securityPillar: event.securityPillar,
      severity: event.severity,
      businessValue: event.businessValue,
      impacts: event.impacts || event.impact ? [event.impact] : [] // Handle both old impact field and new impacts array
    });
    setCustomEventMode(true);
    // S'assurer que nous sommes dans la vue formulaire
    setActiveSectionTab('form');
  };

  // Mettre à jour un événement existant
  const handleUpdateEvent = () => {
    if (!eventBeingEdited || !newEvent.name.trim()) {
      showErrorToast(t('dreadedEvents.status.fillRequiredFields'));
      return;
    }

    setDreadedEvents(dreadedEvents.map(event =>
      event.id === eventBeingEdited.id
        ? {
            ...event,
            name: newEvent.name,
            description: newEvent.description,
            severity: newEvent.severity,
            impacts: newEvent.impacts // Update impacts array
          }
        : event
    ));

    // Réinitialiser le formulaire mais conserver le contexte
    setNewEvent({
      name: '',
      description: '',
      securityPillar: newEvent.securityPillar,
      severity: 'moderate',
      businessValue: newEvent.businessValue,
      impacts: [] // Reset impacts after update
    });
    setEventBeingEdited(null);

    // Show success toast
    showSuccessToast(t('dreadedEvents.status.eventUpdated', { name: newEvent.name }));
  };

  // Fonction pour éditer une suggestion avant de l'ajouter
  const handleEditSuggestion = (suggestion, index) => {
    // Vérifier si cette suggestion est déjà dans les sélections
    if (!selectedSuggestions.some(s => s.name === suggestion.name)) {
      toggleSuggestion(suggestion);
    }

    setEditingSuggestion({
      ...suggestion,
      index
    });
  };

  // Fonction pour mettre à jour une suggestion modifiée
  const handleUpdateSuggestion = (updatedSuggestion) => {
    // Mettre à jour selectedSuggestions
    const updatedSuggestions = selectedSuggestions.map(s =>
      s.name === editingSuggestion.name ? {
        ...updatedSuggestion,
        impacts: updatedSuggestion.impacts || (updatedSuggestion.impact ? [updatedSuggestion.impact] : []) // Convert to array if needed
      } : s
    );
    setSelectedSuggestions(updatedSuggestions);
    setEditingSuggestion(null);
  };

  // Fonction pour annuler l'édition d'une suggestion
  const handleCancelEditSuggestion = () => {
    setEditingSuggestion(null);
  };

  // Fonction pour filtrer les événements selon des critères
  const handleFilterEvents = (filterType) => {
    if (filterType === 'critical') {
      alert("Filtrer les événements critiques - fonctionnalité à implémenter");
    } else if (filterType === 'byPillar') {
      alert("Filtrer par pilier - fonctionnalité à implémenter");
    }
  };

  // Fonction pour basculer une suggestion de la sélection
  const toggleSuggestion = (suggestion) => {
    if (selectedSuggestions.some(s => s.name === suggestion.name)) {
      setSelectedSuggestions(selectedSuggestions.filter(s => s.name !== suggestion.name));
    } else {
      setSelectedSuggestions([...selectedSuggestions, {...suggestion}]);
    }
  };

  // Fonction pour obtenir l'acronyme d'un pilier
  const getPillarAcronym = (pillarValue) => {
    // Normalize the pillar value to handle case inconsistencies
    const normalizedValue = pillarValue?.toLowerCase();

    if (normalizedValue === '0' || normalizedValue === 'confidentialite') return 'C';
    if (normalizedValue === '4' || normalizedValue === 'integrite') return 'I';
    if (normalizedValue === '1' || normalizedValue === 'disponibilite') return 'D';
    if (normalizedValue === '2' || normalizedValue === 'tracabilite') return 'T';
    if (normalizedValue === '3' || normalizedValue === 'preuve') return 'P';
    if (normalizedValue === '5' || normalizedValue === 'auditabilite') return 'A';
    return '';
  };

  // NEW: Function to fetch MORE AI suggestions
  const handleFetchMoreAiSuggestions = async () => {
    if (!currentAnalysis?.id || !newEvent.businessValue || !newEvent.securityPillar) {
      // Should not happen if the modal is open, but good practice
      return;
    }

    setIsFetchingAiSuggestions(true);
    setAiSuggestionsError(null); // Clear previous errors
    // DO NOT clear existing suggestions: setAiSuggestions([]);

    try {
      // Reuse the same context gathering logic as handleFetchAiSuggestions
      const selectedBusinessValue = effectiveBusinessValues.find(bv => bv.id.toString() === newEvent.businessValue);
      const pillarLabel = pillarLabels[newEvent.securityPillar] || newEvent.securityPillar;

      const contextPayload = {
        analysisId: currentAnalysis.id,
        analysisName: currentAnalysis.name,
        analysisDescription: currentAnalysis.description,
        organizationContext: currentContextData?.scope || currentContextData?.organizationName || 'Non défini',
        businessValue: {
          id: selectedBusinessValue?.id,
          name: selectedBusinessValue?.name,
          description: selectedBusinessValue?.description,
        },
        securityPillar: pillarLabel,
        // Add existing suggestions to payload to request variety
        existingSuggestionNames: aiSuggestions.map(s => s.name)
      };

      const response = await dreadedEventService.getAiDreadedEventSuggestions(contextPayload);

      if (response.success && response.data?.suggestions) {
        const newSuggestions = response.data.suggestions;
        const formattedNewSuggestions = newSuggestions.map(s => ({
            name: s.name || 'Suggestion sans nom',
            description: s.description || '',
            severity: s.severity || 'moderate',
            impacts: s.impacts || (s.impact ? [s.impact] : [])
        })).filter(s => s.name);

        // Append new suggestions to the existing ones
        setAiSuggestions(prevSuggestions => [...prevSuggestions, ...formattedNewSuggestions]);

        if (formattedNewSuggestions.length > 0) {
          const plural = formattedNewSuggestions.length > 1 ? 's' : '';
          showSuccessToast(t('dreadedEvents.status.newAiSuggestionsGenerated', { count: formattedNewSuggestions.length, plural }));
        } else {
          showInfoToast(t('dreadedEvents.status.noNewAiSuggestions'));
        }
      } else {
        throw new Error(response.message || 'Failed to retrieve more AI suggestions.');
      }
    } catch (error) {
      console.error('Error fetching more AI suggestions:', error);
      const errorMessage = error.message || 'Une erreur est survenue lors de la récupération de suggestions supplémentaires.';
      setAiSuggestionsError(errorMessage);
      showErrorToast(errorMessage);
      // Keep the modal open to show the error
    } finally {
      setIsFetchingAiSuggestions(false);
    }
  };

  // Original function to fetch initial suggestions (opens modal)
  const handleFetchAiSuggestions = async () => {
    if (!currentAnalysis?.id || !newEvent.businessValue || !newEvent.securityPillar) {
      showErrorToast(t('dreadedEvents.status.selectAnalysisValuePillar'));
      return;
    }

    setIsFetchingAiSuggestions(true);
    setAiSuggestionsError(null);
    setAiSuggestions([]); // Clear previous suggestions for the initial fetch
    setShowAiSuggestionsModal(true); // Open modal

    try {
      // Context gathering and API call logic (same as in handleFetchMoreAiSuggestions initially)
      const selectedBusinessValue = effectiveBusinessValues.find(bv => bv.id.toString() === newEvent.businessValue);
      const pillarLabel = pillarLabels[newEvent.securityPillar] || newEvent.securityPillar;
      const contextPayload = {
        analysisId: currentAnalysis.id,
        analysisName: currentAnalysis.name,
        analysisDescription: currentAnalysis.description,
        organizationContext: (currentContextData?.missions && currentContextData.missions.length > 0)
          ? currentContextData.missions.join(', ')
          : currentContextData?.scope || currentContextData?.organizationName || 'Non défini',
        businessValue: { id: selectedBusinessValue?.id, name: selectedBusinessValue?.name, description: selectedBusinessValue?.description },
        securityPillar: pillarLabel,
      };
      const response = await dreadedEventService.getAiDreadedEventSuggestions(contextPayload);
      if (response.success && response.data?.suggestions) {
        const suggestions = response.data.suggestions;
        const formattedSuggestions = suggestions.map(s => ({
            name: s.name || 'Suggestion sans nom',
            description: s.description || '',
            severity: s.severity || 'moderate',
            impacts: s.impacts || (s.impact ? [s.impact] : [])
        })).filter(s => s.name);
        setAiSuggestions(formattedSuggestions);

        if (formattedSuggestions.length > 0) {
          const plural = formattedSuggestions.length > 1 ? 's' : '';
          showSuccessToast(t('dreadedEvents.status.aiSuggestionsGenerated', { count: formattedSuggestions.length, plural }));
        } else {
          showInfoToast(t('dreadedEvents.status.noAiSuggestions'));
        }
      } else {
        throw new Error(response.message || 'Failed to retrieve AI suggestions.');
      }
    } catch (error) {
      console.error('Error fetching AI suggestions:', error);
      const errorMessage = error.message || 'Une erreur est survenue lors de la récupération des suggestions IA.';
      setAiSuggestionsError(errorMessage);
      showErrorToast(errorMessage);
    } finally {
      setIsFetchingAiSuggestions(false);
    }
  };

  // Function to load suggestions from database
  const loadSuggestionsFromDatabase = async (securityPillar) => {
    setIsLoadingDbSuggestions(true);
    try {
      const response = await dreadedEventService.getEventSuggestions(securityPillar);
      if (response.success && response.data) {
        setDbSuggestions(response.data || []); // Correctly access the data array
      } else {
        console.error('Failed to load suggestions:', response.message);
        // Fallback to constants if API fails
        setDbSuggestions(EVENT_SUGGESTIONS[securityPillar] || []);
      }
    } catch (error) {
      console.error('Error loading suggestions:', error);
      // Fallback to constants if API fails
      setDbSuggestions(EVENT_SUGGESTIONS[securityPillar] || []);
    } finally {
      setIsLoadingDbSuggestions(false);
    }
  };

  // Filtrer les suggestions selon le terme de recherche
  const filteredSuggestions = dbSuggestions.length > 0
    ? dbSuggestions.filter(suggestion =>
        suggestion.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (suggestion.description && suggestion.description.toLowerCase().includes(searchTerm.toLowerCase()))
      )
    : [];

  if (isWorkshopDataLoading) {
    return (
      <div className="bg-gray-50 p-6 rounded-lg flex justify-center items-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p className="text-gray-600">{t('dreadedEvents.status.saving')}</p>
        </div>
      </div>
    );
  }

  // Show error state
  if (workshopDataError?.dreadedEvents) {
    return (
      <div className="bg-gray-50 p-6 rounded-lg">
        <div className="bg-red-50 text-red-700 p-4 rounded-lg mb-4">
          <h3 className="font-bold mb-2">Error Loading Data</h3>
          <p>{workshopDataError.dreadedEvents}</p>
          <button
            className="mt-2 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
            onClick={() => window.location.reload()}
          >
            {t('dreadedEvents.buttons.reset')}
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
      {/* === MERGED HEADER === */}
      <div className="bg-white rounded-xl shadow-sm border border-slate-100 p-4 mb-6">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          {/* Left Side: Breadcrumb & Title */}
          <div>
            <div className="flex items-center text-sm text-slate-500 mb-2">
              <span className="hover:text-blue-600 transition-colors">{t('dreadedEvents.breadcrumb.workshop1')}</span>
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mx-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
              <span className="text-blue-600 font-medium">{t('dreadedEvents.breadcrumb.dreadedEvents')}</span>
            </div>
            <h1 className="text-2xl sm:text-3xl font-bold text-slate-800 flex items-center">
              <AlertCircle size={28} className="mr-3 text-blue-600" />
              {t('dreadedEvents.title')}
            </h1>
          </div>

          {/* Right Side: Action Buttons */}
          <div className="flex items-center space-x-3">
            {/* Save Button */}
            <button
              className="text-sm font-medium bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center shadow-sm transition duration-200 disabled:opacity-50"
              onClick={handleEnhancedSave}
              disabled={isSaving}
            >
              {isSaving ? (
                <>
                  <RefreshCw size={16} className="mr-2 animate-spin" />
                  {t('dreadedEvents.buttons.saving')}
                </>
              ) : (
                <>
                  <Save size={16} className="mr-2" />
                  {t('dreadedEvents.buttons.save')}
                </>
              )}
            </button>

            {/* Help Button */}
            <button
              className="text-sm font-medium bg-slate-100 text-slate-700 px-4 py-2 rounded-lg hover:bg-slate-200 flex items-center shadow-sm transition duration-200"
              onClick={() => setIsGuideOpen(true)}
            >
              <Info size={16} className="mr-2" />
              {t('dreadedEvents.buttons.help')}
            </button>
          </div>
        </div>
      </div>
      {/* === END MERGED HEADER === */}

      {/* Loading indicator */}
      {isLoading && (
        <div className="bg-blue-50 p-4 rounded-lg border border-blue-100 mb-6 flex items-center">
          <RefreshCw size={20} className="text-blue-500 mr-2 animate-spin" />
          <p className="text-blue-700">{t('dreadedEvents.buttons.loading')}</p>
        </div>
      )}

      <div className="bg-gradient-to-r from-blue-50 to-indigo-50 p-4 rounded-lg border border-blue-100 mb-6">
        <div className="flex items-start gap-3">
          <div className="text-blue-500 bg-white p-2 rounded-full shadow-sm">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
            </svg>
          </div>
          <div>
            <h3 className="text-blue-800 font-medium mb-1">{t('dreadedEvents.sections.definition')}</h3>
            <p className="text-blue-700 text-sm">
              {t('dreadedEvents.info.definition')}
            </p>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
        {/* Colonne 1: Valeur métier et pilier */}
        <div className="lg:col-span-1">
          <div className="sticky top-4 space-y-6">
            <Card className="p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">{t('dreadedEvents.sections.criteriaSelection')}</h3>

              {/* Sélection valeur métier avec indicateurs */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {t('dreadedEvents.fields.businessValue')}
                </label>
                <select
                  className="w-full px-3 py-2 bg-white border border-gray-300 rounded-md shadow-sm
                           focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  value={newEvent.businessValue}
                  onChange={(e) => handleBusinessValueChange(e.target.value)}
                >
                  <option value="">{t('dreadedEvents.placeholders.selectBusinessValue')}</option>
                  {effectiveBusinessValues.map(value => {
                    // Compter les événements redoutés pour cette valeur métier
                    const eventsCount = dreadedEvents.filter(e => e.businessValue === value.id.toString()).length;
                    // Déterminer la couleur de fond en fonction du nombre d'événements
                    const hasDreadedEvents = eventsCount > 0;

                    return (
                      <option
                        key={value.id}
                        value={value.id}
                        style={{
                          fontWeight: hasDreadedEvents ? 'bold' : 'normal',
                          backgroundColor: hasDreadedEvents ? '#EBF5FF' : 'white'
                        }}
                      >
                        {value.name} {eventsCount > 0 ? `(${eventsCount} ER)` : ''}
                      </option>
                    );
                  })}
                </select>

                {/* Légende pour les valeurs métier */}
                <div className="mt-1 text-xs text-gray-500 flex items-center">
                  <div className="w-3 h-3 bg-blue-100 mr-1 border border-blue-200 rounded-sm"></div>
                  <span>{t('dreadedEvents.info.businessValuesWithEvents')}</span>
                </div>
              </div>

              {/* Sélection pilier de sécurité avec indicateurs */}
              {newEvent.businessValue && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    {t('dreadedEvents.fields.securityPillar')}
                  </label>

                  {effectiveBusinessValues.find(v => v.id.toString() === newEvent.businessValue)?.securityPillars?.length > 0 ? (
                    <div className="space-y-2">
                      {effectiveBusinessValues.find(v => v.id.toString() === newEvent.businessValue)?.securityPillars.map(pillarValue => {
                        // Compter les événements redoutés pour cette valeur métier et ce pilier
                        const pillarEventsCount = dreadedEvents.filter(
                          e => e.businessValue === newEvent.businessValue &&
                               e.securityPillar === (pillarValue.toLowerCase() === 'auditabilite' ? 'Auditabilite' : pillarValue)
                        ).length;

                        return (
                          <div key={pillarValue} className="relative">
                            <PillarButton
                              pillarValue={pillarValue}
                              isSelected={newEvent.securityPillar === pillarValue}
                              onClick={() => setNewEvent({...newEvent, securityPillar: pillarValue.toLowerCase() === 'auditabilite' ? 'Auditabilite' : pillarValue})} // Force correct casing for 'Auditabilite'
                              pillarLabels={pillarLabels}
                              getPillarAcronym={getPillarAcronym}
                              pillarColors={pillarColors}
                            />
                            {pillarEventsCount > 0 && (
                              <div className="absolute top-0 right-0 transform -translate-y-1/3 translate-x-1/3">
                                <span className="flex items-center justify-center w-5 h-5 bg-blue-600 text-white text-xs font-bold rounded-full shadow-sm">
                                  {pillarEventsCount}
                                </span>
                              </div>
                            )}
                          </div>
                        );
                      })}
                    </div>
                  ) : (
                    <div className="bg-gray-50 border border-gray-200 rounded-lg p-4 text-center">
                      <p className="text-gray-500">{t('dreadedEvents.empty.noSuggestionsForPillar')}</p>
                    </div>
                  )}
                </div>
              )}

              {/* AI Suggestions Button moved to tab navigation */}

            </Card>
          </div>
        </div>

        {/* Colonnes 2-3: Formulaire dynamique ou message */}
        <div className="lg:col-span-2">
          {newEvent.businessValue && newEvent.securityPillar ? (
          <Card className="p-6">
          {/* Onglets pour les modes de sélection et bouton IA */}
          <div className="flex items-center border-b mb-6">
            <div className="flex">
              {['suggestions', 'form'].map(tab => (
                <button
                  key={tab}
                  className={`px-4 py-2 text-sm font-medium ${
                    activeSectionTab === tab
                      ? 'text-blue-600 border-b-2 border-blue-600'
                      : 'text-gray-500 hover:text-gray-700'
                  }`}
                  onClick={() => setActiveSectionTab(tab)}
                >
                  {tab === 'suggestions' ? t('dreadedEvents.sections.suggestions') :
                   t('dreadedEvents.sections.customEvent')}
                </button>
              ))}
            </div>

            {/* AI Suggestions Button - Enhanced */}
            {newEvent.businessValue && newEvent.securityPillar && (
              <button
                onClick={handleFetchAiSuggestions}
                disabled={isFetchingAiSuggestions}
                className="ml-auto px-4 py-1.5 text-sm font-medium text-white bg-gradient-to-r from-purple-500 to-indigo-600 hover:from-purple-600 hover:to-indigo-700 rounded-md flex items-center shadow-sm transition-all duration-200 transform hover:scale-105 hover:shadow focus:outline-none focus:ring-2 focus:ring-offset-1 focus:ring-indigo-500 disabled:opacity-60 disabled:cursor-not-allowed disabled:hover:scale-100 disabled:hover:shadow-sm"
              >
                <Zap size={16} className="mr-1.5 text-yellow-100" />
                {isFetchingAiSuggestions ?
                  <span className="flex items-center">
                    <RefreshCw size={14} className="mr-1.5 animate-spin" />
                    {t('dreadedEvents.buttons.searchingAi')}
                  </span> :
                  t('dreadedEvents.buttons.getAiSuggestions')
                }
              </button>
            )}
          </div>

          {/* Contenu selon l'onglet actif */}
          {activeSectionTab === 'suggestions' ? (
            <div className="space-y-6">
              <div className="flex justify-between items-center">
                <h3 className="text-lg font-medium text-gray-900">
                  {t('dreadedEvents.sections.proposalsFor')} {translatedPillarLabels[newEvent.securityPillar]}
                </h3>
                <span className="text-sm text-gray-500">
                  {selectedSuggestions.length} {t('dreadedEvents.status.selected')}
                </span>
              </div>

              {/* Barre de recherche */}
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <svg className="h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clipRule="evenodd" />
                  </svg>
                </div>
                <input
                  type="text"
                  className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  placeholder={t('dreadedEvents.placeholders.searchSuggestion')}
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>

              {/* Liste des suggestions */}
              <div className="border rounded-lg overflow-hidden mb-4">
                <div className="overflow-y-auto max-h-80">
                  {isLoadingDbSuggestions ? (
                    <div className="p-6 text-center">
                      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-3"></div>
                      <p className="text-gray-500">{t('dreadedEvents.buttons.loading')}</p>
                    </div>
                  ) : filteredSuggestions.length > 0 ? (
                    <div className="divide-y divide-gray-200">
                      {filteredSuggestions.map((suggestion, index) => {
                        const isSelected = selectedSuggestions.some(s => s.name === suggestion.name);
                        const isEditing = editingSuggestion && editingSuggestion.index === index;

                        return (
                          <div
                            key={index}
                            className={`transition-colors p-4 ${
                              isEditing ? 'bg-blue-50' :
                              isSelected ? 'bg-blue-50 border-l-4 border-blue-500' :
                              'hover:bg-gray-50'
                            }`}
                          >
                            {isEditing ? (
                              <div className="space-y-3">
                                {[
                                  {label: t('dreadedEvents.fields.eventName'), type: "text", value: editingSuggestion.name,
                                   onChange: (e) => setEditingSuggestion({...editingSuggestion, name: e.target.value})},
                                  {label: t('dreadedEvents.fields.description'), type: "textarea", value: editingSuggestion.description,
                                   onChange: (e) => setEditingSuggestion({...editingSuggestion, description: e.target.value})}
                                ].map((field, idx) => (
                                  <div key={idx}>
                                    <label className="block text-xs font-medium text-gray-500 mb-1">
                                      {field.label}
                                    </label>
                                    {field.type === "textarea" ? (
                                      <textarea
                                        className="w-full p-2 border rounded-md"
                                        rows="2"
                                        value={field.value}
                                        onChange={field.onChange}
                                      />
                                    ) : (
                                      <input
                                        type={field.type}
                                        className="w-full p-2 border rounded-md"
                                        value={field.value}
                                        onChange={field.onChange}
                                      />
                                    )}
                                  </div>
                                ))}
                                <div>
                                  <label className="block text-xs font-medium text-gray-500 mb-1">
                                    {t('dreadedEvents.fields.gravity')}
                                  </label>
                                  <select
                                    className="w-full p-2 border rounded-md"
                                    value={editingSuggestion.severity}
                                    onChange={(e) => setEditingSuggestion({...editingSuggestion, severity: e.target.value})}
                                  >
                                    {SEVERITY_OPTIONS.map(option => (
                                      <option key={option.value} value={option.value}>{option.label}</option>
                                    ))}
                                  </select>
                                </div>
                                <div>
                                  <label className="block text-xs font-medium text-gray-500 mb-1">
                                    {t('dreadedEvents.fields.impacts')}
                                  </label>
                                  <div className="space-y-1 mt-1">
                                    {DREADED_EVENT_IMPACTS.map((impactOption) => {
                                      // Handle both old impact field and new impacts array
                                      const impacts = editingSuggestion.impacts ||
                                        (editingSuggestion.impact ? [editingSuggestion.impact] : []);
                                      const isSelected = impacts.includes(impactOption.value);

                                      return (
                                        <div
                                          key={impactOption.value}
                                          className={`flex items-center p-1.5 rounded border ${isSelected ? 'bg-blue-50 border-blue-300' : 'border-gray-200'}`}
                                        >
                                          <input
                                            type="checkbox"
                                            id={`edit-impact-${impactOption.value}`}
                                            checked={isSelected}
                                            onChange={() => {
                                              const currentImpacts = editingSuggestion.impacts ||
                                                (editingSuggestion.impact ? [editingSuggestion.impact] : []);

                                              const newImpacts = isSelected
                                                ? currentImpacts.filter(i => i !== impactOption.value)
                                                : [...currentImpacts, impactOption.value];

                                              setEditingSuggestion({
                                                ...editingSuggestion,
                                                impacts: newImpacts,
                                                impact: undefined // Remove old single impact field
                                              });
                                            }}
                                            className="h-3 w-3 text-blue-600 focus:ring-blue-500 border-gray-300 rounded mr-1"
                                          />
                                          <label
                                            htmlFor={`edit-impact-${impactOption.value}`}
                                            className="ml-1 block text-xs text-gray-700 cursor-pointer flex-1"
                                          >
                                            {impactOption.label}
                                          </label>
                                        </div>
                                      );
                                    })}
                                  </div>
                                </div>
                                <div className="flex justify-end space-x-2 mt-2">
                                  <button
                                    className="text-gray-500 hover:text-gray-700 text-sm px-3 py-1 border rounded-md"
                                    onClick={handleCancelEditSuggestion}
                                  >
                                    {t('dreadedEvents.buttons.cancel')}
                                  </button>
                                  <button
                                    className="bg-blue-600 text-white text-sm px-3 py-1 rounded-md hover:bg-blue-700"
                                    onClick={() => handleUpdateSuggestion(editingSuggestion)}
                                  >
                                    {t('dreadedEvents.buttons.validate')}
                                  </button>
                                </div>
                              </div>
                            ) : (
                              <div>
                                <div className="flex items-start">
                                  <input
                                    type="checkbox"
                                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                    checked={isSelected}
                                    onChange={() => toggleSuggestion(suggestion)}
                                  />
                                  <div className="ml-3 flex-1">
                                    <div className="flex justify-between">
                                      <label className="font-medium text-gray-900 cursor-pointer" onClick={() => toggleSuggestion(suggestion)}>
                                        {suggestion.name}
                                      </label>
                                      <SeverityBadge severity={suggestion.severity} />
                                    </div>
                                    <p className="mt-1 text-sm text-gray-500">{suggestion.description}</p>
                                  </div>
                                </div>
                                <div className="mt-2 flex justify-end">
                                  <button
                                    className="text-sm text-blue-600 hover:text-blue-800"
                                    onClick={() => handleEditSuggestion(suggestion, index)}
                                  >
                                    {t('dreadedEvents.buttons.edit')}
                                  </button>
                                </div>
                              </div>
                            )}
                          </div>
                        );
                      })}
                    </div>
                  ) : (
                    <div className="p-6 text-center text-gray-500">
                      {searchTerm ? t('dreadedEvents.empty.noSearchResults') :
                       t('dreadedEvents.empty.noSuggestionsForPillar')}
                    </div>
                  )}
                </div>
              </div>

              {/* Bouton d'ajout */}
              <button
                className="w-full inline-flex justify-center items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
                disabled={selectedSuggestions.length === 0}
                onClick={handleAddSelectedSuggestions}
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-11a1 1 0 10-2 0v2H7a1 1 0 100 2h2v2a1 1 0 102 0v-2h2a1 1 0 100-2h-2V7z" clipRule="evenodd" />
                </svg>
                {t('dreadedEvents.buttons.add')} {selectedSuggestions.length} {t('dreadedEvents.status.events')} {t('dreadedEvents.status.selected')}
              </button>
            </div>
          ) : (
            <div className="space-y-6">
              <h3 className="text-lg font-medium text-gray-900">
                {eventBeingEdited ? t('dreadedEvents.modal.editEvent') : t('dreadedEvents.modal.createCustomEvent')}
              </h3>

              {/* Form fields - name and description */}
              {['name', 'description'].map((field, idx) => (
                <div key={idx}>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    {field === 'name' ? t('dreadedEvents.fields.eventName') : t('dreadedEvents.fields.description')}
                  </label>
                  {field === 'description' ? (
                    <textarea
                      className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                      rows="3"
                      value={newEvent[field]}
                      onChange={(e) => setNewEvent({...newEvent, [field]: e.target.value})}
                      placeholder={field === 'name' ? t('dreadedEvents.placeholders.eventName') : t('dreadedEvents.placeholders.eventDescription')}
                    />
                  ) : (
                    <input
                      type="text"
                      className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                      value={newEvent[field]}
                      onChange={(e) => setNewEvent({...newEvent, [field]: e.target.value})}
                      placeholder={field === 'name' ? t('dreadedEvents.placeholders.eventName') : ""}
                    />
                  )}
                </div>
              ))}

              {/* Severity selection */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {t('dreadedEvents.fields.severity')}
                </label>
                <div className="grid grid-cols-5 gap-2">
                  {translatedSeverityOptions.map(option => {
                    const severityColors = {
                      'minor': { bg: 'bg-emerald-50', text: 'text-emerald-700', border: 'border-emerald-300' },
                      'moderate': { bg: 'bg-amber-50', text: 'text-amber-700', border: 'border-amber-300' },
                      'major': { bg: 'bg-orange-50', text: 'text-orange-800', border: 'border-orange-300' },
                      'critical': { bg: 'bg-red-50', text: 'text-red-700', border: 'border-red-300' },
                      'catastrophic': { bg: 'bg-slate-900', text: 'text-slate-50', border: 'border-slate-700' }
                    };

                    const colors = severityColors[option.value] || { bg: 'bg-gray-50', text: 'text-gray-800', border: 'border-gray-300' };

                    return (
                      <button
                        key={option.value}
                        type="button"
                        onClick={() => setNewEvent({...newEvent, severity: option.value})}
                        className={`p-3 rounded-md border transition-all text-center
                          ${colors.bg} ${colors.text} ${colors.border}
                          ${newEvent.severity === option.value ? 'ring-2 ring-offset-1 ring-blue-500' : ''}
                          hover:opacity-90`}
                      >
                        <div className="font-medium text-sm">{option.label}</div>
                      </button>
                    );
                  })}
                </div>
              </div>

              {/* Impact selection - Multiple */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {t('dreadedEvents.fields.impacts')}
                </label>
                <div className="grid grid-cols-1 gap-2 mt-2">
                  {translatedDreadedEventImpacts.map((impactOption) => {
                    const isSelected = newEvent.impacts.includes(impactOption.value);
                    return (
                      <div
                        key={impactOption.value}
                        className={`flex items-center p-2 rounded border ${isSelected ? 'bg-blue-50 border-blue-300' : 'border-gray-300'}`}
                      >
                        <input
                          type="checkbox"
                          id={`impact-${impactOption.value}`}
                          checked={isSelected}
                          onChange={() => {
                            const newImpacts = isSelected
                              ? newEvent.impacts.filter(i => i !== impactOption.value)
                              : [...newEvent.impacts, impactOption.value];
                            setNewEvent({ ...newEvent, impacts: newImpacts });
                          }}
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded mr-2"
                        />
                        <label htmlFor={`impact-${impactOption.value}`} className="ml-2 block text-sm text-gray-700 cursor-pointer flex-1">
                          {impactOption.label}
                        </label>
                      </div>
                    );
                  })}
                </div>
              </div>

              {/* Action buttons */}
              <div className="space-y-3 pt-4">
                <button
                  className="w-full inline-flex justify-center items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
                  disabled={!newEvent.name.trim() || !newEvent.securityPillar || !newEvent.businessValue}
                  onClick={eventBeingEdited ? handleUpdateEvent : handleAddEvent}
                >
                  {eventBeingEdited ? t('dreadedEvents.buttons.updateEvent') : t('dreadedEvents.buttons.addCustomEvent')}
                </button>

                {eventBeingEdited && (
                  <button
                    className="w-full inline-flex justify-center items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                    onClick={() => {
                      setEventBeingEdited(null);
                      setNewEvent({
                        name: '',
                        description: '',
                        securityPillar: newEvent.securityPillar,
                        severity: 'moderate',
                        businessValue: newEvent.businessValue
                      });
                    }}
                  >
                    {t('dreadedEvents.buttons.cancelModification')}
                  </button>
                )}
              </div>
            </div>
          )}
        </Card>
          ) : (
            <div className="bg-white border border-dashed border-gray-300 rounded-lg flex flex-col items-center justify-center p-12 h-full">
              <div className="w-24 h-24 mb-6 rounded-full bg-blue-50 flex items-center justify-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                </svg>
              </div>
              <h3 className="text-xl font-medium text-gray-900 mb-2">{t('dreadedEvents.sections.selectCriteria')}</h3>
              <p className="text-center text-gray-500 max-w-md mb-6">
                {t('dreadedEvents.empty.selectCriteriaDescription')}
              </p>
            </div>
          )}
        </div>
      </div>

      {/* Tableau récapitulatif */}
      <div className="mt-10">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-medium text-gray-900 flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-red-500" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
            </svg>
            {t('dreadedEvents.sections.identifiedEvents')}
          </h3>

          {dreadedEvents.length > 0 && (
            <div className="flex space-x-2">
              <button
                className="inline-flex items-center px-3 py-1.5 border border-gray-300 rounded-md text-xs font-medium bg-white text-gray-700 hover:bg-gray-50"
                onClick={() => {
                  const confirmed = window.confirm("Voulez-vous exporter les événements redoutés au format CSV?");
                  if (confirmed) {
                    alert(t('dreadedEvents.export.toImplement'));
                  }
                }}
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                </svg>
                {t('dreadedEvents.buttons.export')}
              </button>
            </div>
          )}
        </div>


        {dreadedEvents.length === 0 ? (
          <div className="bg-white border border-gray-200 rounded-lg p-8 text-center">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16 mx-auto text-gray-300 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" />
            </svg>
            <h3 className="text-lg font-medium text-gray-900 mb-2">{t('dreadedEvents.empty.noEvents')}</h3>
            <p className="text-gray-500 max-w-md mx-auto">
              {t('dreadedEvents.empty.noEventsDescription')}
            </p>
          </div>
        ) : (
          <div className="bg-white overflow-hidden border border-gray-200 rounded-lg shadow-sm">
            {/* Search and Filter Bar */}
            <div className="p-4 border-b border-gray-200 bg-gray-50">
              <div className="flex flex-wrap items-center gap-4">
                <div className="flex-1 min-w-[240px]">
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <svg className="h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                        <path fillRule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clipRule="evenodd" />
                      </svg>
                    </div>
                    <input
                      type="text"
                      className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                      placeholder={t('dreadedEvents.placeholders.searchEvent')}
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                    />
                  </div>
                </div>



                {searchTerm && (
                  <button
                    onClick={() => {
                      setSearchTerm('');
                    }}
                    className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                  >
                    <X size={16} className="mr-1" />
                    {t('dreadedEvents.buttons.reset')}
                  </button>
                )}
              </div>
            </div>
            {/* Filter Results and Table */}
            {(() => {
              // Define the filtering function
              const filterEvent = (event) => {
                // Search term filter
                const matchesSearch = !searchTerm ||
                  event.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                  (event.description && event.description.toLowerCase().includes(searchTerm.toLowerCase()));

                return matchesSearch;
              };

              // Apply filters
              const filteredEvents = dreadedEvents.filter(filterEvent);

              // Sort the filtered events
              const sortedEvents = [...filteredEvents].sort((a, b) => {
                // Sort by business value first
                const valMetierA = effectiveBusinessValues.find(v => v.id.toString() === a.businessValue)?.name || '';
                const valMetierB = effectiveBusinessValues.find(v => v.id.toString() === b.businessValue)?.name || '';

                if (valMetierA !== valMetierB) {
                  return valMetierA.localeCompare(valMetierB);
                }

                // Then by security pillar (using a fixed order)
                const pilierOrder = {
                  '0': 0, 'confidentialite': 0,  // Confidentialité en premier
                  '4': 1, 'integrite': 1,        // Intégrité en deuxième
                  '1': 2, 'disponibilite': 2,    // Disponibilité en troisième
                  '2': 3, 'tracabilite': 3,      // Traçabilité en quatrième
                  '3': 4, 'preuve': 4,           // Preuve en dernier
                  '5': 5, 'Auditabilite': 5,
                };

                const orderA = pilierOrder[a.securityPillar] !== undefined ? pilierOrder[a.securityPillar] : 99;
                const orderB = pilierOrder[b.securityPillar] !== undefined ? pilierOrder[b.securityPillar] : 99;

                if (orderA !== orderB) {
                  return orderA - orderB;
                }

                // If same pillar, sort by ID to maintain add order
                return a.id - b.id;
              });

              // Store the filtered and sorted events for use in the table
              const filteredAndSortedEvents = sortedEvents;

              // Check if any filters are applied
              const isFiltered = searchTerm;

              // Show filter results count if filters are applied
              const filterCountElement = isFiltered ? (
                <div className="px-4 py-2 bg-blue-50 border-b border-blue-100 flex items-center justify-between">
                  <div>
                    <span className="text-sm text-blue-800">
                      {filteredEvents.length} événement{filteredEvents.length !== 1 ? 's' : ''} affiché{filteredEvents.length !== 1 ? 's' : ''} sur {dreadedEvents.length}
                    </span>
                  </div>
                </div>
              ) : null;

              // Show empty state if no results
              if (filteredEvents.length === 0 && isFiltered) {
                return (
                  <>
                    {filterCountElement}
                    <div className="p-8 text-center">
                      <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-gray-100 text-gray-500 mb-4">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                        </svg>
                      </div>
                      <h3 className="text-lg font-medium text-gray-900 mb-2">Aucun résultat trouvé</h3>
                      <p className="text-gray-500 max-w-md mx-auto mb-4">
                        Aucun événement ne correspond à vos critères de recherche. Essayez de modifier vos filtres.
                      </p>
                      <button
                        onClick={() => {
                          setSearchTerm('');
                        }}
                        className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                      >
                        Réinitialiser les filtres
                      </button>
                    </div>
                  </>
                );
              }

              // Return the filter count and table with filtered events
              return (
                <>
                  {filterCountElement}
                  <div className="overflow-x-auto rounded-lg border border-gray-200 shadow-sm">
                    <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      {t('dreadedEvents.table.headers.businessValue')}
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      {t('dreadedEvents.table.headers.impactedPillar')}
                    </th>
                    <th scope="col" className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                      <div className="flex items-center justify-center">
                        <input
                          type="checkbox"
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                          checked={selectAll}
                          onChange={() => {
                            if (selectAll) {
                              setSelectedEvents([]);
                            } else {
                              setSelectedEvents(filteredAndSortedEvents.map(e => e.id));
                            }
                            setSelectAll(!selectAll);
                          }}
                        />
                      </div>
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      {t('dreadedEvents.table.headers.dreadedEvent')}
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      {t('dreadedEvents.table.headers.impacts')}
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      {t('dreadedEvents.table.headers.severity')}
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      {t('dreadedEvents.table.headers.actions')}
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white">
                  {filteredAndSortedEvents.map((event, index, array) => {
                      // Déterminer si c'est le premier événement d'une valeur métier
                      const isFirstForBusinessValue = index === 0 ||
                        array[index - 1].businessValue !== event.businessValue;

                      // Déterminer si c'est le dernier événement d'une valeur métier
                      const isLastForBusinessValue = index === array.length - 1 ||
                        array[index + 1].businessValue !== event.businessValue;

                      // Déterminer si c'est un changement de valeur métier (pour la ligne précédente)
                      const isPreviousRowLastOfBusinessValue = index > 0 &&
                        array[index - 1].businessValue !== event.businessValue;

                      // Déterminer si c'est le premier événement d'un pilier pour une valeur métier
                      const isFirstForPillar = isFirstForBusinessValue ||
                        (array[index - 1].businessValue === event.businessValue &&
                         array[index - 1].securityPillar !== event.securityPillar);

                      // Déterminer si c'est un changement de pilier (mais pas le premier événement d'une valeur métier)
                      const isPillarChange = !isFirstForBusinessValue && isFirstForPillar;

                      // Compter combien d'événements pour ce pilier et cette valeur métier
                      const pillarEventsCount = array.filter(e =>
                        e.businessValue === event.businessValue &&
                        e.securityPillar === event.securityPillar
                      ).length;

                      // Compter combien d'événements pour cette valeur métier
                      const businessValueEventsCount = array.filter(e =>
                        e.businessValue === event.businessValue
                      ).length;

                      // Get business value and pillar colors for enhanced styling
                      const businessValue = effectiveBusinessValues.find(v => v.id.toString() === event.businessValue);

                      // Normalize security pillar to handle case and accent inconsistencies
                      let normalizedPillar = event.securityPillar;

                      // Handle different variations of auditabilite
                      if (event.securityPillar?.toLowerCase() === 'auditabilite') {
                        normalizedPillar = 'Auditabilite';
                      }

                      // Find the pillar in SECURITY_PILLARS using id instead of value
                      const pillar = SECURITY_PILLARS.find(p => p.id.toLowerCase() === normalizedPillar?.toLowerCase());

                      // Ensure we get the correct colors, especially for Auditabilite which should be red
                      let colors;
                      if (normalizedPillar === 'Auditabilite') {
                        colors = pillarColors['auditabilite'] || { bg: 'bg-red-500', text: 'text-white', border: 'border-red-600', hover: 'hover:bg-red-600' };
                      } else {
                        colors = pillarColors[normalizedPillar] || pillarColors[event.securityPillar] || { bg: 'bg-gray-100', text: 'text-gray-700' };
                      }
                      const acronym = getPillarAcronym(normalizedPillar);
                      const pillarDisplay = pillarLabels[normalizedPillar] || pillarLabels[event.securityPillar] || pillar?.name || normalizedPillar;

                      return (
                        <tr key={event.id} className={`hover:bg-gray-50 transition-colors ${isFirstForBusinessValue ? 'border-t-[1px] border-blue-400 shadow-sm bg-blue-50/30' : ''} ${isLastForBusinessValue ? 'border-b-[1px] border-blue-400' : ''} ${isPillarChange ? 'border-t border-blue-200 bg-blue-50/10' : ''} ${selectedEvents.includes(event.id) ? 'bg-blue-50' : ''}`}>
                          {/* Cellule Valeur métier - fusionnée pour tous les événements de la même valeur */}
                          {isFirstForBusinessValue ? (
                            <td className="px-6 py-4 bg-blue-100 border-b border-blue-200 border-r border-blue-200 shadow-inner" rowSpan={businessValueEventsCount}>
                              <div>
                                <span className="font-bold text-blue-800 block text-base">
                                  {businessValue?.name || '-'}
                                </span>
                                <span className="text-xs text-blue-700 mt-1 block font-medium">
                                  {businessValueEventsCount} {t('dreadedEvents.status.events')}
                                </span>
                              </div>
                            </td>
                          ) : null}

                          {/* Cellule Pilier impacté - fusionnée pour tous les événements du même pilier */}
                          {isFirstForPillar ? (
                            <td className="px-6 py-4 bg-gray-50 border-r border-gray-200" rowSpan={pillarEventsCount}>
                              <div className="flex items-center gap-2">
                                <div className={`w-8 h-8 rounded-full flex items-center justify-center font-bold ${colors.bg} ${colors.text}`}>
                                  {acronym}
                                </div>
                                <span className="text-sm font-medium">
                                  {pillarDisplay}
                                </span>
                              </div>
                            </td>
                          ) : null}

                          {/* Checkbox for bulk selection */}
                          <td className="px-4 py-4 whitespace-nowrap text-center">
                            <div className="flex items-center justify-center">
                              <input
                                type="checkbox"
                                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                checked={selectedEvents.includes(event.id)}
                                onChange={() => {
                                  if (selectedEvents.includes(event.id)) {
                                    setSelectedEvents(selectedEvents.filter(id => id !== event.id));
                                    setSelectAll(false);
                                  } else {
                                    setSelectedEvents([...selectedEvents, event.id]);
                                    if (selectedEvents.length + 1 === filteredAndSortedEvents.length) {
                                      setSelectAll(true);
                                    }
                                  }
                                }}
                              />
                            </div>
                          </td>

                          {/* Événement redouté */}
                          <td className="px-6 py-4">
                            <div>
                              <span className="font-medium text-gray-900">{event.name}</span>
                              {event.description && (
                                <p className="text-sm text-gray-500 mt-1">{event.description}</p>
                              )}
                            </div>
                          </td>

                          {/* Impacts - Multiple */}
                          <td className="px-6 py-4">
                            {/* Handle both old impact field and new impacts array */}
                            {(() => {
                              const impactArray = event.impacts || (event.impact ? [event.impact] : []);

                              if (impactArray.length === 0) {
                                return <span className="text-gray-400 text-sm">{t('dreadedEvents.status.notSpecified')}</span>;
                              }

                              return (
                                <div className="flex flex-wrap gap-1">
                                  {impactArray.map(impactValue => {
                                    const impactLabel = translatedDreadedEventImpacts.find(imp => imp.value === impactValue)?.label || impactValue;
                                    return (
                                      <span
                                        key={impactValue}
                                        className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-blue-50 text-blue-800 border border-blue-200"
                                      >
                                        {impactLabel}
                                      </span>
                                    );
                                  })}
                                </div>
                              );
                            })()}
                          </td>

                          {/* Gravité */}
                          <td className="px-6 py-4 whitespace-nowrap">
                            <SeverityBadge severity={event.severity} />
                          </td>

                          {/* Actions */}
                          <td className="px-6 py-4 whitespace-nowrap text-sm">
                            <div className="flex space-x-3">
                              <button
                                className="text-blue-600 hover:text-blue-900 flex items-center"
                                onClick={() => handleEditEvent(event)}
                              >
                                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                                </svg>
                                {t('dreadedEvents.buttons.edit')}
                              </button>
                              <button
                                className="text-red-600 hover:text-red-900 flex items-center"
                                onClick={() => handleRemoveEvent(event.id)}
                              >
                                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                </svg>
                                {t('dreadedEvents.buttons.delete')}
                              </button>
                            </div>
                          </td>
                        </tr>
                      );
                    })}
                </tbody>
                    </table>
                  </div>
                </>
              );
            })()}

            <div className="bg-gray-50 px-6 py-4 border-t border-gray-200">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-4">
                  <div className="text-sm text-gray-500">
                    {dreadedEvents.length} {t('dreadedEvents.status.events')}
                  </div>

                  {selectedEvents.length > 0 && (
                    <div className="flex items-center gap-2">
                      <span className="text-sm font-medium text-blue-600">
                        {selectedEvents.length} {t('dreadedEvents.status.selected')}
                      </span>
                      <div className="border-l border-gray-300 h-5"></div>
                      <div className="flex gap-2">
                        <button
                          className="inline-flex items-center px-2 py-1 text-xs font-medium text-red-700 bg-red-100 rounded-md hover:bg-red-200"
                          onClick={() => {
                            if (window.confirm(t('dreadedEvents.status.bulkDeleteConfirm', { count: selectedEvents.length, plural: selectedEvents.length > 1 ? 's' : '' }))) {
                              // Remove selected events
                              const updatedEvents = dreadedEvents.filter(event => !selectedEvents.includes(event.id));
                              setDreadedEvents(updatedEvents);
                              setSelectedEvents([]);
                              setSelectAll(false);
                              const plural = selectedEvents.length > 1 ? 's' : '';
                              showSuccessToast(t('dreadedEvents.status.bulkDeleteSuccess', { count: selectedEvents.length, plural }));
                            }
                          }}
                        >
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-3.5 w-3.5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                          </svg>
                          {t('dreadedEvents.buttons.delete')}
                        </button>
                        <button
                          className="inline-flex items-center px-2 py-1 text-xs font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200"
                          onClick={() => setSelectedEvents([])}
                        >
                          <X size={14} className="mr-1" />
                          {t('dreadedEvents.buttons.cancel')}
                        </button>
                      </div>
                    </div>
                  )}
                </div>

                <div className="inline-flex gap-3">
                  <button
                    className="inline-flex items-center px-3 py-1.5 border border-gray-300 rounded-md text-xs font-medium bg-white text-gray-700 hover:bg-gray-50"
                    onClick={() => {/* Fonctionnalité à implémenter */}}
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                    </svg>
                    {t('dreadedEvents.buttons.export')}
                  </button>

                  <button
                    className="inline-flex items-center px-3 py-1.5 border border-gray-300 rounded-md text-xs font-medium bg-white text-gray-700 hover:bg-gray-50"
                    onClick={handleEnhancedSave}
                    disabled={isSaving}
                  >
                    {isSaving ? (
                      <>
                        <RefreshCw size={14} className="mr-1 animate-spin" />
                        {t('dreadedEvents.buttons.saving')}
                      </>
                    ) : (
                      <>
                        <Save size={14} className="mr-1" />
                        {t('dreadedEvents.buttons.save')}
                      </>
                    )}
                  </button>
              </div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* AI Suggestions Modal - Pass the new handler */}
      <AiSuggestionsModal
        isOpen={showAiSuggestionsModal}
        onClose={() => setShowAiSuggestionsModal(false)}
        suggestions={aiSuggestions}
        isLoading={isFetchingAiSuggestions}
        error={aiSuggestionsError}
        onAddSelected={handleAddSelectedAiSuggestions}
        businessValueName={effectiveBusinessValues.find(bv => bv.id.toString() === newEvent.businessValue)?.name || t('dreadedEvents.status.undefined')}
        pillarLabel={translatedPillarLabels[newEvent.securityPillar] || newEvent.securityPillar}
        onFetchMore={handleFetchMoreAiSuggestions}
      />

      {/* Guide Modal */}
      <GuideModal isOpen={isGuideOpen} onClose={() => setIsGuideOpen(false)} />
    </div>
  );
};

export default DreadedEvents;