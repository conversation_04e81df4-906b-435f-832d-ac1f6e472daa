// Test utility to verify translations are working correctly
import i18n from '../i18n';
import {
  getTranslatedSeverityOptions,
  getTranslatedDreadedEventImpacts,
  getTranslatedSecurityPillars,
  getTranslatedSeverityLabel,
  getTranslatedImpactLabels
} from './translatedConstants';

// Test function to verify translations
export const testTranslations = () => {
  console.log('Testing translations...');

  // Test French translations
  i18n.changeLanguage('fr');
  console.log('French translations:');
  console.log('Severity options:', getTranslatedSeverityOptions());
  console.log('Impact categories:', getTranslatedDreadedEventImpacts());
  console.log('Security pillars:', getTranslatedSecurityPillars());
  console.log('Critical severity:', getTranslatedSeverityLabel('critical'));
  console.log('Financial impact:', getTranslatedImpactLabels(['financier']));

  // Test English translations
  i18n.changeLanguage('en');
  console.log('\nEnglish translations:');
  console.log('Severity options:', getTranslatedSeverityOptions());
  console.log('Impact categories:', getTranslatedDreadedEventImpacts());
  console.log('Security pillars:', getTranslatedSecurityPillars());
  console.log('Critical severity:', getTranslatedSeverityLabel('critical'));
  console.log('Financial impact:', getTranslatedImpactLabels(['financier']));

  console.log('Translation test completed!');
};

// Export for use in development
if (process.env.NODE_ENV === 'development') {
  window.testTranslations = testTranslations;
}
