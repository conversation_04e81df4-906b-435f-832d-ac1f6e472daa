// src/App.js - Updated to automatically redirect based on user role
import React from "react";
import { BrowserRouter, Routes, Route, Navigate } from "react-router-dom";
import { AuthProvider } from "./context/AuthContext";
import { AnalysisProvider } from "./context/AnalysisContext"; // Import AnalysisProvider
import { LanguageProvider } from "./context/LanguageContext"; // Import LanguageProvider
import './i18n'; // Initialize i18n
import PrivateRoute from "./components/common/PrivateRoute";
import AccessDenied from "./components/common/AccessDenied";
import LoginForm from "./components/auth/LoginForm";
// Token refresh notification removed

// Layouts
import AdminLayout from "./components/layout/AdminLayout";
import ProviderLayout from "./components/layout/ProviderLayout";

// Pages SuperAdmin
import SuperAdminDashboard from "./pages/admin/SuperAdminDashboard";
import CompanyManagement from "./pages/admin/CompanyManagement";
import UserManagement from "./pages/admin/UserManagement";
import ActivityLogs from "./pages/admin/ActivityLogs";

// Pages Provider
import ProviderDashboard from "./pages/provider/ProviderDashboard";
import ProviderUserManagement from "./pages/provider/UserManagement";
import AnalysisManagement from "./pages/provider/AnalysisManagement";
import CompanyActivities from "./pages/provider/CompanyActivities";

// Application principale existante
import EbiosRMApp from "./EbiosRMApp";
import AuthWrapper from "./AuthWrapper";
import { useAuth } from "./context/AuthContext";

// New component to handle role-based redirects
const RoleBasedRedirect = () => {
  const { user } = useAuth();

  // If user has superadmin role, redirect to superadmin dashboard
  if (user && user.role === 'superadmin') {
    return <Navigate to="/superadmin/dashboard" replace />;
  }

  // If user has admin (provider) role, redirect to provider dashboard
  if (user && user.role === 'admin') {
    return <Navigate to="/admin/dashboard" replace />;
  }

  // If user has simpleuser (analyst) role, redirect to the application
  if (user && user.role === 'simpleuser') {
    return <Navigate to="/app" replace />;
  }

  // If user role is not recognized or user is not authenticated
  return <Navigate to="/login" replace />;
};

const App = () => {
  return (
    <AuthProvider>
      <LanguageProvider>
        <BrowserRouter>
        {/* Token refresh notification removed */}
        <Routes>
          {/* Route de connexion accessible à tous */}
          <Route path="/login" element={<LoginForm />} />

          {/* Page d'accès refusé */}
          <Route path="/access-denied" element={<AccessDenied />} />

          {/* Home route - redirects based on role */}
          <Route
            path="/"
            element={
              <PrivateRoute>
                <RoleBasedRedirect />
              </PrivateRoute>
            }
          />

          {/* Routes Super Admin - accessibles uniquement par les superadmins */}
          <Route
            path="/superadmin/dashboard"
            element={
              <PrivateRoute roles="superadmin">
                <AdminLayout>
                  <SuperAdminDashboard />
                </AdminLayout>
              </PrivateRoute>
            }
          />
          <Route
            path="/superadmin/companies"
            element={
              <PrivateRoute roles="superadmin">
                <AdminLayout>
                  <CompanyManagement />
                </AdminLayout>
              </PrivateRoute>
            }
          />
          <Route
            path="/superadmin/users"
            element={
              <PrivateRoute roles="superadmin">
                <AdminLayout>
                  <UserManagement />
                </AdminLayout>
              </PrivateRoute>
            }
          />
          <Route
            path="/superadmin/activities"
            element={
              <PrivateRoute roles="superadmin">
                <AdminLayout>
                  <ActivityLogs />
                </AdminLayout>
              </PrivateRoute>
            }
          />

          {/* Routes Provider - accessibles uniquement par les providers (admin) */}
          <Route
            path="/admin/dashboard"
            element={
              <PrivateRoute roles="admin">
                <ProviderLayout>
                  <ProviderDashboard />
                </ProviderLayout>
              </PrivateRoute>
            }
          />
          <Route
            path="/admin/users"
            element={
              <PrivateRoute roles="admin">
                <ProviderLayout>
                  <ProviderUserManagement />
                </ProviderLayout>
              </PrivateRoute>
            }
          />
          <Route
            path="/admin/analyses"
            element={
              <PrivateRoute roles="admin">
                <ProviderLayout>
                  <AnalysisManagement />
                </ProviderLayout>
              </PrivateRoute>
            }
          />
          <Route
            path="/admin/activities"
            element={
              <PrivateRoute roles="admin">
                <ProviderLayout>
                  <CompanyActivities />
                </ProviderLayout>
              </PrivateRoute>
            }
          />

          {/* Application principale - accessible par tous utilisateurs authentifiés */}
          <Route
            path="/app"
            element={
              <PrivateRoute roles={['superadmin', 'admin', 'simpleuser']}>
                <AnalysisProvider> {/* Wrap with AnalysisProvider */}
                  <AuthWrapper>
                    <EbiosRMApp />
                  </AuthWrapper>
                </AnalysisProvider>
              </PrivateRoute>
            }
          />

          {/* Route par défaut - redirection vers la page d'accueil */}
          <Route path="*" element={<Navigate to="/" replace />} />
        </Routes>
        </BrowserRouter>
      </LanguageProvider>
    </AuthProvider>
  );
};

export default App;