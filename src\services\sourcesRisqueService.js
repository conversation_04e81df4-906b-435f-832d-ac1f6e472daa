import apiClient from '../api/apiClient';

/**
 * Get AI-generated source de risque suggestions
 * @param {Object} data - The data to send to the AI
 * @param {string} data.analysisId - The ID of the analysis
 * @param {string} data.analysisName - The name of the analysis
 * @param {string} data.analysisDescription - The description of the analysis
 * @param {string} data.organizationContext - The context of the organization
 * @param {Array} data.threatCategories - The categories of threats to consider
 * @param {Array} data.existingSourceNames - The names of existing sources
 * @param {boolean} data.requestMore - Whether to request more suggestions
 * @param {Array} data.businessValues - The business values of the organization
 * @param {Array} data.businessAssets - The business assets of the organization
 * @returns {Promise<Object>} - The AI suggestions
 */
export const getAiSourcesRisqueSuggestions = async (data) => {
  try {
    // Add a timestamp to ensure we get fresh suggestions when requesting more
    const requestData = {
      ...data,
      timestamp: Date.now(),
      requestMore: data.requestMore || false
    };

    // The apiClient now handles circular references, so we can pass the data directly

    // Fix: Remove the duplicate '/api/' prefix since apiClient already adds it
    const response = await apiClient.post('/ai/suggest-sources-risque', requestData);
    console.log('AI suggestion response:', response);

    // If successful, save to localStorage for persistence
    if (response.success && response.data?.suggestions && data.analysisId) {
      try {
        // Get existing saved suggestions
        const savedSuggestions = JSON.parse(localStorage.getItem(`aiSuggestions_${data.analysisId}`) || '[]');

        // Add new suggestions if not already saved
        const allSuggestions = [...savedSuggestions];
        response.data.suggestions.forEach(suggestion => {
          if (!savedSuggestions.some(s => s.name === suggestion.name)) {
            allSuggestions.push(suggestion);
          }
        });

        // Save back to localStorage
        localStorage.setItem(`aiSuggestions_${data.analysisId}`, JSON.stringify(allSuggestions));
      } catch (error) {
        console.error('Error saving suggestions to localStorage:', error);
      }
    }

    return response;
  } catch (error) {
    console.error('Error getting AI sources de risque suggestions:', error);
    throw error;
  }
};

/**
 * Save a source de risque to the database
 * @param {Object} source - The source de risque to save
 * @param {string} analysisId - The ID of the analysis
 * @returns {Promise<Object>} - The saved source de risque
 */
export const saveSourceRisque = async (source, analysisId) => {
  try {
    // Format the data according to the expected structure
    const formattedData = {
      analysisId,
      componentType: 'sources-risque',
      data: {
        sources: [source]
      }
    };

    const response = await apiClient.post('/api/components', formattedData);
    return response.data;
  } catch (error) {
    console.error('Error saving source de risque:', error);
    throw error;
  }
};

/**
 * Update a source de risque in the database
 * @param {Object} source - The source de risque to update
 * @param {string} analysisId - The ID of the analysis
 * @param {string} componentId - The ID of the component
 * @returns {Promise<Object>} - The updated source de risque
 */
export const updateSourceRisque = async (source, analysisId, componentId) => {
  try {
    // Get the current component first
    const currentComponent = await apiClient.get(`/api/components/${componentId}`);

    // Find the index of the source to update
    const sources = currentComponent.data.data.sources || [];
    const sourceIndex = sources.findIndex(s => s.id === source.id);

    // Update the source if found, otherwise add it
    if (sourceIndex >= 0) {
      sources[sourceIndex] = source;
    } else {
      sources.push(source);
    }

    // Format the data according to the expected structure
    const formattedData = {
      analysisId,
      componentType: 'sources-risque',
      data: {
        sources
      }
    };

    const response = await apiClient.put(`/api/components/${componentId}`, formattedData);
    return response.data;
  } catch (error) {
    console.error('Error updating source de risque:', error);
    throw error;
  }
};
