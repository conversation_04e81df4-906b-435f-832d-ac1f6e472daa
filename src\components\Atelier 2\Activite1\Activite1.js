// src/components/Atelier 2/Activite1/Activite1.js
import React, { useState, useEffect } from 'react';
import { AlertCircle, Info, Sparkles, RefreshCw, X, Check, Save } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import ThreatCategories, { THREAT_CATEGORIES } from './ThreatCategories';
import CategorySourcesTable from './CategorySourcesTable';
import GuideModal from './GuideModal';
import { useAnalysis } from '../../../context/AnalysisContext';
import { getAiSourcesRisqueSuggestions } from '../../../services/sourcesRisqueService';
import { showSuccessToast, showErrorToast, showLoadingToast, updateToast } from '../../../utils/toastUtils';

const Activite1 = () => {
  const { t } = useTranslation();
  const [sourcesRisque, setSourcesRisque] = useState([]);
  const [selectedCategories, setSelectedCategories] = useState([]);
  const [isGuideOpen, setIsGuideOpen] = useState(false);
  const [isAiModalOpen, setIsAiModalOpen] = useState(false);
  const [aiSuggestions, setAiSuggestions] = useState([]);
  const [isLoadingAi, setIsLoadingAi] = useState(false);
  const [selectedAiSuggestions, setSelectedAiSuggestions] = useState([]);
  const [requestMore, setRequestMore] = useState(false);

  const { currentAnalysis, getSourcesDeRisque, saveSourcesDeRisque, currentBusinessValues } = useAnalysis();

  // Load sources de risque on component mount and when they change
  useEffect(() => {
    const fetchData = async () => {
      if (currentAnalysis?.id) {
        try {
          console.log("Fetching sources de risque for analysis:", currentAnalysis.id);

          // Use the API client to make the request
          const sourcesData = await getSourcesDeRisque(currentAnalysis.id);

          console.log("Received sources de risque data:", sourcesData);

          // If we have data from the context, use it
          if (sourcesData && Array.isArray(sourcesData)) {
            console.log("Setting sources de risque state with", sourcesData.length, "items");
            // Update sources de risque state
            setSourcesRisque(sourcesData);

            // Just use whatever data we got from the API
            console.log("Using sources data from API");

            // Extract unique categories from sources de risque
            const uniqueCategories = [...new Set(sourcesData
              .filter(source => source.category) // Filter out sources without category
              .map(source => source.category))];

            // Convert to the format expected by the component
            const formattedCategories = uniqueCategories.map(categoryId => {
              // Find the category in THREAT_CATEGORIES if it exists
              const predefinedCategory = THREAT_CATEGORIES.find(cat => cat.id === categoryId);

              if (predefinedCategory) {
                return predefinedCategory.id;
              }

              return categoryId;
            });

            // Set selected categories based on sources de risque
            setSelectedCategories(formattedCategories);
          }
        } catch (error) {
          console.error('Error loading data:', error);
        }
      }
    };

    fetchData();
  }, [currentAnalysis?.id, getSourcesDeRisque]);

  // Update local state when sources de risque change
  useEffect(() => {
    if (sourcesRisque && Array.isArray(sourcesRisque) && sourcesRisque.length > 0) {
      // Extract unique categories from sources de risque
      const uniqueCategories = [...new Set(sourcesRisque
        .filter(source => source.category) // Filter out sources without category
        .map(source => source.category))];

      // Convert to the format expected by the component
      const formattedCategories = uniqueCategories.map(categoryId => {
        // Find the category in THREAT_CATEGORIES if it exists
        const predefinedCategory = THREAT_CATEGORIES.find(cat => cat.id === categoryId);

        if (predefinedCategory) {
          return predefinedCategory.id;
        }

        return categoryId;
      });

      // Set selected categories based on sources de risque
      setSelectedCategories(formattedCategories);
    }
  }, [sourcesRisque]);

  // Handle AI suggestions
  const handleGetAiSuggestions = async (isRequestingMore = false) => {
    // Update the requestMore state
    setRequestMore(isRequestingMore);
    if (!currentAnalysis?.id) {
      showErrorToast(t('workshop2.activity1.errors.noAnalysisSelected'));
      return;
    }

    setIsLoadingAi(true);
    setIsAiModalOpen(true);

    // Always get fresh AI suggestions, don't use localStorage
    // This ensures we always get real AI suggestions

    const toastId = showLoadingToast(isRequestingMore ? t('workshop2.activity1.ai.generatingMoreSuggestions') : t('workshop2.activity1.ai.generatingSuggestions'));

    try {
      // Get existing source names to avoid duplicates
      const existingSourceNames = sourcesRisque.map(source => source.name);

      // Also add current suggestion names to avoid duplicates when requesting more
      if (isRequestingMore && aiSuggestions.length > 0) {
        existingSourceNames.push(...aiSuggestions.map(s => s.name));
      }

      // Get threat categories
      const threatCategories = selectedCategories.map(categoryId => {
        const category = THREAT_CATEGORIES.find(c => c.id === categoryId);
        return category ? category.id : null;
      }).filter(Boolean);

      // Extract business assets from all business values
      const businessAssets = [];
      if (currentBusinessValues && currentBusinessValues.length > 0) {
        currentBusinessValues.forEach(bv => {
          if (bv.supportAssets && bv.supportAssets.length > 0) {
            businessAssets.push(...bv.supportAssets);
          }
        });
      }

      try {
        // Create a clean context object without any circular references
        // Only include primitive values and simple objects
        const cleanBusinessValues = currentBusinessValues ?
          currentBusinessValues.map(bv => ({
            id: bv.id,
            name: bv.name,
            description: bv.description,
            // Only include simple properties, not complex objects or DOM elements
          })) : [];

        const cleanBusinessAssets = businessAssets ?
          businessAssets.map(asset => ({
            id: asset.id,
            name: asset.name,
            description: asset.description,
            // Only include simple properties
          })) : [];

        // Call AI API with clean context data
        const response = await getAiSourcesRisqueSuggestions({
          analysisId: currentAnalysis.id,
          analysisName: currentAnalysis.name,
          analysisDescription: currentAnalysis.description || '',
          organizationContext: currentAnalysis.organizationContext || '',
          threatCategories,
          existingSourceNames,
          businessValues: cleanBusinessValues,
          businessAssets: cleanBusinessAssets,
          requestMore: isRequestingMore // Add flag to indicate requesting more suggestions
        });

        if (response.success && response.data.suggestions) {
          // If requesting more, append to existing suggestions
          if (isRequestingMore) {
            const newSuggestions = response.data.suggestions;
            setAiSuggestions(prev => [...prev, ...newSuggestions]);
            // Don't auto-select new suggestions
          } else {
            // Otherwise, replace existing suggestions
            const newSuggestions = response.data.suggestions;
            setAiSuggestions(newSuggestions);
            // Clear any previous selections
            setSelectedAiSuggestions([]);
          }
          updateToast(toastId, t('workshop2.activity1.ai.suggestionsGenerated'), 'success');
        } else {
          throw new Error(t('workshop2.activity1.errors.invalidApiResponse'));
        }
      } catch (apiError) {
        console.error('API Error:', apiError);
        // Show error toast and close modal
        updateToast(toastId, t('workshop2.activity1.errors.aiGenerationError'), 'error');
        setIsAiModalOpen(false);
        setIsLoadingAi(false);
        return;
      }
    } catch (error) {
      console.error('Error getting AI suggestions:', error);
      updateToast(toastId, t('workshop2.activity1.errors.aiGenerationError'), 'error');
      if (!isRequestingMore) {
        setIsAiModalOpen(false);
      }
    } finally {
      setIsLoadingAi(false);
    }
  };

  // Handle requesting more suggestions
  const handleGetMoreSuggestions = () => {
    handleGetAiSuggestions(true);
  };

  // Handle selecting/deselecting AI suggestions
  const handleToggleAiSuggestion = (suggestion) => {
    setSelectedAiSuggestions(prev => {
      const isSelected = prev.some(s => s.name === suggestion.name);
      if (isSelected) {
        return prev.filter(s => s.name !== suggestion.name);
      } else {
        return [...prev, suggestion];
      }
    });
  };

  // Handle adding selected AI suggestions
  const handleAddAiSuggestions = () => {
    if (selectedAiSuggestions.length === 0) {
      showErrorToast(t('workshop2.activity1.errors.noSuggestionSelected'));
      return;
    }

    // Format suggestions to match source de risque structure
    const newSources = selectedAiSuggestions.map(suggestion => ({
      ...suggestion,
      id: `source-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
      createdAt: new Date().toISOString(),
      // Map objectifViseCategory to objectifVise if needed
      objectifVise: suggestion.objectifVise || suggestion.objectifViseCategory || 'confidentialite',
      // Map category to match the expected format
      category: suggestion.category || ''
    }));

    // Add to sources
    setSourcesRisque(prev => [...prev, ...newSources]);

    // Clear only the selected suggestions, but keep the modal open and keep the suggestions
    setSelectedAiSuggestions([]);

    // Clear previous selections

    // Save suggestions to localStorage to persist them
    try {
      // Get existing saved suggestions
      const savedSuggestions = JSON.parse(localStorage.getItem(`aiSuggestions_${currentAnalysis?.id}`) || '[]');

      // Add current suggestions if not already saved
      const allSuggestions = [...savedSuggestions];
      aiSuggestions.forEach(suggestion => {
        if (!savedSuggestions.some(s => s.name === suggestion.name)) {
          allSuggestions.push(suggestion);
        }
      });

      // Save back to localStorage
      localStorage.setItem(`aiSuggestions_${currentAnalysis?.id}`, JSON.stringify(allSuggestions));
    } catch (error) {
      console.error('Error saving suggestions to localStorage:', error);
    }

    showSuccessToast(t('workshop2.activity1.success.sourcesAdded', { count: newSources.length }));
  };

  // Helper functions for styling
  const getObjectifBadgeColor = (type) => {
    switch (type) {
      case 'confidentialite': return 'bg-blue-100 text-blue-800';
      case 'integrite': return 'bg-green-100 text-green-800';
      case 'disponibilite': return 'bg-purple-100 text-purple-800';
      case 'auditabilite': return 'bg-red-100 text-red-800';
      case 'espionnage': return 'bg-blue-100 text-blue-800';
      case 'prepositionnement': return 'bg-indigo-100 text-indigo-800';
      case 'influence': return 'bg-yellow-100 text-yellow-800';
      case 'entrave': return 'bg-orange-100 text-orange-800';
      case 'lucratif': return 'bg-green-100 text-green-800';
      case 'defi': return 'bg-purple-100 text-purple-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getMotivationColor = (level) => {
    switch (level) {
      case 'faible': return 'bg-green-100 text-green-800';
      case 'moyen': return 'bg-yellow-100 text-yellow-800';
      case 'eleve': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getResourcesColor = (level) => {
    switch (level) {
      case 'faibles': return 'bg-green-100 text-green-800';
      case 'moyennes': return 'bg-yellow-100 text-yellow-800';
      case 'importantes': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white rounded-xl shadow-sm border border-slate-100 p-4 mb-6">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          {/* Left Side: Breadcrumb & Title */}
          <div>
            <div className="flex items-center text-sm text-slate-500 mb-2">
              <span className="hover:text-blue-600 transition-colors">{t('workshop2.title')}</span>
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mx-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
              <span className="text-blue-600 font-medium">{t('workshop2.activity1.breadcrumb')}</span>
            </div>
            <h1 className="text-2xl sm:text-3xl font-bold text-slate-800 flex items-center">
              <AlertCircle size={28} className="mr-3 text-blue-600" />
              {t('workshop2.activity1.title')}
            </h1>
          </div>

          {/* Right Side: Buttons */}
          <div className="flex items-center gap-2 flex-wrap">
            {/* Save Button */}
            <button
              onClick={() => {
                // First, add any selected AI suggestions to the sources list
                if (selectedAiSuggestions && selectedAiSuggestions.length > 0) {
                  // Format suggestions to match source de risque structure
                  const newSources = selectedAiSuggestions.map(suggestion => ({
                    ...suggestion,
                    id: `source-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
                    createdAt: new Date().toISOString(),
                    // Map objectifViseCategory to objectifVise if needed
                    objectifVise: suggestion.objectifVise || suggestion.objectifViseCategory || 'confidentialite',
                    // Map category to match the expected format
                    category: suggestion.category || ''
                  }));

                  // Add to sources
                  setSourcesRisque(prev => [...prev, ...newSources]);

                  // Clear selected suggestions
                  setSelectedAiSuggestions([]);

                  showSuccessToast(t('workshop2.activity1.success.sourcesAddedBeforeSave', { count: newSources.length }));
                }

                // Then trigger the save event
                if (window.dispatchEvent) {
                  console.log("Dispatching save-sources-risque event");
                  // Create a custom event to trigger save in SourcesRisques component
                  window.dispatchEvent(new CustomEvent('save-sources-risque'));
                  // Removed toast notification as it will be shown by the handler
                } else {
                  console.error("window.dispatchEvent is not available");
                }
              }}
              className="text-sm font-medium bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 flex items-center shadow-sm transition duration-200"
            >
              <Save size={16} className="mr-2" />
              {t('common.save')}
            </button>

            {/* AI Button */}
            <button
              onClick={handleGetAiSuggestions}
              disabled={isLoadingAi || !currentAnalysis?.id}
              className="text-sm font-medium text-white bg-gradient-to-r from-purple-500 to-indigo-600 hover:from-purple-600 hover:to-indigo-700 px-4 py-2 rounded-md flex items-center shadow-sm transition-all duration-200 transform hover:scale-105 hover:shadow focus:outline-none focus:ring-2 focus:ring-offset-1 focus:ring-indigo-500 disabled:opacity-60 disabled:cursor-not-allowed disabled:hover:scale-100 disabled:hover:shadow-sm"
            >
              <Sparkles size={16} className="mr-1.5 text-yellow-100" />
              {isLoadingAi ? (
                <span className="flex items-center">
                  <RefreshCw size={14} className="mr-1.5 animate-spin" />
                  {t('workshop2.activity1.ai.generating')}
                </span>
              ) : (
                t('workshop2.activity1.ai.suggestions')
              )}
            </button>

            {/* Help Button */}
            <button
              className="text-sm font-medium bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 flex items-center shadow-sm transition duration-200"
              onClick={() => setIsGuideOpen(true)}
            >
              <Info size={16} className="mr-2" />
              {t('common.help')}
            </button>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden p-6">
        <div className="space-y-10">
          <ThreatCategories
            externalSelectedCategories={selectedCategories}
            setExternalSelectedCategories={setSelectedCategories}
          />

          {selectedCategories.length > 0 && (
            <div className="mt-8">
              <h2 className="text-xl font-bold text-gray-800 mb-4">{t('workshop2.activity1.riskSourcesByCategory')}</h2>
              <CategorySourcesTable
                selectedCategories={selectedCategories}
                sourcesRisque={sourcesRisque}
                setSourcesRisque={setSourcesRisque}
                onRequestAiSuggestions={handleGetAiSuggestions}
                isLoadingAi={isLoadingAi}
                analysisData={currentAnalysis}
              />
            </div>
          )}
        </div>
      </div>

      {/* Guide Modal */}
      <GuideModal isOpen={isGuideOpen} onClose={() => setIsGuideOpen(false)} />

      {/* AI Suggestions Modal */}
      {isAiModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] flex flex-col">
            <div className="p-5 border-b flex justify-between items-center bg-gradient-to-r from-purple-600 to-indigo-600 text-white rounded-t-lg">
              <h3 className="text-xl font-bold flex items-center">
                <Sparkles size={22} className="mr-3 text-yellow-300" />
                {t('workshop2.activity1.ai.modalTitle')}
                {isLoadingAi && requestMore ? (
                  <div className="ml-3 flex items-center bg-purple-700 bg-opacity-50 px-3 py-1 rounded-full">
                    <RefreshCw size={14} className="text-white animate-spin mr-2" />
                    <span className="text-xs font-medium">{t('common.loading')}</span>
                  </div>
                ) : null}
              </h3>
              <button
                onClick={() => setIsAiModalOpen(false)}
                className="text-white hover:text-gray-200 transition-colors"
              >
                <X size={22} />
              </button>
            </div>

            <div className="p-4 overflow-auto flex-grow">
              {isLoadingAi ? (
                <div className="flex flex-col items-center justify-center h-64">
                  <div className="relative">
                    <RefreshCw size={50} className="text-purple-500 animate-spin mb-4" />
                    <Sparkles size={20} className="text-yellow-400 absolute top-0 right-0" />
                  </div>
                  <p className="text-gray-700 font-medium text-lg mb-2">{t('workshop2.activity1.ai.generatingInProgress')}</p>
                  <p className="text-gray-500 text-sm">{t('workshop2.activity1.ai.analyzingContext')}</p>
                </div>
              ) : aiSuggestions.length === 0 ? (
                <div className="flex flex-col items-center justify-center h-64">
                  <AlertCircle size={40} className="text-gray-400 mb-4" />
                  <p className="text-gray-600">{t('workshop2.activity1.ai.noSuggestions')}</p>
                  <p className="text-sm text-gray-500 mt-2">{t('workshop2.activity1.ai.createManually')}</p>
                  <div className="mt-4">
                    <button
                      onClick={() => {
                        // Create a manual source de risque
                        const manualSource = {
                          id: `source-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
                          name: "Nouvelle source de risque",
                          description: "Description de la source de risque",
                          category: "criminel",
                          objectifVise: "Espionnage industriel",
                          objectifViseCategory: "espionnage",
                          motivation: "moyen",
                          activite: "moyen",
                          ressources: "moyennes",
                          createdAt: new Date().toISOString()
                        };
                        setAiSuggestions([manualSource]);
                        setSelectedAiSuggestions([manualSource]);
                      }}
                      className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                    >
                      {t('workshop2.activity1.ai.createManuallyButton')}
                    </button>
                  </div>
                </div>
              ) : (
                <div className="space-y-4">
                  <div className="mt-2">
                    <h4 className="font-medium text-gray-800 mb-2">{t('workshop2.activity1.ai.suggestionsTitle')}</h4>
                    <p className="text-sm text-gray-600 mb-4">
                      {t('workshop2.activity1.ai.selectSuggestions')}
                      {selectedAiSuggestions.length > 0 && (
                        <span className="ml-2 font-medium text-purple-600">
                          {t('workshop2.activity1.ai.selectedCount', { count: selectedAiSuggestions.length })}
                        </span>
                      )}
                    </p>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      {aiSuggestions.map((suggestion, index) => {
                        const isSelected = selectedAiSuggestions.some(s => s.name === suggestion.name);
                        return (
                          <div
                            key={index}
                            className={`p-5 rounded-lg shadow-sm hover:shadow-md ${isSelected ? 'border-2 border-purple-500 bg-purple-50' : 'border border-gray-200 hover:border-purple-300'} cursor-pointer transition-all duration-200`}
                            onClick={() => handleToggleAiSuggestion(suggestion)}
                          >
                            <div className="flex justify-between items-start">
                              <div className="flex-1">
                                <div className="flex flex-wrap items-center gap-2 mb-2">
                                  <h4 className="font-semibold text-gray-800 text-lg">{suggestion.name}</h4>
                                  <span className={`px-2.5 py-1 rounded-full text-xs font-medium ${getObjectifBadgeColor(suggestion.objectifVise || suggestion.objectifViseCategory || 'confidentialite')}`}>
                                    {suggestion.objectifVise || suggestion.objectifViseCategory || 'Confidentialité'}
                                  </span>
                                  <span className="px-2.5 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                    {suggestion.category || t('workshop2.activity1.ai.uncategorized')}
                                  </span>
                                </div>
                                <p className="text-sm text-gray-600 mb-3">{suggestion.description}</p>
                              </div>
                              <div className={`w-6 h-6 rounded-full border-2 flex-shrink-0 ${isSelected ? 'bg-purple-500 border-purple-500' : 'border-gray-300'}`}>
                                {isSelected && <Check size={18} className="text-white m-auto" />}
                              </div>
                            </div>

                            <div className="mt-3 flex flex-wrap gap-3">
                              <div className="flex items-center bg-gray-50 px-3 py-1.5 rounded-md">
                                <span className="text-xs font-semibold text-gray-600 mr-2">{t('workshop2.activity1.ai.motivation')}:</span>
                                <span className={`inline-block px-2 py-0.5 rounded-full text-xs font-medium ${getMotivationColor(suggestion.motivation || 'faible')}`}>
                                  {suggestion.motivation === 'faible' ? t('workshop2.activity1.levels.low') :
                                   suggestion.motivation === 'moyen' ? t('workshop2.activity1.levels.medium') :
                                   suggestion.motivation === 'eleve' ? t('workshop2.activity1.levels.high') :
                                   suggestion.motivation || t('workshop2.activity1.levels.low')}
                                </span>
                              </div>
                              <div className="flex items-center bg-gray-50 px-3 py-1.5 rounded-md">
                                <span className="text-xs font-semibold text-gray-600 mr-2">{t('workshop2.activity1.ai.resources')}:</span>
                                <span className={`inline-block px-2 py-0.5 rounded-full text-xs font-medium ${getResourcesColor(suggestion.ressources || 'faibles')}`}>
                                  {suggestion.ressources === 'faibles' ? t('workshop2.activity1.levels.low') :
                                   suggestion.ressources === 'moyennes' ? t('workshop2.activity1.levels.medium') :
                                   suggestion.ressources === 'importantes' ? t('workshop2.activity1.levels.high') :
                                   suggestion.ressources || t('workshop2.activity1.levels.low')}
                                </span>
                              </div>
                              <div className="flex items-center bg-gray-50 px-3 py-1.5 rounded-md">
                                <span className="text-xs font-semibold text-gray-600 mr-2">{t('workshop2.activity1.ai.activity')}:</span>
                                <span className={`inline-block px-2 py-0.5 rounded-full text-xs font-medium ${getMotivationColor(suggestion.activite || 'faible')}`}>
                                  {suggestion.activite === 'faible' ? t('workshop2.activity1.levels.low') :
                                   suggestion.activite === 'moyen' ? t('workshop2.activity1.levels.medium') :
                                   suggestion.activite === 'eleve' ? t('workshop2.activity1.levels.high') :
                                   suggestion.activite || t('workshop2.activity1.levels.low')}
                                </span>
                              </div>
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  </div>
                </div>
              )}
            </div>

            <div className="p-5 border-t flex justify-between items-center bg-gray-50">
              {/* Left side - More suggestions button */}
              <div>
                <button
                  onClick={handleGetMoreSuggestions}
                  disabled={isLoadingAi}
                  className="px-5 py-2.5 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center shadow-sm transition-all duration-200 transform hover:scale-105"
                >
                  <RefreshCw size={16} className={`mr-2 ${isLoadingAi ? 'animate-spin' : ''}`} />
                  {t('workshop2.activity1.ai.moreSuggestions')}
                </button>
              </div>

              {/* Right side - Cancel and Add buttons */}
              <div className="flex space-x-3">
                <button
                  onClick={() => setIsAiModalOpen(false)}
                  className="px-5 py-2.5 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors"
                >
                  {t('common.close')}
                </button>
                <button
                  onClick={handleAddAiSuggestions}
                  disabled={selectedAiSuggestions.length === 0 || isLoadingAi}
                  className="px-5 py-2.5 bg-gradient-to-r from-purple-600 to-indigo-600 text-white rounded-md hover:from-purple-700 hover:to-indigo-700 disabled:opacity-50 disabled:cursor-not-allowed shadow-sm transition-all duration-200 transform hover:scale-105 font-medium"
                >
                  {t('workshop2.activity1.ai.addButton')} {selectedAiSuggestions.length > 0 ? `(${selectedAiSuggestions.length})` : ''}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Activite1;
