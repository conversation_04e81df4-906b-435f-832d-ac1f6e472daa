import { POSITION_OPTIONS } from '../constants/index';

// Enhanced function to save security framework data with more details
const saveSecurityFrameworkData = (frameworks, selectedRules, securityControls) => {
  try {
    // Format the data with more comprehensive structure
    const securityFrameworkData = {
      frameworks: frameworks?.map(framework => ({
        id: framework.id,
        name: framework.name,
        status: framework.status,
        justification: framework.justification,
        documentationAvailable: framework.documentationAvailable,
        isImported: framework.isImported || false,
        isCustom: framework.isCustom || false,
        lastModified: new Date().toISOString()
      })) || [],
      selectedRules: selectedRules || {},
      securityControls: securityControls || [],
      lastSaved: new Date().toISOString()
    };

    // Save to localStorage
    localStorage.setItem('securityFrameworkData', JSON.stringify(securityFrameworkData));

    console.log("SECURITY FRAMEWORK SAVED successfully."); // Log success, not data
    return securityFrameworkData;
  } catch (error) {
    console.error("Error saving security framework data:", error);
    return null;
  }
};

// Fonction pour formater et sauvegarder les données selon l'onglet actif
export const handleSaveData = (
  activeTab,
  {
    organizationName,
    scope,
    analysisDate,
    participants,
    matrix,
    WORKSHOPS,
    businessValues,
    dreadedEvents,
    // Ajout des nouveaux paramètres pour le socle de sécurité
    frameworks,
    selectedRules,
    // Dashboard data (si nécessaire)
    dashboardPreferences,
    securityControls
  }
) => {
  switch(activeTab) {
    case 'dashboard':
      // Sauvegarde des préférences du tableau de bord (filtres, vue active, etc.)
      if (dashboardPreferences) {
        // Removed sensitive data logging for security
        localStorage.setItem('dashboardPreferences', JSON.stringify(dashboardPreferences));
      }
      return dashboardPreferences;

    case 'context':
      // Collect participant data properly formatted
      const participantsData = participants.map(p => {
        // Get actual position text
        let positionText = p.position;
        if (p.position === "OTHER" && p.customPosition) {
          positionText = p.customPosition;
        } else if (p.position) {
          const foundOption = POSITION_OPTIONS.find(opt => opt.value === p.position);
          positionText = foundOption?.label || p.position;
        }

        // Get workshops participation
        const workshopRoles = {};
        WORKSHOPS.forEach(workshop => {
          workshopRoles[workshop.name] = matrix[p.id]?.[workshop.id] || "";
        });

        return {
          id: p.id,
          name: p.name,
          position: positionText,
          workshopRoles
        };
      });

      const contextData = {
        organizationName,
        scope,
        analysisDate,
        participants: participantsData
      };

      // Removed sensitive data logging for security

      // Sauvegarde dans le stockage local
      localStorage.setItem('contextData', JSON.stringify(contextData));

      return contextData;

    case 'business-values':
      // Removed sensitive data logging for security

      // Sauvegarde dans le stockage local
      localStorage.setItem('businessValuesData', JSON.stringify(businessValues));

      return businessValues;

    case 'events':
      // Format dreaded events with full info
      const formattedEvents = dreadedEvents.map(event => {
        const businessValue = businessValues.find(v => v.id.toString() === event.businessValue);

        return {
          ...event,
          businessValueName: businessValue?.name || "Unknown"
        };
      });

      // Removed sensitive data logging for security

      // Sauvegarde dans le stockage local
      localStorage.setItem('dreadedEventsData', JSON.stringify(formattedEvents));

      return formattedEvents;

    case 'security':
      // Use the enhanced function to save security framework data
      return saveSecurityFrameworkData(frameworks, selectedRules, securityControls);

    default:
      // Removed sensitive data logging for security
      return null;
  }
};

// Fonction pour charger les préférences du tableau de bord
export const loadDashboardPreferences = () => {
  try {
    const savedData = localStorage.getItem('dashboardPreferences');
    return savedData ? JSON.parse(savedData) : {
      activeSection: 'overview',
      filters: {}
    };
  } catch (error) {
    // Log without revealing sensitive data
    console.error("Erreur lors du chargement des préférences du tableau de bord");
    return {
      activeSection: 'overview',
      filters: {}
    };
  }
};

// Enhanced function to load security framework data with better defaults
export const loadSecurityFrameworkData = (defaultFrameworks = []) => {
  try {
    const savedData = localStorage.getItem('securityFrameworkData');

    if (!savedData) {
      return {
        frameworks: defaultFrameworks,
        selectedRules: {},
        securityControls: [],
        lastSaved: null
      };
    }

    const parsedData = JSON.parse(savedData);

    // Ensure the data has all expected properties with defaults if missing
    return {
      frameworks: parsedData.frameworks || defaultFrameworks,
      selectedRules: parsedData.selectedRules || {},
      securityControls: parsedData.securityControls || [],
      lastSaved: parsedData.lastSaved || null
    };
  } catch (error) {
    // Log without revealing sensitive data
    console.error("Error loading security framework data");
    return {
      frameworks: defaultFrameworks,
      selectedRules: {},
      securityControls: [],
      lastSaved: null
    };
  }
};

// Function to backup security framework data with timestamp
export const backupSecurityFrameworkData = () => {
  try {
    const currentData = localStorage.getItem('securityFrameworkData');
    if (!currentData) return false;

    // Create a timestamped backup
    const timestamp = new Date().toISOString().replace(/:/g, '-');
    localStorage.setItem(`securityFrameworkData_backup_${timestamp}`, currentData);

    // Store list of backups
    const backupsList = JSON.parse(localStorage.getItem('securityFrameworkBackups') || '[]');
    backupsList.push(timestamp);
    localStorage.setItem('securityFrameworkBackups', JSON.stringify(backupsList));

    return timestamp;
  } catch (error) {
    // Log without revealing sensitive data
    console.error("Error creating security framework backup");
    return false;
  }
};

// Function to restore from a specific backup
export const restoreSecurityFrameworkBackup = (timestamp) => {
  try {
    const backupKey = `securityFrameworkData_backup_${timestamp}`;
    const backupData = localStorage.getItem(backupKey);

    if (!backupData) {
      throw new Error(`Backup not found for timestamp: ${timestamp}`);
    }

    // Backup current data before overwriting
    backupSecurityFrameworkData();

    // Restore from backup
    localStorage.setItem('securityFrameworkData', backupData);

    return JSON.parse(backupData);
  } catch (error) {
    // Log without revealing sensitive data
    console.error("Error restoring security framework backup");
    return null;
  }
};

// Function to get list of available backups
export const getSecurityFrameworkBackups = () => {
  try {
    return JSON.parse(localStorage.getItem('securityFrameworkBackups') || '[]');
  } catch (error) {
    // Log without revealing sensitive data
    console.error("Error retrieving security framework backups");
    return [];
  }
};

// Function to export security framework data to a file
export const exportSecurityFrameworkData = () => {
  try {
    const data = loadSecurityFrameworkData();

    // Create a downloadable JSON file
    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);

    const a = document.createElement('a');
    a.href = url;
    a.download = `security-framework-export-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    return true;
  } catch (error) {
    // Log without revealing sensitive data
    console.error("Error exporting security framework data");
    return false;
  }
};

// Function to import security framework data from a file
export const importSecurityFrameworkData = async (file) => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();

    reader.onload = (event) => {
      try {
        const data = JSON.parse(event.target.result);

        // Validate that the imported data has the right structure
        if (!data.frameworks || !Array.isArray(data.frameworks)) {
          reject(new Error("Invalid security framework data format"));
          return;
        }

        // Backup current data before overwriting
        backupSecurityFrameworkData();

        // Save the imported data
        localStorage.setItem('securityFrameworkData', JSON.stringify({
          ...data,
          importedAt: new Date().toISOString()
        }));

        resolve(data);
      } catch (error) {
        reject(error);
      }
    };

    reader.onerror = (error) => reject(error);
    reader.readAsText(file);
  });
};

// Fonction pour charger les données du contexte
export const loadContextData = () => {
  try {
    const savedData = localStorage.getItem('contextData');
    return savedData ? JSON.parse(savedData) : null;
  } catch (error) {
    // Log without revealing sensitive data
    console.error("Erreur lors du chargement des données du contexte");
    return null;
  }
};

// Fonction pour charger les données des valeurs métier
export const loadBusinessValuesData = () => {
  try {
    const savedData = localStorage.getItem('businessValuesData');
    return savedData ? JSON.parse(savedData) : [];
  } catch (error) {
    // Log without revealing sensitive data
    console.error("Erreur lors du chargement des valeurs métier");
    return [];
  }
};

// Fonction pour charger les données des événements redoutés
export const loadDreadedEventsData = () => {
  try {
    const savedData = localStorage.getItem('dreadedEventsData');
    return savedData ? JSON.parse(savedData) : [];
  } catch (error) {
    // Log without revealing sensitive data
    console.error("Erreur lors du chargement des événements redoutés");
    return [];
  }
};

// Fonction pour charger toutes les données
export const loadAllData = () => {
  return {
    dashboard: loadDashboardPreferences(),
    context: loadContextData(),
    businessValues: loadBusinessValuesData(),
    dreadedEvents: loadDreadedEventsData(),
    securityFramework: loadSecurityFrameworkData()
  };
};

// Fonction pour convertir les données du contexte au format compatible avec RACIVisualization
export const convertContextToRACIFormat = (contextData) => {
  // Si aucune donnée n'est disponible, renvoyer des objets vides
  if (!contextData || !contextData.participants) {
    return {
      participants: [],
      workshops: [],
      matrix: {}
    };
  }

  // Extraire les noms des ateliers uniques à partir des participants
  const workshopNames = new Set();
  contextData.participants.forEach(p => {
    if (p.workshopRoles) {
      Object.keys(p.workshopRoles).forEach(w => workshopNames.add(w));
    }
  });

  // Créer les objets workshops
  const workshops = Array.from(workshopNames).map(name => ({
    id: name,
    name: name
  }));

  // Créer les objets participants au format attendu
  const participants = contextData.participants.map(p => ({
    id: p.id,
    name: p.name,
    position: p.position,
    customPosition: p.customPosition || ""
  }));

  // Créer la matrice RACI
  const matrix = {};
  contextData.participants.forEach(p => {
    matrix[p.id] = {};
    if (p.workshopRoles) {
      Object.entries(p.workshopRoles).forEach(([workshop, role]) => {
        matrix[p.id][workshop] = role;
      });
    }
  });

  return { participants, workshops, matrix };
};

// Fonction pour exporter toutes les données au format JSON
export const exportAllData = () => {
  const data = loadAllData();
  // Créer un Blob avec les données au format JSON
  const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
  const url = URL.createObjectURL(blob);

  // Créer un lien de téléchargement
  const a = document.createElement('a');
  a.href = url;
  a.download = `ebios-rm-export-${new Date().toISOString().split('T')[0]}.json`;
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
  URL.revokeObjectURL(url);
};

// Fonction pour importer les données depuis un fichier JSON
export const importDataFromJSON = async (file) => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();

    reader.onload = (event) => {
      try {
        const data = JSON.parse(event.target.result);

        // Vérifier que le format est correct
        if (data.context || data.businessValues || data.dreadedEvents || data.securityFramework) {
          // Sauvegarder les données importées
          if (data.dashboard) {
            localStorage.setItem('dashboardPreferences', JSON.stringify(data.dashboard));
          }
          if (data.context) {
            localStorage.setItem('contextData', JSON.stringify(data.context));
          }
          if (data.businessValues) {
            localStorage.setItem('businessValuesData', JSON.stringify(data.businessValues));
          }
          if (data.dreadedEvents) {
            localStorage.setItem('dreadedEventsData', JSON.stringify(data.dreadedEvents));
          }
          if (data.securityFramework) {
            localStorage.setItem('securityFrameworkData', JSON.stringify(data.securityFramework));
          }

          resolve(data);
        } else {
          reject(new Error("Format de fichier incorrect"));
        }
      } catch (error) {
        reject(error);
      }
    };

    reader.onerror = (error) => reject(error);

    reader.readAsText(file);
  });
};