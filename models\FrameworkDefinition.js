const mongoose = require('mongoose');

const RuleSchema = new mongoose.Schema({
  // We'll use the ID generated on the frontend for consistency during import/save cycle,
  // but Mongoose will add its own _id automatically.
  id: {
    type: String,
    required: true
  },
  name: {
    type: String,
    required: [true, 'Rule name is required'],
    trim: true
  },
  description: {
    type: String,
    trim: true,
    default: ''
  }
}, { _id : false }); // Don't create a separate _id for subdocument rules initially if not needed

const FrameworkDefinitionSchema = new mongoose.Schema({
  name: {
    type: String,
    required: [true, 'Framework definition name is required'],
    trim: true,
  },
  description: {
    type: String,
    trim: true,
    default: ''
  },
  rules: [RuleSchema],
  is_predefined: {
    type: Boolean,
    default: false, // Defaults to false, set true for seeded/global frameworks
  },
  analysisId: { // Link to a specific analysis if not predefined
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Analysis',
    required: function() { return !this.is_predefined; }, // Required only if it's a custom framework
    index: true, // Index for faster lookups by analysis
    default: null,
  },
  createdBy: { // Track who created custom frameworks
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: function() { return !this.is_predefined; } // Required only if it's a custom framework
  },
  // Add companyId if needed for authorization checks, especially for predefined ones
  // companyId: {
  //   type: mongoose.Schema.Types.ObjectId,
  //   ref: 'Company',
  // },
}, {
  timestamps: true // Automatically add createdAt and updatedAt
});

// Optional: Add an index if you frequently query by name + analysisId or name + is_predefined
// FrameworkDefinitionSchema.index({ analysisId: 1, name: 1 });
// FrameworkDefinitionSchema.index({ is_predefined: 1, name: 1 });

const FrameworkDefinition = mongoose.model('FrameworkDefinition', FrameworkDefinitionSchema);

module.exports = FrameworkDefinition;