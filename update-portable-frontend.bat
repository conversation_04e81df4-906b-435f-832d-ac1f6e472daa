@echo off
echo ==========================================
echo Updating Portable Package with Fresh Frontend
echo ==========================================

echo [1/3] Creating fresh build...
cd /d "C:\Users\<USER>\Desktop\EBROS RM Project\APP\Atelier 1\my-ebios-app"

REM Build the latest frontend
echo Building latest frontend...
call npm run build

echo [2/3] Updating portable package with fresh build...
if exist build (
    if exist ebiosrm-portable\build rmdir /s /q ebiosrm-portable\build
    xcopy build ebiosrm-portable\build\ /E /I /Y
    echo ✅ Portable package updated with fresh frontend!
) else (
    echo ❌ Build failed - no build folder found
    exit /b 1
)

echo [3/3] Verifying update...
if exist ebiosrm-portable\build\asset-manifest.json (
    echo ✅ Fresh build confirmed in portable package
    echo New asset manifest:
    type ebiosrm-portable\build\asset-manifest.json
) else (
    echo ❌ Update failed
    exit /b 1
)

echo.
echo ==========================================
echo ✅ Portable Package Updated!
echo ==========================================
echo.
echo Your portable package now includes:
echo ✅ Latest frontend with Atelier 3 and 4
echo ✅ Fresh backend with all dependencies
echo ✅ Complete database
echo.
echo Ready to copy ebiosrm-portable folder to other PC!
echo.
pause
