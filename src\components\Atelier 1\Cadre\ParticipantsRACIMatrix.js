import React from 'react';
import { Trash2, UserPlus } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { ROLES, POSITION_OPTIONS } from '../../../constants'; // Make sure POSITION_OPTIONS is imported here or passed as prop
import { showSuccessToast, showErrorToast } from '../../../utils/toastUtils';

const ParticipantsRACIMatrix = ({
  workshops = [], // Default to empty array
  // positionOptions = [], // Use imported constant instead
  participants = [], // Default to empty array
  setParticipants,
  matrix = {}, // Default to empty object
  setMatrix,
  isTestModeActive // Receive mode status
}) => {
  const { t } = useTranslation();

  // Create translated ROLES array
  const translatedRoles = [
    { value: "", label: t('context.fields.selectRole') },
    { value: "-", label: t('context.raci.notInvolved') },
    { value: "R", label: `R - ${t('context.raci.responsible')}` },
    { value: "A", label: `A - ${t('context.raci.accountable')}` },
    { value: "C", label: `C - ${t('context.raci.consulted')}` },
    { value: "I", label: `I - ${t('context.raci.informed')}` }
  ];

  // Add participant is now handled in Context.js to coordinate state updates

  const handleRemoveParticipant = (id) => {
    if (isTestModeActive) {
        showErrorToast("Suppression impossible en mode test.");
        return;
    }
    if (participants.length <= 1) {
        showErrorToast("Vous ne pouvez pas supprimer le dernier participant.");
        return; // Prevent removing the last participant
    }
    if (window.confirm("Êtes-vous sûr de vouloir supprimer ce participant et ses rôles associés ?")) {
        // Find the participant to be removed for the toast message
        const participantToRemove = participants.find(p => p.id === id);

        setParticipants(prev => prev.filter(p => p.id !== id));
        setMatrix(prevMatrix => {
            const newMatrix = { ...prevMatrix };
            delete newMatrix[id];
            return newMatrix;
        });

        // Show success toast
        if (participantToRemove) {
            showSuccessToast(`Participant "${participantToRemove.name || 'Sans nom'}" supprimé avec succès!`);
        } else {
            showSuccessToast('Participant supprimé avec succès!');
        }
    }
  };

  const handleParticipantChange = (id, field, value) => {
     if (isTestModeActive) return; // Prevent changes in test mode
     setParticipants(prev =>
         prev.map(p => {
             if (p.id === id) {
                 const updatedParticipant = { ...p, [field]: value };
                 // Reset customPosition if position changes away from 'OTHER'
                 if (field === 'position' && value !== 'OTHER') {
                     updatedParticipant.customPosition = '';
                 }
                 return updatedParticipant;
             }
             return p;
         })
     );
  };


  const handleRoleChange = (participantId, workshopId, role) => {
    if (isTestModeActive) return; // Prevent changes in test mode
    setMatrix(prevMatrix => ({
      ...prevMatrix,
      [participantId]: {
        ...(prevMatrix[participantId] || {}), // Ensure participant row exists
        [workshopId]: role
      }
    }));
  };


  // Render check - show placeholder if no workshops or participants
   if (!participants || participants.length === 0) {
       return (
           <div className="text-center py-6 px-4 border border-dashed border-slate-300 rounded-lg bg-slate-50">
               <p className="text-slate-600">Aucun participant à afficher.</p>
               {!isTestModeActive && <p className="text-sm text-slate-500 mt-1">Cliquez sur "Ajouter Participant" pour commencer.</p>}
               {isTestModeActive && <p className="text-sm text-slate-500 mt-1">Les données proviennent du mode test.</p>}
           </div>
       );
   }
    if (!workshops || workshops.length === 0) {
       return (
           <div className="text-center py-6 px-4 border border-dashed border-slate-300 rounded-lg bg-slate-50">
               <p className="text-slate-600">Aucun atelier défini.</p>
                {isTestModeActive && <p className="text-sm text-slate-500 mt-1">Les ateliers proviennent du mode test.</p>}
                 {!isTestModeActive && <p className="text-sm text-slate-500 mt-1">Vérifiez la configuration ou les données chargées.</p>}
           </div>
       );
   }

  return (
    <div className="overflow-x-auto shadow-md rounded-xl border border-slate-200 bg-white">
      <table className="min-w-full divide-y divide-slate-200">
        <thead className="bg-gradient-to-r from-slate-50 to-blue-50">
          <tr>
            <th scope="col" className="py-4 px-4 text-left text-xs font-semibold text-slate-700 uppercase tracking-wider w-36 sticky left-0 bg-gradient-to-r from-slate-50 to-blue-50 z-10 border-r border-slate-200">
              <div className="flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1 text-blue-500" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clipRule="evenodd" />
                </svg>
                {t('context.table.participant')}
              </div>
            </th>
            <th scope="col" className="py-4 px-4 text-left text-xs font-semibold text-slate-700 uppercase tracking-wider w-36 border-r border-slate-200">
              <div className="flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1 text-blue-500" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M6 6V5a3 3 0 013-3h2a3 3 0 013 3v1h2a2 2 0 012 2v3.57A22.952 22.952 0 0110 13a22.95 22.95 0 01-8-1.43V8a2 2 0 012-2h2zm2-1a1 1 0 011-1h2a1 1 0 011 1v1H8V5zm1 5a1 1 0 011-1h.01a1 1 0 110 2H10a1 1 0 01-1-1z" clipRule="evenodd" />
                </svg>
                {t('context.table.position')}
              </div>
            </th>
            {workshops.map(workshop => (
              <th key={workshop.id} scope="col" className="py-4 px-4 text-center text-xs font-semibold text-slate-700 uppercase tracking-wider min-w-[100px] border-r border-slate-200 whitespace-nowrap">
                <div className="flex items-center justify-center">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1 text-blue-500" viewBox="0 0 20 20" fill="currentColor">
                    <path d="M9 4.804A7.968 7.968 0 005.5 4c-1.255 0-2.443.29-3.5.804v10A7.969 7.969 0 015.5 14c1.669 0 3.218.51 4.5 1.385A7.962 7.962 0 0114.5 14c1.255 0 2.443.29 3.5.804v-10A7.968 7.968 0 0014.5 4c-1.255 0-2.443.29-3.5.804V12a1 1 0 11-2 0V4.804z" />
                  </svg>
                  {t(`context.table.workshop${workshop.id.slice(-1)}`) || workshop.name}
                </div>
              </th>
            ))}
            <th scope="col" className="py-4 px-4 text-center text-xs font-semibold text-slate-700 uppercase tracking-wider w-8 sticky right-0 bg-gradient-to-r from-slate-50 to-blue-50 z-10">
              <span className="sr-only">Actions</span>
            </th>
          </tr>
        </thead>
        <tbody className="bg-white divide-y divide-slate-200">
          {participants.map((participant, index) => (
            <tr key={participant.id} className={`hover:bg-blue-50/30 transition-colors duration-150 ${index % 2 !== 0 ? 'bg-slate-50/50' : ''}`}>
              {/* Participant Name - Sticky */}
              <td className="py-3 px-4 whitespace-nowrap text-sm text-slate-800 sticky left-0 z-0 border-r border-slate-200" style={{ backgroundColor: index % 2 !== 0 ? '#F8FAFC' /* bg-slate-50/50 */ : '#FFFFFF' /* bg-white */ }}>
                <div className="relative group">
                  <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-slate-400 group-focus-within:text-blue-500 transition-colors duration-200" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <input
                    type="text"
                    className={`w-full p-2 pl-9 border rounded-md text-sm shadow-sm transition duration-150 ${isTestModeActive ? 'bg-slate-100 border-slate-200 cursor-not-allowed' : 'border-slate-300 focus:ring-2 focus:ring-blue-500 focus:border-blue-500'}`}
                    placeholder={t('context.fields.participantNamePlaceholder')}
                    value={participant.name || ''}
                    onChange={(e) => handleParticipantChange(participant.id, 'name', e.target.value)}
                    disabled={isTestModeActive}
                    aria-label={`Nom du participant ${index + 1}`}
                  />
                </div>
              </td>
              {/* Position / Custom Position */}
              <td className="py-3 px-4 whitespace-nowrap text-sm text-slate-800 border-r border-slate-200">
                <div className="flex flex-col space-y-2">
                  <div className="relative group">
                    <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-slate-400 group-focus-within:text-blue-500 transition-colors duration-200" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M6 6V5a3 3 0 013-3h2a3 3 0 013 3v1h2a2 2 0 012 2v3.57A22.952 22.952 0 0110 13a22.95 22.95 0 01-8-1.43V8a2 2 0 012-2h2zm2-1a1 1 0 011-1h2a1 1 0 011 1v1H8V5zm1 5a1 1 0 011-1h.01a1 1 0 110 2H10a1 1 0 01-1-1z" clipRule="evenodd" />
                      </svg>
                    </div>
                    <select
                      className={`w-full p-2 pl-9 border rounded-md text-sm shadow-sm transition duration-150 ${isTestModeActive ? 'bg-slate-100 border-slate-200 cursor-not-allowed appearance-none' : 'border-slate-300 focus:ring-2 focus:ring-blue-500 focus:border-blue-500'}`}
                      value={participant.position || ''}
                      onChange={(e) => handleParticipantChange(participant.id, 'position', e.target.value)}
                      disabled={isTestModeActive}
                      aria-label={`Fonction du participant ${index + 1}`}
                    >
                      <option value="" disabled>-- {t('context.fields.position')} --</option>
                      {POSITION_OPTIONS.map(option => (
                        <option key={option.value} value={option.value}>
                          {option.label}
                        </option>
                      ))}
                    </select>
                  </div>

                  {participant.position === "OTHER" && (
                    <input
                      type="text"
                       className={`w-full p-2 border rounded-md text-sm shadow-sm transition duration-150 ${isTestModeActive ? 'bg-slate-100 border-slate-200 cursor-not-allowed' : 'border-slate-300 focus:ring-1 focus:ring-blue-500 focus:border-blue-500'}`}
                      placeholder={t('context.fields.customPositionPlaceholder')}
                      value={participant.customPosition || ''}
                      onChange={(e) => handleParticipantChange(participant.id, 'customPosition', e.target.value)}
                      disabled={isTestModeActive}
                       aria-label={`Fonction personnalisée du participant ${index + 1}`}
                    />
                  )}
                </div>
              </td>
              {/* Workshop Roles */}
              {workshops.map(workshop => (
                <td key={workshop.id} className="py-3 px-4 whitespace-nowrap text-center border-r border-slate-200">
                  <div className="relative group">
                    <select
                      className={`w-full p-2 border rounded-md text-sm shadow-sm transition duration-150 ${isTestModeActive ? 'bg-slate-100 border-slate-200 cursor-not-allowed appearance-none' : 'border-slate-300 focus:ring-2 focus:ring-blue-500 focus:border-blue-500'}`}
                      value={matrix[participant.id]?.[workshop.id] || ""}
                      onChange={(e) => handleRoleChange(participant.id, workshop.id, e.target.value)}
                      disabled={isTestModeActive}
                      aria-label={`Rôle du participant ${index + 1} pour ${workshop.name}`}
                      style={{
                        backgroundColor: matrix[participant.id]?.[workshop.id] === 'R' ? 'rgba(240, 253, 244, 0.85)' :
                                        matrix[participant.id]?.[workshop.id] === 'A' ? 'rgba(239, 246, 255, 0.85)' :
                                        matrix[participant.id]?.[workshop.id] === 'C' ? 'rgba(254, 249, 195, 0.85)' :
                                        matrix[participant.id]?.[workshop.id] === 'I' ? 'rgba(254, 242, 242, 0.85)' : ''
                      }}
                    >
                      {translatedRoles.map(role => (
                        <option key={role.value} value={role.value}>
                          {role.label}
                        </option>
                      ))}
                    </select>
                  </div>
                </td>
              ))}
              {/* Actions - Sticky */}
              <td className="py-3 px-4 whitespace-nowrap text-center text-sm font-medium sticky right-0 z-0" style={{ backgroundColor: index % 2 !== 0 ? '#F8FAFC' : '#FFFFFF' }}>
                 <button
                    type="button"
                    className={`p-2 rounded-full transition-all duration-200 ${
                        participants.length > 1 && !isTestModeActive
                         ? 'text-red-500 hover:text-white hover:bg-red-500 shadow-sm'
                         : 'text-slate-300 cursor-not-allowed'
                    }`}
                    onClick={() => handleRemoveParticipant(participant.id)}
                    disabled={participants.length <= 1 || isTestModeActive}
                    title={participants.length <= 1 ? "Impossible de supprimer le dernier participant" : (isTestModeActive ? "Suppression désactivée en mode test" : "Supprimer ce participant")}
                     aria-label={`Supprimer le participant ${index + 1}`}
                 >
                    <Trash2 size={18} />
                 </button>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
      {/* Add participant button moved to Context.js for better state management */}
    </div>
  );
};

export default ParticipantsRACIMatrix;