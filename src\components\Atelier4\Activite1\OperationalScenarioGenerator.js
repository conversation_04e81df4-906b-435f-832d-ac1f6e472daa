// src/components/Atelier4/Activite1/OperationalScenarioGenerator.js
import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  Wand2,
  Target,
  Shield,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Loader2,
  Download,
  Eye,
  Edit3,
  Save
} from 'lucide-react';
import { useAnalysis } from '../../../context/AnalysisContext';
import { api } from '../../../api/apiClient';
import { useTranslation } from 'react-i18next';

const OperationalScenarioGenerator = ({
  selectedAttackPath,
  threatIntelligence,
  businessAssets = [],
  onScenarioGenerated
}) => {
  const { t } = useTranslation();
  const { currentAnalysis } = useAnalysis();
  const [generating, setGenerating] = useState(false);
  const [generatedScenario, setGeneratedScenario] = useState(null);
  const [error, setError] = useState(null);
  const [showPreview, setShowPreview] = useState(false);

  // Attack phases for scenario generation
  const attackPhases = [
    {
      id: 'CONNAITRE',
      name: 'Reconnaissance',
      description: 'Information gathering and target reconnaissance',
      color: 'bg-blue-500'
    },
    {
      id: 'RENTRER',
      name: 'Initial Access',
      description: 'Gaining initial foothold in the target system',
      color: 'bg-orange-500'
    },
    {
      id: 'TROUVER',
      name: 'Discovery & Lateral Movement',
      description: 'Internal reconnaissance and lateral movement',
      color: 'bg-purple-500'
    },
    {
      id: 'EXPLOITER',
      name: 'Data Exploitation',
      description: 'Data collection and exfiltration',
      color: 'bg-red-500'
    }
  ];

  // Generate operational scenario using AI
  const generateScenario = async () => {
    if (!currentAnalysis?.id || !selectedAttackPath) {
      setError('Please select an attack path first');
      return;
    }

    setGenerating(true);
    setError(null);

    try {
      console.log('[OperationalScenarioGenerator] Generating scenario for attack path:', selectedAttackPath);

      const response = await api.post(`/ai/generate-enhanced-operational-scenarios`, {
        analysisId: currentAnalysis.id,
        attackPath: selectedAttackPath,
        threatIntelligence: threatIntelligence || {},
        businessAssets: businessAssets,
        options: {
          includeVulnerabilities: true,
          includeMitreTechniques: true,
          detailLevel: 'comprehensive'
        }
      });

      if (response.data && response.data.success) {
        // The enhanced AI endpoint returns { scenario: {...} } instead of direct data
        const scenario = response.data.data.scenario || response.data.data;
        setGeneratedScenario(scenario);
        setShowPreview(true);
        console.log('[OperationalScenarioGenerator] Enhanced scenario generated:', scenario);
      } else {
        throw new Error(response.data?.message || 'Failed to generate scenario');
      }
    } catch (error) {
      console.error('[OperationalScenarioGenerator] Error generating scenario:', error);
      setError(error.response?.data?.message || error.message || 'Failed to generate operational scenario');
    } finally {
      setGenerating(false);
    }
  };

  // Save generated scenario
  const saveScenario = async () => {
    if (!generatedScenario || !currentAnalysis?.id) return;

    try {
      const response = await api.post(`/analyses/${currentAnalysis.id}/operational-scenarios`, {
        scenarios: [generatedScenario]
      });

      if (response.data && response.data.success) {
        if (onScenarioGenerated) {
          onScenarioGenerated(generatedScenario);
        }
        setShowPreview(false);
        setGeneratedScenario(null);
      } else {
        throw new Error(response.data?.message || 'Failed to save scenario');
      }
    } catch (error) {
      console.error('[OperationalScenarioGenerator] Error saving scenario:', error);
      setError(error.response?.data?.message || error.message || 'Failed to save scenario');
    }
  };

  // Get difficulty color
  const getDifficultyColor = (difficulty) => {
    if (difficulty <= 1) return 'bg-green-100 text-green-800';
    if (difficulty <= 2) return 'bg-yellow-100 text-yellow-800';
    if (difficulty <= 3) return 'bg-orange-100 text-orange-800';
    return 'bg-red-100 text-red-800';
  };

  // Get probability color
  const getProbabilityColor = (probability) => {
    if (probability >= 0.8) return 'bg-red-100 text-red-800';
    if (probability >= 0.6) return 'bg-orange-100 text-orange-800';
    if (probability >= 0.4) return 'bg-yellow-100 text-yellow-800';
    return 'bg-green-100 text-green-800';
  };

  // Render scenario phase
  const renderPhase = (phase) => {
    const phaseInfo = attackPhases.find(p => p.id === phase.phase) || {};

    return (
      <div key={phase.phase} className="border border-gray-200 rounded-lg p-4">
        <div className="flex items-center mb-3">
          <div className={`${phaseInfo.color || 'bg-gray-500'} p-2 rounded-lg mr-3`}>
            <Target size={16} className="text-white" />
          </div>
          <div>
            <h4 className="font-medium text-gray-800">{phase.name}</h4>
            <p className="text-sm text-gray-600">{phaseInfo.description}</p>
          </div>
        </div>

        {phase.actions && phase.actions.length > 0 && (
          <div className="space-y-3">
            {phase.actions.map((action, idx) => (
              <div key={action.id || idx} className="bg-gray-50 p-3 rounded-lg">
                <div className="flex items-start justify-between mb-2">
                  <h5 className="font-medium text-sm text-gray-800">{action.title}</h5>
                  <div className="flex items-center space-x-2">
                    <span className={`px-2 py-1 rounded text-xs font-medium ${getDifficultyColor(action.difficulty)}`}>
                      Difficulty: {action.difficulty}/4
                    </span>
                    <span className={`px-2 py-1 rounded text-xs font-medium ${getProbabilityColor(action.probability)}`}>
                      Probability: {Math.round(action.probability * 100)}%
                    </span>
                  </div>
                </div>

                <p className="text-sm text-gray-600 mb-2">{action.description}</p>

                {action.techniques && action.techniques.length > 0 && (
                  <div className="flex flex-wrap gap-1 mb-2">
                    {action.techniques.map((technique, techIdx) => (
                      <span
                        key={techIdx}
                        className="px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs font-mono"
                      >
                        {technique}
                      </span>
                    ))}
                  </div>
                )}

                {action.assets && action.assets.length > 0 && (
                  <div className="text-xs text-gray-500">
                    Targets: {action.assets.map(asset => asset.name || asset).join(', ')}
                  </div>
                )}
              </div>
            ))}
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-medium text-gray-800 flex items-center">
          <Wand2 size={20} className="mr-2 text-purple-600" />
          AI Operational Scenario Generator
        </h3>

        <button
          onClick={generateScenario}
          disabled={generating || !selectedAttackPath}
          className="flex items-center px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
        >
          {generating ? (
            <Loader2 size={16} className="mr-2 animate-spin" />
          ) : (
            <Wand2 size={16} className="mr-2" />
          )}
          {generating ? 'Generating...' : 'Generate Scenario'}
        </button>
      </div>

      {/* Prerequisites Check */}
      <div className="mb-6 space-y-2">
        <h4 className="font-medium text-gray-700 mb-3">Prerequisites:</h4>
        <div className="flex items-center space-x-2">
          {selectedAttackPath ? (
            <CheckCircle size={16} className="text-green-600" />
          ) : (
            <XCircle size={16} className="text-red-600" />
          )}
          <span className={`text-sm ${selectedAttackPath ? 'text-green-700' : 'text-red-700'}`}>
            Attack path selected: {selectedAttackPath?.title || 'None'}
          </span>
        </div>

        <div className="flex items-center space-x-2">
          {businessAssets && businessAssets.length > 0 ? (
            <CheckCircle size={16} className="text-green-600" />
          ) : (
            <AlertTriangle size={16} className="text-yellow-600" />
          )}
          <span className={`text-sm ${businessAssets && businessAssets.length > 0 ? 'text-green-700' : 'text-yellow-700'}`}>
            Business assets: {businessAssets?.length || 0} configured
          </span>
        </div>

        <div className="flex items-center space-x-2">
          {threatIntelligence && Object.keys(threatIntelligence).length > 0 ? (
            <CheckCircle size={16} className="text-green-600" />
          ) : (
            <AlertTriangle size={16} className="text-yellow-600" />
          )}
          <span className={`text-sm ${threatIntelligence && Object.keys(threatIntelligence).length > 0 ? 'text-green-700' : 'text-yellow-700'}`}>
            Threat intelligence: {threatIntelligence && Object.keys(threatIntelligence).length > 0 ? 'Available' : 'Optional'}
          </span>
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
          <div className="flex items-center">
            <AlertTriangle size={16} className="text-red-600 mr-2" />
            <span className="text-red-800 text-sm">{error}</span>
          </div>
        </div>
      )}

      {/* Generated Scenario Preview */}
      {showPreview && generatedScenario && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="border border-gray-200 rounded-lg p-6 bg-gray-50"
        >
          <div className="flex items-center justify-between mb-4">
            <h4 className="font-medium text-gray-800">Generated Scenario Preview</h4>
            <div className="flex items-center space-x-2">
              <button
                onClick={() => setShowPreview(false)}
                className="px-3 py-1 text-sm text-gray-600 hover:text-gray-800 transition-colors"
              >
                <XCircle size={16} />
              </button>
              <button
                onClick={saveScenario}
                className="flex items-center px-3 py-1 bg-green-600 text-white rounded text-sm hover:bg-green-700 transition-colors"
              >
                <Save size={14} className="mr-1" />
                Save
              </button>
            </div>
          </div>

          <div className="bg-white rounded-lg p-4 mb-4">
            <h5 className="font-medium text-gray-800 mb-2">{generatedScenario.title}</h5>
            <p className="text-sm text-gray-600 mb-3">{generatedScenario.description}</p>

            {generatedScenario.phases && (
              <div className="space-y-4">
                {generatedScenario.phases.map(phase => renderPhase(phase))}
              </div>
            )}
          </div>

          {generatedScenario.generatedAt && (
            <div className="text-xs text-gray-500">
              Generated: {new Date(generatedScenario.generatedAt).toLocaleString()}
            </div>
          )}
        </motion.div>
      )}

      {/* Empty State */}
      {!showPreview && !generating && (
        <div className="text-center py-8 text-gray-500">
          <Wand2 size={48} className="mx-auto mb-4 text-gray-300" />
          <p className="mb-2">Generate AI-powered operational scenarios based on your attack paths and threat intelligence.</p>
          <p className="text-sm">Select an attack path and click "Generate Scenario" to begin.</p>
        </div>
      )}
    </div>
  );
};

export default OperationalScenarioGenerator;
