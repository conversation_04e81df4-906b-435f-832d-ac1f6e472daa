// src/services/securityControlsService.js
import api from '../api/apiClient';

/**
 * Service for handling security controls operations (Analysis Control Plan)
 */
const securityControlsService = {
  /**
   * Get the analysis control plan for a specific analysis
   * @param {string} analysisId - Analysis ID
   * @returns {Promise<Object>} The analysis control plan document (containing planData)
   */
  getSecurityControls: async (analysisId) => {
    if (!analysisId) throw new Error('Analysis ID is required to fetch controls');
    // Removed sensitive data logging for security
    try {
      // Call the specific endpoint for controls
      const response = await api.get(`/analyses/${analysisId}/controls`);

      // The controller returns { success: true, data: { _id, analysisId, planData, createdAt, updatedAt } }
      // or { success: true, message: 'No plan found', data: { planData: {} } }
      if (response.success) {
        // Removed sensitive data logging for security
        return response.data; // Return the whole data object (incl. planData)
      } else {
         // Handle case where controller returns success:false (should be caught by fetchWithAuth)
          throw new Error(response.message || 'Failed to fetch security controls data from API');
      }
    } catch (error) {
      // Log without revealing sensitive data
      console.error('Error fetching security controls data');
      // Re-throw the error to be handled by the caller (e.g., AnalysisContext)
      throw error;
    }
  },

  /**
   * Save the analysis control plan for a specific analysis
   * @param {string} analysisId - Analysis ID
   * @param {Object} planData - The control plan data object to save
   * @returns {Promise<Object>} Response from the API
   */
  saveSecurityControls: async (analysisId, planData) => {
    if (!analysisId) throw new Error('Analysis ID is required to save controls');
    if (planData === undefined || planData === null) {
         throw new Error('planData is required to save controls');
    }
    // Removed sensitive data logging for security
    // The controller expects the planData object directly in the body
    const requestBody = {
      planData: planData
    };

    try {
      // Use PUT to the specific controls endpoint
      const response = await api.put(`/analyses/${analysisId}/controls`, requestBody);

      // Removed sensitive data logging for security
      // Return the full API response { success, data }
      return response;
    } catch (error) {
      // Log without revealing sensitive data
      console.error('Error saving security controls data');
       // Re-throw the error to be handled by the caller
      throw error;
    }
  },

  /**
   * Get AI suggestions for security controls based on context.
   * @param {Object} context - Context including rule and event details.
   * @param {string} context.ruleName - Name of the security rule.
   * @param {string} [context.ruleDescription] - Description of the security rule.
   * @param {string} context.dreadedEventName - Name of the dreaded event.
   * @param {string} [context.dreadedEventSeverity] - Severity of the dreaded event.
   * @param {string[]} [context.existingControlNames] - Optional list of existing control names to avoid duplicates.
   * @returns {Promise<string[]>} A promise that resolves to an array of suggested control names.
   */
  getAiControlSuggestions: async (context) => {
    if (!context || !context.ruleName || !context.dreadedEventName) {
      throw new Error('Context with ruleName and dreadedEventName is required for AI suggestions.');
    }
    // Removed sensitive data logging for security
    try {
      const response = await api.post('/ai/suggest-security-controls', context);

      if (response.success && response.data && Array.isArray(response.data.suggestions)) {
        // Removed sensitive data logging for security
        return response.data.suggestions; // Return the array of strings
      } else {
        throw new Error(response.message || 'Failed to retrieve valid AI suggestions.');
      }
    } catch (error) {
      // Log without revealing sensitive data
      console.error('Error getting AI control suggestions');
      // Re-throw the error for the component to handle
      throw error;
    }
  },

  /**
   * Get the master list of all possible security control definitions.
   * Assumes an API endpoint like GET /api/controls/definitions exists.
   * @returns {Promise<Object>} Response from the API, expecting { success: true, data: [...] } where data is an array of control objects.
   */
  getAllControlDefinitions: async () => {
    // Removed sensitive data logging for security
    try {
      // Adjust the endpoint URL if needed
      const response = await api.get(`/controls/definitions`);

      if (response.success) {
        // Removed sensitive data logging for security
        // Ensure data is an array, return it directly
        if (!Array.isArray(response.data)) {
           // Removed sensitive data logging for security
           return { success: true, data: [] }; // Return empty array if format is wrong
        }
        return response; // Return the full response { success, data }
      } else {
          throw new Error(response.message || 'Failed to fetch master control list from API');
      }
    } catch (error) {
      // Log without revealing sensitive data
      console.error('Error fetching master control list');
      // Re-throw the error to be handled by the caller (e.g., AnalysisContext)
      throw error;
    }
  },

  /**
   * Format the security controls data from the API response
   * @param {Object} apiData - The API response data
   * @returns {Object} Formatted security controls data
   */
  formatSecurityControlsData: (apiData) => {
    if (!apiData || !apiData.data) {
      // Removed sensitive data logging for security
      return { planData: { plan: {} } };
    }

    // Check if data is nested under a 'data' property (common API pattern)
    const data = apiData.data ? apiData.data : apiData;

    // Ensure planData exists and has a plan property
    if (!data.planData) {
      // Removed sensitive data logging for security
      return { planData: { plan: {} } };
    }

    if (!data.planData.plan) {
      // Removed sensitive data logging for security
      data.planData.plan = {};
    }

    return data;
  },
  // Removed getSecurityControlsHistory for now, can be added back if needed for a specific feature

  /**
   * Get all available security controls from the database
   * @returns {Promise<Array>} Array of security control objects
   */
  getAvailableSecurityControls: async () => {
    // Removed sensitive data logging for security
    try {
      // Call the API endpoint for security controls
      const response = await api.get('/security-controls');

      if (response.success && Array.isArray(response.data)) {
        // Removed sensitive data logging for security
        return response.data;
      } else {
        // Removed sensitive data logging for security
        return [];
      }
    } catch (error) {
      // Log without revealing sensitive data
      console.error('Error fetching available security controls');
      throw error;
    }
  }
};

export default securityControlsService;