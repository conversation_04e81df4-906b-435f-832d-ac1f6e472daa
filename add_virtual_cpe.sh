#!/bin/bash

FILE="src/services/ctiService.js"

# Insert the searchByVirtualCPE function before searchByVendorProduct (line 258)
sed -i '257a\
\
  // Search by virtual CPE (partial CPE with wildcards)\
  async searchByVirtualCPE(asset) {\
    if (!asset.vendor) return [];\
\
    // Create virtual CPE with wildcards for broader matching\
    const virtualCPE = `cpe:2.3:*:${asset.vendor.toLowerCase()}:*`;\
    console.log(`[CTI] Searching by virtual CPE: ${virtualCPE}`);\
\
    const params = new URLSearchParams({\
      virtualMatchString: virtualCPE,\
      resultsPerPage: '"'"'10'"'"',\
      startIndex: '"'"'0'"'"'\
    });\
\
    try {\
      const response = await fetch(`${this.nistBaseUrl}?${params}`, {\
        method: '"'"'GET'"'"',\
        headers: {\
          '"'"'Accept'"'"': '"'"'application/json'"'"',\
        }\
      });\
\
      if (!response.ok) {\
        console.error(`[CTI] NIST virtual CPE search error: ${response.status} ${response.statusText}`);\
        return [];\
      }\
\
      const data = await response.json();\
      console.log(`[CTI] Virtual CPE search response for ${asset.name}:`, {\
        totalResults: data.totalResults,\
        vulnerabilitiesCount: data.vulnerabilities?.length || 0\
      });\
\
      return this.parseVulnerabilities(data);\
    } catch (error) {\
      console.error(`[CTI] Virtual CPE search failed for ${asset.name}:`, error);\
      return [];\
    }\
  }' "$FILE"

echo "Added searchByVirtualCPE function to ctiService.js"
