// src/components/Atelier4/Activite1/AttackSequenceForm.js
import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { X, Plus, Trash2, Save, AlertCircle } from 'lucide-react';

const AttackSequenceForm = ({ path, phases, onSave, onCancel }) => {
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    likelihood: 'V2',
    phases: []
  });
  const [errors, setErrors] = useState({});

  // Likelihood levels
  const likelihoodLevels = {
    'V4': 'Quasi-certain',
    'V3': 'Très vraisemblable', 
    'V2': 'Vraisemblable',
    'V1': 'Peu vraisemblable',
    'V0': 'Invraisemblable'
  };

  // Difficulty levels
  const difficultyLevels = {
    0: 'Négligeable',
    1: 'Faible',
    2: 'Modérée',
    3: 'Élevée',
    4: 'Très élevée'
  };

  // Probability levels
  const probabilityLevels = {
    0: 'Très faible',
    1: 'Faible',
    2: 'Significative',
    3: 'Très élevée',
    4: 'Quasi-certaine'
  };

  useEffect(() => {
    if (path) {
      setFormData({
        title: path.title || '',
        description: path.description || '',
        likelihood: path.likelihood || 'V2',
        phases: path.phases || []
      });
    } else {
      // Initialize with empty phases
      const initialPhases = phases.map(phase => ({
        phase: phase.id,
        actions: []
      }));
      setFormData(prev => ({
        ...prev,
        phases: initialPhases
      }));
    }
  }, [path, phases]);

  const validateForm = () => {
    const newErrors = {};

    if (!formData.title.trim()) {
      newErrors.title = 'Le titre est requis';
    }

    if (!formData.description.trim()) {
      newErrors.description = 'La description est requise';
    }

    // Check if at least one action exists
    const hasActions = formData.phases.some(phase => phase.actions.length > 0);
    if (!hasActions) {
      newErrors.actions = 'Au moins une action élémentaire est requise';
    }

    // Validate each action
    formData.phases.forEach((phase, phaseIndex) => {
      phase.actions.forEach((action, actionIndex) => {
        if (!action.title?.trim()) {
          newErrors[`phase_${phaseIndex}_action_${actionIndex}_title`] = 'Le titre de l\'action est requis';
        }
      });
    });

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    if (validateForm()) {
      onSave(formData);
    }
  };

  const addAction = (phaseIndex) => {
    const newAction = {
      id: `action_${Date.now()}`,
      title: '',
      description: '',
      difficulty: 1,
      probability: 2
    };

    setFormData(prev => ({
      ...prev,
      phases: prev.phases.map((phase, index) => 
        index === phaseIndex 
          ? { ...phase, actions: [...phase.actions, newAction] }
          : phase
      )
    }));
  };

  const removeAction = (phaseIndex, actionIndex) => {
    setFormData(prev => ({
      ...prev,
      phases: prev.phases.map((phase, index) => 
        index === phaseIndex 
          ? { ...phase, actions: phase.actions.filter((_, idx) => idx !== actionIndex) }
          : phase
      )
    }));
  };

  const updateAction = (phaseIndex, actionIndex, field, value) => {
    setFormData(prev => ({
      ...prev,
      phases: prev.phases.map((phase, pIndex) => 
        pIndex === phaseIndex 
          ? {
              ...phase,
              actions: phase.actions.map((action, aIndex) => 
                aIndex === actionIndex 
                  ? { ...action, [field]: value }
                  : action
              )
            }
          : phase
      )
    }));
  };

  const getPhaseInfo = (phaseId) => {
    return phases.find(p => p.id === phaseId);
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        className="bg-white rounded-xl shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden"
      >
        {/* Header */}
        <div className="flex justify-between items-center p-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-800">
            {path ? 'Modifier le scénario' : 'Nouveau scénario opérationnel'}
          </h2>
          <button
            onClick={onCancel}
            className="text-gray-400 hover:text-gray-600 p-1 rounded-full hover:bg-gray-100"
          >
            <X size={20} />
          </button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="overflow-y-auto max-h-[calc(90vh-140px)]">
          <div className="p-6 space-y-6">
            {/* Basic Information */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Titre du scénario *
                </label>
                <input
                  type="text"
                  value={formData.title}
                  onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
                  className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                    errors.title ? 'border-red-300' : 'border-gray-300'
                  }`}
                  placeholder="Ex: Espionnage données R&D"
                />
                {errors.title && (
                  <p className="mt-1 text-sm text-red-600">{errors.title}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Vraisemblance
                </label>
                <select
                  value={formData.likelihood}
                  onChange={(e) => setFormData(prev => ({ ...prev, likelihood: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  {Object.entries(likelihoodLevels).map(([key, label]) => (
                    <option key={key} value={key}>{key} - {label}</option>
                  ))}
                </select>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Description *
              </label>
              <textarea
                value={formData.description}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                rows={3}
                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                  errors.description ? 'border-red-300' : 'border-gray-300'
                }`}
                placeholder="Décrivez l'objectif et le contexte du scénario d'attaque"
              />
              {errors.description && (
                <p className="mt-1 text-sm text-red-600">{errors.description}</p>
              )}
            </div>

            {/* Attack Phases */}
            <div>
              <h3 className="text-lg font-medium text-gray-800 mb-4">Séquence d'attaque</h3>
              {errors.actions && (
                <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-lg flex items-center">
                  <AlertCircle size={16} className="text-red-500 mr-2" />
                  <span className="text-sm text-red-600">{errors.actions}</span>
                </div>
              )}

              <div className="space-y-6">
                {formData.phases.map((phaseData, phaseIndex) => {
                  const phaseInfo = getPhaseInfo(phaseData.phase);
                  const IconComponent = phaseInfo?.icon;

                  return (
                    <div key={phaseData.phase} className="border border-gray-200 rounded-lg p-4">
                      <div className="flex items-center justify-between mb-4">
                        <div className="flex items-center">
                          <div className={`${phaseInfo?.color} p-2 rounded-lg mr-3`}>
                            {IconComponent && <IconComponent size={20} className="text-white" />}
                          </div>
                          <div>
                            <h4 className="font-medium text-gray-800">{phaseInfo?.name}</h4>
                            <p className="text-sm text-gray-600">{phaseInfo?.description}</p>
                          </div>
                        </div>
                        <button
                          type="button"
                          onClick={() => addAction(phaseIndex)}
                          className="flex items-center px-3 py-1 text-sm bg-blue-50 text-blue-600 rounded-lg hover:bg-blue-100"
                        >
                          <Plus size={14} className="mr-1" />
                          Ajouter action
                        </button>
                      </div>

                      {/* Actions */}
                      <div className="space-y-3">
                        {phaseData.actions.map((action, actionIndex) => (
                          <div key={actionIndex} className="bg-gray-50 p-3 rounded-lg">
                            <div className="flex justify-between items-start mb-3">
                              <div className="flex-1 grid grid-cols-1 md:grid-cols-2 gap-3">
                                <div>
                                  <input
                                    type="text"
                                    value={action.title}
                                    onChange={(e) => updateAction(phaseIndex, actionIndex, 'title', e.target.value)}
                                    placeholder="Titre de l'action"
                                    className={`w-full px-3 py-2 border rounded-lg text-sm ${
                                      errors[`phase_${phaseIndex}_action_${actionIndex}_title`] 
                                        ? 'border-red-300' : 'border-gray-300'
                                    }`}
                                  />
                                  {errors[`phase_${phaseIndex}_action_${actionIndex}_title`] && (
                                    <p className="mt-1 text-xs text-red-600">
                                      {errors[`phase_${phaseIndex}_action_${actionIndex}_title`]}
                                    </p>
                                  )}
                                </div>
                                <div className="flex space-x-2">
                                  <div className="flex-1">
                                    <label className="block text-xs text-gray-600 mb-1">Difficulté</label>
                                    <select
                                      value={action.difficulty}
                                      onChange={(e) => updateAction(phaseIndex, actionIndex, 'difficulty', parseInt(e.target.value))}
                                      className="w-full px-2 py-1 border border-gray-300 rounded text-sm"
                                    >
                                      {Object.entries(difficultyLevels).map(([key, label]) => (
                                        <option key={key} value={key}>{label}</option>
                                      ))}
                                    </select>
                                  </div>
                                  <div className="flex-1">
                                    <label className="block text-xs text-gray-600 mb-1">Probabilité</label>
                                    <select
                                      value={action.probability}
                                      onChange={(e) => updateAction(phaseIndex, actionIndex, 'probability', parseInt(e.target.value))}
                                      className="w-full px-2 py-1 border border-gray-300 rounded text-sm"
                                    >
                                      {Object.entries(probabilityLevels).map(([key, label]) => (
                                        <option key={key} value={key}>{label}</option>
                                      ))}
                                    </select>
                                  </div>
                                </div>
                              </div>
                              <button
                                type="button"
                                onClick={() => removeAction(phaseIndex, actionIndex)}
                                className="ml-2 text-red-500 hover:text-red-700 p-1 rounded hover:bg-red-50"
                              >
                                <Trash2 size={14} />
                              </button>
                            </div>
                            <textarea
                              value={action.description}
                              onChange={(e) => updateAction(phaseIndex, actionIndex, 'description', e.target.value)}
                              placeholder="Description détaillée de l'action"
                              rows={2}
                              className="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm"
                            />
                          </div>
                        ))}
                        
                        {phaseData.actions.length === 0 && (
                          <div className="text-center py-4 text-gray-500 text-sm">
                            Aucune action définie pour cette phase
                          </div>
                        )}
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          </div>

          {/* Footer */}
          <div className="flex justify-end space-x-3 p-6 border-t border-gray-200 bg-gray-50">
            <button
              type="button"
              onClick={onCancel}
              className="px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50"
            >
              Annuler
            </button>
            <button
              type="submit"
              className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
            >
              <Save size={16} className="mr-2" />
              {path ? 'Mettre à jour' : 'Créer le scénario'}
            </button>
          </div>
        </form>
      </motion.div>
    </div>
  );
};

export default AttackSequenceForm;