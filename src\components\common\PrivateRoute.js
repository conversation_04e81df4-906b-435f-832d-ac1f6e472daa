// src/components/common/PrivateRoute.js - Enhanced version
import React from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAuth } from '../../context/AuthContext';
import LoadingSpinner from './LoadingSpinner';

/**
 * Enhanced Private Route component with better handling of
 * loading states and role-based access control
 */
const PrivateRoute = ({ 
  children, 
  roles, 
  showAccessDenied = false
}) => {
  const { user, isLoading, isAuthenticated, checkRole, authCheckComplete } = useAuth();
  const location = useLocation();

  // Display a loading spinner while checking authentication
  if (isLoading || !authCheckComplete) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <LoadingSpinner size="large" color="blue" text="Vérification de l'authentification..." />
      </div>
    );
  }

  // Check if authentication is required
  if (!isAuthenticated) {
    // Redirect to login page with return URL
    return <Navigate to="/login" state={{ from: location }} replace />;
  }

  // Check role-based access (if roles are specified)
  if (roles && isAuthenticated && !checkRole(roles)) {
    // Either show access denied or redirect based on the user's role
    if (showAccessDenied) {
      return <Navigate to="/access-denied" replace />;
    } else {
      // Redirect based on role
      if (user.role === 'superadmin') {
        return <Navigate to="/admin/dashboard" replace />;
      } else if (user.role === 'admin') {
        return <Navigate to="/provider/dashboard" replace />;
      } else if (user.role === 'simpleuser') {
        return <Navigate to="/app" replace />;
      } else {
        return <Navigate to="/" replace />; 
      }
    }
  }

  // All checks passed, render the protected content
  return children;
};

export default PrivateRoute;