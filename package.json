{"name": "ebiosrm-backend", "version": "1.0.0", "description": "Backend for EBIOS RM application", "main": "server.js", "scripts": {"start": "node server.js", "server": "nodemon server.js", "client": "npm start --prefix ../client", "dev": "concurrently \"npm run server\" \"npm run client\"", "data:import": "node utils/seeder", "data:destroy": "node utils/seeder -d", "seed:event-suggestions": "node scripts/seedEventSuggestions.js"}, "author": "Your Name", "license": "MIT", "dependencies": {"@google/generative-ai": "^0.24.0", "axios": "^1.6.0", "bcryptjs": "^2.4.3", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-async-handler": "^1.2.0", "jsonwebtoken": "^9.0.2", "mongoose": "^7.5.0", "uuid": "^9.0.0"}, "devDependencies": {"concurrently": "^8.2.1", "nodemon": "^3.0.1"}}