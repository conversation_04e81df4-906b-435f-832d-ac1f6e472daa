{"name": "my-ebios-app", "version": "0.1.0", "private": true, "dependencies": {"@google/generative-ai": "^0.24.0", "@headlessui/react": "^2.2.0", "@heroicons/react": "^2.2.0", "@tanstack/react-query": "^5.71.1", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.2.0", "@testing-library/user-event": "^13.5.0", "@vercel/speed-insights": "^1.2.0", "axios": "^1.6.5", "date-fns": "^4.1.0", "framer-motion": "^12.6.3", "html-to-image": "^1.11.13", "i18next": "^22.5.1", "i18next-browser-languagedetector": "~7.0.0", "isomorphic-fetch": "^3.0.0", "jspdf": "^3.0.1", "jwt-decode": "^4.0.0", "lodash": "^4.17.21", "lucide-react": "^0.483.0", "react": "^18.2.0", "react-accessible-treeview": "^2.11.1", "react-dom": "^18.2.0", "react-grid-layout": "^1.5.1", "react-hot-toast": "^2.5.2", "react-i18next": "~12.0.0", "react-query": "^3.39.3", "react-resizable": "^3.0.5", "react-router-dom": "^7.3.0", "react-scripts": "5.0.1", "reactflow": "^11.11.4", "recharts": "^2.15.1", "web-vitals": "^2.1.4", "zustand": "^4.4.7"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "proxy": "http://localhost:5000", "devDependencies": {"@babel/core": "^7.26.10", "@babel/preset-env": "^7.26.9", "@babel/preset-react": "^7.26.3", "autoprefixer": "^10.4.21", "postcss": "^8.5.3", "prettier": "^3.5.3", "tailwindcss": "^3.4.0", "webpack": "^5.98.0", "webpack-cli": "^6.0.1", "webpack-dev-server": "^5.2.0"}, "description": "This project was bootstrapped with [Create React App](https://github.com/facebook/create-react-app).", "main": "index.js", "keywords": [], "author": "", "license": "ISC"}