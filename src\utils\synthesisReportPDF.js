// src/utils/synthesisReportPDF.js
import { PDFReportGenerator } from './pdfReportGenerator';

export const generateSynthesisReportPDF = (progressionData, analysisName = 'Analyse EBIOS RM', businessValuesData = null) => {
  const { eventProgression, globalStats, recommendations } = progressionData;

  const pdf = new PDFReportGenerator();

  // Helper function to get business value details
  const getBusinessValueDetails = (eventId, businessValueId) => {
    if (!businessValuesData || !businessValuesData.businessValues) {
      return { name: 'Non définie', supportAssets: [] };
    }

    const businessValue = businessValuesData.businessValues.find(bv =>
      bv.id === businessValueId || bv.id === parseInt(businessValueId)
    );

    if (!businessValue) {
      return { name: 'Non définie', supportAssets: [] };
    }

    return {
      name: businessValue.name || 'Non définie',
      supportAssets: businessValue.supportAssets || []
    };
  };

  // Helper function to format support assets
  const formatSupportAssets = (supportAssets) => {
    if (!supportAssets || supportAssets.length === 0) {
      return 'Aucun';
    }

    return supportAssets.slice(0, 3).map(asset =>
      `${asset.name} (${asset.type || 'Type non défini'})`
    ).join(', ') + (supportAssets.length > 3 ? '...' : '');
  };

  // Helper function to get severity label
  const getSeverityLabel = (severity) => {
    const severityMap = {
      'critical': 'Critique',
      'major': 'Majeur',
      'moderate': 'Modéré',
      'minor': 'Mineur',
      'negligible': 'Négligeable'
    };
    return severityMap[severity] || severity || 'Non définie';
  };

  // Add header
  pdf.addHeader(
    'RAPPORT DE SYNTHÈSE',
    'Progression des Plans de Traitement du Risque (PTR)'
  );

  // Add analysis info
  pdf.addParagraph(`Analyse : ${analysisName}`, { fontStyle: 'bold' });
  pdf.addParagraph(`Nombre d'événements redoutés analysés : ${eventProgression.length}`);
  pdf.addSpace(10);

  // Executive summary
  pdf.addSectionHeading('RÉSUMÉ EXÉCUTIF');
  pdf.addParagraph(
    'Cette analyse présente l\'état d\'avancement des Plans de Traitement du Risque (PTR) en comparant les mesures définies dans l\'Atelier 1 (mesures initiales) avec celles développées dans l\'Atelier 3 (mesures écosystémiques détaillées).'
  );
  pdf.addSpace(5);

  // Global statistics
  pdf.addStatsBox({
    'Événements enrichis': globalStats.enrichedEvents || 0,
    'Événements partiels': globalStats.partiallyTreatedEvents || 0,
    'Événements non traités': globalStats.untreatedEvents || 0,
    'Taux d\'enrichissement': `${globalStats.enrichmentRate || 0}%`,
    'Taux de traitement': `${globalStats.treatmentRate || 0}%`,
    'Score de cohérence': `${globalStats.consistencyScore || 0}%`
  });

  // Add visual progress indicators
  pdf.addSectionHeading('INDICATEURS DE PROGRESSION');
  pdf.addProgressBar('Taux d\'enrichissement', globalStats.enrichmentRate || 0, 100, { color: '#059669' });
  pdf.addProgressBar('Taux de traitement global', globalStats.treatmentRate || 0, 100, { color: '#1e40af' });
  pdf.addProgressBar('Score de cohérence', globalStats.consistencyScore || 0, 100, { color: '#7c3aed' });
  pdf.addSpace(10);

  // Add distribution chart with better visualization
  const distributionData = [
    { label: 'Enrichis', value: globalStats.enrichedEvents || 0 },
    { label: 'Partiels', value: globalStats.partiallyTreatedEvents || 0 },
    { label: 'Non traités', value: globalStats.untreatedEvents || 0 }
  ];
  pdf.addDonutChart(distributionData, 'RÉPARTITION DES ÉVÉNEMENTS PAR STATUT');
  pdf.addSpace(5);

  // Events by status
  pdf.addSectionHeading('ANALYSE PAR STATUT');

  // Enriched events
  const enrichedEvents = eventProgression.filter(e => e.status === 'enriched');
  if (enrichedEvents.length > 0) {
    pdf.addSectionHeading('🟢 ÉVÉNEMENTS ENRICHIS', 2);
    pdf.addParagraph(
      `Ces événements bénéficient d'un traitement complet avec des mesures définies dans les deux ateliers (${enrichedEvents.length} événements) :`
    );

    enrichedEvents.forEach(event => {
      pdf.addBulletPoint(
        `${event.name} (${event.gravity}) - ${event.treatmentDetails.a1Count} mesure(s) A1, ${event.treatmentDetails.a3Count} mesure(s) A3`
      );
    });
    pdf.addSpace(5);
  }

  // Partially treated events
  const partialEvents = eventProgression.filter(e => e.status === 'partially-treated');
  if (partialEvents.length > 0) {
    pdf.addSectionHeading('🟡 ÉVÉNEMENTS PARTIELLEMENT TRAITÉS', 2);
    pdf.addParagraph(
      `Ces événements nécessitent une attention particulière car ils ne sont traités que par un seul atelier (${partialEvents.length} événements) :`
    );

    partialEvents.forEach(event => {
      const treatment = event.treatmentDetails.hasA1
        ? `${event.treatmentDetails.a1Count} mesure(s) A1 uniquement`
        : `${event.treatmentDetails.a3Count} mesure(s) A3 uniquement`;
      pdf.addBulletPoint(`${event.name} (${event.gravity}) - ${treatment}`);
    });
    pdf.addSpace(5);
  }

  // Untreated events
  const untreatedEvents = eventProgression.filter(e => e.status === 'untreated');
  if (untreatedEvents.length > 0) {
    pdf.addSectionHeading('🔴 ÉVÉNEMENTS NON TRAITÉS', 2);
    pdf.addParagraph(
      `Ces événements nécessitent une action immédiate car aucune mesure n'a été définie (${untreatedEvents.length} événements) :`
    );

    untreatedEvents.forEach(event => {
      pdf.addBulletPoint(`${event.name} (${event.gravity}) - Aucune mesure définie`);
    });
    pdf.addSpace(5);
  }

  // Detailed table with business values and support assets
  pdf.addSectionHeading('TABLEAU DÉTAILLÉ DES ÉVÉNEMENTS');

  const tableHeaders = [
    'Événement Redouté',
    'Gravité',
    'Valeur Métier',
    'Biens Supports',
    'Pilier',
    'A1',
    'A3',
    'Statut'
  ];

  // Custom column widths for better readability
  const columnWidths = [35, 15, 25, 30, 15, 8, 8, 15]; // Total: 151mm (fits in ~170mm available)

  const tableRows = eventProgression.map(event => {
    // Get business value details for this event
    const businessValueDetails = getBusinessValueDetails(event.id, event.businessValue);
    const supportAssetsText = formatSupportAssets(businessValueDetails.supportAssets);

    return [
      event.name.length > 25 ? event.name.substring(0, 25) + '...' : event.name,
      getSeverityLabel(event.severity || event.gravity),
      businessValueDetails.name.length > 18 ? businessValueDetails.name.substring(0, 18) + '...' : businessValueDetails.name,
      supportAssetsText.length > 25 ? supportAssetsText.substring(0, 25) + '...' : supportAssetsText,
      event.securityPillar.length > 12 ? event.securityPillar.substring(0, 12) + '...' : event.securityPillar,
      event.treatmentDetails.a1Count.toString(),
      event.treatmentDetails.a3Count.toString(),
      event.statusLabel
    ];
  });

  pdf.addTable(tableHeaders, tableRows, {
    fontSize: 8,
    rowHeight: 12,
    columnWidths: columnWidths
  });

  // Detailed business values and support assets section
  pdf.addSectionHeading('DÉTAIL DES VALEURS MÉTIER ET BIENS SUPPORTS');

  eventProgression.forEach((event, index) => {
    const businessValueDetails = getBusinessValueDetails(event.id, event.businessValue);

    // Add page break for every 3 events to prevent overcrowding
    if (index > 0 && index % 3 === 0) {
      pdf.checkPageBreak(60);
    }

    pdf.addSectionHeading(`${event.name}`, 2);
    pdf.addParagraph(
      `Gravité: ${getSeverityLabel(event.severity || event.gravity)} | Pilier: ${event.securityPillar}`,
      {
        fontStyle: 'bold',
        fontSize: 10,
        lineSpacing: 1.1
      }
    );

    // Business value information with better spacing
    pdf.addParagraph(
      `Valeur métier: ${businessValueDetails.name}`,
      {
        indent: 5,
        lineSpacing: 1.2
      }
    );

    // Support assets information with improved formatting
    if (businessValueDetails.supportAssets && businessValueDetails.supportAssets.length > 0) {
      pdf.addParagraph('Biens supports associés:', {
        indent: 5,
        fontStyle: 'bold',
        lineSpacing: 1.1
      });

      businessValueDetails.supportAssets.slice(0, 5).forEach(asset => { // Limit to 5 assets to prevent overflow
        const assetText = `${asset.name} (${asset.type || 'Type non défini'})`;
        const description = asset.description ? ` - ${asset.description.substring(0, 50)}${asset.description.length > 50 ? '...' : ''}` : '';
        pdf.addBulletPoint(assetText + description, 1);
      });

      if (businessValueDetails.supportAssets.length > 5) {
        pdf.addParagraph(`... et ${businessValueDetails.supportAssets.length - 5} autre(s) bien(s) support`, {
          indent: 15,
          color: '#6b7280',
          fontSize: 9
        });
      }
    } else {
      pdf.addParagraph('Aucun bien support défini', {
        indent: 5,
        color: '#6b7280',
        lineSpacing: 1.1
      });
    }

    // Treatment summary with status color
    const statusColor = event.status === 'enriched' ? '#059669' :
                       event.status === 'partially-treated' ? '#d97706' : '#dc2626';

    pdf.addParagraph(
      `Traitement: ${event.treatmentDetails.a1Count} mesure(s) A1, ${event.treatmentDetails.a3Count} mesure(s) A3 - ${event.statusLabel}`,
      {
        indent: 5,
        fontStyle: 'bold',
        color: statusColor,
        lineSpacing: 1.1
      }
    );

    pdf.addSpace(10);
  });

  // Recommendations
  if (recommendations && recommendations.length > 0) {
    pdf.addSectionHeading('RECOMMANDATIONS PRIORITAIRES');

    const criticalRecs = recommendations.filter(r => r.type === 'critical');
    const improvementRecs = recommendations.filter(r => r.type === 'improvement');

    if (criticalRecs.length > 0) {
      pdf.addSectionHeading('Actions Critiques', 2);
      criticalRecs.forEach(rec => {
        pdf.addBulletPoint(`${rec.message} - ${rec.action}`);
      });
      pdf.addSpace(5);
    }

    if (improvementRecs.length > 0) {
      pdf.addSectionHeading('Améliorations Suggérées', 2);
      improvementRecs.forEach(rec => {
        pdf.addBulletPoint(`${rec.message} - ${rec.action}`);
      });
      pdf.addSpace(5);
    }
  }

  // Analysis by security pillar with charts
  pdf.addSectionHeading('ANALYSE PAR PILIER DE SÉCURITÉ');

  const pillarStats = {};
  eventProgression.forEach(event => {
    const pillar = event.securityPillar || 'Non défini';
    if (!pillarStats[pillar]) {
      pillarStats[pillar] = { total: 0, enriched: 0, partial: 0, untreated: 0 };
    }
    pillarStats[pillar].total++;
    pillarStats[pillar][event.status.replace('-', '')]++;
  });

  // Create bar chart data for pillars
  const pillarChartData = Object.entries(pillarStats).map(([pillar, stats]) => ({
    label: pillar.length > 10 ? pillar.substring(0, 10) + '...' : pillar,
    value: stats.total
  }));

  if (pillarChartData.length > 0) {
    pdf.addBarChart(pillarChartData, 'RÉPARTITION DES ÉVÉNEMENTS PAR PILIER', {
      chartWidth: 140,
      chartHeight: 50
    });
  }

  // Detailed pillar analysis
  Object.entries(pillarStats).forEach(([pillar, stats]) => {
    pdf.addSectionHeading(pillar, 2);
    pdf.addParagraph(
      `Total: ${stats.total} événements - Enrichis: ${stats.enriched || 0}, Partiels: ${stats.partial || 0}, Non traités: ${stats.untreated || 0}`,
      { lineSpacing: 1.1 }
    );

    // Add progress bar for this pillar
    const enrichmentRate = stats.total > 0 ? Math.round((stats.enriched / stats.total) * 100) : 0;
    pdf.addProgressBar(`Taux d'enrichissement ${pillar}`, enrichmentRate, 100, {
      width: 80,
      color: '#059669'
    });
    pdf.addSpace(5);
  });

  pdf.addSpace(10);

  // Conclusion
  pdf.addSectionHeading('CONCLUSION ET PROCHAINES ÉTAPES');

  let conclusion = '';
  const enrichmentRate = globalStats.enrichmentRate || 0;

  if (enrichmentRate >= 80) {
    conclusion = 'L\'analyse montre un excellent niveau de maturité dans le traitement des risques. La majorité des événements redoutés bénéficient d\'un traitement complet et cohérent entre les ateliers 1 et 3.';
  } else if (enrichmentRate >= 60) {
    conclusion = 'L\'analyse révèle un bon niveau de traitement des risques, mais des améliorations sont possibles pour atteindre une couverture complète des événements redoutés.';
  } else if (enrichmentRate >= 40) {
    conclusion = 'L\'analyse indique un niveau de traitement modéré. Il est recommandé de renforcer la cohérence entre les mesures des ateliers 1 et 3.';
  } else {
    conclusion = 'L\'analyse révèle des lacunes importantes dans le traitement des risques. Une action prioritaire est nécessaire pour améliorer la couverture et la cohérence des mesures.';
  }

  pdf.addParagraph(conclusion);
  pdf.addSpace(5);

  // Next steps
  pdf.addSectionHeading('Actions Recommandées', 2);
  pdf.addBulletPoint('Prioriser le traitement des événements non traités ou partiellement traités');
  pdf.addBulletPoint('Renforcer la cohérence entre les mesures des ateliers 1 et 3');
  pdf.addBulletPoint('Développer des mesures écosystémiques pour les événements manquants');
  pdf.addBulletPoint('Planifier des revues périodiques de l\'avancement des PTR');

  return pdf;
};

export const downloadSynthesisReportPDF = (progressionData, analysisName = 'Analyse EBIOS RM', businessValuesData = null) => {
  const pdf = generateSynthesisReportPDF(progressionData, analysisName, businessValuesData);
  const filename = `Rapport_Synthese_PTR_${new Date().toISOString().split('T')[0]}.pdf`;
  pdf.save(filename);
};
