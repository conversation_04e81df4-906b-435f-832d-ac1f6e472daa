// src/components/Atelier 3/Activite1/StakeholderTable.js
import React, { useState } from 'react';
import { Edit, Trash2, ChevronUp, ChevronDown, AlertCircle, Shield, Users, Building } from 'lucide-react';

const StakeholderTable = ({ stakeholders, onEdit, onDelete, isLoading }) => {
  const [sortField, setSortField] = useState('threatLevel');
  const [sortDirection, setSortDirection] = useState('desc');
  
  // Handle sort click
  const handleSort = (field) => {
    if (sortField === field) {
      // Toggle direction if same field
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      // Set new field and default to ascending
      setSortField(field);
      setSortDirection('asc');
    }
  };
  
  // Sort stakeholders
  const sortedStakeholders = [...stakeholders].sort((a, b) => {
    let aValue = a[sortField];
    let bValue = b[sortField];
    
    // Handle string comparison
    if (typeof aValue === 'string' && typeof bValue === 'string') {
      aValue = aValue.toLowerCase();
      bValue = bValue.toLowerCase();
    }
    
    // Compare values
    if (aValue < bValue) return sortDirection === 'asc' ? -1 : 1;
    if (aValue > bValue) return sortDirection === 'asc' ? 1 : -1;
    return 0;
  });
  
  // Get threat zone based on threat level
  const getThreatZone = (threatLevel) => {
    if (threatLevel >= 3) return 'danger';
    if (threatLevel >= 1.5) return 'control';
    if (threatLevel >= 0.5) return 'watch';
    return 'outside';
  };
  
  // Get color for threat zone
  const getThreatZoneColor = (zone) => {
    switch (zone) {
      case 'danger': return 'text-red-600 bg-red-50';
      case 'control': return 'text-orange-600 bg-orange-50';
      case 'watch': return 'text-green-600 bg-green-50';
      default: return 'text-gray-600 bg-gray-50';
    }
  };
  
  // Get text for threat zone
  const getThreatZoneText = (zone) => {
    switch (zone) {
      case 'danger': return 'Zone de danger';
      case 'control': return 'Zone de contrôle';
      case 'watch': return 'Zone de veille';
      default: return 'Hors périmètre';
    }
  };
  
  // Get icon for category
  const getCategoryIcon = (category) => {
    switch (category) {
      case 'client': return <Users size={16} className="text-blue-500" />;
      case 'partner': return <Building size={16} className="text-green-500" />;
      case 'provider': return <Shield size={16} className="text-purple-500" />;
      case 'technical': return <AlertCircle size={16} className="text-orange-500" />;
      case 'business': return <Building size={16} className="text-indigo-500" />;
      case 'subsidiary': return <Building size={16} className="text-pink-500" />;
      default: return <Users size={16} className="text-gray-500" />;
    }
  };
  
  // Get text for category
  const getCategoryText = (category) => {
    switch (category) {
      case 'client': return 'Client';
      case 'partner': return 'Partenaire';
      case 'provider': return 'Prestataire';
      case 'technical': return 'Service technique';
      case 'business': return 'Service métier';
      case 'subsidiary': return 'Filiale';
      default: return category;
    }
  };
  
  // Get text for type
  const getTypeText = (type) => {
    return type === 'internal' ? 'Interne' : 'Externe';
  };
  
  // Get text for rank
  const getRankText = (rank) => {
    return `Rang ${rank}`;
  };
  
  // Render sort icon
  const renderSortIcon = (field) => {
    if (sortField !== field) return null;
    
    return sortDirection === 'asc' 
      ? <ChevronUp size={16} className="ml-1" />
      : <ChevronDown size={16} className="ml-1" />;
  };
  
  // Render loading state
  if (isLoading) {
    return (
      <div className="p-8 text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto"></div>
        <p className="mt-4 text-gray-600">Chargement des parties prenantes...</p>
      </div>
    );
  }
  
  // Render empty state
  if (stakeholders.length === 0) {
    return (
      <div className="p-8 text-center">
        <div className="bg-blue-50 p-4 rounded-full inline-flex items-center justify-center mb-4">
          <Users size={32} className="text-blue-500" />
        </div>
        <h3 className="text-lg font-medium text-gray-900 mb-2">Aucune partie prenante</h3>
        <p className="text-gray-600 mb-4">
          Ajoutez des parties prenantes pour commencer à cartographier l'écosystème.
        </p>
      </div>
    );
  }
  
  return (
    <div className="overflow-x-auto">
      <table className="min-w-full divide-y divide-gray-200">
        <thead className="bg-gray-50">
          <tr>
            <th 
              scope="col" 
              className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
              onClick={() => handleSort('name')}
            >
              <div className="flex items-center">
                Nom {renderSortIcon('name')}
              </div>
            </th>
            <th 
              scope="col" 
              className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
              onClick={() => handleSort('category')}
            >
              <div className="flex items-center">
                Catégorie {renderSortIcon('category')}
              </div>
            </th>
            <th 
              scope="col" 
              className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
              onClick={() => handleSort('type')}
            >
              <div className="flex items-center">
                Type {renderSortIcon('type')}
              </div>
            </th>
            <th 
              scope="col" 
              className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
              onClick={() => handleSort('rank')}
            >
              <div className="flex items-center">
                Rang {renderSortIcon('rank')}
              </div>
            </th>
            <th 
              scope="col" 
              className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
              onClick={() => handleSort('dependency')}
            >
              <div className="flex items-center">
                Dépendance {renderSortIcon('dependency')}
              </div>
            </th>
            <th 
              scope="col" 
              className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
              onClick={() => handleSort('penetration')}
            >
              <div className="flex items-center">
                Pénétration {renderSortIcon('penetration')}
              </div>
            </th>
            <th 
              scope="col" 
              className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
              onClick={() => handleSort('cyberMaturity')}
            >
              <div className="flex items-center">
                Maturité {renderSortIcon('cyberMaturity')}
              </div>
            </th>
            <th 
              scope="col" 
              className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
              onClick={() => handleSort('trust')}
            >
              <div className="flex items-center">
                Confiance {renderSortIcon('trust')}
              </div>
            </th>
            <th 
              scope="col" 
              className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
              onClick={() => handleSort('threatLevel')}
            >
              <div className="flex items-center">
                Menace {renderSortIcon('threatLevel')}
              </div>
            </th>
            <th 
              scope="col" 
              className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
            >
              Zone
            </th>
            <th 
              scope="col" 
              className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"
            >
              Actions
            </th>
          </tr>
        </thead>
        <tbody className="bg-white divide-y divide-gray-200">
          {sortedStakeholders.map((stakeholder) => {
            const threatZone = getThreatZone(stakeholder.threatLevel);
            const zoneColor = getThreatZoneColor(threatZone);
            
            return (
              <tr key={stakeholder.id} className="hover:bg-gray-50">
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="font-medium text-gray-900">{stakeholder.name}</div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center">
                    {getCategoryIcon(stakeholder.category)}
                    <span className="ml-2">{getCategoryText(stakeholder.category)}</span>
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  {getTypeText(stakeholder.type)}
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  {getRankText(stakeholder.rank)}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-center">
                  {stakeholder.dependency}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-center">
                  {stakeholder.penetration}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-center">
                  {stakeholder.cyberMaturity}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-center">
                  {stakeholder.trust}
                </td>
                <td className="px-6 py-4 whitespace-nowrap font-medium">
                  {stakeholder.threatLevel.toFixed(2)}
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${zoneColor}`}>
                    {getThreatZoneText(threatZone)}
                  </span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                  <button
                    onClick={() => onEdit(stakeholder)}
                    className="text-blue-600 hover:text-blue-900 mr-3"
                  >
                    <Edit size={18} />
                  </button>
                  <button
                    onClick={() => onDelete(stakeholder.id)}
                    className="text-red-600 hover:text-red-900"
                  >
                    <Trash2 size={18} />
                  </button>
                </td>
              </tr>
            );
          })}
        </tbody>
      </table>
    </div>
  );
};

export default StakeholderTable;
