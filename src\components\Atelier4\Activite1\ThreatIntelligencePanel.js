// src/components/Atelier4/Activite1/ThreatIntelligencePanel.js
import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  Shield,
  AlertTriangle,
  Search,
  Database,
  Target,
  ChevronDown,
  ChevronRight,
  ExternalLink,
  Info,
  Loader2,
  RefreshCw
} from 'lucide-react';
import { useAnalysis } from '../../../context/AnalysisContext';
import { api } from '../../../api/apiClient';
import { useTranslation } from 'react-i18next';

const ThreatIntelligencePanel = ({ selectedAttackPath, businessAssets = [], onThreatIntelligenceUpdate }) => {
  const { t } = useTranslation();
  const { currentAnalysis } = useAnalysis();
  const [threatData, setThreatData] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [assetKeywords, setAssetKeywords] = useState({});
  const [expandedAssets, setExpandedAssets] = useState({});
  const [expandedCVEs, setExpandedCVEs] = useState({});

  // Initialize asset keywords from business assets
  useEffect(() => {
    if (businessAssets && businessAssets.length > 0) {
      const initialKeywords = {};
      businessAssets.forEach(asset => {
        initialKeywords[asset.id || asset.name] = asset.technicalKeywords || '';
      });
      setAssetKeywords(initialKeywords);
    }
  }, [businessAssets]);

  // Handle asset keyword changes
  const handleKeywordChange = (assetId, keywords) => {
    setAssetKeywords(prev => ({
      ...prev,
      [assetId]: keywords
    }));
  };

  // Fetch threat intelligence
  const fetchThreatIntelligence = async () => {
    if (!currentAnalysis?.id) {
      setError('No analysis selected');
      return;
    }

    // Get keywords from all assets
    const keywordsList = Object.values(assetKeywords)
      .filter(keywords => keywords && keywords.trim())
      .map(keywords => keywords.trim());

    if (keywordsList.length === 0) {
      setError('Please enter technical keywords for at least one asset');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      console.log('[ThreatIntelligencePanel] Generating mock threat intelligence for keywords:', keywordsList);

      // For now, generate mock threat intelligence data
      // This will be replaced with real API calls when the backend services are implemented
      const mockThreatData = generateMockThreatIntelligence(keywordsList);

      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 2000));

      setThreatData(mockThreatData);
      console.log('[ThreatIntelligencePanel] Mock threat intelligence generated:', mockThreatData);

      // Notify parent component about the threat intelligence update
      if (onThreatIntelligenceUpdate) {
        onThreatIntelligenceUpdate(mockThreatData);
      }
    } catch (error) {
      console.error('[ThreatIntelligencePanel] Error generating threat intelligence:', error);
      setError(error.message || 'Failed to generate threat intelligence');
    } finally {
      setLoading(false);
    }
  };

  // Generate mock threat intelligence data
  const generateMockThreatIntelligence = (keywords) => {
    const mockCVEs = [
      {
        cveId: 'CVE-2023-4911',
        description: 'Buffer overflow vulnerability in glibc\'s ld.so',
        severity: 'HIGH',
        score: 7.8,
        publishedDate: '2023-10-03',
        attackTechniques: [
          { techniqueId: 'T1055', techniqueName: 'Process Injection', tactic: 'Defense Evasion' },
          { techniqueId: 'T1068', techniqueName: 'Exploitation for Privilege Escalation', tactic: 'Privilege Escalation' }
        ]
      },
      {
        cveId: 'CVE-2023-38545',
        description: 'Heap buffer overflow in curl\'s SOCKS5 proxy handshake',
        severity: 'HIGH',
        score: 9.8,
        publishedDate: '2023-10-11',
        attackTechniques: [
          { techniqueId: 'T1190', techniqueName: 'Exploit Public-Facing Application', tactic: 'Initial Access' },
          { techniqueId: 'T1203', techniqueName: 'Exploitation for Client Execution', tactic: 'Execution' }
        ]
      },
      {
        cveId: 'CVE-2023-22515',
        description: 'Privilege escalation vulnerability in Atlassian Confluence',
        severity: 'CRITICAL',
        score: 10.0,
        publishedDate: '2023-10-04',
        attackTechniques: [
          { techniqueId: 'T1068', techniqueName: 'Exploitation for Privilege Escalation', tactic: 'Privilege Escalation' },
          { techniqueId: 'T1190', techniqueName: 'Exploit Public-Facing Application', tactic: 'Initial Access' }
        ]
      }
    ];

    const mockTechniques = [
      {
        techniqueId: 'T1190',
        techniqueName: 'Exploit Public-Facing Application',
        tactic: 'Initial Access',
        description: 'Adversaries may attempt to take advantage of a weakness in an Internet-facing computer or program',
        platforms: ['Linux', 'Windows', 'macOS'],
        associatedCVEs: ['CVE-2023-38545', 'CVE-2023-22515'],
        confidence: 'HIGH'
      },
      {
        techniqueId: 'T1055',
        techniqueName: 'Process Injection',
        tactic: 'Defense Evasion',
        description: 'Adversaries may inject code into processes in order to evade process-based defenses',
        platforms: ['Linux', 'Windows', 'macOS'],
        associatedCVEs: ['CVE-2023-4911'],
        confidence: 'MEDIUM'
      },
      {
        techniqueId: 'T1068',
        techniqueName: 'Exploitation for Privilege Escalation',
        tactic: 'Privilege Escalation',
        description: 'Adversaries may exploit software vulnerabilities in an attempt to elevate privileges',
        platforms: ['Linux', 'Windows', 'macOS'],
        associatedCVEs: ['CVE-2023-4911', 'CVE-2023-22515'],
        confidence: 'HIGH'
      }
    ];

    const assetThreats = {};
    keywords.forEach((keyword, index) => {
      // Assign different CVEs to different keywords for variety
      const relevantCVEs = mockCVEs.slice(index % mockCVEs.length, (index % mockCVEs.length) + 2);
      const relevantTechniques = mockTechniques.slice(0, Math.min(3, mockTechniques.length));

      assetThreats[keyword] = {
        vulnerabilities: relevantCVEs,
        techniques: relevantTechniques,
        riskScore: Math.round((7.5 + (index * 0.5) + Math.random()) * 10) / 10,
        recommendations: [
          {
            type: 'CRITICAL',
            title: 'Update System Components',
            description: `Update ${keyword} to latest version to address known vulnerabilities`,
            priority: 'HIGH'
          },
          {
            type: 'TACTICAL',
            title: 'Implement Monitoring',
            description: `Deploy monitoring solutions to detect exploitation attempts targeting ${keyword}`,
            priority: 'MEDIUM'
          }
        ]
      };
    });

    return {
      assetThreats,
      globalTechniques: mockTechniques,
      summary: {
        totalAssets: keywords.length,
        totalCVEs: mockCVEs.length,
        totalTechniques: mockTechniques.length,
        severityDistribution: { HIGH: 2, MEDIUM: 0, LOW: 0, CRITICAL: 1, UNKNOWN: 0 },
        tacticDistribution: {
          'Initial Access': 2,
          'Defense Evasion': 1,
          'Privilege Escalation': 2,
          'Execution': 1
        }
      }
    };
  };

  // Toggle asset expansion
  const toggleAssetExpansion = (assetKey) => {
    setExpandedAssets(prev => ({
      ...prev,
      [assetKey]: !prev[assetKey]
    }));
  };

  // Toggle CVE expansion
  const toggleCVEExpansion = (cveId) => {
    setExpandedCVEs(prev => ({
      ...prev,
      [cveId]: !prev[cveId]
    }));
  };

  // Get severity color
  const getSeverityColor = (severity) => {
    switch (severity?.toUpperCase()) {
      case 'CRITICAL': return 'bg-red-100 text-red-800 border-red-200';
      case 'HIGH': return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'MEDIUM': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'LOW': return 'bg-green-100 text-green-800 border-green-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  // Render CVE details
  const renderCVEDetails = (cve) => {
    const isExpanded = expandedCVEs[cve.cveId];

    return (
      <div key={cve.cveId} className="border border-gray-200 rounded-lg p-4 bg-gray-50">
        <div
          className="flex items-center justify-between cursor-pointer"
          onClick={() => toggleCVEExpansion(cve.cveId)}
        >
          <div className="flex items-center space-x-3">
            {isExpanded ? <ChevronDown size={16} /> : <ChevronRight size={16} />}
            <span className="font-mono text-sm font-medium">{cve.cveId}</span>
            <span className={`px-2 py-1 rounded-full text-xs font-medium border ${getSeverityColor(cve.severity)}`}>
              {cve.severity}
            </span>
            {cve.score && (
              <span className="text-sm text-gray-600">
                Score: {cve.score}
              </span>
            )}
          </div>
        </div>

        {isExpanded && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            className="mt-3 space-y-3"
          >
            <p className="text-sm text-gray-700">{cve.description}</p>

            {cve.attackTechniques && cve.attackTechniques.length > 0 && (
              <div>
                <h5 className="text-sm font-medium text-gray-800 mb-2">MITRE ATT&CK Techniques:</h5>
                <div className="flex flex-wrap gap-2">
                  {cve.attackTechniques.map((technique, idx) => (
                    <span
                      key={idx}
                      className="px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs font-mono"
                      title={technique.techniqueName}
                    >
                      {technique.techniqueId}
                    </span>
                  ))}
                </div>
              </div>
            )}

            {cve.publishedDate && (
              <div className="text-xs text-gray-500">
                Published: {new Date(cve.publishedDate).toLocaleDateString()}
              </div>
            )}
          </motion.div>
        )}
      </div>
    );
  };

  // Render asset threat information
  const renderAssetThreats = (assetKey, assetData) => {
    const isExpanded = expandedAssets[assetKey];
    const vulnerabilityCount = assetData.vulnerabilities?.length || 0;
    const techniqueCount = assetData.techniques?.length || 0;

    return (
      <div key={assetKey} className="border border-gray-200 rounded-lg overflow-hidden">
        <div
          className="bg-white p-4 cursor-pointer hover:bg-gray-50 transition-colors"
          onClick={() => toggleAssetExpansion(assetKey)}
        >
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              {isExpanded ? <ChevronDown size={16} /> : <ChevronRight size={16} />}
              <Target size={16} className="text-blue-600" />
              <span className="font-medium text-gray-800">{assetKey}</span>
            </div>
            <div className="flex items-center space-x-4 text-sm text-gray-600">
              <span className="flex items-center">
                <AlertTriangle size={14} className="mr-1" />
                {vulnerabilityCount} CVEs
              </span>
              <span className="flex items-center">
                <Shield size={14} className="mr-1" />
                {techniqueCount} Techniques
              </span>
              {assetData.riskScore && (
                <span className={`px-2 py-1 rounded text-xs font-medium ${
                  assetData.riskScore >= 7 ? 'bg-red-100 text-red-800' :
                  assetData.riskScore >= 4 ? 'bg-yellow-100 text-yellow-800' :
                  'bg-green-100 text-green-800'
                }`}>
                  Risk: {assetData.riskScore}/10
                </span>
              )}
            </div>
          </div>
        </div>

        {isExpanded && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="border-t border-gray-200 p-4 bg-gray-50 space-y-4"
          >
            {/* Vulnerabilities */}
            {assetData.vulnerabilities && assetData.vulnerabilities.length > 0 && (
              <div>
                <h4 className="font-medium text-gray-800 mb-3 flex items-center">
                  <AlertTriangle size={16} className="mr-2 text-orange-600" />
                  Vulnerabilities ({assetData.vulnerabilities.length})
                </h4>
                <div className="space-y-2">
                  {assetData.vulnerabilities.map(cve => renderCVEDetails(cve))}
                </div>
              </div>
            )}

            {/* Recommendations */}
            {assetData.recommendations && assetData.recommendations.length > 0 && (
              <div>
                <h4 className="font-medium text-gray-800 mb-3 flex items-center">
                  <Info size={16} className="mr-2 text-blue-600" />
                  Recommendations
                </h4>
                <div className="space-y-2">
                  {assetData.recommendations.map((rec, idx) => (
                    <div key={idx} className={`p-3 rounded-lg border-l-4 ${
                      rec.priority === 'HIGH' ? 'bg-red-50 border-red-400' :
                      rec.priority === 'MEDIUM' ? 'bg-yellow-50 border-yellow-400' :
                      'bg-blue-50 border-blue-400'
                    }`}>
                      <h5 className="font-medium text-sm">{rec.title}</h5>
                      <p className="text-sm text-gray-600 mt-1">{rec.description}</p>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </motion.div>
        )}
      </div>
    );
  };

  return (
    <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-medium text-gray-800 flex items-center">
          <Database size={20} className="mr-2 text-blue-600" />
          Threat Intelligence
        </h3>
        <button
          onClick={fetchThreatIntelligence}
          disabled={loading}
          className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
        >
          {loading ? (
            <Loader2 size={16} className="mr-2 animate-spin" />
          ) : (
            <Search size={16} className="mr-2" />
          )}
          {loading ? 'Analyzing...' : 'Find Threats'}
        </button>
      </div>

      {/* Asset Keywords Input */}
      <div className="space-y-4 mb-6">
        <h4 className="font-medium text-gray-700">Technical Keywords for Assets:</h4>
        {businessAssets && businessAssets.length > 0 ? (
          businessAssets.map(asset => (
            <div key={asset.id || asset.name} className="space-y-2">
              <label className="block text-sm font-medium text-gray-600">
                {asset.name || asset.title}
              </label>
              <input
                type="text"
                value={assetKeywords[asset.id || asset.name] || ''}
                onChange={(e) => handleKeywordChange(asset.id || asset.name, e.target.value)}
                placeholder="e.g., Apache 2.4, Windows Server 2019, MySQL 8.0"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
          ))
        ) : (
          <div className="text-sm text-gray-500 bg-gray-50 p-4 rounded-lg">
            No business assets found. Please configure business assets in Workshop 1 first.
          </div>
        )}
      </div>

      {/* Error Display */}
      {error && (
        <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
          <div className="flex items-center">
            <AlertTriangle size={16} className="text-red-600 mr-2" />
            <span className="text-red-800 text-sm">{error}</span>
          </div>
        </div>
      )}

      {/* Threat Intelligence Results */}
      {threatData && (
        <div className="space-y-6">
          {/* Summary */}
          {threatData.summary && (
            <div className="bg-blue-50 p-4 rounded-lg">
              <h4 className="font-medium text-blue-800 mb-2">Threat Intelligence Summary</h4>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                <div>
                  <span className="text-blue-600 font-medium">Assets:</span>
                  <span className="ml-2">{threatData.summary.totalAssets}</span>
                </div>
                <div>
                  <span className="text-blue-600 font-medium">CVEs:</span>
                  <span className="ml-2">{threatData.summary.totalCVEs}</span>
                </div>
                <div>
                  <span className="text-blue-600 font-medium">Techniques:</span>
                  <span className="ml-2">{threatData.summary.totalTechniques}</span>
                </div>
              </div>
            </div>
          )}

          {/* Asset Threats */}
          {threatData.assetThreats && Object.keys(threatData.assetThreats).length > 0 && (
            <div>
              <h4 className="font-medium text-gray-800 mb-4">Asset-Specific Threats</h4>
              <div className="space-y-4">
                {Object.entries(threatData.assetThreats).map(([assetKey, assetData]) =>
                  renderAssetThreats(assetKey, assetData)
                )}
              </div>
            </div>
          )}
        </div>
      )}

      {/* Empty State */}
      {!threatData && !loading && !error && (
        <div className="text-center py-8 text-gray-500">
          <Database size={48} className="mx-auto mb-4 text-gray-300" />
          <p>Enter technical keywords for your assets and click "Find Threats" to analyze vulnerabilities and attack techniques.</p>
        </div>
      )}
    </div>
  );
};

export default ThreatIntelligencePanel;
