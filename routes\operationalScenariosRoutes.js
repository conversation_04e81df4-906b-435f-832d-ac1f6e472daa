// routes/operationalScenariosRoutes.js
const express = require('express');
const {
  getOperationalScenarios,
  getOperationalScenariosByAttackPath,
  createOperationalScenario,
  updateOperationalScenario,
  deleteOperationalScenario,
  bulkCreateOperationalScenarios,
  getOperationalScenarioStats
} = require('../controllers/operationalScenariosController');

const { protect } = require('../middleware/authMiddleware');

const router = express.Router();

// All routes need authentication (temporarily disabled for testing)
// router.use(protect);

// Operational scenarios routes for a specific analysis
router.route('/analyses/:analysisId/operational-scenarios')
  .get(getOperationalScenarios)
  .post(createOperationalScenario);

// Bulk create operational scenarios
router.route('/analyses/:analysisId/operational-scenarios/bulk')
  .post(bulkCreateOperationalScenarios);

// Get operational scenarios statistics
router.route('/analyses/:analysisId/operational-scenarios/stats')
  .get(getOperationalScenarioStats);

// Get operational scenarios by attack path
router.route('/analyses/:analysisId/operational-scenarios/attack-path/:attackPathId')
  .get(getOperationalScenariosByAttackPath);

// Individual operational scenario routes
router.route('/analyses/:analysisId/operational-scenarios/:scenarioId')
  .put(updateOperationalScenario)
  .delete(deleteOperationalScenario);

// Export operational scenarios (can be extended for different formats)
router.route('/analyses/:analysisId/operational-scenarios/export')
  .get(async (req, res) => {
    try {
      const { analysisId } = req.params;
      const { format = 'json' } = req.query;

      const OperationalScenario = require('../models/OperationalScenario');
      const scenarios = await OperationalScenario.find({ analysisId })
        .populate('createdBy', 'name email')
        .sort({ createdAt: -1 });

      if (format === 'json') {
        res.setHeader('Content-Type', 'application/json');
        res.setHeader('Content-Disposition', `attachment; filename="operational-scenarios-${analysisId}.json"`);
        res.json({
          exportDate: new Date().toISOString(),
          analysisId,
          totalScenarios: scenarios.length,
          scenarios
        });
      } else {
        res.status(400).json({
          success: false,
          message: 'Unsupported export format'
        });
      }
    } catch (error) {
      console.error('Error exporting operational scenarios:', error);
      res.status(500).json({
        success: false,
        message: 'Error exporting operational scenarios',
        error: error.message
      });
    }
  });

module.exports = router;
