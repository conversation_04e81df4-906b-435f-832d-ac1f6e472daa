// src/components/Atelier 2/Activite1/CategorySourcesTable.js
import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Plus, Trash2, Edit, Save, X, AlertCircle, ChevronDown, ChevronUp, FileText, Target, User, Eye, Zap, MessageSquare, Ban, DollarSign, Gamepad2, Sparkles, RefreshCw } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { THREAT_CATEGORIES, getThreatCategories } from './ThreatCategories';
import { useAnalysis } from '../../../context/AnalysisContext';
import { showSuccessToast, showErrorToast } from '../../../utils/toastUtils';
import SliderSelector from '../common/SliderSelector';
import CompactSliderSelector from '../common/CompactSliderSelector';

// Define objectif visé categories
const OBJECTIF_VISE_CATEGORIES = [
  {
    id: 'espionnage',
    name: 'ESPIONNAGE',
    description: 'Opération de renseignement (étatique, économique). Dans de nombreux cas, l\'attaquant s\'installe durablement dans le système d\'information et en toute discrétion.',
    icon: <Eye size={16} />,
    color: 'bg-blue-100 text-blue-800'
  },
  {
    id: 'prepositionnement',
    name: 'PRÉPOSITIONNEMENT STRATÉGIQUE',
    description: 'Prépositionnement visant généralement une attaque sur le long terme, sans que la finalité poursuivie soit clairement établie.',
    icon: <Zap size={16} />,
    color: 'bg-purple-100 text-purple-800'
  },
  {
    id: 'influence',
    name: 'INFLUENCE',
    description: 'Opération visant à diffuser de fausses informations ou à les altérer, mobiliser les leaders d\'opinion sur les réseaux sociaux, détruire des réputations.',
    icon: <MessageSquare size={16} />,
    color: 'bg-yellow-100 text-yellow-800'
  },
  {
    id: 'entrave',
    name: 'ENTRAVE AU FONCTIONNEMENT',
    description: 'Opération de sabotage visant par exemple à rendre indisponible un site Internet, à provoquer une saturation informationnelle.',
    icon: <Ban size={16} />,
    color: 'bg-red-100 text-red-800'
  },
  {
    id: 'lucratif',
    name: 'LUCRATIF',
    description: 'Opération visant un gain financier, de façon directe ou indirecte. Généralement liée au crime organisé.',
    icon: <DollarSign size={16} />,
    color: 'bg-green-100 text-green-800'
  },
  {
    id: 'defi',
    name: 'DÉFI, AMUSEMENT',
    description: 'Opération visant à réaliser un exploit à des fins de reconnaissance sociale, de défi ou de simple amusement.',
    icon: <Gamepad2 size={16} />,
    color: 'bg-indigo-100 text-indigo-800'
  }
];

const CategorySourcesTable = ({ selectedCategories, sourcesRisque, setSourcesRisque, onRequestAiSuggestions, isLoadingAi, analysisData }) => {
  const { t } = useTranslation();

  // Get translated threat categories
  const translatedCategories = getThreatCategories(t);

  // State for the single new source form
  const [newSource, setNewSource] = useState({
    name: '',
    description: '',
    objectifVise: '',
    objectifViseCategory: '',
    motivation: 'faible',
    activite: 'faible',
    ressources: 'faibles',
  });

  // State for objectif visé dropdown
  const [isObjectifDropdownOpen, setIsObjectifDropdownOpen] = useState(false);

  // State for the selected category
  const [selectedCategory, setSelectedCategory] = useState(null);

  // State for editing
  const [editingSource, setEditingSource] = useState(null);
  const [expandedCategories, setExpandedCategories] = useState([]);
  const [isEditObjectifDropdownOpen, setIsEditObjectifDropdownOpen] = useState(false);
  const { saveSourcesDeRisque, getSourcesDeRisque } = useAnalysis();

  // Initialize selected category but keep all categories collapsed initially
  useEffect(() => {
    // Set the first category as selected if there is one and none is currently selected
    if (selectedCategories.length > 0 && !selectedCategory) {
      setSelectedCategory(selectedCategories[0]);
    }

    // Note: We're not setting expandedCategories here to keep all categories collapsed initially
  }, [selectedCategories, selectedCategory]);

  // Add event listener for save event
  useEffect(() => {
    const handleSaveEvent = () => {
      console.log("CategorySourcesTable - Received save-sources-risque event");
      handleSaveAllSources();
    };

    window.addEventListener('save-sources-risque', handleSaveEvent);

    return () => {
      window.removeEventListener('save-sources-risque', handleSaveEvent);
    };
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [sourcesRisque, analysisData]);

  // Handle clicking outside the dropdown
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (isObjectifDropdownOpen && !event.target.closest('.objectif-dropdown-container')) {
        setIsObjectifDropdownOpen(false);
      }
      if (isEditObjectifDropdownOpen && !event.target.closest('.edit-objectif-dropdown-container')) {
        setIsEditObjectifDropdownOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isObjectifDropdownOpen, isEditObjectifDropdownOpen]);

  // Toggle category expansion
  const toggleCategoryExpansion = (categoryId) => {
    setExpandedCategories(prevExpanded => {
      if (prevExpanded.includes(categoryId)) {
        // Remove the category from expanded list (close it)
        return prevExpanded.filter(id => id !== categoryId);
      } else {
        // Add the category to expanded list (open it)
        return [...prevExpanded, categoryId];
      }
    });
  };

  // Handle category selection
  const handleCategorySelect = (categoryId) => {
    setSelectedCategory(categoryId);

    // No longer auto-expanding the selected category
  };

  // Handle adding a new source
  const handleAddSource = () => {
    if (!selectedCategory) {
      showErrorToast(t('workshop2.activity1.messages.selectCategory'));
      return;
    }

    if (!newSource.name.trim()) {
      showErrorToast(t('workshop2.activity1.messages.nameRequired'));
      return;
    }

    if (!newSource.objectifVise.trim()) {
      showErrorToast(t('workshop2.activity1.messages.targetObjectiveRequired'));
      return;
    }

    const sourceToAdd = {
      id: `source-${Date.now()}`,
      ...newSource,
      category: selectedCategory,
      createdAt: new Date().toISOString(),
    };

    setSourcesRisque([...sourcesRisque, sourceToAdd]);

    // Reset the form
    setNewSource({
      name: '',
      description: '',
      objectifVise: '',
      objectifViseCategory: '',
      motivation: 'faible',
      activite: 'faible',
      ressources: 'faibles',
    });

    showSuccessToast(t('workshop2.activity1.messages.sourceAdded'));

    // We no longer auto-expand the category when a source is added
  };

  // Handle editing a source
  const handleEditSource = (source) => {
    setEditingSource({ ...source });
  };

  // Handle saving edited source
  const handleSaveEdit = () => {
    if (!editingSource.name.trim()) {
      showErrorToast(t('workshop2.activity1.messages.nameRequired'));
      return;
    }

    if (!editingSource.objectifVise.trim()) {
      showErrorToast(t('workshop2.activity1.messages.targetObjectiveRequired'));
      return;
    }

    const updatedSources = sourcesRisque.map(source =>
      source.id === editingSource.id ? editingSource : source
    );

    setSourcesRisque(updatedSources);
    setEditingSource(null);
    showSuccessToast(t('workshop2.activity1.messages.sourceUpdated'));
  };

  // Handle deleting a source
  const handleDeleteSource = (sourceId) => {
    const updatedSources = sourcesRisque.filter(source => source.id !== sourceId);
    setSourcesRisque(updatedSources);
    showSuccessToast(t('workshop2.activity1.messages.sourceDeleted'));
  };

  // Handle saving all sources
  const handleSaveAllSources = async () => {
    if (analysisData?.id) {
      try {
        // No loading toast - we'll only show the final result

        // Save the sources
        await saveSourcesDeRisque(analysisData.id, sourcesRisque);

        // Show a success toast
        showSuccessToast(t('workshop2.activity1.messages.sourcesSaved'));

        // Reload the data to ensure we have the latest version
        const updatedSources = await getSourcesDeRisque(analysisData.id);
        if (updatedSources && Array.isArray(updatedSources)) {
          setSourcesRisque(updatedSources);
        }
      } catch (error) {
        console.error('Error saving sources de risque:', error);
        // Show error toast
        showErrorToast(t('workshop2.activity1.messages.saveError'));
      }
    } else {
      showErrorToast(t('workshop2.activity1.messages.noAnalysisSelected'));
    }
  };

  // Get color for activity, resources, and motivation
  const getActivityColor = (level) => {
    switch (level) {
      case 'faible': return 'bg-green-100 text-green-800';
      case 'moyen': return 'bg-yellow-100 text-yellow-800';
      case 'eleve': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getResourcesColor = (level) => {
    switch (level) {
      case 'faibles': return 'bg-green-100 text-green-800';
      case 'moyennes': return 'bg-yellow-100 text-yellow-800';
      case 'importantes': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getMotivationColor = (level) => {
    switch (level) {
      case 'faible': return 'bg-green-100 text-green-800';
      case 'moyen': return 'bg-yellow-100 text-yellow-800';
      case 'eleve': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  // This function is used in the OBJECTIF_VISE_CATEGORIES array above

  // Color maps for sliders
  const activityColorMap = {
    'faible': 'bg-green-600',
    'moyen': 'bg-yellow-600',
    'eleve': 'bg-red-600'
  };

  const resourcesColorMap = {
    'faibles': 'bg-green-600',
    'moyennes': 'bg-yellow-600',
    'importantes': 'bg-red-600'
  };

  const motivationColorMap = {
    'faible': 'bg-green-600',
    'moyen': 'bg-yellow-600',
    'eleve': 'bg-red-600'
  };

  // Get sources for a specific category
  const getSourcesByCategory = (categoryId) => {
    return sourcesRisque.filter(source => source.category === categoryId);
  };

  // Get category details by ID
  const getCategoryById = (categoryId) => {
    // First try to find in translated categories
    const category = translatedCategories.find(category => category.id === categoryId);
    if (category) return category;

    // If not found, create a default category object with the ID
    return {
      id: categoryId,
      name: categoryId.toUpperCase(),
      description: t('workshop2.activity1.addSource.unknownCategory'),
      color: 'bg-gray-600 border-gray-700 text-gray-50',
      icon: '🔍'
    };
  };

  return (
    <div className="space-y-8">
      {selectedCategories.length === 0 ? (
        <div className="bg-yellow-50 p-6 rounded-xl border border-yellow-200 shadow-sm">
          <div className="flex items-start">
            <div className="bg-yellow-100 p-2 rounded-full mr-4 flex-shrink-0">
              <AlertCircle size={24} className="text-yellow-600" />
            </div>
            <div>
              <h3 className="font-semibold text-yellow-800 text-lg">{t('workshop2.activity1.messages.noCategorySelected')}</h3>
              <p className="text-yellow-600 mt-1">
                {t('workshop2.activity1.messages.selectCategoriesFirst')}
              </p>
            </div>
          </div>
        </div>
      ) : (
        <>
          {/* Two-column layout */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {/* Left column - Add source form */}
            <div className="md:col-span-1">
              <div className="bg-white rounded-xl shadow-lg border border-gray-200 sticky top-4 transition-all duration-300 ease-in-out">
                <div className="p-4 bg-gradient-to-r from-gray-700 to-gray-900 rounded-t-xl">
                  <h3 className="font-bold text-white text-lg flex items-center">
                    <Plus size={20} className="mr-2 text-gray-300" />
                    {t('workshop2.activity1.addSource.title')}
                  </h3>
                  <p className="text-gray-300 text-sm mt-1">
                    {t('workshop2.activity1.addSource.subtitle')}
                  </p>
                </div>
                <div className="p-4">
                  <div className="space-y-3">
                    <div>
                      <label className="block text-xs font-medium text-gray-700 mb-1">{t('workshop2.activity1.addSource.selectedCategory')}</label>
                      <div className="relative">
                        <button
                          onClick={() => document.getElementById('category-dropdown').classList.toggle('hidden')}
                          className="w-full p-2 bg-gray-50 border border-gray-200 rounded-md flex items-center justify-between hover:bg-gray-100 transition-colors"
                        >
                          {selectedCategory ? (
                            <div className="flex items-center">
                              <div className={`w-4 h-4 rounded-full mr-2 ${getCategoryById(selectedCategory)?.color || 'bg-gray-400'}`}></div>
                              <span className="text-sm font-medium">{getCategoryById(selectedCategory)?.name || t('workshop2.activity1.addSource.unknownCategory')}</span>
                            </div>
                          ) : (
                            <span className="text-sm text-gray-500 italic">{t('workshop2.activity1.addSource.noCategory')}</span>
                          )}
                          <ChevronDown size={16} className="text-gray-500" />
                        </button>

                        <div id="category-dropdown" className="absolute z-10 mt-1 w-full bg-white border border-gray-200 rounded-md shadow-lg hidden">
                          <div className="max-h-48 overflow-y-auto py-1">
                            {selectedCategories.map(catId => {
                              const cat = getCategoryById(catId);
                              return (
                                <button
                                  key={catId}
                                  onClick={() => {
                                    handleCategorySelect(catId);
                                    document.getElementById('category-dropdown').classList.add('hidden');
                                  }}
                                  className={`w-full text-left px-3 py-2 flex items-center hover:bg-gray-100 ${selectedCategory === catId ? 'bg-gray-50' : ''}`}
                                >
                                  <div className={`w-3 h-3 rounded-full mr-2 ${cat?.color || 'bg-gray-400'}`}></div>
                                  <span className="text-sm">{cat?.name || t('workshop2.activity1.addSource.unknownCategory')}</span>
                                </button>
                              );
                            })}
                          </div>
                        </div>
                      </div>
                    </div>
                    <div>
                      <label className="block text-xs font-medium text-gray-700 mb-1">{t('workshop2.activity1.addSource.name')}</label>
                      <div className="relative">
                        <input
                          type="text"
                          value={newSource.name}
                          onChange={(e) => setNewSource({...newSource, name: e.target.value})}
                          className="w-full p-2 pl-8 text-sm border border-gray-300 rounded-md focus:ring-1 focus:ring-gray-500 focus:border-gray-500 transition-all"
                          placeholder={t('workshop2.activity1.addSource.namePlaceholder')}
                        />
                        <div className="absolute inset-y-0 left-0 pl-2 flex items-center pointer-events-none">
                          <User size={16} className="text-gray-400" />
                        </div>
                      </div>
                    </div>
                    <div>
                      <label className="block text-xs font-medium text-gray-700 mb-1">{t('workshop2.activity1.addSource.targetObjectiveCategory')}</label>
                      <div className="relative objectif-dropdown-container">
                        <button
                          onClick={() => setIsObjectifDropdownOpen(!isObjectifDropdownOpen)}
                          className="w-full p-2 pl-8 text-sm border border-gray-300 rounded-md focus:ring-1 focus:ring-gray-500 focus:border-gray-500 transition-all text-left flex items-center justify-between"
                        >
                          {newSource.objectifViseCategory ? (
                            <div className="flex items-center">
                              <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${OBJECTIF_VISE_CATEGORIES.find(cat => cat.id === newSource.objectifViseCategory)?.color || 'bg-gray-100 text-gray-800'}`}>
                                {OBJECTIF_VISE_CATEGORIES.find(cat => cat.id === newSource.objectifViseCategory)?.name || t('workshop2.activity1.addSource.unknownCategory')}
                              </span>
                            </div>
                          ) : (
                            <span className="text-gray-500">{t('workshop2.activity1.addSource.selectObjectiveCategory')}</span>
                          )}
                          <ChevronDown size={16} className="text-gray-500" />
                        </button>
                        <div className="absolute inset-y-0 left-0 pl-2 flex items-center pointer-events-none">
                          <Target size={16} className="text-gray-400" />
                        </div>

                        {isObjectifDropdownOpen && (
                          <div className="absolute z-50 mt-1 w-full bg-white border border-gray-200 rounded-md shadow-lg">
                            <div className="max-h-60 overflow-y-auto py-1">
                              {OBJECTIF_VISE_CATEGORIES.map(category => (
                                <button
                                  key={category.id}
                                  className="w-full text-left px-4 py-2 hover:bg-gray-100 flex items-center"
                                  onClick={() => {
                                    setNewSource({
                                      ...newSource,
                                      objectifViseCategory: category.id,
                                      objectifVise: ''
                                    });
                                    setIsObjectifDropdownOpen(false);
                                  }}
                                >
                                  <div className="mr-2">{category.icon}</div>
                                  <div className="font-medium">{category.name}</div>
                                </button>
                              ))}
                            </div>
                          </div>
                        )}
                      </div>

                      <div className="mt-2">
                        <label className="block text-xs font-medium text-gray-700 mb-1">{t('workshop2.activity1.addSource.targetObjectiveDetailRequired')}</label>
                        <div className="relative">
                          <input
                            type="text"
                            value={newSource.objectifVise}
                            onChange={(e) => setNewSource({...newSource, objectifVise: e.target.value})}
                            className="w-full p-2 pl-8 text-sm border border-gray-300 rounded-md focus:ring-1 focus:ring-gray-500 focus:border-gray-500 transition-all"
                            placeholder={t('workshop2.activity1.addSource.targetObjectivePlaceholder')}
                          />
                          <div className="absolute inset-y-0 left-0 pl-2 flex items-center pointer-events-none">
                            <FileText size={16} className="text-gray-400" />
                          </div>
                        </div>
                      </div>
                    </div>
                    <div>
                      <SliderSelector
                        label={t('workshop2.activity1.addSource.resources')}
                        value={newSource.ressources}
                        onChange={(value) => setNewSource({...newSource, ressources: value})}
                        options={[
                          { value: 'faibles', label: t('workshop2.activity1.levelLabels.faibles') },
                          { value: 'moyennes', label: t('workshop2.activity1.levelLabels.moyennes') },
                          { value: 'importantes', label: t('workshop2.activity1.levelLabels.importantes') }
                        ]}
                        colorMap={resourcesColorMap}
                      />
                    </div>
                    <div>
                      <SliderSelector
                        label={t('workshop2.activity1.addSource.activity')}
                        value={newSource.activite}
                        onChange={(value) => setNewSource({...newSource, activite: value})}
                        options={[
                          { value: 'faible', label: t('workshop2.activity1.levelLabels.faible') },
                          { value: 'moyen', label: t('workshop2.activity1.levelLabels.moyen') },
                          { value: 'eleve', label: t('workshop2.activity1.levelLabels.eleve') }
                        ]}
                        colorMap={activityColorMap}
                      />
                    </div>
                    <div>
                      <SliderSelector
                        label={t('workshop2.activity1.addSource.motivation')}
                        value={newSource.motivation}
                        onChange={(value) => setNewSource({...newSource, motivation: value})}
                        options={[
                          { value: 'faible', label: t('workshop2.activity1.levelLabels.faible') },
                          { value: 'moyen', label: t('workshop2.activity1.levelLabels.moyen') },
                          { value: 'eleve', label: t('workshop2.activity1.levelLabels.eleve') }
                        ]}
                        colorMap={motivationColorMap}
                      />
                    </div>
                    <div>
                      <label className="block text-xs font-medium text-gray-700 mb-1">{t('workshop2.activity1.addSource.description')}</label>
                      <div className="relative">
                        <textarea
                          value={newSource.description}
                          onChange={(e) => setNewSource({...newSource, description: e.target.value})}
                          className="w-full p-2 pl-8 text-sm border border-gray-300 rounded-md focus:ring-1 focus:ring-gray-500 focus:border-gray-500 transition-all"
                          placeholder={t('workshop2.activity1.addSource.descriptionPlaceholder')}
                          rows="2"
                        ></textarea>
                        <div className="absolute top-2 left-0 pl-2 flex items-start pointer-events-none">
                          <FileText size={16} className="text-gray-400" />
                        </div>
                      </div>
                    </div>
                    <div className="pt-4">
                      <button
                        onClick={handleAddSource}
                        disabled={!selectedCategory}
                        className={`w-full py-2.5 px-4 rounded-md flex items-center justify-center transition-all duration-200 ${!selectedCategory ? 'bg-gray-400 text-gray-200 cursor-not-allowed' : 'bg-gray-800 text-white hover:bg-gray-900 shadow-md hover:shadow-lg'}`}
                      >
                        <Plus size={18} className="mr-2" />
                        {t('workshop2.activity1.addSource.addButton')}
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Right column - Categories */}
            <div className="md:col-span-2">
              <h3 className="text-lg font-semibold text-gray-800 mb-3">{t('workshop2.activity1.sourcesList.title')}</h3>

              {/* Sort categories to put the selected one at the top */}
              {selectedCategories
                .slice() // Create a copy to avoid mutating the original array
                .sort((a, b) => {
                  if (a === selectedCategory) return -1; // Selected category comes first
                  if (b === selectedCategory) return 1; // Other categories come after
                  return 0; // Keep original order for non-selected categories
                })
                .map(categoryId => {
            const category = getCategoryById(categoryId);
            const categorySources = getSourcesByCategory(categoryId);
            const isExpanded = expandedCategories.includes(categoryId);

            if (!category) return null;

            return (
              <motion.div
                key={categoryId}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="rounded-xl overflow-hidden shadow-md border border-gray-200"
              >
                {/* Category Header */}
                <div
                  className={`p-4 flex justify-between items-center cursor-pointer bg-white border-l-4 ${category.color.replace('bg-', 'border-')} ${selectedCategory === categoryId ? 'shadow-md' : ''}`}
                  onClick={() => toggleCategoryExpansion(categoryId)}
                >
                  <div className="flex items-center">
                    <div className={`${category.color.split(' ')[0].replace('bg-', 'bg-')} p-2 rounded-full mr-3`}>
                      <span className="text-2xl text-white">{category.icon}</span>
                    </div>
                    <div>
                      <h3 className={`font-bold text-gray-800`}>{category.name}</h3>
                      <p className="text-gray-600 text-sm">{category.description}</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-3">
                    <span className={`${category.color.split(' ')[0].replace('bg-', 'bg-')} bg-opacity-10 px-3 py-1 rounded-full ${category.color.split(' ')[0].replace('bg-', 'text-')} text-sm`}>
                      {categorySources.length} source{categorySources.length !== 1 ? 's' : ''}
                    </span>
                    <button
                      onClick={(e) => {
                        e.stopPropagation(); // Prevent the parent div's onClick from firing
                        handleCategorySelect(categoryId);
                      }}
                      className={`px-3 py-1 rounded-full text-sm ${selectedCategory === categoryId ?
                        `${category.color.split(' ')[0].replace('bg-', 'bg-')} text-white font-medium shadow-sm` :
                        `${category.color.split(' ')[0].replace('bg-', 'bg-')} bg-opacity-10 ${category.color.split(' ')[0].replace('bg-', 'text-')} hover:bg-opacity-20`}`}
                    >
                      {selectedCategory === categoryId ? t('workshop2.activity1.threatCategories.selectedButton') : t('workshop2.activity1.threatCategories.selectButton')}
                    </button>
                    <button
                      onClick={(e) => {
                        e.stopPropagation(); // Prevent the parent div's onClick from firing
                        toggleCategoryExpansion(categoryId);
                      }}
                      className={`${category.color.split(' ')[0].replace('bg-', 'bg-')} bg-opacity-10 p-2 rounded-full ${category.color.split(' ')[0].replace('bg-', 'text-')} hover:bg-opacity-20`}
                    >
                      {isExpanded ? <ChevronUp size={20} /> : <ChevronDown size={20} />}
                    </button>
                  </div>
                </div>

                {/* Category Content */}
                <AnimatePresence>
                  {isExpanded && (
                    <motion.div
                      initial={{ opacity: 0, height: 0 }}
                      animate={{ opacity: 1, height: 'auto' }}
                      exit={{ opacity: 0, height: 0 }}
                      transition={{ duration: 0.3 }}
                    >
                      {/* Content with form on left and table on right */}
                      <div className="p-4 bg-white">
                        {/* Sources Table */}
                        {categorySources.length === 0 ? (
                            <div className="text-center py-6 bg-gray-50 rounded-lg h-full flex items-center justify-center">
                              <div>
                                <AlertCircle size={40} className="mx-auto text-gray-300 mb-2" />
                                <p className="text-gray-500 font-medium">{t('workshop2.activity1.sourcesList.noSources')}</p>
                                <p className="text-gray-400 text-sm mt-1">{t('workshop2.activity1.sourcesList.noSourcesHint')}</p>
                              </div>
                            </div>
                          ) : (
                            <div className="overflow-x-auto rounded-lg border border-gray-200">
                              <table className="min-w-full divide-y divide-gray-200">
                                <thead className="bg-gray-50">
                                  <tr>
                                    <th scope="col" className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{t('workshop2.activity1.table.headers.riskSource')}</th>
                                    <th scope="col" className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{t('workshop2.activity1.table.headers.targetObjectiveDetail')}</th>
                                    <th scope="col" className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{t('workshop2.activity1.table.headers.objectiveCategory')}</th>
                                    <th scope="col" className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{t('workshop2.activity1.table.headers.resources')}</th>
                                    <th scope="col" className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{t('workshop2.activity1.table.headers.activity')}</th>
                                    <th scope="col" className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{t('workshop2.activity1.table.headers.motivation')}</th>
                                    <th scope="col" className="px-3 py-2 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"></th>
                                  </tr>
                                </thead>
                                <tbody className="bg-white divide-y divide-gray-200">
                                  {categorySources.map(source => (
                                    <tr key={source.id} className="hover:bg-gray-50">
                                      {editingSource && editingSource.id === source.id ? (
                                        // Edit mode - Enhanced layout
                                        <>
                                          <td colSpan="6" className="px-0 py-0">
                                            <div className="bg-blue-50 border border-blue-100 rounded-lg p-4 my-2 shadow-sm">
                                              <div className="flex justify-between items-center mb-4">
                                                <h3 className="text-lg font-semibold text-blue-800 flex items-center">
                                                  <Edit size={18} className="mr-2 text-blue-600" />
                                                  {t('workshop2.activity1.sourcesList.editingTitle')}
                                                </h3>
                                                <div className="flex space-x-2">
                                                  <button
                                                    onClick={handleSaveEdit}
                                                    className="bg-blue-600 text-white px-3 py-1.5 rounded-md hover:bg-blue-700 flex items-center text-sm font-medium transition-colors shadow-sm"
                                                  >
                                                    <Save size={16} className="mr-1.5" />
                                                    {t('workshop2.activity1.sourcesList.saveButton')}
                                                  </button>
                                                  <button
                                                    onClick={() => setEditingSource(null)}
                                                    className="bg-gray-200 text-gray-700 px-3 py-1.5 rounded-md hover:bg-gray-300 flex items-center text-sm font-medium transition-colors"
                                                  >
                                                    <X size={16} className="mr-1.5" />
                                                    {t('workshop2.activity1.sourcesList.cancelButton')}
                                                  </button>
                                                </div>
                                              </div>

                                              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                                {/* Left column - Basic information */}
                                                <div className="space-y-4">
                                                  <div>
                                                    <label className="block text-sm font-medium text-blue-800 mb-1">{t('workshop2.activity1.sourcesList.nameLabel')}</label>
                                                    <input
                                                      type="text"
                                                      value={editingSource.name}
                                                      onChange={(e) => setEditingSource({ ...editingSource, name: e.target.value })}
                                                      className="w-full p-2 text-sm border border-blue-200 rounded-md focus:ring-blue-500 focus:border-blue-500"
                                                      placeholder={t('workshop2.activity1.addSource.namePlaceholder')}
                                                    />
                                                  </div>

                                                  <div>
                                                    <label className="block text-sm font-medium text-blue-800 mb-1">{t('workshop2.activity1.sourcesList.descriptionLabel')}</label>
                                                    <textarea
                                                      value={editingSource.description || ''}
                                                      onChange={(e) => setEditingSource({ ...editingSource, description: e.target.value })}
                                                      className="w-full p-2 text-sm border border-blue-200 rounded-md focus:ring-blue-500 focus:border-blue-500"
                                                      placeholder={t('workshop2.activity1.addSource.descriptionPlaceholder')}
                                                      rows="3"
                                                    ></textarea>
                                                  </div>

                                                  <div>
                                                    <label className="block text-sm font-medium text-blue-800 mb-1">{t('workshop2.activity1.sourcesList.targetObjectiveLabel')}</label>
                                                    <input
                                                      type="text"
                                                      value={editingSource.objectifVise || ''}
                                                      onChange={(e) => setEditingSource({ ...editingSource, objectifVise: e.target.value })}
                                                      className="w-full p-2 text-sm border border-blue-200 rounded-md focus:ring-blue-500 focus:border-blue-500"
                                                      placeholder={t('workshop2.activity1.addSource.targetObjectivePlaceholder')}
                                                    />
                                                  </div>

                                                  <div>
                                                    <label className="block text-sm font-medium text-blue-800 mb-1">{t('workshop2.activity1.addSource.targetObjectiveCategory')}</label>
                                                    <div className="relative edit-objectif-dropdown-container">
                                                      <button
                                                        onClick={() => setIsEditObjectifDropdownOpen(!isEditObjectifDropdownOpen)}
                                                        className="w-full p-2 text-sm border border-blue-200 rounded-md text-left flex items-center justify-between bg-white focus:ring-blue-500 focus:border-blue-500"
                                                      >
                                                        {editingSource.objectifViseCategory ? (
                                                          <div className="flex items-center">
                                                            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${OBJECTIF_VISE_CATEGORIES.find(cat => cat.id === editingSource.objectifViseCategory)?.color || 'bg-gray-100 text-gray-800'}`}>
                                                              {OBJECTIF_VISE_CATEGORIES.find(cat => cat.id === editingSource.objectifViseCategory)?.name || 'Catégorie inconnue'}
                                                            </span>
                                                          </div>
                                                        ) : (
                                                          <span className="text-gray-500">{t('workshop2.activity1.addSource.selectObjectiveCategory')}</span>
                                                        )}
                                                        <ChevronDown size={16} className="text-gray-500" />
                                                      </button>

                                                      {isEditObjectifDropdownOpen && (
                                                        <div className="absolute z-50 mt-1 w-full bg-white border border-gray-200 rounded-md shadow-lg">
                                                          <div className="max-h-48 overflow-y-auto py-1">
                                                            {OBJECTIF_VISE_CATEGORIES.map(category => (
                                                              <button
                                                                key={category.id}
                                                                className="w-full text-left px-3 py-2 hover:bg-blue-50 flex items-center"
                                                                onClick={() => {
                                                                  setEditingSource({
                                                                    ...editingSource,
                                                                    objectifViseCategory: category.id,
                                                                  });
                                                                  setIsEditObjectifDropdownOpen(false);
                                                                }}
                                                              >
                                                                <div className="mr-2">{category.icon}</div>
                                                                <div className="font-medium">{category.name}</div>
                                                              </button>
                                                            ))}
                                                          </div>
                                                        </div>
                                                      )}
                                                    </div>
                                                  </div>
                                                </div>

                                                {/* Right column - Ratings */}
                                                <div className="space-y-6 bg-white p-4 rounded-lg border border-blue-100 shadow-sm">
                                                  <div>
                                                    <label className="block text-sm font-medium text-blue-800 mb-2">{t('workshop2.activity1.sourcesList.resourcesLabel')}</label>
                                                    <CompactSliderSelector
                                                      value={editingSource.ressources}
                                                      onChange={(value) => setEditingSource({ ...editingSource, ressources: value })}
                                                      options={[
                                                        { value: 'faibles', label: t('workshop2.activity1.levelLabels.faibles') },
                                                        { value: 'moyennes', label: t('workshop2.activity1.levelLabels.moyennes') },
                                                        { value: 'importantes', label: t('workshop2.activity1.levelLabels.importantes') }
                                                      ]}
                                                      colorMap={resourcesColorMap}
                                                    />
                                                  </div>

                                                  <div>
                                                    <label className="block text-sm font-medium text-blue-800 mb-2">{t('workshop2.activity1.sourcesList.activityLabel')}</label>
                                                    <CompactSliderSelector
                                                      value={editingSource.activite || 'faible'}
                                                      onChange={(value) => setEditingSource({ ...editingSource, activite: value })}
                                                      options={[
                                                        { value: 'faible', label: t('workshop2.activity1.levelLabels.faible') },
                                                        { value: 'moyen', label: t('workshop2.activity1.levelLabels.moyen') },
                                                        { value: 'eleve', label: t('workshop2.activity1.levelLabels.eleve') }
                                                      ]}
                                                      colorMap={activityColorMap}
                                                    />
                                                  </div>

                                                  <div>
                                                    <label className="block text-sm font-medium text-blue-800 mb-2">{t('workshop2.activity1.sourcesList.motivationLabel')}</label>
                                                    <CompactSliderSelector
                                                      value={editingSource.motivation || 'faible'}
                                                      onChange={(value) => setEditingSource({ ...editingSource, motivation: value })}
                                                      options={[
                                                        { value: 'faible', label: t('workshop2.activity1.levelLabels.faible') },
                                                        { value: 'moyen', label: t('workshop2.activity1.levelLabels.moyen') },
                                                        { value: 'eleve', label: t('workshop2.activity1.levelLabels.eleve') }
                                                      ]}
                                                      colorMap={motivationColorMap}
                                                    />
                                                  </div>
                                                </div>
                                              </div>
                                            </div>
                                          </td>
                                        </>
                                      ) : (
                                        // View mode
                                        <>
                                          <td className="px-3 py-2 whitespace-normal">
                                            <div className="text-sm font-bold text-gray-900">{source.name}</div>
                                            {source.description && (
                                              <div className="text-xs text-gray-500 mt-1">{source.description}</div>
                                            )}
                                          </td>
                                          <td className="px-3 py-2 whitespace-normal">
                                            <div className="font-bold">
                                              <span className="text-sm text-gray-900">
                                                {source.objectifVise ?
                                                  source.objectifVise :
                                                  <span className="text-gray-400 italic">Non spécifié</span>
                                                }
                                              </span>
                                            </div>
                                          </td>
                                          <td className="px-3 py-2 whitespace-nowrap">
                                            <div className="flex flex-col space-y-1 font-bold">
                                              {source.objectifViseCategory ? (
                                                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${OBJECTIF_VISE_CATEGORIES.find(cat => cat.id === source.objectifViseCategory)?.color || 'bg-gray-100 text-gray-800'}`}>
                                                  {OBJECTIF_VISE_CATEGORIES.find(cat => cat.id === source.objectifViseCategory)?.name || source.objectifViseCategory}
                                                </span>
                                              ) : (
                                                <span className="text-gray-400 italic text-xs">Non spécifié</span>
                                              )}
                                            </div>
                                          </td>
                                          <td className="px-3 py-2 whitespace-nowrap">
                                            <span className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${getResourcesColor(source.ressources)} capitalize`}>
                                              {source.ressources || '-'}
                                            </span>
                                          </td>
                                          <td className="px-3 py-2 whitespace-nowrap">
                                            <span className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${getActivityColor(source.activite)} capitalize`}>
                                              {source.activite || '-'}
                                            </span>
                                          </td>
                                          <td className="px-3 py-2 whitespace-nowrap">
                                            <span className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${getMotivationColor(source.motivation)} capitalize`}>
                                              {source.motivation || '-'}
                                            </span>
                                          </td>

                                          <td className="px-3 py-2 whitespace-nowrap text-right text-sm font-medium">
                                            <div className="flex space-x-2">
                                              <button
                                                onClick={() => handleEditSource(source)}
                                                className="text-indigo-600 hover:text-indigo-900 p-1 rounded-full hover:bg-indigo-100"
                                              >
                                                <Edit size={18} />
                                              </button>
                                              <button
                                                onClick={() => handleDeleteSource(source.id)}
                                                className="text-red-600 hover:text-red-900 p-1 rounded-full hover:bg-red-100"
                                              >
                                                <Trash2 size={18} />
                                              </button>
                                            </div>
                                          </td>
                                        </>
                                      )}
                                    </tr>
                                  ))}
                                </tbody>
                              </table>
                            </div>
                          )}
                      </div>
                    </motion.div>
                  )}
                </AnimatePresence>
              </motion.div>
            );
          })}

              {/* Save and AI buttons */}
              <div className="flex justify-end mt-6 space-x-4">
                <button
                  onClick={onRequestAiSuggestions}
                  disabled={isLoadingAi || !analysisData?.id}
                  className="px-4 py-2 text-sm font-medium text-white bg-gradient-to-r from-purple-500 to-indigo-600 hover:from-purple-600 hover:to-indigo-700 rounded-md flex items-center shadow-sm transition-all duration-200 transform hover:scale-105 hover:shadow focus:outline-none focus:ring-2 focus:ring-offset-1 focus:ring-indigo-500 disabled:opacity-60 disabled:cursor-not-allowed disabled:hover:scale-100 disabled:hover:shadow-sm"
                >
                  <Sparkles size={16} className="mr-1.5 text-yellow-100" />
                  {isLoadingAi ? (
                    <span className="flex items-center">
                      <RefreshCw size={14} className="mr-1.5 animate-spin" />
                      {t('workshop2.activity1.ai.generating')}
                    </span>
                  ) : (
                    t('workshop2.activity1.ai.suggestions')
                  )}
                </button>
                <button
                  onClick={() => {
                    console.log("Save button clicked directly");
                    handleSaveAllSources();
                  }}
                  className="bg-gradient-to-r from-blue-600 to-indigo-600 text-white px-6 py-3 rounded-lg hover:from-blue-700 hover:to-indigo-700 shadow-md hover:shadow-lg transition-all duration-200 flex items-center"
                >
                  <Save size={18} className="mr-2" />
                  {t('common.save')}
                </button>
              </div>
            </div>
          </div>
        </>
      )}
    </div>
  );
};

export default CategorySourcesTable;
