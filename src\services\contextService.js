// src/services/contextService.js
import api from '../api/apiClient';

/**
 * Service for handling context operations
 * API-first approach without localStorage fallback for loading
 */
const contextService = {
  /**
   * Get context data for a specific analysis
   * @param {string} analysisId - Analysis ID
   * @returns {Promise<Object>} Context data
   */
  getContext: async (analysisId) => {
    try {
      // Try to get from API
      const response = await api.get(`/analyses/${analysisId}/context`);

      if (response.success) {
        return response.data;
      }

      throw new Error(response.message || 'Failed to fetch context data');
    } catch (error) {
      console.error('Error fetching context data:', error);
      // No localStorage fallback - API is the source of truth
      throw error;
    }
  },

  /**
   * Save context data for a specific analysis
   * @param {string} analysisId - Analysis ID
   * @param {Object} contextData - Context data to save
   * @returns {Promise<Object>} Saved context data
   */
  saveContext: async (analysisId, contextData) => {
    // Format data for API (if needed)
    const formattedData = {
      ...contextData,
      analysisId
    };

    try {
      // Save to API
      const response = await api.post(`/analyses/${analysisId}/context`, { data: formattedData });

      // Also save to local storage as backup
      localStorage.setItem('contextData', JSON.stringify(contextData));

      return response;
    } catch (error) {
      console.error('Error saving context data:', error);

      // If API fails, at least try to save locally
      localStorage.setItem('contextData', JSON.stringify(contextData));

      // Return a success response with a local flag
      return {
        success: true,
        data: contextData,
        savedLocally: true,
        message: 'Data saved locally only. Will sync when connection is restored.'
      };
    }
  },

  /**
   * Convert raw context data to the format expected by components
   * @param {Object} rawData - Raw context data from API
   * @returns {Object} Formatted context data including business assets
   */
  formatContextData: (rawData) => {
    // Handle cases where rawData might be null or not in the expected structure
    if (!rawData) {
        console.warn('formatContextData received null or undefined rawData');
        return { organizationName: '', missions: [], scope: '', analysisDate: '', participants: [], businessAssets: [] };
    }

    // Check if data is nested under a 'data' property (common API pattern)
    const data = rawData.data ? rawData.data : rawData;

    // Ensure participants and businessAssets are always arrays
    const participants = Array.isArray(data.participants) ? data.participants : [];
    const businessAssets = Array.isArray(data.businessAssets) ? data.businessAssets : [];

    return {
      // Add default empty strings/arrays for robustness
      organizationName: data.organizationName || '',
      missions: Array.isArray(data.missions) ? data.missions : (data.mission ? [data.mission] : []),
      scope: data.scope || '',
      analysisDate: data.analysisDate || '',
      participants: participants,
      // Ensure business assets are extracted and defaulted to an empty array
      businessAssets: businessAssets
    };
  },

  /**
   * Get context history
   * @param {string} analysisId - Analysis ID
   * @returns {Promise<Array>} Context history
   */
  getContextHistory: async (analysisId) => {
    try {
      const response = await api.get(`/analyses/${analysisId}/components/context/history`);
      return response.data || [];
    } catch (error) {
      console.error('Error fetching context history:', error);
      throw error;
    }
  }
};

export default contextService;