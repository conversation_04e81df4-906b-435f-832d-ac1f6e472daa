// businessValuesUtils.js

// Générer un ID court pour chaque valeur métier
export const getShortId = (index) => `VM${index + 1}`;

// Générer un ID court pour chaque bien support
export const getSupportAssetShortId = (vmId, index) => `BS${vmId.substring(2)}-${index + 1}`;

// Mise à jour des IDs courts pour les valeurs métier
export const ensureShortIds = (businessValues) => {
  if (businessValues.some(value => !value.shortId)) {
    return businessValues.map((value, index) => {
      if (!value.shortId) {
        return { ...value, shortId: getShortId(index) };
      }
      return value;
    });
  }
  return businessValues;
};

// Mise à jour des IDs courts pour les biens supports
export const ensureSupportAssetShortIds = (businessValues) => {
  return businessValues.map(value => {
    if (!value.supportAssets || value.supportAssets.length === 0) {
      return value;
    }
    
    const updatedSupportAssets = value.supportAssets.map((asset, index) => {
      if (!asset.shortId) {
        return { ...asset, shortId: getSupportAssetShortId(value.shortId, index) };
      }
      return asset;
    });
    
    return { ...value, supportAssets: updatedSupportAssets };
  });
};