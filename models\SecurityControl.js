const mongoose = require('mongoose');

const securityControlSchema = new mongoose.Schema({
  measure_description: {
    type: String,
    required: [true, 'Measure description is required.'],
    trim: true,
  },
  pillar: {
    type: String,
    required: [true, 'Pillar is required.'],
    enum: ['Confidentialité', 'Intégrité', 'Disponibilité', 'Traçabilité', 'Preuve', 'Auditabilité'], // Based on provided data
    trim: true,
  },
  measure_type: {
    type: String,
    required: false, // Optional for backward compatibility
    enum: ['gouvernance et anticipation', 'protection', 'défense', 'résilience'],
    trim: true,
  },
  risk_treatment_strategy: {
    type: String,
    required: [true, 'Risk treatment strategy is required.'],
    enum: ['Réduire le risque', 'Éviter le risque', 'Transférer le risque', 'Accepter le risque'], // Based on provided data
    trim: true,
  },
  // Optional: Add timestamps if you want to track creation/update times
  // timestamps: true,
}, {
  // Optional: Specify the collection name explicitly
  collection: 'security_controls',
  timestamps: true, // Add createdAt and updatedAt timestamps
});

// Optional: Add an index for faster searching if needed, e.g., on description
// securityControlSchema.index({ measure_description: 'text' });

const SecurityControl = mongoose.model('SecurityControl', securityControlSchema);

module.exports = SecurityControl;