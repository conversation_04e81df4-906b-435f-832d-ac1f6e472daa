// src/components/Atelier3/Activite3/CustomEdges.js
import React from 'react';
import { BaseEdge, EdgeLabelRenderer, getBezierPath, getSmoothStepPath } from 'reactflow';

// Custom animated edge with SVG animation
export const AnimatedEdge = ({
  id,
  source,
  target,
  sourceX,
  sourceY,
  targetX,
  targetY,
  sourcePosition,
  targetPosition,
  style = {},
  data,
  markerEnd,
  label,
  labelStyle
}) => {
  // Get the path based on the edge type
  const [edgePath, labelX, labelY] = getSmoothStepPath({
    sourceX,
    sourceY,
    sourcePosition,
    targetX,
    targetY,
    targetPosition,
  });

  // Define the animation
  const getAnimationStyle = () => {
    // Default animation
    let animationStyle = {
      strokeDasharray: '5,5',
      animation: 'flowAnimation 30s linear infinite',
    };

    // If it's a direct attack edge, use a special animation
    if (data?.edgeType === 'direct-attack') {
      return {
        strokeDasharray: '5,2',
        animation: 'flowAnimation 15s linear infinite',
        filter: 'drop-shadow(0 0 2px rgba(255, 69, 0, 0.5))',
      };
    }

    return animationStyle;
  };

  return (
    <>
      <BaseEdge path={edgePath} markerEnd={markerEnd} style={{ ...style, ...getAnimationStyle() }} />

      {/* Add animated dots along the path */}
      <>
        {/* For regular edges */}
        {data?.edgeType !== 'direct-attack' && (
          <>
            <circle r="3" fill={style.stroke || '#555'} className="edge-dot-1">
              <animateMotion dur="10s" repeatCount="indefinite" path={edgePath} />
            </circle>
            <circle r="3" fill={style.stroke || '#555'} className="edge-dot-2">
              <animateMotion dur="10s" begin="3.33s" repeatCount="indefinite" path={edgePath} />
            </circle>
            <circle r="3" fill={style.stroke || '#555'} className="edge-dot-3">
              <animateMotion dur="10s" begin="6.66s" repeatCount="indefinite" path={edgePath} />
            </circle>
          </>
        )}

        {/* For direct attack edges - faster and more dramatic */}
        {data?.edgeType === 'direct-attack' && (
          <>
            <circle r="4" fill={style.stroke || '#ff4500'} className="edge-dot-direct-1" style={{ filter: 'drop-shadow(0 0 3px rgba(255, 69, 0, 0.8))' }}>
              <animateMotion dur="5s" repeatCount="indefinite" path={edgePath} />
            </circle>
            <circle r="4" fill={style.stroke || '#ff4500'} className="edge-dot-direct-2" style={{ filter: 'drop-shadow(0 0 3px rgba(255, 69, 0, 0.8))' }}>
              <animateMotion dur="5s" begin="1.25s" repeatCount="indefinite" path={edgePath} />
            </circle>
            <circle r="4" fill={style.stroke || '#ff4500'} className="edge-dot-direct-3" style={{ filter: 'drop-shadow(0 0 3px rgba(255, 69, 0, 0.8))' }}>
              <animateMotion dur="5s" begin="2.5s" repeatCount="indefinite" path={edgePath} />
            </circle>
            <circle r="4" fill={style.stroke || '#ff4500'} className="edge-dot-direct-4" style={{ filter: 'drop-shadow(0 0 3px rgba(255, 69, 0, 0.8))' }}>
              <animateMotion dur="5s" begin="3.75s" repeatCount="indefinite" path={edgePath} />
            </circle>
          </>
        )}
      </>

      {/* Edge Label */}
      {label && (
        <EdgeLabelRenderer>
          <div
            style={{
              position: 'absolute',
              // Center the label on the line (not above it)
              transform: `translate(-50%, -50%) translate(${labelX}px,${labelY}px)`,
              fontSize: 12,
              // Everything inside EdgeLabelRenderer has no pointer events by default
              pointerEvents: 'all',
              backgroundColor: 'white', // Ensure solid background color
              opacity: 1, // Ensure full opacity
              zIndex: 10, // Ensure label is in front of the line
              cursor: 'pointer', // Show that it's clickable
              ...labelStyle,
            }}
            className="nodrag nopan px-2 py-1 rounded-md shadow-sm border border-slate-200 hover:bg-blue-50 hover:border-blue-300 transition-colors"
            title="Cliquez pour modifier le titre de la connexion"
          >
            {label}
          </div>
        </EdgeLabelRenderer>
      )}
    </>
  );
};

// Edge type mapping
export const edgeTypes = {
  animated: AnimatedEdge,
};
