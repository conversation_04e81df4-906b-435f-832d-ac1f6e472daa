// backend/routes/userRoutes.js
const express = require('express');
const { 
  getUsers, 
  getUserById, 
  createUser, 
  updateUser, 
  updatePassword, 
  deleteUser 
} = require('../controllers/userController');
const { getUserLogs } = require('../controllers/activityLogController');
const { protect, restrictTo, restrictToOwnerOrAdmin } = require('../middleware/authMiddleware');

const router = express.Router();

// All routes need authentication
router.use(protect);

// Routes for admin/superadmin only
router.route('/')
  .get(restrictTo('superadmin', 'admin'), getUsers)
  .post(restrictTo('superadmin', 'admin'), createUser);

// Routes for specific user
router.route('/:id')
  .get(restrictToOwnerOrAdmin((req) => req.params.id), getUserById)
  .put(restrictToOwnerOrAdmin((req) => req.params.id), updateUser)
  .delete(restrictTo('superadmin', 'admin'), deleteUser);

// Password update
router.route('/:id/password')
  .put(restrictToOwnerOrAdmin((req) => req.params.id), updatePassword);

// Get user activity logs
router.route('/:userId/logs')
  .get(restrictToOwnerOrAdmin((req) => req.params.userId), getUserLogs);

module.exports = router;