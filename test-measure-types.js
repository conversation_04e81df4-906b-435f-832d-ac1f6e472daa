// Test script pour vérifier les types de mesures
// Ce script peut être exécuté dans la console du navigateur pour tester les fonctions

// Fonction de test pour les badges de types de mesures
function testMeasureTypeBadges() {
    console.log("=== Test des badges de types de mesures ===");
    
    // Fonction getMeasureTypeBadge (copiée du composant)
    const getMeasureTypeBadge = (measureType) => {
        if (!measureType) return null;
        
        const typeConfig = {
            'gouvernance et anticipation': {
                color: 'bg-blue-100 text-blue-800 border-blue-200',
                label: 'Gouvernance'
            },
            'protection': {
                color: 'bg-green-100 text-green-800 border-green-200',
                label: 'Protection'
            },
            'défense': {
                color: 'bg-orange-100 text-orange-800 border-orange-200',
                label: 'Défense'
            },
            'résilience': {
                color: 'bg-purple-100 text-purple-800 border-purple-200',
                label: 'Résilience'
            }
        };
        
        return typeConfig[measureType] || null;
    };
    
    // Tests
    const testCases = [
        'gouvernance et anticipation',
        'protection',
        'défense',
        'résilience',
        'invalid_type',
        null,
        undefined
    ];
    
    testCases.forEach(testCase => {
        const result = getMeasureTypeBadge(testCase);
        console.log(`Type: "${testCase}" => `, result);
    });
}

// Fonction de test pour le format des suggestions AI
function testAiSuggestionFormat() {
    console.log("\n=== Test du format des suggestions AI ===");
    
    // Exemples de suggestions dans l'ancien format (chaînes)
    const oldFormatSuggestions = [
        "Mettre en œuvre l'authentification multifacteur",
        "Analyse régulière des vulnérabilités",
        "Chiffrer les données sensibles"
    ];
    
    // Exemples de suggestions dans le nouveau format (objets)
    const newFormatSuggestions = [
        {
            name: "Mettre en œuvre l'authentification multifacteur",
            measure_type: "protection"
        },
        {
            name: "Définir une politique de sécurité",
            measure_type: "gouvernance et anticipation"
        },
        {
            name: "Surveillance des accès",
            measure_type: "défense"
        },
        {
            name: "Plan de continuité d'activité",
            measure_type: "résilience"
        }
    ];
    
    // Fonction pour traiter les suggestions (copiée du composant)
    const processSuggestion = (suggestion) => {
        const suggestionName = typeof suggestion === 'string' ? suggestion : suggestion.name;
        const measureType = typeof suggestion === 'object' ? suggestion.measure_type : null;
        
        return {
            name: suggestionName,
            measure_type: measureType,
            id: `ai_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
            isNewSuggestion: true,
            description: 'Suggestion générée par IA.',
            type: 'AI',
        };
    };
    
    console.log("Ancien format:");
    oldFormatSuggestions.forEach(suggestion => {
        const processed = processSuggestion(suggestion);
        console.log(`"${suggestion}" => `, processed);
    });
    
    console.log("\nNouveau format:");
    newFormatSuggestions.forEach(suggestion => {
        const processed = processSuggestion(suggestion);
        console.log(`${suggestion.name} (${suggestion.measure_type}) => `, processed);
    });
}

// Fonction de test pour la validation du modèle
function testModelValidation() {
    console.log("\n=== Test de validation du modèle ===");
    
    const validMeasureTypes = ['gouvernance et anticipation', 'protection', 'défense', 'résilience'];
    
    const testControls = [
        {
            measure_description: "Test control 1",
            pillar: "Confidentialité",
            measure_type: "protection",
            risk_treatment_strategy: "Réduire le risque"
        },
        {
            measure_description: "Test control 2",
            pillar: "Intégrité",
            measure_type: "invalid_type", // Type invalide
            risk_treatment_strategy: "Éviter le risque"
        },
        {
            measure_description: "Test control 3",
            pillar: "Disponibilité",
            // measure_type manquant
            risk_treatment_strategy: "Transférer le risque"
        }
    ];
    
    testControls.forEach((control, index) => {
        console.log(`Contrôle ${index + 1}:`);
        console.log(`  Description: ${control.measure_description}`);
        console.log(`  Type de mesure: ${control.measure_type || 'NON DÉFINI'}`);
        console.log(`  Valide: ${!control.measure_type || validMeasureTypes.includes(control.measure_type)}`);
        console.log('');
    });
}

// Exécuter tous les tests
function runAllTests() {
    testMeasureTypeBadges();
    testAiSuggestionFormat();
    testModelValidation();
    console.log("\n=== Tests terminés ===");
}

// Exporter les fonctions pour utilisation
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        testMeasureTypeBadges,
        testAiSuggestionFormat,
        testModelValidation,
        runAllTests
    };
} else {
    // Dans le navigateur, attacher au window
    window.measureTypeTests = {
        testMeasureTypeBadges,
        testAiSuggestionFormat,
        testModelValidation,
        runAllTests
    };
    
    console.log("Tests disponibles via window.measureTypeTests");
    console.log("Exécutez window.measureTypeTests.runAllTests() pour lancer tous les tests");
}
