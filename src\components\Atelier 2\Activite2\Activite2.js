// src/components/Atelier 2/Activite2/Activite2.js
import React, { useState, useEffect, useRef } from 'react';
import { AlertCircle, Info, Printer, Save, BarChart2 } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { useAnalysis } from '../../../context/AnalysisContext';
import EvaluationTable from './EvaluationTable';
import RadarVisualization from './RadarVisualization';
import GuideModal from './GuideModal';
import useUnsavedChangesWarning from '../../../hooks/useUnsavedChangesWarning';
import { showErrorToast, showSuccessToast, showLoadingToast, updateToast, dismissToast } from '../../../utils/toastUtils';

const Activite2 = () => {
  const { t } = useTranslation();
  const [sourcesRisque, setSourcesRisque] = useState([]);
  const [couplesEvalues, setCouplesEvalues] = useState([]);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [originalSourcesRisque, setOriginalSourcesRisque] = useState([]);
  const [isGuideOpen, setIsGuideOpen] = useState(false);
  const [activeView, setActiveView] = useState('table'); // 'table' or 'chart'

  const contentRef = useRef(null);
  const { currentAnalysis, getSourcesDeRisque, saveSourcesDeRisque } = useAnalysis();

  // Handle print functionality with charts - Fixed version
  const handlePrint = () => {
    // Create the print window first
    const printWindow = window.open('', '_blank');

    // Write a loading message immediately
    printWindow.document.write(`
      <html>
        <head>
          <title>{t('workshop2.activity2.print.preparing')}</title>
          <style>
            body { font-family: Arial, sans-serif; display: flex; justify-content: center; align-items: center; height: 100vh; }
            .loader { border: 5px solid #f3f3f3; border-top: 5px solid #3498db; border-radius: 50%; width: 50px; height: 50px; animation: spin 1s linear infinite; }
            @keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }
            .message { margin-left: 20px; font-size: 18px; }
          </style>
        </head>
        <body>
          <div style="display: flex; align-items: center;">
            <div class="loader"></div>
            <div class="message">{t('workshop2.activity2.print.preparingDocument')}</div>
          </div>
        </body>
      </html>
    `);

    // Close the document to finish the initial write
    printWindow.document.close();

    // Get chart SVGs if available
    let chartsSvg = '';
    const chartElements = document.querySelectorAll('.chart-container svg');

    if (chartElements && chartElements.length > 0) {
      chartsSvg = `
        <h2 style="color: #1e40af; font-size: 20px; margin: 30px 0 15px 0;">{t('workshop2.activity2.print.visualizationTitle')}</h2>
        <div style="display: flex; flex-wrap: wrap; gap: 20px; justify-content: center; margin-bottom: 30px;">
      `;

      chartElements.forEach((chart, index) => {
        // Clone the SVG to avoid modifying the original
        const svgClone = chart.cloneNode(true);
        // Set explicit width and height
        svgClone.setAttribute('width', '500');
        svgClone.setAttribute('height', '400');
        // Add the SVG to the print content
        chartsSvg += `
          <div style="border: 1px solid #e5e7eb; border-radius: 8px; padding: 15px; background-color: #f9fafb;">
            <h3 style="color: #4b5563; font-size: 16px; margin-top: 0; margin-bottom: 10px; text-align: center;">
              ${index === 0 ? t('workshop2.activity2.print.viewByObjective') : t('workshop2.activity2.print.viewBySource')}
            </h3>
            ${svgClone.outerHTML}
          </div>
        `;
      });

      chartsSvg += '</div>';
    }

    // Create print content with styles - with visualization on a separate page
    const printContent = `
      <html>
        <head>
          <title>${t('workshop2.activity2.print.title')} - ${currentAnalysis?.name || t('workshop2.activity2.print.analysis')}</title>
          <style>
            body { font-family: Arial, sans-serif; padding: 20px; }
            h1 { color: #1e40af; font-size: 24px; margin-bottom: 20px; }
            h2 { color: #1e40af; font-size: 20px; margin: 30px 0 15px 0; }
            table { width: 100%; border-collapse: collapse; margin-bottom: 30px; }
            th { background-color: #f3f4f6; text-align: left; padding: 12px; font-size: 12px; text-transform: uppercase; }
            td { padding: 12px; border-bottom: 1px solid #e5e7eb; }
            .niveau-1 { background-color: #d1fae5; color: #065f46; padding: 4px 8px; border-radius: 9999px; font-weight: bold; }
            .niveau-2 { background-color: #fef3c7; color: #92400e; padding: 4px 8px; border-radius: 9999px; font-weight: bold; }
            .niveau-3 { background-color: #fee2e2; color: #b91c1c; padding: 4px 8px; border-radius: 9999px; font-weight: bold; }
            .selected { background-color: #fee2e2; }
            .header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px; }
            .footer { margin-top: 30px; text-align: center; font-size: 12px; color: #6b7280; }
            .justification { font-style: italic; color: #4b5563; }
            .page-break { page-break-before: always; }
            .visualization-page { height: 100%; display: flex; flex-direction: column; }
            .visualization-content { flex-grow: 1; }
            @media print {
              body { -webkit-print-color-adjust: exact !important; color-adjust: exact !important; }
              .niveau-1, .niveau-2, .niveau-3 { print-color-adjust: exact; -webkit-print-color-adjust: exact; }
              .page-break { page-break-before: always; }
            }
          </style>
          <base href="${window.location.origin}" />
        </head>
        <body>
          <!-- First page: Table of couples SR/OV -->
          <div>
            <div class="header">
              <h1>${t('workshop2.activity2.print.title')} - ${currentAnalysis?.name || t('workshop2.activity2.print.analysis')}</h1>
              <div>${t('workshop2.activity2.print.date')}: ${new Date().toLocaleDateString()}</div>
            </div>

            <table>
              <thead>
                <tr>
                  <th>${t('workshop2.activity2.table.headers.riskSource')}</th>
                  <th>${t('workshop2.activity2.table.headers.category')}</th>
                  <th>${t('workshop2.activity2.table.headers.targetObjective')}</th>
                  <th>${t('workshop2.activity2.table.headers.resources')}</th>
                  <th>${t('workshop2.activity2.table.headers.activity')}</th>
                  <th>${t('workshop2.activity2.table.headers.motivation')}</th>
                  <th>${t('workshop2.activity2.table.headers.level')}</th>
                  <th>${t('workshop2.activity2.table.headers.selected')}</th>
                  <th>${t('workshop2.activity2.table.headers.justification')}</th>
                </tr>
              </thead>
              <tbody>
                ${couplesEvalues.map(couple => `
                  <tr class="${couple.selected ? 'selected' : ''}">
                    <td>${couple.sourceName || ''}</td>
                    <td>${couple.sourceCategory || ''}</td>
                    <td>${couple.objectifVise || ''}<br><small>${couple.objectifViseCategory || ''}</small></td>
                    <td>${couple.ressources === 'importantes' ? t('workshop2.activity1.levels.high') : couple.ressources === 'moyennes' ? t('workshop2.activity1.levels.medium') : t('workshop2.activity1.levels.low')}</td>
                    <td>${couple.activite === 'eleve' ? t('workshop2.activity1.levels.high') : couple.activite === 'moyen' ? t('workshop2.activity1.levels.medium') : t('workshop2.activity1.levels.low')}</td>
                    <td>${couple.motivation === 'eleve' ? t('workshop2.activity1.levels.high') : couple.motivation === 'moyen' ? t('workshop2.activity1.levels.medium') : t('workshop2.activity1.levels.low')}</td>
                    <td><span class="niveau-${couple.niveau}">${couple.niveau}</span></td>
                    <td>${couple.selected ? '✓' : '✗'}</td>
                    <td class="justification">${couple.justification || (couple.selected ? t('workshop2.activity2.print.noJustification') : t('workshop2.activity2.print.notSelected'))}</td>
                  </tr>
                `).join('')}
              </tbody>
            </table>

            <div class="footer">
              <p>${t('workshop2.activity2.print.page1')} - ${t('workshop2.activity2.print.generatedOn')} ${new Date().toLocaleString()} - EBIOS RM</p>
            </div>
          </div>

          ${chartsSvg ? `
          <!-- Second page: Visualizations -->
          <div class="page-break visualization-page">
            <div class="header">
              <h1>${t('workshop2.activity2.print.visualizationTitle')} - ${currentAnalysis?.name || t('workshop2.activity2.print.analysis')}</h1>
              <div>${t('workshop2.activity2.print.date')}: ${new Date().toLocaleDateString()}</div>
            </div>

            <div class="visualization-content">
              ${chartsSvg}
            </div>

            <div class="footer">
              <p>${t('workshop2.activity2.print.page2')} - ${t('workshop2.activity2.print.generatedOn')} ${new Date().toLocaleString()} - EBIOS RM</p>
            </div>
          </div>
          ` : ''}
        </body>
      </html>
    `;

    // Wait a moment to ensure the window is ready, then replace the content
    setTimeout(() => {
      // Clear the document and write the new content
      printWindow.document.open();
      printWindow.document.write(printContent);
      printWindow.document.close();

      // Wait for the content to be fully loaded before printing
      setTimeout(() => {
        printWindow.focus(); // Focus the window before printing
        printWindow.print();
      }, 1000);
    }, 500);

    showSuccessToast(t('workshop2.activity2.print.documentReady'));
  };

  // Use our custom hook to warn about unsaved changes
  useUnsavedChangesWarning(hasUnsavedChanges);

  // Load sources de risque on component mount
  useEffect(() => {
    const fetchData = async () => {
      if (currentAnalysis?.id) {
        try {
          console.log("Activite2 - Fetching sources de risque for analysis:", currentAnalysis.id);

          // Show loading toast
          const toastId = showLoadingToast(t('common.loading'));

          // Load sources de risque
          const sourcesData = await getSourcesDeRisque(currentAnalysis.id);

          console.log("Activite2 - Received sources de risque data:", sourcesData);

          if (sourcesData && Array.isArray(sourcesData) && sourcesData.length > 0) {
            // Store the original sources data
            setSourcesRisque(sourcesData);

            // Format the sources data for the radar visualization
            const formattedCouples = sourcesData.map(source => ({
              id: source.id,
              sourceId: source.id,
              sourceName: source.name || t('workshop2.activity2.errors.sourceWithoutName'),
              sourceCategory: source.category || t('workshop2.activity2.errors.unknownCategory'),
              objectifVise: source.objectifVise || '',
              objectifViseCategory: source.objectifViseCategory || '',
              motivation: source.motivation || 'faible',
              activite: source.activite || 'faible',
              ressources: source.ressources || 'faibles',
              niveau: calculateNiveau(
                source.motivation || 'faible',
                source.activite || 'faible',
                source.ressources || 'faibles'
              ),
              selected: source.selected || false,
              justification: source.justification || ''
            }));

            setCouplesEvalues(formattedCouples);
            setOriginalSourcesRisque(JSON.parse(JSON.stringify(sourcesData)));

            // Dismiss the toast
            dismissToast(toastId);
          } else {
            console.log("No sources data found");

            // Initialize with empty arrays
            setSourcesRisque([]);
            setCouplesEvalues([]);
            setOriginalSourcesRisque([]);

            // Update toast
            updateToast(toastId, t('workshop2.activity2.errors.noRiskSources'), 'info');
          }
        } catch (error) {
          console.error('Error loading data in Activite2:', error);
          console.log("Error details:", {
            message: error.message,
            stack: error.stack,
            name: error.name
          });
          showErrorToast(t('workshop2.activity2.errors.loadingError', { message: error.message || t('workshop2.activity2.errors.unknownError') }));
        }
      }
    };

    fetchData();
  }, [currentAnalysis?.id, getSourcesDeRisque]);

  // Check for unsaved changes
  useEffect(() => {
    if (originalSourcesRisque && originalSourcesRisque.length > 0 && couplesEvalues && couplesEvalues.length > 0) {
      // Compare the current couples with the original sources
      const hasChanges = couplesEvalues.some(couple => {
        const originalSource = originalSourcesRisque.find(source => source.id === couple.sourceId);
        if (!originalSource) return true;

        return (
          couple.motivation !== originalSource.motivation ||
          couple.activite !== originalSource.activite ||
          couple.ressources !== originalSource.ressources ||
          couple.selected !== originalSource.selected ||
          couple.justification !== originalSource.justification
        );
      });

      setHasUnsavedChanges(hasChanges);
    }
  }, [couplesEvalues, originalSourcesRisque]);

  // Calculate niveau based on motivation, activite, and ressources
  const calculateNiveau = (motivation, activite, ressources) => {
    // Normalize inputs to handle different grammatical forms
    const normalizedMotivation = String(motivation || '').toLowerCase();
    const normalizedActivite = String(activite || '').toLowerCase();
    const normalizedRessources = String(ressources || '').toLowerCase();

    // Convert string values to numeric scores
    const motivationScore = normalizedMotivation.startsWith('elev') ? 3 :
                           normalizedMotivation.startsWith('moyen') ? 2 : 1;

    const activiteScore = normalizedActivite.startsWith('elev') ? 3 :
                         normalizedActivite.startsWith('moyen') ? 2 : 1;

    const ressourcesScore = normalizedRessources.startsWith('import') ? 3 :
                           normalizedRessources.startsWith('moyen') ? 2 : 1;

    // Calculate total score
    const totalScore = motivationScore + activiteScore + ressourcesScore;

    // Determine niveau based on the sum
    if (totalScore < 6) {
      return 1; // Risque modéré
    } else if (totalScore === 6) {
      return 2; // Risque moyen
    } else {
      return 3; // Risque élevé
    }
  };

  // Update couple evaluation
  const updateCouple = (id, field, value) => {
    const updatedCouples = couplesEvalues.map(couple => {
      if (couple.id === id || couple.sourceId === id) {
        const updatedCouple = { ...couple, [field]: value };

        // If one of the factors changed, recalculate the niveau
        if (field === 'motivation' || field === 'activite' || field === 'ressources') {
          updatedCouple.niveau = calculateNiveau(
            field === 'motivation' ? value : couple.motivation,
            field === 'activite' ? value : couple.activite,
            field === 'ressources' ? value : couple.ressources
          );
        }

        // If niveau is changed, update motivation, activite, and ressources accordingly
        if (field === 'niveau') {
          const level = parseInt(value);
          if (level === 3) {
            updatedCouple.motivation = 'eleve';
            updatedCouple.activite = 'eleve';
            updatedCouple.ressources = 'importantes';
          } else if (level === 2) {
            updatedCouple.motivation = 'moyen';
            updatedCouple.activite = 'moyen';
            updatedCouple.ressources = 'moyennes';
          } else {
            updatedCouple.motivation = 'faible';
            updatedCouple.activite = 'faible';
            updatedCouple.ressources = 'faibles';
          }
        }

        return updatedCouple;
      }
      return couple;
    });

    setCouplesEvalues(updatedCouples);
    setHasUnsavedChanges(true);
  };

  // Toggle selection of a couple
  const toggleCoupleSelection = (id) => {
    const updatedCouples = couplesEvalues.map(couple =>
      (couple.id === id || couple.sourceId === id) ? { ...couple, selected: !couple.selected } : couple
    );

    setCouplesEvalues(updatedCouples);
    setHasUnsavedChanges(true);
  };

  // Save all couples back to sources-risque
  const handleSaveAll = async () => {
    if (currentAnalysis?.id) {
      try {
        // Show loading toast
        const toastId = showLoadingToast(t('workshop2.activity2.saving'));

        // Update the original sources with the new evaluation data
        const updatedSources = sourcesRisque.map(source => {
          // Find the corresponding couple
          const couple = couplesEvalues.find(c => c.sourceId === source.id);

          if (couple) {
            // Update the source with the couple's data
            return {
              ...source,
              motivation: couple.motivation || 'faible',
              activite: couple.activite || 'faible',
              ressources: couple.ressources || 'faibles',
              selected: couple.selected || false,
              justification: couple.justification || ''
            };
          }

          return source;
        });

        // Save the updated sources
        const response = await saveSourcesDeRisque(currentAnalysis.id, updatedSources);

        if (response && response.success) {
          // Update original data to match current data
          setSourcesRisque(updatedSources);
          setOriginalSourcesRisque(JSON.parse(JSON.stringify(updatedSources)));

          // Reset unsaved changes flag
          setHasUnsavedChanges(false);

          // Show a success toast instead of dismissing it
          updateToast(toastId, t('workshop2.activity2.success.riskSourcesSaved'), 'success');
        } else {
          // Show error toast
          updateToast(toastId, t('workshop2.activity2.errors.saveError', { message: response?.message || t('workshop2.activity2.errors.unknownError') }), 'error');
        }
      } catch (error) {
        console.error('Error saving sources de risque:', error);

        // Show detailed error message
        if (error.message) {
          showErrorToast(t('workshop2.activity2.errors.errorWithMessage', { message: error.message }));
        } else {
          showErrorToast(t('workshop2.activity2.errors.saveRiskSourcesError'));
        }
      }
    } else {
      showErrorToast(t('workshop2.activity1.errors.noAnalysisSelected'));
    }
  };

  return (
    <div className="space-y-6" ref={contentRef}>
      {/* Header with modern design */}
      <div className="bg-gradient-to-r from-blue-50 via-white to-blue-50 rounded-xl shadow-sm border border-slate-100 p-6 mb-6">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          {/* Left Side: Breadcrumb & Title */}
          <div>
            <div className="flex items-center text-sm text-slate-500 mb-2">
              <span className="hover:text-blue-600 transition-colors">{t('workshop2.title')}</span>
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mx-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
              <span className="text-blue-600 font-medium">{t('workshop2.activity2.breadcrumb')}</span>
            </div>
            <h1 className="text-2xl sm:text-3xl font-bold text-slate-800 flex items-center">
              <AlertCircle size={28} className="mr-3 text-blue-600" />
              {t('workshop2.activity2.title')}
            </h1>
          </div>

          {/* Right Side: Action Buttons */}
          <div className="flex items-center gap-3 flex-wrap">
            {/* Save Button */}
            <button
              className={`text-sm font-medium px-4 py-2 rounded-md flex items-center shadow-sm transition duration-200 ${
                hasUnsavedChanges
                  ? 'bg-blue-600 text-white hover:bg-blue-700'
                  : 'bg-gray-100 text-gray-400 cursor-not-allowed'
              }`}
              onClick={handleSaveAll}
              disabled={!hasUnsavedChanges}
            >
              <Save size={16} className="mr-2" />
              {t('common.save')}
            </button>



            {/* Print Button */}
            <button
              className="text-sm font-medium bg-indigo-600 text-white px-4 py-2 rounded-md hover:bg-indigo-700 flex items-center shadow-sm transition duration-200"
              onClick={handlePrint}
            >
              <Printer size={16} className="mr-2" />
              {t('common.print')}
            </button>

            {/* Help Button */}
            <button
              className="text-sm font-medium bg-gray-100 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-200 flex items-center shadow-sm transition duration-200"
              onClick={() => setIsGuideOpen(true)}
            >
              <Info size={16} className="mr-2" />
              {t('common.help')}
            </button>
          </div>
        </div>
      </div>

      {/* View Toggle */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-semibold text-gray-800">{t('workshop2.activity2.evaluationTitle')}</h2>
          <div className="flex bg-gray-100 rounded-lg p-1">
            <button
              className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                activeView === 'table'
                  ? 'bg-white text-blue-600 shadow-sm'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
              onClick={() => setActiveView('table')}
            >
              {t('workshop2.activity2.views.table')}
            </button>
            <button
              className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                activeView === 'chart'
                  ? 'bg-white text-blue-600 shadow-sm'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
              onClick={() => setActiveView('chart')}
            >
              <BarChart2 size={16} className="inline mr-1" />
              {t('workshop2.activity2.views.visualization')}
            </button>
          </div>
        </div>

        {/* Content based on active view */}
        <div className="mt-4">
          {activeView === 'table' ? (
            <EvaluationTable
              couples={couplesEvalues}
              updateCouple={updateCouple}
              toggleSelection={toggleCoupleSelection}
              hasUnsavedChanges={hasUnsavedChanges}
              onSave={handleSaveAll}
            />
          ) : (
            <RadarVisualization
              couples={couplesEvalues}
              onCoupleUpdate={(id, field, value) => {
                updateCouple(id, field, value);
              }}
            />
          )}
        </div>
      </div>

      {/* Guide Modal */}
      <GuideModal isOpen={isGuideOpen} onClose={() => setIsGuideOpen(false)} />
    </div>
  );
};

export default Activite2;
