// routes/mitreRoutes.js
const express = require('express');
const axios = require('axios');
const router = express.Router();

// MITRE ATT&CK API URLs
const MITRE_STIX_URL = 'https://raw.githubusercontent.com/mitre/cti/master/enterprise-attack/enterprise-attack.json';
const MITRE_BASE_URL = 'https://attack.mitre.org/api/v2';
const LOCAL_MITRE_ATTACK_URL = 'http://localhost:3030/search/attack'; // Local MITRE ATT&CK API
const LOCAL_MITRE_ATLAS_URL = 'http://localhost:3030/search/atlas'; // Local MITRE ATLAS API
const LOCAL_MITRE_LEGACY_URL = 'http://localhost:3030/search'; // Legacy combined endpoint

// NOTE: These routes are intentionally PUBLIC (no authentication required)
// They serve as proxy routes for external CTI APIs and should be accessible
// without authentication to allow CTI analysis functionality

// Middleware to explicitly bypass any global authentication
router.use((req, res, next) => {
  // Remove any authorization headers that might cause issues
  delete req.headers.authorization;
  console.log(`[MITRE Proxy] ${req.method} ${req.path} - Authentication bypassed`);
  next();
});

// Cache for MITRE data (refresh every 24 hours)
let mitreCache = {
  stixData: {
    data: null,
    lastUpdated: null,
    cacheExpiry: 24 * 60 * 60 * 1000 // 24 hours in milliseconds
  }
};

// @route   GET /api/mitre/test
// @desc    Test endpoint to verify MITRE routes are working
// @access  Public (no auth required)
router.get('/test', (req, res) => {
  console.log('[MITRE Proxy] Test endpoint called - routes are working!');
  res.json({
    success: true,
    message: 'MITRE proxy routes are working',
    timestamp: new Date().toISOString(),
    stixUrl: MITRE_STIX_URL,
    baseUrl: MITRE_BASE_URL
  });
});

// @route   GET /api/mitre/stix-data
// @desc    Proxy MITRE ATT&CK STIX data from GitHub
// @access  Public
router.get('/stix-data', async (req, res) => {
  try {
    console.log('[MITRE Proxy] STIX data request');

    // Check cache first
    const now = Date.now();
    if (mitreCache.stixData.data && mitreCache.stixData.lastUpdated &&
        (now - mitreCache.stixData.lastUpdated) < mitreCache.stixData.cacheExpiry) {
      console.log('[MITRE Proxy] Using cached STIX data');
      return res.json(mitreCache.stixData.data);
    }

    console.log('[MITRE Proxy] Fetching fresh STIX data from GitHub...');
    console.log('[MITRE Proxy] STIX URL:', MITRE_STIX_URL);

    const response = await axios.get(MITRE_STIX_URL, {
      headers: {
        'Accept': 'application/json',
        'User-Agent': 'EBIOS-RM-CTI-Service/1.0'
      },
      timeout: 120000, // Increase to 120 seconds for large STIX file
      maxContentLength: 100 * 1024 * 1024, // 100MB max content length
      maxBodyLength: 100 * 1024 * 1024 // 100MB max body length
    });

    console.log('[MITRE Proxy] STIX data response:', {
      status: response.status,
      objectsCount: response.data?.objects?.length || 0,
      dataSize: JSON.stringify(response.data).length
    });

    // Update cache
    mitreCache.stixData.data = response.data;
    mitreCache.stixData.lastUpdated = now;

    res.json(response.data);
  } catch (error) {
    console.error('[MITRE Proxy] STIX data error:', {
      message: error.message,
      status: error.response?.status,
      statusText: error.response?.statusText
    });

    res.status(error.response?.status || 500).json({
      error: 'MITRE STIX data request failed',
      message: error.message,
      status: error.response?.status || 500
    });
  }
});

// @route   GET /api/mitre/techniques
// @desc    Get parsed MITRE techniques from STIX data
// @access  Public
router.get('/techniques', async (req, res) => {
  try {
    console.log('[MITRE Proxy] Techniques request');

    // Get STIX data (from cache or fresh)
    const stixResponse = await axios.get(`${req.protocol}://${req.get('host')}/api/mitre/stix-data`);
    const stixData = stixResponse.data;

    // Parse techniques from STIX data
    const techniques = stixData.objects
      ?.filter(obj => obj.type === 'attack-pattern')
      ?.map(technique => ({
        id: technique.id,
        techniqueId: technique.external_references?.[0]?.external_id || 'Unknown',
        name: technique.name || 'Unknown Technique',
        description: technique.description || 'No description available',
        tactics: technique.kill_chain_phases?.map(phase => phase.phase_name) || [],
        platforms: technique.x_mitre_platforms || [],
        detection: technique.x_mitre_detection || 'No detection information available',
        isSubtechnique: technique.x_mitre_is_subtechnique || false,
        references: technique.external_references || [],
        created: technique.created,
        modified: technique.modified
      })) || [];

    console.log('[MITRE Proxy] Parsed techniques:', {
      totalTechniques: techniques.length,
      subtechniques: techniques.filter(t => t.isSubtechnique).length
    });

    res.json({
      techniques: techniques,
      totalCount: techniques.length,
      lastUpdated: mitreCache.stixData.lastUpdated,
      source: 'MITRE ATT&CK Enterprise'
    });

  } catch (error) {
    console.error('[MITRE Proxy] Techniques parsing error:', {
      message: error.message,
      status: error.response?.status
    });

    res.status(error.response?.status || 500).json({
      error: 'MITRE techniques parsing failed',
      message: error.message,
      status: error.response?.status || 500
    });
  }
});

// @route   GET /api/mitre/groups
// @desc    Get MITRE threat groups from STIX data
// @access  Public
router.get('/groups', async (req, res) => {
  try {
    console.log('[MITRE Proxy] Threat groups request');

    // Get STIX data
    const stixResponse = await axios.get(`${req.protocol}://${req.get('host')}/api/mitre/stix-data`);
    const stixData = stixResponse.data;

    // Parse threat groups from STIX data
    const groups = stixData.objects
      ?.filter(obj => obj.type === 'intrusion-set')
      ?.map(group => ({
        id: group.id,
        name: group.name || 'Unknown Group',
        description: group.description || 'No description available',
        aliases: group.aliases || [],
        references: group.external_references || [],
        created: group.created,
        modified: group.modified
      })) || [];

    console.log('[MITRE Proxy] Parsed threat groups:', {
      totalGroups: groups.length
    });

    res.json({
      groups: groups,
      totalCount: groups.length,
      lastUpdated: mitreCache.stixData.lastUpdated,
      source: 'MITRE ATT&CK Enterprise'
    });

  } catch (error) {
    console.error('[MITRE Proxy] Threat groups parsing error:', {
      message: error.message,
      status: error.response?.status
    });

    res.status(error.response?.status || 500).json({
      error: 'MITRE threat groups parsing failed',
      message: error.message,
      status: error.response?.status || 500
    });
  }
});

// @route   GET /api/mitre/clear-cache
// @desc    Clear MITRE cache (for testing/debugging)
// @access  Public
router.get('/clear-cache', (req, res) => {
  console.log('[MITRE Proxy] Clearing cache');

  mitreCache.stixData.data = null;
  mitreCache.stixData.lastUpdated = null;

  res.json({
    success: true,
    message: 'MITRE cache cleared',
    timestamp: new Date().toISOString()
  });
});

// @route   GET /api/mitre/local-attack
// @desc    Proxy to local MITRE ATT&CK API server
// @access  Public
router.get('/local-attack', async (req, res) => {
  try {
    console.log('[MITRE Proxy] Local ATT&CK API search request:', req.query);

    // Forward all query parameters to local MITRE ATT&CK API
    const params = new URLSearchParams(req.query);
    const localApiUrl = `${LOCAL_MITRE_ATTACK_URL}?${params}`;

    console.log('[MITRE Proxy] Calling local ATT&CK API:', localApiUrl);

    let response;
    try {
      // Try new separate endpoint first
      response = await axios.get(localApiUrl, {
        timeout: 15000, // 15 second timeout
        headers: {
          'Accept': 'application/json',
          'User-Agent': 'EBIOS-RM-Backend-Proxy/1.0'
        }
      });
    } catch (error) {
      if (error.response?.status === 404) {
        console.log('[MITRE Proxy] New endpoint not found, trying legacy endpoint');
        // Fallback to legacy combined endpoint
        const legacyUrl = `${LOCAL_MITRE_LEGACY_URL}?${params}`;
        response = await axios.get(legacyUrl, {
          timeout: 15000,
          headers: {
            'Accept': 'application/json',
            'User-Agent': 'EBIOS-RM-Backend-Proxy/1.0'
          }
        });

        // Extract only ATT&CK techniques from legacy response
        if (response.data && response.data.enterpriseAttack) {
          response.data = response.data.enterpriseAttack;
        }
      } else {
        throw error;
      }
    }

    console.log('[MITRE Proxy] Local ATT&CK API response:', {
      status: response.status,
      resultsCount: Array.isArray(response.data) ? response.data.length : 0
    });

    // Return the response from local MITRE ATT&CK API
    res.json({
      success: true,
      data: response.data, // Should be an array of ATT&CK techniques
      source: 'Local MITRE ATT&CK API (localhost:3030/search/attack)',
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('[MITRE Proxy] Local ATT&CK API error:', {
      message: error.message,
      code: error.code,
      status: error.response?.status
    });

    // Return error response
    res.status(error.response?.status || 500).json({
      success: false,
      error: 'Local MITRE ATT&CK API request failed',
      message: error.message,
      status: error.response?.status || 500,
      fallback: 'Consider using GitHub STIX data as fallback'
    });
  }
});

// @route   GET /api/mitre/local-atlas
// @desc    Proxy to local MITRE ATLAS API server
// @access  Public
router.get('/local-atlas', async (req, res) => {
  try {
    console.log('[MITRE Proxy] Local ATLAS API search request:', req.query);

    // Forward all query parameters to local MITRE ATLAS API
    const params = new URLSearchParams(req.query);
    const localApiUrl = `${LOCAL_MITRE_ATLAS_URL}?${params}`;

    console.log('[MITRE Proxy] Calling local ATLAS API:', localApiUrl);

    const response = await axios.get(localApiUrl, {
      timeout: 15000, // 15 second timeout
      headers: {
        'Accept': 'application/json',
        'User-Agent': 'EBIOS-RM-Backend-Proxy/1.0'
      }
    });

    console.log('[MITRE Proxy] Local ATLAS API response:', {
      status: response.status,
      resultsCount: Array.isArray(response.data) ? response.data.length : 0
    });

    // Return the response from local MITRE ATLAS API
    res.json({
      success: true,
      data: response.data, // Should be an array of ATLAS techniques
      source: 'Local MITRE ATLAS API (localhost:3030/search/atlas)',
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('[MITRE Proxy] Local ATLAS API error:', {
      message: error.message,
      code: error.code,
      status: error.response?.status
    });

    // Return error response
    res.status(error.response?.status || 500).json({
      success: false,
      error: 'Local MITRE ATLAS API request failed',
      message: error.message,
      status: error.response?.status || 500,
      fallback: 'Consider using static ATLAS data as fallback'
    });
  }
});

module.exports = router;
