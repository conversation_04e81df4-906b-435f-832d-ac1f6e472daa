#!/bin/bash

# Update the search strategy in ctiService.js
FILE="src/services/ctiService.js"

# First, let's replace the search strategy section (lines 150-166)
sed -i '150,166c\
      // Try multiple search strategies in order of effectiveness\
      let vulnerabilities = [];\
      \
      // Strategy 1: Keyword search (most likely to find results)\
      console.log(`[CTI] Starting with keyword search for ${asset.name}`);\
      vulnerabilities = await this.searchByKeywords(asset);\
      \
      // Strategy 2: If few results, try virtual CPE match (partial CPE)\
      if (vulnerabilities.length < 3) {\
        console.log(`[CTI] Found ${vulnerabilities.length} with keywords, trying virtual CPE for ${asset.name}`);\
        const virtualResults = await this.searchByVirtualCPE(asset);\
        // Merge results, avoiding duplicates\
        const existingIds = new Set(vulnerabilities.map(v => v.id));\
        const newResults = virtualResults.filter(v => !existingIds.has(v.id));\
        vulnerabilities = [...vulnerabilities, ...newResults];\
      }\
      \
      // Strategy 3: If still few results, try exact CPE\
      if (vulnerabilities.length < 5) {\
        console.log(`[CTI] Found ${vulnerabilities.length} total, trying exact CPE for ${asset.name}`);\
        const cpeResults = await this.searchByCPE(asset);\
        const existingIds = new Set(vulnerabilities.map(v => v.id));\
        const newResults = cpeResults.filter(v => !existingIds.has(v.id));\
        vulnerabilities = [...vulnerabilities, ...newResults];\
      }' "$FILE"

echo "Updated search strategy in ctiService.js"
