// src/components/Atelier3/Activite4/Activite4.jsx
import React, { useState, useEffect } from 'react';
import { useAnalysis } from '../../../context/AnalysisContext';
import { useTranslation } from 'react-i18next';
import { showSuccessToast, showErrorToast, showLoadingToast, dismissToast } from '../../../utils/toastUtils';
import { Shield, Plus, Save, Lightbulb, Info } from 'lucide-react';
import MeasureTable from './MeasureTable';
import MeasureForm from './MeasureForm';
import AIGenerationModal from './AIGenerationModal';
import ecosystemMeasuresService from '../../../services/ecosystemMeasuresService';

const Activite4 = () => {
  const { currentAnalysis } = useAnalysis();
  const { t } = useTranslation();
  const [ecosystemMeasures, setEcosystemMeasures] = useState([]);
  const [showAddForm, setShowAddForm] = useState(false);
  const [showAIModal, setShowAIModal] = useState(false);
  const [editingMeasure, setEditingMeasure] = useState(null);
  const [isLoading, setIsLoading] = useState(true);

  // Load existing measures
  useEffect(() => {
    if (currentAnalysis?.id) {
      loadEcosystemMeasures();
    } else {
      setIsLoading(false);
    }
  }, [currentAnalysis]);

  // Load measures from API
  const loadEcosystemMeasures = async () => {
    setIsLoading(true);
    try {
      const response = await ecosystemMeasuresService.getEcosystemMeasures(currentAnalysis.id);
      if (response.success && response.data) {
        setEcosystemMeasures(response.data);
      } else {
        setEcosystemMeasures([]);
      }
    } catch (error) {
      console.error('Error loading ecosystem measures:', error);
      setEcosystemMeasures([]);
    } finally {
      setIsLoading(false);
    }
  };

  // Save measures to API
  const saveMeasures = async () => {
    const toastId = showLoadingToast(t('workshop3.activity4.saving'));
    try {
      const response = await ecosystemMeasuresService.saveEcosystemMeasures(
        currentAnalysis.id,
        ecosystemMeasures
      );

      if (response.success) {
        dismissToast(toastId);
        showSuccessToast(t('workshop3.activity4.success.measuresSaved'));
        return true;
      } else {
        throw new Error(response.message || t('workshop3.activity4.errors.saveError'));
      }
    } catch (error) {
      console.error('Error saving ecosystem measures:', error);
      dismissToast(toastId);
      showErrorToast(`${t('workshop3.activity4.errors.error')}: ${error.message || t('workshop3.activity4.errors.saveProblem')}`);
      return false;
    }
  };

  // Handle AI-generated measures
  const handleAIGenerated = (generatedMeasures) => {
    // Add generated measures to existing ones
    setEcosystemMeasures(prev => {
      // Filter out duplicates based on title
      const existingTitles = new Set(prev.map(m => m.title));

      // Process new measures to ensure they have proper IDs and timestamps
      const newMeasures = generatedMeasures
        .filter(m => !existingTitles.has(m.title))
        .map(measure => ({
          ...measure,
          id: measure.id || `measure-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
          createdAt: measure.createdAt || new Date().toISOString(),
          updatedAt: new Date().toISOString()
        }));

      return [...prev, ...newMeasures];
    });

    setShowAIModal(false);
  };

  // Add or update a measure
  const handleSaveMeasure = (measure) => {
    if (editingMeasure) {
      // Update existing measure
      setEcosystemMeasures(prev =>
        prev.map(m => m.id === editingMeasure.id ? { ...measure, id: m.id } : m)
      );
    } else {
      // Add new measure
      setEcosystemMeasures(prev => [
        ...prev,
        { ...measure, id: `measure-${Date.now()}` }
      ]);
    }

    setShowAddForm(false);
    setEditingMeasure(null);
  };

  // Delete a measure
  const handleDeleteMeasure = (measureId) => {
    setEcosystemMeasures(prev => prev.filter(m => m.id !== measureId));
  };

  // Bulk delete measures
  const handleBulkDeleteMeasures = (measureIds) => {
    setEcosystemMeasures(prev => prev.filter(m => !measureIds.includes(m.id)));
  };

  return (
    <div className="space-y-6">
      {/* Enhanced Header - Matching Scénarios Stratégiques style */}
      <div className="bg-white rounded-xl shadow-sm border border-slate-100 p-4 mb-6">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          {/* Left Side: Breadcrumb & Title */}
          <div>
            <div className="flex items-center text-sm text-slate-500 mb-2">
              <span className="hover:text-blue-600 transition-colors">{t('navigation.workshop3')}</span>
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mx-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
              <span className="text-blue-600 font-medium">{t('workshop3.activity4.breadcrumb')}</span>
            </div>
            <h1 className="text-2xl sm:text-3xl font-bold text-slate-800 flex items-center">
              <Shield size={28} className="mr-3 text-blue-600" />
{t('workshop3.activity4.title')}
            </h1>
          </div>

          {/* Right Side: Action Buttons */}
          <div className="flex items-center space-x-3">
            {/* Help Button */}
            <button
              className="text-sm font-medium bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 flex items-center shadow-sm transition duration-200"
              onClick={() => {/* TODO: Add help modal */}}
            >
              <Info size={16} className="mr-2" />
{t('common.help')}
            </button>

            {/* AI Generation Button */}
            <button
              onClick={() => setShowAIModal(true)}
              className="text-sm font-medium bg-purple-100 text-purple-700 px-4 py-2 rounded-lg hover:bg-purple-200 flex items-center shadow-sm transition duration-200"
            >
              <Lightbulb size={16} className="mr-2" />
{t('workshop3.activity4.generateWithAI')}
            </button>

            {/* Add Manual Button */}
            <button
              onClick={() => {
                setEditingMeasure(null);
                setShowAddForm(true);
              }}
              className="text-sm font-medium bg-blue-100 text-blue-700 px-4 py-2 rounded-lg hover:bg-blue-200 flex items-center shadow-sm transition duration-200"
            >
              <Plus size={16} className="mr-2" />
{t('common.add')}
            </button>

            {/* Save Button */}
            <button
              onClick={saveMeasures}
              className="text-sm font-medium bg-green-100 text-green-700 px-4 py-2 rounded-lg hover:bg-green-200 flex items-center shadow-sm transition duration-200"
            >
              <Save size={16} className="mr-2" />
{t('common.save')}
            </button>
          </div>
        </div>

        {/* Description */}
        <div className="mt-4 p-4 bg-blue-50 rounded-lg border border-blue-100">
          <p className="text-sm text-blue-700">
            {t('workshop3.activity4.description')}
          </p>
        </div>
      </div>

      {/* Measure Form */}
      {showAddForm && (
        <MeasureForm
          measure={editingMeasure}
          onSave={handleSaveMeasure}
          onCancel={() => {
            setShowAddForm(false);
            setEditingMeasure(null);
          }}
          analysisId={currentAnalysis?.id}
        />
      )}

      {/* Measures Table */}
      <MeasureTable
        measures={ecosystemMeasures}
        onEdit={(measure) => {
          setEditingMeasure(measure);
          setShowAddForm(true);
        }}
        onDelete={handleDeleteMeasure}
        onBulkDelete={handleBulkDeleteMeasures}
        isLoading={isLoading}
      />

      {/* AI Generation Modal */}
      {showAIModal && (
        <AIGenerationModal
          onClose={() => setShowAIModal(false)}
          onGenerate={handleAIGenerated}
          analysisId={currentAnalysis?.id}
        />
      )}
    </div>
  );
};

export default Activite4;
