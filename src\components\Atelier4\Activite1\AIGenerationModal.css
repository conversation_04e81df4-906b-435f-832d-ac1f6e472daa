/* src/components/Atelier4/Activite2/AIGenerationModal.css */

.ai-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
}

.ai-modal {
  background: white;
  border-radius: 16px;
  width: 100%;
  max-width: 900px;
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

.modal-header {
  padding: 25px;
  border-bottom: 1px solid #dee2e6;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
  color: white;
}

.header-content h3 {
  margin: 0 0 8px 0;
  font-size: 1.4rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 10px;
}

.header-content p {
  margin: 0;
  opacity: 0.9;
  font-size: 0.95rem;
  line-height: 1.4;
}

.close-btn {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  color: white;
  font-size: 1.2rem;
  cursor: pointer;
  padding: 8px;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.close-btn:hover {
  background: rgba(255, 255, 255, 0.3);
}

.modal-body {
  padding: 25px;
  overflow-y: auto;
  flex: 1;
}

.generation-options {
  margin-bottom: 30px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 12px;
  border: 1px solid #e9ecef;
}

.generation-options h4 {
  margin: 0 0 20px 0;
  color: #495057;
  font-size: 1.1rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
}

.generation-options h4::before {
  content: "⚙️";
  font-size: 1rem;
}

.options-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.option-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.option-group label {
  font-weight: 500;
  color: #495057;
  font-size: 0.9rem;
}

.option-group select {
  padding: 10px 12px;
  border: 1px solid #ced4da;
  border-radius: 8px;
  background: white;
  color: #495057;
  font-size: 0.9rem;
  transition: all 0.2s ease;
}

.option-group select:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

.option-checkbox {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 12px;
  background: white;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.option-checkbox:hover {
  background: #f8f9fa;
  border-color: #007bff;
}

.option-checkbox label {
  display: flex;
  align-items: center;
  gap: 10px;
  cursor: pointer;
  margin: 0;
  font-weight: 500;
  color: #495057;
}

.option-checkbox input[type="checkbox"] {
  margin: 0;
  transform: scale(1.1);
}

.path-selection {
  margin-bottom: 25px;
}

.selection-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  flex-wrap: wrap;
  gap: 15px;
}

.selection-header h4 {
  margin: 0;
  color: #495057;
  font-size: 1.1rem;
  font-weight: 600;
}

.select-all-btn {
  background: #6c757d;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.9rem;
  font-weight: 500;
  transition: all 0.2s ease;
}

.select-all-btn:hover {
  background: #545b62;
  transform: translateY(-1px);
}

.paths-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
  max-height: 300px;
  overflow-y: auto;
  padding: 5px;
}

.path-item {
  border: 2px solid #e9ecef;
  border-radius: 12px;
  transition: all 0.2s ease;
  background: white;
}

.path-item.selected {
  border-color: #007bff;
  background: #f8f9fa;
  box-shadow: 0 2px 8px rgba(0, 123, 255, 0.1);
}

.path-item:hover {
  border-color: #007bff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.path-checkbox {
  display: block;
  cursor: pointer;
  padding: 16px;
  margin: 0;
}

.path-checkbox input[type="checkbox"] {
  margin: 0 12px 0 0;
  transform: scale(1.2);
}

.path-content {
  flex: 1;
}

.path-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 10px;
  gap: 15px;
}

.path-name {
  font-weight: 600;
  color: #495057;
  font-size: 1rem;
  line-height: 1.3;
  flex: 1;
}

.existing-badge {
  background: #17a2b8;
  color: white;
  padding: 3px 8px;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 500;
  white-space: nowrap;
}

.path-details {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.path-info {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #6c757d;
  font-size: 0.85rem;
  font-weight: 500;
}

.info-item i {
  color: #007bff;
  width: 14px;
}

.path-description {
  margin: 0;
  color: #6c757d;
  font-size: 0.85rem;
  line-height: 1.4;
  font-style: italic;
}

.generation-summary {
  margin-bottom: 20px;
}

.summary-card {
  background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
  border: 1px solid #bbdefb;
  border-radius: 12px;
  padding: 20px;
}

.summary-card h4 {
  margin: 0 0 15px 0;
  color: #1565c0;
  font-size: 1rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
}

.summary-card h4::before {
  content: "📊";
  font-size: 1rem;
}

.summary-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 15px;
}

.stat {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 5px;
  padding: 12px;
  background: white;
  border-radius: 8px;
  border: 1px solid #e1f5fe;
}

.stat-label {
  font-size: 0.8rem;
  color: #6c757d;
  text-transform: uppercase;
  font-weight: 500;
  text-align: center;
}

.stat-value {
  font-size: 1.2rem;
  font-weight: 600;
  color: #1565c0;
}

.modal-footer {
  padding: 20px 25px;
  border-top: 1px solid #dee2e6;
  background: #f8f9fa;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 15px;
}

.footer-info {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 0.9rem;
  font-weight: 500;
}

.footer-info .warning {
  color: #dc3545;
}

.footer-info .info {
  color: #17a2b8;
}

.footer-info i {
  font-size: 1rem;
}

.footer-actions {
  display: flex;
  gap: 12px;
}

.btn {
  padding: 10px 20px;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 0.9rem;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.2s ease;
  text-decoration: none;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
}

.btn-secondary {
  background: #6c757d;
  color: white;
}

.btn-secondary:hover:not(:disabled) {
  background: #545b62;
  transform: translateY(-1px);
}

.btn-primary {
  background: #007bff;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background: #0056b3;
  transform: translateY(-1px);
}

.spinner {
  width: 16px;
  height: 16px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
  .ai-modal-overlay {
    padding: 10px;
  }

  .ai-modal {
    max-width: 100%;
    max-height: 95vh;
  }

  .modal-header {
    padding: 20px;
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }

  .close-btn {
    align-self: flex-end;
  }

  .modal-body {
    padding: 20px;
  }

  .options-grid {
    grid-template-columns: 1fr;
  }

  .selection-header {
    flex-direction: column;
    align-items: stretch;
  }

  .path-header {
    flex-direction: column;
    gap: 8px;
  }

  .path-info {
    flex-direction: column;
    gap: 8px;
  }

  .summary-stats {
    grid-template-columns: 1fr;
  }

  .modal-footer {
    flex-direction: column;
    align-items: stretch;
  }

  .footer-actions {
    justify-content: stretch;
  }

  .btn {
    flex: 1;
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .modal-header {
    padding: 15px;
  }

  .header-content h3 {
    font-size: 1.2rem;
  }

  .modal-body {
    padding: 15px;
  }

  .generation-options {
    padding: 15px;
  }

  .path-checkbox {
    padding: 12px;
  }

  .summary-card {
    padding: 15px;
  }

  .modal-footer {
    padding: 15px;
  }
}