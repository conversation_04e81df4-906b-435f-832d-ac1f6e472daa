import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useTranslation } from 'react-i18next';
import BusinessValuesHeader from './BusinessValuesHeader';
import BusinessValuesAdd from './BusinessValuesAdd';
import BusinessValueListView from './BusinessValueListView';
import BusinessValueChartView from './BusinessValueChartView';
import GuideModal from './GuideModal';
import { getShortId, ensureShortIds, ensureSupportAssetShortIds, getSupportAssetShortId } from './businessValuesUtils';
import { useAnalysis } from '../../../context/AnalysisContext'; // Import useAnalysis hook
import { showSuccessToast, showErrorToast, showLoadingToast, updateToast } from '../../../utils/toastUtils';

// Asset Types will be translated dynamically

// Simple Modal Component (can be replaced with a library component)
const Modal = ({ isOpen, onClose, children }) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50 flex justify-center items-center">
      <div className="relative bg-white p-6 rounded-lg shadow-xl w-full max-w-md">
        <button
          onClick={onClose}
          className="absolute top-2 right-2 text-gray-500 hover:text-gray-700"
          aria-label="Close modal"
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
        {children}
      </div>
    </div>
  );
};

const BusinessValues = () => {
  const { t } = useTranslation();

  // Define Asset Types with translations
  const ASSET_TYPES = [
    { value: 'organisation', label: t('businessValues.assetTypes.organisation') },
    { value: 'locaux', label: t('businessValues.assetTypes.locaux') },
    { value: 'equipements', label: t('businessValues.assetTypes.equipements') },
    { value: 'reseaux', label: t('businessValues.assetTypes.reseaux') },
    { value: 'logiciels', label: t('businessValues.assetTypes.logiciels') }
  ];

  // Get data and functions from AnalysisContext
  const {
    currentBusinessValues,
    saveCurrentBusinessValues,
    isWorkshopDataLoading,
    workshopDataError
  } = useAnalysis();

  // Local state for UI and form handling
  const [businessValues, setBusinessValues] = useState([]);
  const [newBusinessValue, setNewBusinessValue] = useState("");
  const [newBusinessValueDescription, setNewBusinessValueDescription] = useState("");
  const [editingValue, setEditingValue] = useState(null);
  const [editValue, setEditValue] = useState('');
  const [editDescription, setEditDescription] = useState('');
  const [expandedValue, setExpandedValue] = useState(null);
  const [newSupportAssetName, setNewSupportAssetName] = useState('');
  const [newSupportAssetDescription, setNewSupportAssetDescription] = useState('');
  const [newSupportAssetType, setNewSupportAssetType] = useState(ASSET_TYPES[0].value);
  const [newSupportAssetOwner, setNewSupportAssetOwner] = useState('');
  const [newSupportAssetLocation, setNewSupportAssetLocation] = useState('');

  // Enhanced fields for CPE discovery
  const [newSupportAssetVendor, setNewSupportAssetVendor] = useState('');
  const [newSupportAssetProduct, setNewSupportAssetProduct] = useState('');
  const [newSupportAssetVersion, setNewSupportAssetVersion] = useState('');
  const [discoveredCPE, setDiscoveredCPE] = useState('');
  const [cpeConfidence, setCpeConfidence] = useState('');
  const [cpeSearching, setCpeSearching] = useState(false);
  const [viewMode, setViewMode] = useState('list');
  const [selectedValueForChart, setSelectedValueForChart] = useState(null);
  const [zoomLevel, setZoomLevel] = useState(0.75); // Start with 1.5x zoom out (0.75 zoom level)
  const [viewportPosition, setViewportPosition] = useState({ x: 0, y: 0 });
  const [isDragging, setIsDragging] = useState(false);
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 });
  const [useShortIds, setUseShortIds] = useState(true);
  const chartContainerRef = useRef(null);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);

  // New state for modal and help section visibility
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [isHelpVisible, setIsHelpVisible] = useState(true); // Start with help visible
  const [isGuideOpen, setIsGuideOpen] = useState(false); // State for guide modal

  // --- State for Editing Support Assets ---
  const [editingAsset, setEditingAsset] = useState(null); // Holds the asset object being edited {businessValueId: string, asset: object}

  // CPE Discovery Service
  const discoverCPE = async (vendor, product, version) => {
    if (!vendor || !product) return null;

    setCpeSearching(true);
    try {
      // Method 1: Try CIRCL CVE Search API (free)
      const searchQuery = `${vendor} ${product} ${version}`.trim();
      const response = await fetch(`https://cve.circl.lu/api/search/${encodeURIComponent(searchQuery)}`);

      if (response.ok) {
        const data = await response.json();
        if (data && data.length > 0) {
          // Look for CPE in the first result
          const firstResult = data[0];
          if (firstResult.vulnerable_configuration && firstResult.vulnerable_configuration.length > 0) {
            const cpe = firstResult.vulnerable_configuration[0];
            setDiscoveredCPE(cpe);
            setCpeConfidence('High');
            return cpe;
          }
        }
      }

      // Method 2: Generate CPE based on common patterns (fallback)
      const generatedCPE = generateCPE(vendor, product, version);
      setDiscoveredCPE(generatedCPE);
      setCpeConfidence('Generated');
      return generatedCPE;

    } catch (error) {
      console.error('CPE discovery error:', error);
      // Fallback to generated CPE
      const generatedCPE = generateCPE(vendor, product, version);
      setDiscoveredCPE(generatedCPE);
      setCpeConfidence('Generated');
      return generatedCPE;
    } finally {
      setCpeSearching(false);
    }
  };

  // Generate CPE based on common patterns
  const generateCPE = (vendor, product, version) => {
    const cleanVendor = vendor.toLowerCase().replace(/[^a-z0-9]/g, '_');
    const cleanProduct = product.toLowerCase().replace(/[^a-z0-9]/g, '_');
    const cleanVersion = version || '*';

    return `cpe:2.3:a:${cleanVendor}:${cleanProduct}:${cleanVersion}:*:*:*:*:*:*:*`;
  };

  // Auto-discover CPE when vendor/product/version change
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (newSupportAssetVendor && newSupportAssetProduct) {
        discoverCPE(newSupportAssetVendor, newSupportAssetProduct, newSupportAssetVersion);
      } else {
        setDiscoveredCPE('');
        setCpeConfidence('');
      }
    }, 1000); // Debounce for 1 second

    return () => clearTimeout(timeoutId);
  }, [newSupportAssetVendor, newSupportAssetProduct, newSupportAssetVersion]);

  // Update local state when context data changes
  useEffect(() => {
    if (currentBusinessValues && Array.isArray(currentBusinessValues)) {
      // Ensure all business values have shortIds
      let updatedValues = ensureShortIds(currentBusinessValues);
      // Ensure all support assets have shortIds
      updatedValues = ensureSupportAssetShortIds(updatedValues);
      setBusinessValues(updatedValues);
    }
  }, [currentBusinessValues]);

  // Initialize chart view with first business value if none selected
  useEffect(() => {
    if (viewMode === 'chart') {
      // Reset zoom to 0.75 (1.5x zoom out) when switching to chart view
      setZoomLevel(0.75);
      setViewportPosition({ x: 0, y: 0 });

      // Select first business value if none selected
      if (businessValues.length > 0 && !selectedValueForChart) {
        setSelectedValueForChart(businessValues[0].id);
      }
    }
  }, [viewMode, businessValues, selectedValueForChart]);

  // Function to save business values to the backend
  const handleSaveData = async () => {
    console.log("Saving business values:", businessValues);
    // Show loading toast
    const toastId = showLoadingToast(t('businessValues.status.saving'));

    try {
      const response = await saveCurrentBusinessValues(businessValues);
      if (response.success) {
        // Update loading toast to success
        updateToast(toastId, t('businessValues.status.saved'), 'success');

        // Reset unsaved changes flag
        setHasUnsavedChanges(false);
      } else {
        // Update loading toast to error
        updateToast(toastId, `${t('businessValues.status.saveError')}: ${response.message || 'Erreur inconnue'}`, 'error');
      }
    } catch (error) {
      console.error('Error saving business values:', error);
      // Update loading toast to error
      updateToast(toastId, `${t('businessValues.status.saveError')}: ${error.message || 'Erreur inconnue'}`, 'error');
    }
  };

  // Functions to manage business values
  const handleAddBusinessValue = () => {
    if (newBusinessValue.trim()) {
      const newValue = {
        id: Date.now(),
        name: newBusinessValue,
        description: newBusinessValueDescription,
        shortId: getShortId(businessValues.length),
        supportAssets: [],
        securityPillars: []
      };

      setBusinessValues([...businessValues, newValue]);
      setNewBusinessValue('');
      setNewBusinessValueDescription('');
      setIsAddModalOpen(false); // Close modal on add

      // Set unsaved changes flag
      setHasUnsavedChanges(true);

      // Show success toast
      showSuccessToast(t('businessValues.status.valueAdded', { name: newValue.name }));
    } else {
      // Show error toast if name is empty
      showErrorToast(t('businessValues.status.emptyNameError'));
    }
  };

  const handleRemoveBusinessValue = (id) => {
    // Find the business value to be removed for the toast message
    const valueToRemove = businessValues.find(value => value.id === id);

    // Update state
    setBusinessValues(businessValues.filter(value => value.id !== id));
    if (selectedValueForChart === id)
      setSelectedValueForChart(businessValues.filter(v => v.id !== id)[0]?.id || null);
    if (expandedValue === id) setExpandedValue(null);

    // Set unsaved changes flag
    setHasUnsavedChanges(true);

    // Show toast notification
    if (valueToRemove) {
      showSuccessToast(t('businessValues.status.valueRemoved', { name: valueToRemove.name }));
    }
  };

  // --- Support asset management ---
  const handleAddSupportAsset = (businessValueId) => {
    if (!newSupportAssetName.trim()) return; // Check name specifically

    const newAsset = {
      id: `support-${Date.now()}-${Math.random().toString(16).slice(2)}`, // More unique ID
      name: newSupportAssetName,
      description: newSupportAssetDescription,
      type: newSupportAssetType,
      owner: newSupportAssetOwner,
      location: newSupportAssetLocation,
      // Enhanced CPE fields
      vendor: newSupportAssetVendor,
      product: newSupportAssetProduct,
      version: newSupportAssetVersion,
      cpe: discoveredCPE,
      cpeConfidence: cpeConfidence,
      // shortId will be added below dynamically
    };

    setBusinessValues(currentBusinessValues =>
        currentBusinessValues.map(value => {
            if (value.id !== businessValueId) return value;

            const updatedAssets = [...(value.supportAssets || [])];
            // Add shortId based on the *new* length
            newAsset.shortId = getSupportAssetShortId(value.shortId, updatedAssets.length);
            updatedAssets.push(newAsset);

            return { ...value, supportAssets: updatedAssets };
        })
    );

    // Set unsaved changes flag
    setHasUnsavedChanges(true);

    // Reset add form fields
    setNewSupportAssetName('');
    setNewSupportAssetDescription('');
    setNewSupportAssetType(ASSET_TYPES[0].value);
    setNewSupportAssetOwner('');
    setNewSupportAssetLocation('');
    // Reset CPE fields
    setNewSupportAssetVendor('');
    setNewSupportAssetProduct('');
    setNewSupportAssetVersion('');
    setDiscoveredCPE('');
    setCpeConfidence('');
  };

  const handleRemoveSupportAsset = (businessValueId, assetId) => {
    setBusinessValues(businessValues.map(value =>
      value.id !== businessValueId ? value : {
        ...value,
        supportAssets: (value.supportAssets || []).filter(asset => asset.id !== assetId)
      }
    ));
    // If the removed asset was being edited, cancel edit mode
    if (editingAsset && editingAsset.asset.id === assetId) {
        setEditingAsset(null);
    }
  };

  // --- Handlers for Editing Support Assets ---
  const handleStartEditSupportAsset = (businessValueId, asset) => {
      setEditingAsset({ businessValueId, asset: { ...asset } }); // Store BV ID and a copy of the asset
  };

  const handleCancelEditSupportAsset = () => {
      setEditingAsset(null);
  };

  const handleEditSupportAssetChange = (field, value) => {
      if (!editingAsset) return;
      setEditingAsset(prev => ({
          ...prev,
          asset: { ...prev.asset, [field]: value }
      }));
  };

  const handleEditSupportAssetSave = () => {
      if (!editingAsset) return;
      const { businessValueId, asset: editedAsset } = editingAsset;

      // Basic validation
      if (!editedAsset.name || !editedAsset.name.trim()) {
          alert(t('businessValues.status.emptyNameError'));
          return;
      }

      setBusinessValues(currentBusinessValues =>
          currentBusinessValues.map(value => {
              if (value.id !== businessValueId) return value;
              return {
                  ...value,
                  supportAssets: (value.supportAssets || []).map(asset =>
                      asset.id === editedAsset.id ? editedAsset : asset
                  )
              };
          })
      );
      setEditingAsset(null); // Exit edit mode
  };

  // Security pillar management
  const handleToggleSecurityPillar = (businessValueId, pillarId) => {
    setBusinessValues(businessValues.map(value => {
      if (value.id !== businessValueId) return value;
      const securityPillars = value.securityPillars || [];
      const isPillarSelected = securityPillars.includes(pillarId);
      return {
        ...value,
        securityPillars: isPillarSelected
          ? securityPillars.filter(id => id !== pillarId)
          : [...securityPillars, pillarId]
      };
    }));
  };

  // Editing business values
  const handleStartEdit = (value) => {
    setEditingValue(value.id);
    setEditValue(value.name);
    setEditDescription(value.description || '');
  };

  const handleEditSave = (id) => {
    if (editValue.trim()) {
      setBusinessValues(businessValues.map(value =>
        value.id === id ? { ...value, name: editValue, description: editDescription } : value
      ));
      setEditingValue(null);
      setEditValue('');
      setEditDescription('');
    }
  };

  const handleCancelEdit = () => {
    setEditingValue(null);
    setEditValue('');
    setEditDescription('');
  };

  // Show loading state
  if (isWorkshopDataLoading) {
    return (
      <div className="bg-gray-50 p-6 rounded-lg flex justify-center items-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p className="text-gray-600">{t('businessValues.status.saving')}</p>
        </div>
      </div>
    );
  }

  // Show error state
  if (workshopDataError?.businessValues) {
    return (
      <div className="bg-gray-50 p-6 rounded-lg">
        <div className="bg-red-50 text-red-700 p-4 rounded-lg mb-4">
          <h3 className="font-bold mb-2">Error Loading Data</h3>
          <p>{workshopDataError.businessValues}</p>
          <button
            className="mt-2 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
            onClick={() => window.location.reload()}
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
      <BusinessValuesHeader
        viewMode={viewMode}
        setViewMode={setViewMode}
        handleSaveData={handleSaveData}
        onAddClick={() => setIsAddModalOpen(true)} // Pass function to open modal
        onToggleHelp={() => setIsHelpVisible(!isHelpVisible)} // Pass function to toggle help
        isHelpVisible={isHelpVisible} // Pass help visibility state
        onOpenGuide={() => setIsGuideOpen(true)} // Pass function to open guide modal
      />

      {/* Modal for adding business values */}
      <Modal isOpen={isAddModalOpen} onClose={() => setIsAddModalOpen(false)}>
        <div className="p-4 bg-blue-50 rounded-lg">
          <h3 className="text-lg font-medium text-blue-800 mb-3">
            {t('businessValues.modal.addTitle')}
          </h3>
          <BusinessValuesAdd
            newBusinessValue={newBusinessValue}
            setNewBusinessValue={setNewBusinessValue}
            newBusinessValueDescription={newBusinessValueDescription}
            setNewBusinessValueDescription={setNewBusinessValueDescription}
            handleAddBusinessValue={handleAddBusinessValue}
            onClose={() => setIsAddModalOpen(false)} // Pass close handler
          />
        </div>
      </Modal>

      {/* Contenu principal avec carte et ombre */}
      <motion.div
        className="mt-6 border rounded-lg shadow-sm overflow-hidden"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
      >
        <AnimatePresence mode="wait">
          {viewMode === 'list' ? (
          <BusinessValueListView
            businessValues={businessValues}
            expandedValue={expandedValue}
            setExpandedValue={setExpandedValue}
            editingValue={editingValue}
            setEditingValue={setEditingValue}
            editValue={editValue}
            setEditValue={setEditValue}
            editDescription={editDescription}
            setEditDescription={setEditDescription}
            handleStartEdit={handleStartEdit}
            handleEditSave={handleEditSave}
            handleCancelEdit={handleCancelEdit}
            handleRemoveBusinessValue={handleRemoveBusinessValue}
            handleToggleSecurityPillar={handleToggleSecurityPillar}
            handleAddSupportAsset={handleAddSupportAsset}
            newSupportAssetName={newSupportAssetName}
            setNewSupportAssetName={setNewSupportAssetName}
            newSupportAssetDescription={newSupportAssetDescription}
            setNewSupportAssetDescription={setNewSupportAssetDescription}
            newSupportAssetType={newSupportAssetType}
            setNewSupportAssetType={setNewSupportAssetType}
            newSupportAssetOwner={newSupportAssetOwner}
            setNewSupportAssetOwner={setNewSupportAssetOwner}
            newSupportAssetLocation={newSupportAssetLocation}
            setNewSupportAssetLocation={setNewSupportAssetLocation}
            // CPE fields
            newSupportAssetVendor={newSupportAssetVendor}
            setNewSupportAssetVendor={setNewSupportAssetVendor}
            newSupportAssetProduct={newSupportAssetProduct}
            setNewSupportAssetProduct={setNewSupportAssetProduct}
            newSupportAssetVersion={newSupportAssetVersion}
            setNewSupportAssetVersion={setNewSupportAssetVersion}
            discoveredCPE={discoveredCPE}
            cpeConfidence={cpeConfidence}
            cpeSearching={cpeSearching}
            assetTypes={ASSET_TYPES}
            handleRemoveSupportAsset={handleRemoveSupportAsset}
            editingAsset={editingAsset}
            handleStartEditSupportAsset={handleStartEditSupportAsset}
            handleCancelEditSupportAsset={handleCancelEditSupportAsset}
            handleEditSupportAssetChange={handleEditSupportAssetChange}
            handleEditSupportAssetSave={handleEditSupportAssetSave}
          />
        ) : (
          <BusinessValueChartView
            businessValues={businessValues}
            selectedValueForChart={selectedValueForChart}
            setSelectedValueForChart={setSelectedValueForChart}
            useShortIds={useShortIds}
            setUseShortIds={setUseShortIds}
            handleToggleSecurityPillar={handleToggleSecurityPillar}
            handleRemoveSupportAsset={handleRemoveSupportAsset}
            setExpandedValue={setExpandedValue}
            setViewMode={setViewMode}
            zoomLevel={zoomLevel}
            setZoomLevel={setZoomLevel}
            viewportPosition={viewportPosition}
            setViewportPosition={setViewportPosition}
            isDragging={isDragging}
            setIsDragging={setIsDragging}
            dragStart={dragStart}
            setDragStart={setDragStart}
            chartContainerRef={chartContainerRef}
          />
        )}
        </AnimatePresence>
      </motion.div>

      {/* Collapsible Section d'aide */}
      <AnimatePresence>
        {isHelpVisible && (
          <motion.div
            className="mt-6 p-4 bg-gray-50 rounded-lg border border-gray-200"
            initial={{ opacity: 0, height: 0, marginTop: 0 }}
            animate={{ opacity: 1, height: 'auto', marginTop: 24 }}
            exit={{ opacity: 0, height: 0, marginTop: 0 }}
            transition={{ duration: 0.3 }}
          >
            <motion.h3
              className="flex items-center text-gray-700 font-medium"
              initial={{ y: -10 }}
              animate={{ y: 0 }}
              transition={{ delay: 0.1 }}
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z" clipRule="evenodd" />
              </svg>
              {t('businessValues.help.title')}
            </motion.h3>
            <motion.p
              className="text-sm text-gray-600 mt-2"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.2 }}
            >
              {t('businessValues.help.description')}
            </motion.p>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Guide Modal */}
      <GuideModal isOpen={isGuideOpen} onClose={() => setIsGuideOpen(false)} />
    </div>
  );
};

export default BusinessValues;