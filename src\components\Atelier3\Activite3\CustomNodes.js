// src/components/Atelier3/Activite3/CustomNodes.js
import React, { memo } from 'react';
import { Handle, Position } from 'reactflow';
import { AlertTriangle, Shield, Target, Database, Box, Briefcase } from 'lucide-react';

// Source Risk Node
export const SourceRiskNode = memo(({ data, isConnectable }) => {
  return (
    <div className="relative p-4 border border-red-500 bg-red-50 rounded-lg shadow-md w-64">

      <div className="flex items-start mb-2">
        <Target className="text-red-600 mr-2 flex-shrink-0" size={20} />
        <div className="font-bold text-red-800">Source de Risque</div>
      </div>

      <div className="text-sm text-red-900 font-medium">{data.label}</div>

      {data.description && (
        <div className="mt-2 text-xs text-red-700">{data.description}</div>
      )}

      <Handle
        type="source"
        position={Position.Right}
        id="source-handle"
        style={{
          background: '#ef4444',
          width: '12px',
          height: '12px',
          border: '2px solid white',
          boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
        }}
        isConnectable={isConnectable}
      />
    </div>
  );
});

// Dreaded Event Node
export const DreadedEventNode = memo(({ data, isConnectable }) => {
  return (
    <div className="relative p-4 border border-yellow-500 bg-yellow-50 rounded-lg shadow-md w-64">
      <div className="absolute top-2 right-2">
        {data.severity && (
          <div className={`
            px-2 py-1 rounded-full text-xs font-bold
            ${data.severity === 'critical' ? 'bg-red-100 text-red-800' :
              data.severity === 'major' ? 'bg-orange-100 text-orange-800' :
              data.severity === 'moderate' ? 'bg-yellow-100 text-yellow-800' :
              'bg-green-100 text-green-800'}
          `}>
            {data.severity === 'critical' ? 'Critique' :
             data.severity === 'major' ? 'Majeur' :
             data.severity === 'moderate' ? 'Modéré' :
             'Mineur'}
          </div>
        )}
      </div>

      <div className="flex items-start mb-2">
        <AlertTriangle className="text-yellow-600 mr-2 flex-shrink-0" size={20} />
        <div className="font-bold text-yellow-800">Événement Redouté</div>
      </div>

      <div className="text-sm text-yellow-900 font-medium">{data.label}</div>

      {data.description && (
        <div className="mt-2 text-xs text-yellow-700">{data.description}</div>
      )}

      <Handle
        type="target"
        position={Position.Left}
        id="target-handle"
        style={{
          background: '#eab308',
          width: '12px',
          height: '12px',
          border: '2px solid white',
          boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
        }}
        isConnectable={isConnectable}
      />

      <Handle
        type="source"
        position={Position.Right}
        id="source-handle"
        style={{
          background: '#eab308',
          width: '12px',
          height: '12px',
          border: '2px solid white',
          boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
        }}
        isConnectable={isConnectable}
      />
    </div>
  );
});

// Business Value Node (used for Objectif Visé)
export const BusinessValueNode = memo(({ data, isConnectable }) => {
  return (
    <div className="relative p-4 border border-green-500 bg-green-50 rounded-lg shadow-md w-80">
      <div className="flex items-start mb-2">
        <Database className="text-green-600 mr-2 flex-shrink-0" size={20} />
        <div className="font-bold text-green-800">Objectif Visé</div>
      </div>

      <div className="text-sm text-green-900 font-medium">{data.label}</div>

      {data.description && (
        <div className="mt-2 text-xs text-green-700">{data.description}</div>
      )}

      {/* Additional information */}
      {data.dreadedEvent && (
        <div className="mt-3 p-2 bg-yellow-50 border border-yellow-200 rounded-md">
          <div className="flex items-start">
            <AlertTriangle className="text-yellow-600 mr-2 flex-shrink-0" size={16} />
            <div>
              <div className="text-xs font-semibold text-yellow-800">Événement Redouté</div>
              <div className="text-xs text-yellow-700">{data.dreadedEvent}</div>
            </div>
          </div>
        </div>
      )}

      {data.businessValue && (
        <div className="mt-2 p-2 bg-blue-50 border border-blue-200 rounded-md">
          <div className="flex items-start">
            <Database className="text-blue-600 mr-2 flex-shrink-0" size={16} />
            <div>
              <div className="text-xs font-semibold text-blue-800">Valeur Métier</div>
              <div className="text-xs text-blue-700">{data.businessValue}</div>
            </div>
          </div>
        </div>
      )}

      <Handle
        type="target"
        position={Position.Left}
        id="target-handle"
        style={{
          background: '#22c55e',
          width: '12px',
          height: '12px',
          border: '2px solid white',
          boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
        }}
        isConnectable={isConnectable}
      />
    </div>
  );
});

// Business Value Node (separate from Objectif Visé)
export const BusinessValueStandaloneNode = memo(({ data, isConnectable }) => {
  return (
    <div className="relative p-4 border border-purple-500 bg-purple-50 rounded-lg shadow-md w-64">
      <div className="flex items-start mb-2">
        <Briefcase className="text-purple-600 mr-2 flex-shrink-0" size={20} />
        <div className="font-bold text-purple-800">Valeur Métier</div>
      </div>

      <div className="text-sm text-purple-900 font-medium">{data.label}</div>

      {data.description && (
        <div className="mt-2 text-xs text-purple-700">{data.description}</div>
      )}

      {data.businessValueType && (
        <div className="mt-2 p-2 bg-purple-100 border border-purple-200 rounded-md">
          <div className="text-xs font-semibold text-purple-800">Type</div>
          <div className="text-xs text-purple-700">{data.businessValueType}</div>
        </div>
      )}

      {data.criticality && (
        <div className="mt-2">
          <div className={`
            px-2 py-1 rounded-full text-xs font-bold inline-block
            ${data.criticality === 'critique' ? 'bg-red-100 text-red-800' :
              data.criticality === 'importante' ? 'bg-orange-100 text-orange-800' :
              data.criticality === 'standard' ? 'bg-yellow-100 text-yellow-800' :
              'bg-green-100 text-green-800'}
          `}>
            {data.criticality === 'critique' ? 'Critique' :
             data.criticality === 'importante' ? 'Importante' :
             data.criticality === 'standard' ? 'Standard' :
             'Modérée'}
          </div>
        </div>
      )}

      <Handle
        type="target"
        position={Position.Left}
        id="target-handle"
        style={{
          background: '#a855f7',
          width: '12px',
          height: '12px',
          border: '2px solid white',
          boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
        }}
        isConnectable={isConnectable}
      />

      <Handle
        type="source"
        position={Position.Right}
        id="source-handle"
        style={{
          background: '#a855f7',
          width: '12px',
          height: '12px',
          border: '2px solid white',
          boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
        }}
        isConnectable={isConnectable}
      />
    </div>
  );
});

// Stakeholder Node
export const StakeholderNode = memo(({ data, isConnectable }) => {
  return (
    <div className="relative p-4 border border-blue-500 bg-blue-50 rounded-lg shadow-md w-64">
      <div className="flex items-start mb-2">
        <Shield className="text-blue-600 mr-2 flex-shrink-0" size={20} />
        <div className="font-bold text-blue-800">Partie Prenante</div>
      </div>

      <div className="text-sm text-blue-900 font-medium">{data.label}</div>

      {data.description && (
        <div className="mt-2 text-xs text-blue-700">{data.description}</div>
      )}

      <Handle
        type="target"
        position={Position.Left}
        id="target-handle"
        style={{
          background: '#3b82f6',
          width: '12px',
          height: '12px',
          border: '2px solid white',
          boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
        }}
        isConnectable={isConnectable}
      />

      <Handle
        type="source"
        position={Position.Right}
        id="source-handle"
        style={{
          background: '#3b82f6',
          width: '12px',
          height: '12px',
          border: '2px solid white',
          boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
        }}
        isConnectable={isConnectable}
      />
    </div>
  );
});

// Custom Node with configurable inputs and outputs
export const CustomNode = memo(({ data, isConnectable }) => {
  // Generate handles based on inputs and outputs count
  const inputHandles = [];
  const outputHandles = [];

  // Create input handles with better spacing
  for (let i = 0; i < (data.inputs || 1); i++) {
    const totalInputs = data.inputs || 1;
    // Better spacing calculation to avoid overlap
    const spacing = totalInputs === 1 ? 50 : 20 + (i * (60 / Math.max(1, totalInputs - 1)));
    inputHandles.push(
      <Handle
        key={`input-${i}`}
        type="target"
        position={Position.Left}
        id={`input-${i}`}
        style={{
          background: data.color || '#6366f1',
          width: '12px',
          height: '12px',
          top: `${spacing}%`,
          border: '2px solid white',
          boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
        }}
        isConnectable={isConnectable}
      />
    );
  }

  // Create output handles with better spacing
  for (let i = 0; i < (data.outputs || 1); i++) {
    const totalOutputs = data.outputs || 1;
    // Better spacing calculation to avoid overlap
    const spacing = totalOutputs === 1 ? 50 : 20 + (i * (60 / Math.max(1, totalOutputs - 1)));
    outputHandles.push(
      <Handle
        key={`output-${i}`}
        type="source"
        position={Position.Right}
        id={`output-${i}`}
        style={{
          background: data.color || '#6366f1',
          width: '12px',
          height: '12px',
          top: `${spacing}%`,
          border: '2px solid white',
          boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
        }}
        isConnectable={isConnectable}
      />
    );
  }

  // Calculate background and border colors
  const bgColor = data.color ? `${data.color}15` : '#f8fafc'; // Very light neutral background
  const borderColor = data.color || '#6366f1'; // Default indigo instead of blue

  return (
    <div
      className="relative p-4 rounded-lg shadow-md w-64"
      style={{
        background: bgColor,
        border: `1px solid ${borderColor}`,
      }}
    >
      <div className="flex items-start mb-2">
        <Box className="mr-2 flex-shrink-0" size={20} style={{ color: borderColor }} />
        <div className="font-bold text-lg" style={{ color: borderColor }}>{data.label || 'Nœud personnalisé'}</div>
      </div>

      <div className="text-xs opacity-75" style={{ color: borderColor }}>
        {data.nodeType === 'custom' ? 'Personnalisé' : (data.nodeType || 'Personnalisé')}
      </div>

      {data.description && (
        <div className="mt-2 text-xs" style={{ color: borderColor }}>{data.description}</div>
      )}

      {/* Render all input and output handles */}
      {inputHandles}
      {outputHandles}
    </div>
  );
});

// Node type mapping
export const nodeTypes = {
  sourceRisk: SourceRiskNode,
  dreadedEvent: DreadedEventNode,
  businessValue: BusinessValueNode,
  businessValueStandalone: BusinessValueStandaloneNode,
  stakeholder: StakeholderNode,
  custom: CustomNode,
};
