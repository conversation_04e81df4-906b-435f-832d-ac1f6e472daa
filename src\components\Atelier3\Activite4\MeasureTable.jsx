// src/components/Atelier3/Activite4/MeasureTable.jsx
import React, { useState, useEffect } from 'react';
import { Edit2, Trash2, ChevronDown, ChevronUp, Eye, Trash } from 'lucide-react';
import api from '../../../api/apiClient';

const CATEGORIES = {
  governance: { label: 'GOUVERNANCE', color: 'bg-blue-100 text-blue-800 border-blue-200' },
  protection: { label: 'PROTECTION', color: 'bg-green-100 text-green-800 border-green-200' },
  defense: { label: 'DÉFENSE', color: 'bg-purple-100 text-purple-800 border-purple-200' },
  resilience: { label: 'RÉSILIENCE', color: 'bg-yellow-100 text-yellow-800 border-yellow-200' },
  other: { label: 'AUTRE', color: 'bg-gray-100 text-gray-800 border-gray-200' }
};

const PRIORITIES = {
  high: { label: 'Haute', color: 'bg-red-100 text-red-800 border-red-200' },
  medium: { label: 'Moyenne', color: 'bg-yellow-100 text-yellow-800 border-yellow-200' },
  low: { label: 'Basse', color: 'bg-green-100 text-green-800 border-green-200' }
};

const IMPLEMENTATION_STATUSES = {
  planned: { label: 'Planifiée', color: 'bg-blue-100 text-blue-800 border-blue-200' },
  'in-progress': { label: 'En cours', color: 'bg-yellow-100 text-yellow-800 border-yellow-200' },
  implemented: { label: 'Implémentée', color: 'bg-green-100 text-green-800 border-green-200' },
  abandoned: { label: 'Abandonnée', color: 'bg-red-100 text-red-800 border-red-200' }
};

const MeasureTable = ({ measures, onEdit, onDelete, onBulkDelete, isLoading }) => {
  const [expandedMeasure, setExpandedMeasure] = useState(null);
  const [sortField, setSortField] = useState('title');
  const [sortDirection, setSortDirection] = useState('asc');
  const [sourceRisks, setSourceRisks] = useState({});
  const [objectifsVises, setObjectifsVises] = useState({});
  const [attackPathsData, setAttackPathsData] = useState([]);
  const [dataLoading, setDataLoading] = useState(false);
  const [selectedMeasures, setSelectedMeasures] = useState(new Set());
  const [selectAll, setSelectAll] = useState(false);

  const toggleExpand = (measureId) => {
    if (expandedMeasure === measureId) {
      setExpandedMeasure(null);
    } else {
      setExpandedMeasure(measureId);
    }
  };

  // Handle individual measure selection
  const handleSelectMeasure = (measureId) => {
    const newSelected = new Set(selectedMeasures);
    if (newSelected.has(measureId)) {
      newSelected.delete(measureId);
    } else {
      newSelected.add(measureId);
    }
    setSelectedMeasures(newSelected);
    setSelectAll(newSelected.size === measures.length);
  };

  // Handle select all toggle
  const handleSelectAll = () => {
    if (selectAll) {
      setSelectedMeasures(new Set());
      setSelectAll(false);
    } else {
      setSelectedMeasures(new Set(measures.map(m => m.id)));
      setSelectAll(true);
    }
  };

  // Handle bulk delete
  const handleBulkDelete = () => {
    if (selectedMeasures.size === 0) return;

    const selectedCount = selectedMeasures.size;
    const confirmMessage = `Êtes-vous sûr de vouloir supprimer ${selectedCount} mesure${selectedCount > 1 ? 's' : ''} sélectionnée${selectedCount > 1 ? 's' : ''} ?`;

    if (window.confirm(confirmMessage)) {
      onBulkDelete(Array.from(selectedMeasures));
      setSelectedMeasures(new Set());
      setSelectAll(false);
    }
  };

  // Reset selection when measures change
  useEffect(() => {
    setSelectedMeasures(new Set());
    setSelectAll(false);
  }, [measures]);

  const handleSort = (field) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  // Helper function to get attack paths associated with a specific measure
  const getAssociatedAttackPaths = (measure) => {

    // Method 1: Use attackPathsDetails directly from the measure (new AI-generated measures)
    if (measure.attackPathsDetails && measure.attackPathsDetails.length > 0) {
      return measure.attackPathsDetails;
    }

    // Method 2: Direct attackPaths array with lookup (manual measures and older AI measures)
    if (measure.attackPaths && measure.attackPaths.length > 0 && attackPathsData.length > 0) {
      return attackPathsData.filter(path =>
        measure.attackPaths.includes(path.id)
      );
    }

    // Method 3: Try to match using sourceRisks and objectifsVises (AI measures fallback)
    if (attackPathsData.length > 0 &&
        ((measure.sourceRisks && measure.sourceRisks.length > 0) ||
         (measure.objectifsVises && measure.objectifsVises.length > 0))) {



      const matchedPaths = attackPathsData.filter(path => {
        // Check if any sourceRisk matches the path's sourceRiskName
        const sourceMatch = measure.sourceRisks && measure.sourceRisks.some(source => {
          if (!path.sourceRiskName) return false;
          const sourceLower = source.toLowerCase();
          const pathSourceLower = path.sourceRiskName.toLowerCase();

          // Strict matching - require significant overlap
          return sourceLower === pathSourceLower || // Exact match
                 (sourceLower.length > 10 && pathSourceLower.includes(sourceLower)) || // Long source contained in path
                 (pathSourceLower.length > 10 && sourceLower.includes(pathSourceLower)) || // Long path contained in source
                 // Require at least 2 meaningful words in common (5+ chars)
                 sourceLower.split(' ').filter(word => word.length >= 5 && pathSourceLower.includes(word)).length >= 2;
        });

        // Check if any objectifVise matches the path's objectifVise
        const objectifMatch = measure.objectifsVises && measure.objectifsVises.some(objectif => {
          if (!path.objectifVise) return false;
          const objectifLower = objectif.toLowerCase();
          const pathObjectifLower = path.objectifVise.toLowerCase();

          // More flexible matching for objectifs
          const match = objectifLower === pathObjectifLower || // Exact match
                       (objectifLower.length > 10 && pathObjectifLower.includes(objectifLower)) || // Long objectif contained in path
                       (pathObjectifLower.length > 10 && objectifLower.includes(pathObjectifLower)) || // Long path contained in objectif
                       // Look for key phrases like "vol de données", "accès", etc.
                       (objectifLower.includes('vol') && objectifLower.includes('données') &&
                        pathObjectifLower.includes('vol') && pathObjectifLower.includes('données')) ||
                       (objectifLower.includes('accès') && pathObjectifLower.includes('accès')) ||
                       (objectifLower.includes('perturbation') && pathObjectifLower.includes('dégradation')) ||
                       // Require at least 1 meaningful word in common (4+ chars) for objectifs
                       objectifLower.split(' ').filter(word => word.length >= 4 && pathObjectifLower.includes(word)).length >= 1;


          return match;
        });

        return sourceMatch || objectifMatch;
      });



      return matchedPaths;
    }

    // Method 4: No associations found
    return [];
  };

  const getSortIcon = (field) => {
    if (sortField !== field) return null;
    return sortDirection === 'asc' ? <ChevronUp size={16} /> : <ChevronDown size={16} />;
  };

  // Load attack paths data when component mounts
  useEffect(() => {
    const loadAttackPathsData = async () => {
      setDataLoading(true);
      try {
        const analysisId = localStorage.getItem('currentAnalysisId');
        if (analysisId) {
          const response = await api.get(`/analyses/${analysisId}/attack-paths`);

          if (response.success && response.data) {
            let attackPaths = [];
            if (response.data.data && response.data.data.attackPaths) {
              attackPaths = response.data.data.attackPaths;
            } else if (response.data.attackPaths) {
              attackPaths = response.data.attackPaths;
            } else if (Array.isArray(response.data)) {
              attackPaths = response.data;
            }

            setAttackPathsData(attackPaths);

            // Créer un dictionnaire des sources de risque avec leurs objectifs visés
            const newSourceRisks = {};
            attackPaths.forEach(path => {
              if (path.sourceRiskId && path.sourceRiskName) {
                newSourceRisks[path.sourceRiskId] = {
                  name: path.sourceRiskName,
                  description: path.sourceRiskDescription || path.sourceRiskName,
                  objectifVise: path.objectifVise || '',
                  referenceCode: path.referenceCode || `CA${String(attackPaths.indexOf(path) + 1).padStart(2, '0')}`,
                  stakeholders: path.stakeholders || []
                };
              }
            });
            setSourceRisks(newSourceRisks);

            // Créer un dictionnaire des objectifs visés
            const newObjectifsVises = {};
            attackPaths.forEach(path => {
              if (path.objectifVise) {
                newObjectifsVises[path.objectifVise] = {
                  name: path.objectifVise,
                  description: path.objectifVise,
                  type: ''
                };
              }
            });
            setObjectifsVises(newObjectifsVises);
          }
        }
      } catch (error) {
        console.error('Error loading attack paths data:', error);
      } finally {
        setDataLoading(false);
      }
    };

    loadAttackPathsData();
  }, []);

  // Sort measures
  const sortedMeasures = [...measures].sort((a, b) => {
    let aValue = a[sortField];
    let bValue = b[sortField];

    // Handle special cases
    if (sortField === 'category') {
      aValue = CATEGORIES[a.category]?.label || '';
      bValue = CATEGORIES[b.category]?.label || '';
    } else if (sortField === 'priority') {
      const priorityOrder = { high: 3, medium: 2, low: 1 };
      aValue = priorityOrder[a.priority] || 0;
      bValue = priorityOrder[b.priority] || 0;
    } else if (sortField === 'implementation') {
      const statusOrder = { implemented: 4, 'in-progress': 3, planned: 2, abandoned: 1 };
      aValue = statusOrder[a.implementation] || 0;
      bValue = statusOrder[b.implementation] || 0;
    }

    // Compare values
    if (aValue < bValue) return sortDirection === 'asc' ? -1 : 1;
    if (aValue > bValue) return sortDirection === 'asc' ? 1 : -1;
    return 0;
  });

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-40">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
        <span className="ml-2 text-gray-600">Chargement des mesures...</span>
      </div>
    );
  }

  if (measures.length === 0) {
    return (
      <div className="bg-white border border-gray-200 rounded-lg p-8 text-center">
        <p className="text-gray-500">
          Aucune mesure de sécurité n'a encore été définie pour l'écosystème.
        </p>
        <p className="text-gray-500 mt-2">
          Utilisez les boutons ci-dessus pour ajouter des mesures manuellement ou générer des suggestions avec l'IA.
        </p>
      </div>
    );
  }

  return (
    <div className="bg-white border border-gray-200 rounded-xl shadow-sm overflow-hidden">
      {/* Header with stats */}
      <div className="bg-gradient-to-r from-blue-50 to-indigo-50 px-6 py-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div>
            <div className="flex items-center space-x-4">
              <div>
                <h3 className="text-lg font-semibold text-gray-900">Mesures de sécurité</h3>
                <p className="text-sm text-gray-600 mt-1">
                  {measures.length} mesure{measures.length > 1 ? 's' : ''} définie{measures.length > 1 ? 's' : ''}
                  {selectedMeasures.size > 0 && (
                    <span className="ml-2 text-blue-600 font-medium">
                      ({selectedMeasures.size} sélectionnée{selectedMeasures.size > 1 ? 's' : ''})
                    </span>
                  )}
                </p>
              </div>

              {/* Bulk delete button */}
              {selectedMeasures.size > 0 && (
                <button
                  onClick={handleBulkDelete}
                  className="inline-flex items-center px-3 py-2 text-sm font-medium text-red-700 bg-red-100 border border-red-300 rounded-lg hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 transition-colors duration-150"
                >
                  <Trash size={16} className="mr-2" />
                  Supprimer ({selectedMeasures.size})
                </button>
              )}
            </div>
          </div>
          <div className="flex items-center space-x-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">
                {measures.filter(m => m.implementation === 'implemented').length}
              </div>
              <div className="text-xs text-gray-500">Implémentées</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-yellow-600">
                {measures.filter(m => m.implementation === 'in-progress').length}
              </div>
              <div className="text-xs text-gray-500">En cours</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">
                {measures.filter(m => m.implementation === 'planned').length}
              </div>
              <div className="text-xs text-gray-500">Planifiées</div>
            </div>
          </div>
        </div>
      </div>

      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-16">
                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    checked={selectAll}
                    onChange={handleSelectAll}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <span className="sr-only">Sélectionner tout</span>
                </div>
              </th>
              <th
                className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100 transition-colors"
                onClick={() => handleSort('title')}
              >
                <div className="flex items-center space-x-1">
                  <span>Titre</span>
                  {getSortIcon('title')}
                </div>
              </th>
              <th
                className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100 transition-colors"
                onClick={() => handleSort('category')}
              >
                <div className="flex items-center space-x-1">
                  <span>Catégorie</span>
                  {getSortIcon('category')}
                </div>
              </th>
              <th
                className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100 transition-colors"
                onClick={() => handleSort('priority')}
              >
                <div className="flex items-center space-x-1">
                  <span>Priorité</span>
                  {getSortIcon('priority')}
                </div>
              </th>
              <th
                className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100 transition-colors"
                onClick={() => handleSort('implementation')}
              >
                <div className="flex items-center space-x-1">
                  <span>Statut</span>
                  {getSortIcon('implementation')}
                </div>
              </th>
              <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Chemins d'attaque
              </th>
              <th className="px-6 py-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider w-24">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {sortedMeasures.map(measure => (
              <React.Fragment key={measure.id}>
                <tr className="hover:bg-gray-50 transition-colors duration-150">
                  <td className="px-6 py-5 whitespace-nowrap">
                    <div className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        checked={selectedMeasures.has(measure.id)}
                        onChange={() => handleSelectMeasure(measure.id)}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                      <button
                        onClick={() => toggleExpand(measure.id)}
                        className="text-gray-400 hover:text-blue-600 hover:bg-blue-50 p-1 rounded-md transition-colors duration-150"
                      >
                        {expandedMeasure === measure.id ? (
                          <ChevronUp size={18} />
                        ) : (
                          <ChevronDown size={18} />
                        )}
                      </button>
                    </div>
                  </td>
                  <td className="px-6 py-5">
                    <div className="flex flex-col">
                      <div className="text-sm font-semibold text-gray-900 leading-tight">{measure.title}</div>
                      {measure.description && (
                        <div className="text-xs text-gray-500 mt-1 line-clamp-2">
                          {measure.description.length > 100
                            ? measure.description.substring(0, 100) + '...'
                            : measure.description
                          }
                        </div>
                      )}
                    </div>
                  </td>
                  <td className="px-6 py-5 whitespace-nowrap">
                    <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium shadow-sm ${CATEGORIES[measure.category]?.color || CATEGORIES.other.color}`}>
                      {CATEGORIES[measure.category]?.label || measure.category}
                    </span>
                  </td>
                  <td className="px-6 py-5 whitespace-nowrap">
                    <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium shadow-sm ${PRIORITIES[measure.priority]?.color || 'bg-gray-100 text-gray-800'}`}>
                      {PRIORITIES[measure.priority]?.label || measure.priority}
                    </span>
                  </td>
                  <td className="px-6 py-5 whitespace-nowrap">
                    <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium shadow-sm ${IMPLEMENTATION_STATUSES[measure.implementation]?.color || 'bg-gray-100 text-gray-800'}`}>
                      {IMPLEMENTATION_STATUSES[measure.implementation]?.label || measure.implementation}
                    </span>
                  </td>
                  <td className="px-6 py-5">
                    <div className="flex flex-wrap gap-2">
                      {(() => {
                        const associatedPaths = getAssociatedAttackPaths(measure);

                        if (associatedPaths.length > 0) {
                          return associatedPaths.map((path) => (
                            <span
                              key={path.id}
                              className="inline-flex items-center px-2.5 py-1 rounded-md text-xs font-bold bg-blue-100 text-blue-800 border border-blue-200 shadow-sm"
                            >
                              {path.referenceCode}
                            </span>
                          ));
                        } else {
                          return (
                            <span className="text-xs text-gray-500 italic">
                              Aucun chemin associé
                            </span>
                          );
                        }
                      })()}
                    </div>
                  </td>
                  <td className="px-6 py-5 whitespace-nowrap text-right">
                    <div className="flex items-center justify-end space-x-2">
                      <button
                        onClick={() => onEdit(measure)}
                        className="inline-flex items-center p-2 text-blue-600 hover:text-blue-700 hover:bg-blue-50 rounded-md transition-colors duration-150"
                        title="Modifier"
                      >
                        <Edit2 size={16} />
                      </button>
                      <button
                        onClick={() => {
                          if (window.confirm('Êtes-vous sûr de vouloir supprimer cette mesure?')) {
                            onDelete(measure.id);
                          }
                        }}
                        className="inline-flex items-center p-2 text-red-600 hover:text-red-700 hover:bg-red-50 rounded-md transition-colors duration-150"
                        title="Supprimer"
                      >
                        <Trash2 size={16} />
                      </button>
                    </div>
                  </td>
                </tr>

                {/* Expanded details */}
                {expandedMeasure === measure.id && (
                  <tr>
                    <td colSpan={8} className="px-6 py-6 bg-gradient-to-r from-gray-50 to-blue-50 border-t border-gray-200">
                      <div className="max-w-6xl mx-auto">
                        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                          {/* Left Column */}
                          <div className="space-y-4">
                            <div className="bg-white rounded-lg p-4 shadow-sm border border-gray-200">
                              <h4 className="font-semibold text-gray-900 mb-2 flex items-center">
                                <div className="w-2 h-2 bg-blue-500 rounded-full mr-2"></div>
                                Description
                              </h4>
                              <p className="text-sm text-gray-700 whitespace-pre-line leading-relaxed">{measure.description}</p>
                            </div>

                            {measure.benefits && measure.benefits.length > 0 && (
                              <div className="bg-white rounded-lg p-4 shadow-sm border border-gray-200">
                                <h4 className="font-semibold text-gray-900 mb-2 flex items-center">
                                  <div className="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                                  Bénéfices
                                </h4>
                                <ul className="space-y-2">
                                  {measure.benefits.map((benefit, index) => (
                                    <li key={benefit.id || index} className="flex items-start">
                                      <div className="w-1.5 h-1.5 bg-green-400 rounded-full mt-2 mr-2 flex-shrink-0"></div>
                                      <span className="text-sm text-gray-700">{benefit.description}</span>
                                    </li>
                                  ))}
                                </ul>
                              </div>
                            )}

                            <div className="bg-white rounded-lg p-4 shadow-sm border border-gray-200">
                              <h4 className="font-semibold text-gray-900 mb-3 flex items-center">
                                <div className="w-2 h-2 bg-purple-500 rounded-full mr-2"></div>
                                Détails de mise en œuvre
                              </h4>
                              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                {measure.responsibleParty && (
                                  <div className="bg-gray-50 rounded-lg p-3">
                                    <h5 className="text-xs font-medium text-gray-500 uppercase tracking-wide mb-1">Responsable</h5>
                                    <p className="text-sm text-gray-900">{measure.responsibleParty}</p>
                                  </div>
                                )}

                                {measure.deadline && (
                                  <div className="bg-gray-50 rounded-lg p-3">
                                    <h5 className="text-xs font-medium text-gray-500 uppercase tracking-wide mb-1">Échéance</h5>
                                    <p className="text-sm text-gray-900">{new Date(measure.deadline).toLocaleDateString()}</p>
                                  </div>
                                )}

                                {measure.effectiveness && (
                                  <div className="bg-gray-50 rounded-lg p-3">
                                    <h5 className="text-xs font-medium text-gray-500 uppercase tracking-wide mb-1">Efficacité</h5>
                                    <div className="flex items-center">
                                      {[...Array(5)].map((_, i) => (
                                        <div
                                          key={i}
                                          className={`w-3 h-3 rounded-full mr-1 ${
                                            i < measure.effectiveness ? 'bg-blue-500' : 'bg-gray-300'
                                          }`}
                                        />
                                      ))}
                                      <span className="ml-2 text-sm text-gray-600">({measure.effectiveness}/5)</span>
                                    </div>
                                  </div>
                                )}
                              </div>
                            </div>
                          </div>

                          {/* Right Column */}
                          <div className="space-y-4">
                            {/* Chemins d'attaque traités */}
                            {(() => {
                              const associatedPaths = getAssociatedAttackPaths(measure);

                              if (associatedPaths.length > 0) {
                                return (
                                  <div className="bg-white rounded-lg p-4 shadow-sm border border-gray-200">
                                    <h4 className="font-semibold text-gray-900 mb-3 flex items-center">
                                      <div className="w-2 h-2 bg-red-500 rounded-full mr-2"></div>
                                      Chemins d'attaque traités
                                    </h4>
                                    <div className="space-y-3">
                                      {associatedPaths.map((path) => (
                                        <div key={path.id} className="bg-gradient-to-r from-gray-50 to-blue-50 rounded-lg p-4 border border-gray-200">
                                          <div className="flex items-center mb-3">
                                            <span className="inline-flex items-center px-3 py-1.5 rounded-md text-sm font-bold bg-blue-100 text-blue-800 border border-blue-200 shadow-sm">
                                              {path.referenceCode}
                                            </span>
                                          </div>

                                          <div className="space-y-3">
                                            {/* Source de risque */}
                                            <div className="bg-white rounded-lg p-3 border border-red-200">
                                              <div className="text-xs font-medium text-red-600 mb-1">Source de risque</div>
                                              <div className="text-sm font-medium text-gray-900">{path.sourceRiskName}</div>
                                            </div>

                                            {/* Parties prenantes */}
                                            {path.stakeholders && path.stakeholders.length > 0 && (
                                              <div className="bg-white rounded-lg p-3 border border-yellow-200">
                                                <div className="text-xs font-medium text-yellow-600 mb-2">Parties prenantes</div>
                                                <div className="flex flex-wrap gap-2">
                                                  {path.stakeholders.map((stakeholder, idx) => (
                                                    <span key={idx} className="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-yellow-100 text-yellow-800 border border-yellow-200">
                                                      {stakeholder.name}
                                                    </span>
                                                  ))}
                                                </div>
                                              </div>
                                            )}

                                            {/* Objectif visé */}
                                            <div className="bg-white rounded-lg p-3 border border-green-200">
                                              <div className="text-xs font-medium text-green-600 mb-1">Objectif visé</div>
                                              <div className="text-sm font-medium text-gray-900">{path.objectifVise}</div>
                                            </div>
                                          </div>
                                        </div>
                                      ))}
                                    </div>
                                    {dataLoading && measure.id === expandedMeasure && (
                                      <p className="text-xs text-gray-500 mt-2 text-center">Chargement des données...</p>
                                    )}
                                  </div>
                                );
                              }
                              return null;
                            })()}

                            {measure.notes && (
                              <div className="bg-white rounded-lg p-4 shadow-sm border border-gray-200">
                                <h4 className="font-semibold text-gray-900 mb-2 flex items-center">
                                  <div className="w-2 h-2 bg-gray-500 rounded-full mr-2"></div>
                                  Notes
                                </h4>
                                <p className="text-sm text-gray-700 whitespace-pre-line leading-relaxed">{measure.notes}</p>
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    </td>
                  </tr>
                )}
              </React.Fragment>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default MeasureTable;
