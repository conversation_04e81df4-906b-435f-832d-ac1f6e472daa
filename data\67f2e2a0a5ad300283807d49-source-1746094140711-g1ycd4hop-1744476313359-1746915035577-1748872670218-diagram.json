{"nodes": [{"id": "source-risk", "type": "sourceRisk", "position": {"x": 16, "y": -44}, "draggable": true, "selectable": true, "data": {"label": "Concurrent malveillant", "description": "Source de risque identifiée dans l'analyse", "riskLevel": "medium", "sequence": 1}, "width": 256, "height": 110, "selected": false, "positionAbsolute": {"x": 16, "y": -44}, "dragging": false}, {"id": "objectif-vise", "type": "businessValue", "position": {"x": 837.5005486853206, "y": -20.27226008774346}, "data": {"label": "Vol de données clients ou dégradation de la réputation", "description": "Objectif visé par la source de risque", "dreadedEvent": "Attaque par déni de service", "businessValue": "Platform Availability & Performance", "sequence": 3}, "width": 320, "height": 249, "selected": false, "positionAbsolute": {"x": 837.5005486853206, "y": -20.27226008774346}, "dragging": false}, {"id": "stakeholder-1746915035577", "type": "stakeholder", "position": {"x": 588.0113948570556, "y": 492.23799928256534}, "data": {"label": "PP3", "description": "Partie prenante concernée par l'événement redouté", "sequence": 2}, "width": 256, "height": 126, "selected": false, "positionAbsolute": {"x": 588.0113948570556, "y": 492.23799928256534}, "dragging": false}, {"id": "node-4", "type": "custom", "position": {"x": -90.62204299231274, "y": 237.68131466705677}, "width": 256, "height": 110, "selected": true, "positionAbsolute": {"x": -90.62204299231274, "y": 237.68131466705677}, "dragging": false}], "edges": [{"id": "e-direct-attack", "source": "source-risk", "target": "objectif-vise", "label": "Attaque directe", "labelStyle": {"fill": "#ff4500", "fontWeight": 600}, "style": {"stroke": "#ff4500", "strokeWidth": 1.5}, "zIndex": 5, "data": {"edgeType": "direct-attack"}, "type": "animated", "selected": false}, {"id": "e-source-stakeholder-1746915035577", "source": "source-risk", "target": "stakeholder-1746915035577", "type": "animated", "label": "Cible", "labelStyle": {"fill": "#D8000C", "fontWeight": 500}, "style": {"stroke": "#D8000C", "strokeWidth": 2}, "zIndex": 1, "selected": false}, {"id": "e-stakeholder-1746915035577-objectif", "source": "stakeholder-1746915035577", "target": "objectif-vise", "type": "animated", "label": "Vise", "labelStyle": {"fill": "#00529B", "fontWeight": 500}, "style": {"stroke": "#00529B", "strokeWidth": 2}, "zIndex": 1, "selected": false}, {"source": "node-4", "sourceHandle": "output-0", "target": "objectif-vise", "targetHandle": "target-handle", "id": "edge-4", "animated": true, "label": "Connexion", "style": {"stroke": "#666", "strokeWidth": 2}, "labelStyle": {"fill": "#666", "fontWeight": 500}, "type": "animated"}, {"source": "node-4", "sourceHandle": "output-0", "target": "stakeholder-1746915035577", "targetHandle": "target-handle", "id": "edge-5", "animated": true, "label": "Connexion", "style": {"stroke": "#666", "strokeWidth": 2}, "labelStyle": {"fill": "#666", "fontWeight": 500}, "type": "animated"}]}