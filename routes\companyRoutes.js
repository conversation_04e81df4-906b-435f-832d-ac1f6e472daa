// backend/routes/companyRoutes.js
const express = require('express');
const { 
  getCompanies, 
  getCompanyById, 
  createCompany, 
  updateCompany, 
  deleteCompany, 
  getCompanyUsers,
  getCompanyMetrics
} = require('../controllers/companyController');
const { getCompanyLogs } = require('../controllers/activityLogController');
const { protect, restrictTo, restrictToOwnCompany } = require('../middleware/authMiddleware');

const router = express.Router();

// All routes need authentication
router.use(protect);

// Company metrics - superadmin only
router.get('/metrics', restrictTo('superadmin'), getCompanyMetrics);

// Get all companies - superadmin only
router.route('/')
  .get(restrictTo('superadmin'), getCompanies)
  .post(restrictTo('superadmin'), createCompany);

// Routes for specific company
router.route('/:id')
  .get(protect, getCompanyById) // User can see their own company, checking in controller
  .put(restrictTo('superadmin'), updateCompany)
  .delete(restrictTo('superadmin'), deleteCompany);

// Get company users - superadmin or admin of the company
router.get('/:companyId/users', restrictToOwnCompany, getCompanyUsers);

// Get company logs - superadmin or admin of the company
router.get('/:companyId/logs', restrictToOwnCompany, getCompanyLogs);

module.exports = router;