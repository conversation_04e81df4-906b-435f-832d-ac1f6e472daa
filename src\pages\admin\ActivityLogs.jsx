// src/pages/admin/ActivityLogs.jsx
import React, { useState, useEffect } from 'react';
import { logService, companyService } from '../../services/apiServices';
import LoadingSpinner from '../../components/common/LoadingSpinner';

const ActivityLogs = () => {
  // State for activity logs
  const [activities, setActivities] = useState([]);
  const [companies, setCompanies] = useState([]);
  
  // Loading and error states
  const [isLoading, setIsLoading] = useState(true);
  const [isFiltering, setIsFiltering] = useState(false);
  const [error, setError] = useState(null);
  
  // Pagination state
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
    total: 0
  });
  
  // Filter state
  const [filters, setFilters] = useState({
    companyId: '',
    actionType: '',
    resourceType: '',
    startDate: '',
    endDate: ''
  });
  
  // Action types for filter dropdown
  const [actionTypes] = useState([
    { value: '', label: 'Toutes les actions' },
    { value: 'USER_LOGIN', label: 'Connexion' },
    { value: 'USER_LOGOUT', label: 'Déconnexion' },
    { value: 'USER_CREATE', label: 'Création utilisateur' },
    { value: 'USER_UPDATE', label: 'Modification utilisateur' },
    { value: 'USER_DELETE', label: 'Suppression utilisateur' },
    { value: 'COMPANY_CREATE', label: 'Création entreprise' },
    { value: 'COMPANY_UPDATE', label: 'Modification entreprise' },
    { value: 'COMPANY_DELETE', label: 'Suppression entreprise' },
    { value: 'ANALYSIS_CREATE', label: 'Création analyse' },
    { value: 'ANALYSIS_UPDATE', label: 'Modification analyse' },
    { value: 'ANALYSIS_DELETE', label: 'Suppression analyse' }
  ]);
  
  // Resource types for filter dropdown
  const [resourceTypes] = useState([
    { value: '', label: 'Toutes les ressources' },
    { value: 'user', label: 'Utilisateur' },
    { value: 'company', label: 'Entreprise' },
    { value: 'analysis', label: 'Analyse' }
  ]);

  // Fetch activities and companies on component mount
  useEffect(() => {
    fetchCompanies();
    fetchActivities();
  }, []);
  
  // Function to fetch activity logs from API
  const fetchActivities = async (page = 1) => {
    setIsLoading(true);
    setError(null);
    
    try {
      // Prepare filter params
      const params = {
        page,
        limit: pagination.limit,
        ...filters
      };
      
      // Remove empty filters
      Object.keys(params).forEach(key => 
        (params[key] === '' || params[key] === null) && delete params[key]
      );
      
      const response = await logService.getLogs(params);
      
      if (response.success) {
        // Make sure all activity records have the required fields with defaults
        const sanitizedActivities = (response.data || []).map(activity => ({
          id: activity.id || `temp-${Math.random()}`,
          timestamp: activity.timestamp || new Date().toISOString(),
          userId: activity.userId || '',
          userName: activity.userName || activity.userId || 'Utilisateur inconnu',
          companyName: activity.companyName || null,
          actionType: activity.actionType || 'UNKNOWN',
          resourceType: activity.resourceType || '',
          ipAddress: activity.ipAddress || '',
          details: activity.details || {}
        }));
        
        setActivities(sanitizedActivities);
        
        // Update pagination if available
        if (response.pagination) {
          setPagination({
            page: response.pagination.currentPage || 1,
            limit: response.pagination.limit || 10,
            total: response.pagination.total || 0
          });
        }
      } else {
        throw new Error(response.message || 'Failed to fetch activity logs');
      }
    } catch (error) {
      console.error('Error fetching activities:', error);
      setError('Erreur lors du chargement des activités. Veuillez réessayer plus tard.');
      
      // Use mock data in development for testing
      if (process.env.NODE_ENV === 'development') {
        setActivities([
          { id: '1', timestamp: '2025-03-19T09:30:00', userId: '<EMAIL>', userName: 'John Doe', companyName: 'Entreprise A', actionType: 'USER_LOGIN', resourceType: 'user', ipAddress: '*************', details: {} },
          { id: '2', timestamp: '2025-03-18T14:45:00', userId: '<EMAIL>', userName: 'Jane Smith', companyName: 'Entreprise B', actionType: 'ANALYSIS_CREATE', resourceType: 'analysis', ipAddress: '*************', details: {} },
          { id: '3', timestamp: '2025-03-18T10:15:00', userId: '<EMAIL>', userName: 'Admin User', companyName: null, actionType: 'COMPANY_CREATE', resourceType: 'company', ipAddress: '*************', details: {} },
          { id: '4', timestamp: '2025-03-17T16:30:00', userId: '<EMAIL>', userName: 'Bob Johnson', companyName: 'Entreprise C', actionType: 'USER_UPDATE', resourceType: 'user', ipAddress: '*************', details: {} }
        ]);
      }
    } finally {
      setIsLoading(false);
      setIsFiltering(false);
    }
  };
  
  // Function to fetch companies from API
  const fetchCompanies = async () => {
    try {
      const response = await companyService.getCompanies();
      
      if (response.success) {
        // Add 'All companies' option to the list
        setCompanies([
          { id: '', name: 'Toutes les entreprises' },
          ...(response.data || [])
        ]);
      } else {
        console.error('Failed to fetch companies:', response.message);
        
        // Use mock data in development
        if (process.env.NODE_ENV === 'development') {
          setCompanies([
            { id: '', name: 'Toutes les entreprises' },
            { id: '1', name: 'Entreprise A' },
            { id: '2', name: 'Entreprise B' },
            { id: '3', name: 'Entreprise C' }
          ]);
        }
      }
    } catch (error) {
      console.error('Error fetching companies:', error);
      
      // Use mock data in development
      if (process.env.NODE_ENV === 'development') {
        setCompanies([
          { id: '', name: 'Toutes les entreprises' },
          { id: '1', name: 'Entreprise A' },
          { id: '2', name: 'Entreprise B' },
          { id: '3', name: 'Entreprise C' }
        ]);
      }
    }
  };

  const handleFilterChange = (e) => {
    const { name, value } = e.target;
    setFilters({
      ...filters,
      [name]: value
    });
  };

  const applyFilters = () => {
    setIsFiltering(true);
    fetchActivities(1); // Reset to first page when filtering
  };
  
  const resetFilters = () => {
    setFilters({
      companyId: '',
      actionType: '',
      resourceType: '',
      startDate: '',
      endDate: ''
    });
    
    // Reset page and fetch activities
    setIsFiltering(true);
    fetchActivities(1);
  };
  
  const changePage = (newPage) => {
    // Prevent going to invalid pages
    if (newPage < 1 || newPage > Math.ceil(pagination.total / pagination.limit)) {
      return;
    }
    
    fetchActivities(newPage);
  };

  // Helper to get the color of the badge based on action type - WITH NULL CHECK
  const getActionColor = (actionType = '') => {
    // Default color for unknown action types
    if (!actionType) return 'bg-gray-100 text-gray-800';
    
    if (actionType.includes('LOGIN')) return 'bg-blue-100 text-blue-800';
    if (actionType.includes('CREATE')) return 'bg-green-100 text-green-800';
    if (actionType.includes('UPDATE')) return 'bg-yellow-100 text-yellow-800';
    if (actionType.includes('DELETE')) return 'bg-red-100 text-red-800';
    if (actionType.includes('LOGOUT')) return 'bg-purple-100 text-purple-800';
    return 'bg-gray-100 text-gray-800';
  };
  
  // Helper to display activity details in a formatted way
  const showActivityDetails = (details) => {
    if (!details || Object.keys(details).length === 0) {
      return alert('Aucun détail disponible pour cette activité.');
    }
    
    // Format the details for display
    const formattedDetails = JSON.stringify(details, null, 2);
    alert(formattedDetails);
  };

  return (
    <div className="p-6">
      <h1 className="text-2xl font-bold mb-6">Logs d'Activité</h1>
      
      {/* Error message */}
      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg mb-6">
          <p>{error}</p>
          <button 
            onClick={() => fetchActivities(pagination.page)} 
            className="mt-2 bg-red-100 hover:bg-red-200 text-red-800 px-3 py-1 rounded"
          >
            Réessayer
          </button>
        </div>
      )}
      
      {/* Filters */}
      <div className="bg-white p-4 rounded-lg shadow-md mb-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Entreprise
            </label>
            <select
              name="companyId"
              value={filters.companyId}
              onChange={handleFilterChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md"
              disabled={isFiltering}
            >
              {companies.map((company) => (
                <option key={company.id} value={company.id}>
                  {company.name}
                </option>
              ))}
            </select>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Type d'action
            </label>
            <select
              name="actionType"
              value={filters.actionType}
              onChange={handleFilterChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md"
              disabled={isFiltering}
            >
              {actionTypes.map((type) => (
                <option key={type.value} value={type.value}>
                  {type.label}
                </option>
              ))}
            </select>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Type de ressource
            </label>
            <select
              name="resourceType"
              value={filters.resourceType}
              onChange={handleFilterChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md"
              disabled={isFiltering}
            >
              {resourceTypes.map((type) => (
                <option key={type.value} value={type.value}>
                  {type.label}
                </option>
              ))}
            </select>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Date de début
            </label>
            <input
              type="date"
              name="startDate"
              value={filters.startDate}
              onChange={handleFilterChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md"
              disabled={isFiltering}
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Date de fin
            </label>
            <input
              type="date"
              name="endDate"
              value={filters.endDate}
              onChange={handleFilterChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md"
              disabled={isFiltering}
            />
          </div>
        </div>
        
        <div className="mt-4 flex space-x-3">
          <button
            onClick={applyFilters}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:bg-blue-300"
            disabled={isFiltering}
          >
            {isFiltering ? 'Chargement...' : 'Appliquer les filtres'}
          </button>
          
          <button
            onClick={resetFilters}
            className="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 disabled:bg-gray-200"
            disabled={isFiltering}
          >
            Réinitialiser
          </button>
        </div>
      </div>
      
      {/* Activity List */}
      {isLoading && activities.length === 0 ? (
        <div className="flex justify-center my-12">
          <LoadingSpinner />
        </div>
      ) : (
        <div className="bg-white rounded-lg shadow-md overflow-hidden">
          {activities.length === 0 ? (
            <div className="p-6 text-center text-gray-500">
              Aucune activité trouvée. Modifiez vos filtres et réessayez.
            </div>
          ) : (
            <>
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Date
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Utilisateur
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Entreprise
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Action
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Ressource
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      IP
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Détails
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {activities.map((activity) => (
                    <tr key={activity.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        {activity.timestamp ? new Date(activity.timestamp).toLocaleString() : '-'}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        {activity.userName || activity.userId || '-'}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        {activity.companyName || 'SuperAdmin'}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span
                          className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getActionColor(
                            activity.actionType
                          )}`}
                        >
                          {activity.actionType || 'UNKNOWN'}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        {activity.resourceType || '-'}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        {activity.ipAddress || '-'}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <button
                          onClick={() => showActivityDetails(activity.details)}
                          className="text-blue-600 hover:text-blue-900"
                        >
                          Voir détails
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
              
              {/* Pagination */}
              {pagination.total > 0 && (
                <div className="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
                  <div className="flex-1 flex justify-between sm:hidden">
                    <button
                      onClick={() => changePage(pagination.page - 1)}
                      disabled={pagination.page === 1}
                      className="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:bg-gray-100 disabled:text-gray-400"
                    >
                      Précédent
                    </button>
                    <button
                      onClick={() => changePage(pagination.page + 1)}
                      disabled={pagination.page >= Math.ceil(pagination.total / pagination.limit)}
                      className="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:bg-gray-100 disabled:text-gray-400"
                    >
                      Suivant
                    </button>
                  </div>
                  <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                    <div>
                      <p className="text-sm text-gray-700">
                        Affichage de <span className="font-medium">{((pagination.page - 1) * pagination.limit) + 1}</span> à{' '}
                        <span className="font-medium">
                          {Math.min(pagination.page * pagination.limit, pagination.total)}
                        </span>{' '}
                        sur <span className="font-medium">{pagination.total}</span> résultats
                      </p>
                    </div>
                    <div>
                      <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                        <button
                          onClick={() => changePage(pagination.page - 1)}
                          disabled={pagination.page === 1}
                          className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:bg-gray-100 disabled:text-gray-400"
                        >
                          <span className="sr-only">Précédent</span>
                          <svg className="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                            <path fillRule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clipRule="evenodd" />
                          </svg>
                        </button>
                        
                        {/* Page numbers - limited to 5 pages for UI simplicity */}
                        {Array.from({
                          length: Math.min(5, Math.ceil(pagination.total / pagination.limit))
                        }).map((_, i) => {
                          // Calculate the page number to display
                          let pageNum;
                          const totalPages = Math.ceil(pagination.total / pagination.limit);
                          
                          if (totalPages <= 5) {
                            // If 5 or fewer pages, show all pages
                            pageNum = i + 1;
                          } else if (pagination.page <= 3) {
                            // If near the start, show first 5 pages
                            pageNum = i + 1;
                          } else if (pagination.page >= totalPages - 2) {
                            // If near the end, show last 5 pages
                            pageNum = totalPages - 4 + i;
                          } else {
                            // Otherwise show 2 pages before and after current page
                            pageNum = pagination.page - 2 + i;
                          }
                          
                          return (
                            <button
                              key={pageNum}
                              onClick={() => changePage(pageNum)}
                              className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${
                                pagination.page === pageNum
                                  ? 'z-10 bg-indigo-50 border-indigo-500 text-indigo-600'
                                  : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'
                              }`}
                            >
                              {pageNum}
                            </button>
                          );
                        })}
                        
                        <button
                          onClick={() => changePage(pagination.page + 1)}
                          disabled={pagination.page >= Math.ceil(pagination.total / pagination.limit)}
                          className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:bg-gray-100 disabled:text-gray-400"
                        >
                          <span className="sr-only">Suivant</span>
                          <svg className="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                            <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
                          </svg>
                        </button>
                      </nav>
                    </div>
                  </div>
                </div>
              )}
            </>
          )}
        </div>
      )}
      <div className="mb-4 p-4 bg-gray-100 rounded">
  <p className="font-bold">Debug Info:</p>
  <pre className="text-xs overflow-auto max-h-48">
    {JSON.stringify({response: activities}, null, 2)}
  </pre>
</div>
    </div>
  );
};

export default ActivityLogs;