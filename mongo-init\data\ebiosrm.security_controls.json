[{"_id": {"$oid": "680575d33fca057a8d18c045"}, "measure_description": "<PERSON><PERSON><PERSON> les données sensibles au repos à l'aide d'algorithmes forts.", "pillar": "Confidentialité", "risk_treatment_strategy": "Ré<PERSON><PERSON> le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.263Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.263Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c046"}, "measure_description": "Mettre en œuvre des politiques strictes de contrôle d'accès basé sur les rôles (RBAC/CBAR).", "pillar": "Confidentialité", "risk_treatment_strategy": "Ré<PERSON><PERSON> le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.264Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.264Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c047"}, "measure_description": "Imposer l'authentification multifacteur (MFA/AMF) pour l'accès à distance.", "pillar": "Confidentialité", "risk_treatment_strategy": "Ré<PERSON><PERSON> le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.264Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.264Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c048"}, "measure_description": "Utiliser le chiffrement TLS/SSL pour toute transmission de données sur les réseaux.", "pillar": "Confidentialité", "risk_treatment_strategy": "Ré<PERSON><PERSON> le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.264Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.264Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c049"}, "measure_description": "Déployer des solutions de prévention de la perte de données (DLP) sur les points de terminaison et le réseau.", "pillar": "Confidentialité", "risk_treatment_strategy": "Ré<PERSON><PERSON> le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.264Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.264Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c04a"}, "measure_description": "Former régulièrement les employés sur la manipulation des données et les politiques de confidentialité.", "pillar": "Confidentialité", "risk_treatment_strategy": "Ré<PERSON><PERSON> le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.264Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.264Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c04b"}, "measure_description": "Mettre en œuvre le masquage ou l'anonymisation des données dans les environnements hors production.", "pillar": "Confidentialité", "risk_treatment_strategy": "Ré<PERSON><PERSON> le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.264Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.264Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c04c"}, "measure_description": "Mener des audits de sécurité périodiques des permissions d'accès.", "pillar": "Confidentialité", "risk_treatment_strategy": "Ré<PERSON><PERSON> le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.264Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.264Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c04d"}, "measure_description": "Appliquer des politiques de complexité et de rotation des mots de passe robustes.", "pillar": "Confidentialité", "risk_treatment_strategy": "Ré<PERSON><PERSON> le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.264Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.264Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c04e"}, "measure_description": "Sécuriser les documents physiques contenant des informations sensibles.", "pillar": "Confidentialité", "risk_treatment_strategy": "Ré<PERSON><PERSON> le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.264Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.264Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c04f"}, "measure_description": "Éviter de collecter des informations personnelles identifiables (IPI/PII) sauf si strictement nécessaire.", "pillar": "Confidentialité", "risk_treatment_strategy": "<PERSON><PERSON><PERSON> le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.264Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.264Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c050"}, "measure_description": "Interdire l'utilisation de périphériques de stockage portables non chiffrés (clés USB).", "pillar": "Confidentialité", "risk_treatment_strategy": "<PERSON><PERSON><PERSON> le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.264Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.264Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c051"}, "measure_description": "Ne pas stocker de données sensibles sur des serveurs accessibles publiquement.", "pillar": "Confidentialité", "risk_treatment_strategy": "<PERSON><PERSON><PERSON> le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.264Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.264Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c052"}, "measure_description": "Restreindre entièrement l'accès aux référentiels de données sensibles pour le personnel non essentiel.", "pillar": "Confidentialité", "risk_treatment_strategy": "<PERSON><PERSON><PERSON> le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.264Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.264Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c053"}, "measure_description": "Éviter de transmettre des données sensibles sur des réseaux Wi-Fi publics non sécurisés.", "pillar": "Confidentialité", "risk_treatment_strategy": "<PERSON><PERSON><PERSON> le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.265Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.265Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c054"}, "measure_description": "<PERSON><PERSON><PERSON><PERSON>re les données sensibles de manière sécurisée une fois leur période de conservation expirée.", "pillar": "Confidentialité", "risk_treatment_strategy": "<PERSON><PERSON><PERSON> le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.265Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.265Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c055"}, "measure_description": "Interdire le partage des identifiants de compte entre utilisateurs.", "pillar": "Confidentialité", "risk_treatment_strategy": "<PERSON><PERSON><PERSON> le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.265Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.265Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c056"}, "measure_description": "Éviter d'utiliser des services tiers ayant des postures de sécurité inadéquates pour les données sensibles.", "pillar": "Confidentialité", "risk_treatment_strategy": "<PERSON><PERSON><PERSON> le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.265Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.265Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c057"}, "measure_description": "Désactiver les services réseau non nécessaires traitant des informations sensibles.", "pillar": "Confidentialité", "risk_treatment_strategy": "<PERSON><PERSON><PERSON> le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.265Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.265Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c058"}, "measure_description": "Interdire l'installation de logiciels non autorisés sur les systèmes accédant aux données sensibles.", "pillar": "Confidentialité", "risk_treatment_strategy": "<PERSON><PERSON><PERSON> le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.265Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.265Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c059"}, "measure_description": "Souscrire une cyberassurance couvrant les incidents de violation de données et la responsabilité.", "pillar": "Confidentialité", "risk_treatment_strategy": "Transférer le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.265Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.265Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c05a"}, "measure_description": "Externaliser les services de destruction sécurisée de données à un fournisseur certifié.", "pillar": "Confidentialité", "risk_treatment_strategy": "Transférer le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.265Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.265Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c05b"}, "measure_description": "Utiliser un fournisseur de cloud avec des contrôles de sécurité robustes et des SLA pour la confidentialité.", "pillar": "Confidentialité", "risk_treatment_strategy": "Transférer le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.265Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.265Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c05c"}, "measure_description": "Employer un fournisseur de services de sécurité gérés (MSSP) tiers pour la surveillance.", "pillar": "Confidentialité", "risk_treatment_strategy": "Transférer le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.265Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.265Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c05d"}, "measure_description": "Utiliser des services de tests d'intrusion tiers pour identifier les vulnérabilités.", "pillar": "Confidentialité", "risk_treatment_strategy": "Transférer le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.265Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.265Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c05e"}, "measure_description": "Mettre en œuvre des services de passerelle de messagerie sécurisée d'un fournisseur spécialisé.", "pillar": "Confidentialité", "risk_treatment_strategy": "Transférer le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.265Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.265Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c05f"}, "measure_description": "S'appuyer sur les contrats des fournisseurs pour les responsabilités de protection des données dans les applications SaaS.", "pillar": "Confidentialité", "risk_treatment_strategy": "Transférer le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.265Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.265Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c060"}, "measure_description": "Utiliser un service d'entiercement (escrow) tiers de confiance pour la gestion des clés sensibles.", "pillar": "Confidentialité", "risk_treatment_strategy": "Transférer le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.265Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.265Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c061"}, "measure_description": "Externaliser les vérifications des antécédents pour le personnel manipulant des données hautement sensibles.", "pillar": "Confidentialité", "risk_treatment_strategy": "Transférer le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.265Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.265Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c062"}, "measure_description": "Engager un conseiller juridique spécialisé dans les réglementations sur la protection des données.", "pillar": "Confidentialité", "risk_treatment_strategy": "Transférer le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.265Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.265Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c063"}, "measure_description": "Accepter formellement le risque d'exposition accidentelle de données dans les environnements de bureau ouverts (open space).", "pillar": "Confidentialité", "risk_treatment_strategy": "Accepter le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.265Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.265Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c064"}, "measure_description": "Documenter l'acceptation du risque résiduel après la mise en œuvre des contrôles de chiffrement.", "pillar": "Confidentialité", "risk_treatment_strategy": "Accepter le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.265Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.265Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c065"}, "measure_description": "Accepter le risque pour des ensembles de données spécifiques à faible sensibilité jugés non critiques.", "pillar": "Confidentialité", "risk_treatment_strategy": "Accepter le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.265Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.265Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c066"}, "measure_description": "Reconnaître le risque associé aux systèmes hérités où le chiffrement est infaisable.", "pillar": "Confidentialité", "risk_treatment_strategy": "Accepter le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.265Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.265Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c067"}, "measure_description": "Accepter le risque lié à l'utilisation de données anonymisées pour l'analyse (ré-identification potentielle).", "pillar": "Confidentialité", "risk_treatment_strategy": "Accepter le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.265Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.265Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c068"}, "measure_description": "<PERSON><PERSON><PERSON><PERSON> de ne pas mettre en œuvre des contrôles coûteux pour les données ayant un très faible impact en cas de compromission.", "pillar": "Confidentialité", "risk_treatment_strategy": "Accepter le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.265Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.265Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c069"}, "measure_description": "Accepter le risque d'utilisation abusive interne des données après avoir fourni une formation adéquate.", "pillar": "Confidentialité", "risk_treatment_strategy": "Accepter le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.265Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.265Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c06a"}, "measure_description": "Accepter formellement le risque d'exposition des données pendant les fenêtres de maintenance système nécessaires.", "pillar": "Confidentialité", "risk_treatment_strategy": "Accepter le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.265Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.265Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c06b"}, "measure_description": "Reconnaître et accepter les limitations de l'efficacité du DLP contre les initiés déterminés.", "pillar": "Confidentialité", "risk_treatment_strategy": "Accepter le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.265Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.265Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c06c"}, "measure_description": "Accepter le risque d'exposition des métadonnées même lorsque le contenu est chiffré.", "pillar": "Confidentialité", "risk_treatment_strategy": "Accepter le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.265Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.265Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c06d"}, "measure_description": "Mettre en œuvre la Surveillance de l'Intégrité des Fichiers (FIM) sur les fichiers système critiques.", "pillar": "Intégrité", "risk_treatment_strategy": "Ré<PERSON><PERSON> le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.265Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.265Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c06e"}, "measure_description": "Utiliser des signatures numériques pour vérifier l'authenticité des logiciels et des documents.", "pillar": "Intégrité", "risk_treatment_strategy": "Ré<PERSON><PERSON> le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.265Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.265Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c06f"}, "measure_description": "Employer des systèmes de contrôle de version pour les fichiers de configuration et le code source.", "pillar": "Intégrité", "risk_treatment_strategy": "Ré<PERSON><PERSON> le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.265Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.265Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c070"}, "measure_description": "Effectuer des sauvegardes de données régulières et tester fréquemment les procédures de restauration.", "pillar": "Intégrité", "risk_treatment_strategy": "Ré<PERSON><PERSON> le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.265Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.265Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c071"}, "measure_description": "Valider toutes les entrées utilisateur pour prévenir les attaques par injection (SQLi, XSS).", "pillar": "Intégrité", "risk_treatment_strategy": "Ré<PERSON><PERSON> le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.265Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.265Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c072"}, "measure_description": "Appliquer rapidement les correctifs de sécurité aux systèmes d'exploitation et aux applications.", "pillar": "Intégrité", "risk_treatment_strategy": "Ré<PERSON><PERSON> le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.265Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.265Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c073"}, "measure_description": "Utiliser des hachages cryptographiques (checksums) pour vérifier l'intégrité des données pendant le transfert.", "pillar": "Intégrité", "risk_treatment_strategy": "Ré<PERSON><PERSON> le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.265Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.265Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c074"}, "measure_description": "Déployer des Systèmes de Détection/Prévention d'Intrusion (IDPS) pour bloquer les modifications malveillantes.", "pillar": "Intégrité", "risk_treatment_strategy": "Ré<PERSON><PERSON> le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.266Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.266Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c075"}, "measure_description": "Appliquer des pratiques de codage sécurisé tout au long du cycle de vie du développement logiciel.", "pillar": "Intégrité", "risk_treatment_strategy": "Ré<PERSON><PERSON> le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.266Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.266Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c076"}, "measure_description": "Mettre en œuvre des processus de gestion des changements avec des flux de travail d'approbation.", "pillar": "Intégrité", "risk_treatment_strategy": "Ré<PERSON><PERSON> le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.266Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.266Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c077"}, "measure_description": "Restreindre l'accès en écriture aux fichiers de configuration et répertoires critiques.", "pillar": "Intégrité", "risk_treatment_strategy": "<PERSON><PERSON><PERSON> le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.266Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.266Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c078"}, "measure_description": "Utiliser des déploiements d'infrastructure immuable lorsque cela est possible.", "pillar": "Intégrité", "risk_treatment_strategy": "<PERSON><PERSON><PERSON> le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.266Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.266Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c079"}, "measure_description": "Interdire l'accès direct pour modification de la base de données aux développeurs en production.", "pillar": "Intégrité", "risk_treatment_strategy": "<PERSON><PERSON><PERSON> le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.266Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.266Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c07a"}, "measure_description": "Désactiver l'installation de logiciels ou d'extensions de navigateur non autorisés.", "pillar": "Intégrité", "risk_treatment_strategy": "<PERSON><PERSON><PERSON> le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.266Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.266Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c07b"}, "measure_description": "Éviter d'utiliser des logiciels provenant de sources non fiables ou non vérifiées.", "pillar": "Intégrité", "risk_treatment_strategy": "<PERSON><PERSON><PERSON> le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.266Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.266Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c07c"}, "measure_description": "Utiliser un stockage WORM (Write-Once-Read-Many) pour les journaux (logs) et archives critiques.", "pillar": "Intégrité", "risk_treatment_strategy": "<PERSON><PERSON><PERSON> le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.266Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.266Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c07d"}, "measure_description": "<PERSON><PERSON><PERSON> les mots de passe et configurations par défaut sur tous les systèmes.", "pillar": "Intégrité", "risk_treatment_strategy": "<PERSON><PERSON><PERSON> le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.266Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.266Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c07e"}, "measure_description": "<PERSON><PERSON><PERSON> d'exécuter des systèmes présentant des vulnérabilités critiques connues et non corrigeables.", "pillar": "Intégrité", "risk_treatment_strategy": "<PERSON><PERSON><PERSON> le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.266Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.266Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c07f"}, "measure_description": "Empêcher les utilisateurs de désactiver les logiciels de sécurité des terminaux.", "pillar": "Intégrité", "risk_treatment_strategy": "<PERSON><PERSON><PERSON> le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.266Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.266Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c080"}, "measure_description": "Ne pas autoriser le déploiement de code sans tests et approbation préalables.", "pillar": "Intégrité", "risk_treatment_strategy": "<PERSON><PERSON><PERSON> le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.266Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.266Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c081"}, "measure_description": "Utiliser une Autorité de Certification (AC) tierce de confiance pour les certificats SSL/TLS.", "pillar": "Intégrité", "risk_treatment_strategy": "Transférer le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.266Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.266Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c082"}, "measure_description": "<PERSON><PERSON>berger les applications critiques chez un fournisseur offrant des garanties d'intégrité et une surveillance.", "pillar": "Intégrité", "risk_treatment_strategy": "Transférer le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.266Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.266Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c083"}, "measure_description": "Utiliser un Réseau de Diffusion de Contenu (CDN) avec des vérifications d'intégrité intégrées.", "pillar": "Intégrité", "risk_treatment_strategy": "Transférer le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.266Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.266Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c084"}, "measure_description": "Souscrire une assurance couvrant la corruption de données ou l'altération malveillante du système.", "pillar": "Intégrité", "risk_treatment_strategy": "Transférer le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.266Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.266Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c085"}, "measure_description": "S'appuyer sur les fournisseurs SaaS pour maintenir l'intégrité de leurs plateformes.", "pillar": "Intégrité", "risk_treatment_strategy": "Transférer le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.266Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.266Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c086"}, "measure_description": "Externaliser la gestion des correctifs à un prestataire de services spécialisé.", "pillar": "Intégrité", "risk_treatment_strategy": "Transférer le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.266Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.266Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c087"}, "measure_description": "Utiliser des services tiers de signature de code pour assurer l'intégrité des logiciels.", "pillar": "Intégrité", "risk_treatment_strategy": "Transférer le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.266Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.266Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c088"}, "measure_description": "Employer des auditeurs de sécurité externes pour vérifier les contrôles d'intégrité.", "pillar": "Intégrité", "risk_treatment_strategy": "Transférer le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.266Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.266Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c089"}, "measure_description": "Utiliser les services de bases de données gérées du fournisseur cloud avec des fonctionnalités d'intégrité.", "pillar": "Intégrité", "risk_treatment_strategy": "Transférer le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.266Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.266Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c08a"}, "measure_description": "Transférer le risque d'infections par malware via une solution de détection et réponse gérée sur les terminaux (EDR).", "pillar": "Intégrité", "risk_treatment_strategy": "Transférer le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.266Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.266Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c08b"}, "measure_description": "Accepter des incohérences de données mineures dans les outils de reporting internes non critiques.", "pillar": "Intégrité", "risk_treatment_strategy": "Accepter le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.266Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.266Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c08c"}, "measure_description": "Accepter formellement le risque de dérive de configuration sur les environnements de développement/test.", "pillar": "Intégrité", "risk_treatment_strategy": "Accepter le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.266Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.266Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c08d"}, "measure_description": "Accepter le potentiel d'erreurs mineures de saisie de données par l'utilisateur après avoir fourni une formation.", "pillar": "Intégrité", "risk_treatment_strategy": "Accepter le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.266Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.266Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c08e"}, "measure_description": "Documenter l'acceptation de l'utilisation de logiciels hérités où la vérification de l'intégrité est limitée.", "pillar": "Intégrité", "risk_treatment_strategy": "Accepter le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.266Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.266Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c08f"}, "measure_description": "Accepter le risque de changements non détectés à faible impact dans les systèmes non critiques.", "pillar": "Intégrité", "risk_treatment_strategy": "Accepter le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.267Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.267Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c090"}, "measure_description": "<PERSON><PERSON><PERSON><PERSON> de ne pas implémenter de FIM coûteux en temps réel sur les actifs à faible risque.", "pillar": "Intégrité", "risk_treatment_strategy": "Accepter le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.267Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.267Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c091"}, "measure_description": "Accepter le risque résiduel des exploits zero-day contournant les contrôles existants.", "pillar": "Intégrité", "risk_treatment_strategy": "Accepter le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.267Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.267Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c092"}, "measure_description": "Reconnaître les problèmes potentiels d'intégrité provenant de flux de données tiers (après validation).", "pillar": "Intégrité", "risk_treatment_strategy": "Accepter le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.267Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.267Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c093"}, "measure_description": "Accepter que les sauvegardes puissent présenter des incohérences mineures entre les cycles de sauvegarde.", "pillar": "Intégrité", "risk_treatment_strategy": "Accepter le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.267Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.267Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c094"}, "measure_description": "Accepter formellement les risques associés à l'intégrité des données des tableurs gérés par les utilisateurs.", "pillar": "Intégrité", "risk_treatment_strategy": "Accepter le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.267Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.267Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c095"}, "measure_description": "Mettre en œuvre l'équilibrage de charge des serveurs sur plusieurs systèmes.", "pillar": "Disponibilité", "risk_treatment_strategy": "Ré<PERSON><PERSON> le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.267Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.267Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c096"}, "measure_description": "Utiliser des composants matériels redondants (alimentations, cartes réseau, disques - RAID).", "pillar": "Disponibilité", "risk_treatment_strategy": "Ré<PERSON><PERSON> le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.267Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.267Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c097"}, "measure_description": "Maintenir et tester régulièrement un plan complet de reprise d'activité (PRA) / récupération après sinistre (DR).", "pillar": "Disponibilité", "risk_treatment_strategy": "Ré<PERSON><PERSON> le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.267Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.267Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c098"}, "measure_description": "Effectuer des sauvegardes système régulières et assurer la capacité de restauration.", "pillar": "Disponibilité", "risk_treatment_strategy": "Ré<PERSON><PERSON> le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.267Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.267Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c099"}, "measure_description": "Déployer des solutions d'atténuation DDoS en périphérie du réseau.", "pillar": "Disponibilité", "risk_treatment_strategy": "Ré<PERSON><PERSON> le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.267Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.267Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c09a"}, "measure_description": "Surveiller de manière proactive les performances du système et l'utilisation des ressources.", "pillar": "Disponibilité", "risk_treatment_strategy": "Ré<PERSON><PERSON> le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.267Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.267Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c09b"}, "measure_description": "Mettre en œuvre des mécanismes de basculement (failover) automatisés pour les services critiques.", "pillar": "Disponibilité", "risk_treatment_strategy": "Ré<PERSON><PERSON> le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.267Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.267Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c09c"}, "measure_description": "Assurer une bande passante réseau adéquate et une redondance (plusieurs FAI).", "pillar": "Disponibilité", "risk_treatment_strategy": "Ré<PERSON><PERSON> le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.267Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.267Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c09d"}, "measure_description": "Appliquer les correctifs et mises à jour pendant les fenêtres de maintenance planifiées.", "pillar": "Disponibilité", "risk_treatment_strategy": "Ré<PERSON><PERSON> le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.267Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.267Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c09e"}, "measure_description": "Maintenir des contrôles environnementaux (refroidissement, extinction d'incendie) dans les centres de données.", "pillar": "Disponibilité", "risk_treatment_strategy": "Ré<PERSON><PERSON> le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.267Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.267Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c09f"}, "measure_description": "Concevoir l'architecture système pour éliminer les points uniques de défaillance.", "pillar": "Disponibilité", "risk_treatment_strategy": "<PERSON><PERSON><PERSON> le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.267Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.267Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c0a0"}, "measure_description": "Éviter de planifier la maintenance des systèmes critiques pendant les heures de pointe.", "pillar": "Disponibilité", "risk_treatment_strategy": "<PERSON><PERSON><PERSON> le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.267Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.267Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c0a1"}, "measure_description": "Ne pas dépendre d'une seule région géographique pour l'infrastructure critique.", "pillar": "Disponibilité", "risk_treatment_strategy": "<PERSON><PERSON><PERSON> le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.267Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.267Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c0a2"}, "measure_description": "É<PERSON>ter les interdépendances système excessivement complexes qui augmentent la fragilité.", "pillar": "Disponibilité", "risk_treatment_strategy": "<PERSON><PERSON><PERSON> le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.267Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.267Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c0a3"}, "measure_description": "Interdire le déploiement de changements non testés dans les environnements de production.", "pillar": "Disponibilité", "risk_treatment_strategy": "<PERSON><PERSON><PERSON> le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.267Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.267Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c0a4"}, "measure_description": "<PERSON><PERSON><PERSON> de déployer des services critiques sur du matériel approchant de sa fin de vie.", "pillar": "Disponibilité", "risk_treatment_strategy": "<PERSON><PERSON><PERSON> le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.267Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.267Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c0a5"}, "measure_description": "Ne pas exécuter de charges de travail de production critiques sur l'infrastructure de développement ou de test.", "pillar": "Disponibilité", "risk_treatment_strategy": "<PERSON><PERSON><PERSON> le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.267Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.267Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c0a6"}, "measure_description": "É<PERSON>iner la dépendance vis-à-vis de logiciels ou de systèmes d'exploitation non supportés.", "pillar": "Disponibilité", "risk_treatment_strategy": "<PERSON><PERSON><PERSON> le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.267Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.267Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c0a7"}, "measure_description": "<PERSON><PERSON><PERSON> l'épuisement des ressources en mettant en œuvre une planification de capacité.", "pillar": "Disponibilité", "risk_treatment_strategy": "<PERSON><PERSON><PERSON> le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.267Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.267Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c0a8"}, "measure_description": "Prévenir les arrêts accidentels en restreignant l'accès physique au matériel critique.", "pillar": "Disponibilité", "risk_treatment_strategy": "<PERSON><PERSON><PERSON> le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.267Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.267Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c0a9"}, "measure_description": "Utiliser des fournisseurs d'hébergement cloud avec des Accords de Niveau de Service (SLA/ANS) de haute disponibilité.", "pillar": "Disponibilité", "risk_treatment_strategy": "Transférer le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.267Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.267Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c0aa"}, "measure_description": "Externaliser les services de protection DDoS à un fournisseur tiers spécialisé.", "pillar": "Disponibilité", "risk_treatment_strategy": "Transférer le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.267Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.267Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c0ab"}, "measure_description": "Souscrire une assurance pertes d'exploitation pour couvrir les pertes financières dues aux temps d'arrêt.", "pillar": "Disponibilité", "risk_treatment_strategy": "Transférer le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.267Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.267Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c0ac"}, "measure_description": "Utiliser un Réseau de Diffusion de Contenu (CDN) pour améliorer la disponibilité globale.", "pillar": "Disponibilité", "risk_treatment_strategy": "Transférer le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.267Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.267Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c0ad"}, "measure_description": "Contracter avec un tiers pour des services gérés de sauvegarde et de reprise d'activité.", "pillar": "Disponibilité", "risk_treatment_strategy": "Transférer le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.267Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.267Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c0ae"}, "measure_description": "Externaliser les opérations du centre de données, y compris la gestion de l'alimentation et du refroidissement.", "pillar": "Disponibilité", "risk_treatment_strategy": "Transférer le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.267Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.267Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c0af"}, "measure_description": "S'appuyer sur les engagements des fournisseurs SaaS pour la disponibilité des applications.", "pillar": "Disponibilité", "risk_treatment_strategy": "Transférer le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.267Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.267Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c0b0"}, "measure_description": "Engager un tiers pour la surveillance et les alertes d'infrastructure 24/7.", "pillar": "Disponibilité", "risk_treatment_strategy": "Transférer le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.268Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.268Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c0b1"}, "measure_description": "Transférer le risque de panne matérielle via des contrats de garantie/support complets.", "pillar": "Disponibilité", "risk_treatment_strategy": "Transférer le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.268Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.268Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c0b2"}, "measure_description": "Utiliser des services DNS gérés avec distribution mondiale et redondance.", "pillar": "Disponibilité", "risk_treatment_strategy": "Transférer le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.268Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.268Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c0b3"}, "measure_description": "Accepter des temps d'arrêt brefs et occasionnels pour les applications internes non critiques.", "pillar": "Disponibilité", "risk_treatment_strategy": "Accepter le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.268Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.268Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c0b4"}, "measure_description": "Définir et accepter des objectifs de disponibilité inférieurs pour les environnements de développement/test.", "pillar": "Disponibilité", "risk_treatment_strategy": "Accepter le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.268Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.268Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c0b5"}, "measure_description": "Accepter le risque de panne matérielle pour les postes de travail utilisateur standard (remplacement en cas de panne).", "pillar": "Disponibilité", "risk_treatment_strategy": "Accepter le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.268Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.268Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c0b6"}, "measure_description": "Accepter formellement des Objectifs de Temps de Reprise (RTO) plus longs pour les données archivées.", "pillar": "Disponibilité", "risk_treatment_strategy": "Accepter le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.268Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.268Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c0b7"}, "measure_description": "Accepter le risque de dégradation mineure du service pendant les périodes de charge de pointe.", "pillar": "Disponibilité", "risk_treatment_strategy": "Accepter le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.268Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.268Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c0b8"}, "measure_description": "<PERSON><PERSON><PERSON><PERSON> de ne pas opter pour une redondance actif-actif coûteuse pour les services à faible impact financier.", "pillar": "Disponibilité", "risk_treatment_strategy": "Accepter le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.268Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.268Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c0b9"}, "measure_description": "Accepter le risque de dépendre d'un seul FAI pour les succursales non critiques.", "pillar": "Disponibilité", "risk_treatment_strategy": "Accepter le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.268Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.268Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c0ba"}, "measure_description": "Reconnaître et accepter les temps d'arrêt potentiels liés aux événements de force majeure.", "pillar": "Disponibilité", "risk_treatment_strategy": "Accepter le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.268Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.268Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c0bb"}, "measure_description": "Accepter le risque de temps d'arrêt planifiés annoncés par les fournisseurs de services tiers.", "pillar": "Disponibilité", "risk_treatment_strategy": "Accepter le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.268Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.268Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c0bc"}, "measure_description": "Accepter formellement les limitations des capacités de reprise d'activité (PRA/DR) pour les systèmes hérités non essentiels.", "pillar": "Disponibilité", "risk_treatment_strategy": "Accepter le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.268Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.268Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c0bd"}, "measure_description": "Activer la journalisation détaillée des événements système, applicatifs et de sécurité.", "pillar": "Traçabilité", "risk_treatment_strategy": "Ré<PERSON><PERSON> le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.268Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.268Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c0be"}, "measure_description": "Centraliser les journaux (logs) des systèmes disparates dans une solution SIEM.", "pillar": "Traçabilité", "risk_treatment_strategy": "Ré<PERSON><PERSON> le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.268Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.268Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c0bf"}, "measure_description": "Attribuer des identifiants utilisateur uniques à tout le personnel accédant aux systèmes.", "pillar": "Traçabilité", "risk_treatment_strategy": "Ré<PERSON><PERSON> le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.268Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.268Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c0c0"}, "measure_description": "Mettre en œuvre une synchronisation horaire précise sur tous les systèmes (NTP).", "pillar": "Traçabilité", "risk_treatment_strategy": "Ré<PERSON><PERSON> le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.268Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.268Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c0c1"}, "measure_description": "Journaliser toutes les actions administratives et les changements apportés aux systèmes critiques.", "pillar": "Traçabilité", "risk_treatment_strategy": "Ré<PERSON><PERSON> le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.268Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.268Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c0c2"}, "measure_description": "Surveiller et journaliser les modèles de flux de trafic réseau.", "pillar": "Traçabilité", "risk_treatment_strategy": "Ré<PERSON><PERSON> le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.268Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.268Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c0c3"}, "measure_description": "Mettre en œuvre la surveillance de l'activité utilisateur sur les systèmes sensibles, le cas échéant.", "pillar": "Traçabilité", "risk_treatment_strategy": "Ré<PERSON><PERSON> le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.268Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.268Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c0c4"}, "measure_description": "Conserver les journaux (logs) conformément aux exigences réglementaires et à la politique interne.", "pillar": "Traçabilité", "risk_treatment_strategy": "Ré<PERSON><PERSON> le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.268Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.268Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c0c5"}, "measure_description": "Corr<PERSON>ler les événements de sécurité entre différentes sources de journaux pour obtenir du contexte.", "pillar": "Traçabilité", "risk_treatment_strategy": "Ré<PERSON><PERSON> le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.268Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.268Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c0c6"}, "measure_description": "Journaliser l'accès aux fichiers et bases de données sensibles.", "pillar": "Traçabilité", "risk_treatment_strategy": "Ré<PERSON><PERSON> le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.268Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.268Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c0c7"}, "measure_description": "Interdire l'utilisation de comptes utilisateur partagés ou génériques.", "pillar": "Traçabilité", "risk_treatment_strategy": "<PERSON><PERSON><PERSON> le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.268Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.268Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c0c8"}, "measure_description": "<PERSON><PERSON><PERSON> <PERSON>activer les journaux d'audit essentiels du système d'exploitation ou de l'application.", "pillar": "Traçabilité", "risk_treatment_strategy": "<PERSON><PERSON><PERSON> le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.268Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.268Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c0c9"}, "measure_description": "Ne pas autoriser l'accès anonyme aux systèmes nécessitant une imputabilité.", "pillar": "Traçabilité", "risk_treatment_strategy": "<PERSON><PERSON><PERSON> le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.268Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.268Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c0ca"}, "measure_description": "Empêcher la modification ou la suppression non autorisée des fichiers journaux.", "pillar": "Traçabilité", "risk_treatment_strategy": "<PERSON><PERSON><PERSON> le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.268Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.268Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c0cb"}, "measure_description": "Éviter d'utiliser excessivement la traduction d'adresses réseau (NAT) lorsque l'IP source est critique.", "pillar": "Traçabilité", "risk_treatment_strategy": "<PERSON><PERSON><PERSON> le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.268Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.268Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c0cc"}, "measure_description": "Restreindre l'accès administratif direct aux systèmes de production chaque fois que possible.", "pillar": "Traçabilité", "risk_treatment_strategy": "<PERSON><PERSON><PERSON> le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.268Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.268Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c0cd"}, "measure_description": "<PERSON><PERSON><PERSON> les configurations système qui masquent les actions utilisateur (par ex., désactivation de l'historique des commandes).", "pillar": "Traçabilité", "risk_treatment_strategy": "<PERSON><PERSON><PERSON> le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.268Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.268Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c0ce"}, "measure_description": "Ne pas autoriser les sessions d'accès à distance non surveillées à l'infrastructure critique.", "pillar": "Traçabilité", "risk_treatment_strategy": "<PERSON><PERSON><PERSON> le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.268Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.268Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c0cf"}, "measure_description": "É<PERSON>iner les systèmes ou applications dépourvus de capacités de journalisation adéquates.", "pillar": "Traçabilité", "risk_treatment_strategy": "<PERSON><PERSON><PERSON> le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.268Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.268Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c0d0"}, "measure_description": "Empêcher les changements en dehors de la gestion des changements établie (qui inclut la journalisation).", "pillar": "Traçabilité", "risk_treatment_strategy": "<PERSON><PERSON><PERSON> le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.268Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.268Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c0d1"}, "measure_description": "Utiliser un service géré de Gestion des Informations et des Événements de Sécurité (SIEM).", "pillar": "Traçabilité", "risk_treatment_strategy": "Transférer le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.268Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.268Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c0d2"}, "measure_description": "Externaliser le stockage et l'archivage des journaux (logs) à un fournisseur cloud spécialisé.", "pillar": "Traçabilité", "risk_treatment_strategy": "Transférer le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.268Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.268Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c0d3"}, "measure_description": "Employer un Centre des Opérations de Sécurité (SOC) tiers pour l'analyse des journaux.", "pillar": "Traçabilité", "risk_treatment_strategy": "Transférer le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.268Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.268Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c0d4"}, "measure_description": "S'appuyer sur les services de journalisation du fournisseur cloud (par ex., AWS CloudTrail, journaux Azure Monitor).", "pillar": "Traçabilité", "risk_treatment_strategy": "Transférer le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.268Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.268Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c0d5"}, "measure_description": "Utiliser des applications SaaS où le fournisseur gère la journalisation de l'activité.", "pillar": "Traçabilité", "risk_treatment_strategy": "Transférer le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.268Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.268Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c0d6"}, "measure_description": "Contracter avec des enquêteurs forensiques qui s'appuient sur les journaux comme preuves.", "pillar": "Traçabilité", "risk_treatment_strategy": "Transférer le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.268Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.268Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c0d7"}, "measure_description": "Utiliser des fournisseurs d'identité (IdP) tiers gérant les journaux d'authentification.", "pillar": "Traçabilité", "risk_treatment_strategy": "Transférer le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.268Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.268Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c0d8"}, "measure_description": "Mettre en œuvre une solution de détection et réponse gérée sur les terminaux (EDR) avec des fonctionnalités de journalisation.", "pillar": "Traçabilité", "risk_treatment_strategy": "Transférer le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.268Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.268Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c0d9"}, "measure_description": "Utiliser des services externes pour surveiller la conformité des employés aux politiques.", "pillar": "Traçabilité", "risk_treatment_strategy": "Transférer le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.268Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.268Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c0da"}, "measure_description": "S'appuyer sur des auditeurs externes pour valider l'efficacité des mécanismes de journalisation.", "pillar": "Traçabilité", "risk_treatment_strategy": "Transférer le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.268Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.268Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c0db"}, "measure_description": "Accepter une traçabilité limitée sur les postes de travail utilisateur standard pour des raisons de confidentialité.", "pillar": "Traçabilité", "risk_treatment_strategy": "Accepter le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.269Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.269Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c0dc"}, "measure_description": "Accepter le manque de journalisation détaillée pour les sites web d'information publique à faible risque.", "pillar": "Traçabilité", "risk_treatment_strategy": "Accepter le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.269Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.269Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c0dd"}, "measure_description": "Accepter des périodes de conservation des journaux réduites pour les systèmes non critiques afin de gérer les coûts.", "pillar": "Traçabilité", "risk_treatment_strategy": "Accepter le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.269Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.269Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c0de"}, "measure_description": "Accepter formellement le manque de traçabilité pour des systèmes hérités spécifiques en fin de vie.", "pillar": "Traçabilité", "risk_treatment_strategy": "Accepter le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.269Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.269Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c0df"}, "measure_description": "Accepter que les journaux ne capturent pas nécessairement l'intention de l'utilisateur, mais seulement les actions effectuées.", "pillar": "Traçabilité", "risk_treatment_strategy": "Accepter le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.269Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.269Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c0e0"}, "measure_description": "<PERSON><PERSON><PERSON><PERSON> de ne pas effectuer de capture coûteuse de paquets réseau à haut volume pour le trafic de routine.", "pillar": "Traçabilité", "risk_treatment_strategy": "Accepter le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.269Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.269Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c0e1"}, "measure_description": "Accepter les limitations dans la corrélation d'événements entre systèmes ayant des horloges non synchronisées.", "pillar": "Traçabilité", "risk_treatment_strategy": "Accepter le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.269Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.269Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c0e2"}, "measure_description": "Reconnaître que des attaquants déterminés peuvent tenter d'échapper aux journaux ou de les falsifier.", "pillar": "Traçabilité", "risk_treatment_strategy": "Accepter le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.269Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.269Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c0e3"}, "measure_description": "Accepter les lacunes dans le suivi de l'activité utilisateur entre les appareils personnels et d'entreprise.", "pillar": "Traçabilité", "risk_treatment_strategy": "Accepter le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.269Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.269Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c0e4"}, "measure_description": "Accepter formellement que les journaux de certains services tiers puissent être insuffisants.", "pillar": "Traçabilité", "risk_treatment_strategy": "Accepter le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.269Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.269Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c0e5"}, "measure_description": "Mettre en œuvre des mécanismes de journalisation infalsifiables / à preuve d'altération (par ex., ajout sécurisé uniquement, hachage).", "pillar": "<PERSON><PERSON>", "risk_treatment_strategy": "Ré<PERSON><PERSON> le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.269Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.269Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c0e6"}, "measure_description": "Utiliser des signatures numériques pour vérifier l'origine et l'intégrité des communications.", "pillar": "<PERSON><PERSON>", "risk_treatment_strategy": "Ré<PERSON><PERSON> le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.269Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.269Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c0e7"}, "measure_description": "Maintenir une chaîne de possession (chain of custody) stricte pour toutes les preuves numériques collectées.", "pillar": "<PERSON><PERSON>", "risk_treatment_strategy": "Ré<PERSON><PERSON> le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.269Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.269Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c0e8"}, "measure_description": "Employer le hachage cryptographique pour assurer l'intégrité des fichiers de preuves stockés.", "pillar": "<PERSON><PERSON>", "risk_treatment_strategy": "Ré<PERSON><PERSON> le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.269Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.269Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c0e9"}, "measure_description": "Enregistrer méticuleusement toutes les actions, décisions et constatations de la réponse aux incidents.", "pillar": "<PERSON><PERSON>", "risk_treatment_strategy": "Ré<PERSON><PERSON> le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.269Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.269Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c0ea"}, "measure_description": "Utiliser des services d'horodatage sécurisé pour les entrées de journal et transactions critiques.", "pillar": "<PERSON><PERSON>", "risk_treatment_strategy": "Ré<PERSON><PERSON> le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.269Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.269Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c0eb"}, "measure_description": "Mettre en œuvre des contrôles de non-répudiation pour les transactions critiques.", "pillar": "<PERSON><PERSON>", "risk_treatment_strategy": "Ré<PERSON><PERSON> le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.269Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.269Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c0ec"}, "measure_description": "Capturer correctement des images forensiques (légales) des systèmes impliqués dans les incidents de sécurité.", "pillar": "<PERSON><PERSON>", "risk_treatment_strategy": "Ré<PERSON><PERSON> le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.269Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.269Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c0ed"}, "measure_description": "S'assurer que les données des journaux incluent suffisamment de détails pour l'analyse forensique (légale).", "pillar": "<PERSON><PERSON>", "risk_treatment_strategy": "Ré<PERSON><PERSON> le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.269Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.269Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c0ee"}, "measure_description": "Corroborer les preuves numériques avec d'autres sources (journaux physiques, déclarations de témoins).", "pillar": "<PERSON><PERSON>", "risk_treatment_strategy": "Ré<PERSON><PERSON> le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.269Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.269Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c0ef"}, "measure_description": "<PERSON><PERSON><PERSON> de supprimer les journaux critiques avant l'expiration de la période de conservation obligatoire.", "pillar": "<PERSON><PERSON>", "risk_treatment_strategy": "<PERSON><PERSON><PERSON> le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.269Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.269Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c0f0"}, "measure_description": "Ne pas autoriser la modification des journaux de preuves originaux (utiliser WORM ou des copies sécurisées).", "pillar": "<PERSON><PERSON>", "risk_treatment_strategy": "<PERSON><PERSON><PERSON> le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.269Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.269Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c0f1"}, "measure_description": "Prévenir les actions qui pourraient contaminer les preuves lors de la réponse aux incidents (par ex., redémarrage prématuré).", "pillar": "<PERSON><PERSON>", "risk_treatment_strategy": "<PERSON><PERSON><PERSON> le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.269Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.269Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c0f2"}, "measure_description": "<PERSON><PERSON><PERSON> d'utiliser des systèmes ou outils ne pouvant produire de pistes de preuves vérifiables.", "pillar": "<PERSON><PERSON>", "risk_treatment_strategy": "<PERSON><PERSON><PERSON> le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.269Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.269Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c0f3"}, "measure_description": "Ne pas se fier uniquement à la mémoire volatile pour les preuves critiques.", "pillar": "<PERSON><PERSON>", "risk_treatment_strategy": "<PERSON><PERSON><PERSON> le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.269Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.269Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c0f4"}, "measure_description": "Interdire les changements non documentés ou non autorisés aux configurations système pertinentes pour les preuves.", "pillar": "<PERSON><PERSON>", "risk_treatment_strategy": "<PERSON><PERSON><PERSON> le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.269Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.269Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c0f5"}, "measure_description": "<PERSON><PERSON><PERSON> <PERSON>er les données d'enquête sensibles avec les données opérationnelles normales.", "pillar": "<PERSON><PERSON>", "risk_treatment_strategy": "<PERSON><PERSON><PERSON> le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.269Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.269Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c0f6"}, "measure_description": "Ne pas stocker de preuves critiques sur des partages réseau facilement accessibles ou modifiables.", "pillar": "<PERSON><PERSON>", "risk_treatment_strategy": "<PERSON><PERSON><PERSON> le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.270Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.270Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c0f7"}, "measure_description": "Éviter d'interrompre les obligations légales de conservation des données (litigation holds).", "pillar": "<PERSON><PERSON>", "risk_treatment_strategy": "<PERSON><PERSON><PERSON> le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.270Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.270Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c0f8"}, "measure_description": "Empêcher le personnel non formé de manipuler des preuves numériques potentielles.", "pillar": "<PERSON><PERSON>", "risk_treatment_strategy": "<PERSON><PERSON><PERSON> le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.270Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.270Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c0f9"}, "measure_description": "Utiliser une autorité d'horodatage tierce de confiance pour des enregistrements temporels vérifiables.", "pillar": "<PERSON><PERSON>", "risk_treatment_strategy": "Transférer le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.270Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.270Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c0fa"}, "measure_description": "Externaliser les enquêtes de criminalistique numérique (forensique) à des entreprises spécialisées certifiées.", "pillar": "<PERSON><PERSON>", "risk_treatment_strategy": "Transférer le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.270Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.270Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c0fb"}, "measure_description": "S'appuyer sur les journaux et attestations du fournisseur cloud comme preuves (vérifier les termes contractuels).", "pillar": "<PERSON><PERSON>", "risk_treatment_strategy": "Transférer le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.270Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.270Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c0fc"}, "measure_description": "Engager des services tiers d'e-discovery (découverte électronique) pour les procédures judiciaires.", "pillar": "<PERSON><PERSON>", "risk_treatment_strategy": "Transférer le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.270Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.270Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c0fd"}, "measure_description": "Stocker les sauvegardes ou archives de preuves critiques auprès d'un dépositaire tiers sécurisé.", "pillar": "<PERSON><PERSON>", "risk_treatment_strategy": "Transférer le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.270Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.270Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c0fe"}, "measure_description": "Utiliser une Infrastructure à Clés Publiques (PKI) gérée de manière externe pour les signatures numériques.", "pillar": "<PERSON><PERSON>", "risk_treatment_strategy": "Transférer le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.270Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.270Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c0ff"}, "measure_description": "S'appuyer sur les rapports d'auditeurs indépendants comme preuve de la mise en œuvre des contrôles.", "pillar": "<PERSON><PERSON>", "risk_treatment_strategy": "Transférer le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.270Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.270Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c100"}, "measure_description": "Utiliser des services d'attestation tiers (par ex., rapports SOC) comme preuve de conformité.", "pillar": "<PERSON><PERSON>", "risk_treatment_strategy": "Transférer le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.270Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.270Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c101"}, "measure_description": "Employer un conseiller juridique externe pour valider les procédures de collecte de preuves.", "pillar": "<PERSON><PERSON>", "risk_treatment_strategy": "Transférer le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.270Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.270Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c102"}, "measure_description": "Utiliser des services basés sur la blockchain pour une preuve de transaction immuable, le cas échéant.", "pillar": "<PERSON><PERSON>", "risk_treatment_strategy": "Transférer le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.270Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.270Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c103"}, "measure_description": "Accepter une non-répudiation plus faible pour les communications par e-mail internes à faible risque.", "pillar": "<PERSON><PERSON>", "risk_treatment_strategy": "Accepter le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.270Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.270Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c104"}, "measure_description": "Accepter le manque de preuve définitive pour les violations mineures de politique traitées de manière informelle.", "pillar": "<PERSON><PERSON>", "risk_treatment_strategy": "Accepter le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.270Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.270Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c105"}, "measure_description": "Reconnaître que les preuves récupérées de certains systèmes hérités peuvent être moins fiables.", "pillar": "<PERSON><PERSON>", "risk_treatment_strategy": "Accepter le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.270Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.270Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c106"}, "measure_description": "Accepter les limitations pour prouver définitivement l'intention de l'utilisateur par rapport à une action accidentelle.", "pillar": "<PERSON><PERSON>", "risk_treatment_strategy": "Accepter le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.270Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.270Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c107"}, "measure_description": "Accepter que les données volatiles (RAM) puissent être perdues si elles ne sont pas capturées immédiatement.", "pillar": "<PERSON><PERSON>", "risk_treatment_strategy": "Accepter le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.270Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.270Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c108"}, "measure_description": "<PERSON><PERSON><PERSON><PERSON> de ne pas utiliser d'outils forensiques coûteux pour tous les événements de sécurité mineurs.", "pillar": "<PERSON><PERSON>", "risk_treatment_strategy": "Accepter le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.270Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.270Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c109"}, "measure_description": "Accepter qu'une chaîne de possession parfaite puisse être difficile à établir pour les appareils gérés par l'utilisateur.", "pillar": "<PERSON><PERSON>", "risk_treatment_strategy": "Accepter le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.270Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.270Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c10a"}, "measure_description": "Accepter formellement que les horodatages des journaux puissent présenter des dérives mineures si le NTP échoue brièvement.", "pillar": "<PERSON><PERSON>", "risk_treatment_strategy": "Accepter le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.270Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.270Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c10b"}, "measure_description": "Reconnaître que les documents signés numériquement pourraient faire face à des contestations juridiques.", "pillar": "<PERSON><PERSON>", "risk_treatment_strategy": "Accepter le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.270Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.270Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c10c"}, "measure_description": "Accepter le risque que les données chiffrées soient irrécupérables sans les clés, limitant ainsi les preuves.", "pillar": "<PERSON><PERSON>", "risk_treatment_strategy": "Accepter le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.270Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.270Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c10d"}, "measure_description": "S'assurer que les journaux d'audit sont complets, précis et protégés contre la falsification.", "pillar": "Auditabilité", "risk_treatment_strategy": "Ré<PERSON><PERSON> le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.270Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.270Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c10e"}, "measure_description": "Mettre en œuvre des rôles d'accès spécifiques en lecture seule pour les auditeurs internes et externes.", "pillar": "Auditabilité", "risk_treatment_strategy": "Ré<PERSON><PERSON> le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.270Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.270Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c10f"}, "measure_description": "Maintenir une documentation à jour de l'architecture système, des flux de données et des politiques.", "pillar": "Auditabilité", "risk_treatment_strategy": "Ré<PERSON><PERSON> le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.270Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.270Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c110"}, "measure_description": "Mener des audits de sécurité internes réguliers basés sur des référentiels établis (ISO 27001, NIST).", "pillar": "Auditabilité", "risk_treatment_strategy": "Ré<PERSON><PERSON> le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.270Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.270Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c111"}, "measure_description": "Faciliter les audits externes périodiques par des tiers indépendants.", "pillar": "Auditabilité", "risk_treatment_strategy": "Ré<PERSON><PERSON> le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.270Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.270Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c112"}, "measure_description": "Utiliser des outils de Gouvernance, Risque et Conformité (GRC) pour gérer les preuves d'audit.", "pillar": "Auditabilité", "risk_treatment_strategy": "Ré<PERSON><PERSON> le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.270Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.270Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c113"}, "measure_description": "S'assurer que les pistes d'audit capturent suffisamment de détails (qui, quoi, quand, où, succès/échec).", "pillar": "Auditabilité", "risk_treatment_strategy": "Ré<PERSON><PERSON> le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.270Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.270Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c114"}, "measure_description": "Mettre en œuvre des processus pour suivre et remédier rapidement aux constatations d'audit.", "pillar": "Auditabilité", "risk_treatment_strategy": "Ré<PERSON><PERSON> le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.270Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.270Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c115"}, "measure_description": "Standardiser les configurations système pour simplifier les processus d'audit.", "pillar": "Auditabilité", "risk_treatment_strategy": "Ré<PERSON><PERSON> le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.270Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.270Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c116"}, "measure_description": "Fournir aux auditeurs les outils et la formation nécessaires pour accéder aux données d'audit.", "pillar": "Auditabilité", "risk_treatment_strategy": "Ré<PERSON><PERSON> le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.270Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.270Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c117"}, "measure_description": "É<PERSON>ter les conceptions de systèmes qui manquent intrinsèquement de capacités d'audit ou de transparence.", "pillar": "Auditabilité", "risk_treatment_strategy": "<PERSON><PERSON><PERSON> le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.270Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.270Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c118"}, "measure_description": "Ne pas désactiver les pistes d'audit ou la journalisation nécessaires requises pour la conformité.", "pillar": "Auditabilité", "risk_treatment_strategy": "<PERSON><PERSON><PERSON> le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.270Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.270Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c119"}, "measure_description": "Éviter d'utiliser des technologies ou des services tiers sans droits d'audit ou rapports.", "pillar": "Auditabilité", "risk_treatment_strategy": "<PERSON><PERSON><PERSON> le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.270Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.270Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c11a"}, "measure_description": "Interdire les exceptions non documentées aux politiques de sécurité qui entravent les audits.", "pillar": "Auditabilité", "risk_treatment_strategy": "<PERSON><PERSON><PERSON> le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.270Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.270Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c11b"}, "measure_description": "<PERSON><PERSON><PERSON> d'accorder des permissions système excessives qui contournent les contrôles d'audit.", "pillar": "Auditabilité", "risk_treatment_strategy": "<PERSON><PERSON><PERSON> le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.270Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.270Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c11c"}, "measure_description": "Ne pas autoriser la destruction des enregistrements d'audit avant la période de conservation requise.", "pillar": "Auditabilité", "risk_treatment_strategy": "<PERSON><PERSON><PERSON> le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.270Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.270Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c11d"}, "measure_description": "É<PERSON>ter les processus manuels là où des flux de travail automatisés et auditables peuvent être utilisés.", "pillar": "Auditabilité", "risk_treatment_strategy": "<PERSON><PERSON><PERSON> le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.270Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.270Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c11e"}, "measure_description": "Empêcher le déploiement de systèmes sans exigences d'audit définies et sans tests.", "pillar": "Auditabilité", "risk_treatment_strategy": "<PERSON><PERSON><PERSON> le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.270Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.270Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c11f"}, "measure_description": "É<PERSON>iner les comptes administratifs partagés qui obscurcissent la responsabilité individuelle pour les audits.", "pillar": "Auditabilité", "risk_treatment_strategy": "<PERSON><PERSON><PERSON> le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.270Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.270Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c120"}, "measure_description": "Ne pas entraver l'accès des auditeurs au personnel ou à la documentation nécessaires.", "pillar": "Auditabilité", "risk_treatment_strategy": "<PERSON><PERSON><PERSON> le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.270Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.270Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c121"}, "measure_description": "Engager des cabinets d'audit externes indépendants pour effectuer des évaluations périodiques.", "pillar": "Auditabilité", "risk_treatment_strategy": "Transférer le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.270Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.270Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c122"}, "measure_description": "Utiliser des fournisseurs cloud certifiés selon des normes reconnues (par ex., SOC 2, ISO 27001).", "pillar": "Auditabilité", "risk_treatment_strategy": "Transférer le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.271Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.271Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c123"}, "measure_description": "S'appuyer sur les rapports d'audit et les certifications fournis par les fournisseurs SaaS.", "pillar": "Auditabilité", "risk_treatment_strategy": "Transférer le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.271Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.271Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c124"}, "measure_description": "Externaliser la fonction d'audit interne (totalement ou partiellement) à une entreprise spécialisée.", "pillar": "Auditabilité", "risk_treatment_strategy": "Transférer le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.271Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.271Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c125"}, "measure_description": "Engager des consultants tiers pour évaluer la conformité à des réglementations spécifiques (PCI DSS, RGPD).", "pillar": "Auditabilité", "risk_treatment_strategy": "Transférer le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.271Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.271Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c126"}, "measure_description": "Utiliser des services gérés où le fournisseur assume la responsabilité des opérations auditables.", "pillar": "Auditabilité", "risk_treatment_strategy": "Transférer le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.271Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.271Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c127"}, "measure_description": "S'appuyer sur un conseiller juridique externe pour interpréter les exigences d'audit dans les réglementations.", "pillar": "Auditabilité", "risk_treatment_strategy": "Transférer le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.271Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.271Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c128"}, "measure_description": "Utiliser des plateformes GRC tierces qui facilitent la collecte de preuves d'audit auprès des fournisseurs.", "pillar": "Auditabilité", "risk_treatment_strategy": "Transférer le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.271Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.271Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c129"}, "measure_description": "Exiger contractuellement des fournisseurs de services qu'ils accordent des droits d'audit.", "pillar": "Auditabilité", "risk_treatment_strategy": "Transférer le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.271Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.271Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c12a"}, "measure_description": "Souscrire une cyberassurance qui peut exiger des audits comme condition.", "pillar": "Auditabilité", "risk_treatment_strategy": "Transférer le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.271Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.271Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c12b"}, "measure_description": "Accepter une auditabilité réduite pour les environnements de développement temporaires ou sandbox (bac à sable).", "pillar": "Auditabilité", "risk_treatment_strategy": "Accepter le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.271Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.271Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c12c"}, "measure_description": "Reconnaître les limitations dans l'audit des composants logiciels tiers \"boîte noire\".", "pillar": "Auditabilité", "risk_treatment_strategy": "Accepter le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.271Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.271Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c12d"}, "measure_description": "Accepter des délais plus longs pour récupérer les données d'audit depuis des archives hors ligne.", "pillar": "Auditabilité", "risk_treatment_strategy": "Accepter le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.271Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.271Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c12e"}, "measure_description": "Accepter formellement que le coût d'une auditabilité complète ne soit pas justifié pour certains actifs à faible risque.", "pillar": "Auditabilité", "risk_treatment_strategy": "Accepter le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.271Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.271Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c12f"}, "measure_description": "Accepter une visibilité limitée sur les contrôles internes de certains fournisseurs en amont.", "pillar": "Auditabilité", "risk_treatment_strategy": "Accepter le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.271Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.271Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c130"}, "measure_description": "<PERSON><PERSON><PERSON><PERSON> de ne pas effectuer d'audits intrusifs sur des systèmes où cela pourrait causer de l'instabilité.", "pillar": "Auditabilité", "risk_treatment_strategy": "Accepter le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.271Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.271Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c131"}, "measure_description": "Accepter que les preuves d'audit pour les systèmes hérités puissent être incomplètes ou non standard.", "pillar": "Auditabilité", "risk_treatment_strategy": "Accepter le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.271Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.271Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c132"}, "measure_description": "Reconnaître la difficulté d'auditer les canaux de communication informels (par ex., conversations de couloir).", "pillar": "Auditabilité", "risk_treatment_strategy": "Accepter le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.271Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.271Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c133"}, "measure_description": "Accepter le risque résiduel que les audits ne détectent pas toutes les non-conformités ou fraudes.", "pillar": "Auditabilité", "risk_treatment_strategy": "Accepter le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.271Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.271Z"}}, {"_id": {"$oid": "680575d33fca057a8d18c134"}, "measure_description": "Accepter formellement les limitations de l'audit de l'adhésion des employés aux politiques d'utilisation acceptable.", "pillar": "Auditabilité", "risk_treatment_strategy": "Accepter le risque", "__v": 0, "createdAt": {"$date": "2025-04-20T22:31:47.271Z"}, "updatedAt": {"$date": "2025-04-20T22:31:47.271Z"}}]