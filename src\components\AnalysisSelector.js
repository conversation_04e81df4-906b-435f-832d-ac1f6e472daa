import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useAnalysis } from '../context/AnalysisContext';
import { useAuth } from '../context/AuthContext';
import {
  Plus,
  FileText,
  FolderOpen,
  RefreshCw,
  Clock,
  Edit2,
  Trash2,
  Calendar,
  BarChart,
  ChevronDown,
  ChevronRight,
  Briefcase
} from 'lucide-react';

/**
 * Enhanced Analysis Selector with history and detailed information
 */
const AnalysisSelector = () => {
  const { t, i18n } = useTranslation();
  const {
    currentAnalysis,
    analyses,
    isLoading,
    selectAnalysis,
    createAnalysis,
    // updateAnalysis, // Unused
    deleteAnalysis,
    loadAnalyses
  } = useAnalysis();

  const { user } = useAuth();

  // Local state
  const [showCreate, setShowCreate] = useState(false);
  const [showDetails, setShowDetails] = useState(false);
  const [newAnalysisName, setNewAnalysisName] = useState('');
  const [newAnalysisDescription, setNewAnalysisDescription] = useState('');
  const [expandedAnalysis, setExpandedAnalysis] = useState(null);
  const [deleteConfirmation, setDeleteConfirmation] = useState(null);
  const [localLoading, setLocalLoading] = useState(false);

  // Load analyses on component mount
  useEffect(() => {
    if (!analyses.length) {
      loadAnalyses();
    }
  }, []);

  const handleCreate = async () => {
    if (!newAnalysisName.trim()) return;

    // Ensure we have company information from the user
    if (!user || !user.companyId || !user.companyName) {
      alert(t('analysisSelector.alerts.mustBeConnected'));
      return;
    }

    setLocalLoading(true);

    try {
      const result = await createAnalysis({
        name: newAnalysisName,
        description: newAnalysisDescription,
        status: 'draft',
        companyId: user.companyId,
        companyName: user.companyName
      });

      if (result) {
        // Reset form
        setNewAnalysisName('');
        setNewAnalysisDescription('');
        setShowCreate(false);

        // Select the newly created analysis
        selectAnalysis(result.id);
      }
    } catch (error) {
      console.error('Error creating analysis:', error);
    } finally {
      setLocalLoading(false);
    }
  };

  const handleDeleteConfirm = async (id) => {
    setLocalLoading(true);
    try {
      await deleteAnalysis(id);
    } finally {
      setLocalLoading(false);
      setDeleteConfirmation(null);
    }
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';

    const date = new Date(dateString);
    const locale = i18n.language === 'en' ? 'en-US' : 'fr-FR';
    return date.toLocaleDateString(locale, {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  // Determine if loading state should be shown
  const showLoading = isLoading || localLoading;

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-6">
      <div className="flex justify-between items-center mb-4">
        <h3 className="font-medium text-gray-800 flex items-center">
          <FileText size={18} className="mr-2 text-blue-500" />
          {t('analysisSelector.title')}
        </h3>

        <div className="flex space-x-2">
          <button
            className={`px-3 py-1 rounded-md text-sm font-medium ${
              showCreate ? 'bg-gray-200 text-gray-700' : 'bg-blue-500 text-white hover:bg-blue-600'
            }`}
            onClick={() => setShowCreate(!showCreate)}
            disabled={showLoading}
          >
            {showCreate ? t('analysisSelector.cancel') : t('analysisSelector.newAnalysis')}
          </button>

          <button
            className="px-3 py-1 rounded-md text-sm font-medium bg-gray-100 text-gray-700 hover:bg-gray-200"
            onClick={loadAnalyses}
            disabled={showLoading}
          >
            <RefreshCw size={16} className={`${showLoading ? 'animate-spin' : ''}`} />
          </button>
        </div>
      </div>

      {showCreate && (
        <div className="mb-4 p-4 bg-blue-50 rounded-lg border border-blue-100">
          <h4 className="font-medium text-blue-800 mb-3">{t('analysisSelector.newAnalysis')}</h4>

          <div className="space-y-3">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                {t('analysisSelector.analysisName')}
              </label>
              <input
                type="text"
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                value={newAnalysisName}
                onChange={(e) => setNewAnalysisName(e.target.value)}
                placeholder={t('analysisSelector.analysisNamePlaceholder')}
                disabled={showLoading}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                {t('analysisSelector.optionalDescription')}
              </label>
              <textarea
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                value={newAnalysisDescription}
                onChange={(e) => setNewAnalysisDescription(e.target.value)}
                placeholder={t('analysisSelector.descriptionPlaceholder')}
                rows={2}
                disabled={showLoading}
              />
            </div>

            <div className="flex justify-end">
              <button
                className={`px-4 py-2 rounded-md text-sm font-medium ${
                  !newAnalysisName.trim() || showLoading
                    ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                    : 'bg-green-500 text-white hover:bg-green-600'
                }`}
                onClick={handleCreate}
                disabled={!newAnalysisName.trim() || showLoading}
              >
                {showLoading ? (
                  <span className="flex items-center">
                    <RefreshCw size={16} className="animate-spin mr-2" />
                    {t('analysisSelector.creating')}
                  </span>
                ) : (
                  <>
                    <Plus size={16} className="mr-1 inline" />
                    {t('analysisSelector.createAnalysis')}
                  </>
                )}
              </button>
            </div>
          </div>
        </div>
      )}

      <div className="mb-2">
        <h4 className="text-sm font-medium text-gray-700 flex items-center mb-2">
          <FolderOpen size={16} className="mr-1 text-gray-500" />
          {t('analysisSelector.availableAnalyses')}
          {showLoading && <RefreshCw size={14} className="ml-2 animate-spin text-blue-500" />}
        </h4>

        {analyses.length > 0 ? (
          <div className="space-y-2 max-h-96 overflow-y-auto">
            {analyses.map((analysis) => (
              <div
                key={analysis.id}
                className={`border rounded-md ${
                  currentAnalysis?.id === analysis.id
                    ? 'border-blue-300 bg-blue-50'
                    : 'border-gray-200 hover:border-blue-200 hover:bg-blue-50/20'
                } transition-colors duration-200`}
              >
                <div className="px-3 py-2 flex justify-between items-center">
                  <div className="flex items-center">
                    <button
                      className="mr-2 focus:outline-none text-gray-500 hover:text-gray-700"
                      onClick={() => setExpandedAnalysis(expandedAnalysis === analysis.id ? null : analysis.id)}
                      disabled={showLoading}
                    >
                      {expandedAnalysis === analysis.id ?
                        <ChevronDown size={16} /> :
                        <ChevronRight size={16} />
                      }
                    </button>
                    <div>
                      <div
                        className={`font-medium flex items-center cursor-pointer ${
                          currentAnalysis?.id === analysis.id ? 'text-blue-700' : 'text-gray-800'
                        }`}
                        onClick={() => {
                          if (!showLoading && currentAnalysis?.id !== analysis.id) {
                            selectAnalysis(analysis.id);
                          }
                        }}
                      >
                        {analysis.name}
                        {analysis.status === 'in-progress' && (
                          <span className="ml-2 px-2 py-0.5 text-xs font-normal bg-blue-100 text-blue-800 rounded-full">
                            {t('analysisSelector.status.inProgress')}
                          </span>
                        )}
                        {analysis.status === 'completed' && (
                          <span className="ml-2 px-2 py-0.5 text-xs font-normal bg-green-100 text-green-800 rounded-full">
                            {t('analysisSelector.status.completed')}
                          </span>
                        )}
                      </div>
                      <div className="text-xs text-gray-500 flex items-center mt-0.5">
                        <Calendar size={10} className="mr-1" />
                        {t('analysisSelector.modifiedOn')} {formatDate(analysis.updatedAt)}
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center space-x-2">
                    <button
                      className={`px-2 py-1 rounded text-sm ${
                        showLoading ?
                          'bg-gray-300 text-gray-500 cursor-not-allowed' :
                          (currentAnalysis?.id === analysis.id
                            ? 'bg-blue-500 text-white'
                            : 'bg-blue-100 text-blue-700 hover:bg-blue-200')
                      }`}
                      onClick={() => {
                        if (!showLoading && currentAnalysis?.id !== analysis.id) {
                          selectAnalysis(analysis.id);
                        }
                      }}
                      disabled={showLoading || currentAnalysis?.id === analysis.id}
                    >
                      {showLoading && currentAnalysis?.id !== analysis.id ?
                        <span className="flex items-center">
                          <RefreshCw size={12} className="animate-spin mr-1" />
                          {t('analysisSelector.loading')}
                        </span> :
                        (currentAnalysis?.id === analysis.id ? t('analysisSelector.selected') : t('analysisSelector.select'))
                      }
                    </button>
                  </div>
                </div>

                {expandedAnalysis === analysis.id && (
                  <div className="bg-gray-50 border-t border-gray-200 px-3 py-2">
                    <p className="text-sm text-gray-700 mb-2">
                      {analysis.description || <span className="italic text-gray-500">{t('analysisSelector.noDescription')}</span>}
                    </p>

                    <div className="flex items-center text-xs text-gray-500 mb-2 gap-3">
                      <div className="flex items-center">
                        <Clock size={12} className="mr-1" />
                        {t('analysisSelector.createdOn')} {formatDate(analysis.createdAt)}
                      </div>
                      <div>
                        {t('analysisSelector.by')}: {analysis.createdByName || t('analysisSelector.unknownUser')}
                      </div>
                    </div>

                    <div className="flex justify-between mt-2">
                      <button
                        className="text-xs bg-blue-100 hover:bg-blue-200 text-blue-700 px-2 py-1 rounded flex items-center"
                        onClick={() => {
                          if (!showLoading && currentAnalysis?.id !== analysis.id) {
                            selectAnalysis(analysis.id);
                          }
                        }}
                        disabled={showLoading || currentAnalysis?.id === analysis.id}
                      >
                        <BarChart size={12} className="mr-1" />
                        {t('analysisSelector.viewDetails')}
                      </button>

                      <div className="flex gap-2">
                        <button
                          className="text-xs bg-gray-100 hover:bg-gray-200 text-gray-700 px-2 py-1 rounded flex items-center"
                          onClick={() => {
                            // Open the edit form (implementation to be added)
                            alert(t('analysisSelector.alerts.editFeatureNotImplemented'));
                          }}
                          disabled={showLoading}
                        >
                          <Edit2 size={12} className="mr-1" />
                          {t('analysisSelector.edit')}
                        </button>

                        {deleteConfirmation === analysis.id ? (
                          <div className="flex items-center gap-1">
                            <button
                              className="text-xs bg-red-500 hover:bg-red-600 text-white px-2 py-1 rounded"
                              onClick={() => handleDeleteConfirm(analysis.id)}
                              disabled={showLoading}
                            >
                              {showLoading ? t('analysisSelector.deleting') : t('analysisSelector.confirm')}
                            </button>
                            <button
                              className="text-xs bg-gray-200 hover:bg-gray-300 text-gray-700 px-2 py-1 rounded"
                              onClick={() => setDeleteConfirmation(null)}
                              disabled={showLoading}
                            >
                              {t('analysisSelector.cancel')}
                            </button>
                          </div>
                        ) : (
                          <button
                            className="text-xs bg-red-100 hover:bg-red-200 text-red-700 px-2 py-1 rounded flex items-center"
                            onClick={() => setDeleteConfirmation(analysis.id)}
                            disabled={showLoading}
                          >
                            <Trash2 size={12} className="mr-1" />
                            {t('analysisSelector.delete')}
                          </button>
                        )}
                      </div>
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        ) : (
          <div className="border border-dashed border-gray-300 rounded-lg p-6 text-center bg-gray-50">
            {showLoading ? (
              <div className="flex flex-col items-center justify-center">
                <RefreshCw size={24} className="animate-spin text-blue-500 mb-2" />
                <p className="text-gray-500">{t('analysisSelector.loadingAnalyses')}</p>
              </div>
            ) : (
              <div className="flex flex-col items-center justify-center">
                <FolderOpen size={24} className="text-gray-400 mb-2" />
                <p className="text-gray-500 mb-1">{t('analysisSelector.noAnalysesAvailable')}</p>
                <p className="text-sm text-gray-400">{t('analysisSelector.createFirstAnalysis')}</p>
              </div>
            )}
          </div>
        )}
      </div>

      {currentAnalysis && (
        <div className="mt-4 pt-4 border-t border-gray-200">
          <div className="flex justify-between items-center bg-gradient-to-r from-blue-600 to-indigo-600 text-white p-3 rounded-t-lg shadow-sm mb-0.5">
            <h4 className="font-medium flex items-center">
              <BarChart size={16} className="mr-2" />
              {t('analysisSelector.selectedAnalysis')}
            </h4>
            <button
              className="text-xs bg-white/20 hover:bg-white/30 px-2 py-1 rounded flex items-center transition-colors"
              onClick={() => setShowDetails(!showDetails)}
              disabled={showLoading}
            >
              {showDetails ? t('analysisSelector.hideDetails') : t('analysisSelector.showDetails')}
              {showDetails ? <ChevronDown size={14} className="ml-1" /> : <ChevronRight size={14} className="ml-1" />}
            </button>
          </div>

          <div className="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg p-4 shadow-sm">
            <div className="font-medium text-blue-800 text-lg flex items-center">
              <FileText size={18} className="text-blue-600 mr-2" />
              {currentAnalysis.name}
            </div>

            {showDetails && (
              <div className="mt-2 text-sm">
                <div className="bg-white p-3 rounded-md border border-blue-100 shadow-sm mb-3">
                  <div className="flex items-start">
                    <FolderOpen size={16} className="text-blue-500 mr-2 mt-0.5" />
                    <div>
                      <span className="font-medium text-gray-700 block mb-1">{t('analysisSelector.description')}</span>
                      <p className="text-gray-600">
                        {currentAnalysis.description || <span className="italic text-gray-400">{t('analysisSelector.noDescription')}</span>}
                      </p>
                    </div>
                  </div>
                </div>

                <div className="mt-3 grid grid-cols-2 gap-3 text-xs bg-white p-3 rounded-md border border-blue-100 shadow-sm">
                  <div className="flex items-center bg-blue-50 p-2 rounded-md">
                    <Calendar size={14} className="text-blue-500 mr-2" />
                    <div>
                      <span className="font-medium text-gray-700 block">{t('analysisSelector.createdOn')}</span>
                      <span className="text-blue-700">{formatDate(currentAnalysis.createdAt)}</span>
                    </div>
                  </div>

                  <div className="flex items-center bg-blue-50 p-2 rounded-md">
                    <RefreshCw size={14} className="text-blue-500 mr-2" />
                    <div>
                      <span className="font-medium text-gray-700 block">{t('analysisSelector.modifiedOn')}</span>
                      <span className="text-blue-700">{formatDate(currentAnalysis.updatedAt)}</span>
                    </div>
                  </div>

                  {currentAnalysis.companyName && (
                    <div className="flex items-center bg-blue-50 p-2 rounded-md col-span-2">
                      <Briefcase size={14} className="text-blue-500 mr-2" />
                      <div>
                        <span className="font-medium text-gray-700 block">{t('analysisSelector.company')}</span>
                        <span className="text-blue-700">{currentAnalysis.companyName}</span>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default AnalysisSelector;