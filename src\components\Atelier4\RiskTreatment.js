// src/components/Atelier4/RiskTreatment.js
import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Shield, Info, BookOpen, CheckCircle, XCircle, AlertTriangle, ArrowRight } from 'lucide-react';
import { useAnalysis } from '../../context/AnalysisContext';

const RiskTreatment = () => {
  const [operationalScenarios, setOperationalScenarios] = useState([]);
  const [treatmentDecisions, setTreatmentDecisions] = useState({});
  const [loading, setLoading] = useState(false);
  const { currentAnalysis } = useAnalysis();

  // Treatment options
  const treatmentOptions = [
    {
      id: 'accept',
      name: 'Accepter',
      description: 'Accepter le risque en l\'état',
      icon: CheckCircle,
      color: 'text-green-600',
      bgColor: 'bg-green-50',
      borderColor: 'border-green-200'
    },
    {
      id: 'reduce',
      name: '<PERSON><PERSON><PERSON><PERSON>',
      description: 'Mettre en place des mesures de sécurité',
      icon: Shield,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
      borderColor: 'border-blue-200'
    },
    {
      id: 'transfer',
      name: 'Transférer',
      description: 'Transférer le risque (assurance, sous-traitance)',
      icon: ArrowRight,
      color: 'text-orange-600',
      bgColor: 'bg-orange-50',
      borderColor: 'border-orange-200'
    },
    {
      id: 'avoid',
      name: 'Éviter',
      description: 'Éviter l\'activité génératrice de risque',
      icon: XCircle,
      color: 'text-red-600',
      bgColor: 'bg-red-50',
      borderColor: 'border-red-200'
    }
  ];

  // Load operational scenarios
  useEffect(() => {
    const loadScenarios = async () => {
      if (currentAnalysis?.id) {
        setLoading(true);
        try {
          console.log('[RiskTreatment] Loading operational scenarios for analysis:', currentAnalysis.id);
          
          // Mock operational scenarios
          const mockScenarios = [
            {
              id: 'scenario1',
              name: 'Attaque par phishing ciblé',
              description: 'Scénario d\'attaque par email de phishing pour voler les identifiants',
              attackPathReference: 'CA01',
              severity: 'Élevé',
              likelihood: 'Moyenne',
              riskLevel: 'Élevé',
              steps: [
                { phase: 'CONNAITRE', name: 'Reconnaissance OSINT' },
                { phase: 'RENTRER', name: 'Phishing ciblé' },
                { phase: 'TROUVER', name: 'Élévation privilèges' },
                { phase: 'EXPLOITER', name: 'Exfiltration données' }
              ],
              impactedAssets: ['Serveur Web', 'Base de données'],
              estimatedCost: 'Élevé',
              detectionDifficulty: 'Moyenne'
            },
            {
              id: 'scenario2',
              name: 'Sabotage interne système',
              description: 'Scénario de sabotage par un employé mécontent',
              attackPathReference: 'CA02',
              severity: 'Très élevé',
              likelihood: 'Faible',
              riskLevel: 'Moyen',
              steps: [
                { phase: 'CONNAITRE', name: 'Reconnaissance interne' },
                { phase: 'RENTRER', name: 'Accès légitime' },
                { phase: 'TROUVER', name: 'Identification cibles' },
                { phase: 'EXPLOITER', name: 'Sabotage système' }
              ],
              impactedAssets: ['Système de production'],
              estimatedCost: 'Très élevé',
              detectionDifficulty: 'Difficile'
            }
          ];
          
          setOperationalScenarios(mockScenarios);
          
          // Initialize treatment decisions
          const initialDecisions = {};
          mockScenarios.forEach(scenario => {
            initialDecisions[scenario.id] = {
              treatment: null,
              justification: '',
              measures: [],
              responsible: '',
              deadline: '',
              budget: ''
            };
          });
          setTreatmentDecisions(initialDecisions);
          
        } catch (error) {
          console.error('[RiskTreatment] Error loading scenarios:', error);
          setOperationalScenarios([]);
        } finally {
          setLoading(false);
        }
      }
    };

    loadScenarios();
  }, [currentAnalysis?.id]);

  // Handle treatment decision change
  const handleTreatmentChange = (scenarioId, treatment) => {
    setTreatmentDecisions(prev => ({
      ...prev,
      [scenarioId]: {
        ...prev[scenarioId],
        treatment
      }
    }));
  };

  // Handle decision details change
  const handleDecisionDetailsChange = (scenarioId, field, value) => {
    setTreatmentDecisions(prev => ({
      ...prev,
      [scenarioId]: {
        ...prev[scenarioId],
        [field]: value
      }
    }));
  };

  // Get risk level color
  const getRiskLevelColor = (level) => {
    switch (level?.toLowerCase()) {
      case 'très élevé': return 'bg-red-100 text-red-800 border-red-200';
      case 'élevé': return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'moyen': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'faible': return 'bg-green-100 text-green-800 border-green-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  // Calculate treatment summary
  const getTreatmentSummary = () => {
    const summary = { accept: 0, reduce: 0, transfer: 0, avoid: 0, pending: 0 };
    Object.values(treatmentDecisions).forEach(decision => {
      if (decision.treatment) {
        summary[decision.treatment]++;
      } else {
        summary.pending++;
      }
    });
    return summary;
  };

  const treatmentSummary = getTreatmentSummary();

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-100">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-800 mb-2 flex items-center">
              <Shield size={28} className="mr-3 text-emerald-600" />
              Phase 5: Traitement des risques
            </h1>
            <p className="text-gray-600">
              Décision de traitement pour chaque scénario opérationnel identifié.
            </p>
          </div>
          <div className="flex items-center space-x-2 text-sm text-gray-500">
            <Info size={16} />
            <span>Accepter • Réduire • Transférer • Éviter</span>
          </div>
        </div>

        {/* Treatment Options Overview */}
        <div className="bg-emerald-50 p-4 rounded-lg border border-emerald-200">
          <h3 className="font-medium text-emerald-800 mb-3 flex items-center">
            <BookOpen size={16} className="mr-2" />
            Options de traitement des risques
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {treatmentOptions.map((option) => {
              const IconComponent = option.icon;
              return (
                <div key={option.id} className={`${option.bgColor} p-3 rounded border ${option.borderColor}`}>
                  <div className={`font-medium ${option.color} mb-1 flex items-center`}>
                    <IconComponent size={14} className="mr-1" />
                    {option.name}
                  </div>
                  <div className="text-gray-600 text-xs">{option.description}</div>
                </div>
              );
            })}
          </div>
        </div>
      </div>

      {/* Treatment Summary */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-white p-6 rounded-xl shadow-sm border border-gray-100"
      >
        <h3 className="text-lg font-medium text-gray-800 mb-4">Résumé des décisions</h3>
        
        <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
          {treatmentOptions.map((option) => (
            <div key={option.id} className={`${option.bgColor} p-4 rounded-lg border ${option.borderColor}`}>
              <div className="text-2xl font-bold text-gray-800">
                {treatmentSummary[option.id]}
              </div>
              <div className={`text-sm ${option.color}`}>{option.name}</div>
            </div>
          ))}
          <div className="bg-gray-50 p-4 rounded-lg border border-gray-200">
            <div className="text-2xl font-bold text-gray-800">
              {treatmentSummary.pending}
            </div>
            <div className="text-sm text-gray-600">En attente</div>
          </div>
        </div>
      </motion.div>

      {/* Scenarios Treatment */}
      <div className="space-y-4">
        {operationalScenarios.map((scenario) => {
          const decision = treatmentDecisions[scenario.id] || {};
          const selectedTreatment = treatmentOptions.find(opt => opt.id === decision.treatment);
          
          return (
            <motion.div
              key={scenario.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="bg-white p-6 rounded-xl shadow-sm border border-gray-100"
            >
              {/* Scenario Header */}
              <div className="flex items-center justify-between mb-4">
                <div>
                  <h3 className="text-lg font-medium text-gray-800">{scenario.name}</h3>
                  <p className="text-sm text-gray-600">{scenario.description}</p>
                </div>
                <div className="flex items-center space-x-2">
                  <span className="font-mono text-sm text-gray-500">{scenario.attackPathReference}</span>
                  <span className={`px-2 py-1 rounded text-xs font-medium border ${getRiskLevelColor(scenario.riskLevel)}`}>
                    {scenario.riskLevel}
                  </span>
                </div>
              </div>

              {/* Scenario Details */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4 text-sm">
                <div>
                  <span className="font-medium text-gray-700">Sévérité:</span>
                  <span className="ml-2">{scenario.severity}</span>
                </div>
                <div>
                  <span className="font-medium text-gray-700">Probabilité:</span>
                  <span className="ml-2">{scenario.likelihood}</span>
                </div>
                <div>
                  <span className="font-medium text-gray-700">Détection:</span>
                  <span className="ml-2">{scenario.detectionDifficulty}</span>
                </div>
              </div>

              {/* Treatment Selection */}
              <div className="border-t border-gray-200 pt-4">
                <h4 className="font-medium text-gray-800 mb-3">Décision de traitement</h4>
                
                <div className="grid grid-cols-1 md:grid-cols-4 gap-3 mb-4">
                  {treatmentOptions.map((option) => {
                    const IconComponent = option.icon;
                    const isSelected = decision.treatment === option.id;
                    
                    return (
                      <button
                        key={option.id}
                        onClick={() => handleTreatmentChange(scenario.id, option.id)}
                        className={`p-3 rounded-lg border transition-all ${
                          isSelected 
                            ? `${option.bgColor} ${option.borderColor} ${option.color}` 
                            : 'border-gray-200 hover:border-gray-300 text-gray-600'
                        }`}
                      >
                        <div className="flex items-center justify-center mb-1">
                          <IconComponent size={16} />
                        </div>
                        <div className="text-sm font-medium">{option.name}</div>
                      </button>
                    );
                  })}
                </div>

                {/* Treatment Details */}
                {decision.treatment && (
                  <motion.div
                    initial={{ opacity: 0, height: 0 }}
                    animate={{ opacity: 1, height: 'auto' }}
                    className="space-y-3"
                  >
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Justification
                      </label>
                      <textarea
                        value={decision.justification || ''}
                        onChange={(e) => handleDecisionDetailsChange(scenario.id, 'justification', e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                        rows={2}
                        placeholder="Justifiez votre décision de traitement..."
                      />
                    </div>
                    
                    {decision.treatment === 'reduce' && (
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            Responsable
                          </label>
                          <input
                            type="text"
                            value={decision.responsible || ''}
                            onChange={(e) => handleDecisionDetailsChange(scenario.id, 'responsible', e.target.value)}
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                            placeholder="Responsable de la mise en œuvre"
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            Échéance
                          </label>
                          <input
                            type="date"
                            value={decision.deadline || ''}
                            onChange={(e) => handleDecisionDetailsChange(scenario.id, 'deadline', e.target.value)}
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            Budget estimé
                          </label>
                          <input
                            type="text"
                            value={decision.budget || ''}
                            onChange={(e) => handleDecisionDetailsChange(scenario.id, 'budget', e.target.value)}
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                            placeholder="Budget en €"
                          />
                        </div>
                      </div>
                    )}
                  </motion.div>
                )}
              </div>
            </motion.div>
          );
        })}
      </div>

      {/* Next Steps */}
      <div className="bg-gray-50 p-6 rounded-xl border border-gray-200">
        <h3 className="font-medium text-gray-800 mb-3">Prochaines étapes</h3>
        <div className="space-y-2 text-sm text-gray-600">
          <div className="flex items-center">
            <div className="w-2 h-2 bg-emerald-500 rounded-full mr-3"></div>
            <span>Finaliser toutes les décisions de traitement</span>
          </div>
          <div className="flex items-center">
            <div className="w-2 h-2 bg-emerald-500 rounded-full mr-3"></div>
            <span>Générer le plan de traitement des risques</span>
          </div>
          <div className="flex items-center">
            <div className="w-2 h-2 bg-emerald-500 rounded-full mr-3"></div>
            <span>Passer à la synthèse finale de l'analyse</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RiskTreatment;
