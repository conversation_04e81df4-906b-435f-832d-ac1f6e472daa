import React, { useState } from 'react';
import { X, Send, Loader } from 'lucide-react';

const ChatbotInterface = ({ isOpen, onClose, context = {} }) => {
  const [messages, setMessages] = useState([]);
  const [inputText, setInputText] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  const handleSendMessage = async () => {
    if (!inputText.trim()) return;

    const userMessage = { sender: 'user', text: inputText };
    setMessages([...messages, userMessage]);
    setInputText('');
    setIsLoading(true);

    // --- Placeholder for LLM API Call ---
    // In a real implementation, you would call the Gemini API here
    // For now, simulate a response
    await new Promise(resolve => setTimeout(resolve, 1500)); // Simulate network delay
    const aiResponse = { 
      sender: 'ai', 
      text: `Placeholder response regarding: ${inputText}. Context: Pillar ${context.pillar || 'N/A'}, Business Value ID ${context.businessValue || 'N/A'}` 
    };
    // --- End Placeholder ---

    setMessages(prevMessages => [...prevMessages, aiResponse]);
    setIsLoading(false);
  };

  const handleInputChange = (e) => {
    setInputText(e.target.value);
  };

  const handleKeyDown = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full flex justify-center items-center z-50">
      <div className="relative mx-auto p-5 border w-full max-w-2xl shadow-lg rounded-md bg-white">
        {/* Modal Header */}
        <div className="flex justify-between items-center border-b pb-3 mb-4">
          <h3 className="text-lg font-medium text-gray-900">AI Assistant</h3>
          <button 
            onClick={onClose}
            className="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm p-1.5 ml-auto inline-flex items-center"
          >
            <X size={20} />
            <span className="sr-only">Close modal</span>
          </button>
        </div>
        
        {/* Chat Messages */}
        <div className="h-96 overflow-y-auto mb-4 p-4 bg-gray-50 rounded-md space-y-4">
          {messages.map((msg, index) => (
            <div key={index} className={`flex ${msg.sender === 'user' ? 'justify-end' : 'justify-start'}`}>
              <div className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${msg.sender === 'user' ? 'bg-blue-500 text-white' : 'bg-gray-200 text-gray-800'}`}>
                {msg.text}
              </div>
            </div>
          ))}
          {isLoading && (
            <div className="flex justify-center">
                <Loader size={24} className="animate-spin text-blue-500" />
            </div>
          )}
        </div>

        {/* Input Area */}
        <div className="flex items-center border-t pt-4">
          <textarea
            rows="2"
            className="flex-grow p-2 border border-gray-300 rounded-md mr-2 focus:outline-none focus:ring-1 focus:ring-blue-500 resize-none"
            placeholder="Ask for event suggestions or clarifications..."
            value={inputText}
            onChange={handleInputChange}
            onKeyDown={handleKeyDown}
            disabled={isLoading}
          />
          <button
            onClick={handleSendMessage}
            className="bg-blue-600 text-white p-2 rounded-md hover:bg-blue-700 disabled:opacity-50"
            disabled={isLoading || !inputText.trim()}
          >
            <Send size={20} />
          </button>
        </div>
      </div>
    </div>
  );
};

export default ChatbotInterface;
