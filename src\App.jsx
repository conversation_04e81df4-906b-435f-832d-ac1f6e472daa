import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { authService } from './services/authService';
import ProtectedRoute from './components/auth/ProtectedRoute';

// Layouts
import AdminLayout from './components/layout/AdminLayout';
import AppLayout from './components/layout/AppLayout';

// Pages
import Login from './pages/auth/Login';
import Dashboard from './pages/admin/Dashboard';
import CompanyManagement from './pages/admin/CompanyManagement';
import UserManagement from './pages/admin/UserManagement';
import ActivityLogs from './pages/admin/ActivityLogs';
import AppDashboard from './pages/app/Dashboard';
import NotFound from './pages/NotFound';

const App = () => {
  return (
    <Router>
      <Routes>
        {/* Public routes */}
        <Route path="/login" element={<Login />} />

        {/* Superadmin routes */}
        <Route
          path="/superadmin/*"
          element={
            <ProtectedRoute requiredRole="superadmin">
              <AdminLayout>
                <Routes>
                  <Route path="dashboard" element={<Dashboard />} />
                  <Route path="companies" element={<CompanyManagement />} />
                  <Route path="users" element={<UserManagement />} />
                  <Route path="activities" element={<ActivityLogs />} />
                  <Route path="*" element={<NotFound />} />
                </Routes>
              </AdminLayout>
            </ProtectedRoute>
          }
        />

        {/* Admin routes */}
        <Route
          path="/admin/*"
          element={
            <ProtectedRoute requiredRole="admin">
              <AdminLayout>
                <Routes>
                  <Route path="dashboard" element={<Dashboard />} />
                  <Route path="companies" element={<CompanyManagement />} />
                  <Route path="users" element={<UserManagement />} />
                  <Route path="activities" element={<ActivityLogs />} />
                  <Route path="*" element={<NotFound />} />
                </Routes>
              </AdminLayout>
            </ProtectedRoute>
          }
        />

        {/* Simple User routes */}
        <Route
          path="/app/*"
          element={
            <ProtectedRoute requiredRole="simpleuser">
              <AppLayout>
                <Routes>
                  <Route path="dashboard" element={<AppDashboard />} />
                  <Route path="*" element={<NotFound />} />
                </Routes>
              </AppLayout>
            </ProtectedRoute>
          }
        />

        {/* Redirect root to appropriate dashboard */}
        <Route
          path="/"
          element={
            <Navigate
              to={authService.isAuthenticated() ? authService.getDashboardPath() : '/login'}
              replace
            />
          }
        />

        {/* Catch all route */}
        <Route path="*" element={<NotFound />} />
      </Routes>
    </Router>
  );
};

export default App; 