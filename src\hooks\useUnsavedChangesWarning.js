// src/hooks/useUnsavedChangesWarning.js
import { useEffect } from 'react';
import { useLocation } from 'react-router-dom';

/**
 * Custom hook to warn users about unsaved changes when navigating away
 * @param {boolean} hasUnsavedChanges - Whether there are unsaved changes
 * @returns {void}
 */
const useUnsavedChangesWarning = (hasUnsavedChanges) => {
  const location = useLocation();

  useEffect(() => {
    // Function to handle beforeunload event
    const handleBeforeUnload = (e) => {
      if (hasUnsavedChanges) {
        // Standard way to show a confirmation dialog
        e.preventDefault();
        // Chrome requires returnValue to be set
        e.returnValue = 'Vous avez des modifications non enregistrées. Êtes-vous sûr de vouloir quitter cette page?';
        // Return a string for other browsers
        return 'Vous avez des modifications non enregistrées. Êtes-vous sûr de vouloir quitter cette page?';
      }
    };

    // Add event listener for page unload
    window.addEventListener('beforeunload', handleBeforeUnload);

    // Clean up event listener
    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, [hasUnsavedChanges]);

  // Note: We can't use the Prompt component from react-router-dom v6 as it's been removed
  // Instead, we would need to implement a custom solution using useBlocker from react-router-dom
  // This is a simplified version that only handles page refresh/close, not navigation within the app
};

export default useUnsavedChangesWarning;
