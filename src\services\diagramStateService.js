// src/services/diagramStateService.js
import api from '../api/apiClient';

/**
 * Service for handling diagram state operations
 */
const diagramStateService = {
  /**
   * Save diagram state for a specific attack path
   * @param {string} analysisId - Analysis ID
   * @param {string} attackPathId - Attack Path ID
   * @param {Object} diagramState - Diagram state (nodes and edges)
   * @returns {Promise<Object>} Response data
   */
  saveDiagramState: async (analysisId, attackPathId, diagramState) => {
    try {
      console.log('Saving diagram state for attack path:', { analysisId, attackPathId });

      // Use the API client which handles authentication automatically
      const response = await api.post(`/analyses/${analysisId}/attack-paths/${attackPathId}/diagram`, { diagramState });

      // Also save to local storage as backup
      const storageKey = `diagram-state-${attackPathId}`;
      localStorage.setItem(storageKey, JSON.stringify(diagramState));

      return response;
    } catch (error) {
      console.error('Error saving diagram state:', error);

      // If API fails, at least try to save locally
      const storageKey = `diagram-state-${attackPathId}`;
      localStorage.setItem(storageKey, JSON.stringify(diagramState));

      // Return a success response with a local flag
      return {
        success: true,
        data: diagramState,
        savedLocally: true,
        message: 'Diagram state saved locally only. Will sync when connection is restored.'
      };
    }
  },

  /**
   * Get diagram state for a specific attack path
   * @param {string} analysisId - Analysis ID
   * @param {string} attackPathId - Attack Path ID
   * @returns {Promise<Object>} Diagram state data
   */
  getDiagramState: async (analysisId, attackPathId) => {
    try {
      // Try to get from API first
      try {
        const response = await api.get(`/analyses/${analysisId}/attack-paths/${attackPathId}/diagram`);
        if (response.success && response.data) {
          return response;
        }
      } catch (apiError) {
        console.warn('Could not get diagram state from API, trying local storage:', apiError);
      }

      // If API fails, try to get from local storage
      const storageKey = `diagram-state-${attackPathId}`;
      const localData = localStorage.getItem(storageKey);
      
      if (localData) {
        const parsedData = JSON.parse(localData);
        return {
          success: true,
          data: parsedData,
          fromLocalStorage: true,
          message: 'Diagram state retrieved from local storage'
        };
      }

      // If no data found, return empty state
      return {
        success: false,
        message: 'No diagram state found for this attack path'
      };
    } catch (error) {
      console.error('Error getting diagram state:', error);
      throw error;
    }
  }
};

export default diagramStateService;
