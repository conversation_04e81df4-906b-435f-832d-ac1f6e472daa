// models/Analysis.js
const mongoose = require('mongoose');

const AnalysisSchema = new mongoose.Schema({
  name: {
    type: String,
    required: [true, 'Please provide an analysis name'],
    trim: true
  },
  description: {
    type: String,
    trim: true
  },
  companyId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Company',
    required: true
  },
  companyName: {
    type: String,
    required: true
  },
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  createdByName: {
    type: String,
    required: true
  },
  status: {
    type: String,
    enum: ['draft', 'in-progress', 'completed', 'archived'],
    default: 'draft'
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  },
  lastUpdatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  lastUpdatedByName: {
    type: String
  },
  // Atelier 3 data
  atelier3: {
    // Stakeholders for ecosystem mapping
    stakeholders: [{
      name: String,
      category: String,
      type: String,
      description: String,
      rank: Number,
      dependency: Number,
      penetration: Number,
      cyberMaturity: Number,
      trust: Number,
      threatLevel: Number,
      notes: String,
      retained: { type: Boolean, default: false }, // Whether the stakeholder is retained or not
      createdAt: Date,
      updatedAt: Date,
      createdBy: String
    }],
    // Thresholds for stakeholder threat zones
    stakeholderThresholds: {
      danger: { type: Number, default: 3 },
      control: { type: Number, default: 1.5 },
      watch: { type: Number, default: 0.5 },
      updatedAt: Date
    }
  }
}, {
  timestamps: true
});

// Virtual for getting workshop data
AnalysisSchema.virtual('components', {
  ref: 'AnalysisComponent',
  localField: '_id',
  foreignField: 'analysisId'
});

module.exports = mongoose.model('Analysis', AnalysisSchema);