import React, { useState } from 'react';
import { Save, Loader2, ChevronDown, ChevronUp, ExternalLink, Shield, Target, Bot, Database, Check, X, AlertTriangle } from 'lucide-react';

const StatusSelector = ({ status, onStatusChange }) => {
    const statuses = [
        { id: 'non_corrige', label: 'Non Corrigé', icon: <X className="h-3 w-3 mr-1" />, color: 'text-red-600' },
        { id: 'corrige', label: 'Corrigé', icon: <Check className="h-3 w-3 mr-1" />, color: 'text-green-600' },
        { id: 'risque_accepte', label: 'Risque Accepté', icon: <AlertTriangle className="h-3 w-3 mr-1" />, color: 'text-yellow-600' }
    ];

    return (
        <div className="flex items-center space-x-2">
            <span className="text-xs font-semibold text-gray-600">Statut:</span>
            {statuses.map(s => (
                <button
                    key={s.id}
                    onClick={() => onStatusChange(s.id)}
                    className={`flex items-center px-2 py-1 text-xs rounded-full transition-all duration-200 ${status === s.id ? `${s.color} bg-opacity-100 font-bold` : 'text-gray-500 hover:bg-gray-200'}`}
                >
                    {s.icon}
                    {s.label}
                </button>
            ))}
        </div>
    );
};

const CTIDisplayOrganized = ({ results, onSave, isSaving, itemStatuses = {}, onStatusChange }) => {
    const [expandedAssets, setExpandedAssets] = useState({});
    const [expandedSections, setExpandedSections] = useState({});
    const [expandedVulns, setExpandedVulns] = useState({});
    const [expandedTechniques, setExpandedTechniques] = useState({});

    if (!results || !results.assets || results.assets.length === 0) {
        return (
            <div className="text-center py-8">
                <p className="text-gray-500">Aucun résultat d'analyse CTI disponible.</p>
            </div>
        );
    }

    const toggleAssetExpansion = (assetId) => {
        setExpandedAssets(prev => ({
            ...prev,
            [assetId]: !prev[assetId]
        }));
    };

    const toggleSectionExpansion = (assetId, section) => {
        const key = `${assetId}-${section}`;
        setExpandedSections(prev => ({
            ...prev,
            [key]: !prev[key]
        }));
    };

    const toggleVulnExpansion = (vulnId) => {
        setExpandedVulns(prev => ({
            ...prev,
            [vulnId]: !prev[vulnId]
        }));
    };

    const toggleTechniqueExpansion = (techId) => {
        setExpandedTechniques(prev => ({
            ...prev,
            [techId]: !prev[techId]
        }));
    };

    // Organize techniques by source
    const organizeTechniquesBySource = (techniques) => {
        const organized = {
            mitre: [],
            atlas: []
        };
        
        techniques.forEach(tech => {
            if (tech.isAtlas || tech.source?.includes('ATLAS') || tech.id?.startsWith('AML.')) {
                organized.atlas.push(tech);
            } else {
                organized.mitre.push(tech);
            }
        });
        
        return organized;
    };

    // Organize vulnerabilities by severity
    const organizeVulnerabilitiesBySeverity = (vulnerabilities) => {
        const organized = {
            CRITICAL: [],
            HIGH: [],
            MEDIUM: [],
            LOW: [],
            UNKNOWN: []
        };

        vulnerabilities.forEach(vuln => {
            const severity = renderSeverity(vuln.severity);
            if (organized[severity]) {
                organized[severity].push(vuln);
            } else {
                organized.UNKNOWN.push(vuln);
            }
        });

        return organized;
    };

    // Helper function to safely render score
    const renderScore = (score) => {
        if (typeof score === 'object' && score !== null) {
            return score.score || score.baseScore || 'N/A';
        }
        return score || 'N/A';
    };

    // Helper function to safely render severity
    const renderSeverity = (severity) => {
        if (typeof severity === 'object' && severity !== null) {
            return severity.severity || severity.level || 'UNKNOWN';
        }
        return severity || 'UNKNOWN';
    };

    return (
        <div className="space-y-6">
            {/* Results Summary */}
            <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-6 border border-blue-200">
                <h3 className="text-lg font-semibold text-gray-800 mb-4">Résultats de l'Analyse CTI</h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="text-center">
                        <div className="text-2xl font-bold text-blue-600">{results.totalVulnerabilities || 0}</div>
                        <div className="text-sm text-gray-600">Vulnérabilités</div>
                    </div>
                    <div className="text-center">
                        <div className="text-2xl font-bold text-red-600">{results.totalAttackTechniques || 0}</div>
                        <div className="text-sm text-gray-600">Techniques d'Attaque</div>
                    </div>
                    <div className="text-center">
                        <div className="text-2xl font-bold text-green-600">{results.assets?.length || 0}</div>
                        <div className="text-sm text-gray-600">Actifs Analysés</div>
                    </div>
                </div>
            </div>

            {/* Assets Results */}
            {results.assets.map(asset => (
                <div key={asset.id} className="bg-white rounded-lg border border-gray-200 overflow-hidden">
                    <div
                        className="bg-gray-50 px-6 py-4 border-b border-gray-200 cursor-pointer hover:bg-gray-100 transition-colors"
                        onClick={() => toggleAssetExpansion(asset.id)}
                    >
                        <div className="flex justify-between items-center">
                            <div>
                                <h4 className="text-lg font-semibold text-gray-800">{asset.name}</h4>
                                <p className="text-sm text-gray-600">{asset.type}</p>
                                <div className="flex items-center space-x-4 mt-2">
                                    {asset.vulnerabilities?.length > 0 && (
                                        <span className="text-xs bg-red-100 text-red-700 px-2 py-1 rounded-full">
                                            {asset.vulnerabilities.length} vulnérabilités
                                        </span>
                                    )}
                                    {asset.attackTechniques?.length > 0 && (
                                        <span className="text-xs bg-purple-100 text-purple-700 px-2 py-1 rounded-full">
                                            {asset.attackTechniques.length} techniques
                                        </span>
                                    )}
                                </div>
                            </div>
                            {expandedAssets[asset.id] ?
                                <ChevronUp className="h-5 w-5 text-gray-500" /> :
                                <ChevronDown className="h-5 w-5 text-gray-500" />
                            }
                        </div>
                    </div>

                    {expandedAssets[asset.id] && (
                        <div className="p-6 space-y-4">
                            {(() => {
                                const organizedVulns = organizeVulnerabilitiesBySeverity(asset.vulnerabilities || []);
                                const organizedTechs = organizeTechniquesBySource(asset.attackTechniques || []);

                                return (
                                    <div className="space-y-4">
                                        {/* Vulnerabilities Section */}
                                        {asset.vulnerabilities?.length > 0 && (
                                            <div className="border border-gray-200 rounded-lg">
                                                <div
                                                    className="bg-gray-100 px-4 py-3 cursor-pointer hover:bg-gray-200 transition-colors flex justify-between items-center"
                                                    onClick={() => toggleSectionExpansion(asset.id, 'vulnerabilities')}
                                                >
                                                    <div className="flex items-center">
                                                        <Shield className="h-4 w-4 mr-2 text-gray-600" />
                                                        <span className="font-semibold text-gray-800">Vulnérabilités ({asset.vulnerabilities.length})</span>
                                                    </div>
                                                    {expandedSections[`${asset.id}-vulnerabilities`] ?
                                                        <ChevronUp className="h-4 w-4 text-gray-600" /> :
                                                        <ChevronDown className="h-4 w-4 text-gray-600" />
                                                    }
                                                </div>
                                                {expandedSections[`${asset.id}-vulnerabilities`] && (
                                                    <div className="p-4 space-y-4">
                                                        {Object.entries(organizedVulns).map(([severity, vulns]) => (
                                                            vulns.length > 0 && (
                                                                <div key={severity} className="border border-gray-200 rounded-lg">
                                                                    <div className={`bg-gray-50 px-4 py-3 flex justify-between items-center`}>
                                                                        <div className="flex items-center">
                                                                            <span className={`font-semibold text-gray-800`}>{severity} ({vulns.length})</span>
                                                                        </div>
                                                                    </div>
                                                                    <div className="p-4 space-y-2">
                                                                        {vulns.map(vuln => (
                                                                            <VulnerabilityCard
                                                                                key={vuln.id}
                                                                                vuln={vuln}
                                                                                expanded={expandedVulns[vuln.id]}
                                                                                onToggle={() => toggleVulnExpansion(vuln.id)}
                                                                                renderScore={renderScore}
                                                                                renderSeverity={renderSeverity}
                                                                                status={itemStatuses[vuln.id] || 'non_corrige'}
                                                                                onStatusChange={(newStatus) => onStatusChange && onStatusChange(vuln.id, newStatus)}
                                                                            />
                                                                        ))}
                                                                    </div>
                                                                </div>
                                                            )
                                                        ))}
                                                    </div>
                                                )}
                                            </div>
                                        )}

                                        {/* Techniques Section */}
                                        {asset.attackTechniques?.length > 0 && (
                                            <div className="border border-gray-200 rounded-lg">
                                                <div
                                                    className="bg-gray-100 px-4 py-3 cursor-pointer hover:bg-gray-200 transition-colors flex justify-between items-center"
                                                    onClick={() => toggleSectionExpansion(asset.id, 'techniques')}
                                                >
                                                    <div className="flex items-center">
                                                        <Target className="h-4 w-4 mr-2 text-gray-600" />
                                                        <span className="font-semibold text-gray-800">Techniques ({asset.attackTechniques.length})</span>
                                                    </div>
                                                    {expandedSections[`${asset.id}-techniques`] ?
                                                        <ChevronUp className="h-4 w-4 text-gray-600" /> :
                                                        <ChevronDown className="h-4 w-4 text-gray-600" />
                                                    }
                                                </div>
                                                {expandedSections[`${asset.id}-techniques`] && (
                                                    <div className="p-4 space-y-4">
                                                        {organizedTechs.mitre.length > 0 && (
                                                            <div className="border border-red-200 rounded-lg">
                                                                <div className="bg-red-50 px-4 py-3 flex justify-between items-center">
                                                                    <div className="flex items-center">
                                                                        <Target className="h-4 w-4 mr-2 text-red-600" />
                                                                        <span className="font-semibold text-red-800">MITRE ATT&CK ({organizedTechs.mitre.length})</span>
                                                                    </div>
                                                                </div>
                                                                <div className="p-4 space-y-2">
                                                                    {organizedTechs.mitre.map(technique => (
                                                                        <TechniqueCard
                                                                            key={technique.id}
                                                                            technique={technique}
                                                                            expanded={expandedTechniques[technique.id]}
                                                                            onToggle={() => toggleTechniqueExpansion(technique.id)}
                                                                            color="red"
                                                                            status={itemStatuses[technique.id] || 'non_corrige'}
                                                                            onStatusChange={(newStatus) => onStatusChange && onStatusChange(technique.id, newStatus)}
                                                                        />
                                                                    ))}
                                                                </div>
                                                            </div>
                                                        )}

                                                        {organizedTechs.atlas.length > 0 && (
                                                            <div className="border border-purple-200 rounded-lg">
                                                                <div className="bg-purple-50 px-4 py-3 flex justify-between items-center">
                                                                    <div className="flex items-center">
                                                                        <Bot className="h-4 w-4 mr-2 text-purple-600" />
                                                                        <span className="font-semibold text-purple-800">MITRE ATLAS ({organizedTechs.atlas.length})</span>
                                                                    </div>
                                                                </div>
                                                                <div className="p-4 space-y-2">
                                                                    {organizedTechs.atlas.map(technique => (
                                                                        <TechniqueCard
                                                                            key={technique.id}
                                                                            technique={technique}
                                                                            expanded={expandedTechniques[technique.id]}
                                                                            onToggle={() => toggleTechniqueExpansion(technique.id)}
                                                                            color="purple"
                                                                            status={itemStatuses[technique.id] || 'non_corrige'}
                                                                            onStatusChange={(newStatus) => onStatusChange && onStatusChange(technique.id, newStatus)}
                                                                        />
                                                                    ))}
                                                                </div>
                                                            </div>
                                                        )}
                                                    </div>
                                                )}
                                            </div>
                                        )}
                                    </div>
                                );
                            })()}
                        </div>
                    )}
                </div>
            ))}

            {/* Save Button */}
            <div className="flex justify-end">
                <button
                    onClick={onSave}
                    disabled={isSaving}
                    className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                    {isSaving ? (
                        <>
                            <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                            Sauvegarde...
                        </>
                    ) : (
                        <>
                            <Save className="h-4 w-4 mr-2" />
                            Sauvegarder les Résultats
                        </>
                    )}
                </button>
            </div>
        </div>
    );
};

// Vulnerability Card Component
const VulnerabilityCard = ({ vuln, expanded, onToggle, renderScore, renderSeverity, status = 'non_corrige', onStatusChange }) => {

    const severityClass = {
        CRITICAL: 'border-red-500',
        HIGH: 'border-orange-500',
        MEDIUM: 'border-yellow-500',
        LOW: 'border-green-500',
        UNKNOWN: 'border-gray-400'
    }[renderSeverity(vuln.severity)] || 'border-gray-400';

    return (
        <div className={`bg-white border-l-4 ${severityClass} rounded-lg shadow-md`}>
            <div
                className="p-4 cursor-pointer hover:bg-gray-50 transition-colors"
                onClick={onToggle}
            >
                <div className="flex justify-between items-start">
                    <div className="flex-1">
                        <div className="flex items-center justify-between">
                            <h6 className="font-bold text-lg text-gray-800">{vuln.id}</h6>
                            <div className="flex items-center space-x-2">
                                <span className={`px-2 py-1 rounded text-xs font-medium ${
                                    renderSeverity(vuln.severity) === 'CRITICAL' ? 'bg-red-100 text-red-800' :
                                    renderSeverity(vuln.severity) === 'HIGH' ? 'bg-orange-100 text-orange-800' :
                                    renderSeverity(vuln.severity) === 'MEDIUM' ? 'bg-yellow-100 text-yellow-800' :
                                    'bg-green-100 text-green-800'
                                }`}>
                                    {renderSeverity(vuln.severity)}
                                </span>
                                <span className="text-2xl font-bold text-gray-800">
                                    {renderScore(vuln.severity)}
                                </span>
                            </div>
                        </div>
                        <div className="flex items-center mt-2 space-x-4">
                            {vuln.epssScore && (
                                <span className="text-xs bg-purple-100 text-purple-700 px-2 py-1 rounded">
                                    EPSS: {vuln.epssScore}%
                                </span>
                            )}
                            {vuln.exploited && (
                                <span className="text-xs bg-red-600 text-white px-2 py-1 rounded">
                                    Exploité
                                </span>
                            )}
                        </div>
                    </div>
                    {expanded ?
                        <ChevronUp className="h-4 w-4 text-gray-600 ml-4" /> :
                        <ChevronDown className="h-4 w-4 text-gray-600 ml-4" />
                    }
                </div>
            </div>
            {expanded && (
                <div className="px-4 pb-4 border-t border-gray-200">
                    <div className="mt-3 space-y-3">
                        <p className="text-sm text-gray-700">{vuln.description}</p>
                        
                        <div className="grid grid-cols-2 gap-4 text-xs">
                            {vuln.publishedDate && (
                                <div>
                                    <span className="font-semibold text-gray-600">Publié:</span>
                                    <span className="ml-2">{new Date(vuln.publishedDate).toLocaleDateString()}</span>
                                </div>
                            )}
                            {vuln.lastModified && (
                                <div>
                                    <span className="font-semibold text-gray-600">Modifié:</span>
                                    <span className="ml-2">{new Date(vuln.lastModified).toLocaleDateString()}</span>
                                </div>
                            )}
                        </div>
                        
                        {vuln.references && Array.isArray(vuln.references) && vuln.references.length > 0 && (
                            <div>
                                <div className="text-xs font-semibold text-gray-600 mb-1">Références:</div>
                                <div className="space-y-1">
                                    {vuln.references.map((ref, idx) => (
                                        <a
                                            key={idx}
                                            href={ref.url || ref}
                                            target="_blank"
                                            rel="noopener noreferrer"
                                            className="block text-xs text-blue-600 hover:text-blue-800 truncate"
                                        >
                                            {ref.name || ref.url || ref}
                                        </a>
                                    ))}
                                </div>
                            </div>
                        )}
                        <div className="pt-2 border-t border-gray-200">
                            <StatusSelector status={status} onStatusChange={onStatusChange} />
                        </div>
                    </div>
                </div>
            )}
        </div>
    );
}

// Technique Card Component
const TechniqueCard = ({ technique, expanded, onToggle, color, status = 'non_corrige', onStatusChange }) => {
    const colorClasses = {
        red: {
            border: 'border-red-500',
            badge: 'bg-red-100 text-red-700'
        },
        purple: {
            border: 'border-purple-500',
            badge: 'bg-purple-100 text-purple-700'
        }
    };
    
    const classes = colorClasses[color] || colorClasses.red;
    
    return (
        <div className={`bg-white border-l-4 ${classes.border} rounded-lg shadow-md`}>
            <div
                className={`p-4 cursor-pointer hover:bg-gray-50 transition-colors`}
                onClick={onToggle}
            >
                <div className="flex justify-between items-start">
                    <div className="flex-1">
                        <div className="flex items-center justify-between">
                            <h6 className={`font-bold text-lg text-gray-800`}>
                                {technique.id} - {technique.name}
                            </h6>
                            {expanded ?
                                <ChevronUp className={`h-4 w-4 text-gray-600`} /> :
                                <ChevronDown className={`h-4 w-4 text-gray-600`} />
                            }
                        </div>
                        <div className="flex items-center mt-2 space-x-4">
                            <span className={`px-2 py-1 rounded text-xs font-medium ${classes.badge}`}>
                                {technique.tactic}
                            </span>
                            {technique.platforms && technique.platforms.length > 0 && (
                                <span className="text-xs text-gray-600">
                                    Plateformes: {technique.platforms.join(', ')}
                                </span>
                            )}
                            {technique.source && (
                                <span className="text-xs bg-gray-100 text-gray-700 px-2 py-1 rounded">
                                    {technique.source}
                                </span>
                            )}
                        </div>
                    </div>
                </div>
            </div>
            {expanded && (
                <div className={`px-4 pb-4 border-t border-gray-200`}>
                    <div className="mt-3 space-y-3">
                        <p className="text-sm text-gray-700">{technique.description}</p>
                        
                        <div className="grid grid-cols-2 gap-4">
                            {technique.dataSource && technique.dataSource.length > 0 && (
                                <div>
                                    <div className="text-xs font-semibold text-gray-600 mb-1">Sources de Données:</div>
                                    <div className="text-xs text-gray-700">
                                        {Array.isArray(technique.dataSource) ?
                                            technique.dataSource.join(', ') :
                                            technique.dataSource
                                        }
                                    </div>
                                </div>
                            )}
                            {technique.url && (
                                <div>
                                    <div className="text-xs font-semibold text-gray-600 mb-1">Référence:</div>
                                    <a
                                        href={technique.url}
                                        target="_blank"
                                        rel="noopener noreferrer"
                                        className="text-xs text-blue-600 hover:text-blue-800 flex items-center"
                                    >
                                        {technique.isAtlas ? 'MITRE ATLAS' : 'MITRE ATT&CK'} <ExternalLink className="h-3 w-3 ml-1" />
                                    </a>
                                </div>
                            )}
                        </div>
                        <div className="pt-2 border-t border-gray-200">
                            <StatusSelector status={status} onStatusChange={onStatusChange} />
                        </div>
                    </div>
                </div>
            )}
        </div>
    );
};

export default CTIDisplayOrganized;