// src/components/dashboard/Dashboard.js
import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>hart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer, Cell } from 'recharts';
import {
    <PERSON><PERSON><PERSON> as <PERSON><PERSON>hartLucide,
    <PERSON><PERSON><PERSON> as PieChartLucide,
    Users,
    Target,
    Calendar,
    Shield,
    AlertTriangle,
    ChevronDown,
    ChevronRight,
    ChevronUp,
    Download,
    RefreshCw,
    Info,
    FileText,
    XCircle,
    Settings,
    ExternalLink,
    TrendingUp,
    Filter,
    Clock,
    CheckCircle,
    HelpCircle,
    Eye,
    LineChart,
    Zap,
    Award
} from 'lucide-react';
import AnalysisSelector from './AnalysisSelector';
import { useAnalysis } from '../context/AnalysisContext';

// --- Enhanced Helper Components ---

// Modern Card with hover effects and optional gradient
const InfoCard = ({ title, message, icon = FileText, value = null, trend = null, color = 'blue' }) => {
    const colorMap = {
        blue: 'from-blue-50 to-blue-100 border-blue-200 shadow-blue-100/40',
        green: 'from-emerald-50 to-emerald-100 border-emerald-200 shadow-emerald-100/40',
        purple: 'from-violet-50 to-violet-100 border-violet-200 shadow-violet-100/40',
        amber: 'from-amber-50 to-amber-100 border-amber-200 shadow-amber-100/40',
        red: 'from-red-50 to-red-100 border-red-200 shadow-red-100/40',
    };

    const iconColorMap = {
        blue: 'text-blue-500',
        green: 'text-emerald-500',
        purple: 'text-violet-500',
        amber: 'text-amber-500',
        red: 'text-red-500',
    };

    return (
        <motion.div
            whileHover={{ y: -3, boxShadow: '0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 8px 10px -6px rgba(0, 0, 0, 0.05)' }}
            transition={{ duration: 0.2 }}
            className={`bg-gradient-to-br ${colorMap[color]} p-5 rounded-xl border shadow-lg relative overflow-hidden flex flex-col justify-between min-h-[130px]`}
        >
            <div className="absolute right-0 top-0 h-full w-1/3 opacity-10 pointer-events-none">
                {React.createElement(icon, { size: 80, className: "absolute -right-5 -top-5 transform rotate-12" })}
            </div>
            <div>
                <div className="flex items-center mb-1">
                    {React.createElement(icon, { size: 18, className: `mr-2 ${iconColorMap[color]}` })}
                    <h3 className="text-sm font-medium text-gray-600">{title}</h3>
                </div>
            </div>
            {value !== null ? (
                <div className="mt-3">
                    <span className="text-3xl font-bold text-gray-800">{value}</span>
                    {trend && (
                        <div className="flex items-center mt-1">
                            <span className={`text-xs ${trend > 0 ? 'text-green-600' : 'text-red-600'} flex items-center`}>
                                {trend > 0 ? '↑' : '↓'} {Math.abs(trend)}%
                                <span className="text-gray-500 ml-1">depuis le dernier mois</span>
                            </span>
                        </div>
                    )}
                </div>
            ) : (
                <div className="flex items-center justify-center text-center text-xs text-gray-500 mt-1 flex-grow bg-white/50 p-2 rounded-lg">
                    {React.createElement(icon, { size: 16, className: "mr-1 flex-shrink-0" })}
                    <span>{message || 'À définir'}</span>
                </div>
            )}
        </motion.div>
    );
};

// Enhanced Section with collapsible functionality
const InfoSection = ({ title, message, icon = FileText, children = null, collapsible = false }) => {
    const [isCollapsed, setIsCollapsed] = useState(false);

    return (
        <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-white rounded-xl border border-gray-200 shadow-md overflow-hidden"
        >
            <div className="p-4 border-b border-gray-200 bg-gray-50 flex justify-between items-center">
                <h3 className="font-medium text-gray-700 flex items-center">
                    {React.createElement(icon, { size: 18, className: "mr-2 text-gray-500" })}
                    {title}
                </h3>
                {collapsible && (
                    <button
                        onClick={() => setIsCollapsed(!isCollapsed)}
                        className="p-1 rounded-full hover:bg-gray-200 transition-colors"
                    >
                        {isCollapsed ? <ChevronDown size={18} /> : <ChevronUp size={18} />}
                    </button>
                )}
            </div>

            <AnimatePresence>
                {(!collapsible || !isCollapsed) && (
                    <motion.div
                        initial={{ height: 0, opacity: 0 }}
                        animate={{ height: "auto", opacity: 1 }}
                        exit={{ height: 0, opacity: 0 }}
                        transition={{ duration: 0.2 }}
                    >
                        {children || (
                            <div className="p-8 text-center">
                                {React.createElement(icon, { size: 48, className: `mx-auto mb-3 text-gray-300` })}
                                <p className={`text-gray-500`}>
                                    {message || 'Aucune donnée définie pour cette section.'}
                                </p>
                            </div>
                        )}
                    </motion.div>
                )}
            </AnimatePresence>
        </motion.div>
    );
};

// Enhanced Empty Chart
const EmptyChartPlaceholder = ({ title, icon = BarChartLucide }) => (
    <motion.div
        whileHover={{ boxShadow: '0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 8px 10px -6px rgba(0, 0, 0, 0.05)' }}
        className="bg-white p-5 rounded-xl border border-gray-200 shadow-md"
    >
        <div className="flex items-center mb-4">
            {React.createElement(icon, { size: 20, className: "mr-2 text-gray-400" })}
            <h3 className="font-medium text-gray-700">{title}</h3>
        </div>
        <div className="h-[300px] flex flex-col items-center justify-center text-center bg-gray-50 rounded-lg">
            {React.createElement(icon, { size: 48, className: `text-gray-300 mb-3 opacity-50` })}
            <p className="text-sm text-gray-400">Données insuffisantes pour afficher le graphique.</p>
        </div>
    </motion.div>
);

// Enhanced chart container with a modern look
const ChartContainer = ({ title, icon, children }) => (
    <motion.div
        whileHover={{ boxShadow: '0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 8px 10px -6px rgba(0, 0, 0, 0.05)' }}
        transition={{ duration: 0.2 }}
        className="bg-white p-5 rounded-xl border border-gray-200 shadow-md"
    >
        <div className="flex items-center justify-between mb-4">
            <div className="flex items-center">
                {React.createElement(icon, { size: 20, className: "mr-2 text-gray-500" })}
                <h3 className="font-medium text-gray-700">{title}</h3>
            </div>
            <div className="flex space-x-1">
                <button className="p-1.5 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-full transition-colors">
                    <RefreshCw size={16} />
                </button>
                <button className="p-1.5 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-full transition-colors">
                    <Settings size={16} />
                </button>
            </div>
        </div>
        {children}
    </motion.div>
);

// --- Main Dashboard Component ---
const Dashboard = ({ preferences, setPreferences }) => {
    // --- Get Data SOLELY from Context ---
    const {
        currentAnalysis,
        currentContextData,
        currentBusinessValues,
        currentDreadedEvents,
        currentSecurityFramework,
        currentSecurityControls,
        currentRiskTreatments,
        isWorkshopDataLoading,
        workshopDataError
    } = useAnalysis();

    // --- Local UI State ---
    const [activeSection, setActiveSection] = useState(preferences?.activeSection || 'overview');
    const [securityFilter, setSecurityFilter] = useState('all');
    const [securityView, setSecurityView] = useState('grid');

    // Update activeSection in preferences
    useEffect(() => {
        if (setPreferences && activeSection !== preferences?.activeSection) {
            setPreferences({ ...preferences, activeSection });
        }
    }, [activeSection, preferences, setPreferences]);

    // --- Use Context Data Directly ---
    // Assign defaults, assume data exists unless explicitly null/undefined
    const context = currentContextData || {};
    const bValues = currentBusinessValues || [];
    const events = currentDreadedEvents || [];
    const frameworkData = currentSecurityFramework || null; // Contains rules, frameworks etc.
    const controlsData = currentSecurityControls || [];
    const treatmentsData = currentRiskTreatments || []; // Not used yet, but available

    // --- Prepare data for charts (only if source data exists) ---
    const participantRolesData = context?.participants?.length > 0 ? (context.participants).reduce((acc, p) => {
        let role = p.position === "OTHER" && p.customPosition ? p.customPosition : (p.position || 'Undefined');
        const existing = acc.find(item => item.name === role);
        if (existing) existing.value += 1; else acc.push({ name: role, value: 1 });
        return acc;
    }, []) : [];

    const securityPillarEventsData = events?.length > 0 ? (events).reduce((acc, event) => {
        // Normalize security pillar to handle case inconsistencies (especially for auditabilite/Auditabilite)
        let pillar = event.securityPillar || 'Undefined';
        // Normalize auditabilite to always use the same capitalization (Auditabilite)
        if (pillar.toLowerCase() === 'auditabilite') {
            pillar = 'Auditabilite';
        }

        const severity = event.severity || 'Undefined';
        let pillarItem = acc.find(item => item.pillar === pillar);
        if (!pillarItem) {
            pillarItem = { pillar, critical: 0, major: 0, moderate: 0, low: 0, catastrophic: 0 };
            acc.push(pillarItem);
        }
        switch (severity) {
            case 'catastrophic': pillarItem.catastrophic += 1; break;
            case 'critical': pillarItem.critical += 1; break;
            case 'major': pillarItem.major += 1; break;
            case 'moderate': pillarItem.moderate += 1; break;
            case 'minor': pillarItem.low += 1; break;
            default: break;
        }
        return acc;
    }, []) : [];

    const businessValuePillarsData = bValues?.length > 0 ? (bValues).reduce((acc, bv) => {
        if (Array.isArray(bv.securityPillars)) {
            bv.securityPillars.forEach(pillar => {
                const pillarName = pillar || 'Undefined';
                const existing = acc.find(item => item.name === pillarName);
                if (existing) existing.value += 1; else acc.push({ name: pillarName, value: 1 });
            });
        }
        return acc;
    }, []) : [];

    // Filter security controls based on selected filter
    const filteredControls = controlsData.filter(control => {
        if (securityFilter === 'all') return true;
        return control.status === securityFilter;
    });

    // Color palettes - Enhanced with more modern colors
    const ROLE_COLORS = ['#6366F1', '#10B981', '#8B5CF6', '#F43F5E', '#F59E0B', '#3B82F6'];
    const SEVERITY_COLORS = {
        catastrophic: '#1E293B',
        critical: '#EF4444',
        major: '#F97316',
        moderate: '#FBBF24',
        low: '#10B981'
    };
    const PILLAR_COLORS = ['#6366F1', '#10B981', '#F43F5E', '#F97316', '#8B5CF6', '#EC4899'];

    // Tab variants for animation
    const tabVariants = {
        active: {
            backgroundColor: '#EFF6FF',
            color: '#1D4ED8',
            borderColor: '#93C5FD',
            boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)'
        },
        inactive: {
            backgroundColor: '#F3F4F6',
            color: '#4B5563',
            borderColor: '#E5E7EB',
            boxShadow: 'none'
        }
    };

    // Animation for container transitions
    const containerVariants = {
        hidden: { opacity: 0, y: 10 },
        visible: {
            opacity: 1,
            y: 0,
            transition: {
                duration: 0.4,
                staggerChildren: 0.1
            }
        }
    };

    const itemVariants = {
        hidden: { opacity: 0, y: 20 },
        visible: { opacity: 1, y: 0 }
    };

    // Button group variants
    const buttonGroupVariants = {
        active: {
            backgroundColor: '#4F46E5',
            color: 'white',
            borderColor: '#4338CA',
        },
        inactive: {
            backgroundColor: '#F9FAFB',
            color: '#6B7280',
            borderColor: '#E5E7EB',
        }
    };

    // --- Main Render ---
    return (
        <motion.div
            initial={{ opacity: 0, y: 15 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="bg-gradient-to-b from-white to-gray-50 p-6 rounded-xl shadow-lg border border-gray-200"
        >
            {/* Enhanced Header */}
            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
                <div>
                    <h2 className="text-2xl font-bold text-gray-800 flex items-center">
                        <BarChartLucide size={24} className="mr-2 text-blue-500" />
                        Tableau de bord
                        <span className="ml-2 px-2 py-1 bg-blue-100 text-blue-800 text-xs font-medium rounded-full">v2.0</span>
                    </h2>
                    <p className="text-sm text-gray-500 mt-1 ml-9">Analyse et suivi des risques de sécurité</p>
                </div>
                <div className="flex flex-wrap gap-2">
                    <div className="bg-gray-100 rounded-md px-3 py-1.5 text-sm text-gray-700 flex items-center">
                        <Calendar size={16} className="mr-1.5 text-gray-500" />
                        {new Date().toLocaleDateString('fr-FR', {weekday: 'long', year: 'numeric', month: 'long', day: 'numeric'})}
                    </div>
                    <motion.button
                        whileHover={{ scale: 1.03 }}
                        whileTap={{ scale: 0.97 }}
                        className="bg-gray-100 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-200 transition-colors duration-200 flex items-center shadow-sm"
                    >
                        <Settings size={18} className="mr-2" />
                        Options
                    </motion.button>
                    <motion.button
                        whileHover={{ scale: 1.03 }}
                        whileTap={{ scale: 0.97 }}
                        className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors duration-200 flex items-center shadow-sm disabled:opacity-50"
                        onClick={() => alert('Fonctionnalité Export à implémenter')}
                        disabled={!currentAnalysis}
                    >
                        <Download size={18} className="mr-2" />
                        Exporter
                    </motion.button>
                </div>
            </div>

            {/* Analysis Selector */}
            <div className="mb-6 bg-white p-4 rounded-xl shadow-md border border-gray-200">
                <div className="flex items-center mb-2">
                    <h3 className="text-sm font-medium text-gray-700">Sélection de l'analyse</h3>
                    <div className="ml-2 px-2 py-0.5 bg-blue-50 rounded-full text-xs text-blue-700 flex items-center">
                        <Info size={12} className="mr-1" />
                        Choisissez une analyse pour voir ses données
                    </div>
                </div>
                <AnalysisSelector />
            </div>

            {/* Loading State */}
            {isWorkshopDataLoading && (
                <motion.div
                    initial={{ opacity: 0, y: 5 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="bg-blue-50 border border-blue-100 rounded-lg p-5 my-6 flex items-center justify-center"
                >
                    <RefreshCw size={22} className="animate-spin text-blue-500 mr-3" />
                    <p className="text-blue-700 font-medium">Chargement des données...</p>
                </motion.div>
            )}

            {/* Error state */}
            {workshopDataError && !isWorkshopDataLoading && (
                <motion.div
                    initial={{ opacity: 0, y: 5 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="bg-red-50 border border-red-100 rounded-lg p-5 my-6 flex items-center"
                >
                    <XCircle size={22} className="text-red-500 mr-3 flex-shrink-0" />
                    <div>
                        <p className="text-red-700 font-medium">Erreur lors du chargement des données</p>
                        <p className="text-red-600 text-sm mt-1">Veuillez réessayer ou contacter le support si le problème persiste.</p>
                    </div>
                    <button className="ml-auto bg-white border border-red-300 text-red-600 px-3 py-1 rounded-md text-sm hover:bg-red-50">
                        Réessayer
                    </button>
                </motion.div>
            )}

            {/* Content Area: Render if NOT loading AND an analysis IS selected */}
            {!isWorkshopDataLoading && currentAnalysis && (
                <AnimatePresence mode="wait">
                    <motion.div
                        key={activeSection}
                        initial="hidden"
                        animate="visible"
                        variants={containerVariants}
                    >
                        {/* Enhanced Navigation Tabs */}
                        <div className="flex flex-wrap gap-3 mb-8">
                            {[
                                { id: 'overview', label: "Vue d'ensemble", icon: BarChartLucide },
                                { id: 'participants', label: "Participants", icon: Users },
                                { id: 'events', label: "Événements redoutés", icon: AlertTriangle },
                                { id: 'security', label: "Mesures de sécurité", icon: Shield },
                                { id: 'analysis', label: "Analyse", icon: LineChart },
                            ].map(section => (
                                <motion.button
                                    key={section.id}
                                    variants={tabVariants}
                                    animate={activeSection === section.id ? 'active' : 'inactive'}
                                    whileHover={{ scale: 1.02 }}
                                    whileTap={{ scale: 0.98 }}
                                    transition={{ duration: 0.2 }}
                                    className="px-5 py-2.5 rounded-lg text-sm font-medium border flex items-center"
                                    onClick={() => setActiveSection(section.id)}
                                >
                                    {React.createElement(section.icon, { size: 16, className: "mr-2" })}
                                    {section.label}
                                </motion.button>
                            ))}
                        </div>

                        {/* Main Content based on activeSection */}
                        <div className="space-y-8">
                            {/* Overview Section */}
                            {activeSection === 'overview' && (
                                <motion.div variants={containerVariants}>
                                    {/* Analytics Overview Banner */}
                                    <motion.div
                                        variants={itemVariants}
                                        className="bg-gradient-to-r from-blue-600 to-indigo-600 rounded-xl p-5 text-white shadow-lg"
                                    >
                                        <div className="flex flex-col md:flex-row justify-between items-start md:items-center">
                                            <div>
                                                <h3 className="text-lg font-medium mb-1 flex items-center">
                                                    <Shield size={20} className="mr-2 text-blue-200" />
                                                    Analyse des risques {currentAnalysis?.name}
                                                </h3>
                                                <p className="text-blue-100 text-sm max-w-xl">
                                                    Ce tableau de bord présente une vue d'ensemble de votre analyse de risques,
                                                    y compris les événements redoutés, les valeurs métier et les mesures de sécurité.
                                                </p>
                                            </div>
                                            <div className="flex gap-3 mt-4 md:mt-0">
                                                <motion.button
                                                    whileHover={{ scale: 1.05 }}
                                                    whileTap={{ scale: 0.95 }}
                                                    className="bg-white text-blue-600 px-4 py-2 rounded-lg text-sm font-medium shadow-sm hover:bg-blue-50 transition-colors flex items-center"
                                                >
                                                    <RefreshCw size={16} className="mr-2" />
                                                    Actualiser
                                                </motion.button>
                                                <motion.button
                                                    whileHover={{ scale: 1.05 }}
                                                    whileTap={{ scale: 0.95 }}
                                                    className="bg-blue-500 text-white px-4 py-2 rounded-lg text-sm font-medium shadow-sm hover:bg-blue-400 transition-colors border border-blue-400 flex items-center"
                                                >
                                                    <Download size={16} className="mr-2" />
                                                    Rapport PDF
                                                </motion.button>
                                            </div>
                                        </div>
                                    </motion.div>

                                    {/* Summary Cards - Enhanced with colors and mock trends */}
                                    <motion.div variants={itemVariants} className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-5 mb-8">
                                        <InfoCard
                                            title="Participants"
                                            value={context?.participants?.length ?? null}
                                            icon={Users}
                                            trend={12}
                                            color="blue"
                                        />
                                        <InfoCard
                                            title="Valeurs métier"
                                            value={bValues?.length ?? null}
                                            icon={Target}
                                            trend={8}
                                            color="green"
                                        />
                                        <InfoCard
                                            title="Événements redoutés"
                                            value={events?.length ?? null}
                                            icon={AlertTriangle}
                                            trend={-5}
                                            color="amber"
                                        />
                                        <InfoCard
                                            title="Mesures de sécurité"
                                            value={controlsData?.length ?? null}
                                            icon={Shield}
                                            trend={15}
                                            color="purple"
                                        />
                                    </motion.div>

                                    {/* Charts - Enhanced with modern containers */}
                                    <motion.div variants={itemVariants} className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                                        {/* Participant Roles Chart */}
                                        {(participantRolesData.length > 0) ? (
                                            <ChartContainer title="Répartition des rôles" icon={Users}>
                                                <ResponsiveContainer width="100%" height={300}>
                                                    <PieChart>
                                                        <Pie
                                                            data={participantRolesData}
                                                            dataKey="value"
                                                            label={({ name, percent }) => `${name} (${(percent * 100).toFixed(0)}%)`}
                                                            outerRadius={90}
                                                            fill="#8884d8"
                                                            labelLine={false}
                                                            animationDuration={1000}
                                                            animationBegin={300}
                                                        >
                                                            {participantRolesData.map((entry, index) => (
                                                                <Cell key={`cell-roles-${index}`} fill={ROLE_COLORS[index % ROLE_COLORS.length]} />
                                                            ))}
                                                        </Pie>
                                                        <Tooltip contentStyle={{ borderRadius: '8px', boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)' }} />
                                                    </PieChart>
                                                </ResponsiveContainer>
                                            </ChartContainer>
                                        ) : (
                                            <EmptyChartPlaceholder title="Répartition des rôles" icon={PieChartLucide} />
                                        )}

                                        {/* Dreaded Events Chart */}
                                        {(securityPillarEventsData.length > 0) ? (
                                            <ChartContainer title="Événements redoutés par pilier" icon={AlertTriangle}>
                                                <ResponsiveContainer width="100%" height={300}>
                                                    <BarChart
                                                        data={securityPillarEventsData}
                                                        layout="vertical"
                                                        margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                                                        barGap={2}
                                                        barCategoryGap={10}
                                                    >
<CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                                                        <XAxis type="number" />
                                                        <YAxis dataKey="pillar" type="category" width={80} />
                                                        <Tooltip
                                                            contentStyle={{
                                                                borderRadius: '8px',
                                                                boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
                                                                border: 'none'
                                                            }}
                                                        />
                                                        <Legend />
                                                        {Object.keys(SEVERITY_COLORS).map((severityKey) => (
                                                            <Bar
                                                                key={severityKey}
                                                                dataKey={severityKey}
                                                                stackId="a"
                                                                fill={SEVERITY_COLORS[severityKey]}
                                                                name={severityKey.charAt(0).toUpperCase() + severityKey.slice(1)}
                                                                radius={[4, 4, 0, 0]}
                                                                animationDuration={1000}
                                                                animationBegin={300}
                                                            />
                                                        ))}
                                                    </BarChart>
                                                </ResponsiveContainer>
                                            </ChartContainer>
                                        ) : (
                                            <EmptyChartPlaceholder title="Événements redoutés par pilier" icon={BarChartLucide}/>
                                        )}
                                    </motion.div>

                                    <motion.div variants={itemVariants} className="grid grid-cols-1 lg:grid-cols-2 gap-6 mt-6">
                                        {/* Business Value Pillars Chart */}
                                        {(businessValuePillarsData.length > 0) ? (
                                            <ChartContainer title="Piliers des valeurs métier" icon={Target}>
                                                <ResponsiveContainer width="100%" height={300}>
                                                    <PieChart>
                                                        <Pie
                                                            data={businessValuePillarsData}
                                                            dataKey="value"
                                                            label={({ name, percent }) => `${name} (${(percent * 100).toFixed(0)}%)`}
                                                            outerRadius={90}
                                                            fill="#8884d8"
                                                            labelLine={false}
                                                            animationDuration={1000}
                                                            animationBegin={300}
                                                        >
                                                            {businessValuePillarsData.map((entry, index) => (
                                                                <Cell key={`cell-bv-${index}`} fill={PILLAR_COLORS[index % PILLAR_COLORS.length]} />
                                                            ))}
                                                        </Pie>
                                                        <Tooltip contentStyle={{ borderRadius: '8px', boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)' }} />
                                                    </PieChart>
                                                </ResponsiveContainer>
                                            </ChartContainer>
                                        ) : (
                                            <EmptyChartPlaceholder title="Piliers des valeurs métier" icon={PieChartLucide}/>
                                        )}

                                        {/* Risk Matrix Chart */}
                                        <ChartContainer title="Matrice des risques" icon={TrendingUp}>
                                            {events?.length > 0 ? (
                                                <div className="relative h-[300px]">
                                                    <div className="absolute inset-0">
                                                        <div className="grid grid-cols-5 grid-rows-5 h-full">
                                                            {/* Background grid with color gradients */}
                                                            {Array.from({ length: 25 }).map((_, index) => {
                                                                const row = Math.floor(index / 5);
                                                                const col = index % 5;

                                                                // Calculate risk score (higher row = higher impact, higher col = higher likelihood)
                                                                const riskScore = (5 - row) * (col + 1);

                                                                // Determine color based on risk score
                                                                let bgColor = 'bg-green-100';
                                                                if (riskScore >= 20) bgColor = 'bg-red-100';
                                                                else if (riskScore >= 12) bgColor = 'bg-orange-100';
                                                                else if (riskScore >= 8) bgColor = 'bg-amber-100';

                                                                return (
                                                                    <div
                                                                        key={`cell-${row}-${col}`}
                                                                        className={`border border-gray-100 ${bgColor}`}
                                                                    ></div>
                                                                );
                                                            })}
                                                        </div>

                                                        {/* Axis labels */}
                                                        <div className="absolute left-0 top-0 h-full flex flex-col justify-between py-2 -ml-14">
                                                            <span className="text-xs text-gray-500">Très élevé</span>
                                                            <span className="text-xs text-gray-500">Élevé</span>
                                                            <span className="text-xs text-gray-500">Moyen</span>
                                                            <span className="text-xs text-gray-500">Faible</span>
                                                            <span className="text-xs text-gray-500">Très faible</span>
                                                        </div>

                                                        <div className="absolute bottom-0 left-0 w-full flex justify-between px-2 -mb-6">
                                                            <span className="text-xs text-gray-500">Rare</span>
                                                            <span className="text-xs text-gray-500">Peu probable</span>
                                                            <span className="text-xs text-gray-500">Possible</span>
                                                            <span className="text-xs text-gray-500">Probable</span>
                                                            <span className="text-xs text-gray-500">Fréquent</span>
                                                        </div>

                                                        {/* Event dots */}
                                                        {events.slice(0, 8).map((event, index) => {
                                                            // Convert severity to impact (row position)
                                                            const severityMap = {
                                                                'minor': 4,
                                                                'moderate': 3,
                                                                'major': 2,
                                                                'critical': 1,
                                                                'catastrophic': 0
                                                            };
                                                            const row = severityMap[event.severity] || 3;

                                                            // Randomize column for visualization purposes (in a real app, use actual likelihood data)
                                                            const col = index % 5;

                                                            // Position with slight random offset to avoid exact overlaps
                                                            const offsetX = (Math.random() * 0.6 + 0.2) * 100; // 20% to 80% of cell width
                                                            const offsetY = (Math.random() * 0.6 + 0.2) * 100; // 20% to 80% of cell height

                                                            return (
                                                                <motion.div
                                                                    key={event.id}
                                                                    initial={{ scale: 0, opacity: 0 }}
                                                                    animate={{ scale: 1, opacity: 1 }}
                                                                    transition={{ delay: index * 0.1 }}
                                                                    className="absolute w-8 h-8 -ml-4 -mt-4 flex items-center justify-center"
                                                                    style={{
                                                                        left: `${(col * 20) + offsetX}%`,
                                                                        top: `${(row * 20) + offsetY}%`
                                                                    }}
                                                                >
                                                                    <div className={`w-5 h-5 rounded-full flex items-center justify-center text-xs font-bold ${
                                                                        event.severity === 'catastrophic' || event.severity === 'critical' ?
                                                                        'bg-red-500 text-white' :
                                                                        event.severity === 'major' ?
                                                                        'bg-orange-500 text-white' :
                                                                        'bg-amber-500 text-white'
                                                                    }`} title={event.name}>
                                                                        {index + 1}
                                                                    </div>
                                                                </motion.div>
                                                            );
                                                        })}

                                                        {/* Axis titles */}
                                                        <div className="absolute -left-8 top-1/2 -translate-y-1/2 -rotate-90 text-xs font-medium text-gray-700">
                                                            Impact
                                                        </div>
                                                        <div className="absolute bottom-0 left-1/2 -translate-x-1/2 -mb-10 text-xs font-medium text-gray-700">
                                                            Probabilité
                                                        </div>
                                                    </div>
                                                </div>
                                            ) : (
                                                <div className="h-[300px] flex flex-col items-center justify-center text-center bg-gray-50 rounded-lg">
                                                    <TrendingUp size={48} className="text-gray-300 mb-3" />
                                                    <p className="text-sm text-gray-500 font-medium">Matrice de risques indisponible</p>
                                                    <p className="text-xs text-gray-400 max-w-xs mt-1">
                                                        Ajoutez des événements redoutés pour générer votre matrice de risques
                                                    </p>
                                                </div>
                                            )}
                                        </ChartContainer>
                                    </motion.div>

                                    <motion.div variants={itemVariants} className="grid grid-cols-1 lg:grid-cols-2 gap-6 mt-6">
                                        {/* Security Status Summary */}
                                        <ChartContainer title="Statut global de sécurité" icon={Shield}>
                                            <div className="p-4">
                                                <div className="mb-6 flex justify-center">
                                                    <motion.div
                                                        initial={{ opacity: 0, scale: 0.8 }}
                                                        animate={{ opacity: 1, scale: 1 }}
                                                        transition={{ delay: 0.5, duration: 0.5 }}
                                                        className="relative w-32 h-32"
                                                    >
                                                        {/* Circular progress indicator */}
                                                        <svg className="w-full h-full" viewBox="0 0 100 100">
                                                            {/* Background circle */}
                                                            <circle
                                                                cx="50" cy="50" r="45"
                                                                fill="none"
                                                                stroke="#f0f0f0"
                                                                strokeWidth="8"
                                                            />

                                                            {/* Progress circle - assuming 65% completion for demo */}
                                                            <motion.circle
                                                                cx="50" cy="50" r="45"
                                                                fill="none"
                                                                stroke="#3B82F6"
                                                                strokeWidth="8"
                                                                strokeLinecap="round"
                                                                strokeDasharray="283"  // 2πr
                                                                initial={{ strokeDashoffset: 283 }}
                                                                animate={{ strokeDashoffset: 283 * (1 - (controlsData?.length ? 0.65 : 0)) }}
                                                                transition={{ duration: 1, delay: 0.8 }}
                                                                transform="rotate(-90 50 50)"
                                                            />
                                                        </svg>
                                                        <div className="absolute top-0 left-0 w-full h-full flex items-center justify-center">
                                                            <div className="text-center">
                                                                <span className="text-2xl font-bold text-gray-800">65%</span>
                                                                <span className="block text-xs text-gray-500">Sécurité</span>
                                                            </div>
                                                        </div>
                                                    </motion.div>
                                                </div>

                                                <div className="space-y-3">
                                                    {/* Exemple de statistiques de sécurité */}
                                                    <div className="flex justify-between items-center">
                                                        <span className="text-sm text-gray-600">Mesures en place</span>
                                                        <div className="flex items-center">
                                                            <span className="text-sm font-medium text-gray-800">
                                                                {controlsData?.filter(c => c.status === 'implemented')?.length || 0}
                                                            </span>
                                                            <span className="text-xs text-gray-500 ml-1">
                                                                /{controlsData?.length || 0}
                                                            </span>
                                                        </div>
                                                    </div>

                                                    <div>
                                                        <div className="flex justify-between items-center mb-1">
                                                            <span className="text-sm text-gray-600">Risques critiques mitigés</span>
                                                            <span className="text-sm font-medium text-gray-800">78%</span>
                                                        </div>
                                                        <div className="h-2 bg-gray-100 rounded-full overflow-hidden">
                                                            <motion.div
                                                                className="h-full bg-green-500 rounded-full"
                                                                initial={{ width: 0 }}
                                                                animate={{ width: '78%' }}
                                                                transition={{ duration: 0.8, delay: 1 }}
                                                            ></motion.div>
                                                        </div>
                                                    </div>

                                                    <div>
                                                        <div className="flex justify-between items-center mb-1">
                                                            <span className="text-sm text-gray-600">Conformité au référentiel</span>
                                                            <span className="text-sm font-medium text-gray-800">52%</span>
                                                        </div>
                                                        <div className="h-2 bg-gray-100 rounded-full overflow-hidden">
                                                            <motion.div
                                                                className="h-full bg-amber-500 rounded-full"
                                                                initial={{ width: 0 }}
                                                                animate={{ width: '52%' }}
                                                                transition={{ duration: 0.8, delay: 1.2 }}
                                                            ></motion.div>
                                                        </div>
                                                    </div>
                                                </div>

                                                <div className="mt-4 pt-3 border-t border-gray-200">
                                                    <h4 className="text-sm font-medium text-gray-700 mb-2">Prochaines étapes recommandées:</h4>
                                                    <ul className="text-xs text-gray-600 space-y-1">
                                                        <li className="flex items-start">
                                                            <span className="text-blue-500 mr-1.5">•</span>
                                                            Compléter l'analyse des événements redoutés
                                                        </li>
                                                        <li className="flex items-start">
                                                            <span className="text-blue-500 mr-1.5">•</span>
                                                            Définir les mesures de sécurité pour les risques critiques
                                                        </li>
                                                    </ul>
                                                </div>
                                            </div>
                                        </ChartContainer>

                                        {/* New Component: Recent Activities */}
                                        <ChartContainer title="Activités récentes" icon={Clock}>
                                            <div className="p-2">
                                                <div className="space-y-3 max-h-[275px] overflow-y-auto pr-2">
                                                    {[
                                                        { id: 1, type: 'control', action: 'add', user: 'Thomas D.', time: '2h', item: 'Contrôle d\'accès réseau' },
                                                        { id: 2, type: 'event', action: 'update', user: 'Sophie M.', time: '4h', item: 'Fuite de données clients' },
                                                        { id: 3, type: 'participant', action: 'add', user: 'Marc L.', time: '1j', item: 'Jean Dupont (RSSI)' },
                                                        { id: 4, type: 'value', action: 'update', user: 'Alice R.', time: '1j', item: 'Confidentialité des données' },
                                                        { id: 5, type: 'control', action: 'delete', user: 'Pierre K.', time: '2j', item: 'Scan de vulnérabilités' },
                                                        { id: 6, type: 'event', action: 'add', user: 'Marie C.', time: '3j', item: 'Attaque par déni de service' }
                                                    ].map(activity => (
                                                        <motion.div
                                                            key={activity.id}
                                                            initial={{ opacity: 0, x: -20 }}
                                                            animate={{ opacity: 1, x: 0 }}
                                                            transition={{ delay: activity.id * 0.1 }}
                                                            className="flex items-start p-2 rounded-lg hover:bg-gray-50"
                                                        >
                                                            <div className={`p-2 rounded-full mr-3 ${
                                                                activity.action === 'add' ? 'bg-green-100' :
                                                                activity.action === 'update' ? 'bg-blue-100' :
                                                                'bg-red-100'
                                                            }`}>
                                                                {activity.action === 'add' ? <CheckCircle size={16} className="text-green-600" /> :
                                                                 activity.action === 'update' ? <RefreshCw size={16} className="text-blue-600" /> :
                                                                 <XCircle size={16} className="text-red-600" />}
                                                            </div>
                                                            <div className="flex-grow">
                                                                <div className="flex justify-between items-start">
                                                                    <p className="text-sm font-medium text-gray-700">
                                                                        {activity.user}
                                                                        <span className="font-normal text-gray-500"> a {
                                                                            activity.action === 'add' ? 'ajouté' :
                                                                            activity.action === 'update' ? 'modifié' :
                                                                            'supprimé'
                                                                        }
                                                                        {activity.type === 'control' ? ' une mesure ' :
                                                                         activity.type === 'event' ? ' un événement ' :
                                                                         activity.type === 'participant' ? ' un participant ' :
                                                                         ' une valeur '}</span>
                                                                    </p>
                                                                    <span className="text-xs text-gray-400">{activity.time}</span>
                                                                </div>
                                                                <p className="text-xs text-gray-600 mt-0.5">{activity.item}</p>
                                                            </div>
                                                        </motion.div>
                                                    ))}
                                                </div>
                                                <div className="pt-2 pb-1 text-center">
                                                    <button className="text-sm text-blue-600 hover:text-blue-800 hover:underline">
                                                        Voir toutes les activités
                                                    </button>
                                                </div>
                                            </div>
                                        </ChartContainer>
                                    </motion.div>
                                </motion.div>
                            )}

                            {/* Participants Section */}
                            {activeSection === 'participants' && (
                                <motion.div variants={containerVariants}>
                                    {(context?.participants?.length > 0) ? (
                                        <InfoSection title="Liste des participants" icon={Users}>
                                            <div className="p-4">
                                                <div className="mb-4 flex flex-wrap gap-2 justify-between items-center">
                                                    <div className="flex items-center gap-2">
                                                        <div className="relative">
                                                            <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                                                                <svg className="w-4 h-4 text-gray-500" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 20 20">
                                                                    <path stroke="currentColor" strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="m19 19-4-4m0-7A7 7 0 1 1 1 8a7 7 0 0 1 14 0Z"/>
                                                                </svg>
                                                            </div>
                                                            <input type="search" className="block w-full p-2 pl-10 text-sm border border-gray-300 rounded-lg bg-gray-50 focus:ring-blue-500 focus:border-blue-500" placeholder="Rechercher un participant..." />
                                                        </div>
                                                        <button className="p-2 text-gray-500 bg-gray-100 hover:bg-gray-200 rounded-lg">
                                                            <Filter size={18} />
                                                        </button>
                                                    </div>
                                                    <button className="px-4 py-2 text-white bg-blue-600 rounded-lg hover:bg-blue-700 transition-colors flex items-center">
                                                        <Users size={16} className="mr-2" /> Ajouter un participant
                                                    </button>
                                                </div>

                                                <div className="overflow-x-auto">
                                                    <table className="min-w-full divide-y divide-gray-200 rounded-lg overflow-hidden">
                                                        <thead className="bg-gray-50">
                                                            <tr>
                                                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Nom</th>
                                                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Fonction</th>
                                                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Rôles RACI</th>
                                                                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                                                            </tr>
                                                        </thead>
                                                        <tbody className="bg-white divide-y divide-gray-200">
                                                            {context.participants.map((p) => (
                                                                <motion.tr
                                                                    key={p.id}
                                                                    whileHover={{ backgroundColor: 'rgba(249, 250, 251, 0.5)' }}
                                                                >
                                                                    <td className="px-6 py-4 whitespace-nowrap">
                                                                        <div className="flex items-center">
                                                                            <div className="flex-shrink-0 h-8 w-8 rounded-full bg-blue-100 flex items-center justify-center text-blue-800 font-medium">
                                                                                {(p.name || 'U').charAt(0).toUpperCase()}
                                                                            </div>
                                                                            <div className="ml-3">
                                                                                <div className="text-sm font-medium text-gray-900">{p.name || 'Sans nom'}</div>
                                                                                <div className="text-xs text-gray-500">{p.email || ''}</div>
                                                                            </div>
                                                                        </div>
                                                                    </td>
                                                                    <td className="px-6 py-4 whitespace-nowrap">
                                                                        <div className="text-sm text-gray-500">
                                                                            {(p.position === "OTHER" && p.customPosition) ?
                                                                                p.customPosition :
                                                                                (p.position || 'Non défini')}
                                                                        </div>
                                                                    </td>
                                                                    <td className="px-6 py-4 whitespace-nowrap">
                                                                        {context.matrix && context.matrix[p.id] ? (
                                                                            <div className="flex space-x-1">
                                                                                {Object.entries(context.matrix[p.id]).map(([w, r]) => r && (
                                                                                    <span
                                                                                        key={`${p.id}-${w}`}
                                                                                        className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium"
                                                                                        style={{
                                                                                            backgroundColor: r === 'R' ? '#d1fae5' : r === 'A' ? '#dbeafe' : r === 'C' ? '#fef3c7' : r === 'I' ? '#fee2e2' : '#f3f4f6',
                                                                                            color: r === 'R' ? '#065f46' : r === 'A' ? '#1e40af' : r === 'C' ? '#92400e' : r === 'I' ? '#991b1b' : '#374151'
                                                                                        }}
                                                                                    >
                                                                                        {r}
                                                                                    </span>
                                                                                ))}
                                                                            </div>
                                                                        ) : ('Aucun rôle')}
                                                                    </td>
                                                                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm">
                                                                        <button className="text-blue-600 hover:text-blue-800 px-2 py-1 rounded hover:bg-blue-50 transition-colors">
                                                                            Détails
                                                                        </button>
                                                                    </td>
                                                                </motion.tr>
                                                            ))}
                                                        </tbody>
                                                    </table>
                                                </div>
                                                <div className="flex justify-end mt-4">
                                                    <button className="px-4 py-2 text-sm text-blue-600 bg-blue-50 rounded-lg hover:bg-blue-100 transition-colors flex items-center">
                                                        <ExternalLink size={16} className="mr-1" /> Voir tous les participants
                                                    </button>
                                                </div>
                                            </div>
                                        </InfoSection>
                                    ) : (
                                        <InfoSection title="Liste des participants" icon={Users}>
                                            <div className="p-8 text-center">
                                                <Users size={48} className="mx-auto mb-3 text-gray-300" />
                                                <h3 className="text-gray-600 font-medium mb-1">Aucun participant trouvé</h3>
                                                <p className="text-gray-500 text-sm mb-4">Ajoutez des participants pour commencer votre analyse</p>
                                                <button className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                                                    Ajouter des participants
                                                </button>
                                            </div>
                                        </InfoSection>
                                    )}
                                </motion.div>
                            )}

                            {/* Dreaded Events Section */}
                            {activeSection === 'events' && (
                                <motion.div variants={containerVariants}>
                                    {(events?.length > 0) ? (
                                        <InfoSection title="Événements redoutés" icon={AlertTriangle}>
                                            <div className="p-4">
                                                <div className="mb-4 flex flex-wrap gap-2 justify-between items-center">
                                                    <div className="flex items-center gap-2">
                                                        <div className="relative">
                                                            <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                                                                <svg className="w-4 h-4 text-gray-500" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 20 20">
                                                                    <path stroke="currentColor" strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="m19 19-4-4m0-7A7 7 0 1 1 1 8a7 7 0 0 1 14 0Z"/>
                                                                </svg>
                                                            </div>
                                                            <input type="search" className="block w-full p-2 pl-10 text-sm border border-gray-300 rounded-lg bg-gray-50 focus:ring-blue-500 focus:border-blue-500" placeholder="Rechercher un événement..." />
                                                        </div>
                                                        <select className="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block p-2">
                                                            <option value="">Tous les piliers</option>
                                                            <option value="confidentiality">Confidentialité</option>
                                                            <option value="integrity">Intégrité</option>
                                                            <option value="availability">Disponibilité</option>
                                                        </select>
                                                        <select className="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block p-2">
                                                            <option value="">Toutes les sévérités</option>
                                                            <option value="catastrophic">Catastrophique</option>
                                                            <option value="critical">Critique</option>
                                                            <option value="major">Majeur</option>
                                                            <option value="moderate">Modéré</option>
                                                            <option value="minor">Mineur</option>
                                                        </select>
                                                    </div>
                                                    <button className="px-4 py-2 text-white bg-amber-600 rounded-lg hover:bg-amber-700 transition-colors flex items-center">
                                                        <AlertTriangle size={16} className="mr-2" /> Ajouter un événement
                                                    </button>
                                                </div>

                                                <div className="space-y-4">
                                                    {events.map((event) => (
                                                        <motion.div
                                                            key={event.id}
                                                            className="border rounded-xl p-5 hover:shadow-md transition-shadow duration-200 bg-white"
                                                            whileHover={{ y: -2, boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1)' }}
                                                        >
                                                            <div className="flex justify-between items-start">
                                                                <h4 className="font-medium text-gray-800 flex items-center">
                                                                    <AlertTriangle size={16} className="mr-2 text-amber-500" />
                                                                    {event.name}
                                                                </h4>
                                                                <span className={`inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium border ${
                                                                    {
                                                                        minor: 'bg-emerald-50 text-emerald-700 border-emerald-200',
                                                                        moderate: 'bg-amber-50 text-amber-700 border-amber-200',
                                                                        major: 'bg-orange-50 text-orange-800 border-orange-200',
                                                                        critical: 'bg-red-50 text-red-700 border-red-200',
                                                                        catastrophic: 'bg-slate-900 text-slate-50 border-slate-700'
                                                                    }[event.severity] || 'bg-gray-100 text-gray-800 border-gray-200'
                                                                }`}>
                                                                    {event.severity?.charAt(0).toUpperCase() + event.severity?.slice(1) || 'Non défini'}
                                                                </span>
                                                            </div>
                                                            <p className="text-sm text-gray-600 mt-3 bg-gray-50 p-3 rounded-lg">
                                                                {event.description}
                                                            </p>
                                                            <div className="mt-4 flex flex-wrap gap-2">
                                                                {event.securityPillar && (
                                                                    <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                                                                        <Shield size={12} className="mr-1" />
                                                                        {event.securityPillar}
                                                                    </span>
                                                                )}
                                                                {event.businessValue && (
                                                                    <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                                                        <Target size={12} className="mr-1" />
                                                                        {bValues.find(bv => bv.id === event.businessValue)?.name || event.businessValue}
                                                                    </span>
                                                                )}
                                                                <div className="flex-grow"></div>
                                                                <button className="text-xs text-blue-600 hover:text-blue-800 hover:underline">Modifier</button>
                                                                <button className="text-xs text-gray-500 hover:text-gray-700 hover:underline">Détails</button>
                                                            </div>
                                                        </motion.div>
                                                    ))}
                                                </div>

                                                {events.length > 5 && (
                                                    <div className="mt-6 flex justify-center">
                                                        <button className="px-4 py-2 bg-white border border-gray-300 rounded-lg shadow-sm text-sm text-gray-700 hover:bg-gray-50 flex items-center">
                                                            <ChevronDown size={16} className="mr-1" /> Voir plus d'événements
                                                        </button>
                                                    </div>
                                                )}
                                            </div>
                                        </InfoSection>
                                    ) : (
                                        <InfoSection title="Événements redoutés" icon={AlertTriangle}>
                                            <div className="p-8 text-center">
                                                <AlertTriangle size={48} className="mx-auto mb-3 text-gray-300" />
                                                <h3 className="text-gray-600 font-medium mb-1">Aucun événement redouté défini</h3>
                                                <p className="text-gray-500 text-sm mb-4">
                                                    Identifiez les événements redoutés pour continuer l'analyse des risques
                                                </p>
                                                <button className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                                                    Définir des événements
                                                </button>
                                            </div>
                                        </InfoSection>
                                    )}
                                </motion.div>
                            )}

                            {/* Security Measures Section */}
                            {activeSection === 'security' && (
                                <motion.div variants={containerVariants}>
                                    {(controlsData?.length > 0) ? (
                                        <InfoSection title="Mesures de sécurité" icon={Shield}>
                                            <div className="p-4">
                                                <div className="mb-4 flex flex-col md:flex-row flex-wrap md:items-center gap-3 justify-between">
                                                    <div className="flex items-center gap-2 flex-wrap">
                                                        <div className="relative">
                                                            <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                                                                <svg className="w-4 h-4 text-gray-500" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 20 20">
                                                                    <path stroke="currentColor" strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="m19 19-4-4m0-7A7 7 0 1 1 1 8a7 7 0 0 1 14 0Z"/>
                                                                </svg>
                                                            </div>
                                                            <input type="search" className="block w-full p-2 pl-10 text-sm border border-gray-300 rounded-lg bg-gray-50 focus:ring-blue-500 focus:border-blue-500" placeholder="Rechercher une mesure..." />
                                                        </div>

                                                        <div className="flex">
                                                            <motion.button
                                                                variants={buttonGroupVariants}
                                                                animate={securityFilter === 'all' ? 'active' : 'inactive'}
                                                                whileHover={{ backgroundColor: securityFilter === 'all' ? '#4338CA' : '#F3F4F6' }}
                                                                onClick={() => setSecurityFilter('all')}
                                                                className="px-3 py-2 text-sm font-medium rounded-l-lg border border-r-0 flex items-center"
                                                            >
                                                                Tous
                                                            </motion.button>
                                                            <motion.button
                                                                variants={buttonGroupVariants}
                                                                animate={securityFilter === 'implemented' ? 'active' : 'inactive'}
                                                                whileHover={{ backgroundColor: securityFilter === 'implemented' ? '#4338CA' : '#F3F4F6' }}
                                                                onClick={() => setSecurityFilter('implemented')}
                                                                className="px-3 py-2 text-sm font-medium border border-r-0 flex items-center"
                                                            >
                                                                <CheckCircle size={14} className="mr-1" /> En place
                                                            </motion.button>
                                                            <motion.button
                                                                variants={buttonGroupVariants}
                                                                animate={securityFilter === 'partial' ? 'active' : 'inactive'}
                                                                whileHover={{ backgroundColor: securityFilter === 'partial' ? '#4338CA' : '#F3F4F6' }}
                                                                onClick={() => setSecurityFilter('partial')}
                                                                className="px-3 py-2 text-sm font-medium border border-r-0 flex items-center"
                                                            >
                                                                <Clock size={14} className="mr-1" /> Partiel
                                                            </motion.button>
                                                            <motion.button
                                                                variants={buttonGroupVariants}
                                                                animate={securityFilter === 'planned' ? 'active' : 'inactive'}
                                                                whileHover={{ backgroundColor: securityFilter === 'planned' ? '#4338CA' : '#F3F4F6' }}
                                                                onClick={() => setSecurityFilter('planned')}
                                                                className="px-3 py-2 text-sm font-medium rounded-r-lg border flex items-center"
                                                            >
                                                                <Calendar size={14} className="mr-1" /> Planifié
                                                            </motion.button>
                                                        </div>
                                                    </div>

                                                    <div className="flex items-center gap-2">
                                                        <div className="flex">
                                                            <motion.button
                                                                variants={buttonGroupVariants}
                                                                animate={securityView === 'grid' ? 'active' : 'inactive'}
                                                                whileHover={{ backgroundColor: securityView === 'grid' ? '#4338CA' : '#F3F4F6' }}
                                                                onClick={() => setSecurityView('grid')}
                                                                className="px-3 py-2 text-sm font-medium rounded-l-lg border border-r-0 flex items-center"
                                                            >
                                                                <svg xmlns="http://www.w3.org/2000/svg" className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zm10 0a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zm10 0a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z" />
                                                                </svg>
                                                            </motion.button>
                                                            <motion.button
                                                                variants={buttonGroupVariants}
                                                                animate={securityView === 'list' ? 'active' : 'inactive'}
                                                                whileHover={{ backgroundColor: securityView === 'list' ? '#4338CA' : '#F3F4F6' }}
                                                                onClick={() => setSecurityView('list')}
                                                                className="px-3 py-2 text-sm font-medium rounded-r-lg border flex items-center"
                                                            >
                                                                <svg xmlns="http://www.w3.org/2000/svg" className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                                                                </svg>
                                                            </motion.button>
                                                        </div>

                                                        <button className="px-4 py-2 text-white bg-purple-600 rounded-lg hover:bg-purple-700 transition-colors flex items-center">
                                                            <Shield size={16} className="mr-2" /> Ajouter une mesure
                                                        </button>
                                                    </div>
                                                </div>

                                                <div className="bg-blue-50 p-3 rounded-lg mb-4 flex items-center justify-between">
                                                    <p className="text-blue-700 text-sm font-medium flex items-center">
                                                        <Info size={16} className="mr-2" />
                                                        {filteredControls.length} contrôles de sécurité affichés sur {controlsData.length} au total
                                                    </p>
                                                    <span className="text-xs text-blue-600 bg-blue-100 px-2 py-1 rounded-full">
                                                        {securityFilter === 'all' ? 'Tous les statuts' :
                                                         securityFilter === 'implemented' ? 'En place uniquement' :
                                                         securityFilter === 'partial' ? 'Partiels uniquement' :
                                                         'Planifiés uniquement'}
                                                    </span>
                                                </div>

                                                {securityView === 'grid' ? (
                                                    <div className="grid gap-3 md:grid-cols-2">
                                                        {filteredControls.map(control => (
                                                            <motion.div
                                                                key={control.id}
                                                                className="border rounded-lg p-3 bg-white hover:shadow-sm transition-all"
                                                                whileHover={{ backgroundColor: 'rgba(249, 250, 251, 0.8)' }}
                                                            >
                                                                <div className="flex justify-between items-start">
                                                                    <h4 className="font-medium text-gray-800 text-sm">{control.name}</h4>
                                                                    <span className={`px-2 py-0.5 rounded-full text-xs ${
                                                                        control.status === 'implemented' ? 'bg-green-100 text-green-800' :
                                                                        control.status === 'partial' ? 'bg-amber-100 text-amber-800' :
                                                                        control.status === 'planned' ? 'bg-blue-100 text-blue-800' :
                                                                        'bg-gray-100 text-gray-800'
                                                                    }`}>
                                                                        {control.status === 'implemented' ? 'En place' :
                                                                         control.status === 'partial' ? 'Partiel' :
                                                                         control.status === 'planned' ? 'Planifié' :
                                                                         control.status || 'Non défini'}
                                                                    </span>
                                                                </div>
                                                                <p className="text-xs text-gray-500 mt-1">
                                                                    {control.description?.substring(0, 100) || 'Aucune description disponible'}
                                                                    {control.description?.length > 100 ? '...' : ''}
                                                                </p>
                                                                <div className="flex justify-between items-center mt-2">
                                                                    <div className="flex items-center">
                                                                        <span className="bg-purple-100 text-purple-800 text-xs px-2 py-0.5 rounded-full">
                                                                            {control.type || 'Indéfini'}
                                                                        </span>
                                                                    </div>
                                                                    <button className="text-xs text-blue-600 hover:text-blue-800">Détails</button>
                                                                </div>
                                                            </motion.div>
                                                        ))}
                                                    </div>
                                                ) : (
                                                    <div className="overflow-x-auto rounded-lg border">
                                                        <table className="min-w-full divide-y divide-gray-200">
                                                            <thead className="bg-gray-50">
                                                                <tr>
                                                                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Mesure</th>
                                                                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Statut</th>
                                                                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                                                                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Priorité</th>
                                                                    <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                                                                </tr>
                                                            </thead>
                                                            <tbody className="bg-white divide-y divide-gray-200">
                                                                {filteredControls.map(control => (
                                                                    <tr key={control.id} className="hover:bg-gray-50">
                                                                        <td className="px-6 py-4 whitespace-nowrap">
                                                                            <div className="flex items-center">
                                                                                <div className="text-sm font-medium text-gray-900">{control.name}</div>
                                                                            </div>
                                                                        </td>
                                                                        <td className="px-6 py-4 whitespace-nowrap">
                                                                            <span className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${
                                                                                control.status === 'implemented' ? 'bg-green-100 text-green-800' :
                                                                                control.status === 'partial' ? 'bg-amber-100 text-amber-800' :
                                                                                control.status === 'planned' ? 'bg-blue-100 text-blue-800' :
                                                                                'bg-gray-100 text-gray-800'
                                                                            }`}>
                                                                                {control.status === 'implemented' ? 'En place' :
                                                                                control.status === 'partial' ? 'Partiel' :
                                                                                control.status === 'planned' ? 'Planifié' :
                                                                                control.status || 'Non défini'}
                                                                            </span>
                                                                        </td>
                                                                        <td className="px-6 py-4 whitespace-nowrap">
                                                                            <div className="text-sm text-gray-500">{control.type || 'Indéfini'}</div>
                                                                        </td>
                                                                        <td className="px-6 py-4 whitespace-nowrap">
                                                                            <div className="text-sm text-gray-500">{control.priority || 'Moyenne'}</div>
                                                                        </td>
                                                                        <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                                                            <button className="text-blue-600 hover:text-blue-900 mr-2">Modifier</button>
                                                                            <button className="text-gray-600 hover:text-gray-900">Détails</button>
                                                                        </td>
                                                                    </tr>
                                                                ))}
                                                            </tbody>
                                                        </table>
                                                    </div>
                                                )}

                                                {filteredControls.length > 10 && (
                                                    <div className="flex justify-center mt-6">
                                                        <button className="px-4 py-2 text-sm text-blue-600 bg-blue-50 hover:bg-blue-100 rounded-lg transition-colors flex items-center">
                                                            <ChevronDown size={16} className="mr-1" />
                                                            Voir plus de mesures
                                                        </button>
                                                    </div>
                                                )}
                                            </div>
                                        </InfoSection>
                                    ) : (
                                        <InfoSection title="Mesures de sécurité" icon={Shield}>
                                            <div className="p-8 text-center">
                                                <Shield size={48} className="mx-auto mb-3 text-gray-300" />
                                                <h3 className="text-gray-600 font-medium mb-1">Aucune mesure de sécurité définie</h3>
                                                <p className="text-gray-500 text-sm mb-4">
                                                    Définissez des mesures de sécurité pour vous protéger contre les événements redoutés
                                                </p>
                                                <button className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                                                    Ajouter des mesures
                                                </button>
                                            </div>
                                        </InfoSection>
                                    )}
                                </motion.div>
                            )}

                            {/* Analysis Section */}
                            {activeSection === 'analysis' && (
                                <motion.div variants={containerVariants}>
                                    <motion.div variants={itemVariants} className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                                        <InfoCard
                                            title="Score de risque global"
                                            value="62.4"
                                            icon={LineChart}
                                            trend={-8}
                                            color="red"
                                        />
                                        <InfoCard
                                            title="Niveau de conformité"
                                            value="71%"
                                            icon={Award}
                                            trend={12}
                                            color="green"
                                        />
                                    </motion.div>

                                    <motion.div variants={itemVariants} className="bg-white rounded-xl shadow-md border border-gray-200 mb-6">
                                        <div className="p-4 border-b border-gray-200">
                                            <h3 className="font-medium text-gray-700 flex items-center">
                                                <LineChart size={18} className="mr-2 text-indigo-600" />
                                                Évolution du niveau de risque
                                            </h3>
                                        </div>
                                        <div className="p-4">
                                            <div className="h-[300px] flex flex-col items-center justify-center bg-gray-50 rounded-xl">
                                                <LineChart size={48} className="text-gray-300 mb-3" />
                                                <p className="text-gray-500 font-medium">Graphique d'évolution du risque</p>
                                                <p className="text-xs text-gray-400 max-w-xs mt-1 mb-4 text-center">
                                                    Visualisez l'évolution du niveau de risque au fil du temps pour suivre l'efficacité de vos mesures de sécurité.
                                                </p>
                                                <button className="px-4 py-2 bg-indigo-600 text-white rounded-lg text-sm">
                                                    Générer le rapport d'analyse
                                                </button>
                                            </div>
                                        </div>
                                    </motion.div>

                                    <motion.div variants={itemVariants} className="grid grid-cols-1 md:grid-cols-3 gap-6">
                                        <ChartContainer title="Impact des mesures" icon={Shield}>
                                            <div className="p-4 text-center">
                                                <div className="flex items-center justify-center h-[240px]">
                                                    <div className="relative inline-flex">
                                                        <div className="w-40 h-40 rounded-full overflow-hidden">
                                                            <div className="w-full h-full bg-emerald-100 flex items-center justify-center">
                                                                <div className="w-32 h-32 rounded-full bg-white flex items-center justify-center text-2xl font-bold text-emerald-700">
                                                                    42%
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div className="absolute -top-1 -right-1 bg-emerald-500 text-white w-8 h-8 rounded-full flex items-center justify-center">
                                                            <Zap size={16} />
                                                        </div>
                                                    </div>
                                                </div>
                                                <p className="text-sm text-gray-500 mt-2">Efficacité des mesures de sécurité</p>
                                            </div>
                                        </ChartContainer>

                                        <ChartContainer title="Répartition des risques" icon={PieChartLucide}>
                                            <div className="p-4 text-center">
                                                <div className="flex flex-col items-center justify-center h-[240px]">
                                                    <div className="grid grid-cols-3 gap-2 w-full max-w-xs mb-4">
                                                        <div className="bg-red-100 rounded-lg p-2 text-center">
                                                            <div className="text-2xl font-bold text-red-700">12</div>
                                                            <div className="text-xs text-red-600">Critiques</div>
                                                        </div>
                                                        <div className="bg-orange-100 rounded-lg p-2 text-center">
                                                            <div className="text-2xl font-bold text-orange-700">24</div>
                                                            <div className="text-xs text-orange-600">Majeurs</div>
                                                        </div>
                                                        <div className="bg-amber-100 rounded-lg p-2 text-center">
                                                            <div className="text-2xl font-bold text-amber-700">38</div>
                                                            <div className="text-xs text-amber-600">Modérés</div>
                                                        </div>
                                                    </div>
                                                    <p className="text-sm text-gray-500">Répartition par niveau de sévérité</p>
                                                </div>
                                            </div>
                                        </ChartContainer>

                                        <ChartContainer title="Actions requises" icon={CheckCircle}>
                                            <div className="p-4">
                                                <div className="space-y-3 h-[240px] overflow-y-auto">
                                                    <div className="bg-blue-50 p-2 rounded-lg border-l-4 border-blue-500">
                                                        <div className="flex items-center">
                                                            <div className="font-medium text-blue-800 text-sm">Mettre à jour les contrôles d'accès</div>
                                                            <span className="ml-auto text-xs text-blue-700 bg-blue-100 px-2 py-0.5 rounded-full">Haute</span>
                                                        </div>
                                                    </div>
                                                    <div className="bg-amber-50 p-2 rounded-lg border-l-4 border-amber-500">
                                                        <div className="flex items-center">
                                                            <div className="font-medium text-amber-800 text-sm">Revoir le plan de continuité</div>
                                                            <span className="ml-auto text-xs text-amber-700 bg-amber-100 px-2 py-0.5 rounded-full">Moyenne</span>
                                                        </div>
                                                    </div>
                                                    <div className="bg-green-50 p-2 rounded-lg border-l-4 border-green-500">
                                                        <div className="flex items-center">
                                                            <div className="font-medium text-green-800 text-sm">Former les utilisateurs</div>
                                                            <span className="ml-auto text-xs text-green-700 bg-green-100 px-2 py-0.5 rounded-full">Normale</span>
                                                        </div>
                                                    </div>
                                                    <div className="bg-purple-50 p-2 rounded-lg border-l-4 border-purple-500">
                                                        <div className="flex items-center">
                                                            <div className="font-medium text-purple-800 text-sm">Mettre à jour les antivirus</div>
<span className="ml-auto text-xs text-purple-700 bg-purple-100 px-2 py-0.5 rounded-full">Moyenne</span>
                                                        </div>
                                                    </div>
                                                    <div className="bg-blue-50 p-2 rounded-lg border-l-4 border-blue-500">
                                                        <div className="flex items-center">
                                                            <div className="font-medium text-blue-800 text-sm">Configurer la surveillance réseau</div>
                                                            <span className="ml-auto text-xs text-blue-700 bg-blue-100 px-2 py-0.5 rounded-full">Haute</span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </ChartContainer>
                                    </motion.div>

                                    <motion.div variants={itemVariants} className="mt-6">
                                        <InfoSection title="Recommandations d'amélioration" icon={HelpCircle} collapsible={true}>
                                            <div className="p-4">
                                                <div className="space-y-4">
                                                    <div className="bg-white rounded-lg border border-gray-200 p-4">
                                                        <div className="flex items-start">
                                                            <div className="bg-red-100 p-2 rounded-full mr-3">
                                                                <AlertTriangle size={16} className="text-red-600" />
                                                            </div>
                                                            <div>
                                                                <h4 className="font-medium text-gray-800 mb-1">Risque élevé: Authentification insuffisante</h4>
                                                                <p className="text-sm text-gray-600 mb-2">
                                                                    Les mécanismes d'authentification actuels ne sont pas suffisamment robustes pour protéger les accès critiques.
                                                                </p>
                                                                <div className="bg-gray-50 p-3 rounded-lg text-sm">
                                                                    <h5 className="font-medium text-gray-700 mb-1">Recommandations:</h5>
                                                                    <ul className="list-disc list-inside text-gray-600 space-y-1">
                                                                        <li>Mettre en place l'authentification multi-facteurs pour tous les accès administrateurs</li>
                                                                        <li>Renforcer la politique de mots de passe</li>
                                                                        <li>Implémenter un système de détection des tentatives d'intrusion</li>
                                                                    </ul>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>

                                                    <div className="bg-white rounded-lg border border-gray-200 p-4">
                                                        <div className="flex items-start">
                                                            <div className="bg-amber-100 p-2 rounded-full mr-3">
                                                                <Shield size={16} className="text-amber-600" />
                                                            </div>
                                                            <div>
                                                                <h4 className="font-medium text-gray-800 mb-1">Risque moyen: Protection des données insuffisante</h4>
                                                                <p className="text-sm text-gray-600 mb-2">
                                                                    La protection des données sensibles ne répond pas aux exigences réglementaires et aux bonnes pratiques.
                                                                </p>
                                                                <div className="bg-gray-50 p-3 rounded-lg text-sm">
                                                                    <h5 className="font-medium text-gray-700 mb-1">Recommandations:</h5>
                                                                    <ul className="list-disc list-inside text-gray-600 space-y-1">
                                                                        <li>Mettre en œuvre le chiffrement des données sensibles au repos et en transit</li>
                                                                        <li>Effectuer un audit complet des accès aux données sensibles</li>
                                                                        <li>Établir une procédure de gestion des incidents liés aux données</li>
                                                                    </ul>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>

                                                    <div className="bg-white rounded-lg border border-gray-200 p-4">
                                                        <div className="flex items-start">
                                                            <div className="bg-green-100 p-2 rounded-full mr-3">
                                                                <Eye size={16} className="text-green-600" />
                                                            </div>
                                                            <div>
                                                                <h4 className="font-medium text-gray-800 mb-1">Risque faible: Visibilité du système insuffisante</h4>
                                                                <p className="text-sm text-gray-600 mb-2">
                                                                    La surveillance et la journalisation des événements système sont insuffisantes pour une détection efficace des incidents.
                                                                </p>
                                                                <div className="bg-gray-50 p-3 rounded-lg text-sm">
                                                                    <h5 className="font-medium text-gray-700 mb-1">Recommandations:</h5>
                                                                    <ul className="list-disc list-inside text-gray-600 space-y-1">
                                                                        <li>Améliorer les mécanismes de journalisation des événements système</li>
                                                                        <li>Mettre en place un système centralisé de gestion des journaux</li>
                                                                        <li>Établir des alertes automatiques pour les comportements anormaux</li>
                                                                    </ul>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>

                                                <div className="flex justify-end mt-4">
                                                    <button className="px-4 py-2 bg-indigo-600 text-white rounded-lg text-sm flex items-center">
                                                        <Download size={16} className="mr-2" />
                                                        Télécharger le rapport complet
                                                    </button>
                                                </div>
                                            </div>
                                        </InfoSection>
                                    </motion.div>
                                </motion.div>
                            )}
                        </div>

                        {/* Enhanced Footer Information */}
                        <motion.div
                            initial={{ opacity: 0 }}
                            animate={{ opacity: 1 }}
                            transition={{ delay: 0.5 }}
                            className="mt-8 text-xs text-gray-500 flex flex-col sm:flex-row items-center gap-4 border-t border-gray-200 pt-4"
                        >
                            <div className="flex items-center gap-x-4 gap-y-1 flex-wrap">
                                <span className="flex items-center bg-gray-100 px-3 py-1.5 rounded-full">
                                    <Calendar size={14} className="mr-1.5 text-blue-500" />
                                    <span className="font-medium text-gray-600">Organisation :</span>
                                    <span className="ml-1.5">{context?.organizationName || currentAnalysis?.companyName || 'N/A'}</span>
                                </span>

                                <span className="flex items-center bg-gray-100 px-3 py-1.5 rounded-full">
                                    <Calendar size={14} className="mr-1.5 text-green-500" />
                                    <span className="font-medium text-gray-600">Date d'analyse :</span>
                                    <span className="ml-1.5">{context?.analysisDate || 'N/A'}</span>
                                </span>

                                <span className="flex items-center bg-gray-100 px-3 py-1.5 rounded-full">
                                    <RefreshCw size={14} className="mr-1.5 text-purple-500" />
                                    <span className="font-medium text-gray-600">Dernière modif.:</span>
                                    <span className="ml-1.5">{currentAnalysis?.updatedAt ? new Date(currentAnalysis.updatedAt).toLocaleString('fr-FR') : 'N/A'}</span>
                                </span>
                            </div>
                            <div className="flex-grow"></div>
                            <button className="text-blue-600 hover:text-blue-800 hover:underline text-xs flex items-center">
                                <Info size={14} className="mr-1.5" />
                                Afficher plus d'informations sur cette analyse
                            </button>
                        </motion.div>
                    </motion.div>
                </AnimatePresence>
            )}

            {/* Enhanced Message when no analysis is selected */}
            {!isWorkshopDataLoading && !currentAnalysis && (
                <motion.div
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="text-center py-16 bg-gradient-to-b from-blue-50 to-gray-50 rounded-xl border border-blue-100 mt-6 px-4"
                >
                    <motion.div
                        initial={{ scale: 0.9, opacity: 0 }}
                        animate={{ scale: 1, opacity: 1 }}
                        transition={{ delay: 0.2, type: "spring", stiffness: 100 }}
                        className="bg-white p-6 rounded-xl shadow-lg inline-block mb-6"
                    >
                        <Info size={64} className="mx-auto text-blue-400" />
                    </motion.div>
                    <h3 className="text-xl text-gray-700 font-medium mb-2">Aucune analyse sélectionnée</h3>
                    <p className="text-gray-500 max-w-md mx-auto mb-6">
                        Veuillez choisir une analyse existante dans le sélecteur ci-dessus ou créez-en une nouvelle pour commencer.
                    </p>
                    <div className="flex flex-col sm:flex-row gap-3 justify-center">
                        <motion.button
                            whileHover={{ scale: 1.03 }}
                            whileTap={{ scale: 0.97 }}
                            className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors shadow-md flex items-center justify-center"
                        >
                            Créer une nouvelle analyse
                        </motion.button>
                        <motion.button
                            whileHover={{ scale: 1.03 }}
                            whileTap={{ scale: 0.97 }}
                            className="px-6 py-3 bg-white text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors shadow-sm flex items-center justify-center"
                        >
                            Voir un exemple
                        </motion.button>
                    </div>
                </motion.div>
            )}
        </motion.div>
    );
};

// Custom hook for dashboard analytics
const useDashboardAnalytics = () => {
    const [isInitialized, setIsInitialized] = useState(false);

    useEffect(() => {
        // Mock analytics initialization
        if (!isInitialized) {
            console.log("Dashboard analytics initialized");
            setIsInitialized(true);
        }

        // Cleanup
        return () => {
            console.log("Dashboard analytics cleanup");
        };
    }, [isInitialized]);

    return {
        trackEvent: (eventName, data) => {
            // Mock analytics tracking
            console.log(`Analytics event: ${eventName}`, data);
        }
    };
};

export default Dashboard;