// src/hooks/useBusinessValues.js
import { useState, useEffect } from 'react';
import { loadBusinessValuesData, loadDataFromAPI } from '../../utils/appStateManagerAPI';

/**
 * Custom hook to fetch and manage business values
 * This provides an alternative to using BusinessValuesContext without modifying App.js
 * 
 * @returns {Object} Object containing business values and related state
 */
const useBusinessValues = () => {
  const [businessValues, setBusinessValues] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);

  // Load business values on component mount
  useEffect(() => {
    const fetchBusinessValues = async () => {
      try {
        setIsLoading(true);
        
        // Try to load from API first, fallback to local storage
        const data = await loadDataFromAPI('business-values');
        
        if (data && Array.isArray(data)) {
          setBusinessValues(data);
        } else {
          // Fallback to local storage
          const localData = loadBusinessValuesData();
          setBusinessValues(localData || []);
        }
      } catch (err) {
        console.error('Error loading business values:', err);
        setError(err.message);
        
        // Fallback to local storage
        const localData = loadBusinessValuesData();
        setBusinessValues(localData || []);
      } finally {
        setIsLoading(false);
      }
    };

    fetchBusinessValues();
  }, []);

  return { businessValues, isLoading, error, setBusinessValues };
};

export default useBusinessValues;