// models/AnalysisComponent.js
const mongoose = require('mongoose');

const AnalysisComponentSchema = new mongoose.Schema({
  analysisId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Analysis',
    required: true
  },
  componentType: {
    type: String,
    enum: ['context', 'business-values', 'dreaded-events', 'security-framework', 'security-controls', 'risk-treatment', 'sources', 'objectives', 'threat-categories', 'sources-risque', 'objectifs-vises', 'couples-srov', 'selected-couples-srov', 'stakeholders', 'attack-paths', 'attack-path-diagrams', 'ecosystem-measures', 'cti-results', 'cti-operational-scenarios'],
    required: true
  },
  data: {
    type: mongoose.Schema.Types.Mixed,
    required: true
  },
  version: {
    type: Number,
    default: 1
  },
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  updatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: true
});

// Compound index to ensure unique component type and version per analysis
AnalysisComponentSchema.index({ analysisId: 1, componentType: 1, version: 1 }, { unique: true });

// Method to create a new version
AnalysisComponentSchema.statics.createNewVersion = async function(analysisId, componentType, data, userId) {
  try {
    // Find the current component
    const currentComponent = await this.findOne({ analysisId, componentType }).sort({ version: -1 });

    // If exists, update the existing component
    if (currentComponent) {
      // Update the existing component
      currentComponent.data = data;
      currentComponent.updatedBy = userId;
      currentComponent.updatedAt = new Date();

      // Save the updated component
      await currentComponent.save();
      return currentComponent;
    }

    // If not exists, create first version
    return await this.create({
      analysisId,
      componentType,
      data,
      version: 1,
      createdBy: userId,
      updatedBy: userId
    });
  } catch (error) {
    console.error(`Error in createNewVersion for ${componentType}:`, error);
    throw error;
  }
};

module.exports = mongoose.model('AnalysisComponent', AnalysisComponentSchema);