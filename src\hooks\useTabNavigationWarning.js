// src/hooks/useTabNavigationWarning.js
import { useState, useCallback, useEffect } from 'react';
import { setUnsavedChanges, setNavigationWarningDialogOpen } from '../utils/navigationUtils';

/**
 * Custom hook to manage unsaved changes warnings when navigating between tabs
 * 
 * @param {boolean} initialHasUnsavedChanges - Initial state of unsaved changes
 * @returns {Object} - Object containing functions and state for managing unsaved changes warnings
 */
const useTabNavigationWarning = (initialHasUnsavedChanges = false) => {
  // State to track unsaved changes
  const [hasUnsavedChanges, setHasUnsavedChangesState] = useState(initialHasUnsavedChanges);
  // State to track if warning dialog is open
  const [isWarningDialogOpen, setIsWarningDialogOpen] = useState(false);
  // State to store pending navigation target
  const [pendingNavigation, setPendingNavigation] = useState(null);

  // Update the global unsaved changes state whenever local state changes
  useEffect(() => {
    setUnsavedChanges(hasUnsavedChanges);
  }, [hasUnsavedChanges]);

  // Function to set unsaved changes state
  const setHasUnsavedChangesValue = useCallback((value) => {
    setHasUnsavedChangesState(value);
  }, []);

  // Function to check for unsaved changes and show warning if needed
  const checkUnsavedChangesAndNavigate = useCallback((navigateFunction) => {
    // Check if there are unsaved changes
    if (hasUnsavedChanges) {
      // Store the pending navigation function
      setPendingNavigation(() => navigateFunction);
      // Show the warning dialog
      setIsWarningDialogOpen(true);
      setNavigationWarningDialogOpen(true);
      return false;
    }

    // If no unsaved changes, navigate directly
    navigateFunction();
    return true;
  }, [hasUnsavedChanges]);

  // Function to handle continuing navigation after warning
  const handleContinueNavigation = useCallback(() => {
    // Close the warning dialog
    setIsWarningDialogOpen(false);
    setNavigationWarningDialogOpen(false);

    // Execute the pending navigation function
    if (pendingNavigation) {
      pendingNavigation();
      setPendingNavigation(null);
    }
  }, [pendingNavigation]);

  // Function to handle saving and continuing navigation
  const handleSaveAndContinue = useCallback((saveFunction) => {
    // Execute the save function if provided
    if (typeof saveFunction === 'function') {
      saveFunction();
    }

    // Reset unsaved changes flag
    setHasUnsavedChangesState(false);

    // Continue with navigation
    handleContinueNavigation();
  }, [handleContinueNavigation]);

  // Function to cancel navigation
  const handleCancelNavigation = useCallback(() => {
    // Close the warning dialog
    setIsWarningDialogOpen(false);
    setNavigationWarningDialogOpen(false);
    // Clear the pending navigation
    setPendingNavigation(null);
  }, []);

  return {
    hasUnsavedChanges,
    setHasUnsavedChanges: setHasUnsavedChangesValue,
    isWarningDialogOpen,
    setIsWarningDialogOpen,
    pendingNavigation,
    setPendingNavigation,
    checkUnsavedChangesAndNavigate,
    handleContinueNavigation,
    handleSaveAndContinue,
    handleCancelNavigation
  };
};

export default useTabNavigationWarning;