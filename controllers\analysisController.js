// controllers/analysisController.js
const Analysis = require('../models/Analysis');
const AnalysisComponent = require('../models/AnalysisComponent');
const Company = require('../models/Company');
const ActivityLog = require('../models/ActivityLog');
const mongoose = require('mongoose');

// Helper function to get IP address
const getIpAddress = (req) => {
  return req.ip || 
         req.headers['x-forwarded-for'] || 
         req.connection.remoteAddress || 
         'unknown';
};

// Helper function to create activity log
const logActivity = async (req, actionType, resourceType, resourceId, details = {}) => {
  try {
    if (!req.user) return;
    
    await ActivityLog.create({
      userId: req.user.id,
      userName: req.user.name,
      companyId: req.user.companyId,
      companyName: req.user.companyName,
      actionType,
      resourceType,
      resourceId,
      ipAddress: getIpAddress(req),
      userAgent: req.headers['user-agent'] || '',
      details: {
        ...details,
        timestamp: new Date()
      }
    });
  } catch (error) {
    console.error('Activity log error:', error);
  }
};

/**
 * Transform analysis document for response
 */
const transformAnalysisForResponse = (analysis) => {
  if (!analysis) return null;
  
  const plainAnalysis = analysis.toObject ? analysis.toObject() : { ...analysis };
  
  return {
    id: plainAnalysis._id.toString(),
    name: plainAnalysis.name,
    description: plainAnalysis.description,
    companyId: plainAnalysis.companyId.toString(),
    companyName: plainAnalysis.companyName,
    status: plainAnalysis.status,
    createdBy: plainAnalysis.createdBy.toString(),
    createdByName: plainAnalysis.createdByName,
    createdAt: plainAnalysis.createdAt.toISOString(),
    updatedAt: plainAnalysis.updatedAt.toISOString(),
    lastUpdatedBy: plainAnalysis.lastUpdatedBy ? plainAnalysis.lastUpdatedBy.toString() : null,
    lastUpdatedByName: plainAnalysis.lastUpdatedByName || null
  };
};

/**
 * @desc    Get all analyses with filters
 * @route   GET /api/analyses
 * @access  Private
 */
exports.getAnalyses = async (req, res) => {
  try {
    const { status, search, companyId, page = 1, limit = 10 } = req.query;
    const skip = (page - 1) * limit;
    
    // Build filter
    const filter = {};
    
    // Status filter
    if (status) {
      filter.status = status;
    }
    
    // Search filter
    if (search) {
      filter.$or = [
        { name: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } }
      ];
    }
    
    // Company filter based on user role
    if (req.user.role === 'superadmin') {
      // Superadmin can see all or filter by company
      if (companyId) {
        filter.companyId = new mongoose.Types.ObjectId(companyId);
      }
    } else if (req.user.role === 'admin') {
      // Admin can only see analyses from their company
      filter.companyId = req.user.companyId;
    } else {
      // Regular users can only see analyses from their company
      filter.companyId = req.user.companyId;
    }
    
    // Execute query with pagination
    const analyses = await Analysis.find(filter)
      .sort({ updatedAt: -1 })
      .skip(skip)
      .limit(parseInt(limit));
    
    // Get total count for pagination
    const total = await Analysis.countDocuments(filter);
    
    // Transform analyses for response
    const transformedAnalyses = analyses.map(transformAnalysisForResponse);
    
    res.status(200).json({
      success: true,
      data: transformedAnalyses,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Get analyses error:', error);
    res.status(500).json({
      success: false,
      message: 'Error retrieving analyses',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * @desc    Get single analysis by ID
 * @route   GET /api/analyses/:id
 * @access  Private
 */
exports.getAnalysisById = async (req, res) => {
  try {
    const analysis = await Analysis.findById(req.params.id);
    
    if (!analysis) {
      return res.status(404).json({
        success: false,
        message: 'Analysis not found'
      });
    }
    
    // Check if user has access to this analysis
    if (req.user.role !== 'superadmin' && analysis.companyId.toString() !== req.user.companyId.toString()) {
      return res.status(403).json({
        success: false,
        message: 'You do not have permission to access this analysis'
      });
    }
    
    res.status(200).json({
      success: true,
      data: transformAnalysisForResponse(analysis)
    });
  } catch (error) {
    console.error('Get analysis error:', error);
    res.status(500).json({
      success: false,
      message: 'Error retrieving analysis',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * @desc    Create new analysis
 * @route   POST /api/analyses
 * @access  Private
 */
exports.createAnalysis = async (req, res) => {
  try {
    const { name, description, companyId, status = 'draft' } = req.body;
    
    // Validate required fields
    if (!name) {
      return res.status(400).json({
        success: false,
        message: 'Analysis name is required'
      });
    }
    
    // Determine which company ID to use
    let targetCompanyId;
    let companyName;
    
    if (req.user.role === 'superadmin' && companyId) {
      // Superadmin can create for any company
      targetCompanyId = companyId;
      
      // Verify company exists
      const company = await Company.findById(companyId);
      if (!company) {
        return res.status(404).json({
          success: false,
          message: 'Company not found'
        });
      }
      companyName = company.name;
    } else {
      // Other users can only create for their company
      targetCompanyId = req.user.companyId;
      companyName = req.user.companyName;
    }
    
    // Create analysis
    const analysis = await Analysis.create({
      name,
      description: description || '',
      companyId: targetCompanyId,
      companyName: companyName,
      status,
      createdBy: req.user.id,
      createdByName: req.user.name,
      lastUpdatedBy: req.user.id,
      lastUpdatedByName: req.user.name
    });
    
    // Log activity
    await logActivity(
      req, 
      'ANALYSIS_CREATE', 
      'analysis', 
      analysis._id, 
      { 
        analysisName: analysis.name, 
        companyId: analysis.companyId 
      }
    );
    
    res.status(201).json({
      success: true,
      data: transformAnalysisForResponse(analysis)
    });
  } catch (error) {
    console.error('Create analysis error:', error);
    res.status(500).json({
      success: false,
      message: 'Error creating analysis',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * @desc    Update analysis
 * @route   PUT /api/analyses/:id
 * @access  Private
 */
exports.updateAnalysis = async (req, res) => {
  try {
    const { name, description, status } = req.body;
    const analysisId = req.params.id;
    
    // Find analysis
    const analysis = await Analysis.findById(analysisId);
    
    if (!analysis) {
      return res.status(404).json({
        success: false,
        message: 'Analysis not found'
      });
    }
    
    // Check if user has access to this analysis
    if (req.user.role !== 'superadmin' && analysis.companyId.toString() !== req.user.companyId.toString()) {
      return res.status(403).json({
        success: false,
        message: 'You do not have permission to update this analysis'
      });
    }
    
    // Update fields
    if (name) analysis.name = name;
    if (description !== undefined) analysis.description = description;
    if (status) analysis.status = status;
    
    // Update metadata
    analysis.updatedAt = Date.now();
    analysis.lastUpdatedBy = req.user.id;
    analysis.lastUpdatedByName = req.user.name;
    
    // Save changes
    await analysis.save();
    
    // Log activity
    await logActivity(
      req, 
      'ANALYSIS_UPDATE', 
      'analysis', 
      analysis._id, 
      { 
        analysisName: analysis.name, 
        changedFields: Object.keys(req.body)
      }
    );
    
    res.status(200).json({
      success: true,
      data: transformAnalysisForResponse(analysis)
    });
  } catch (error) {
    console.error('Update analysis error:', error);
    res.status(500).json({
      success: false,
      message: 'Error updating analysis',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * @desc    Delete analysis
 * @route   DELETE /api/analyses/:id
 * @access  Private
 */
exports.deleteAnalysis = async (req, res) => {
  try {
    const analysisId = req.params.id;
    
    // Find analysis
    const analysis = await Analysis.findById(analysisId);
    
    if (!analysis) {
      return res.status(404).json({
        success: false,
        message: 'Analysis not found'
      });
    }
    
    // Check if user has access to this analysis
    if (req.user.role !== 'superadmin' && req.user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        message: 'You do not have permission to delete analyses'
      });
    }
    
    if (req.user.role === 'admin' && analysis.companyId.toString() !== req.user.companyId.toString()) {
      return res.status(403).json({
        success: false,
        message: 'You do not have permission to delete analyses from other companies'
      });
    }
    
    // Store analysis information for logging
    const analysisInfo = {
      id: analysis._id,
      name: analysis.name,
      companyId: analysis.companyId,
      companyName: analysis.companyName
    };
    
    // Delete all associated components
    await AnalysisComponent.deleteMany({ analysisId: analysis._id });
    
    // Delete analysis
    await Analysis.findByIdAndDelete(analysisId);
    
    // Log activity
    await logActivity(
      req, 
      'ANALYSIS_DELETE', 
      'analysis', 
      analysisInfo.id, 
      analysisInfo
    );
    
    res.status(200).json({
      success: true,
      message: 'Analysis and all associated components deleted successfully',
      data: analysisInfo
    });
  } catch (error) {
    console.error('Delete analysis error:', error);
    res.status(500).json({
      success: false,
      message: 'Error deleting analysis',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * @desc    Get all analyses for a specific company
 * @route   GET /api/companies/:companyId/analyses
 * @access  Private
 */
exports.getCompanyAnalyses = async (req, res) => {
  try {
    const companyId = req.params.companyId;
    
    // Validate companyId
    if (!mongoose.Types.ObjectId.isValid(companyId)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid company ID'
      });
    }
    
    // Check permissions - Admin can only see their own company analyses
    if (req.user.role === 'admin' && req.user.companyId.toString() !== companyId) {
      return res.status(403).json({
        success: false,
        message: 'You do not have permission to see analyses from this company'
      });
    }
    
    // Check if company exists
    const company = await Company.findById(companyId);
    
    if (!company) {
      return res.status(404).json({
        success: false,
        message: 'Company not found'
      });
    }
    
    // Get pagination parameters
    const { page = 1, limit = 20 } = req.query;
    const skip = (page - 1) * limit;
    
    // Get analyses
    const analyses = await Analysis.find({ companyId })
      .sort({ updatedAt: -1 })
      .skip(skip)
      .limit(parseInt(limit));
    
    // Get total count for pagination
    const total = await Analysis.countDocuments({ companyId });
    
    // Transform analyses for response
    const transformedAnalyses = analyses.map(transformAnalysisForResponse);
    
    res.status(200).json({
      success: true,
      data: transformedAnalyses,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Get company analyses error:', error);
    res.status(500).json({
      success: false,
      message: 'Error retrieving company analyses',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};