const fetchCompanies = async () => {
  setIsLoading(true);
  setError(null);
  
  try {
    const response = await companyService.getCompanies();
    
    if (response.success) {
      setCompanies(response.data || []);
    } else {
      throw new Error(response.message || 'Failed to fetch companies');
    }
  } catch (error) {
    console.error('Error fetching companies:', error);
    setError('Erreur lors du chargement des entreprises. Veuillez réessayer plus tard.');
  } finally {
    setIsLoading(false);
  }
};

const handleSubmit = async (e) => {
  e.preventDefault();
  setIsLoading(true);
  
  try {
    if (isEditing) {
      // Update existing company
      const response = await companyService.updateCompany(currentCompanyId, formData);
      
      if (response.success) {
        await fetchCompanies(); // Refresh the list
        resetForm();
      } else {
        throw new Error(response.message || 'Failed to update company');
      }
    } else {
      // Create new company
      const response = await companyService.createCompany(formData);
      
      if (response.success) {
        await fetchCompanies(); // Refresh the list
        resetForm();
      } else {
        throw new Error(response.message || 'Failed to create company');
      }
    }
  } catch (error) {
    console.error(isEditing ? 'Update company error:' : 'Create company error:', error);
    setError(`Erreur lors de ${isEditing ? 'la mise à jour' : 'la création'} de l'entreprise. Veuillez réessayer.`);
  } finally {
    setIsLoading(false);
  }
};

const handleDelete = async (companyId) => {
  if (!window.confirm('Êtes-vous sûr de vouloir supprimer cette entreprise ?')) {
    return;
  }

  setIsLoading(true);
  try {
    const response = await companyService.deleteCompany(companyId);
    
    if (response.success) {
      await fetchCompanies(); // Refresh the list
    } else {
      throw new Error(response.message || 'Failed to delete company');
    }
  } catch (error) {
    console.error('Delete company error:', error);
    setError('Erreur lors de la suppression de l\'entreprise. Veuillez réessayer.');
  } finally {
    setIsLoading(false);
  }
}; 