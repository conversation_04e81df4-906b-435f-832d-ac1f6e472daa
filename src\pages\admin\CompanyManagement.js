// src/pages/admin/CompanyManagement.jsx
import React, { useState, useEffect } from 'react';
import { companyService } from '../../services/apiServices';
import LoadingSpinner from '../../components/common/LoadingSpinner';

const CompanyManagement = () => {
  // State for company data
  const [companies, setCompanies] = useState([]);
  
  // Loading and error states
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  
  // Form data state
  const [formData, setFormData] = useState({
    name: '',
    domain: '',
    status: 'active'
  });
  
  // Editing state
  const [isEditing, setIsEditing] = useState(false);
  const [currentCompanyId, setCurrentCompanyId] = useState(null);

  // Fetch companies on component mount
  useEffect(() => {
    fetchCompanies();
  }, []);

  // Function to fetch companies from API
  const fetchCompanies = async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      const response = await companyService.getCompanies();
      console.log('API Response:', response); // Debug log
      
      if (response.success) {
        // Ensure each company has a valid ID
        const companiesWithIds = (response.data || []).map(company => ({
          ...company,
          id: company.id || company._id || String(company.id) // Handle different ID formats
        }));
        console.log('Processed companies:', companiesWithIds); // Debug log
        setCompanies(companiesWithIds);
      } else {
        throw new Error(response.message || 'Failed to fetch companies');
      }
    } catch (error) {
      console.error('Error fetching companies:', error);
      setError('Erreur lors du chargement des entreprises. Veuillez réessayer plus tard.');
      
      // Use mock data in development for testing
      if (process.env.NODE_ENV === 'development') {
        const mockCompanies = [
          { id: '1', name: 'Entreprise A', domain: 'entreprisea.com', status: 'active', createdAt: '2025-01-15T10:00:00' },
          { id: '2', name: 'Entreprise B', domain: 'entrepriseb.com', status: 'active', createdAt: '2025-02-10T14:30:00' },
          { id: '3', name: 'Entreprise C', domain: 'entreprisec.com', status: 'inactive', createdAt: '2025-03-05T09:15:00' }
        ];
        console.log('Using mock companies:', mockCompanies); // Debug log
        setCompanies(mockCompanies);
      }
    } finally {
      setIsLoading(false);
    }
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsLoading(true);
    
    try {
      if (isEditing) {
        // Update existing company
        const response = await companyService.updateCompany(currentCompanyId, formData);
        
        if (response.success) {
          // Refresh the companies list to get the latest data
          await fetchCompanies();
          resetForm();
        } else {
          throw new Error(response.message || 'Failed to update company');
        }
      } else {
        // Create new company
        const response = await companyService.createCompany(formData);
        
        if (response.success) {
          // Refresh the companies list to get the latest data
          await fetchCompanies();
          resetForm();
        } else {
          throw new Error(response.message || 'Failed to create company');
        }
      }
    } catch (error) {
      console.error(isEditing ? 'Update company error:' : 'Create company error:', error);
      setError(`Erreur lors de ${isEditing ? 'la mise à jour' : 'la création'} de l'entreprise. Veuillez réessayer.`);
    } finally {
      setIsLoading(false);
    }
  };

  const handleEdit = (company) => {
    try {
      console.log('Editing company:', company); // Debug log
      
      if (!company) {
        throw new Error('Company object is required');
      }

      // Ensure company has a valid ID
      const companyId = company.id || company._id;
      if (!companyId) {
        throw new Error('Company ID is missing');
      }

      // Validate required fields
      const requiredFields = ['name', 'domain', 'status'];
      const missingFields = requiredFields.filter(field => !company[field]);
      
      if (missingFields.length > 0) {
        throw new Error(`Missing required fields: ${missingFields.join(', ')}`);
      }

      setFormData({
        name: company.name,
        domain: company.domain,
        status: company.status
      });
      setCurrentCompanyId(companyId);
      setIsEditing(true);
      setError(null); // Clear any previous errors
    } catch (error) {
      console.error('Error in handleEdit:', error);
      setError(`Erreur lors de la modification: ${error.message}`);
    }
  };

  const resetForm = () => {
    setFormData({ name: '', domain: '', status: 'active' });
    setIsEditing(false);
    setCurrentCompanyId(null);
    setError(null);
  };

  // Add a new function to handle company deletion
  const handleDelete = async (companyId) => {
    if (!window.confirm('Êtes-vous sûr de vouloir supprimer cette entreprise ?')) {
      return;
    }

    setIsLoading(true);
    try {
      const response = await companyService.deleteCompany(companyId);
      
      if (response.success) {
        // Refresh the companies list to get the latest data
        await fetchCompanies();
      } else {
        throw new Error(response.message || 'Failed to delete company');
      }
    } catch (error) {
      console.error('Delete company error:', error);
      setError('Erreur lors de la suppression de l\'entreprise. Veuillez réessayer.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="p-6">
      <h1 className="text-2xl font-bold mb-6">Gestion des Entreprises</h1>
      
      {/* Error message */}
      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg mb-6">
          <p>{error}</p>
        </div>
      )}
      
      <div className="bg-white p-6 rounded-lg shadow-md mb-8">
        <h2 className="text-lg font-semibold mb-4">
          {isEditing ? 'Modifier une entreprise' : 'Ajouter une entreprise'}
        </h2>
        <form onSubmit={handleSubmit}>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Nom de l'entreprise
              </label>
              <input
                type="text"
                name="name"
                value={formData.name}
                onChange={handleInputChange}
                required
                className="w-full px-3 py-2 border border-gray-300 rounded-md"
                disabled={isLoading}
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Domaine
              </label>
              <input
                type="text"
                name="domain"
                value={formData.domain}
                onChange={handleInputChange}
                placeholder="example.com"
                className="w-full px-3 py-2 border border-gray-300 rounded-md"
                disabled={isLoading}
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Statut
              </label>
              <select
                name="status"
                value={formData.status}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md"
                disabled={isLoading}
              >
                <option value="active">Actif</option>
                <option value="inactive">Inactif</option>
              </select>
            </div>
          </div>
          
          <div className="mt-4 flex space-x-3">
            <button
              type="submit"
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:bg-blue-300"
              disabled={isLoading}
            >
              {isLoading ? 'Chargement...' : isEditing ? 'Mettre à jour' : 'Ajouter'}
            </button>
            
            {isEditing && (
              <button
                type="button"
                onClick={resetForm}
                className="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 disabled:bg-gray-200"
                disabled={isLoading}
              >
                Annuler
              </button>
            )}
          </div>
        </form>
      </div>
      
      {/* Companies list */}
      {isLoading && !companies.length ? (
        <div className="flex justify-center my-12">
          <LoadingSpinner />
        </div>
      ) : (
        <div className="bg-white rounded-lg shadow-md overflow-hidden">
          {companies.length === 0 ? (
            <div className="p-6 text-center text-gray-500">
              Aucune entreprise trouvée. Ajoutez votre première entreprise ci-dessus.
            </div>
          ) : (
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Nom
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Domaine
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Statut
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Date de création
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {companies.map((company) => (
                  <tr key={company.id}>
                    <td className="px-6 py-4 whitespace-nowrap">{company.name || '-'}</td>
                    <td className="px-6 py-4 whitespace-nowrap">{company.domain || '-'}</td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span
                        className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                          company.status === 'active'
                            ? 'bg-green-100 text-green-800'
                            : 'bg-red-100 text-red-800'
                        }`}
                      >
                        {company.status === 'active' ? 'Actif' : 'Inactif'}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {company.createdAt ? new Date(company.createdAt).toLocaleDateString() : '-'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      <div className="flex space-x-3">
                        <button
                          onClick={() => handleEdit(company)}
                          className="text-indigo-600 hover:text-indigo-900"
                          disabled={isLoading}
                        >
                          Modifier
                        </button>
                        
                        <a 
                          href={`/superadmin/companies/${company.id}/users`}
                          className="text-blue-600 hover:text-blue-900"
                        >
                          Utilisateurs
                        </a>
                        
                        <a 
                          href={`/superadmin/companies/${company.id}/logs`}
                          className="text-orange-600 hover:text-orange-900"
                        >
                          Logs
                        </a>

                        <button
                          onClick={() => handleDelete(company.id)}
                          className="text-red-600 hover:text-red-900"
                          disabled={isLoading}
                        >
                          Supprimer
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          )}
        </div>
      )}
    </div>
  );
};

export default CompanyManagement;