# EBIOS RM Project Summary

## Project Overview

**EBIOS RM** is a comprehensive cybersecurity risk management application implementing the French EBIOS Risk Manager methodology. It's a full-stack web application designed to guide organizations through structured cybersecurity risk assessment across 5 workshops (Ateliers).

### Core Purpose
- Systematic cybersecurity risk assessment following EBIOS RM methodology
- Structured approach through 5 sequential workshops
- Risk identification, analysis, and treatment planning
- Stakeholder collaboration and documentation

## Technical Architecture

### Frontend Stack
- **Framework**: React 18 with functional components and hooks
- **Routing**: React Router v6 with role-based protected routes
- **State Management**: React Context API (AuthContext, AnalysisContext, LanguageContext)
- **Styling**: Tailwind CSS with custom components
- **Animations**: Framer Motion for smooth transitions
- **Icons**: Lucide React icon library
- **Internationalization**: i18next with French/English support
- **Build Tool**: Create React App

### Backend Stack
- **Runtime**: Node.js with Express.js framework
- **Database**: MongoDB with Mongoose ODM
- **Authentication**: JWT-based with access/refresh token pattern
- **Security**: bcrypt for password hashing, role-based authorization
- **AI Integration**: Google Generative AI for content generation
- **API Architecture**: RESTful APIs with structured error handling

### Key Libraries & Dependencies
```json
Frontend: react, react-router-dom, framer-motion, lucide-react, i18next, tailwindcss
Backend: express, mongoose, jsonwebtoken, bcryptjs, @google/generative-ai, cors
```

### Database Schema
- **Users**: Role-based (superadmin, admin, simpleuser) with company associations
- **Companies**: Multi-tenant organization management
- **Analyses**: Core EBIOS RM analysis containers
- **AnalysisComponents**: Flexible storage for workshop data (context, business-values, dreaded-events, etc.)
- **SecurityControls**: Predefined security measures database

### Authentication & Authorization
- JWT-based authentication with 30-minute access tokens and 7-day refresh tokens
- Three-tier role system: superadmin, admin (provider), simpleuser (analyst)
- Company-based data isolation for multi-tenant architecture
- Protected routes with role-based access control

## Current Implementation Status

### ✅ Completed Workshops

#### Workshop 1 (Atelier 1) - Context & Assets
- **Context Definition**: Organization scope, participants, RACI matrix
- **Business Values (VM)**: Critical business assets with support assets
- **Dreaded Events (ER)**: Risk scenarios with gravity and security pillars
- **Security Framework**: Control framework selection and configuration
- **Security Controls**: Detailed security measures management
- **Risk Treatment Plans**: Risk mitigation strategies

#### Workshop 2 (Atelier 2) - Risk Sources & Objectives
- **Risk Sources**: Threat actor identification and categorization
- **Target Objectives**: Asset targeting analysis
- **Threat Categories**: Structured threat classification
- **SROV Couples**: Source-Risk-Objective-Vulnerability relationships

#### Workshop 3 (Atelier 3) - Strategic Scenarios
- **Stakeholder Analysis**: Ecosystem mapping with cyber reliability metrics
- **Strategic Scenarios**: High-level attack scenarios
- **Attack Path Analysis**: Detailed attack progression modeling
- **Ecosystem Security Measures**: Targeted security controls
- **Risk Treatment Progression (PTR)**: Cross-workshop progress tracking

### 🚧 Partially Implemented

#### Workshop 4 (Atelier 4) - Operational Scenarios
**Current Status**: Basic structure implemented, needs enhancement

**Existing Components**:
- `Atelier4.js` - Main workshop container
- `Activite1/` - Attack graph elaboration (partially complete)
  - `AttackGraphVisualization.js` - Visual attack path representation
  - `AttackSequenceForm.js` - Attack sequence creation
  - `AttackPathTable.js` - Tabular attack path management
  - `OperationalScenarios.js` - Scenario management interface
- `Activite2/` - Operational scenario analysis (basic structure)

**Current Features**:
- Attack path visualization with 4-phase structure (CONNAITRE, RENTRER, TROUVER, EXPLOITER)
- Basic operational scenario creation and management
- Attack graph visualization with difficulty levels
- Integration with Workshop 3 attack paths

### ❌ Not Yet Implemented

#### Workshop 5 (Atelier 5) - Risk Treatment
- Treatment strategy definition
- Implementation planning
- Monitoring and review processes

## Key Features Implemented

### 🌐 Internationalization
- Complete French/English language support
- Flag-based language switching in navbar
- Systematic translation across all components
- Dynamic content localization

### 🎨 User Experience
- Responsive design with Tailwind CSS
- Smooth animations with Framer Motion
- Consistent navigation with breadcrumbs
- Role-based UI adaptation
- Print-friendly report generation

### 🤖 AI Integration
- Google Generative AI for content suggestions
- Automated security measure generation
- Context-aware recommendations
- Attack scenario generation assistance

### 📊 Data Visualization
- Interactive charts and graphs
- Attack path visualization
- Progress tracking dashboards
- Ecosystem mapping with color-coded metrics

### 💾 Data Management
- Real-time data persistence
- Cross-workshop data relationships
- Bulk operations and selections
- Export/import capabilities

### 🔒 Security & Compliance
- Role-based access control
- Audit logging
- Secure API endpoints
- Data validation and sanitization

## Deployment Architecture

### Current Setup
- **Development**: Separate frontend (React dev server) and backend (Node.js)
- **Database**: MongoDB with initialization scripts
- **Environment**: Docker-ready with docker-compose configuration

### Deployment Options
- **Portable Version**: Complete Docker-based deployment package
- **Production**: Nginx reverse proxy with containerized services
- **Database**: MongoDB with pre-seeded reference data

## Workshop 4 Enhancement Requirements

### 🎯 Primary Objectives for Workshop 4

#### 1. **Enhanced Operational Scenario Development**
- **4-Phase Attack Sequence**: Systematic CONNAITRE → RENTRER → TROUVER → EXPLOITER progression
- **MITRE ATT&CK Integration**: Map attack techniques to each phase
- **Vulnerability Integration**: Connect business asset vulnerabilities to attack vectors
- **Realistic Scenario Generation**: AI-powered scenario creation based on business context

#### 2. **Advanced Attack Path Modeling**
- **Multi-Vector Analysis**: Support for multiple attack paths per scenario
- **Difficulty Assessment**: Granular difficulty scoring for each attack step
- **Success Probability**: Likelihood calculations for attack progression
- **Countermeasure Mapping**: Link security controls to specific attack steps

#### 3. **Threat Intelligence Integration**
- **MITRE ATT&CK Database**: Complete technique and procedure mapping
- **CVE Integration**: Vulnerability database connectivity
- **NIS Directive Compliance**: European cybersecurity framework alignment
- **ATLAS MITRE**: AI/ML specific threat patterns

#### 4. **Enhanced Visualization & Analysis**
- **Interactive Attack Graphs**: Dynamic, explorable attack path visualization
- **Timeline Analysis**: Temporal attack progression modeling
- **Impact Assessment**: Business impact calculation for each scenario
- **Risk Scoring**: Quantitative risk assessment integration

#### 5. **AI-Powered Enhancements**
- **Scenario Generation**: Context-aware operational scenario creation
- **Attack Vector Suggestions**: AI-recommended attack paths based on business assets
- **Countermeasure Recommendations**: Intelligent security control suggestions
- **Risk Prioritization**: AI-assisted risk ranking and treatment prioritization

### 🔧 Technical Implementation Needs

#### Database Enhancements
- **Operational Scenarios Schema**: Structured storage for complex scenarios
- **MITRE ATT&CK Data**: Comprehensive technique and procedure database
- **Vulnerability Database**: CVE and custom vulnerability storage
- **Attack Path Relationships**: Complex many-to-many relationship modeling

#### API Integrations
- **External Threat Intelligence**: CVE, NIS, MITRE ATT&CK API connections
- **AI Service Enhancement**: Advanced prompt engineering for scenario generation
- **Real-time Updates**: Live threat intelligence feed integration

#### Frontend Components
- **Advanced Visualizations**: Interactive attack graph components
- **Scenario Builder**: Drag-and-drop scenario construction interface
- **Timeline Views**: Temporal attack progression visualization
- **Risk Dashboard**: Comprehensive risk overview and metrics

This summary provides a comprehensive foundation for planning Workshop 4 enhancements while maintaining consistency with the existing EBIOS RM methodology and technical architecture.
