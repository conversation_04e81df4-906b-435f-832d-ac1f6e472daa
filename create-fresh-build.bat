@echo off
echo ==========================================
echo Creating Fresh Build for EBIOS RM
echo ==========================================

echo [1/4] Navigating to project directory...
cd /d "C:\Users\<USER>\Desktop\EBROS RM Project\APP\Atelier 1\my-ebios-app"

echo [2/4] Installing/updating dependencies...
call npm install

echo [3/4] Building React application...
call npm run build

echo [4/4] Copying build to new-build folder...
if exist build (
    if exist new-build rmdir /s /q new-build
    xcopy build new-build\ /E /I /Y
    echo ✅ Fresh build created in 'new-build' folder!
    echo.
    echo Build contents:
    dir new-build
) else (
    echo ❌ Build failed - build folder not found
    exit /b 1
)

echo.
echo ==========================================
echo ✅ Fresh Build Complete!
echo ==========================================
echo.
echo Your fresh build is ready in: new-build\
echo.
pause
