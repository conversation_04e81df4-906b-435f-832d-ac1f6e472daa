// src/config/threatIntelConfig.js
// Configuration for threat intelligence sources and update schedules

export const THREAT_INTEL_CONFIG = {
  // Data source URLs
  sources: {
    mitre: {
      enterprise: 'https://raw.githubusercontent.com/mitre/cti/master/enterprise-attack/enterprise-attack.json',
      mobile: 'https://raw.githubusercontent.com/mitre/cti/master/mobile-attack/mobile-attack.json',
      ics: 'https://raw.githubusercontent.com/mitre/cti/master/ics-attack/ics-attack.json'
    },
    cisa: {
      kev: 'https://www.cisa.gov/sites/default/files/feeds/known_exploited_vulnerabilities.json',
      alerts: 'https://www.cisa.gov/uscert/ncas/alerts.xml'
    },
    nist: {
      nvd: 'https://services.nvd.nist.gov/rest/json/cves/2.0',
      cpeDictionary: 'https://services.nvd.nist.gov/rest/json/cpes/2.0'
    },
    epss: {
      scores: 'https://api.first.org/data/v1/epss'
    },
    openCTI: {
      // Configure your OpenCTI instance
      baseUrl: process.env.OPENCTI_URL || null,
      apiKey: process.env.OPENCTI_API_KEY || null
    },
    alienvault: {
      // AlienVault OTX (if you have API key)
      baseUrl: 'https://otx.alienvault.com/api/v1',
      apiKey: process.env.ALIENVAULT_API_KEY || null
    }
  },

  // Update schedules (in milliseconds)
  updateSchedules: {
    mitreTechniques: 24 * 60 * 60 * 1000,    // 24 hours
    cisaKEV: 6 * 60 * 60 * 1000,             // 6 hours
    nistCVE: 12 * 60 * 60 * 1000,            // 12 hours
    threatGroups: 24 * 60 * 60 * 1000,       // 24 hours
    epssScores: 24 * 60 * 60 * 1000          // 24 hours
  },

  // Rate limiting settings
  rateLimits: {
    nist: {
      requestsPerMinute: 5,
      delayBetweenRequests: 12000 // 12 seconds
    },
    mitre: {
      requestsPerMinute: 10,
      delayBetweenRequests: 6000 // 6 seconds
    },
    cisa: {
      requestsPerMinute: 10,
      delayBetweenRequests: 6000
    }
  },

  // Cache settings
  cache: {
    enabled: true,
    maxSize: 100, // MB
    compression: true,
    encryption: false // Set to true if storing sensitive data
  },

  // Data quality settings
  dataQuality: {
    minimumCVSSScore: 0.0,
    maximumAge: 365, // days
    requiredFields: ['id', 'description', 'severity'],
    excludeDeprecated: true,
    excludeRevoked: true
  },

  // Notification settings for critical updates
  notifications: {
    enabled: true,
    criticalCVSSThreshold: 9.0,
    cisaKEVAlerts: true,
    newThreatGroupAlerts: true,
    webhookUrl: process.env.THREAT_INTEL_WEBHOOK_URL || null,
    emailNotifications: {
      enabled: false,
      recipients: [],
      smtpConfig: {}
    }
  },

  // Integration settings
  integrations: {
    siem: {
      enabled: false,
      type: 'splunk', // 'splunk', 'elastic', 'qradar'
      endpoint: process.env.SIEM_ENDPOINT || null,
      apiKey: process.env.SIEM_API_KEY || null
    },
    ticketing: {
      enabled: false,
      type: 'jira', // 'jira', 'servicenow', 'github'
      endpoint: process.env.TICKETING_ENDPOINT || null,
      apiKey: process.env.TICKETING_API_KEY || null
    }
  }
};

// Threat intelligence source priorities
export const SOURCE_PRIORITIES = {
  'CISA KEV': 10,        // Highest priority - known exploited
  'MITRE ATT&CK': 9,     // High priority - authoritative
  'NIST NVD': 8,         // High priority - comprehensive
  'EPSS': 7,             // Medium-high - exploit prediction
  'OpenCTI': 6,          // Medium - contextual
  'AlienVault OTX': 5,   // Medium - community
  'Custom': 4            // Lower - internal sources
};

// Asset type to threat intelligence mapping
export const ASSET_THREAT_MAPPING = {
  'Serveur': {
    primarySources: ['MITRE ATT&CK', 'CISA KEV', 'NIST NVD'],
    focusAreas: ['Initial Access', 'Execution', 'Persistence', 'Lateral Movement'],
    criticalTechniques: ['T1190', 'T1078', 'T1021', 'T1053']
  },
  'Base de données': {
    primarySources: ['CISA KEV', 'NIST NVD', 'MITRE ATT&CK'],
    focusAreas: ['Credential Access', 'Collection', 'Exfiltration'],
    criticalTechniques: ['T1552', 'T1005', 'T1041', 'T1190']
  },
  'Application': {
    primarySources: ['MITRE ATT&CK', 'NIST NVD', 'EPSS'],
    focusAreas: ['Initial Access', 'Execution', 'Defense Evasion'],
    criticalTechniques: ['T1190', 'T1059', 'T1055', 'T1203']
  },
  'Réseau': {
    primarySources: ['MITRE ATT&CK', 'CISA KEV'],
    focusAreas: ['Discovery', 'Lateral Movement', 'Credential Access'],
    criticalTechniques: ['T1018', 'T1021', 'T1040', 'T1557']
  },
  'Équipement': {
    primarySources: ['MITRE ATT&CK', 'NIST NVD'],
    focusAreas: ['Initial Access', 'Persistence', 'Defense Evasion'],
    criticalTechniques: ['T1566', 'T1547', 'T1055', 'T1078']
  }
};

// Business sector to threat group mapping
export const SECTOR_THREAT_MAPPING = {
  'Financial': {
    primaryThreats: ['FIN7', 'Carbanak', 'Lazarus Group'],
    commonTechniques: ['T1566.001', 'T1078', 'T1005', 'T1041'],
    riskFactors: ['High value targets', 'Regulatory compliance', 'Customer data']
  },
  'Healthcare': {
    primaryThreats: ['APT1', 'Conti', 'Ryuk'],
    commonTechniques: ['T1566.001', 'T1190', 'T1486', 'T1005'],
    riskFactors: ['Patient data', 'Medical devices', 'Life-critical systems']
  },
  'Government': {
    primaryThreats: ['APT28', 'APT29', 'Lazarus Group'],
    commonTechniques: ['T1566.002', 'T1078', 'T1083', 'T1041'],
    riskFactors: ['Classified information', 'Nation-state actors', 'Supply chain']
  },
  'Technology': {
    primaryThreats: ['APT40', 'Lazarus Group', 'FIN7'],
    commonTechniques: ['T1195', 'T1078', 'T1005', 'T1041'],
    riskFactors: ['Intellectual property', 'Supply chain', 'Customer data']
  },
  'Energy': {
    primaryThreats: ['APT33', 'TEMP.Veles', 'Triton'],
    commonTechniques: ['T1566.001', 'T1190', 'T1021', 'T1105'],
    riskFactors: ['Critical infrastructure', 'ICS/SCADA', 'Physical impact']
  }
};

// Export configuration validation function
export function validateThreatIntelConfig() {
  const errors = [];
  
  // Check required environment variables
  if (!process.env.NODE_ENV) {
    errors.push('NODE_ENV environment variable not set');
  }
  
  // Validate source URLs
  Object.entries(THREAT_INTEL_CONFIG.sources).forEach(([source, config]) => {
    Object.entries(config).forEach(([key, url]) => {
      if (url && !url.startsWith('http')) {
        errors.push(`Invalid URL for ${source}.${key}: ${url}`);
      }
    });
  });
  
  // Validate update schedules
  Object.entries(THREAT_INTEL_CONFIG.updateSchedules).forEach(([key, interval]) => {
    if (interval < 60000) { // Less than 1 minute
      errors.push(`Update interval for ${key} too short: ${interval}ms`);
    }
  });
  
  return {
    isValid: errors.length === 0,
    errors
  };
}

export default THREAT_INTEL_CONFIG;
