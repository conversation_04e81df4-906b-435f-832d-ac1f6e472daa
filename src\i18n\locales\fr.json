{"common": {"print": "Imprimer le Rapport", "loading": "Chargement...", "error": "<PERSON><PERSON><PERSON>", "save": "Enregistrer", "cancel": "Annuler", "delete": "<PERSON><PERSON><PERSON><PERSON>", "edit": "Modifier", "add": "Ajouter", "search": "<PERSON><PERSON><PERSON>", "filter": "<PERSON><PERSON><PERSON>", "export": "Exporter", "import": "Importer", "logout": "Déconnexion", "previous": "Précédent", "next": "Suivant", "startAnalysis": "Commencer l'analyse", "description": "Description", "noDescription": "Pas de description", "help": "Aide", "close": "<PERSON><PERSON><PERSON>"}, "navigation": {"workshop1": "Atelier 1: Cadrage", "context": "Contexte & Acteurs", "businessValues": "<PERSON><PERSON>", "dreadedEvents": "Événements Redoutés", "securityFoundation": "<PERSON><PERSON> de s<PERSON>", "securityControls": "Mesures de contrôle", "riskTreatment": "Plan de Traitement"}, "riskTreatment": {"title": "Plan de Traitement des Risques - Synthèse", "executiveSummaryTitle": "Synthèse Exécutive", "generalInfo": "Informations Générales", "analysisName": "Nom de l'analyse :", "creationDate": "Date de création :", "organization": "Organisation :", "scope": "Périmètre :", "missions": "Missions :", "participants": "Participants", "businessContext": {"supportAssets": "Biens Supports ({{count}}):", "type": "Type", "location": "Localisation", "description": "Desc", "noSupportAssets": "Aucun bien support associé.", "noBusinessValues": "Aucune valeur métier définie."}, "initialDistribution": {"description": "Répartition des {{count}} événements redoutés par niveau de gravité initial :"}, "riskIdentification": {"totalEvents": "Nombre total d'événements redoutés identifiés : {{count}}"}, "securityFramework": "<PERSON><PERSON> de Sécurité (Règles Sélectionnées)", "treatmentStrategies": "Stratégies de Traitement Adoptées", "detailedPlan": "Plan de Traitement Détaillé", "acceptedRisks": "Risques Acceptés", "residualDistribution": "Distribution Résiduelle des Risques (Après Traitement)", "executiveSummary": {"intro": "<PERSON>tte synthèse résume les résultats de l'Atelier 1 d'analyse de risques EBIOS RM pour l'analyse \"{{analysisName}}\", portant sur le périmètre \"{{scope}}\".", "businessValues": "Au total, {{businessValuesCount}} valeurs métiers essentielles ont été identifiées, menant à {{dreadedEventsCount}} événements redoutés potentiels. Parmi ceux-ci, {{criticalEventsCount}} événements présentent une gravité initiale Critique (G4) ou Catastrophique (G5).", "securityRules": "Pour adresser ces risques, {{rulesCount}} règles de sécurité ont été sélectionnées et évaluées. Le plan de traitement détaillé expose les décisions prises : {{reduceCount}} Réductions, {{acceptCount}} Acceptations, {{transferCount}} Transferts, {{avoidCount}} Évitements.", "undefined": "{{count}} Non définis", "objective": "L'objectif est de ramener le niveau de risque résiduel à un seuil acceptable pour l'organisation."}, "table": {"headers": {"severityLevel": "Niveau de Gravité", "eventCount": "Nombre d'Événements", "eventName": "Nom Événement", "associatedBusinessValue": "<PERSON><PERSON>", "primaryImpact": "Impact Principal", "initialSeverity": "Gravité Initiale"}}, "noMissions": "Aucune mission définie"}, "businessValues": {"title": "<PERSON><PERSON>", "breadcrumb": {"workshop1": "Atelier 1", "businessValues": "<PERSON><PERSON>"}, "buttons": {"list": "Liste", "visualize": "Visualiser", "add": "Ajouter", "save": "<PERSON><PERSON><PERSON><PERSON>", "help": "Aide", "edit": "Modifier", "delete": "<PERSON><PERSON><PERSON><PERSON>", "cancel": "Annuler", "addAsset": "Ajouter Bien", "manage": "<PERSON><PERSON><PERSON>", "close": "<PERSON><PERSON><PERSON>", "start": "Commencer", "addValue": "Ajouter", "remove": "<PERSON><PERSON><PERSON>", "expand": "<PERSON><PERSON><PERSON>", "collapse": "<PERSON><PERSON><PERSON><PERSON>"}, "fields": {"name": "Nom", "description": "Description", "businessValueName": "Nom de la valeur métier", "businessValueDescription": "Description de la valeur métier (optionnelle)", "supportAssetName": "Nom du bien support", "supportAssetDescription": "Description du bien support (optionnel)", "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON> (optionnel)", "location": "Localisation (optionnel)", "type": "Type"}, "placeholders": {"businessValueName": "Nom de la valeur métier", "businessValueDescription": "Description de la valeur métier (optionnelle)", "supportAssetName": "Nom du bien support *", "supportAssetDescription": "Description du bien support (optionnel)", "owner": "<PERSON><PERSON><PERSON><PERSON><PERSON> (optionnel)", "location": "Localisation (optionnel)"}, "sections": {"identifiedValues": "Valeurs métier identifiées", "securityPillars": "Piliers de sécurité concernés", "supportAssets": "Biens supports associés", "addSupportAsset": "Ajouter un bien support", "legend": "Légende"}, "status": {"saving": "Enregistrement des valeurs métier...", "saved": "Valeurs métier enregistrées avec succès!", "saveError": "Erreur lors de l'enregistrement", "valueAdded": "<PERSON><PERSON> métier \"{name}\" ajoutée avec succès!", "valueRemoved": "<PERSON><PERSON> métier \"{name}\" supprimée avec succès!", "emptyNameError": "Le nom de la valeur métier ne peut pas être vide.", "selected": "sélectionnés", "assets": "biens"}, "confirmations": {"deleteValue": "Êtes-vous sûr de vouloir supprimer la valeur métier \"{name}\" ?", "deleteAsset": "Supprimer le bien support \"{name}\" ?"}, "empty": {"noValues": "Aucune valeur métier n'a été ajoutée", "noAssets": "Aucun bien support n'a été associé", "noAssetsChart": "Aucun bien support associé", "addValuesForChart": "Ajoutez des valeurs métier pour visualiser leur cartographie"}, "modal": {"addTitle": "Ajouter une nouvelle valeur métier"}, "chart": {"selectValue": "<PERSON><PERSON><PERSON><PERSON>ner une valeur métier", "useShortIds": "Utiliser les identifiants courts (VM1, VM2...)", "securityPillars": "Piliers de sécurité", "supportAssets": "Biens Supports", "globalMapping": "Cartographie globale", "zoomIn": "Zoom avant", "zoomOut": "Zoom arri<PERSON>", "resetView": "Réinitialiser la vue", "studyPerimeter": "Périmètre d'étude", "supportAssetCount": "bien(s) support", "pillarCount": "pilier(s)", "assets": "biens"}, "legend": {"informationSystem": "Système d'Information", "businessValue": "<PERSON><PERSON>", "supportAsset": "Bien Support", "securityPillar": "<PERSON>lier de Sécurité", "studyPerimeter": "Périmètre d'étude"}, "help": {"title": "Aide sur les valeurs métier", "description": "Les valeurs métier représentent les ressources essentielles de votre organisation. Utilisez la vue liste pour gérer individuellement chaque valeur ou basculez sur la vue graphique pour visualiser les relations entre vos valeurs métier et leurs biens supports."}, "guide": {"title": "Guide d'utilisation", "steps": {"identify": {"title": "Identifier les valeurs métier", "description": "Ident<PERSON><PERSON>z les processus, services ou missions essentiels de votre organisation qui doivent être protégés."}, "evaluate": {"title": "Évaluer l'importance", "description": "Pour chaque valeur m<PERSON>, <PERSON><PERSON><PERSON>z son niveau d'importance pour l'organisation (faible, moyen, élevé)."}, "assets": {"title": "Identifier les biens supports", "description": "Pour chaque valeur mé<PERSON>, identifiez les biens supports (mat<PERSON><PERSON>, logic<PERSON><PERSON>, r<PERSON>eaux, personnel) qui la soutiennent."}, "links": {"title": "Définir les liens", "description": "Établissez les liens entre les valeurs métier et les biens supports pour comprendre les dépendances."}}, "advice": {"title": "Conseil", "description": "Concentrez-vous sur les valeurs métier les plus critiques pour votre organisation. Une analyse trop large peut diluer l'efficacité de votre démarche de sécurité."}}, "assetTypes": {"organisation": "Organisation", "locaux": "Lo<PERSON>ux", "equipements": "Équipements", "reseaux": "Réseaux", "logiciels": "Logiciels et Applications"}}, "footer": {"copyright": "EBIOS RM - Application d'analyse des risques © 2025"}, "dashboard": {"title": "Tableau de bord", "subtitle": "Analyse et suivi des risques de sécurité", "version": "v2.0", "analysisSelector": {"title": "Sélection de l'analyse", "info": "Choisissez une analyse pour voir ses données"}, "status": {"loading": "Chargement des données...", "error": "Erreur lors du chargement des données", "errorDetails": "Veuillez réessayer ou contacter le support si le problème persiste.", "retry": "<PERSON><PERSON><PERSON><PERSON>"}, "tabs": {"overview": "Vue d'ensemble", "participants": "Participants", "events": "Événements redoutés", "security": "Mesures de sécurité", "analysis": "Analyse"}, "cards": {"participants": "Participants", "businessValues": "<PERSON><PERSON> m<PERSON>", "dreadedEvents": "Événements redoutés", "securityMeasures": "Mesures de sécurité", "differentRoles": "r<PERSON>les différents", "pillarsCovered": "piliers couverts", "critical": "critiques", "implemented": "en place", "toDefine": "À définir"}, "charts": {"securityDistribution": "Distribution des mesures de sécurité", "eventsByPillar": "Événements redoutés par pilier", "impactTypes": "Types d'impacts les plus importants", "implementationTimeline": "<PERSON><PERSON><PERSON> de mise en œuvre", "globalSecurityStatus": "Statut global de sécurité", "numberOfMeasures": "Nombre de mesures", "numberOfEvents": "Nombre d'événements", "security": "Sécurité", "eventsBySeverity": "Événements par sévérité", "businessValuePillars": "Piliers des valeurs métier"}, "statusLabels": {"implemented": "En place", "partial": "Partiel", "planned": "Planifié", "toApply": "À appliquer", "toAbandon": "À abandonner", "implementedMeasures": "Mesures en place", "partialMeasures": "Mesures partielles", "plannedMeasures": "Mesures planifiées"}, "empty": {"noSecurityMeasures": "Aucune mesure de sécurité définie", "defineSecurityMeasures": "Définissez des mesures de sécurité pour visualiser le calendrier"}, "footer": {"organization": "Organisation", "analysisDate": "Date d'analyse", "lastModified": "Dernière modif."}}, "analysisSelector": {"title": "Sélection de l'analyse", "newAnalysis": "Nouvelle analyse", "cancel": "Annuler", "availableAnalyses": "Analyses disponibles", "loading": "Chargement", "loadingAnalyses": "Chargement des analyses...", "selected": "Sélectionnée", "select": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "modifiedOn": "Modifi<PERSON> le", "createdOn": "<PERSON><PERSON><PERSON>", "by": "Par", "unknownUser": "Utilisateur inconnu", "noDescription": "Aucune description", "viewDetails": "Voir les détails", "edit": "Modifier", "delete": "<PERSON><PERSON><PERSON><PERSON>", "confirm": "Confirmer", "deleting": "Suppression...", "creating": "Création...", "createAnalysis": "C<PERSON>er l'analyse", "analysisName": "Nom de l'analyse", "analysisNamePlaceholder": "Ex: <PERSON><PERSON><PERSON> de risques - Projet X", "description": "Description", "optionalDescription": "Description (optionnelle)", "descriptionPlaceholder": "Description du cadre de l'analyse...", "noAnalysesAvailable": "Aucune analyse disponible", "createFirstAnalysis": "<PERSON><PERSON>ez votre première analyse en cliquant sur \"Nouvelle analyse\"", "selectedAnalysis": "<PERSON><PERSON><PERSON>", "showDetails": "Aff<PERSON>r les détails", "hideDetails": "Masquer les détails", "company": "Entreprise", "status": {"inProgress": "En cours", "completed": "Terminée"}, "alerts": {"mustBeConnected": "V<PERSON> devez être connecté à une entreprise pour créer une analyse.", "editFeatureNotImplemented": "Fonctionnalité de modification à implémenter"}}, "context": {"title": "Cadre de l'Étude & Matrice RACI", "breadcrumb": {"workshop1": "Atelier 1", "framing": "Cadrage"}, "buttons": {"export": "Exporter", "save": "<PERSON><PERSON><PERSON><PERSON>", "help": "Aide", "addParticipant": "Ajouter un participant", "addMission": "Ajouter"}, "sections": {"generalInfo": "Informations Générales", "participants": "Participants & Matrice RACI", "visualization": "Visualisation de la Matrice RACI"}, "steps": {"step1": "Étape 1/3", "step2": "Étape 2/3", "step3": "Étape 3/3"}, "fields": {"organizationName": "Nom de l'organisation", "organizationNamePlaceholder": "Entrez le nom de votre organisation", "organizationNameHelp": "Nom de l'entité concernée par l'analyse", "analysisDate": "Date de l'analyse", "analysisDateHelp": "Date de début de l'analyse de risque", "missions": "Missions", "missionPlaceholder": "Ajouter une mission...", "scope": "Objectif/Périmètre", "scopePlaceholder": "Décrivez l'objectif et le périmètre de l'analyse...", "scopeHelp": "Définition claire des objectifs et du périmètre d'analyse", "participantName": "Nom du participant", "participantNamePlaceholder": "Nom complet", "position": "Poste/Fonction", "customPosition": "<PERSON>e personnal<PERSON>", "customPositionPlaceholder": "Spécifiez le poste...", "selectRole": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "status": {"loading": "Chargement des données du contexte...", "saving": "Enregistrement des données du contexte...", "saved": "Données du contexte sauvegardées avec succès !", "exported": "Données exportées avec succès!", "participantAdded": "Nouveau participant a<PERSON><PERSON>"}, "errors": {"loadingError": "Erreur de chargement du contexte", "retry": "<PERSON><PERSON><PERSON><PERSON>", "saveError": "<PERSON><PERSON><PERSON> lors de la sauvegarde", "exportError": "Erreur lors de l'exportation", "invalidParticipants": "Certains participants n'ont pas de nom ou sont invalides et ne seront pas sauvegardés. Continuer ?", "serverError": "E<PERSON>ur lors de la communication avec le serveur", "fileGenerationError": "<PERSON><PERSON>ur lors de la génération du fichier"}, "tooltips": {"export": "Exporter les données au format JSON", "save": "Sauvegarder les données du contexte"}, "required": "*", "advice": {"title": "Conseil :", "missionScope": "Définissez clairement la mission et le périmètre de votre analyse pour guider l'identification des risques et la définition des rôles. Une mission et un périmètre bien définis facilitent l'attribution des responsabilités RACI."}, "table": {"participant": "Participant", "position": "Fonction / Poste", "workshop1": "Atelier 1", "workshop2": "Atelier 2", "workshop3": "Atelier 3", "workshop4": "Atelier 4", "workshop5": "Atelier 5", "noMissions": "Aucune mission ajoutée", "noParticipants": "Aucun participant à afficher.", "addParticipantHint": "Cliquez sur \"Ajouter Participant\" pour commencer.", "cannotDisplayVisualization": "Impossible d'afficher la visualisation", "addParticipantsHint": "Ajoutez des participants et des ateliers, ou vérifiez les données de test pour activer la visualisation RACI."}, "raci": {"legend": "Légende RACI", "responsible": "Responsable", "responsibleDesc": "Exécute la tâche", "accountable": "Approbateur", "accountableDesc": "Valide le résultat", "consulted": "Consulté", "consultedDesc": "<PERSON>ne son avis", "informed": "Informé", "informedDesc": "Est tenu au courant", "notInvolved": "Non Impliqué", "notInvolvedDesc": "Ne participe pas"}, "visualization": {"matrixSynthesis": "<PERSON><PERSON>", "workshopFocus": "Focus Atelier", "participantFocus": "Focus Participant", "filterByRole": "Filtrer par Rôle", "allRoles": "To<PERSON> les rôles", "filterByWorkshop": "Filtrer par Atelier", "allWorkshops": "Tous les ateliers", "activeFilters": "Filtres actifs:", "role": "Rôle:", "workshop": "Atelier:", "removeRoleFilter": "<PERSON><PERSON><PERSON> le filtre de rôle", "removeWorkshopFilter": "<PERSON><PERSON><PERSON> le filtre d'atelier", "function": "Fonction", "distribution": "Répartition", "globalDistribution": "Distribution Globale des Rôles Assignés", "noAssignments": "Aucune assignation de rôle trouvée avec les filtres actuels.", "noParticipant": "Aucun participant", "noResults": "Aucun résultat à afficher", "adjustFilters": "Les filtres sélectionnés ne retournent aucune donnée. Essayez d'ajuster les filtres ou vérifiez les données RACI saisies.", "noAssignmentForParticipant": "Aucune assignation pour ce participant avec les filtres actuels.", "none": "Aucun"}}, "dreadedEvents": {"title": "Événements Redoutés", "breadcrumb": {"workshop1": "Atelier 1", "dreadedEvents": "Événements Redoutés"}, "buttons": {"save": "<PERSON><PERSON><PERSON><PERSON>", "saving": "Sauvegarde...", "help": "Aide", "add": "Ajouter", "edit": "Modifier", "delete": "<PERSON><PERSON><PERSON><PERSON>", "cancel": "Annuler", "validate": "Valider", "export": "Exporter", "reset": "Réinitialiser", "resetFilters": "Réinitialiser les filtres", "close": "<PERSON><PERSON><PERSON>", "start": "Commencer", "loadMore": "Plus de suggestions", "loading": "Chargement...", "addSelection": "Ajouter la sélection", "getAiSuggestions": "Obtenir Suggestions IA", "searchingAi": "Recherche IA...", "updateEvent": "Mettre à jour l'événement", "addCustomEvent": "Ajouter l'événement personnalisé", "cancelModification": "Annuler la modification"}, "sections": {"definition": "Définition des événements redoutés", "criteriaSelection": "Sélection des critères", "suggestions": "Suggestions", "customEvent": "Év<PERSON><PERSON> person<PERSON>", "identifiedEvents": "Événements redoutés identifiés", "selectCriteria": "Sélectionnez les critères", "proposalsFor": "Propositions pour"}, "fields": {"businessValue": "<PERSON><PERSON> métier concern<PERSON>", "securityPillar": "Pilier de sécurité impacté", "eventName": "Nom de l'événement", "description": "Description", "severity": "<PERSON><PERSON><PERSON>", "impacts": "Impacts (sélection multiple)", "gravity": "Gravité"}, "placeholders": {"selectBusinessValue": "<PERSON><PERSON><PERSON><PERSON>ner une valeur métier", "eventName": "Ex: <PERSON><PERSON> de donn<PERSON> client", "eventDescription": "Description de l'événement redouté et ses conséquences", "searchSuggestion": "Rechercher une suggestion...", "searchEvent": "Rechercher un événement..."}, "status": {"saving": "Enregistrement des événements redoutés...", "saved": "Événements redoutés sauvegardés avec succès!", "saveError": "<PERSON><PERSON><PERSON> lors de la sauvegarde", "eventAdded": "Événement redouté \"{name}\" ajouté avec succès!", "eventRemoved": "Événement redouté \"{name}\" supprimé avec succès!", "eventUpdated": "Événement redouté \"{name}\" mis à jour avec succès!", "suggestionsAdded": "{count} suggestion{plural} ajoutée{plural} avec succès!", "aiSuggestionsGenerated": "{count} suggestion{plural} IA générée{plural} avec succès!", "newAiSuggestionsGenerated": "{count} nouvelle{plural} suggestion{plural} IA générée{plural}!", "noNewAiSuggestions": "Aucune nouvelle suggestion IA n'a pu être générée.", "noAiSuggestions": "Aucune suggestion IA n'a pu être générée.", "fillRequiredFields": "Veu<PERSON>z remplir tous les champs obligatoires.", "selectAtLeastOne": "Veuillez sélectionner au moins une suggestion.", "selectBusinessValueAndPillar": "Veuillez d'abord sélectionner une Valeur Métier et un Pilier de Sécurité.", "selectAnalysisValuePillar": "Veuillez sélectionner une analyse, une valeur métier et un pilier de sécurité.", "selected": "sélectionné(s)", "events": "événement(s)", "eventsDisplayed": "événement{plural} affiché{plural} sur", "bulkDeleteConfirm": "Êtes-vous sûr de vouloir supprimer {count} événement{plural} ?", "bulkDeleteSuccess": "{count} événement{plural} supprimé{plural} avec succès", "notSpecified": "Non spécifié", "undefined": "Non défini"}, "empty": {"noEvents": "Aucun événement redouté défini", "noEventsDescription": "Sélectionnez une valeur métier et un pilier de sécurité, puis ajoutez des événements redoutés pour les voir apparaître ici.", "noSuggestions": "Aucune suggestion trouvée.", "noSuggestionsForPillar": "Aucune suggestion disponible pour ce pilier. Sélectionnez un autre pilier de sécurité.", "noSearchResults": "Aucune suggestion ne correspond à votre recherche", "noResults": "Aucun résultat trouvé", "noResultsDescription": "Aucun événement ne correspond à vos critères de recherche. Essayez de modifier vos filtres.", "selectCriteriaDescription": "Pour définir des événements redoutés, veuillez d'abord sélectionner une valeur métier et un pilier de sécurité impacté dans la colonne de gauche."}, "info": {"definition": "Pour chaque valeur mé<PERSON>, identifiez les événements redoutés qui pourraient impacter chacun des piliers de sécurité associés et évaluez leur gravité.", "businessValuesWithEvents": "Valeurs métier avec événements redoutés existants"}, "table": {"headers": {"businessValue": "<PERSON><PERSON>", "impactedPillar": "Pilier impacté", "dreadedEvent": "Événement redouté", "impacts": "Impacts", "severity": "Gravité", "actions": "Actions"}}, "modal": {"aiSuggestions": "Suggestions IA pour \"{businessValue}\" ({pillar})", "editEvent": "Modifier l'événement", "createCustomEvent": "C<PERSON>er un événement personnalisé", "error": "<PERSON><PERSON><PERSON>"}, "export": {"confirmCsv": "Voulez-vous exporter les événements redoutés au format CSV?", "toImplement": "Fonctionnalité d'export à implémenter"}, "guide": {"title": "Guide d'utilisation", "steps": {"identify": {"title": "Identifier les événements redoutés", "description": "Identifiez les événements redoutés qui pourraient affecter vos valeurs métier, comme la divulgation d'informations confidentielles ou l'indisponibilité d'un service critique."}, "associate": {"title": "Associer aux valeurs métier", "description": "Pour chaque événement redouté, associez-le à une ou plusieurs valeurs métier qui seraient impactées."}, "evaluate": {"title": "Évaluer les impacts", "description": "Évaluez l'impact de chaque événement redouté sur différentes dimensions : missions/services, humain/matériel/environnemental, gouvernance, financier, juridique, et image/confiance."}, "pillars": {"title": "Identifier les piliers de sécurité", "description": "Associez chaque événement redouté à un pilier de sécurité (confidentialité, intégrité, disponibilité, auditabilité, preuve, traçabilité)."}}, "advice": {"title": "Conseil", "description": "Utilisez le bouton de suggestions IA pour générer des événements redoutés pertinents en fonction de vos valeurs métier et du contexte de votre organisation."}}}, "severity": {"minor": "Mineure", "moderate": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "major": "<PERSON><PERSON><PERSON>", "critical": "Critique", "catastrophic": "Catastrophique"}, "impacts": {"missions_services": "Impacts sur les missions et services de l'organisation", "humain_materiel_environnemental": "<PERSON>s humains, matériels ou environnementaux", "gouvernance": "Impacts sur la gouvernance", "financier": "Impacts financiers", "juridique": "Impacts juridiques", "image_confiance": "Impacts sur l'image et la confiance"}, "securityPillars": {"confidentialite": "Confidentialité", "integrite": "Intégrité", "disponibilite": "Disponibilité", "tracabilite": "Traçabilité", "preuve": "<PERSON><PERSON>", "auditabilite": "Auditabilité"}, "workshop2": {"title": "Atelier 2 - Sources de risque et objectifs visés", "navigationTitle": "Atelier 2: SR et OV", "currentAnalysis": "Analyse: {{name}}", "noAnalysisSelected": "Aucune analyse sélectionnée", "activities": {"identifySROV": "Identifier SR/OV", "evaluateSROV": "Évaluer SR/OV", "selectSROV": "Sélectionner SR/OV"}, "navigation": {"identifySROV": "Identifier les SR et OV", "evaluateSROV": "Évaluer les couples SR/OV", "associateSRER": "Associer les SR aux ER"}, "activity1": {"breadcrumb": "Activité 1", "title": "Identifier les sources de risque et objectifs visés", "riskSourcesByCategory": "Sources de risque par catégorie", "errors": {"noAnalysisSelected": "Aucune analyse sélectionnée", "invalidApiResponse": "Réponse invalide de l'API", "aiGenerationError": "Erreur lors de la génération des suggestions IA. Veuillez réessayer.", "noSuggestionSelected": "Aucune suggestion sélectionnée"}, "success": {"sourcesAdded": "{{count}} source(s) de risque ajoutée(s)", "sourcesAddedBeforeSave": "{{count}} source(s) de risque ajoutée(s) avant sauvegarde"}, "ai": {"suggestions": "Suggestions IA", "generating": "Génération IA...", "modalTitle": "Suggestions IA de Sources de Risque", "generatingInProgress": "Génération de suggestions IA en cours...", "analyzingContext": "Analyse de votre contexte métier et du paysage des menaces", "noSuggestions": "Aucune suggestion IA disponible", "createManually": "V<PERSON> pouvez créer des sources de risque manuellement avec le formulaire ci-dessus", "createManuallyButton": "<PERSON><PERSON><PERSON> une Source Manuel<PERSON>", "suggestionsTitle": "Sources de Risque Générées par IA", "selectSuggestions": "Sélectionnez les sources de risque que vous souhaitez ajouter à votre analyse :", "selectedCount": "{{count}} sélectionnée(s)", "uncategorized": "Non catégorisé", "motivation": "Motivation", "activity": "Activité", "resources": "Ressources", "moreSuggestions": "Plus de Suggestions", "addButton": "Ajouter les Sources Sélectionnées"}, "levels": {"low": "Faible", "medium": "<PERSON><PERSON><PERSON>", "high": "<PERSON><PERSON><PERSON>"}, "threatCategories": {"title": "Catégories de menaces", "selectedCount": "{{count}} catégories sélectionnées sur {{total}}", "selectButton": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "selectedButton": "Sélectionné", "deselectButton": "Désé<PERSON><PERSON>ner"}, "addSource": {"title": "Ajouter une source de risque", "subtitle": "Sélectionnez une catégorie et ajoutez une nouvelle source", "selectedCategory": "<PERSON><PERSON><PERSON><PERSON>", "noCategory": "Aucune catégorie s<PERSON>ée", "unknownCategory": "Catégorie inconnue", "name": "Nom", "namePlaceholder": "Nom de la source", "targetObjectiveCategory": "Catégorie d'Objectif <PERSON>", "selectObjectiveCategory": "Sélectionner une catégorie d'objectif", "targetObjectiveDetail": "Détail de l'Objectif Visé", "targetObjectiveDetailRequired": "Détail de l'Objectif Visé *", "targetObjectivePlaceholder": "Précisez l'objectif visé par la source", "motivation": "Motivation", "activity": "Activité", "resources": "Ressources", "description": "Description", "descriptionPlaceholder": "Description (optionnelle)", "addButton": "Ajouter la source"}, "sourcesList": {"title": "Catégories de menaces", "noSources": "Aucune source de risque pour cette catégorie", "noSourcesHint": "Sélectionnez cette catégorie et utilisez le formulaire en haut pour ajouter une source", "editingTitle": "Modification de la source de risque", "saveButton": "Enregistrer", "cancelButton": "Annuler", "editButton": "Modifier", "deleteButton": "<PERSON><PERSON><PERSON><PERSON>", "nameLabel": "Nom de la source de risque", "descriptionLabel": "Description", "targetObjectiveLabel": "Détail de l'objectif visé", "motivationLabel": "Motivation", "activityLabel": "Activité", "resourcesLabel": "Ressources"}, "table": {"headers": {"riskSource": "Source de risque", "targetObjectiveDetail": "<PERSON><PERSON><PERSON>O<PERSON>", "objectiveCategory": "Catégorie d'OV", "motivation": "Motivation", "activity": "Activité", "resources": "Ressources"}}, "messages": {"selectCategory": "Veuillez sélectionner une catégorie", "nameRequired": "Le nom de la source de risque est requis", "targetObjectiveRequired": "Le détail de l'objectif visé est requis", "sourceAdded": "Source de risque ajoutée", "sourceUpdated": "Source de risque mise à jour", "sourceDeleted": "Source de risque supprimée", "sourcesSaved": "Sources de risque sauvegardées avec succès", "saveError": "Erreur lors de la sauvegarde des sources de risque", "noAnalysisSelected": "Aucune analyse sélectionnée", "noCategorySelected": "Aucune catégorie de menace sélectionnée", "selectCategoriesFirst": "Veuillez d'abord sélectionner des catégories de menaces dans l'onglet \"Catégories de Menaces\"."}, "objectiveCategories": {"confidentialite": "CONFIDENTIALITÉ", "integrite": "INTÉGRITÉ", "disponibilite": "DISPONIBILITÉ", "tracabilite": "TRAÇABILITÉ", "preuve": "PREUVE", "defi": "DÉFI, AMUSEMENT"}, "levelLabels": {"faible": "Faible", "moyen": "<PERSON><PERSON><PERSON>", "eleve": "<PERSON><PERSON><PERSON>", "faibles": "Faibles", "moyennes": "Mo<PERSON>nnes", "importantes": "Importantes"}, "categories": {"etatique": {"name": "ÉTATIQUE", "description": "États, agences de renseignement.", "details": "Attaques professionnelles, ressources stables, capacité opérationnelle à long terme. Peuvent acheter ou découvrir des vulnérabilités zero-day (0-Day) et s'infiltrer dans des réseaux isolés."}, "criminel": {"name": "CRIME ORGANISÉ", "description": "Organisations cybercriminelles (mafias, gangs, officines).", "details": "Escroqueries en ligne, rançongiciels, exploitation de botnets. Opérations sophistiquées à des fins lucratives. Certains peuvent acheter des vulnérabilités zero-day."}, "terroriste": {"name": "TERRORISTE", "description": "Cyberterroristes, cybermilices.", "details": "Attaques peu sophistiquées mais déterminées visant la déstabilisation et la destruction : déni de service, arrêts de systèmes critiques."}, "activiste": {"name": "ACTIVISTE IDÉOLOGIQUE", "description": "Cyber-hacktivistes, groupements d'intérêt, sectes.", "details": "Modes opératoires similaires aux cyberterroristes mais avec des intentions moins destructrices. Véhiculent une idéologie ou un message."}, "officine": {"name": "OFFICINE SPÉCIALISÉE", "description": "Cybermercenaires avec capacités techniques élevées.", "details": "Créent et vendent des outils et kits d'attaque. Principalement motivés par le gain financier."}, "amateur": {"name": "AMATEUR", "description": "Hackers \"script-kiddies\" ou avec bonnes connaissances informatiques.", "details": "Motivés par la reconnaissance sociale, le divertissement ou le défi. Utilisent des kits d'attaque accessibles en ligne."}, "vengeur": {"name": "VENGEUR", "description": "Individus guidés par un esprit de vengeance ou sentiment d'injustice.", "details": "Ex-employ<PERSON> licenci<PERSON>, prestataires mécontents. Redoutables par leur détermination et leur connaissance interne des systèmes."}, "malveillant": {"name": "MALVEILLANT PATHOLOGIQUE", "description": "Individus aux motivations pathologiques ou opportunistes.", "details": "Concurrents d<PERSON>, clients ma<PERSON><PERSON><PERSON><PERSON><PERSON>, escrocs. Peuvent exploiter des kits d'attaque ou sous-traiter à des officines spécialisées."}}}, "activity2": {"breadcrumb": "Activité 2", "title": "Évaluer les couples SR/OV", "evaluationTitle": "Évaluation des couples SR/OV", "saving": "Sauvegarde en cours...", "views": {"table": "<PERSON><PERSON>", "visualization": "Visualisation"}, "errors": {"sourceWithoutName": "Source sans nom", "unknownCategory": "Catégorie inconnue", "noRiskSources": "Aucune source de risque trouvée. Veuillez en ajouter dans l'Activité 1.", "loadingError": "Erreur lors du chargement des données: {{message}}", "unknownError": "<PERSON><PERSON><PERSON> inconnue", "saveError": "Erreur lors de la sauvegarde: {{message}}", "errorWithMessage": "Erreur: {{message}}", "saveRiskSourcesError": "Erreur lors de la sauvegarde des sources de risque"}, "success": {"riskSourcesSaved": "Sources de risque sauvegardées avec succès"}, "print": {"preparing": "Préparation de l'impression...", "preparingDocument": "Préparation du document pour impression...", "visualizationTitle": "Visualisation des couples SR/OV", "viewByObjective": "Vision par objectif visé", "viewBySource": "Vision par source de risque", "title": "Évaluation des couples SR/OV", "analysis": "Analyse", "date": "Date", "noJustification": "Aucune justification fournie", "notSelected": "Non retenu", "page1": "Page 1", "page2": "Page 2", "generatedOn": "Document généré le", "documentReady": "Document prêt pour impression"}, "table": {"headers": {"riskSource": "Source de risque", "category": "<PERSON><PERSON><PERSON><PERSON>", "targetObjective": "Object<PERSON> vis<PERSON>", "motivation": "Motivation", "activity": "Activité", "resources": "Ressources", "level": "Niveau", "selected": "Retenu", "justification": "Justification"}, "search": "Rechercher...", "allCategories": "Toutes les catégories", "total": "Total", "retained": "<PERSON><PERSON><PERSON>", "highLevel": "Niveau élevé", "noResults": "Aucun couple SR/OV ne correspond à votre recherche", "resetFilters": "Réinitialiser les filtres", "noJustification": "Aucune justification fournie", "notRetained": "Non retenu", "clickToEdit": "Cliquez pour modifier", "cancel": "Annuler", "save": "Enregistrer", "edit": "Modifier", "confirm": "Confirmer", "enterJustification": "Entrez une justification..."}, "visualization": {"visionByObjective": "Vision par objectif visé", "visionBySource": "Vision par source de risque", "pointsRepresentSources": "Les points représentent les sources de risque (SR)", "pointsRepresentObjectives": "Les points représentent les objectifs visés (OV)", "interpretationGuide": "Guide d'interprétation", "dragToModifyLevel": "Faites glisser un point pour modifier son niveau (1-3). Plus proche du centre = niveau plus élevé.", "dragToModifyImpact": "Faites glisser un point pour modifier son impact (1-3). Plus proche du centre = impact plus important.", "criticalZone": "Zone critique", "moderateZone": "Zone modérée", "lowZone": "Zone faible", "level": "Niveau", "priorityAttention": "Attention prioritaire requise", "vigilanceRecommended": "Vigilance recommandée", "limitedRisk": "Risque limité", "interpretation": "Interprétation", "interpretationSources": "Cette visualisation montre comment les sources de risque se répartissent selon les objectifs visés et leur niveau de criticité.", "interpretationObjectives": "Cette visualisation montre comment les objectifs visés se répartissent selon les sources de risque et leur niveau de criticité.", "closerToCenter": "Les éléments plus proches du centre représentent un niveau de risque plus élevé et nécessitent une attention particulière.", "retained": "Retenu", "notRetained": "Non retenu", "dataTable": {"id": "ID", "name": "Nom", "status": "Statut", "level": "Niveau", "sector": "<PERSON><PERSON><PERSON>", "activity": "Activité", "motivation": "Motivation", "resources": "Ressources", "objective": "Objectif", "category": "<PERSON><PERSON><PERSON><PERSON>", "actions": "Actions", "critical": "Critique", "moderate": "<PERSON><PERSON><PERSON><PERSON>", "low": "Faible", "remove": "<PERSON><PERSON><PERSON>", "retain": "Retenir", "riskSources": "Sources de risque", "targetObjectives": "Objectifs visés"}}}, "activity3": {"breadcrumb": "Activité 3", "title": "Association SR/ER", "subtitle": "Association des Sources de Risque Retenues aux Événements Redoutés", "showTable": "<PERSON><PERSON><PERSON><PERSON> le tableau", "hideTable": "Ma<PERSON>r le tableau", "saving": "Sauvegarde des associations sources de risque / événements redoutés...", "errors": {"loadingError": "Erreur lors du chargement des données", "saveError": "Erreur lors de la sauvegarde des associations"}, "success": {"associationsSaved": "Associations sauvegardées avec succès"}, "cards": {"retainedRiskSources": "Sources de Risque Retenues", "outOfIdentified": "sur {{total}} sources identifiées", "dreadedEvents": "Événements Redoutés", "eventsAvailable": "événements disponibles", "associations": "Associations", "associationsCreated": "associations créées"}, "filter": {"title": "Filtrer les sources de risque", "search": "<PERSON><PERSON><PERSON>", "searchPlaceholder": "Rechercher une source de risque...", "filterByCategory": "Filtrer par catégorie", "allCategories": "Toutes les catégories", "activeFilters": "Filtres actifs", "category": "<PERSON><PERSON><PERSON><PERSON>", "reset": "Réinitialiser", "resetFilters": "Réinitialiser les filtres"}, "loading": {"title": "Chargement des données", "message": "Veuillez patienter pendant que nous récupérons les informations..."}, "empty": {"noRetainedSources": "Aucune source de risque retenue trouvée", "noSourcesRetained": "Aucune source de risque n'a été retenue dans l'activité précédente.", "selectSourcesFirst": "Veuillez d'abord sélectionner des sources de risque à retenir dans l'activité précédente avant de continuer.", "noMatchingCriteria": "Aucune source de risque ne correspond à vos critères de recherche."}, "sourceCard": {"objective": "Objectif", "targetObjective": "<PERSON><PERSON><PERSON>", "notSpecified": "Non spécifié", "noDreadedEvents": "Aucun événement redouté disponible", "eventsSelected": "{{count}} événement(s) sélectionné(s)", "selectEvents": "Sélectionner des événements redoutés..."}, "guide": {"title": "Guide d'utilisation", "selectRiskSources": "Sélectionner les sources de risque", "selectRiskSourcesDesc": "Parcourez les sources de risque retenues et cliquez sur une carte pour voir les détails et associer des événements redoutés.", "associateDreadedEvents": "Associer aux événements redoutés", "associateDreadedEventsDesc": "Pour chaque source de risque, sélectionnez un ou plusieurs événements redoutés pertinents dans la liste.", "understandSecurityPillars": "Comprendre les piliers de sécurité", "understandSecurityPillarsDesc": "Chaque événement redouté est lié à un pilier de sécurité (confidentialité, intégrité, disponibilité, etc.) qui sera affecté.", "evaluateGravity": "Évaluer la gravité", "evaluateGravityDesc": "La gravité de chaque association est déterminée par l'impact de l'événement redouté et les capacités de la source de risque.", "consultSummaryTable": "Consulter le tableau récapitulatif", "consultSummaryTableDesc": "Utilisez le bouton 'Afficher le tableau' pour voir toutes les associations dans un format tabulaire complet.", "tip": "Conseil", "tipMessage": "N'oubliez pas d'enregistrer vos associations avant de quitter la page pour ne pas perdre votre travail.", "start": "Commencer"}}}, "workshop3": {"title": "Atelier 3: Scénarios stratégiques", "navigationTitle": "Atelier 3: <PERSON><PERSON><PERSON><PERSON>", "subtitle": "Cartographier l'écosystème et élaborer des scénarios stratégiques pour l'analyse de risque.", "activities": {"mapEcosystem": "Cartographier l'écosystème", "strategicScenarios": "Scénarios stratégiques", "attackPaths": "Chemins d'attaque", "securityMeasures": "Mesures de sécurité", "eventProgression": "Progression des événements"}, "navigation": {"mapEcosystem": "Cartographier l'écosystème", "strategicScenarios": "Scénarios stratégiques", "attackPaths": "Chemins d'attaque", "securityMeasures": "Mesures de sécurité", "eventProgression": "Progression des PTR"}, "activity1": {"breadcrumb": "Cartographier l'écosystème", "title": "Cartographier l'écosystème", "stakeholder": "<PERSON><PERSON>", "stakeholders": "Parties Prenantes", "addStakeholder": "Ajouter une partie prenante", "editStakeholder": "Modifier la partie prenante", "filters": "Filtres", "retained": "retenue", "notRetained": "non retenue", "saving": "Enregistrement des parties prenantes...", "updatingThresholds": "Mise à jour des seuils...", "radarVisualization": "Visualisation radar", "modifyThresholds": "Modifier les seuils", "filter": {"allTypes": "Tous les types", "internal": "Interne", "external": "Externe", "allCategories": "Toutes les catégories"}, "categories": {"client": "Client", "partner": "Partenaire", "provider": "Presta<PERSON>", "technical": "Service technique", "business": "Service métier", "subsidiary": "Filiale"}, "legend": {"title": "Légende", "zones": "ZONES", "danger": "Danger", "control": "Contr<PERSON>le", "watch": "<PERSON><PERSON><PERSON>", "types": "TYPES", "categories": "CATÉGORIES", "cyberReliability": "Fiabilit<PERSON>", "exposure": "Exposition", "weak": "Faible", "average": "<PERSON><PERSON><PERSON>", "good": "<PERSON><PERSON>", "excellent": "<PERSON>e", "minimal": "Minimal", "moderate": "Moderate", "high": "High", "critical": "Critical"}, "table": {"sortBy": "Trier par", "name": "Nom", "category": "<PERSON><PERSON><PERSON><PERSON>", "type": "Type", "threat": "Menace", "dependency": "<PERSON><PERSON><PERSON>", "penetration": "Pén", "cyberMaturity": "Mat", "trust": "Conf", "retained": "Retenu", "zone": "Zone", "actions": "Actions", "rank": "<PERSON>ng"}, "loading": "Chargement des parties prenantes...", "empty": {"noStakeholders": "Aucune partie prenante", "addStakeholders": "Ajoutez des parties prenantes pour commencer à cartographier l'écosystème."}, "errors": {"loadingError": "Erreur lors du chargement des données", "saveError": "Erreur: {{message}}", "unknownError": "<PERSON><PERSON><PERSON> inconnue", "saveStakeholdersError": "Erreur lors de l'enregistrement des parties prenantes", "updateThresholdsError": "Erreur lors de la mise à jour des seuils"}, "success": {"stakeholdersSaved": "Parties prenantes enregistrées avec succès", "thresholdsUpdated": "<PERSON><PERSON><PERSON> mis à jour avec succès", "stakeholderUpdated": "Partie prenante mise à jour avec succès", "stakeholderAdded": "Partie prenante ajoutée avec succès", "stakeholderDeleted": "{{name}} supprimée avec succès", "stakeholderStatusChanged": "<PERSON>ie prenante {{name}} marquée comme {{status}}"}, "stakeholderForm": {"name": "Nom", "namePlaceholder": "Nom de la partie prenante", "category": "<PERSON><PERSON><PERSON><PERSON>", "type": "Type", "rank": "<PERSON>ng", "rank1": "Rang 1 (interaction directe)", "rank2": "Rang 2 (interaction indirecte)", "rank3": "Rang 3 (interaction distante)", "description": "Description", "descriptionPlaceholder": "Description de la partie prenante", "criteriaEvaluation": "Évaluation des critères", "dependency": "Dépendance", "penetration": "Pénétration", "cyberMaturity": "Maturité cyber", "trust": "Confiance", "dependency1": "Relation non nécessaire aux fonctions stratégiques", "dependency2": "Relation utile aux fonctions stratégiques", "dependency3": "Relation indispensable mais non exclusive", "dependency4": "Relation indispensable et unique (pas de substitution possible à court terme)", "penetration1": "Pas d'accès ou accès avec privilèges de type utilisateur à des terminaux utilisateurs", "penetration2": "Accès avec privilèges de type administrateur à des terminaux utilisateurs ou accès physique aux sites", "penetration3": "Accès avec privilèges de type administrateur à des serveurs « métier »", "penetration4": "Accès avec privilèges de type administrateur à des équipements d'infrastructure ou accès physique aux salles serveurs", "cyberMaturity1": "Des règles d'hygiène informatique sont appliquées ponctuellement et non formalisées", "cyberMaturity2": "Les règles d'hygiène et la réglementation sont prises en compte, sans intégration dans une politique globale", "cyberMaturity3": "Une politique globale est appliquée en matière de sécurité numérique", "cyberMaturity4": "La partie prenante met en œuvre une politique de management du risque", "trust1": "Les intentions de la partie prenante ne peuvent être évaluées", "trust2": "Les intentions de la partie prenante sont considérées comme neutres", "trust3": "Les intentions de la partie prenante sont connues et probablement positives", "trust4": "Les intentions de la partie prenante sont parfaitement connues et pleinement compatibles", "calculatedThreatLevel": "Niveau de menace calculé", "threatLevelFormula": "(Dépendance × Pénétration) / (Maturité cyber × Confiance)", "notes": "Notes", "notesPlaceholder": "Notes addition<PERSON>es", "retainedDescription": "Les parties prenantes retenues seront incluses dans l'analyse de risque", "update": "Mettre à jour", "add": "Ajouter"}, "guide": {"title": "Guide: Cartographier l'écosystème", "description": "Cette activité vous permet de cartographier l'écosystème de parties prenantes et d'évaluer le niveau de menace qu'elles représentent pour l'objet de l'étude.", "stepsToFollow": "Étapes à suivre", "step1Title": "Identifier les parties prenantes", "step1Description": "Identifiez les parties prenantes internes et externes qui interagissent avec l'objet de l'étude. Classez-les par catégorie (client, partenaire, prestataire, etc.) et par type (interne ou externe).", "step2Title": "Évaluer les critères d'exposition", "step2Description": "Pour chaque partie prenante, <PERSON><PERSON><PERSON><PERSON> le niveau de dépendance (relation vitale pour l'activité) et de pénétration (accès aux ressources internes).", "step3Title": "Évaluer les critères de fiabilité cyber", "step3Description": "Évaluez la maturité cyber (capacités en matière de sécurité) et la confiance (intentions et intérêts) de chaque partie prenante.", "step4Title": "Cal<PERSON>r le niveau de menace", "step4Description": "Le niveau de menace est calculé selon la formule : (Dépendance × Pénétration) / (Maturité cyber × Confiance). Plus le niveau est élevé, plus la partie prenante représente une menace."}}, "activity4": {"breadcrumb": "Mesures de Sécurité", "title": "Mesures de Sécurité", "generateWithAI": "Générer avec IA", "description": "Définissez des mesures de sécurité spécifiques pour l'écosystème, visant à réduire le niveau de danger induit par les parties prenantes critiques et à agir sur les scénarios stratégiques identifiés.", "saving": "Sauvegarde des mesures en cours...", "success": {"measuresSaved": "Mesures de sécurité sauvegardées avec succès"}, "errors": {"saveError": "<PERSON><PERSON><PERSON> lors de la sauvegarde", "error": "<PERSON><PERSON><PERSON>", "saveProblem": "Problème de sauvegarde"}}, "activity5": {"breadcrumb": "Progression des PTR", "title": "Progression des Plans de Traitement du Risque", "loading": "Chargement des données...", "description": "Suivez la progression des Plans de Traitement du Risque (PTR) en analysant la couverture des événements redoutés par les mesures de l'Atelier 1 et de l'Atelier 3. Identifiez les événements non traités, partiellement traités ou enrichis pour optimiser votre stratégie de sécurité.", "demoMode": "Mode Démonstration", "demoModeDescription": "Les données affichées sont des exemples. Pour voir vos données réelles, définissez des événements redoutés dans l'Atelier 1 et des mesures de sécurité dans l'Atelier 3.", "printTitle": "Progression des Plans de Traitement du Risque", "defaultAnalysisName": "Analyse EBIOS RM", "print": {"generalInfo": "Informations Générales", "analysis": "Analyse", "generationDate": "Date de génération", "eventCount": "Nombre d'événements", "globalStats": "Statistiques Globales", "totalEvents": "Total événements", "enrichedEvents": "Événements enrichis (A1 + A3)", "partialEvents": "Événements partiels (A1 ou A3)", "notTreatedEvents": "Événements non traités", "globalCoverageRate": "Taux de couverture global", "progressCharts": "Graphiques de Progression", "eventDistribution": "Répartition des Événements", "enriched": "<PERSON><PERSON><PERSON>", "partial": "Partiels", "notTreated": "Non traités", "progressionRates": "Taux de Progression", "enrichment": "Enrichissement", "globalCoverage": "Couverture Globale", "totalMeasures": "Mesures totales", "workshop1": "Atelier 1", "workshop3": "Atelier 3", "averageProgression": "Progression moyenne", "eventDetails": "Détail des Événements Redoutés", "dreadedEvent": "Événement Redouté", "businessValue": "<PERSON><PERSON>", "supportAssets": "Biens Supports", "progression": "Progression", "status": "Statut", "notDefined": "Non définie", "none": "Aucun", "measure": "mesure", "measures": "mesures", "noDataAvailable": "Aucune donnée disponible pour l'impression.", "toolName": "EBIOS RM - Risk Management Tool", "reportGenerated": "Rapport généré automatiquement le", "methodology": "Méthodologie EBIOS Risk Manager - ANSSI"}, "guide": {"title": "Guide de la Progression des PTR", "objective": "Objectif de cette activité", "objectiveDescription": "Cette activité vous permet de suivre la progression des Plans de Traitement du Risque (PTR) en analysant la couverture des événements redoutés par les mesures de l'Atelier 1 et de l'Atelier 3.", "progressionLogic": "Nouvelle logique de progression", "enriched": "<PERSON><PERSON><PERSON>", "enrichedDescription": "Événement traité par les deux ateliers (A1 + A3)", "partiallyTreated": "Partiellement <PERSON>", "partiallyTreatedDescription": "Événement traité par un seul atelier (A1 OU A3)", "notTreated": "Non traité", "notTreatedDescription": "Événement non traité par aucun atelier", "generateReport": "Générez un rapport de synthèse pour une analyse complète", "tableUsage": "Utilisation du tableau", "tableUsageDescription": "Utilisez les filtres pour analyser des sous-ensembles d'événements. Cliquez sur les flèches pour voir les détails des mesures de chaque atelier. Les statistiques en haut vous donnent une vue d'ensemble de la progression globale."}}, "activity2": {"breadcrumb": "Scénarios Stratégiques", "title": "Scénarios Stratégiques", "subtitle": "Définissez les scénarios stratégiques pour votre analyse de risque en associant les couples SR/OV, les événements redoutés, les valeurs métier et les parties prenantes.", "loading": "Chargement des données...", "loadingMessage": "Chargement des données des scénarios stratégiques...", "saveAttackPaths": "Enregistrer les chemins d'attaque", "searchPlaceholder": "Rechercher...", "allCategories": "Toutes catégories", "controlStatus": {"implemented": "Implémenté", "planned": "Planifié", "notDefined": "Non défini"}, "table": {"scenarios": "Scénarios", "riskSource": "Source de Risque", "targetObjective": "<PERSON><PERSON><PERSON>", "dreadedEvent": "Événement Redouté", "businessValueAssets": "Valeur Métier & Biens Support", "controlMeasures": "Mesures de Contrôle", "concernedStakeholders": "Parties Prenantes Concernées", "category": "<PERSON><PERSON><PERSON><PERSON>", "supportAssets": "Biens Support", "responsible": "Responsable", "noSupportAssets": "Aucun bien support associé", "noControlMeasures": "Aucune mesure de contrôle définie", "noRetainedStakeholders": "Aucune partie prenante retenue"}, "empty": {"noScenariosFound": "Aucun scénario stratégique trouvé", "modifySearchCriteria": "Essayez de modifier vos critères de recherche"}, "errors": {"loadingError": "Erreur lors du chargement des données. Veuillez réessayer.", "loadingDataError": "Erreur lors du chargement des données", "loadingErrorTitle": "Erreur de chargement", "noAttackPathSelected": "Aucun chemin d'attaque sélectionné. Veuillez sélectionner au moins une partie prenante pour un scénario.", "saveAttackPathsError": "Erreur lors de l'enregistrement des chemins d'attaque"}, "success": {"attackPathsSaved": "Les chemins d'attaque ont été enregistrés avec succès"}, "guide": {"title": "Guide des Scénarios Stratégiques", "context": {"title": "Contexte des scénarios stratégiques", "description": "Les scénarios stratégiques vous permettent d'identifier les chemins d'attaque potentiels en associant les sources de risque, les objectifs visés, les événements redoutés, les valeurs métier et les parties prenantes concernées."}, "understanding": {"title": "Comprendre le tableau", "scenarios": "Scénarios Stratégiques", "scenariosDesc": "Identifiant unique pour chaque scénario (S01, S02, etc.)", "riskSource": "Source de Risque", "riskSourceDesc": "La source de risque identifiée avec sa catégorie", "targetObjective": "<PERSON><PERSON><PERSON>", "targetObjectiveDesc": "L'objectif visé par la source de risque", "dreadedEvent": "Événement Redouté", "dreadedEventDesc": "Les événements redoutés associés à cette source de risque", "businessValue": "Valeur Métier & Biens Support", "businessValueDesc": "Les valeurs métier impactées et leurs biens support", "controlMeasures": "Mesures de Contrôle", "controlMeasuresDesc": "Les mesures de contrôle identifiées pour traiter ces risques", "stakeholders": "Parties Prenantes Concernées", "stakeholdersDesc": "Les parties prenantes concernées par ce scénario"}, "actions": {"title": "Actions possibles", "search": "Utilisez la barre de recherche pour filtrer les scénarios", "filter": "Filtrez par catégorie en utilisant le menu déroulant", "sort": "Cliquez sur les en-têtes de colonnes pour trier les données", "selectStakeholders": "Sélectionnez les parties prenantes concernées pour chaque scénario"}}}, "activity3": {"breadcrumb": "Chemins d'Attaque", "title": "Chemins d'Attaque", "subtitle": "Visualisez les chemins d'attaque identifiés pour votre analyse de risque.", "description": "Visualisez et analysez les chemins d'attaque identifiés pour votre analyse de risque. Chaque chemin relie une source de risque à un objectif visé via un événement redouté.", "loading": "Chargement des chemins d'attaque...", "loadingMessage": "Chargement des chemins d'attaque...", "searchPlaceholder": "Rechercher...", "visualizeSelection": "Visualiser sélection", "visualize": "Visualiser", "visualizeAttackPath": "Visualiser le chemin d'attaque", "collapse": "<PERSON><PERSON><PERSON><PERSON>", "details": "Détails", "closeVisualization": "<PERSON><PERSON><PERSON> la visualisation", "export": {"button": "Exporter", "csv": "Exporter CSV", "filename": "chemins-attaque"}, "table": {"selection": "Sélection", "reference": "Référence", "riskSource": "Source de Risque", "targetObjective": "<PERSON><PERSON><PERSON>", "dreadedEvent": "Événement Redouté", "businessValue": "<PERSON><PERSON>", "stakeholders": "Parties Prenantes", "actions": "Actions", "none": "Aucune"}, "empty": {"noAttackPaths": "Aucun chemin d'attaque trouvé", "noAttackPathsMessage": "Aucun chemin d'attaque n'a été défini pour cette analyse. Veuillez retourner à l'Activité 2 pour définir des chemins d'attaque."}, "errors": {"loadingError": "Erreur lors du chargement des données. Veuillez réessayer.", "loadingAttackPathsError": "Erreur lors du chargement des chemins d'attaque", "loadingErrorTitle": "Erreur de chargement", "selectAttackPath": "Veuillez sélectionner un chemin d'attaque"}, "detailsModal": {"attackPathDetails": "<PERSON>é<PERSON> du chemin d'attaque", "concernedStakeholder": "Partie Prenante concernée", "noAssociatedStakeholder": "Aucune partie prenante associée"}, "visualization": {"title": "Visualisation du Chemin d'Attaque: {{sourceRisk}} → {{dreadedEvent}}"}}}, "session": {"warning10Min": "Votre session expirera dans 10 minutes. Veuillez sauvegarder votre travail.", "warning5Min": "Votre session expirera dans 5 minutes. Veuillez sauvegarder votre travail immédiatement.", "expired": "Session expirée. Redirection vers la page de connexion..."}, "securityFoundation": {"title": "<PERSON><PERSON> Sécu<PERSON>", "breadcrumb": {"workshop1": "Atelier 1", "securityFoundation": "<PERSON><PERSON> Sécu<PERSON>"}, "buttons": {"save": "<PERSON><PERSON><PERSON><PERSON>", "saving": "Sauvegarde...", "import": "Importer CSV", "print": "<PERSON><PERSON><PERSON><PERSON>", "cancel": "Annuler", "add": "Ajouter", "modify": "Modifier", "delete": "<PERSON><PERSON><PERSON><PERSON>", "close": "<PERSON><PERSON><PERSON>", "selectAll": "<PERSON><PERSON>", "deselectAll": "<PERSON><PERSON>", "visible": "(Visible)", "remove": "<PERSON><PERSON><PERSON>", "link": "Lier...", "confirm": "Confirmer", "modifyRule": "Modifier cette règle", "deleteRule": "Supp<PERSON>er cette règle"}, "sections": {"frameworks": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "availableRules": "R<PERSON>gles disponibles", "selectedRules": "<PERSON><PERSON><PERSON> s<PERSON>", "summary": "Résumé", "status": "Statut", "justification": "Justification", "linkedDreadedEvents": "Événements Redoutés Associés", "rule": "<PERSON><PERSON><PERSON>"}, "placeholders": {"searchRule": "Rechercher une règle...", "frameworkName": "Ex: Exigences Projet Y", "configure": "Configurez...", "optional": "Optionnel..."}, "status": {"available": "disponible(s)", "selected": "sélectionnée(s)", "rulesFound": "règle(s) trouvée(s) dans le fichier CSV", "noFrameworkLoaded": "<PERSON><PERSON>n r<PERSON><PERSON><PERSON><PERSON><PERSON> chargé.", "noRuleFound": "Aucune règle trouvée pour ce terme.", "noRuleDefined": "Aucune règle définie pour ce référentiel.", "noRuleSelected": "Aucune règle sélectionnée pour ce référentiel.", "addRulesFromList": "Ajoutez des règles depuis la liste ci-dessus.", "selectFramework": "Sélectionnez un référentiel dans la liste de gauche pour commencer.", "loadingFrameworks": "Chargement des référentiels...", "noRuleSelectedSummary": "Aucune règle sélectionnée pour afficher le résumé.", "specifyFrameworkName": "Veuillez spécifier un nom pour le référentiel.", "importedOn": "Importé le", "frameworkImported": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> \"{{name}}\" importé avec succès! ({{count}} règles)", "printAreaNotFound": "Erreur: Impossible de trouver la zone d'impression.", "tableNotFound": "Erreur: Tableau non trouvé.", "printingInProgress": "Impression du résumé du socle de sécurité en cours...", "loadingData": "Chargement des données du socle de sécurité...", "loadingError": "Erreur de chargement:", "saveError": "<PERSON><PERSON><PERSON> <PERSON> Sauvegarde:", "noFrameworkDefinitions": "Aucune définition de référentiel trouvée pour cette analyse.", "ruleNotFoundForModification": "Erreur : R<PERSON><PERSON> introuvable pour la modification.", "ruleNotFound": "Erreur : <PERSON><PERSON><PERSON> introuvable.", "confirmDeleteRule": "Êtes-vous sûr de vouloir supprimer la règle \"{{name}}\" ? Cette action est irréversible.", "noAnalysisSelected": "Erreur : Aucune analyse sélectionnée pour la sauvegarde.", "savingSecurityFoundation": "Sauvegarde du socle de sécurité...", "rulesSelected": "{{count}} règle{{count, plural, one{} other{s}}} sélectionnée{{count, plural, one{} other{s}}}", "applied": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "partially": "Partiellement", "notApplied": "Non appliqué", "unknown": "Inconnu", "linkedCount": "{{count}} lié{{count, plural, one{} other{s}}} - Modifier", "noDreadedEventsAvailable": "Aucun événement redouté disponible.", "noDescriptionAvailable": "Aucune description disponible.", "undefined": "Non défini", "none": "Aucune", "noLinkedBusinessValue": "Au<PERSON>ne valeur métier liée trouvée.", "noDreadedEventsLinked": "Aucun événement redouté lié."}, "import": {"title": "Importer des règles depuis CSV", "frameworkName": "Nom du nouveau référentiel", "importButton": "Importer", "importTooltip": "Importer des règles depuis un fichier CSV"}, "modal": {"ruleDescription": "Description de la règle", "linkDreadedEvents": "Lier Événements Redoutés", "rule": "Règle :"}, "table": {"headers": {"framework": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rule": "<PERSON><PERSON><PERSON>", "status": "Statut", "justification": "Justification", "eventsValuesAssets": "Événements Redoutés / Valeurs Métier / Biens Supports", "actions": "Actions"}}}, "securityControls": {"title": "Mesures de Contrôle", "breadcrumb": {"workshop1": "Atelier 1", "securityControls": "Mesures de contrôle"}, "buttons": {"save": "<PERSON><PERSON><PERSON><PERSON>", "saving": "Sauvegarde...", "print": "<PERSON><PERSON><PERSON><PERSON>", "addControls": "Ajouter des Contrôles", "generateAI": "Générer des Suggestions", "cancel": "Annuler", "add": "Ajouter", "addSelection": "Ajouter la Sélection", "close": "<PERSON><PERSON><PERSON>", "modify": "Modifier", "delete": "<PERSON><PERSON><PERSON><PERSON>", "addControlsFromDatabase": "Ajouter des contrôles depuis la base de données", "suggestControlsWithAI": "Suggérer des contrôles avec l'IA"}, "sections": {"riskTreatmentPlan": "Plan de Traitement des Risques", "linkedRules": "Règles liées aux événements redoutés", "controls": "<PERSON><PERSON><PERSON><PERSON>", "treatment": "Traitement", "residualSeverity": "Gravité résiduelle", "responsible": "Responsable", "decision": "Décision", "date": "Date", "securityRules": "<PERSON><PERSON><PERSON> de Sécurité", "dreadedEvents": "Événements Redoutés", "identifiedControls": "Contrôles Identifiés", "control": "Contr<PERSON>le", "value": "<PERSON><PERSON>"}, "table": {"headers": {"rule": "<PERSON><PERSON><PERSON>", "dreadedEvent": "Événement redouté", "severity": "Gravité", "controls": "Contrôles de sécurité", "treatment": "Traitement du risque", "residualSeverity": "Gravité résiduelle", "responsible": "Responsable", "decision": "Décision d'implémentation", "actions": "Actions"}}, "treatment": {"reduce": "Ré<PERSON><PERSON> le risque", "transfer": "Transférer le risque", "avoid": "<PERSON><PERSON><PERSON> le risque", "accept": "Accepter le risque"}, "decision": {"implemented": "En place", "partial": "Partiel", "planned": "Planifié", "apply_before": "À appliquer avant le", "abandoned": "À abandonner"}, "severity": {"catastrophic": "Catastrophique", "critical": "Critique", "major": "<PERSON><PERSON><PERSON>", "moderate": "Significative", "minor": "Mineure", "undefined": "Non défini"}, "placeholders": {"selectTreatment": "Sélectionner un traitement", "selectSeverity": "Sélectionner une gravité", "selectDecision": "Sé<PERSON><PERSON>ner une décision", "responsibleName": "Nom du responsable", "selectDate": "Sélectionner une date", "who": "Qui?"}, "status": {"noLinkedRules": "Aucune règle liée aux événements redoutés trouvée.", "linkRulesFirst": "Veuillez d'abord lier des règles aux événements redoutés dans la section Socle de Sécurité.", "loadingControls": "Chargement des contrôles...", "noControlsFound": "Aucun contrôle trouvé dans la base de données.", "controlsFiltered": "Contrôles filtrés par:", "pillarOfEvent": "Pilier de l'événement redouté:", "treatmentStrategy": "Stratégie de traitement:", "generating": "Génération en cours...", "noSuggestions": "Aucune suggestion générée.", "suggestionsGenerated": "Suggestions générées :", "planSaved": "Plan de traitement sauvegardé avec succès!", "saveError": "Erreur lors de la sauvegarde du plan de traitement.", "rulesDefined": "règles définies.", "eventsIdentified": "événements identifiés.", "controlsIdentified": "contrôles identifiés.", "unknownRule": "<PERSON><PERSON><PERSON> inconnue", "noControlsSelected": "Aucun contrôle sélectionné/suggéré."}, "modal": {"addControls": "Ajouter des Contrôles de Sécurité", "aiSuggestions": "Suggérer des Contrôles (IA)", "forRule": "Pour la règle :", "linkedToEvent": "Liée à l'événement :", "generateSuggestions": "Générer des Suggestions", "error": "Erreur:"}, "info": {"riskTreatmentPlan": "Configurez les contrôles de sécurité pour chaque règle liée aux événements redoutés identifiés."}}}