// Constants for EBIOS RM application

// Define workshops list for RACI matrix
export const WORKSHOPS = [
  { id: 1, name: "Atelier 1" },
  { id: 2, name: "Atelier 2" },
  { id: 3, name: "Atelier 3" },
  { id: 4, name: "Atelier 4" },
  { id: 5, name: "Atelier 5" }
];

// Define roles list for RACI matrix
export const ROLES = [
  { value: "", label: "Sélectionner" },
  { value: "-", label: "Non participant" },
  { value: "R", label: "Responsable (R)" },
  { value: "A", label: "Approbateur (A)" },
  { value: "C", label: "Consulté (C)" },
  { value: "I", label: "Informé (I)" }
];

// Define position options list
export const POSITION_OPTIONS = [
  { value: "", label: "Sélectionner" },
  { value: "RSSI", label: "RSSI / CISO" },
  { value: "DSI", label: "DSI / CIO" },
  { value: "DPO", label: "DPO / DPD" },
  { value: "CEO", label: "CEO / PDG" },
  { value: "CFO", label: "CFO / Directeur Financier" },
  { value: "CTO", label: "CTO / Directeur Technique" },
  { value: "RISK", label: "Risk Manager" },
  { value: "AUDIT", label: "Auditeur" },
  { value: "JURIST", label: "Juriste" },
  { value: "COMM", label: "Communication" },
  { value: "PROJ", label: "Chef de projet" },
  { value: "CONS", label: "Consultant" },
  { value: "OTHER", label: "Autre..." }
];

// Severity options
export const SEVERITY_OPTIONS = [
  { value: "minor", label: "Mineure" },
  { value: "moderate", label: "Modérée" },
  { value: "major", label: "Majeure" },
  { value: "critical", label: "Critique" },
  { value: "catastrophic", label: "Catastrophique" }
];

// Security framework status options
export const FRAMEWORK_STATUS_OPTIONS = [
  { value: "applied", label: "Appliqué" },
  { value: "restricted", label: "Appliqué avec restrictions" },
  { value: "not-applied", label: "Non appliqué" },
  { value: "certification", label: "Certification obtenue" }
];

export const SECURITY_PILLARS = [
  { id: 'confidentialite', name: 'Confidentialité', color: '#4299E1' },
  { id: 'integrite', name: 'Intégrité', color: '#48BB78' },
  { id: 'disponibilite', name: 'Disponibilité', color: '#ECC94B' },
  { id: 'tracabilite', name: 'Traçabilité', color: '#ED8936' },
  { id: 'preuve', name: 'Preuve', color: '#8F11EA' },
  { id: 'Auditabilite', name: 'Auditabilité', color: '#BF1515' } // Changed id to match DB casing
];

// Suggestions d'événements redoutés par pilier
export const EVENT_SUGGESTIONS = {
  'confidentialite': [
    { name: "Fuite de données confidentielles", description: "Divulgation non autorisée des données sensibles à des tiers", severity: "major" },
    { name: "Vol de données par intrusion", description: "Accès non autorisé aux systèmes et exfiltration de données", severity: "critical" },
    { name: "Divulgation accidentelle d'informations", description: "Partage involontaire de données confidentielles par des collaborateurs", severity: "moderate" }
  ],
  'integrite': [
    { name: "Altération de données critiques", description: "Modification non autorisée de données essentielles au fonctionnement", severity: "major" },
    { name: "Corruption de base de données", description: "Détérioration de l'intégrité des données stockées", severity: "critical" },
    { name: "Injection de fausses données", description: "Introduction volontaire de données erronées dans le système", severity: "major" }
  ],
  'disponibilite': [
    { name: "Indisponibilité du service", description: "Interruption du service empêchant son utilisation normale", severity: "major" },
    { name: "Attaque par déni de service", description: "Saturation volontaire des ressources rendant le service inaccessible", severity: "critical" },
    { name: "Panne d'infrastructure", description: "Défaillance technique des composants d'infrastructure", severity: "moderate" }
  ],
  'tracabilite': [
    { name: "Perte des journaux d'activité", description: "Suppression ou altération des logs permettant de tracer les actions", severity: "major" },
    { name: "Désactivation de l'audit", description: "Désactivation volontaire des mécanismes de traçabilité", severity: "critical" },
    { name: "Actions non attribuables", description: "Impossibilité d'identifier l'auteur d'une action dans le système", severity: "moderate" }
  ],
  'preuve': [
    { name: "Répudiation d'une transaction", description: "Contestation de l'exécution d'une action par son auteur", severity: "major" },
    { name: "Falsification de preuve", description: "Modification ou suppression d'éléments probants", severity: "critical" },
    { name: "Défaut de conservation", description: "Non-respect des durées légales de conservation des preuves", severity: "moderate" }
  ],
  'auditabilite': [ // Using 'auditabilite' as key (without accent) but displaying 'Auditabilité' (with accent)
    { name: "Impossibilité d'audit", description: "Absence de mécanismes permettant l'audit du système", severity: "major" },
    { name: "Refus d'audit", description: "Opposition à la réalisation d'un audit interne ou externe", severity: "critical" },
    { name: "Lacunes des outils d'audit", description: "Outils d'audit inadaptés ou incomplets", severity: "moderate" }
  ]
};

// Constantes pour les niveaux de gravité
export const SEVERITY_LEVELS = [
  { value: 'minor', label: 'Mineure', color: '#10B981' },
  { value: 'moderate', label: 'Modérée', color: '#F59E0B' },
  { value: 'major', label: 'Majeure', color: '#F97316' },
  { value: 'critical', label: 'Critique', color: '#EF4444' },
  { value: 'catastrophic', label: 'Catastrophique', color: '#1F2937' }
];

// NEW: Dreaded Event Impacts
export const DREADED_EVENT_IMPACTS = [
  { value: 'missions_services', label: 'Impacts sur les missions et services de l’organisation' },
  { value: 'humain_materiel_environnemental', label: 'Impacts humains, matériels ou environnementaux' },
  { value: 'gouvernance', label: 'Impacts sur la gouvernance' },
  { value: 'financier', label: 'Impacts financiers' },
  { value: 'juridique', label: 'Impacts juridiques' },
  { value: 'image_confiance', label: 'Impacts sur l’image et la confiance' }
];

// Helper function to format multiple impacts
export const formatImpacts = (impacts) => {
  if (!impacts) return [];

  // If impacts is a string (single impact), convert to array
  if (typeof impacts === 'string') {
    return [impacts];
  }

  // If impacts is already an array, return it
  if (Array.isArray(impacts)) {
    return impacts;
  }

  // If impacts is a comma-separated string, split it
  if (typeof impacts === 'string' && impacts.includes(',')) {
    return impacts.split(',').map(i => i.trim());
  }

  return [];
};

// Helper function to get impact labels
export const getImpactLabels = (impacts) => {
  const impactArray = formatImpacts(impacts);
  return impactArray.map(impactValue => {
    const impact = DREADED_EVENT_IMPACTS.find(imp => imp.value === impactValue);
    return impact ? impact.label : impactValue;
  });
};

// Mapping de piliers pour compatibilité avec l'ancien format numérique
export const PILLAR_MAPPING = {
  '0': 'confidentialite',
  '1': 'disponibilite',
  '2': 'tracabilite',
  '3': 'preuve',
  '4': 'integrite',
  '5': 'auditabilite'
};

export const pillarLabels = {
  '0': 'Confidentialité',
  'confidentialite': 'Confidentialité',
  '4': 'Intégrité',
  'integrite': 'Intégrité',
  '1': 'Disponibilité',
  'disponibilite': 'Disponibilité',
  '2': 'Traçabilité',
  'tracabilite': 'Traçabilité',
  '5': 'Auditabilité',
  'auditabilite': 'Auditabilité',
  '3': 'Preuve',
  'preuve': 'Preuve'
};

// Définir les couleurs par pilier
export const pillarColors = {
  '0': { bg: 'bg-blue-500', text: 'text-white', border: 'border-blue-600', hover: 'hover:bg-blue-600' },
  'confidentialite': { bg: 'bg-blue-500', text: 'text-white', border: 'border-blue-600', hover: 'hover:bg-blue-600' },
  '4': { bg: 'bg-green-500', text: 'text-white', border: 'border-green-600', hover: 'hover:bg-green-600' },
  'integrite': { bg: 'bg-green-500', text: 'text-white', border: 'border-green-600', hover: 'hover:bg-green-600' },
  '1': { bg: 'bg-yellow-400', text: 'text-white', border: 'border-yellow-500', hover: 'hover:bg-yellow-500' },
  'disponibilite': { bg: 'bg-yellow-400', text: 'text-white', border: 'border-yellow-500', hover: 'hover:bg-yellow-500' },
  '2': { bg: 'bg-orange-400', text: 'text-white', border: 'border-orange-500', hover: 'hover:bg-orange-500' },
  'tracabilite': { bg: 'bg-orange-400', text: 'text-white', border: 'border-orange-500', hover: 'hover:bg-orange-500' },
  '3': { bg: 'bg-purple-500', text: 'text-white', border: 'border-purple-600', hover: 'hover:bg-purple-600' },
  'preuve': { bg: 'bg-purple-500', text: 'text-white', border: 'border-purple-600', hover: 'hover:bg-purple-600' },
  '5': { bg: 'bg-red-500', text: 'text-white', border: 'border-red-600', hover: 'hover:bg-red-600' },
  'auditabilite': { bg: 'bg-red-500', text: 'text-white', border: 'border-red-600', hover: 'hover:bg-red-600' }
};

// AI suggestions data structure by pillar
export const AI_SUGGESTIONS = {
  'confidentialite': [
    { name: "IA: Analyse des points d'exfiltration potentiels", description: "Évaluation automatisée des canaux possibles de fuite de données", severity: "major" },
    { name: "IA: Détection des comportements anormaux d'accès", description: "Surveillance des patterns d'accès inhabituels aux données confidentielles", severity: "critical" },
    { name: "IA: Prévention de partage non autorisé", description: "Mécanismes intelligents pour bloquer les tentatives de partage inapproprié", severity: "moderate" }
  ],
  'integrite': [
    { name: "IA: Vérification continue d'intégrité", description: "Contrôle permanent de l'état des données par IA", severity: "major" },
    { name: "IA: Protection contre les attaques d'injection", description: "Barrières intelligentes contre les tentatives d'altération", severity: "critical" },
    { name: "IA: Détection de modifications suspectes", description: "Algorithme d'apprentissage pour repérer les modifications anormales", severity: "major" }
  ],
  'disponibilite': [
    { name: "IA: Prédiction de charge et scaling", description: "Anticipation des pics d'utilisation pour maintenir la disponibilité", severity: "moderate" },
    { name: "IA: Détection précoce d'attaques DoS", description: "Identification de patterns d'attaques avant impact majeur", severity: "critical" },
    { name: "IA: Redondance intelligente", description: "Allocation dynamique des ressources basée sur la criticité", severity: "major" }
  ],
  'tracabilite': [
    { name: "IA: Corrélation avancée d'événements", description: "Analyse intelligente des logs pour reconstituer des séquences d'actions", severity: "major" },
    { name: "IA: Détection de suppression de logs", description: "Identification des tentatives d'effacement de traces", severity: "critical" },
    { name: "IA: Traçabilité prédictive", description: "Anticipation des actions nécessitant une traçabilité renforcée", severity: "moderate" }
  ],
  'preuve': [
    { name: "IA: Validation d'authenticité", description: "Vérification automatique de l'authenticité des preuves", severity: "major" },
    { name: "IA: Protection contre la falsification", description: "Mécanismes intelligents de détection d'altération des preuves", severity: "critical" },
    { name: "IA: Gestion proactive des durées de conservation", description: "Système d'alerte anticipée pour la gestion des preuves", severity: "moderate" }
  ],
  'auditabilite': [ // Using 'auditabilite' as key (without accent) but displaying 'Auditabilité' (with accent)
    { name: "IA: Automatisation des contrôles d'audit", description: "Vérification continue de la conformité aux exigences d'audit", severity: "major" },
    { name: "IA: Détection de résistance à l'audit", description: "Identification des tentatives d'obstruction aux procédures d'audit", severity: "critical" },
    { name: "IA: Amélioration continue des instruments d'audit", description: "Recommandations intelligentes pour optimiser les outils d'audit", severity: "moderate" }
  ]
};

export const roleColors = {
  'R': {
    bg: 'bg-green-100',
    text: 'text-green-800',
    border: 'border-green-200',
    pill: 'bg-green-500',
    label: 'Responsable'
  },
  'A': {
    bg: 'bg-blue-100',
    text: 'text-blue-800',
    border: 'border-blue-200',
    pill: 'bg-blue-500',
    label: 'Approbateur'
  },
  'C': {
    bg: 'bg-yellow-100',
    text: 'text-yellow-800',
    border: 'border-yellow-200',
    pill: 'bg-yellow-500',
    label: 'Consulté'
  },
  'I': {
    bg: 'bg-red-100',
    text: 'text-red-800',
    border: 'border-red-200',
    pill: 'bg-red-500',
    label: 'Informé'
  },
  '-': {
    bg: 'bg-gray-100',
    text: 'text-gray-800',
    border: 'border-gray-200',
    pill: 'bg-gray-400',
    label: 'Non participant'
  },
  '': {
    bg: 'bg-white',
    text: 'text-gray-300',
    border: 'border-gray-100',
    pill: 'bg-gray-200',
    label: 'Non défini'
  }
};

// Status options for frameworks and rules
export const SIMPLIFIED_STATUS_OPTIONS = [
  { value: 'applied', label: 'Appliqué' },
  { value: 'partially-applied', label: 'Partiellement appliqué' },
  { value: 'not-applied', label: 'Non appliqué' }
];

// Default security frameworks
export const DEFAULT_SECURITY_FRAMEWORKS = [
  {
    id: 'iso27001',
    name: 'ISO 27001',
    status: 'not-applied',
    justification: '',
    documentationAvailable: false
  },
  {
    id: 'nist',
    name: 'NIST Cybersecurity Framework',
    status: 'not-applied',
    justification: '',
    documentationAvailable: false
  }
];

// French security frameworks
export const FRENCH_FRAMEWORKS = [
  {
    id: 'anssi',
    name: 'Référentiel ANSSI',
    status: 'not-applied',
    justification: '',
    documentationAvailable: false
  },
  {
    id: 'cnil',
    name: 'Recommandations CNIL',
    status: 'not-applied',
    justification: '',
    documentationAvailable: false
  }
];

// Framework rules (mapping of framework ID to array of rules)
export const FRAMEWORK_RULES = {
  'iso27001': [
    { id: 'iso-1', name: 'A.5.1 Politique de sécurité de l\'information' },
    { id: 'iso-2', name: 'A.6.1 Organisation interne' },
    { id: 'iso-3', name: 'A.7.1 Avant l\'embauche' },
    { id: 'iso-4', name: 'A.8.1 Responsabilité relative aux actifs' }
  ],
  'nist': [
    { id: 'nist-1', name: 'ID.AM-1: Inventaire des périphériques physiques' },
    { id: 'nist-2', name: 'ID.AM-2: Inventaire des plateformes et applications logicielles' },
    { id: 'nist-3', name: 'PR.AC-1: Gestion des identités et identification' },
    { id: 'nist-4', name: 'PR.AC-4: Gestion des permissions d\'accès' }
  ],
  'anssi': [
    { id: 'anssi-1', name: 'R1 - Définir une politique de sécurité' },
    { id: 'anssi-2', name: 'R2 - Connaître le système d\'information' },
    { id: 'anssi-3', name: 'R3 - Authentifier l\'utilisateur' },
    { id: 'anssi-4', name: 'R4 - Gérer les habilitations' }
  ],
  'cnil': [
    { id: 'cnil-1', name: 'Sensibiliser les utilisateurs' },
    { id: 'cnil-2', name: 'Authentifier les utilisateurs' },
    { id: 'cnil-3', name: 'Gérer les habilitations' },
    { id: 'cnil-4', name: 'Tracer les accès et gérer les incidents' }
  ]
};

// Function to convert context participants and workshops to RACI matrix format
export const convertContextToRACIFormat = (participants, workshops) => {
  if (!participants || !workshops) {
    return [];
  }

  const matrix = [];

  participants.forEach(participant => {
    workshops.forEach(workshopItem => {
      matrix.push({
        participantId: participant.id,
        workshopId: workshopItem.id,
        value: ''
      });
    });
  });

  return matrix;
};

// Export a default object as a fallback
export default {
  WORKSHOPS,
  POSITION_OPTIONS,
  ROLES,
  SEVERITY_OPTIONS,
  FRAMEWORK_STATUS_OPTIONS,
  DREADED_EVENT_IMPACTS // Export the new constant
};

// Re-export translated constants utilities for convenience
export {
  getTranslatedSeverityOptions,
  getTranslatedDreadedEventImpacts,
  getTranslatedSecurityPillars,
  getTranslatedPillarLabels,
  getTranslatedSeverityLabel,
  getTranslatedImpactLabels
} from '../utils/translatedConstants';
