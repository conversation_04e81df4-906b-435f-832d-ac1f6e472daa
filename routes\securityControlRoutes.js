const express = require('express');
const {
  getAllSecurityControls,
  addSecurityControlsBulk,
} = require('../controllers/securityControlController');

// Include authentication middleware if needed
// const { protect, authorize } = require('../middleware/auth'); // Adjust path as necessary

const router = express.Router();

// Define routes
router
  .route('/')
  // Add protect middleware if only logged-in users can fetch controls
  // .get(protect, getAllSecurityControls); 
  .get(getAllSecurityControls); // Assuming public access for now, adjust if needed

router
  .route('/bulk')
  // Add protect and authorize middleware if only specific roles (e.g., admin) can add controls
  // .post(protect, authorize('admin'), addSecurityControlsBulk); 
  .post(addSecurityControlsBulk); // Assuming protected access needed, adjust middleware as required

module.exports = router;