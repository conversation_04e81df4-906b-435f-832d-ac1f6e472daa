// src/components/Atelier 2/Activite3/MultiSelect.js
import React, { useState, useRef, useEffect } from 'react';
import { ChevronDown, Check, X, Search, Shield, Info } from 'lucide-react';

const MultiSelect = ({
  options,
  selectedValues,
  onChange,
  placeholder = "Sélectionner...",
  maxHeight = 250,
  showCount = true,
  showSearch = true,
  groupBy = null,
  onViewDetails = null, // New prop for viewing details
  allData = [] // New prop for all data objects
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const dropdownRef = useRef(null);
  const searchInputRef = useRef(null);

  // State for dropdown position
  const [dropdownPosition, setDropdownPosition] = useState({ left: 0, width: '100%' });

  // Update dropdown position when it opens
  useEffect(() => {
    if (isOpen && dropdownRef.current) {
      const rect = dropdownRef.current.getBoundingClientRect();
      setDropdownPosition({
        left: rect.left,
        width: rect.width
      });
    }
  }, [isOpen]);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Focus search input when dropdown opens
  useEffect(() => {
    if (isOpen && showSearch && searchInputRef.current) {
      searchInputRef.current.focus();
    }
  }, [isOpen, showSearch]);

  // Filter options based on search term
  const filteredOptions = options.filter(option =>
    option.label.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Group options if groupBy is provided
  const groupedOptions = groupBy
    ? filteredOptions.reduce((acc, option) => {
        const groupKey = option[groupBy] || 'Autre';
        if (!acc[groupKey]) {
          acc[groupKey] = [];
        }
        acc[groupKey].push(option);
        return acc;
      }, {})
    : null;

  // Toggle selection of an option
  const toggleOption = (value) => {
    const newSelectedValues = selectedValues.includes(value)
      ? selectedValues.filter(v => v !== value)
      : [...selectedValues, value];

    onChange(newSelectedValues);
  };

  // Clear all selections
  const clearSelections = (e) => {
    e.stopPropagation();
    onChange([]);
  };

  // Get display text for the dropdown
  const getDisplayText = () => {
    if (selectedValues.length === 0) {
      return placeholder;
    }

    if (selectedValues.length === 1) {
      const selectedOption = options.find(option => option.value === selectedValues[0]);
      return selectedOption ? selectedOption.label : placeholder;
    }

    return `${selectedValues.length} sélectionnés`;
  };

  return (
    <div className="relative w-full" ref={dropdownRef}>
      {/* Dropdown trigger button */}
      <div
        className={`w-full p-3 border rounded-lg flex items-center justify-between cursor-pointer transition-all ${
          isOpen
            ? 'border-blue-400 ring-2 ring-blue-200 ring-opacity-50 bg-white'
            : 'border-gray-200 bg-white hover:border-gray-300'
        }`}
        onClick={() => setIsOpen(!isOpen)}
      >
        <div className="flex items-center truncate">
          {selectedValues.length > 0 && (
            <div className="bg-blue-100 text-blue-800 w-6 h-6 rounded-full flex items-center justify-center mr-2 text-xs font-medium">
              {selectedValues.length}
            </div>
          )}
          <span className={`truncate ${selectedValues.length === 0 ? 'text-gray-500' : 'text-gray-800'}`}>
            {getDisplayText()}
          </span>
        </div>
        <div className="flex items-center">
          {selectedValues.length > 0 && (
            <button
              className="p-1 hover:bg-gray-100 rounded-full mr-1 text-gray-500 hover:text-gray-700"
              onClick={clearSelections}
            >
              <X size={16} />
            </button>
          )}
          <ChevronDown size={18} className={`text-gray-500 transition-transform ${isOpen ? 'transform rotate-180' : ''}`} />
        </div>
      </div>

      {/* Dropdown menu */}
      {isOpen && (
        <div
          className="fixed z-50 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg overflow-hidden"
          style={{
            width: dropdownPosition.width,
            left: dropdownPosition.left,
            top: dropdownRef.current ? dropdownRef.current.getBoundingClientRect().bottom + window.scrollY : 0
          }}>
          {/* Search input */}
          {showSearch && (
            <div className="p-2 border-b border-gray-100">
              <div className="relative">
                <input
                  ref={searchInputRef}
                  type="text"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-8 pr-3 py-2 text-sm border border-gray-200 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-200 focus:border-blue-400"
                  placeholder="Rechercher..."
                />
                <Search size={16} className="absolute left-2.5 top-2.5 text-gray-400" />
                {searchTerm && (
                  <button
                    className="absolute right-2.5 top-2.5 text-gray-400 hover:text-gray-600"
                    onClick={() => setSearchTerm('')}
                  >
                    <X size={14} />
                  </button>
                )}
              </div>
            </div>
          )}

          {/* Options list */}
          <div className="overflow-y-auto" style={{ maxHeight: `${maxHeight}px` }}>
            {filteredOptions.length === 0 ? (
              <div className="p-4 text-center text-gray-500 text-sm">
                Aucun résultat trouvé
              </div>
            ) : groupBy ? (
              // Grouped options
              Object.entries(groupedOptions).map(([group, groupOptions]) => (
                <div key={group} className="border-b border-gray-100 last:border-b-0">
                  <div className="px-3 py-2 bg-gray-50 text-xs font-medium text-gray-500 uppercase tracking-wide">
                    {group}
                  </div>
                  {groupOptions.map(option => (
                    <div
                      key={option.value}
                      className={`px-3 py-2 flex items-center hover:bg-gray-50 ${
                        selectedValues.includes(option.value) ? 'bg-blue-50' : ''
                      }`}
                    >
                      <div
                        className="flex-1 flex items-center cursor-pointer"
                        onClick={() => toggleOption(option.value)}
                      >
                        <div className={`w-5 h-5 rounded border flex items-center justify-center mr-3 ${
                          selectedValues.includes(option.value)
                            ? 'bg-blue-600 border-blue-600'
                            : 'border-gray-300'
                        }`}>
                          {selectedValues.includes(option.value) && (
                            <Check size={14} className="text-white" />
                          )}
                        </div>
                        <div className="flex-1 truncate">{option.label}</div>
                        {option.badge && (
                          <span className={`ml-2 px-2 py-0.5 rounded-full text-xs ${option.badgeClass || 'bg-gray-100 text-gray-800'}`}>
                            {option.badge}
                          </span>
                        )}
                      </div>

                      {/* Details button */}
                      {onViewDetails && (
                        <button
                          className="ml-2 p-1 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-full"
                          onClick={(e) => {
                            e.stopPropagation();
                            const fullData = allData.find(item => item.id === option.value);
                            if (fullData) {
                              onViewDetails(fullData);
                            }
                          }}
                          title="Voir les détails"
                        >
                          <Info size={14} />
                        </button>
                      )}
                    </div>
                  ))}
                </div>
              ))
            ) : (
              // Flat options list
              filteredOptions.map(option => (
                <div
                  key={option.value}
                  className={`px-3 py-2 flex items-center hover:bg-gray-50 ${
                    selectedValues.includes(option.value) ? 'bg-blue-50' : ''
                  }`}
                >
                  <div
                    className="flex-1 flex items-center cursor-pointer"
                    onClick={() => toggleOption(option.value)}
                  >
                    <div className={`w-5 h-5 rounded border flex items-center justify-center mr-3 ${
                      selectedValues.includes(option.value)
                        ? 'bg-blue-600 border-blue-600'
                        : 'border-gray-300'
                    }`}>
                      {selectedValues.includes(option.value) && (
                        <Check size={14} className="text-white" />
                      )}
                    </div>
                    <div className="flex-1 truncate">{option.label}</div>
                    {option.badge && (
                      <span className={`ml-2 px-2 py-0.5 rounded-full text-xs ${option.badgeClass || 'bg-gray-100 text-gray-800'}`}>
                        {option.badge}
                      </span>
                    )}
                  </div>

                  {/* Details button */}
                  {onViewDetails && (
                    <button
                      className="ml-2 p-1 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-full"
                      onClick={(e) => {
                        e.stopPropagation();
                        const fullData = allData.find(item => item.id === option.value);
                        if (fullData) {
                          onViewDetails(fullData);
                        }
                      }}
                      title="Voir les détails"
                    >
                      <Info size={14} />
                    </button>
                  )}
                </div>
              ))
            )}
          </div>

          {/* Footer with selection count */}
          {showCount && selectedValues.length > 0 && (
            <div className="p-2 border-t border-gray-100 bg-gray-50 text-xs text-gray-600 flex justify-between items-center">
              <span>{selectedValues.length} sélectionné(s)</span>
              <button
                className="text-blue-600 hover:text-blue-800 font-medium"
                onClick={clearSelections}
              >
                Tout effacer
              </button>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default MultiSelect;
