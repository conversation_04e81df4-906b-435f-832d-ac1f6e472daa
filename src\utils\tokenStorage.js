/**
 * Utilitaires pour gérer les tokens JWT dans le localStorage
 */

// Clés pour stocker les tokens dans localStorage
const ACCESS_TOKEN_KEY = 'ebiosrm_access_token';
const REFRESH_TOKEN_KEY = 'ebiosrm_refresh_token';
const USER_INFO_KEY = 'ebiosrm_user';

/**
 * Enregistre les tokens et informations utilisateur dans le localStorage
 * @param {Object} data - Données reçues de l'API après connexion
 */
export const saveAuthData = (data) => {
  // Removed sensitive data logging for security
  if (data.tokens?.accessToken) {
    localStorage.setItem(ACCESS_TOKEN_KEY, data.tokens.accessToken);
  }

  if (data.tokens?.refreshToken) {
    localStorage.setItem(REFRESH_TOKEN_KEY, data.tokens.refreshToken);
  }

  if (data.user) {
    localStorage.setItem(USER_INFO_KEY, JSON.stringify(data.user));
  }
};

/**
 * <PERSON><PERSON><PERSON><PERSON> le token d'accès depuis le localStorage
 * @returns {string|null} - Le token d'accès ou null s'il n'existe pas
 */
export const getAccessToken = () => {
  try {
    const token = localStorage.getItem(ACCESS_TOKEN_KEY);
    if (!token) return null;

    // Ensure the token is a valid string
    if (typeof token !== 'string' || token.length < 10) {
      // Log without revealing token details
      console.error('Invalid token format found in storage');
      clearAuthData(); // Clear invalid token
      return null;
    }

    return token;
  } catch (error) {
    // Log without revealing sensitive details
    console.error('Error retrieving access token');
    return null;
  }
};

/**
 * Récupère le token de rafraîchissement depuis le localStorage
 * @returns {string|null} - Le token de rafraîchissement ou null s'il n'existe pas
 */
export const getRefreshToken = () => {
  return localStorage.getItem(REFRESH_TOKEN_KEY);
};

/**
 * Récupère les informations utilisateur depuis le localStorage
 * @returns {Object|null} - L'objet utilisateur ou null s'il n'existe pas
 */
export const getUserInfo = () => {
  const userInfo = localStorage.getItem(USER_INFO_KEY);

  if (!userInfo) {
    return null;
  }

  try {
    return JSON.parse(userInfo);
  } catch (error) {
    // Log without revealing user details
    console.error('Error parsing user info - clearing auth data');
    clearAuthData();
    return null;
  }
};

/**
 * Met à jour le token d'accès dans le localStorage
 * @param {string} accessToken - Nouveau token d'accès
 */
export const updateAccessToken = (accessToken) => {
  localStorage.setItem(ACCESS_TOKEN_KEY, accessToken);
};

/**
 * Supprime toutes les données d'authentification du localStorage
 */
export const clearAuthData = () => {
  localStorage.removeItem(ACCESS_TOKEN_KEY);
  localStorage.removeItem(REFRESH_TOKEN_KEY);
  localStorage.removeItem(USER_INFO_KEY);
};

/**
 * Décode un token JWT et retourne sa charge utile
 * @param {string} token - Token JWT à décoder
 * @returns {Object|null} - Charge utile du token ou null si invalide
 */
export const decodeToken = (token) => {
  if (!token) {
    return null;
  }

  try {
    const parts = token.split('.');
    if (parts.length !== 3) {
      return null;
    }

    const payload = JSON.parse(atob(parts[1]));
    return payload;
  } catch (error) {
    console.error('Error decoding token'); // Removed error object from log
    return null;
  }
};

/**
 * Vérifie si un token est expiré
 * @param {Object} payload - Charge utile du token décodé
 * @returns {boolean} - True si le token est expiré
 */
export const isTokenExpired = (payload) => {
  if (!payload || !payload.exp) {
    return true;
  }

  const now = Math.floor(Date.now() / 1000);
  return payload.exp <= now;
};

/**
 * Vérifie si l'utilisateur est authentifié et son token est valide
 * @returns {boolean} - True si l'utilisateur est authentifié avec un token valide
 */
export const isAuthenticated = () => {
  const token = getAccessToken();
  if (!token) {
    return false;
  }

  const payload = decodeToken(token);
  if (!payload) {
    return false;
  }

  // Vérifier si le token est expiré
  return !isTokenExpired(payload);
};

/**
 * Vérifie si l'utilisateur a un rôle spécifique
 * @param {string|Array} roles - Rôle(s) à vérifier
 * @returns {boolean} - True si l'utilisateur a l'un des rôles spécifiés
 */
export const hasRole = (roles) => {
  const user = getUserInfo();

  if (!user || !user.role) {
    return false;
  }

  if (Array.isArray(roles)) {
    return roles.includes(user.role);
  }

  return user.role === roles;
};