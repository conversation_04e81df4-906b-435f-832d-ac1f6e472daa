// test-auth-fix.js
// Test script to verify that CTI routes are accessible without authentication

const axios = require('axios');

const BASE_URL = 'http://localhost:5000';

async function testAuthenticationFix() {
  console.log('🧪 Testing CTI Authentication Fix...\n');

  // Test routes that should be PUBLIC (no authentication required)
  const publicRoutes = [
    { name: 'NIST Test', url: `${BASE_URL}/api/nist/test` },
    { name: 'NIST Search', url: `${BASE_URL}/api/nist/search?keywordSearch=apache&resultsPerPage=5` },
    { name: 'EUVD Test', url: `${BASE_URL}/api/euvd/test` },
    { name: 'MITRE Test', url: `${BASE_URL}/api/mitre/test` },
    { name: 'ATLAS Test', url: `${BASE_URL}/api/atlas/test` },
    { name: 'CTI Load Results', url: `${BASE_URL}/api/cti/load-results` }
  ];

  // Test routes that should be PROTECTED (authentication required)
  const protectedRoutes = [
    { name: 'Analyses List', url: `${BASE_URL}/api/analyses` },
    { name: 'Users List', url: `${BASE_URL}/api/users` },
    { name: 'Companies List', url: `${BASE_URL}/api/companies` }
  ];

  console.log('🔓 Testing PUBLIC routes (should work without authentication)...\n');

  for (const route of publicRoutes) {
    try {
      console.log(`Testing ${route.name}...`);
      
      const response = await axios.get(route.url, {
        timeout: 10000,
        validateStatus: function (status) {
          // Accept 200, 404 (endpoint not found), but not 401 (unauthorized)
          return status !== 401;
        }
      });

      if (response.status === 200) {
        console.log(`✅ ${route.name}: SUCCESS - No authentication required`);
        if (response.data.message) {
          console.log(`   Response: ${response.data.message}`);
        }
        if (response.data.totalResults !== undefined) {
          console.log(`   Results: ${response.data.totalResults} found`);
        }
      } else if (response.status === 404) {
        console.log(`⚠️ ${route.name}: Route accessible but endpoint not found (expected for some test endpoints)`);
      } else {
        console.log(`⚠️ ${route.name}: Accessible but returned status ${response.status}`);
      }

    } catch (error) {
      if (error.response && error.response.status === 401) {
        console.log(`❌ ${route.name}: FAILED - Still requires authentication (401 Unauthorized)`);
        console.log(`   Error: ${error.response.data?.message || 'Unauthorized'}`);
      } else if (error.code === 'ECONNREFUSED') {
        console.log(`❌ ${route.name}: Connection refused - server not running`);
        break; // No point testing other routes if server is down
      } else {
        console.log(`❌ ${route.name}: ${error.message}`);
      }
    }

    // Add small delay between requests
    await new Promise(resolve => setTimeout(resolve, 500));
  }

  console.log('\n🔒 Testing PROTECTED routes (should require authentication)...\n');

  for (const route of protectedRoutes) {
    try {
      console.log(`Testing ${route.name}...`);
      
      const response = await axios.get(route.url, {
        timeout: 5000,
        validateStatus: function (status) {
          return status < 500; // Accept any status under 500
        }
      });

      if (response.status === 401) {
        console.log(`✅ ${route.name}: CORRECTLY PROTECTED - Requires authentication`);
      } else if (response.status === 200) {
        console.log(`⚠️ ${route.name}: WARNING - Route is accessible without authentication`);
      } else {
        console.log(`⚠️ ${route.name}: Returned status ${response.status}`);
      }

    } catch (error) {
      if (error.response && error.response.status === 401) {
        console.log(`✅ ${route.name}: CORRECTLY PROTECTED - Requires authentication`);
      } else if (error.code === 'ECONNREFUSED') {
        console.log(`❌ ${route.name}: Connection refused - server not running`);
        break;
      } else {
        console.log(`❌ ${route.name}: ${error.message}`);
      }
    }

    // Add small delay between requests
    await new Promise(resolve => setTimeout(resolve, 500));
  }

  console.log('\n🏁 Authentication fix testing completed!');
  console.log('\n📋 Summary:');
  console.log('   ✅ PUBLIC routes should work without authentication');
  console.log('   🔒 PROTECTED routes should return 401 Unauthorized');
  console.log('   🔄 If issues persist, restart the backend server');
}

// Run the test
if (require.main === module) {
  testAuthenticationFix().catch(console.error);
}

module.exports = { testAuthenticationFix };
