import React from 'react';
import { Target, ExternalLink } from 'lucide-react';

const TechniqueItem = ({ technique, source, mitreUrl, techniqueId, techniqueName, tactic }) => {
    return (
        <div className="bg-white border border-gray-200 rounded-lg p-3 hover:shadow-md transition-all duration-200 hover:border-gray-300">
            {/* Compact One-Line Layout */}
            <div className="flex items-center justify-between">
                {/* Left: Technique Info */}
                <div className="flex items-center space-x-3 flex-1 min-w-0">
                    <div className={`p-1.5 rounded ${
                        source === 'MITRE' ? 'bg-red-50' :
                        source === 'ATLAS' ? 'bg-purple-50' :
                        'bg-gray-50'
                    }`}>
                        <Target className={`h-3 w-3 ${
                            source === 'MITRE' ? 'text-red-600' :
                            source === 'ATLAS' ? 'text-purple-600' :
                            'text-gray-600'
                        }`} />
                    </div>
                    <a
                        href={mitreUrl}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="font-mono text-sm font-semibold text-gray-900 hover:text-red-600 transition-colors flex items-center space-x-1"
                    >
                        <span>{techniqueId}</span>
                        <ExternalLink className="h-3 w-3" />
                    </a>
                    <div className="text-sm text-gray-800 truncate flex-1 font-medium" title={techniqueName}>
                        {techniqueName}
                    </div>
                </div>

                {/* Right: Badges */}
                <div className="flex items-center space-x-2 flex-shrink-0">
                    <span className={`inline-flex px-2 py-1 text-xs font-medium rounded ${
                        source === 'MITRE' ? 'bg-orange-100 text-orange-800' :
                        source === 'ATLAS' ? 'bg-indigo-100 text-indigo-800' :
                        'bg-gray-100 text-gray-800'
                    }`}>
                        {tactic}
                    </span>
                    <span className={`inline-flex px-2 py-1 text-xs font-medium rounded ${
                        source === 'MITRE' ? 'bg-red-100 text-red-800' :
                        source === 'ATLAS' ? 'bg-purple-100 text-purple-800' :
                        'bg-gray-100 text-gray-800'
                    }`}>
                        {source}
                    </span>
                </div>
            </div>
        </div>
    );
};

export default TechniqueItem;
