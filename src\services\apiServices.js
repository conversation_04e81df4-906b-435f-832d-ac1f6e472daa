// src/services/apiServices.js
import api from '../api/apiClient';

/**
 * Authentication services
 */
export const authService = {
  // Login user
  login: async (email, password) => {
    try {
      return await api.post('/auth/login', { email, password });
    } catch (error) {
      console.error('Login API error:', error);
      throw error;
    }
  },

  // Refresh token - DISABLED
  refreshToken: async (refreshToken) => {
    console.log('Token refresh has been disabled. Please log in again.');
    throw new Error('Token refresh has been disabled');
  },

  // Logout user
  logout: async () => {
    try {
      return await api.post('/auth/logout');
    } catch (error) {
      console.error('Logout API error:', error);
      // Even if the API call fails, we still want to clear local storage
      return { success: true };
    }
  },

  // Get current user info
  getCurrentUser: async (silentAuth = false) => {
    try {
      // Pass silentAuth option to prevent page reload on token expiry
      return await api.get('/auth/me', {}, { silentAuth });
    } catch (error) {
      console.error('Get current user API error:', error);
      throw error;
    }
  }
};

/**
 * User management services
 */
export const userService = {
  // Get all users (with filters)
  getUsers: async (filters = {}) => {
    try {
      return await api.get('/users', filters);
    } catch (error) {
      console.error('Get users API error:', error);
      throw error;
    }
  },

  // Get user by ID
  getUserById: async (id) => {
    try {
      return await api.get(`/users/${id}`);
    } catch (error) {
      console.error('Get user API error:', error);
      throw error;
    }
  },

  // Create new user
  createUser: async (userData) => {
    try {
      return await api.post('/users', userData);
    } catch (error) {
      console.error('Create user API error:', error);
      throw error;
    }
  },

  // Update user
  updateUser: async (id, userData) => {
    try {
      return await api.put(`/users/${id}`, userData);
    } catch (error) {
      console.error('Update user API error:', error);
      throw error;
    }
  },

  // Update user password
  updatePassword: async (id, { currentPassword, newPassword }) => {
    try {
      return await api.put(`/users/${id}/password`, { currentPassword, newPassword });
    } catch (error) {
      console.error('Update password API error:', error);
      throw error;
    }
  },

  // Delete user
  deleteUser: async (id) => {
    try {
      return await api.delete(`/users/${id}`);
    } catch (error) {
      console.error('Delete user API error:', error);
      throw error;
    }
  },

  // Get user logs
  getUserLogs: async (userId, filters = {}) => {
    try {
      return await api.get(`/users/${userId}/logs`, filters);
    } catch (error) {
      console.error('Get user logs API error:', error);
      throw error;
    }
  }
};

/**
 * Company management services
 */
export const companyService = {
  // Get all companies (with filters)
  getCompanies: async (filters = {}) => {
    try {
      return await api.get('/companies', filters);
    } catch (error) {
      console.error('Get companies API error:', error);
      throw error;
    }
  },

  // Get company by ID
  getCompanyById: async (id) => {
    try {
      return await api.get(`/companies/${id}`);
    } catch (error) {
      console.error('Get company API error:', error);
      throw error;
    }
  },

  // Create new company
  createCompany: async (companyData) => {
    try {
      return await api.post('/companies', companyData);
    } catch (error) {
      console.error('Create company API error:', error);
      throw error;
    }
  },

  // Update company
  updateCompany: async (id, companyData) => {
    try {
      return await api.put(`/companies/${id}`, companyData);
    } catch (error) {
      console.error('Update company API error:', error);
      throw error;
    }
  },

  // Delete company
  deleteCompany: async (id) => {
    try {
      return await api.delete(`/companies/${id}`);
    } catch (error) {
      console.error('Delete company API error:', error);
      throw error;
    }
  },

  // Get company users
  getCompanyUsers: async (companyId) => {
    try {
      return await api.get(`/companies/${companyId}/users`);
    } catch (error) {
      console.error('Get company users API error:', error);
      throw error;
    }
  },

  // Get company logs
  getCompanyLogs: async (companyId, filters = {}) => {
    try {
      return await api.get(`/companies/${companyId}/logs`, filters);
    } catch (error) {
      console.error('Get company logs API error:', error);
      throw error;
    }
  },

  // Get company metrics
  getCompanyMetrics: async () => {
    try {
      return await api.get('/companies/metrics');
    } catch (error) {
      console.error('Get company metrics API error:', error);

      // Fallback to mock data for development if API isn't ready
      if (process.env.NODE_ENV === 'development') {
        console.warn('Using mock metrics data for development');
        return {
          success: true,
          data: {
            totalCompanies: 8,
            totalUsers: 45,
            activeAnalyses: 23,
            usersByRole: {
              admin: 12,
              analyst: 25,
              viewer: 8
            },
            companyStatuses: {
              active: 7,
              inactive: 1
            }
          }
        };
      }

      throw error;
    }
  }
};

/**
 * Activity logs services
 */
export const logService = {
  // Get all activity logs (with filters)
  getLogs: async (filters = {}) => {
    try {
      return await api.get('/logs', filters);
    } catch (error) {
      console.error('Get logs API error:', error);

      // In development mode, return mock data if API fails
      if (process.env.NODE_ENV === 'development') {
        console.warn('Using mock logs data for development');

        const mockLogs = [
          { id: '1', timestamp: new Date(Date.now() - 30 * 60000).toISOString(), userId: '<EMAIL>', userName: 'John Doe', companyName: 'Entreprise A', actionType: 'USER_LOGIN', resourceType: 'user', ipAddress: '*************' },
          { id: '2', timestamp: new Date(Date.now() - 120 * 60000).toISOString(), userId: '<EMAIL>', userName: 'Jane Smith', companyName: 'Entreprise B', actionType: 'ANALYSIS_CREATE', resourceType: 'analysis', ipAddress: '*************' },
          { id: '3', timestamp: new Date(Date.now() - 240 * 60000).toISOString(), userId: '<EMAIL>', userName: 'Admin User', companyName: null, actionType: 'COMPANY_CREATE', resourceType: 'company', ipAddress: '*************' },
          { id: '4', timestamp: new Date(Date.now() - 480 * 60000).toISOString(), userId: '<EMAIL>', userName: 'Bob Johnson', companyName: 'Entreprise C', actionType: 'USER_UPDATE', resourceType: 'user', ipAddress: '*************' },
          { id: '5', timestamp: new Date(Date.now() - 1440 * 60000).toISOString(), userId: '<EMAIL>', userName: 'Jane Smith', companyName: 'Entreprise B', actionType: 'ANALYSIS_UPDATE', resourceType: 'analysis', ipAddress: '*************' }
        ];

        // Apply any filtering
        let filteredLogs = [...mockLogs];

        // Apply limit
        if (filters.limit) {
          filteredLogs = filteredLogs.slice(0, parseInt(filters.limit));
        }

        return {
          success: true,
          data: filteredLogs
        };
      }

      throw error;
    }
  },

  // Get activity stats
  getActivityStats: async () => {
    try {
      return await api.get('/logs/stats');
    } catch (error) {
      console.error('Get activity stats API error:', error);

      // In development mode, return mock data if API fails
      if (process.env.NODE_ENV === 'development') {
        console.warn('Using mock activity stats for development');
        return {
          success: true,
          data: {
            total: 125,
            byType: {
              USER_LOGIN: 45,
              ANALYSIS_CREATE: 20,
              ANALYSIS_UPDATE: 30,
              USER_CREATE: 15,
              USER_UPDATE: 10,
              COMPANY_CREATE: 5
            },
            byCompany: {
              'Entreprise A': 40,
              'Entreprise B': 35,
              'Entreprise C': 30,
              'Entreprise D': 20
            }
          }
        };
      }

      throw error;
    }
  }
};

/**
 * Dashboard services - for dedicated dashboard endpoints
 */
export const dashboardService = {
  /**
   * Get dashboard metrics for superadmin
   * @returns {Promise<Object>} Dashboard metrics data
   */
  getSuperAdminMetrics: async () => {
    try {
      // Try to get metrics from the API
      return await api.get('/dashboard/metrics/superadmin');
    } catch (error) {
      console.error('Get superadmin metrics API error:', error);

      // If in development, return mock data
      if (process.env.NODE_ENV === 'development') {
        console.warn('Using mock superadmin metrics data for development');
        return {
          success: true,
          data: {
            totalCompanies: 8,
            totalUsers: 45,
            activeAnalyses: 23,
            usersByRole: {
              admin: 12,
              analyst: 25,
              viewer: 8
            },
            companyStatuses: {
              active: 7,
              inactive: 1
            },
            recentActivities: [
              { id: '1', actionType: 'USER_LOGIN', userId: '<EMAIL>', userName: 'Admin User', timestamp: new Date(Date.now() - 30 * 60000).toISOString() },
              { id: '2', actionType: 'COMPANY_CREATE', userId: '<EMAIL>', userName: 'Admin User', companyName: 'Nouvelle Entreprise', timestamp: new Date(Date.now() - 120 * 60000).toISOString() },
              { id: '3', actionType: 'USER_CREATE', userId: '<EMAIL>', userName: 'Admin User', companyName: 'Entreprise A', timestamp: new Date(Date.now() - 240 * 60000).toISOString() },
              { id: '4', actionType: 'ANALYSIS_UPDATE', userId: '<EMAIL>', userName: 'John Doe', companyName: 'Entreprise B', timestamp: new Date(Date.now() - 480 * 60000).toISOString() },
              { id: '5', actionType: 'USER_UPDATE', userId: '<EMAIL>', userName: 'Admin User', companyName: 'Entreprise C', timestamp: new Date(Date.now() - 1440 * 60000).toISOString() }
            ]
          }
        };
      }

      throw error;
    }
  },

  /**
   * Get dashboard metrics for company provider
   * @param {string} companyId - Company ID (optional, uses current user's company if not provided)
   * @returns {Promise<Object>} Dashboard metrics data for the company
   */
  getProviderMetrics: async (companyId = null) => {
    try {
      const endpoint = companyId
        ? `/dashboard/metrics/provider/${companyId}`
        : '/dashboard/metrics/provider';

      return await api.get(endpoint);
    } catch (error) {
      console.error('Get provider metrics API error:', error);

      // If in development, return mock data
      if (process.env.NODE_ENV === 'development') {
        console.warn('Using mock provider metrics data for development');
        return {
          success: true,
          data: {
            totalUsers: 12,
            activeAnalyses: 8,
            usersByRole: {
              analyst: 10,
              viewer: 2
            },
            recentActivities: [
              { id: '1', userName: 'John Doe', actionType: 'USER_LOGIN', timestamp: new Date(Date.now() - 30 * 60000).toISOString() },
              { id: '2', userName: 'Jane Smith', actionType: 'ANALYSIS_CREATE', timestamp: new Date(Date.now() - 120 * 60000).toISOString() },
              { id: '3', userName: 'John Doe', actionType: 'ANALYSIS_UPDATE', timestamp: new Date(Date.now() - 240 * 60000).toISOString() }
            ]
          }
        };
      }

      throw error;
    }
  },

  /**
   * Get activity summary by time period (day, week, month)
   * @param {Object} params - Filter parameters
   * @param {string} params.period - Time period ('day', 'week', 'month')
   * @param {string} params.companyId - Company ID (optional)
   * @returns {Promise<Object>} Activity summary data
   */
  getActivitySummary: async (params = {}) => {
    try {
      return await api.get('/dashboard/activity-summary', params);
    } catch (error) {
      console.error('Get activity summary API error:', error);

      // If in development, return mock data
      if (process.env.NODE_ENV === 'development') {
        console.warn('Using mock activity summary data for development');

        // Generate mock data based on the period
        let mockData;

        switch (params.period) {
          case 'day':
            mockData = [
              { name: '00h', login: 5, create: 1, update: 2 },
              { name: '04h', login: 2, create: 0, update: 1 },
              { name: '08h', login: 15, create: 4, update: 8 },
              { name: '12h', login: 22, create: 7, update: 12 },
              { name: '16h', login: 18, create: 6, update: 10 },
              { name: '20h', login: 8, create: 2, update: 4 }
            ];
            break;
          case 'month':
            mockData = [
              { name: 'Jan', login: 150, create: 40, update: 85 },
              { name: 'Fév', login: 180, create: 45, update: 95 },
              { name: 'Mar', login: 220, create: 55, update: 110 },
              { name: 'Avr', login: 240, create: 60, update: 120 },
              { name: 'Mai', login: 210, create: 50, update: 105 },
              { name: 'Jui', login: 190, create: 45, update: 95 },
              { name: 'Juil', login: 170, create: 42, update: 85 },
              { name: 'Aoû', login: 160, create: 40, update: 80 },
              { name: 'Sep', login: 200, create: 50, update: 100 },
              { name: 'Oct', login: 230, create: 58, update: 115 },
              { name: 'Nov', login: 210, create: 52, update: 105 },
              { name: 'Déc', login: 180, create: 45, update: 90 }
            ];
            break;
          case 'week':
          default:
            mockData = [
              { name: 'Lun', login: 20, create: 5, update: 8 },
              { name: 'Mar', login: 15, create: 3, update: 10 },
              { name: 'Mer', login: 22, create: 7, update: 12 },
              { name: 'Jeu', login: 19, create: 4, update: 9 },
              { name: 'Ven', login: 25, create: 6, update: 15 },
              { name: 'Sam', login: 10, create: 2, update: 5 },
              { name: 'Dim', login: 8, create: 1, update: 3 }
            ];
        }

        return {
          success: true,
          data: mockData
        };
      }

      throw error;
    }
  }
};

/**
 * Security Controls services
 */
export const securityControlService = {
  /**
   * Add multiple security controls in bulk
   * @param {Array<Object>} controlsData - Array of control objects { measure_description, pillar, risk_treatment_strategy }
   * @returns {Promise<Object>} API response
   */
  addSecurityControlsBulk: async (controlsData) => {
    try {
      // NOTE: Endpoint '/security-controls/bulk' needs to be implemented in the backend
      return await api.post('/security-controls/bulk', { controls: controlsData });
    } catch (error) {
      console.error('Add security controls bulk API error:', error);
      throw error;
    }
  },

  /**
   * Get all security controls
   * @param {Object} filters - Optional filters
   * @returns {Promise<Object>} API response containing the list of controls
   */
  getSecurityControls: async (filters = {}) => {
    try {
      // NOTE: Endpoint '/security-controls' needs to be implemented in the backend
      return await api.get('/security-controls', filters);
    } catch (error) {
      console.error('Get security controls API error:', error);
      // Provide mock data in development if API fails
      if (process.env.NODE_ENV === 'development') {
        console.warn('API failed, returning mock security controls for development');
        return {
          success: true,
          data: [
            { id: 'mock1', measure_description: "Mock: Encrypt sensitive data at rest.", pillar: "Confidentialité", risk_treatment_strategy: "Réduire le risque" },
            { id: 'mock2', measure_description: "Mock: Implement RBAC.", pillar: "Confidentialité", risk_treatment_strategy: "Réduire le risque" },
            { id: 'mock3', measure_description: "Mock: Use FIM.", pillar: "Intégrité", risk_treatment_strategy: "Réduire le risque" },
            { id: 'mock4', measure_description: "Mock: Maintain DR plan.", pillar: "Disponibilité", risk_treatment_strategy: "Réduire le risque" },
          ]
        };
      }
      throw error;
    }
  }
};