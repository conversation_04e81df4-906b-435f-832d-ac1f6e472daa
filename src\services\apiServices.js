export const companyService = {
  // Get all companies (with filters)
  getCompanies: async (filters = {}) => {
    try {
      const queryParams = new URLSearchParams();
      if (filters.status) queryParams.append('status', filters.status);
      if (filters.search) queryParams.append('search', filters.search);
      if (filters.page) queryParams.append('page', filters.page);
      if (filters.limit) queryParams.append('limit', filters.limit);

      const response = await api.get(`/companies?${queryParams.toString()}`);
      return response.data;
    } catch (error) {
      console.error('Get companies API error:', error);
      throw error;
    }
  },
  
  // Get company by ID
  getCompanyById: async (id) => {
    try {
      const response = await api.get(`/companies/${id}`);
      return response.data;
    } catch (error) {
      console.error('Get company API error:', error);
      throw error;
    }
  },
  
  // Create new company
  createCompany: async (companyData) => {
    try {
      const response = await api.post('/companies', companyData);
      return response.data;
    } catch (error) {
      console.error('Create company API error:', error);
      throw error;
    }
  },
  
  // Update company
  updateCompany: async (id, companyData) => {
    try {
      const response = await api.put(`/companies/${id}`, companyData);
      return response.data;
    } catch (error) {
      console.error('Update company API error:', error);
      throw error;
    }
  },
  
  // Delete company
  deleteCompany: async (id) => {
    try {
      const response = await api.delete(`/companies/${id}`);
      return response.data;
    } catch (error) {
      console.error('Delete company API error:', error);
      throw error;
    }
  },
  
  // Get company users
  getCompanyUsers: async (companyId) => {
    try {
      const response = await api.get(`/companies/${companyId}/users`);
      return response.data;
    } catch (error) {
      console.error('Get company users API error:', error);
      throw error;
    }
  },
  
  // Get company logs
  getCompanyLogs: async (companyId, filters = {}) => {
    try {
      const queryParams = new URLSearchParams();
      if (filters.page) queryParams.append('page', filters.page);
      if (filters.limit) queryParams.append('limit', filters.limit);
      if (filters.startDate) queryParams.append('startDate', filters.startDate);
      if (filters.endDate) queryParams.append('endDate', filters.endDate);

      const response = await api.get(`/companies/${companyId}/logs?${queryParams.toString()}`);
      return response.data;
    } catch (error) {
      console.error('Get company logs API error:', error);
      throw error;
    }
  },
  
  // Get company metrics
  getCompanyMetrics: async () => {
    try {
      const response = await api.get('/companies/metrics');
      return response.data;
    } catch (error) {
      console.error('Get company metrics API error:', error);
      throw error;
    }
  }
}; 