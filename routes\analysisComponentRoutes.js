// routes/analysisComponentRoutes.js
const express = require('express');
const {
  getComponent,
  saveComponent,
  getAllComponents,
  getComponentHistory,
  getComponentVersion,
  getContext,
  saveContext,
  getBusinessValues,
  saveBusinessValues,
  getDreadedEvents,
  saveDreadedEvents,
  getSecurityFramework,
  saveSecurityFramework,
  getSecurityControls,
  saveSecurityControls,
  getRiskTreatment,
  saveRiskTreatment,
  getSources,
  saveSources,
  getObjectives,
  saveObjectives,
  getCTIResults,
  saveCTIResults,
  deleteCTIResults
} = require('../controllers/analysisComponentController');

const { protect } = require('../middleware/authMiddleware');

const router = express.Router();

// All routes need authentication
router.use(protect);

// General component routes
router.route('/analyses/:analysisId/components')
  .get(protect, getAllComponents);

router.route('/analyses/:analysisId/components/:componentType')
  .get(protect, getComponent)
  .post(protect, saveComponent);

router.route('/analyses/:analysisId/components/:componentType/history')
  .get(protect, getComponentHistory);

router.route('/analyses/:analysisId/components/:componentType/versions/:version')
  .get(protect, getComponentVersion);

// Specific component routes for better semantics
// Context routes
router.route('/analyses/:analysisId/context')
  .get(protect, getContext)
  .post(protect, saveContext);

// Business values routes
router.route('/analyses/:analysisId/business-values')
  .get(protect, getBusinessValues)
  .post(protect, saveBusinessValues)
  .put(protect, saveBusinessValues);


// Dreaded events routes
router.route('/analyses/:analysisId/dreaded-events')
  .get(protect, getDreadedEvents)
  .post(protect, saveDreadedEvents);

// Security framework routes
router.route('/analyses/:analysisId/security-framework')
  .get(protect, getSecurityFramework)
  .post(protect, saveSecurityFramework)
  .put(protect, saveSecurityFramework);  // Add PUT route


// Security controls routes
router.route('/analyses/:analysisId/security-controls')
  .get(protect, getSecurityControls)
  .post(protect, saveSecurityControls);

// Risk treatment routes
router.route('/analyses/:analysisId/risk-treatment')
  .get(protect, getRiskTreatment)
  .post(protect, saveRiskTreatment);

// Sources routes (for workshop 2)
router.route('/analyses/:analysisId/sources')
  .get(protect, getSources)
  .post(protect, saveSources);

// Objectives routes (for workshop 2)
router.route('/analyses/:analysisId/objectives')
  .get(protect, getObjectives)
  .post(protect, saveObjectives);

// CTI Results routes
router.route('/analyses/:analysisId/cti-results')
  .get(protect, getCTIResults)
  .post(protect, saveCTIResults)
  .put(protect, saveCTIResults)
  .delete(protect, deleteCTIResults);

module.exports = router;