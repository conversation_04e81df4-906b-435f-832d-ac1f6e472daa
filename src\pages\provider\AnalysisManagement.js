// src/pages/provider/AnalysisManagement.jsx
import React, { useState } from 'react';

const AnalysisManagement = () => {
  // Données simulées - à remplacer par des appels API réels plus tard
  const [analyses, setAnalyses] = useState([
    { id: '1', name: 'Analyse de risques - Projet A', status: 'en cours', createdBy: '<PERSON>', updatedAt: '2025-03-18T14:30:00' },
    { id: '2', name: 'Analy<PERSON> de risques - Projet B', status: 'brouillon', createdBy: '<PERSON>', updatedAt: '2025-03-15T09:45:00' },
    { id: '3', name: 'Analyse de risques - Projet C', status: 'terminé', createdBy: '<PERSON>', updatedAt: '2025-03-10T16:20:00' }
  ]);

  return (
    <div className="p-6">
      <h1 className="text-2xl font-bold mb-6">Gestion des Analyses</h1>
      
      <div className="mb-6 flex justify-between items-center">
        <div>
          <h2 className="text-lg font-semibold">Liste des analyses</h2>
          <p className="text-gray-600">Vue d'ensemble de toutes les analyses de votre entreprise</p>
        </div>
        
         <a href="/provider/analyses/new"
          className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
        >
          Nouvelle analyse
        </a>
      </div>
      
      <div className="bg-white rounded-lg shadow-md overflow-hidden">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Nom
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Statut
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Créée par
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Dernière modification
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {analyses.map((analysis) => (
              <tr key={analysis.id}>
                <td className="px-6 py-4 whitespace-nowrap">
                  {analysis.name}
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <span
                    className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                      analysis.status === 'en cours'
                        ? 'bg-blue-100 text-blue-800'
                        : analysis.status === 'brouillon'
                        ? 'bg-yellow-100 text-yellow-800'
                        : 'bg-green-100 text-green-800'
                    }`}
                  >
                    {analysis.status === 'en cours'
                      ? 'En cours'
                      : analysis.status === 'brouillon'
                      ? 'Brouillon'
                      : 'Terminé'}
                  </span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  {analysis.createdBy}
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  {new Date(analysis.updatedAt).toLocaleString()}
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex space-x-3">
                    
                     <a href={`/provider/analyses/${analysis.id}`}
                      className="text-blue-600 hover:text-blue-900"
                    >
                      Voir
                    </a>
                    
                     <a href={`/provider/analyses/${analysis.id}/edit`}
                      className="text-indigo-600 hover:text-indigo-900"
                    >
                      Modifier
                    </a>
                    <button
                      onClick={() => {
                        if (window.confirm('Êtes-vous sûr de vouloir supprimer cette analyse?')) {
                          setAnalyses(analyses.filter(a => a.id !== analysis.id));
                        }
                      }}
                      className="text-red-600 hover:text-red-900"
                    >
                      Supprimer
                    </button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default AnalysisManagement;