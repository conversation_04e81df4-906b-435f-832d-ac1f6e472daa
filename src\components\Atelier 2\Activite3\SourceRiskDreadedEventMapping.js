// src/components/Atelier 2/Activite3/SourceRiskDreadedEventMapping.js
import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Save, RefreshCw, AlertCircle, Target, Search, Filter, Link, Check, X, Info, BarChart2, Plus, Shield, ChevronDown, Table } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { useAnalysis } from '../../../context/AnalysisContext';
import { showSuccessToast, showErrorToast, showLoadingToast, updateToast } from '../../../utils/toastUtils';
import dreadedEventService from '../../../services/dreadedEventsService';
import DreadedEventDetails from './DreadedEventDetails';
import DreadedEventsLayer from './DreadedEventsLayer';
import SummaryTable from './SummaryTable';

const SourceRiskDreadedEventMapping = ({
  showSummaryTable,
  setShowSummaryTable,
  isSaving,
  setIsSaving,
  saveButtonId
}) => {
  const { t } = useTranslation();
  // State for source risks and dreaded events
  const [sourcesRisque, setSourcesRisque] = useState([]);
  const [dreadedEvents, setDreadedEvents] = useState([]);
  const [mappings, setMappings] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterCategory, setFilterCategory] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const [selectedDreadedEvent, setSelectedDreadedEvent] = useState(null);
  const [activeSourceForEvents, setActiveSourceForEvents] = useState(null);

  // Get analysis context
  const {
    currentAnalysis,
    getSourcesDeRisque,
    saveSelectedCouplesSROV,
    getSelectedCouplesSROV,
    currentDreadedEvents,
  } = useAnalysis();

  // Load data on component mount
  useEffect(() => {
    const fetchData = async () => {
      if (currentAnalysis?.id) {
        setIsLoading(true);
        try {
          // Load sources de risque
          const sourcesData = await getSourcesDeRisque(currentAnalysis.id);
          if (sourcesData) {
            setSourcesRisque(sourcesData);
          }

          // Load dreaded events
          if (currentDreadedEvents && currentDreadedEvents.length > 0) {
            setDreadedEvents(currentDreadedEvents);
          } else {
            try {
              const dreadedEventsResponse = await dreadedEventService.getDreadedEvents(currentAnalysis.id);
              if (dreadedEventsResponse.success && dreadedEventsResponse.data) {
                setDreadedEvents(dreadedEventsResponse.data.data?.dreadedEvents || []);
              }
            } catch (error) {
              console.error('Error loading dreaded events:', error);
              setDreadedEvents([]);
            }
          }

          // Load existing mappings
          const mappingsData = await getSelectedCouplesSROV(currentAnalysis.id);
          if (mappingsData) {
            setMappings(mappingsData);
          }
        } catch (error) {
          console.error('Error loading data:', error);
          showErrorToast(t('workshop2.activity3.errors.loadingError'));
        } finally {
          setIsLoading(false);
        }
      }
    };

    fetchData();
  }, [currentAnalysis?.id, getSourcesDeRisque, getSelectedCouplesSROV, currentDreadedEvents]);

  // Handle toggling mapping between source risk and dreaded event
  const handleToggleMapping = (sourceId, dreadedEventId) => {
    // Check if mapping already exists
    const existingMapping = mappings.find(
      mapping => mapping.sourceId === sourceId && mapping.dreadedEventId === dreadedEventId
    );

    if (existingMapping) {
      // Remove mapping
      setMappings(mappings.filter(
        mapping => !(mapping.sourceId === sourceId && mapping.dreadedEventId === dreadedEventId)
      ));
    } else {
      // Add new mapping
      const newMapping = {
        id: `${sourceId}-${dreadedEventId}-${Date.now()}`,
        sourceId,
        dreadedEventId,
        createdAt: new Date().toISOString()
      };
      setMappings([...mappings, newMapping]);
    }
  };

  // Handle saving mappings
  const handleSaveMappings = async () => {
    if (!currentAnalysis?.id) {
      showErrorToast(t('workshop2.activity1.errors.noAnalysisSelected'));
      return;
    }

    setIsSaving(true);
    const toastId = showLoadingToast(t('workshop2.activity3.saving'));

    try {
      await saveSelectedCouplesSROV(currentAnalysis.id, mappings);
      updateToast(toastId, t('workshop2.activity3.success.associationsSaved'), 'success');
    } catch (error) {
      console.error('Error saving mappings:', error);
      updateToast(toastId, t('workshop2.activity3.errors.saveError'), 'error');
    } finally {
      setIsSaving(false);
    }
  };

  // Add event listener to the save button in the header
  useEffect(() => {
    const saveButton = document.getElementById(saveButtonId);
    if (saveButton) {
      saveButton.addEventListener('click', handleSaveMappings);
    }

    // Cleanup function
    return () => {
      if (saveButton) {
        saveButton.removeEventListener('click', handleSaveMappings);
      }
    };
  }, [saveButtonId, mappings, currentAnalysis]);

  // Get source risk by ID
  const getSourceRisk = (id) => {
    return sourcesRisque.find(source => source.id === id);
  };

  // Get dreaded event by ID
  const getDreadedEvent = (id) => {
    return dreadedEvents.find(event => event.id === id);
  };

  // Get mappings for a source risk
  const getMappingsForSource = (sourceId) => {
    return mappings.filter(mapping => mapping.sourceId === sourceId)
      .map(mapping => mapping.dreadedEventId);
  };

  // Get color for motivation/activity level
  const getLevelColor = (level) => {
    switch (level) {
      case 'faible': return 'bg-green-100 text-green-800';
      case 'moyen': return 'bg-yellow-100 text-yellow-800';
      case 'eleve': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  // Get color for resources level
  const getResourcesColor = (level) => {
    switch (level) {
      case 'faibles': return 'bg-green-100 text-green-800';
      case 'moyennes': return 'bg-yellow-100 text-yellow-800';
      case 'importantes': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  // Get color for security pillar
  const getPillarColor = (pillar) => {
    switch (pillar) {
      case 'confidentialite': return 'bg-blue-100 text-blue-800';
      case 'integrite': return 'bg-purple-100 text-purple-800';
      case 'disponibilite': return 'bg-orange-100 text-orange-800';
      case 'tracabilite': return 'bg-green-100 text-green-800';
      case 'preuve': return 'bg-indigo-100 text-indigo-800';
      case 'Auditabilite': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  // Helper function to get badge color for security pillar (for MultiSelect component)
  const getPillarBadgeColor = (pillar) => {
    switch (pillar) {
      case 'confidentialite': return 'bg-blue-100 text-blue-700';
      case 'integrite': return 'bg-purple-100 text-purple-700';
      case 'disponibilite': return 'bg-orange-100 text-orange-700';
      case 'tracabilite': return 'bg-green-100 text-green-700';
      case 'preuve': return 'bg-indigo-100 text-indigo-700';
      case 'Auditabilite': return 'bg-red-100 text-red-700';
      default: return 'bg-gray-100 text-gray-700';
    }
  };

  // Get color for severity
  const getSeverityColor = (severity) => {
    switch (severity) {
      case 'minor': return 'bg-green-100 text-green-800';
      case 'moderate': return 'bg-yellow-100 text-yellow-800';
      case 'major': return 'bg-orange-100 text-orange-800';
      case 'critical': return 'bg-red-100 text-red-800';
      case 'catastrophic': return 'bg-purple-100 text-purple-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  // Get color for objectif visé category
  const getObjectifBadgeColor = (category) => {
    switch (category) {
      case 'espionnage': return 'bg-blue-100 text-blue-800';
      case 'prepositionnement': return 'bg-purple-100 text-purple-800';
      case 'influence': return 'bg-yellow-100 text-yellow-800';
      case 'entrave': return 'bg-red-100 text-red-800';
      case 'lucratif': return 'bg-green-100 text-green-800';
      case 'defi': return 'bg-indigo-100 text-indigo-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  // Get color for source risk category
  const getSourceCategoryColor = (category) => {
    switch (category?.toLowerCase()) {
      case 'humain': return {
        gradient: 'from-red-500 to-red-600',
        bg: 'bg-red-500',
        text: 'text-red-700',
        light: 'bg-red-50',
        border: 'border-red-200'
      };
      case 'organisationnel': return {
        gradient: 'from-blue-500 to-blue-600',
        bg: 'bg-blue-500',
        text: 'text-blue-700',
        light: 'bg-blue-50',
        border: 'border-blue-200'
      };
      case 'technique': return {
        gradient: 'from-green-500 to-green-600',
        bg: 'bg-green-500',
        text: 'text-green-700',
        light: 'bg-green-50',
        border: 'border-green-200'
      };
      case 'physique': return {
        gradient: 'from-yellow-500 to-yellow-600',
        bg: 'bg-yellow-500',
        text: 'text-yellow-700',
        light: 'bg-yellow-50',
        border: 'border-yellow-200'
      };
      case 'environnemental': return {
        gradient: 'from-purple-500 to-purple-600',
        bg: 'bg-purple-500',
        text: 'text-purple-700',
        light: 'bg-purple-50',
        border: 'border-purple-200'
      };
      default: return {
        gradient: 'from-gray-500 to-gray-600',
        bg: 'bg-gray-500',
        text: 'text-gray-700',
        light: 'bg-gray-50',
        border: 'border-gray-200'
      };
    }
  };

  // Get unique categories for filtering
  const categories = [...new Set(sourcesRisque.map(source => source.category))];

  // Filter sources based on search term, category filter, and retained status
  const filteredSources = sourcesRisque.filter(source => {
    const matchesSearch =
      (source.name?.toLowerCase() || '').includes(searchTerm.toLowerCase()) ||
      (source.description?.toLowerCase() || '').includes(searchTerm.toLowerCase()) ||
      (source.objectifVise?.toLowerCase() || '').includes(searchTerm.toLowerCase());

    const matchesCategory = filterCategory ? source.category === filterCategory : true;

    // Only include sources that are marked as retained (selected)
    const isRetained = source.selected === true;

    return matchesSearch && matchesCategory && isRetained;
  });

  // CSS variables for category colors - using the same colors as in ThreatCategories.js
  const categoryColorStyles = `
    :root {
      --étatique-start: #dc2626;
      --étatique-end: #b91c1c;
      --étatique-light: #fee2e2;
      --étatique-text: #991b1b;
      --étatique-border: #fecaca;

      --criminel-start: #9333ea;
      --criminel-end: #7e22ce;
      --criminel-light: #f3e8ff;
      --criminel-text: #6b21a8;
      --criminel-border: #e9d5ff;

      --crime-organisé-start: #9333ea;
      --crime-organisé-end: #7e22ce;
      --crime-organisé-light: #f3e8ff;
      --crime-organisé-text: #6b21a8;
      --crime-organisé-border: #e9d5ff;

      --terroriste-start: #f97316;
      --terroriste-end: #ea580c;
      --terroriste-light: #ffedd5;
      --terroriste-text: #c2410c;
      --terroriste-border: #fed7aa;

      --activiste-idéologique-start: #16a34a;
      --activiste-idéologique-end: #15803d;
      --activiste-idéologique-light: #dcfce7;
      --activiste-idéologique-text: #166534;
      --activiste-idéologique-border: #bbf7d0;

      --activiste-start: #16a34a;
      --activiste-end: #15803d;
      --activiste-light: #dcfce7;
      --activiste-text: #166534;
      --activiste-border: #bbf7d0;

      --officine-spécialisée-start: #2563eb;
      --officine-spécialisée-end: #1d4ed8;
      --officine-spécialisée-light: #dbeafe;
      --officine-spécialisée-text: #1e40af;
      --officine-spécialisée-border: #bfdbfe;

      --officine-start: #2563eb;
      --officine-end: #1d4ed8;
      --officine-light: #dbeafe;
      --officine-text: #1e40af;
      --officine-border: #bfdbfe;

      --amateur-start: #ca8a04;
      --amateur-end: #a16207;
      --amateur-light: #fef9c3;
      --amateur-text: #854d0e;
      --amateur-border: #fef08a;

      --vengeur-start: #db2777;
      --vengeur-end: #be185d;
      --vengeur-light: #fce7f3;
      --vengeur-text: #9d174d;
      --vengeur-border: #fbcfe8;

      --malveillant-pathologique-start: #4f46e5;
      --malveillant-pathologique-end: #4338ca;
      --malveillant-pathologique-light: #e0e7ff;
      --malveillant-pathologique-text: #3730a3;
      --malveillant-pathologique-border: #c7d2fe;

      --malveillant-start: #4f46e5;
      --malveillant-end: #4338ca;
      --malveillant-light: #e0e7ff;
      --malveillant-text: #3730a3;
      --malveillant-border: #c7d2fe;

      --default-start: #6b7280;
      --default-end: #4b5563;
      --default-light: #f9fafb;
      --default-text: #374151;
      --default-border: #e5e7eb;
    }
  `;

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="bg-gray-50 p-6 rounded-lg"
    >
      <style>{categoryColorStyles}</style>


      {/* Enhanced Info cards with cleaner design */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <div className="bg-white rounded-lg border border-gray-200 overflow-hidden hover:shadow-md transition-shadow duration-300">
          <div className="border-l-4 border-blue-500">
            <div className="p-4">
              <div className="flex items-center">
                <div className="bg-blue-50 p-2 rounded-lg mr-3 flex-shrink-0">
                  <AlertCircle size={20} className="text-blue-600" />
                </div>
                <div>
                  <h3 className="font-medium text-gray-700 text-sm uppercase tracking-wide">{t('workshop2.activity3.cards.retainedRiskSources')}</h3>
                  <div className="flex items-baseline mt-1">
                    <span className="text-xl font-bold text-gray-900 mr-2">
                      {sourcesRisque.filter(source => source.selected === true).length}
                    </span>
                    <span className="text-xs text-gray-500">
                      {t('workshop2.activity3.cards.outOfIdentified', { total: sourcesRisque.length })}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg border border-gray-200 overflow-hidden hover:shadow-md transition-shadow duration-300">
          <div className="border-l-4 border-purple-500">
            <div className="p-4">
              <div className="flex items-center">
                <div className="bg-purple-50 p-2 rounded-lg mr-3 flex-shrink-0">
                  <Target size={20} className="text-purple-600" />
                </div>
                <div>
                  <h3 className="font-medium text-gray-700 text-sm uppercase tracking-wide">{t('workshop2.activity3.cards.dreadedEvents')}</h3>
                  <div className="flex items-baseline mt-1">
                    <span className="text-xl font-bold text-gray-900 mr-2">
                      {dreadedEvents.length}
                    </span>
                    <span className="text-xs text-gray-500">
                      {t('workshop2.activity3.cards.eventsAvailable')}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg border border-gray-200 overflow-hidden hover:shadow-md transition-shadow duration-300">
          <div className="border-l-4 border-green-500">
            <div className="p-4">
              <div className="flex items-center">
                <div className="bg-green-50 p-2 rounded-lg mr-3 flex-shrink-0">
                  <Link size={20} className="text-green-600" />
                </div>
                <div>
                  <h3 className="font-medium text-gray-700 text-sm uppercase tracking-wide">{t('workshop2.activity3.cards.associations')}</h3>
                  <div className="flex items-baseline mt-1">
                    <span className="text-xl font-bold text-gray-900 mr-2">
                      {mappings.length}
                    </span>
                    <span className="text-xs text-gray-500">
                      {t('workshop2.activity3.cards.associationsCreated')}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Enhanced Search and filter */}
      <div className="bg-white rounded-lg border border-gray-200 mb-8">
        <div className="border-b border-gray-200 px-4 py-3">
          <h3 className="font-medium text-gray-700">{t('workshop2.activity3.filter.title')}</h3>
        </div>

        <div className="p-4">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <label className="block text-xs font-medium text-gray-500 mb-1.5">{t('workshop2.activity3.filter.search')}</label>
              <div className="relative">
                <input
                  type="text"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full p-2 pl-9 border border-gray-300 rounded focus:ring-1 focus:ring-blue-300 focus:border-blue-300 transition-all"
                  placeholder={t('workshop2.activity3.filter.searchPlaceholder')}
                />
                <div className="absolute left-0 top-0 h-full flex items-center justify-center w-9 text-gray-400">
                  <Search size={16} />
                </div>
                {searchTerm && (
                  <button
                    className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                    onClick={() => setSearchTerm('')}
                  >
                    <X size={14} />
                  </button>
                )}
              </div>
            </div>

            <div className="md:w-64">
              <label className="block text-xs font-medium text-gray-500 mb-1.5">{t('workshop2.activity3.filter.filterByCategory')}</label>
              <div className="relative">
                <select
                  value={filterCategory}
                  onChange={(e) => setFilterCategory(e.target.value)}
                  className="w-full p-2 pl-3 pr-8 appearance-none border border-gray-300 rounded focus:ring-1 focus:ring-blue-300 focus:border-blue-300 transition-all bg-white"
                >
                  <option value="">{t('workshop2.activity3.filter.allCategories')}</option>
                  {categories.map((category) => (
                    <option key={category} value={category}>
                      {category.charAt(0).toUpperCase() + category.slice(1)}
                    </option>
                  ))}
                </select>
                <div className="absolute right-0 top-0 h-full flex items-center justify-center w-8 pointer-events-none text-gray-500">
                  <ChevronDown size={14} />
                </div>
              </div>
            </div>
          </div>

          {/* Active filters */}
          {(searchTerm || filterCategory) && (
            <div className="mt-3 flex flex-wrap gap-2 items-center">
              <span className="text-xs text-gray-500">{t('workshop2.activity3.filter.activeFilters')}:</span>

              {searchTerm && (
                <div className="bg-gray-100 text-gray-700 px-2 py-1 rounded text-xs flex items-center">
                  <span className="mr-1">{t('workshop2.activity3.filter.search')}:</span>
                  <span className="font-medium">{searchTerm}</span>
                  <button
                    className="ml-1.5 text-gray-500 hover:text-gray-700"
                    onClick={() => setSearchTerm('')}
                  >
                    <X size={12} />
                  </button>
                </div>
              )}

              {filterCategory && (
                <div className="bg-gray-100 text-gray-700 px-2 py-1 rounded text-xs flex items-center">
                  <span className="mr-1">{t('workshop2.activity3.filter.category')}:</span>
                  <span className="font-medium">{filterCategory.charAt(0).toUpperCase() + filterCategory.slice(1)}</span>
                  <button
                    className="ml-1.5 text-gray-500 hover:text-gray-700"
                    onClick={() => setFilterCategory('')}
                  >
                    <X size={12} />
                  </button>
                </div>
              )}

              <button
                className="text-blue-600 hover:text-blue-800 text-xs flex items-center"
                onClick={() => {
                  setSearchTerm('');
                  setFilterCategory('');
                }}
              >
                <RefreshCw size={12} className="mr-1" />
                {t('workshop2.activity3.filter.reset')}
              </button>
            </div>
          )}
        </div>
      </div>

      {/* Modern Loading state with animation */}
      {isLoading ? (
        <div className="bg-white rounded-xl shadow-md border border-gray-100 p-12 text-center">
          <div className="relative w-24 h-24 mx-auto mb-6">
            <div className="absolute inset-0 border-4 border-blue-100 rounded-full"></div>
            <div className="absolute inset-0 border-4 border-transparent border-t-blue-600 rounded-full animate-spin"></div>
            <div className="absolute inset-3 bg-white rounded-full flex items-center justify-center">
              <RefreshCw size={24} className="text-blue-600 animate-spin" />
            </div>
          </div>
          <h3 className="text-xl font-semibold text-gray-800 mb-2">{t('workshop2.activity3.loading.title')}</h3>
          <p className="text-gray-500">{t('workshop2.activity3.loading.message')}</p>
        </div>
      ) : (
        <>
          {/* Modern Empty state with illustration */}
          {filteredSources.length === 0 ? (
            <div className="bg-white rounded-xl shadow-md border border-gray-100 p-8 text-center">
              <div className="w-24 h-24 mx-auto mb-6 bg-gray-100 rounded-full flex items-center justify-center">
                <AlertCircle size={40} className="text-gray-400" />
              </div>
              <h3 className="text-xl font-semibold text-gray-800 mb-3">{t('workshop2.activity3.empty.noRetainedSources')}</h3>

              {sourcesRisque.filter(source => source.selected === true).length === 0 ? (
                <div className="max-w-md mx-auto">
                  <p className="text-gray-600 mb-4">
                    {t('workshop2.activity3.empty.noSourcesRetained')}
                  </p>
                  <div className="bg-blue-50 border-l-4 border-blue-500 p-4 text-left">
                    <div className="flex">
                      <Info size={20} className="text-blue-500 mr-2 flex-shrink-0" />
                      <p className="text-sm text-blue-700">
                        {t('workshop2.activity3.empty.selectSourcesFirst')}
                      </p>
                    </div>
                  </div>
                </div>
              ) : (
                <div className="max-w-md mx-auto">
                  <p className="text-gray-600 mb-4">
                    {t('workshop2.activity3.empty.noMatchingCriteria')}
                  </p>
                  <button
                    onClick={() => {
                      setSearchTerm('');
                      setFilterCategory('');
                    }}
                    className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                  >
                    <RefreshCw size={16} className="mr-2" />
                    {t('workshop2.activity3.filter.resetFilters')}
                  </button>
                </div>
              )}
            </div>
          ) : (
            <>
              {/* Source risk cards with dreaded events mapping */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {filteredSources.map((source) => {
                  const mappedDreadedEventIds = getMappingsForSource(source.id);

                  return (
                    <div
                      key={source.id}
                      className={`relative overflow-hidden rounded-xl border ${getSourceCategoryColor(source.category).border} bg-white shadow-lg hover:shadow-xl transition-all duration-300`}
                    >
                      {/* Category badge - top right corner */}
                      <div className="absolute top-0 right-0">
                        <div
                          className="text-white text-xs font-medium py-1 px-3 rounded-bl-lg"
                          style={{
                            background: `linear-gradient(to right, var(--${source.category?.toLowerCase().replace(/ /g, '-') || 'default'}-start, #6366f1), var(--${source.category?.toLowerCase().replace(/ /g, '-') || 'default'}-end, #4f46e5))`
                          }}
                        >
                          {source.category.charAt(0).toUpperCase() + source.category.slice(1)}
                        </div>
                      </div>

                      {/* Source risk header with category-colored gradient background */}
                      <div className={`bg-gradient-to-r from-white to-${getSourceCategoryColor(source.category).light} p-4 border-b ${getSourceCategoryColor(source.category).border}`}>
                        <div className="flex items-center">
                          <div className={`${getSourceCategoryColor(source.category).light} p-2 rounded-full shadow-sm mr-3`}>
                            <AlertCircle size={16} className={getSourceCategoryColor(source.category).text} />
                          </div>
                          <div>
                            <h3 className="text-base font-semibold text-gray-800 truncate">{source.name}</h3>
                            {source.objectifViseCategory && (
                              <div className="flex items-center mt-1">
                                <span className="text-xs text-gray-500 mr-1">{t('workshop2.activity3.sourceCard.objective')}:</span>
                                <span className={`px-2 py-0.5 rounded-full text-xs ${getObjectifBadgeColor(source.objectifViseCategory)}`}>
                                  {source.objectifViseCategory}
                                </span>
                              </div>
                            )}
                          </div>
                        </div>
                      </div>

                      {/* Source details with modern layout */}
                      <div className="p-4">
                        {/* Description with truncation */}
                        {source.description && (
                          <div className="mb-3 pb-3 border-b border-gray-100">
                            <p className="text-xs text-gray-600 line-clamp-2">
                              {source.description}
                            </p>
                          </div>
                        )}

                        {/* Objectif visé */}
                        <div className="mb-3">
                          <h4 className="text-xs font-medium text-gray-500 uppercase tracking-wide mb-1">{t('workshop2.activity3.sourceCard.targetObjective')}</h4>
                          <p className="text-sm text-gray-800">{source.objectifVise || t('workshop2.activity3.sourceCard.notSpecified')}</p>
                        </div>

                        {/* Attributes with modern indicators */}
                        <div className="grid grid-cols-3 gap-2 mb-4">
                          <div>
                            <h4 className="text-xs font-medium text-gray-500 uppercase tracking-wide mb-1">{t('workshop2.activity1.ai.resources')}</h4>
                            <div className="flex items-center">
                              <div className={`w-2 h-2 rounded-full mr-1.5 ${
                                source.ressources === 'faibles' ? 'bg-green-500' :
                                source.ressources === 'moyennes' ? 'bg-yellow-500' :
                                source.ressources === 'importantes' ? 'bg-red-500' : 'bg-gray-300'
                              }`}></div>
                              <span className="text-xs font-medium">
                                {source.ressources === 'faibles' ? t('workshop2.activity1.levels.low') :
                                 source.ressources === 'moyennes' ? t('workshop2.activity1.levels.medium') :
                                 source.ressources === 'importantes' ? t('workshop2.activity1.levels.high') :
                                 source.ressources || t('workshop2.activity1.levels.low')}
                              </span>
                            </div>
                          </div>

                          <div>
                            <h4 className="text-xs font-medium text-gray-500 uppercase tracking-wide mb-1">{t('workshop2.activity1.ai.activity')}</h4>
                            <div className="flex items-center">
                              <div className={`w-2 h-2 rounded-full mr-1.5 ${
                                source.activite === 'faible' ? 'bg-green-500' :
                                source.activite === 'moyen' ? 'bg-yellow-500' :
                                source.activite === 'eleve' ? 'bg-red-500' : 'bg-gray-300'
                              }`}></div>
                              <span className="text-xs font-medium">
                                {source.activite === 'faible' ? t('workshop2.activity1.levels.low') :
                                 source.activite === 'moyen' ? t('workshop2.activity1.levels.medium') :
                                 source.activite === 'eleve' ? t('workshop2.activity1.levels.high') :
                                 source.activite || t('workshop2.activity1.levels.low')}
                              </span>
                            </div>
                          </div>

                          <div>
                            <h4 className="text-xs font-medium text-gray-500 uppercase tracking-wide mb-1">{t('workshop2.activity1.ai.motivation')}</h4>
                            <div className="flex items-center">
                              <div className={`w-2 h-2 rounded-full mr-1.5 ${
                                source.motivation === 'faible' ? 'bg-green-500' :
                                source.motivation === 'moyen' ? 'bg-yellow-500' :
                                source.motivation === 'eleve' ? 'bg-red-500' : 'bg-gray-300'
                              }`}></div>
                              <span className="text-xs font-medium">
                                {source.motivation === 'faible' ? t('workshop2.activity1.levels.low') :
                                 source.motivation === 'moyen' ? t('workshop2.activity1.levels.medium') :
                                 source.motivation === 'eleve' ? t('workshop2.activity1.levels.high') :
                                 source.motivation || t('workshop2.activity1.levels.low')}
                              </span>
                            </div>
                          </div>
                        </div>

                        {/* Enhanced Dreaded events selection with layer */}
                        <div>
                          <div className="flex items-center justify-between mb-2">
                            <div className="flex items-center">
                              <div className="bg-blue-100 p-1 rounded-full mr-2">
                                <Shield size={12} className="text-blue-600" />
                              </div>
                              <h4 className="text-xs font-medium text-gray-500 uppercase tracking-wide">{t('workshop2.activity3.cards.dreadedEvents')}</h4>
                            </div>
                            <div className="bg-blue-50 text-blue-700 text-xs font-medium px-2 py-0.5 rounded-full">
                              {mappedDreadedEventIds.length} / {dreadedEvents.length}
                            </div>
                          </div>

                          {dreadedEvents.length === 0 ? (
                            <div className="text-center py-2 bg-gray-50 rounded-md">
                              <p className="text-gray-500 text-xs">{t('workshop2.activity3.sourceCard.noDreadedEvents')}</p>
                            </div>
                          ) : (
                            <div className="border border-gray-200 rounded-lg overflow-hidden">
                              {/* Button to open the layer */}
                              <button
                                className="w-full p-3 bg-white hover:bg-gray-50 border-gray-200 flex justify-between items-center cursor-pointer transition-colors"
                                onClick={() => setActiveSourceForEvents(source)}
                              >
                                <div className="flex items-center">
                                  <div className={`w-6 h-6 rounded-full flex items-center justify-center mr-2 ${
                                    mappedDreadedEventIds.length > 0
                                      ? 'bg-blue-100 text-blue-600'
                                      : 'bg-gray-100 text-gray-500'
                                  }`}>
                                    <Shield size={14} />
                                  </div>
                                  <span className="text-sm">
                                    {mappedDreadedEventIds.length > 0
                                      ? t('workshop2.activity3.sourceCard.eventsSelected', { count: mappedDreadedEventIds.length })
                                      : t('workshop2.activity3.sourceCard.selectEvents')}
                                  </span>
                                </div>

                                {/* Preview of selected events */}
                                {mappedDreadedEventIds.length > 0 && (
                                  <div className="flex items-center">
                                    <div className="flex -space-x-2 mr-2">
                                      {mappedDreadedEventIds.slice(0, 3).map((eventId) => {
                                        const event = dreadedEvents.find(e => e.id === eventId);
                                        if (!event) return null;

                                        return (
                                          <div
                                            key={eventId}
                                            className={`w-6 h-6 rounded-full flex items-center justify-center text-xs font-medium border-2 border-white ${getPillarBadgeColor(event.securityPillar)}`}
                                            title={event.name}
                                          >
                                            {event.name.charAt(0)}
                                          </div>
                                        );
                                      })}

                                      {mappedDreadedEventIds.length > 3 && (
                                        <div className="w-6 h-6 rounded-full bg-gray-200 flex items-center justify-center text-xs font-medium border-2 border-white">
                                          +{mappedDreadedEventIds.length - 3}
                                        </div>
                                      )}
                                    </div>
                                  </div>
                                )}
                              </button>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>

              {/* Summary Table - Now displayed after the cards */}
              {showSummaryTable && (
                <div className="mt-8">
                  <SummaryTable
                    mappings={mappings}
                    sourcesRisque={sourcesRisque.filter(source => source.selected === true)}
                    dreadedEvents={dreadedEvents}
                    getPillarBadgeColor={getPillarBadgeColor}
                    setSelectedDreadedEvent={setSelectedDreadedEvent}
                  />
                </div>
              )}
            </>
          )}
        </>
      )}
      {/* Dreaded Event Details Modal */}
      {selectedDreadedEvent && (() => {
        // Prepare businessValues for DreadedEventDetails from selectedDreadedEvent.impacts
        const businessValuesForDetails = selectedDreadedEvent.impacts
          ? Object.entries(selectedDreadedEvent.impacts).map(([type, value]) => ({ type, value }))
          : [];

        return (
          <DreadedEventDetails
            event={selectedDreadedEvent}
            onClose={() => setSelectedDreadedEvent(null)}
            businessValues={businessValuesForDetails}
            supportingAssets={selectedDreadedEvent.supportingAssets || []}
            getPillarColor={getPillarColor}
            getSeverityColor={getSeverityColor}
          />
        );
      })()}

      {/* Dreaded Events Layer */}
      {activeSourceForEvents && (
        <DreadedEventsLayer
          isOpen={!!activeSourceForEvents}
          onClose={() => setActiveSourceForEvents(null)}
          sourceRisk={activeSourceForEvents}
          dreadedEvents={dreadedEvents}
          mappedDreadedEventIds={getMappingsForSource(activeSourceForEvents.id)}
          handleToggleMapping={handleToggleMapping}
          setSelectedDreadedEvent={setSelectedDreadedEvent}
          getPillarBadgeColor={getPillarBadgeColor}
        />
      )}
    </motion.div>
  );
};

export default SourceRiskDreadedEventMapping;
