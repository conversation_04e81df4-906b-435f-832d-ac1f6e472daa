// components/Atelier4/steps/AttackPathSelector.js
import React, { useState, useEffect } from 'react';
import { useWorkflow } from '../../../contexts/WorkflowContext';
import { api } from '../../../api/apiClient';
import { Target, Users, AlertTriangle, ArrowRight, CheckCircle, Database, Shield } from 'lucide-react';

const AttackPathSelector = () => {
  const { updateStep, selectedAttackPath, currentSession } = useWorkflow();
  const [attackPaths, setAttackPaths] = useState([]);
  const [selectedPath, setSelectedPath] = useState(selectedAttackPath);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [isUpdating, setIsUpdating] = useState(false);

  useEffect(() => {
    if (currentSession) {
      loadAttackPaths();
    }
  }, [currentSession]);

  const loadAttackPaths = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await api.get(`/workflow/sessions/${currentSession._id}/attack-paths`);

      if (response.data.success) {
        setAttackPaths(response.data.data);
        console.log('[AttackPathSelector] Loaded attack paths:', response.data.data.length);
        console.log('[AttackPathSelector] Sample attack path:', response.data.data[0]);
      } else if (Array.isArray(response.data)) {
        // Direct array response
        setAttackPaths(response.data);
        console.log('[AttackPathSelector] Loaded attack paths:', response.data.length);
        console.log('[AttackPathSelector] Sample attack path:', response.data[0]);
      } else {
        // No attack paths available
        setAttackPaths([]);
        console.log('[AttackPathSelector] No attack paths available');
      }
    } catch (error) {
      console.error('[AttackPathSelector] Error loading attack paths:', error);
      // Don't show error for missing attack paths, just show empty state
      setAttackPaths([]);
      setError(null);
    } finally {
      setIsLoading(false);
    }
  };

  const handlePathSelection = (path) => {
    setSelectedPath(path);
  };

  const handleContinue = async () => {
    console.log('[AttackPathSelector] handleContinue called');
    console.log('[AttackPathSelector] selectedPath:', selectedPath);

    if (!selectedPath) {
      console.log('[AttackPathSelector] No path selected');
      setError('Veuillez sélectionner un chemin d\'attaque.');
      return;
    }

    setIsUpdating(true);
    setError(null);

    try {
      const pathData = {
        selectedAttackPath: {
          pathId: selectedPath.id,
          pathName: selectedPath.name || selectedPath.title || `Chemin ${selectedPath.id}`,
          sourceRisk: selectedPath.sourceRisk,
          targetObjectives: selectedPath.targetObjectives,
          stakeholders: selectedPath.stakeholders,
          criticality: selectedPath.criticality,
          supportAssets: selectedPath.supportAssets
        }
      };

      console.log('[AttackPathSelector] Calling updateStep with data:', pathData);

      const success = await updateStep(2, pathData);

      console.log('[AttackPathSelector] updateStep result:', success);

      if (success) {
        console.log('[AttackPathSelector] Successfully moved to step 2');
      } else {
        setError('Erreur lors de la mise à jour. Veuillez réessayer.');
      }
    } catch (error) {
      console.error('[AttackPathSelector] Error updating step:', error);
      setError('Erreur lors de la sauvegarde. Veuillez réessayer.');
    } finally {
      setIsUpdating(false);
    }
  };

  const getCriticalityColor = (criticality) => {
    switch (criticality?.toLowerCase()) {
      case 'critique':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'élevé':
        return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'moyen':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'faible':
        return 'bg-green-100 text-green-800 border-green-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Chargement des chemins d'attaque...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-md p-4">
        <div className="flex">
          <AlertTriangle className="h-5 w-5 text-red-400" />
          <div className="ml-3">
            <h3 className="text-sm font-medium text-red-800">Erreur</h3>
            <p className="mt-1 text-sm text-red-700">{error}</p>
            <button
              onClick={loadAttackPaths}
              className="mt-2 text-sm text-red-600 hover:text-red-500 underline"
            >
              Réessayer
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* En-tête */}
      <div className="text-center">
        <div className="flex items-center justify-center mb-4">
          <div className="p-3 bg-indigo-100 rounded-full">
            <Target className="h-8 w-8 text-indigo-600" />
          </div>
        </div>
        <h1 className="text-3xl font-bold text-gray-900 mb-2">
          Sélection du Chemin d'Attaque
        </h1>
        <p className="text-lg text-gray-600 max-w-2xl mx-auto">
          Choisissez le chemin d'attaque à analyser pour l'intelligence des menaces.
          Cette sélection déterminera les biens supports et les vecteurs d'attaque à examiner.
        </p>
      </div>

      {/* Statistiques */}
      {attackPaths.length > 0 && (
        <div className="bg-white rounded-lg shadow p-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-indigo-600">{attackPaths.length}</div>
              <div className="text-sm text-gray-500">Chemins disponibles</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-orange-600">
                {attackPaths.filter(p => p.criticality === 'élevé' || p.criticality === 'critique').length}
              </div>
              <div className="text-sm text-gray-500">Criticité élevée</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">
                {attackPaths.reduce((sum, p) => sum + (p.supportAssets?.length || 0), 0)}
              </div>
              <div className="text-sm text-gray-500">Biens supports total</div>
            </div>
          </div>
        </div>
      )}

      {/* Liste des chemins d'attaque */}
      {attackPaths.length === 0 ? (
        <div className="text-center py-12">
          <Target className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            Aucun chemin d'attaque trouvé
          </h3>
          <p className="text-gray-600">
            Veuillez d'abord créer des chemins d'attaque dans l'Atelier 3.
          </p>
        </div>
      ) : (
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-1">
          {attackPaths.map((path) => (
            <div
              key={path.id}
              className={`
                relative p-6 border-2 rounded-lg cursor-pointer transition-all duration-200
                ${selectedPath?.id === path.id
                  ? 'border-indigo-500 bg-indigo-50 shadow-md'
                  : 'border-gray-200 bg-white hover:border-gray-300 hover:shadow-sm'
                }
              `}
              onClick={() => handlePathSelection(path)}
            >
              {/* Sélection */}
              <div className="absolute top-4 right-4">
                {selectedPath?.id === path.id ? (
                  <CheckCircle className="h-6 w-6 text-indigo-600" />
                ) : (
                  <div className="h-6 w-6 border-2 border-gray-300 rounded-full" />
                )}
              </div>

              {/* Contenu principal */}
              <div className="pr-8">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-2">
                      <h3 className="text-lg font-semibold text-gray-900">
                        {path.referenceCode || path.name || path.title || `Chemin d'attaque ${path.id?.slice(-8) || 'N/A'}`}
                      </h3>
                      {path.referenceCode && (
                        <span className="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-indigo-100 text-indigo-800">
                          {path.referenceCode}
                        </span>
                      )}
                    </div>

                    {/* Business Value and Dreaded Event */}
                    {(path.businessValueName || path.dreadedEventName) && (
                      <div className="space-y-2 mb-3">
                        {path.businessValueName && (
                          <div className="flex items-center text-sm">
                            <div className="w-2 h-2 bg-blue-500 rounded-full mr-2"></div>
                            <span className="font-medium text-gray-700">Valeur métier:</span>
                            <span className="ml-2 text-gray-600">{path.businessValueName}</span>
                          </div>
                        )}
                        {path.dreadedEventName && (
                          <div className="flex items-center text-sm">
                            <div className="w-2 h-2 bg-red-500 rounded-full mr-2"></div>
                            <span className="font-medium text-gray-700">Événement redouté:</span>
                            <span className="ml-2 text-gray-600">{path.dreadedEventName}</span>
                          </div>
                        )}
                      </div>
                    )}

                    {path.description && (
                      <p className="text-gray-600 text-sm mb-3">
                        {path.description}
                      </p>
                    )}
                  </div>
                </div>

                {/* Criticité - only show if defined */}
                {path.criticality && (
                  <div className="mb-4">
                    <span className={`
                      inline-flex items-center px-3 py-1 rounded-full text-xs font-medium border
                      ${getCriticalityColor(path.criticality)}
                    `}>
                      <AlertTriangle className="h-3 w-3 mr-1" />
                      Criticité: {path.criticality}
                    </span>
                  </div>
                )}

                {/* Détails du chemin */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                  {/* Source de risque - only show if exists */}
                  {(path.sourceRisk || path.riskSource || path.source) && (
                    <div className="flex items-center">
                      <Shield className="h-4 w-4 text-red-500 mr-2" />
                      <div>
                        <div className="font-medium text-gray-700">Source</div>
                        <div className="text-gray-600">
                          {path.sourceRisk || path.riskSource || path.source}
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Objectifs visés - only show if > 0 */}
                  {(path.targetObjectives?.length > 0) && (
                    <div className="flex items-center">
                      <Target className="h-4 w-4 text-blue-500 mr-2" />
                      <div>
                        <div className="font-medium text-gray-700">Objectifs</div>
                        <div className="text-gray-600">
                          {path.targetObjectives.length} objectif(s)
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Biens supports - only show if > 0 */}
                  {(path.supportAssets?.length > 0) && (
                    <div className="flex items-center">
                      <Database className="h-4 w-4 text-green-500 mr-2" />
                      <div>
                        <div className="font-medium text-gray-700">Biens supports</div>
                        <div className="text-gray-600">
                          {path.supportAssets.length} bien(s)
                        </div>
                      </div>
                    </div>
                  )}
                </div>

                {/* Parties prenantes */}
                {path.stakeholders && path.stakeholders.length > 0 && (
                  <div className="mt-4 pt-4 border-t border-gray-200">
                    <div className="flex items-center text-sm">
                      <Users className="h-4 w-4 text-purple-500 mr-2" />
                      <span className="font-medium text-gray-700">Parties prenantes:</span>
                      <span className="ml-2 text-gray-600">
                        {path.stakeholders.slice(0, 2).map(stakeholder =>
                          typeof stakeholder === 'object' ? stakeholder.name || stakeholder.label || 'Partie prenante' : stakeholder
                        ).join(', ')}
                        {path.stakeholders.length > 2 && ` +${path.stakeholders.length - 2} autres`}
                      </span>
                    </div>
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Bouton de continuation */}
      {attackPaths.length > 0 && (
        <div className="flex justify-center pt-6">
          <button
            onClick={handleContinue}
            disabled={!selectedPath || isUpdating}
            className={`
              flex items-center px-8 py-3 rounded-lg font-medium transition-all duration-200
              ${selectedPath && !isUpdating
                ? 'bg-indigo-600 text-white hover:bg-indigo-700 shadow-md hover:shadow-lg'
                : 'bg-gray-300 text-gray-500 cursor-not-allowed'
              }
            `}
          >
            {isUpdating ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Sauvegarde...
              </>
            ) : (
              <>
                Continuer vers la sélection des biens
                <ArrowRight className="ml-2 h-4 w-4" />
              </>
            )}
          </button>
        </div>
      )}
    </div>
  );
};

export default AttackPathSelector;
