// src/services/attackPathsService.js
import { api } from '../api/apiClient';

// Service for managing attack paths data
export const attackPathsService = {
  // Get all attack paths for an analysis
  getAttackPaths: async (analysisId) => {
    try {
      const response = await api.get(`/analyses/${analysisId}/attack-paths`);
      return response; // Return the full response object
    } catch (error) {
      console.error('Error fetching attack paths:', error);
      throw error;
    }
  },

  // Save attack paths data
  saveAttackPaths: async (analysisId, attackPathsData) => {
    try {
      const requestBody = { attackPaths: attackPathsData };
      const response = await api.post(`/analyses/${analysisId}/attack-paths`, requestBody);
      return response; // Return the full response object
    } catch (error) {
      console.error('Error saving attack paths:', error);
      throw error;
    }
  },

  // Update specific attack path
  updateAttackPath: async (analysisId, pathId, pathData) => {
    try {
      const response = await api.put(`/analyses/${analysisId}/attack-paths/${pathId}`, pathData);
      return response; // Return the full response object
    } catch (error) {
      console.error('Error updating attack path:', error);
      throw error;
    }
  },

  // Delete attack path
  deleteAttackPath: async (analysisId, pathId) => {
    try {
      const response = await api.delete(`/analyses/${analysisId}/attack-paths/${pathId}`);
      return response; // Return the full response object
    } catch (error) {
      console.error('Error deleting attack path:', error);
      throw error;
    }
  },

  // Generate attack sequences from strategic scenarios
  generateAttackSequences: async (analysisId, strategicScenarios) => {
    try {
      const response = await api.post(`/analyses/${analysisId}/attack-paths/generate`, {
        strategicScenarios
      });
      return response; // Return the full response object
    } catch (error) {
      console.error('Error generating attack sequences:', error);
      throw error;
    }
  },

  // Analyze attack path complexity
  analyzeComplexity: async (analysisId, pathId) => {
    try {
      const response = await api.get(`/analyses/${analysisId}/attack-paths/${pathId}/complexity`);
      return response; // Return the full response object
    } catch (error) {
      console.error('Error analyzing attack path complexity:', error);
      throw error;
    }
  }
};

// Local storage fallback functions
export const attackPathsLocalStorage = {
  // Get attack paths from local storage
  getAttackPaths: () => {
    try {
      const data = localStorage.getItem('ebiosrm_attack_paths');
      return data ? JSON.parse(data) : [];
    } catch (error) {
      console.error('Error reading attack paths from local storage:', error);
      return [];
    }
  },

  // Save attack paths to local storage
  saveAttackPaths: (attackPaths) => {
    try {
      localStorage.setItem('ebiosrm_attack_paths', JSON.stringify(attackPaths));
      return true;
    } catch (error) {
      console.error('Error saving attack paths to local storage:', error);
      return false;
    }
  },

  // Clear attack paths from local storage
  clearAttackPaths: () => {
    try {
      localStorage.removeItem('ebiosrm_attack_paths');
      return true;
    } catch (error) {
      console.error('Error clearing attack paths from local storage:', error);
      return false;
    }
  }
};

export default attackPathsService;
