// src/components/Atelier4/Activite1/AttackGraphVisualization.js
import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { ArrowRight, ArrowDown, Eye, EyeOff, Info } from 'lucide-react';

const AttackGraphVisualization = ({ attackPath, phases }) => {
  const [showDetails, setShowDetails] = useState(true);
  const [selectedAction, setSelectedAction] = useState(null);

  // Transform steps into phases format if needed
  const getAttackPhases = () => {
    if (attackPath?.phases) {
      return attackPath.phases;
    }

    if (attackPath?.steps) {
      // Group steps by phase
      const phaseGroups = {};
      attackPath.steps.forEach(step => {
        if (!phaseGroups[step.phase]) {
          phaseGroups[step.phase] = [];
        }
        phaseGroups[step.phase].push({
          id: step.id,
          title: step.name,
          description: step.description,
          difficulty: 2, // Default difficulty
          probability: 2, // Default probability
          techniques: step.techniques || [],
          duration: step.duration
        });
      });

      // Convert to phases array
      return Object.entries(phaseGroups).map(([phaseId, actions]) => ({
        phase: phaseId,
        actions: actions
      }));
    }

    return [];
  };

  // Difficulty colors
  const difficultyColors = {
    0: 'bg-green-100 border-green-300 text-green-800',
    1: 'bg-blue-100 border-blue-300 text-blue-800',
    2: 'bg-yellow-100 border-yellow-300 text-yellow-800',
    3: 'bg-orange-100 border-orange-300 text-orange-800',
    4: 'bg-red-100 border-red-300 text-red-800'
  };

  // Probability colors
  const probabilityColors = {
    0: 'bg-gray-100 border-gray-300 text-gray-800',
    1: 'bg-red-100 border-red-300 text-red-800',
    2: 'bg-yellow-100 border-yellow-300 text-yellow-800',
    3: 'bg-orange-100 border-orange-300 text-orange-800',
    4: 'bg-green-100 border-green-300 text-green-800'
  };

  const getPhaseInfo = (phaseId) => {
    return phases.find(p => p.id === phaseId);
  };

  const getDifficultyLabel = (level) => {
    const labels = {
      0: 'Négligeable',
      1: 'Faible',
      2: 'Modérée',
      3: 'Élevée',
      4: 'Très élevée'
    };
    return labels[level] || 'Non défini';
  };

  const getProbabilityLabel = (level) => {
    const labels = {
      0: 'Très faible',
      1: 'Faible',
      2: 'Significative',
      3: 'Très élevée',
      4: 'Quasi-certaine'
    };
    return labels[level] || 'Non défini';
  };

  if (!attackPath) {
    return null;
  }

  return (
    <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-100">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h3 className="text-lg font-medium text-gray-800">{attackPath.title}</h3>
          <p className="text-gray-600">{attackPath.description}</p>
        </div>
        <div className="flex items-center space-x-3">
          <button
            onClick={() => setShowDetails(!showDetails)}
            className="flex items-center px-3 py-2 text-sm bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200"
          >
            {showDetails ? <EyeOff size={14} className="mr-1" /> : <Eye size={14} className="mr-1" />}
            {showDetails ? 'Masquer détails' : 'Afficher détails'}
          </button>
        </div>
      </div>

      {/* Attack Graph */}
      <div className="space-y-8">
        {getAttackPhases().map((phaseData, phaseIndex) => {
          const phaseInfo = getPhaseInfo(phaseData.phase);
          const IconComponent = phaseInfo?.icon;
          const isLastPhase = phaseIndex === getAttackPhases().length - 1;

          return (
            <motion.div
              key={phaseData.phase}
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: phaseIndex * 0.2 }}
              className="relative"
            >
              {/* Phase Header */}
              <div className="flex items-center mb-4">
                <div className={`${phaseInfo?.color} p-3 rounded-lg mr-4`}>
                  {IconComponent && <IconComponent size={24} className="text-white" />}
                </div>
                <div>
                  <h4 className="text-lg font-medium text-gray-800">{phaseInfo?.name}</h4>
                  <p className="text-sm text-gray-600">{phaseInfo?.description}</p>
                </div>
              </div>

              {/* Actions */}
              <div className="ml-16 space-y-3">
                {phaseData.actions?.map((action, actionIndex) => {
                  const isLastAction = actionIndex === phaseData.actions.length - 1;

                  return (
                    <motion.div
                      key={action.id}
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: (phaseIndex * 0.2) + (actionIndex * 0.1) }}
                      className="relative"
                    >
                      <div
                        className={`border-2 border-dashed border-gray-300 rounded-lg p-4 cursor-pointer transition-all hover:border-blue-400 hover:bg-blue-50 ${
                          selectedAction?.id === action.id ? 'border-blue-500 bg-blue-50' : ''
                        }`}
                        onClick={() => setSelectedAction(selectedAction?.id === action.id ? null : action)}
                      >
                        <div className="flex justify-between items-start">
                          <div className="flex-1">
                            <h5 className="font-medium text-gray-800 mb-1">{action.title}</h5>
                            {showDetails && action.description && (
                              <p className="text-sm text-gray-600 mb-3">{action.description}</p>
                            )}

                            {showDetails && (
                              <div className="flex space-x-3">
                                <div className={`px-2 py-1 rounded text-xs border ${difficultyColors[action.difficulty]}`}>
                                  Difficulté: {getDifficultyLabel(action.difficulty)}
                                </div>
                                <div className={`px-2 py-1 rounded text-xs border ${probabilityColors[action.probability]}`}>
                                  Probabilité: {getProbabilityLabel(action.probability)}
                                </div>
                              </div>
                            )}
                          </div>

                          <button
                            className="text-gray-400 hover:text-gray-600 p-1"
                            onClick={(e) => {
                              e.stopPropagation();
                              setSelectedAction(selectedAction?.id === action.id ? null : action);
                            }}
                          >
                            <Info size={16} />
                          </button>
                        </div>
                      </div>

                      {/* Arrow to next action */}
                      {!isLastAction && (
                        <div className="flex justify-center my-2">
                          <ArrowDown size={20} className="text-gray-400" />
                        </div>
                      )}
                    </motion.div>
                  );
                })}
              </div>

              {/* Arrow to next phase */}
              {!isLastPhase && (
                <div className="flex justify-center my-6">
                  <div className="flex items-center">
                    <div className="h-px bg-gray-300 w-8"></div>
                    <ArrowRight size={24} className="text-gray-400 mx-2" />
                    <div className="h-px bg-gray-300 w-8"></div>
                  </div>
                </div>
              )}
            </motion.div>
          );
        })}
      </div>

      {/* Action Details Panel */}
      {selectedAction && (
        <motion.div
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: 'auto' }}
          exit={{ opacity: 0, height: 0 }}
          className="mt-6 border-t border-gray-200 pt-6"
        >
          <div className="bg-gray-50 rounded-lg p-4">
            <h4 className="font-medium text-gray-800 mb-3">Détails de l'action</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <h5 className="text-sm font-medium text-gray-700 mb-1">Titre</h5>
                <p className="text-sm text-gray-900">{selectedAction.title}</p>
              </div>
              <div>
                <h5 className="text-sm font-medium text-gray-700 mb-1">Évaluation</h5>
                <div className="flex space-x-2">
                  <span className={`px-2 py-1 rounded text-xs border ${difficultyColors[selectedAction.difficulty]}`}>
                    Difficulté: {getDifficultyLabel(selectedAction.difficulty)}
                  </span>
                  <span className={`px-2 py-1 rounded text-xs border ${probabilityColors[selectedAction.probability]}`}>
                    Probabilité: {getProbabilityLabel(selectedAction.probability)}
                  </span>
                </div>
              </div>
              {selectedAction.description && (
                <div className="md:col-span-2">
                  <h5 className="text-sm font-medium text-gray-700 mb-1">Description</h5>
                  <p className="text-sm text-gray-900">{selectedAction.description}</p>
                </div>
              )}
            </div>
          </div>
        </motion.div>
      )}

      {/* Summary */}
      <div className="mt-6 bg-gray-50 rounded-lg p-4">
        <h4 className="font-medium text-gray-800 mb-3">Résumé du scénario</h4>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
          <div>
            <span className="text-gray-600">Phases d'attaque:</span>
            <span className="ml-2 font-medium">{getAttackPhases().length || 0}</span>
          </div>
          <div>
            <span className="text-gray-600">Actions élémentaires:</span>
            <span className="ml-2 font-medium">
              {getAttackPhases().reduce((total, phase) => total + (phase.actions?.length || 0), 0) || 0}
            </span>
          </div>
          <div>
            <span className="text-gray-600">Vraisemblance:</span>
            <span className="ml-2 font-medium">{attackPath.likelihood}</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AttackGraphVisualization;