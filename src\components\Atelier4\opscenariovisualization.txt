// src/components/Atelier4/InlineScenarioVisualization.js
import React, { useState, useCallback, useEffect, useRef } from 'react';
import ReactFlow, {
  MiniMap,
  Controls,
  Background,
  addEdge,
  applyNodeChanges,
  applyEdgeChanges,
  useReactFlow,
} from 'reactflow';
import 'reactflow/dist/style.css';
import { Eye, RefreshCw, Download, ChevronDown, ChevronUp } from 'lucide-react';
import { toPng } from 'html-to-image';

// Main scenario node (parent)
const ScenarioMainNode = ({ data, selected }) => {
  return (
    <div 
      className={`relative p-6 border-2 rounded-xl shadow-lg w-96 bg-gradient-to-br from-indigo-50 to-blue-50 ${
        selected ? 'border-blue-500' : 'border-indigo-300'
      }`}
    >
      <div className="flex items-center mb-3">
        <div className="w-10 h-10 rounded-full bg-indigo-600 flex items-center justify-center mr-3">
          <Eye className="text-white" size={20} />
        </div>
        <div>
          <div className="font-bold text-indigo-800 text-lg">{data.name}</div>
          <div className="text-sm text-indigo-600">Scénario Principal</div>
        </div>
      </div>
      
      <div className="text-sm text-indigo-700 mb-4">{data.description}</div>
      
      <div className="grid grid-cols-2 gap-3 text-xs">
        {data.severity && (
          <div className="bg-white bg-opacity-60 p-2 rounded">
            <div className="text-indigo-600 font-semibold">Sévérité</div>
            <div className="text-indigo-800 font-bold">{data.severity}</div>
          </div>
        )}
        {data.likelihood && (
          <div className="bg-white bg-opacity-60 p-2 rounded">
            <div className="text-indigo-600 font-semibold">Probabilité</div>
            <div className="text-indigo-800 font-bold">{data.likelihood}</div>
          </div>
        )}
        {data.timeline && (
          <div className="bg-white bg-opacity-60 p-2 rounded">
            <div className="text-indigo-600 font-semibold">Durée</div>
            <div className="text-indigo-800 font-bold">{data.timeline}</div>
          </div>
        )}
        {data.detectionDifficulty && (
          <div className="bg-white bg-opacity-60 p-2 rounded">
            <div className="text-indigo-600 font-semibold">Détection</div>
            <div className="text-indigo-800 font-bold">{data.detectionDifficulty}</div>
          </div>
        )}
      </div>
    </div>
  );
};

// Step nodes (children)
const ScenarioStepNode = ({ data, selected }) => {
  const getPhaseColor = (phase) => {
    switch (phase) {
      case 'CONNAITRE': return '#3B82F6'; // Blue
      case 'RENTRER': return '#EF4444'; // Red
      case 'TROUVER': return '#F59E0B'; // Amber
      case 'EXPLOITER': return '#10B981'; // Green
      default: return '#6B7280'; // Gray
    }
  };

  const phaseColor = getPhaseColor(data.phase);

  return (
    <div 
      className={`relative p-3 border-2 rounded-lg shadow-md w-72 bg-white ${
        selected ? 'border-blue-500' : 'border-gray-300'
      }`}
      style={{ borderColor: selected ? '#3B82F6' : phaseColor }}
    >
      <div className="flex items-start mb-2">
        <div 
          className="w-8 h-8 rounded-full flex items-center justify-center text-white text-sm font-bold mr-3 flex-shrink-0"
          style={{ backgroundColor: phaseColor }}
        >
          {data.stepNumber}
        </div>
        <div className="flex-1">
          <div className="flex items-center mb-1">
            {data.phase && (
              <span 
                className="px-2 py-1 rounded text-xs font-semibold text-white mr-2"
                style={{ backgroundColor: phaseColor }}
              >
                {data.phase}
              </span>
            )}
          </div>
          <div className="font-bold text-gray-800 text-sm mb-1">{data.name || data.technique}</div>
          <div className="text-xs text-gray-600 mb-2">{data.description}</div>
          
          <div className="flex justify-between text-xs text-gray-500">
            {data.duration && (
              <div><strong>Durée:</strong> {data.duration}</div>
            )}
            {data.difficulty && (
              <div><strong>Difficulté:</strong> {data.difficulty}</div>
            )}
          </div>
        </div>
      </div>

      {/* Techniques */}
      {data.techniques && data.techniques.length > 0 && (
        <div className="mb-2">
          <div className="text-xs font-semibold text-gray-600 mb-1">Techniques:</div>
          <div className="flex flex-wrap gap-1">
            {data.techniques.slice(0, 2).map((technique, index) => (
              <span key={index} className="inline-flex items-center px-2 py-0.5 rounded text-xs bg-purple-100 text-purple-800">
                {technique}
              </span>
            ))}
            {data.techniques.length > 2 && (
              <span className="text-xs text-gray-500">+{data.techniques.length - 2}</span>
            )}
          </div>
        </div>
      )}

      {/* Indicators */}
      {data.indicators && data.indicators.length > 0 && (
        <div>
          <div className="text-xs font-semibold text-gray-600 mb-1">Indicateurs:</div>
          <div className="flex flex-wrap gap-1">
            {data.indicators.slice(0, 2).map((indicator, index) => (
              <span key={index} className="inline-flex items-center px-2 py-0.5 rounded text-xs bg-yellow-100 text-yellow-800">
                {indicator}
              </span>
            ))}
            {data.indicators.length > 2 && (
              <span className="text-xs text-gray-500">+{data.indicators.length - 2}</span>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

const nodeTypes = {
  scenarioMain: ScenarioMainNode,
  scenarioStep: ScenarioStepNode,
};

// Inline visualization component
const InlineScenarioVisualization = ({ scenario, isExpanded, onToggle }) => {
  const [nodes, setNodes] = useState([]);
  const [edges, setEdges] = useState([]);
  const reactFlowWrapper = useRef(null);

  // Create hierarchical nodes and edges
  useEffect(() => {
    if (!scenario || !isExpanded) return;

    const createHierarchicalVisualization = () => {
      const newNodes = [];
      const newEdges = [];

      // Create main scenario node (parent)
      const mainNode = {
        id: 'main-scenario',
        type: 'scenarioMain',
        position: { x: 100, y: 50 },
        data: {
          name: scenario.name || scenario.title || 'Scénario Opérationnel',
          description: scenario.description || '',
          severity: scenario.severity,
          likelihood: scenario.likelihood,
          timeline: scenario.timeline,
          detectionDifficulty: scenario.detectionDifficulty,
        },
      };
      newNodes.push(mainNode);

      // Create step nodes if available
      if (scenario.steps && scenario.steps.length > 0) {
        scenario.steps.forEach((step, index) => {
          const stepNode = {
            id: `step-${index}`,
            type: 'scenarioStep',
            position: { 
              x: 50 + (index % 4) * 320, 
              y: 280 + Math.floor(index / 4) * 200 
            },
            data: {
              stepNumber: step.stepNumber || index + 1,
              phase: step.phase,
              name: step.name,
              technique: step.technique,
              description: step.description,
              duration: step.duration,
              difficulty: step.difficulty,
              techniques: step.techniques || [],
              indicators: step.indicators || [],
              tools: step.tools || [],
            },
          };
          newNodes.push(stepNode);

          // Connect main node to each step
          const mainToStepEdge = {
            id: `main-to-step-${index}`,
            source: 'main-scenario',
            target: `step-${index}`,
            type: 'smoothstep',
            animated: false,
            style: { 
              stroke: '#94A3B8', 
              strokeWidth: 2,
              strokeDasharray: '5,5'
            },
          };
          newEdges.push(mainToStepEdge);

          // Connect steps in sequence (attack path flow)
          if (index > 0) {
            const sequenceEdge = {
              id: `sequence-${index}`,
              source: `step-${index - 1}`,
              target: `step-${index}`,
              type: 'smoothstep',
              animated: true,
              style: { 
                stroke: '#6366F1', 
                strokeWidth: 3 
              },
              label: `Étape ${index + 1}`,
              labelStyle: { 
                fill: '#6366F1', 
                fontWeight: 600,
                fontSize: 12
              },
            };
            newEdges.push(sequenceEdge);
          }

          // Add phase progression connections
          if (index > 0) {
            const currentPhase = step.phase;
            const previousPhase = scenario.steps[index - 1].phase;
            
            if (currentPhase !== previousPhase) {
              const phaseEdge = {
                id: `phase-${index}`,
                source: `step-${index - 1}`,
                target: `step-${index}`,
                type: 'step',
                animated: true,
                style: { 
                  stroke: '#F59E0B', 
                  strokeWidth: 2,
                  strokeDasharray: '3,3'
                },
                label: `${previousPhase} → ${currentPhase}`,
                labelStyle: { 
                  fill: '#F59E0B', 
                  fontWeight: 500,
                  fontSize: 10
                },
              };
            }
          }
        });
      }

      setNodes(newNodes);
      setEdges(newEdges);
    };

    createHierarchicalVisualization();
  }, [scenario, isExpanded]);

  const onNodesChange = useCallback(
    (changes) => setNodes((nds) => applyNodeChanges(changes, nds)),
    []
  );

  const onEdgesChange = useCallback(
    (changes) => setEdges((eds) => applyEdgeChanges(changes, eds)),
    []
  );

  const exportAsPng = useCallback(() => {
    if (reactFlowWrapper.current === null) return;

    toPng(reactFlowWrapper.current, {
      backgroundColor: '#fff',
      width: reactFlowWrapper.current.offsetWidth,
      height: reactFlowWrapper.current.offsetHeight,
    })
      .then((dataUrl) => {
        const link = document.createElement('a');
        link.download = `scenario-${scenario.name || 'unknown'}-${new Date().toISOString().split('T')[0]}.png`;
        link.href = dataUrl;
        link.click();
      })
      .catch((error) => {
        console.error('Error exporting PNG:', error);
      });
  }, [scenario]);

  if (!isExpanded) return null;

  return (
    <div className="mt-4 border-2 border-gray-200 rounded-lg overflow-hidden bg-gray-50">
      {/* Visualization Header */}
      <div className="bg-white border-b border-gray-200 p-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <Eye size={18} className="mr-2 text-indigo-600" />
            <span className="font-semibold text-gray-800">Visualisation du Scénario</span>
          </div>
          <div className="flex items-center space-x-2">
            <button
              onClick={exportAsPng}
              className="flex items-center px-3 py-1 bg-blue-100 text-blue-700 rounded hover:bg-blue-200 transition text-sm"
              title="Exporter en PNG"
            >
              <Download size={14} className="mr-1" />
              PNG
            </button>
            <button
              onClick={onToggle}
              className="flex items-center px-3 py-1 bg-gray-100 text-gray-700 rounded hover:bg-gray-200 transition text-sm"
              title="Masquer la visualisation"
            >
              <ChevronUp size={14} />
            </button>
          </div>
        </div>
      </div>

      {/* Flow Diagram */}
      <div className="h-96">
        <div
          ref={reactFlowWrapper}
          className="w-full h-full"
        >
          <ReactFlow
            nodes={nodes}
            edges={edges}
            onNodesChange={onNodesChange}
            onEdgesChange={onEdgesChange}
            nodeTypes={nodeTypes}
            fitView
            fitViewOptions={{ padding: 0.1 }}
            attributionPosition="bottom-right"
            nodesDraggable={true}
            nodesConnectable={false}
            elementsSelectable={true}
          >
            <Controls 
              showZoom={true}
              showFitView={true}
              showInteractive={false}
              style={{
                background: 'white',
                border: '1px solid #e2e8f0',
                borderRadius: '6px',
                boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
              }}
            />
            <MiniMap
              nodeColor={(n) => {
                if (n.type === 'scenarioMain') return '#E0E7FF';
                if (n.type === 'scenarioStep') return '#F3F4F6';
                return '#E2E8F0';
              }}
              nodeStrokeWidth={2}
              style={{
                background: 'white',
                border: '1px solid #e2e8f0',
                borderRadius: '6px',
              }}
            />
            <Background color="#e2e8f0" variant="dots" gap={20} size={1} />
          </ReactFlow>
        </div>
      </div>

      {/* Footer */}
      <div className="p-2 border-t border-gray-200 text-xs text-gray-500 bg-gray-50">
        <div className="flex items-center justify-between">
          <span>
            <strong>Légende:</strong> 
            <span className="ml-2 text-blue-600">■ Connexions hiérarchiques</span>
            <span className="ml-2 text-indigo-600">■ Flux d'attaque</span>
            <span className="ml-2 text-amber-600">■ Changement de phase</span>
          </span>
          <span>Déplacez les nœuds pour explorer • Utilisez les contrôles pour naviguer</span>
        </div>
      </div>
    </div>
  );
};

// Updated scenario card component with inline visualization
const ScenarioCard = ({ scenario, index }) => {
  const [isVisualizationExpanded, setIsVisualizationExpanded] = useState(false);

  const toggleVisualization = () => {
    setIsVisualizationExpanded(!isVisualizationExpanded);
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
      {/* Card Header */}
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <div className="w-8 h-8 rounded-full bg-indigo-100 flex items-center justify-center mr-3">
              <span className="text-indigo-600 font-bold text-sm">{index + 1}</span>
            </div>
            <div>
              <h3 className="font-semibold text-gray-800">{scenario.name || scenario.title}</h3>
              <p className="text-sm text-gray-600">{scenario.description}</p>
            </div>
          </div>
          <button
            onClick={toggleVisualization}
            className="flex items-center px-3 py-2 bg-indigo-100 text-indigo-700 rounded-lg hover:bg-indigo-200 transition text-sm"
          >
            <Eye size={16} className="mr-1" />
            {isVisualizationExpanded ? 'Masquer' : 'Visualiser'}
            {isVisualizationExpanded ? <ChevronUp size={16} className="ml-1" /> : <ChevronDown size={16} className="ml-1" />}
          </button>
        </div>
      </div>

      {/* Card Content */}
      <div className="p-4">
        {/* Scenario metadata */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
          {scenario.severity && (
            <div className="text-center p-2 bg-red-50 rounded">
              <div className="text-xs text-red-600 font-semibold">Sévérité</div>
              <div className="text-sm text-red-800 font-bold">{scenario.severity}</div>
            </div>
          )}
          {scenario.likelihood && (
            <div className="text-center p-2 bg-amber-50 rounded">
              <div className="text-xs text-amber-600 font-semibold">Probabilité</div>
              <div className="text-sm text-amber-800 font-bold">{scenario.likelihood}</div>
            </div>
          )}
          {scenario.timeline && (
            <div className="text-center p-2 bg-blue-50 rounded">
              <div className="text-xs text-blue-600 font-semibold">Durée</div>
              <div className="text-sm text-blue-800 font-bold">{scenario.timeline}</div>
            </div>
          )}
          {scenario.detectionDifficulty && (
            <div className="text-center p-2 bg-green-50 rounded">
              <div className="text-xs text-green-600 font-semibold">Détection</div>
              <div className="text-sm text-green-800 font-bold">{scenario.detectionDifficulty}</div>
            </div>
          )}
        </div>

        {/* Steps summary */}
        {scenario.steps && scenario.steps.length > 0 && (
          <div className="mb-4">
            <div className="text-sm font-semibold text-gray-700 mb-2">
              Étapes du scénario ({scenario.steps.length})
            </div>
            <div className="flex flex-wrap gap-2">
              {scenario.steps.map((step, stepIndex) => (
                <span
                  key={stepIndex}
                  className="px-2 py-1 bg-gray-100 text-gray-700 rounded text-xs"
                >
                  {step.phase || `Étape ${stepIndex + 1}`}
                </span>
              ))}
            </div>
          </div>
        )}
      </div>

      {/* Inline Visualization */}
      <InlineScenarioVisualization
        scenario={scenario}
        isExpanded={isVisualizationExpanded}
        onToggle={toggleVisualization}
      />
    </div>
  );
};

export default ScenarioCard;
export { InlineScenarioVisualization };

// CSS Styles for inline visualization
const inlineStyles = `
/* Inline Scenario Visualization Styles */
.react-flow__node-scenarioMain {
  background: transparent !important;
  border: none !important;
}

.react-flow__node-scenarioStep {
  background: transparent !important;
  border: none !important;
}

.react-flow__edge-path {
  stroke-width: 2;
}

.react-flow__edge-text {
  font-size: 11px;
  font-weight: 500;
}

.react-flow__controls {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  bottom: 10px;
  left: 10px;
}

.react-flow__controls button {
  background: white;
  border: none;
  border-bottom: 1px solid #e2e8f0;
  color: #64748b;
  transition: all 0.2s;
  width: 32px;
  height: 32px;
}

.react-flow__controls button:hover {
  background: #f8fafc;
  color: #334155;
}

.react-flow__controls button:last-child {
  border-bottom: none;
}

.react-flow__minimap {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  bottom: 10px;
  right: 10px;
  width: 150px;
  height: 100px;
}

.react-flow__background {
  background-color: #f8fafc;
}

/* Custom edge styles */
.react-flow__edge.react-flow__edge-smoothstep {
  stroke-dasharray: none;
}

.react-flow__edge.react-flow__edge-smoothstep[data-animated="true"] {
  stroke-dasharray: 5;
  animation: dashdraw 0.5s linear infinite;
}

@keyframes dashdraw {
  to {
    stroke-dashoffset: -10;
  }
}

/* Phase color indicators */
.phase-connaitre { border-color: #3B82F6 !important; }
.phase-rentrer { border-color: #EF4444 !important; }
.phase-trouver { border-color: #F59E0B !important; }
.phase-exploiter { border-color: #10B981 !important; }

/* Responsive adjustments */
@media (max-width: 768px) {
  .react-flow__minimap {
    width: 120px;
    height: 80px;
  }
  
  .react-flow__controls {
    bottom: 5px;
    left: 5px;
  }
  
  .react-flow__controls button {
    width: 28px;
    height: 28px;
  }
}
`;

// Inject styles if not already present
if (typeof document !== 'undefined' && !document.getElementById('inline-scenario-visualization-styles')) {
  const styleSheet = document.createElement('style');
  styleSheet.id = 'inline-scenario-visualization-styles';
  styleSheet.textContent = inlineStyles;
  document.head.appendChild(styleSheet);
}