// src/components/navigation/Atelier4Navigation.js
import React from 'react';
import {
  ChevronDown,
  ChevronRight,
  Target,
  Database,
  Brain,
  FileText
} from 'lucide-react';
import { getUnsavedChanges, setNavigationWarningDialogOpen } from '../../utils/navigationUtils';

// Define tab keys for Workshop 4 phases
const ATELIER4_TABS = {
    THREAT_INTELLIGENCE: 'atelier4-threat-intelligence',
    CTI_SCENARIOS: 'atelier4-cti-scenarios',
    SYNTHESIS: 'atelier4-synthesis'
};

const Atelier4Navigation = ({
  activeTab,
  setActiveTab,
  expanded,
  toggleExpand,
  baseNavItemClass,
  workshopTitleClass,
  activeStyle,
  inactiveStyle
}) => {
  // Check if any tab within this workshop is active
  const isAtelierActive = Object.values(ATELIER4_TABS).includes(activeTab);

  // Sub-item class with added padding
  const subItemClass = `${baseNavItemClass} pl-8`; // Indent sub-items

  // Generic handler for tab navigation with unsaved changes check
  const handleTabClick = async (tabKey) => {
    // Check for unsaved changes before navigation
    if (getUnsavedChanges()) {
      // Show the warning dialog
      setNavigationWarningDialogOpen(true);
      // Store the target tab for later use
      localStorage.setItem('ebiosrm_pending_tab', tabKey);
      return;
    }
    // If no unsaved changes, navigate directly
    setActiveTab(tabKey);
  };

  return (
    <li className="w-full">
      <button
        className={`${workshopTitleClass} ${isAtelierActive ? 'text-white' : 'text-gray-300 hover:bg-gray-700 hover:text-white'} group`}
        onClick={toggleExpand}
      >
        <div className="flex items-center w-full justify-between">
          <div className="flex items-center">
            <div className={`${isAtelierActive ? 'bg-blue-500' : 'bg-gray-700 group-hover:bg-gray-600'} p-1.5 rounded-md transition-colors duration-200 mr-3`}>
              <Target size={16} className="flex-shrink-0 text-white" />
            </div>
            <span className="truncate">Atelier 4: Opérationnels</span>
          </div>
          <div className="text-gray-400 transition-transform duration-200">
            {expanded ? <ChevronDown size={16} /> : <ChevronRight size={16} />}
          </div>
        </div>
      </button>

      {expanded && (
        <ul className="mt-1 space-y-1 w-full">
          {/* Phase 1: Threat Intelligence */}
          <li>
            <button
              className={`${subItemClass} ${activeTab === ATELIER4_TABS.THREAT_INTELLIGENCE ? activeStyle : inactiveStyle} group`}
              onClick={() => handleTabClick(ATELIER4_TABS.THREAT_INTELLIGENCE)}
            >
              <div className="flex items-center">
                <div className={`${activeTab === ATELIER4_TABS.THREAT_INTELLIGENCE ? 'bg-blue-500' : 'bg-gray-700 group-hover:bg-gray-600'} p-1 rounded transition-colors duration-200 mr-2`}>
                  <Database size={14} className="flex-shrink-0 text-white" />
                </div>
                <span className="truncate">Intelligence des menaces</span>
              </div>
            </button>
          </li>

          {/* Phase 2: CTI-based Operational Scenarios AI */}
          <li>
            <button
              className={`${subItemClass} ${activeTab === ATELIER4_TABS.CTI_SCENARIOS ? activeStyle : inactiveStyle} group`}
              onClick={() => handleTabClick(ATELIER4_TABS.CTI_SCENARIOS)}
            >
              <div className="flex items-center">
                <div className={`${activeTab === ATELIER4_TABS.CTI_SCENARIOS ? 'bg-blue-500' : 'bg-gray-700 group-hover:bg-gray-600'} p-1 rounded transition-colors duration-200 mr-2`}>
                  <Brain size={14} className="flex-shrink-0 text-white" />
                </div>
                <span className="truncate">Scénarios opérationnels IA</span>
              </div>
            </button>
          </li>

          {/* Phase 3: Synthesis */}
          <li>
            <button
              className={`${subItemClass} ${activeTab === ATELIER4_TABS.SYNTHESIS ? activeStyle : inactiveStyle} group`}
              onClick={() => handleTabClick(ATELIER4_TABS.SYNTHESIS)}
            >
              <div className="flex items-center">
                <div className={`${activeTab === ATELIER4_TABS.SYNTHESIS ? 'bg-blue-500' : 'bg-gray-700 group-hover:bg-gray-600'} p-1 rounded transition-colors duration-200 mr-2`}>
                  <FileText size={14} className="flex-shrink-0 text-white" />
                </div>
                <span className="truncate">Synthèse</span>
              </div>
            </button>
          </li>
        </ul>
      )}
    </li>
  );
};

export default Atelier4Navigation;
export { ATELIER4_TABS };