// src/api/analysisApi.js
import api from './apiClient';

/**
 * API client for analyses management operations
 */
const analysisApi = {
  /**
   * Get all analyses with optional filtering
   * @param {Object} params - Optional filtering parameters
   * @returns {Promise<Object>} List of analyses
   */
  getAnalyses: async (params = {}) => {
    try {
      return await api.get('/analyses', params);
    } catch (error) {
      console.error('Get analyses API error:', error);
      throw error;
    }
  },
  
  /**
   * Get analyses for a specific company
   * @param {string} companyId - Company ID
   * @param {Object} params - Optional filtering parameters
   * @returns {Promise<Object>} List of analyses for the company
   */
  getCompanyAnalyses: async (companyId, params = {}) => {
    try {
      return await api.get(`/companies/${companyId}/analyses`, params);
    } catch (error) {
      console.error('Get company analyses API error:', error);
      throw error;
    }
  },
  
  /**
   * Get a specific analysis by ID
   * @param {string} analysisId - Analysis ID
   * @returns {Promise<Object>} Analysis data
   */
  getAnalysis: async (analysisId) => {
    try {
      return await api.get(`/analyses/${analysisId}`);
    } catch (error) {
      console.error('Get analysis API error:', error);
      throw error;
    }
  },
  
  /**
   * Create a new analysis
   * @param {Object} analysisData - Analysis data to create
   * @returns {Promise<Object>} Created analysis
   */
  createAnalysis: async (analysisData) => {
    try {
      return await api.post('/analyses', analysisData);
    } catch (error) {
      console.error('Create analysis API error:', error);
      throw error;
    }
  },
  
  /**
   * Update an existing analysis
   * @param {string} analysisId - Analysis ID
   * @param {Object} analysisData - Analysis data to update
   * @returns {Promise<Object>} Updated analysis
   */
  updateAnalysis: async (analysisId, analysisData) => {
    try {
      return await api.put(`/analyses/${analysisId}`, analysisData);
    } catch (error) {
      console.error('Update analysis API error:', error);
      throw error;
    }
  },
  
  /**
   * Delete an analysis
   * @param {string} analysisId - Analysis ID
   * @returns {Promise<Object>} Deletion result
   */
  deleteAnalysis: async (analysisId) => {
    try {
      return await api.delete(`/analyses/${analysisId}`);
    } catch (error) {
      console.error('Delete analysis API error:', error);
      throw error;
    }
  },
  
  /**
   * Get analysis components summary
   * @param {string} analysisId - Analysis ID
   * @returns {Promise<Object>} Analysis components summary
   */
  getAnalysisSummary: async (analysisId) => {
    try {
      return await api.get(`/analyses/${analysisId}/summary`);
    } catch (error) {
      console.error('Get analysis summary API error:', error);
      throw error;
    }
  },
  
  /**
   * Export analysis to PDF
   * @param {string} analysisId - Analysis ID
   * @returns {Promise<Blob>} PDF blob
   */
  exportAnalysisPDF: async (analysisId) => {
    try {
      return await api.download(`/analyses/${analysisId}/export/pdf`);
    } catch (error) {
      console.error('Export analysis PDF API error:', error);
      throw error;
    }
  },
  
  /**
   * Export analysis to Excel
   * @param {string} analysisId - Analysis ID
   * @returns {Promise<Blob>} Excel blob
   */
  exportAnalysisExcel: async (analysisId) => {
    try {
      return await api.download(`/analyses/${analysisId}/export/excel`);
    } catch (error) {
      console.error('Export analysis Excel API error:', error);
      throw error;
    }
  },
  
  /**
   * Clone an existing analysis
   * @param {string} analysisId - Analysis ID to clone
   * @param {Object} options - Cloning options (e.g., new name)
   * @returns {Promise<Object>} Cloned analysis
   */
  cloneAnalysis: async (analysisId, options = {}) => {
    try {
      return await api.post(`/analyses/${analysisId}/clone`, options);
    } catch (error) {
      console.error('Clone analysis API error:', error);
      throw error;
    }
  },
  
  /**
   * Get component history for an analysis
   * @param {string} analysisId - Analysis ID
   * @param {string} componentType - Component type (e.g., 'context', 'businessValues')
   * @returns {Promise<Object>} Component history
   */
  getComponentHistory: async (analysisId, componentType) => {
    try {
      return await api.get(`/analyses/${analysisId}/components/${componentType}/history`);
    } catch (error) {
      console.error(`Get ${componentType} history API error:`, error);
      throw error;
    }
  },
  
  /**
   * Get specific version of a component
   * @param {string} analysisId - Analysis ID
   * @param {string} componentType - Component type
   * @param {number} version - Version number
   * @returns {Promise<Object>} Component version
   */
  getComponentVersion: async (analysisId, componentType, version) => {
    try {
      return await api.get(`/analyses/${analysisId}/components/${componentType}/versions/${version}`);
    } catch (error) {
      console.error(`Get ${componentType} version API error:`, error);
      throw error;
    }
  }
};

// Export the API client
export default analysisApi;