// src/components/Atelier 3/Atelier3.js
import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { AlertCircle, Users, CheckSquare } from 'lucide-react';
import Activite1 from './Activite1/Activite1';
import { useAnalysis } from '../../context/AnalysisContext';

const Atelier3 = () => {
  const [activeActivity, setActiveActivity] = useState('activite1');
  const { currentAnalysis } = useAnalysis();

  // Load data when component mounts
  useEffect(() => {
    const loadInitialData = async () => {
      if (currentAnalysis?.id) {
        try {
          console.log('Atelier3 - Component mounted, letting Activite1 handle data loading');
          // We'll let the Activite1 component handle the data loading directly
        } catch (error) {
          console.error('Error loading initial Atelier 3 data:', error);
        }
      }
    };

    loadInitialData();
  }, [currentAnalysis?.id]);

  // Helper function to get progress status for each activity
  const getProgressStatus = (activity) => {
    // This will be implemented later when we have progress tracking
    return 'pending';
  };

  // Helper function to get status icon based on progress
  const getStatusIcon = (status) => {
    switch (status) {
      case 'completed':
        return <span className="w-2 h-2 bg-green-500 rounded-full mr-2"></span>;
      case 'in-progress':
        return <span className="w-2 h-2 bg-yellow-500 rounded-full mr-2"></span>;
      case 'pending':
      default:
        return <span className="w-2 h-2 bg-gray-300 rounded-full mr-2"></span>;
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-100">
        <h1 className="text-2xl font-bold text-gray-800 mb-2">Atelier 3: Scénarios stratégiques</h1>
        <p className="text-gray-600 mb-4">
          Cartographier l'écosystème et élaborer des scénarios stratégiques pour l'analyse de risque.
        </p>

        {/* Activity Tabs */}
        <div className="border-b border-gray-200">
          <div className="flex space-x-1">
            <button
              className={`flex items-center px-4 py-2 rounded-md ${
                activeActivity === 'activite1'
                  ? 'bg-blue-50 text-blue-700 font-medium'
                  : 'text-gray-600 hover:bg-gray-50'
              }`}
              onClick={() => setActiveActivity('activite1')}
            >
              {getStatusIcon(getProgressStatus('activite1'))}
              <div className="ml-2 flex items-center">
                <Users size={16} className="mr-1.5" />
                <span>Cartographier l'écosystème</span>
              </div>
            </button>
            {/* Additional activities will be added here in the future */}
          </div>
        </div>

        {/* Activity Content */}
        <div>
          {activeActivity === 'activite1' && <Activite1 />}
          {/* Additional activities will be rendered here in the future */}
        </div>
      </div>
    </div>
  );
};

export default Atelier3;
