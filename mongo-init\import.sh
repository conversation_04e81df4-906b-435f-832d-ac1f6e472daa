#!/bin/bash
set -e

# Wait for MongoDB to start
sleep 5

# Import all JSON files
for file in /docker-entrypoint-initdb.d/data/*.json; do
  filename=$(basename -- "$file")
  collection="${filename%.*}"
  collection="${collection#ebiosrm.}"

  echo "Importing $filename to $collection"
  mongoimport --db ebiosrm --collection $collection --file $file --jsonArray --username root --password rootpassword --authenticationDatabase admin
done

echo "All data imported successfully"