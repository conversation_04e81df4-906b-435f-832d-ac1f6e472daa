[{"_id": {"$oid": "67e28f271ed3f1537e02fe36"}, "analysisId": {"$oid": "67e28e111ed3f1537e02fe15"}, "componentType": "context", "data": {"organizationName": "ACGTRY123456", "scope": "try11112222", "analysisDate": "2025-03-25", "participants": [{"id": 1, "name": "<PERSON>", "position": "CEO", "workshopRoles": {"Atelier 1": "R", "Atelier 2": "", "Atelier 3": "", "Atelier 4": "C", "Atelier 5": ""}}, {"id": 1742747216559, "name": "<PERSON>", "position": "RISK", "workshopRoles": {"Atelier 1": "", "Atelier 2": "R", "Atelier 3": "I", "Atelier 4": "R", "Atelier 5": ""}}, {"id": 1742900982733, "name": "<PERSON><PERSON>", "position": "COMM", "workshopRoles": {"Atelier 1": "R", "Atelier 2": "C", "Atelier 3": "R", "Atelier 4": "", "Atelier 5": ""}}, {"id": 1743644837080, "name": "IHEB", "position": "OTHER", "customPosition": "ECHEF", "workshopRoles": {"Atelier 1": "R", "Atelier 2": "R", "Atelier 3": "C", "Atelier 4": "", "Atelier 5": ""}}], "analysisId": "67e28e111ed3f1537e02fe15"}, "version": 9, "createdBy": {"$oid": "67e02839208a45ab0a3168b5"}, "updatedBy": {"$oid": "67e02839208a45ab0a3168b5"}, "createdAt": {"$date": "2025-03-25T11:10:31.589Z"}, "updatedAt": {"$date": "2025-04-03T01:48:11.373Z"}, "__v": 0}, {"_id": {"$oid": "67e290971ed3f1537e02fe3f"}, "analysisId": {"$oid": "67e28e111ed3f1537e02fe15"}, "componentType": "business-values", "data": {"businessValues": [{"id": 1742901375427, "name": "VM11115", "shortId": "VM1", "supportAssets": [{"id": "support-1742901386671", "name": "BSSSS111", "shortId": "BS1-1"}], "securityPillars": ["confidentialite", "integrite", "auditabilite"]}, {"id": 1742907380801, "name": "etheeeeeNI", "shortId": "VM2", "supportAssets": [{"id": "support-1742907397535", "name": "a77", "shortId": "BS2-1"}, {"id": "support-1742907400266", "name": "nin", "shortId": "BS2-2"}], "securityPillars": ["confidentialite", "integrite", "tracabilite"]}, {"id": 1743038319915, "name": "thaleth", "shortId": "VM3", "supportAssets": [{"id": "support-1743117215303", "name": "ta thaleth", "shortId": "BS3-1"}], "securityPillars": ["disponibilite"]}], "analysisId": "67e28e111ed3f1537e02fe15"}, "version": 9, "createdBy": {"$oid": "67e02839208a45ab0a3168b5"}, "updatedBy": {"$oid": "67e02839208a45ab0a3168b5"}, "createdAt": {"$date": "2025-03-25T11:16:39.103Z"}, "updatedAt": {"$date": "2025-03-27T23:13:38.017Z"}, "__v": 0}, {"_id": {"$oid": "67e2a85f0f4fd35116a26e8a"}, "analysisId": {"$oid": "67e28e111ed3f1537e02fe15"}, "componentType": "dreaded-events", "__v": 0, "createdAt": {"$date": "2025-03-25T12:58:07.175Z"}, "createdBy": {"$oid": "67e02839208a45ab0a3168b5"}, "data": {"dreadedEvents": [{"id": 1743621865886, "name": "Corruption de base de données", "description": "Détérioration de l'intégrité des données stockées", "securityPillar": "integrite", "severity": "critical", "businessValue": "1742907380801"}, {"id": 1743621854201, "name": "Indisponibilité du service", "description": "Interruption du service empêchant son utilisation normale", "securityPillar": "disponibilite", "severity": "major", "businessValue": "1743038319915"}, {"id": 1743621804697, "name": "Fuite de donn<PERSON>", "description": "Divulgation non autorisée des données sensibles à des tiers", "securityPillar": "confidentialite", "severity": "major", "businessValue": "1742901375427"}, {"id": 1743621827021, "name": "Fuite de donn<PERSON>", "description": "Divulgation non autorisée des données sensibles à des tiers", "securityPillar": "confidentialite", "severity": "major", "businessValue": "1742901375427"}, {"id": 1743621827022, "name": "Vol de données par intrusion", "description": "Accès non autorisé aux systèmes et exfiltration de données", "securityPillar": "confidentialite", "severity": "critical", "businessValue": "1742901375427"}, {"id": 1743621796630, "name": "Injection de fausses donn<PERSON>", "description": "Introduction volontaire de données erronées dans le système", "securityPillar": "integrite", "severity": "major", "businessValue": "1742901375427"}, {"id": 1743621792756, "name": "Impossibilité d'audit", "description": "Absence de mécanismes permettant l'audit du système", "securityPillar": "auditabilite", "severity": "major", "businessValue": "1742901375427"}]}, "updatedAt": {"$date": "2025-04-02T19:24:35.714Z"}, "updatedBy": {"$oid": "67e02839208a45ab0a3168b5"}, "version": 7}, {"_id": {"$oid": "67e5a0a00f4fd35116a398a4"}, "analysisId": {"$oid": "67e28e111ed3f1537e02fe15"}, "componentType": "security-framework", "__v": 0, "createdAt": {"$date": "2025-03-27T19:01:52.307Z"}, "createdBy": {"$oid": "67e02839208a45ab0a3168b5"}, "data": {"selectedRules": {"iso27001": [{"id": "iso-1", "name": "A.5.1 Politique de sécurité de l'information", "status": "applied", "justification": "J2"}, {"id": "iso-2", "name": "A.6.1 Organisation interne", "status": "partially-applied", "justification": "J3"}], "nist": [{"id": "nist-1", "name": "ID.AM-1: Inventaire des périphériques physiques", "status": "applied", "justification": "nbbrraa"}], "custom-1743728091823-ry590": [{"id": "custom-ixj<PERSON><PERSON><PERSON><PERSON>", "name": "CIS Safeguard 8,8: Collect Command-Line Audit Logs", "status": "applied", "justification": ""}, {"id": "custom-5uq7g0pqp", "name": "CIS Safeguard 8,9: Centralize Audit Logs", "status": "applied", "justification": ""}, {"id": "custom-4815rnhua", "name": "CIS Safeguard 8,10: <PERSON><PERSON>t Logs", "status": "applied", "justification": ""}, {"id": "custom-ty4li9ope", "name": "CIS Safeguard 8,11: Conduct Audit Log Reviews", "status": "applied", "justification": ""}]}, "ruleBusinessValues": {"custom-ty4li9ope": ["1742901375427"], "custom-5uq7g0pqp": ["1742901375427"]}, "ruleDreadedEvents": {"custom-ty4li9ope": [1743621792756], "custom-5uq7g0pqp": [1743621792756]}, "summary": [{"id": "iso-1", "name": "A.5.1 Politique de sécurité de l'information", "status": "applied", "justification": "J2", "frameworkName": "iso27001", "frameworkId": "iso27001", "dreadedEvents": []}, {"id": "iso-2", "name": "A.6.1 Organisation interne", "status": "partially-applied", "justification": "J3", "frameworkName": "iso27001", "frameworkId": "iso27001", "dreadedEvents": []}, {"id": "nist-1", "name": "ID.AM-1: Inventaire des périphériques physiques", "status": "applied", "justification": "nbbrraa", "frameworkName": "nist", "frameworkId": "nist", "dreadedEvents": []}, {"id": "custom-ixj<PERSON><PERSON><PERSON><PERSON>", "name": "CIS Safeguard 8,8: Collect Command-Line Audit Logs", "status": "applied", "justification": "", "frameworkName": "custom-1743728091823-ry590", "frameworkId": "custom-1743728091823-ry590", "dreadedEvents": []}, {"id": "custom-5uq7g0pqp", "name": "CIS Safeguard 8,9: Centralize Audit Logs", "status": "applied", "justification": "", "frameworkName": "custom-1743728091823-ry590", "frameworkId": "custom-1743728091823-ry590", "dreadedEvents": [{"id": 1743621792756, "name": "Impossibilité d'audit", "description": "Absence de mécanismes permettant l'audit du système", "securityPillar": "auditabilite", "severity": "major", "businessValue": "1742901375427", "linkedBusinessValue": {"id": 1742901375427, "name": "VM11115", "shortId": "VM1", "supportAssets": [{"id": "support-1742901386671", "name": "BSSSS111", "shortId": "BS1-1"}], "securityPillars": ["confidentialite", "integrite", "auditabilite"]}}]}, {"id": "custom-4815rnhua", "name": "CIS Safeguard 8,10: <PERSON><PERSON>t Logs", "status": "applied", "justification": "", "frameworkName": "custom-1743728091823-ry590", "frameworkId": "custom-1743728091823-ry590", "dreadedEvents": []}, {"id": "custom-ty4li9ope", "name": "CIS Safeguard 8,11: Conduct Audit Log Reviews", "status": "applied", "justification": "", "frameworkName": "custom-1743728091823-ry590", "frameworkId": "custom-1743728091823-ry590", "dreadedEvents": [{"id": 1743621792756, "name": "Impossibilité d'audit", "description": "Absence de mécanismes permettant l'audit du système", "securityPillar": "auditabilite", "severity": "major", "businessValue": "1742901375427", "linkedBusinessValue": {"id": 1742901375427, "name": "VM11115", "shortId": "VM1", "supportAssets": [{"id": "support-1742901386671", "name": "BSSSS111", "shortId": "BS1-1"}], "securityPillars": ["confidentialite", "integrite", "auditabilite"]}}]}]}, "updatedAt": {"$date": "2025-04-04T00:58:13.220Z"}, "updatedBy": {"$oid": "67e02839208a45ab0a3168b5"}, "version": 4}, {"_id": {"$oid": "67e890cbf9b4c33ec4fd3504"}, "analysisId": {"$oid": "67e4a998b8ec79118b11e01a"}, "componentType": "business-values", "__v": 0, "createdAt": {"$date": "2025-03-30T00:31:07.062Z"}, "createdBy": {"$oid": "67e02839208a45ab0a3168b5"}, "data": {"businessValues": [{"id": 1742901375427, "name": "VM11115", "shortId": "VM1", "supportAssets": [{"id": "support-1742901386671", "name": "BSSSS111", "shortId": "BS1-1"}], "securityPillars": ["confidentialite", "integrite", "auditabilite", "disponibilite"]}, {"id": 1742907380801, "name": "etheeeeeNI", "shortId": "VM2", "supportAssets": [{"id": "support-1742907397535", "name": "a77", "shortId": "BS2-1"}, {"id": "support-1742907400266", "name": "nin", "shortId": "BS2-2"}], "securityPillars": ["confidentialite", "integrite", "tracabilite"]}, {"id": 1743038319915, "name": "thaleth", "shortId": "VM3", "supportAssets": [{"id": "support-1743117215303", "name": "ta thaleth", "shortId": "BS3-1"}, {"id": "support-1743693419520", "name": "BIEN SUPPORT  2 ta thaLeth", "shortId": "BS3-2"}], "securityPillars": ["disponibilite", "confidentialite", "tracabilite"]}, {"id": 1743302217013, "name": "une nouvelle valeur métier métier et technique", "shortId": "VM5", "supportAssets": [{"id": "support-1743302244716", "name": "Biens supports associé a une nouvelle valeur métier", "shortId": "BS5-1"}], "securityPillars": ["confidentialite"]}]}, "updatedAt": {"$date": "2025-04-03T15:17:01.761Z"}, "updatedBy": {"$oid": "67e02839208a45ab0a3168b5"}, "version": 7}, {"_id": {"$oid": "67e89591f9b4c33ec4fd3672"}, "analysisId": {"$oid": "67e4a998b8ec79118b11e01a"}, "componentType": "dreaded-events", "__v": 0, "createdAt": {"$date": "2025-03-30T00:51:29.635Z"}, "createdBy": {"$oid": "67e02839208a45ab0a3168b5"}, "data": {"dreadedEvents": [{"id": 1743693452222, "name": "Vol de données par intrusion", "description": "Accès non autorisé aux systèmes et exfiltration de données", "securityPillar": "confidentialite", "severity": "critical", "businessValue": "1743038319915"}, {"id": 1743604490888, "name": "Attaque par déni de service", "description": "Saturation volontaire des ressources rendant le service inaccessible", "securityPillar": "disponibilite", "severity": "critical", "businessValue": "1743038319915"}, {"id": 1743604491654, "name": "Panne d'infrastructure", "description": "Défaillance technique des composants d'infrastructure", "securityPillar": "disponibilite", "severity": "moderate", "businessValue": "1743038319915"}, {"id": 1743693476617, "name": "Actions non attribuables", "description": "Impossibilité d'identifier l'auteur d'une action dans le système", "securityPillar": "tracabilite", "severity": "moderate", "businessValue": "1743038319915"}, {"id": 1743688238877, "name": "Nom de l'événement 1", "description": "Pour chaque valeur mé<PERSON>, identifiez les événements redoutés qui pourraient impacter chacun des piliers de sécurité associés et évaluez leur gravité.\n\n", "securityPillar": "confidentialite", "severity": "catastrophic", "businessValue": "1743302217013"}, {"id": 1743630980686, "name": "Panne d'infrastructure", "description": "Défaillance technique des composants d'infrastructure", "securityPillar": "disponibilite", "severity": "moderate", "businessValue": "1742901375427"}]}, "updatedAt": {"$date": "2025-04-03T15:17:58.046Z"}, "updatedBy": {"$oid": "67e02839208a45ab0a3168b5"}, "version": 12}, {"_id": {"$oid": "67e896adf9b4c33ec4fd37b3"}, "analysisId": {"$oid": "67e4a998b8ec79118b11e01a"}, "componentType": "context", "__v": 0, "createdAt": {"$date": "2025-03-30T00:56:13.006Z"}, "createdBy": {"$oid": "67e4a836b8ec79118b11dea3"}, "data": {"organizationName": "analyse ", "scope": " analysis data,  analysis data,  analysis data,  analysis data,  analysis data,  analysis data,  analysis data,  analysis data,  analysis data,  analysis data, \n\n\n", "analysisDate": "2025-03-01", "participants": [{"id": 1, "name": "<PERSON>", "position": "CEO", "customPosition": "", "workshopRoles": {"Atelier 1": "R", "Atelier 2": "-", "Atelier 3": "", "Atelier 4": "", "Atelier 5": ""}}, {"id": 1742747216559, "name": "<PERSON>", "position": "CTO", "customPosition": "", "workshopRoles": {"Atelier 1": "", "Atelier 2": "R", "Atelier 3": "", "Atelier 4": "", "Atelier 5": "I"}}, {"id": 1742900982733, "name": "<PERSON><PERSON>", "position": "CONS", "customPosition": "", "workshopRoles": {"Atelier 1": "", "Atelier 2": "", "Atelier 3": "", "Atelier 4": "", "Atelier 5": ""}}], "analysisId": "67e4a998b8ec79118b11e01a"}, "updatedAt": {"$date": "2025-04-02T00:17:37.154Z"}, "updatedBy": {"$oid": "67e4a836b8ec79118b11dea3"}, "version": 9}, {"_id": {"$oid": "67e89e8bf9b4c33ec4fd3ba4"}, "analysisId": {"$oid": "67e4a998b8ec79118b11e01a"}, "componentType": "security-framework", "__v": 0, "createdAt": {"$date": "2025-03-30T01:29:47.185Z"}, "createdBy": {"$oid": "67e02839208a45ab0a3168b5"}, "data": {"selectedRules": {"custom-projx": [{"id": "projx-rule-2", "name": "Chiffrement données sensibles", "status": "partially-applied", "justification": "abcdef"}, {"id": "projx-rule-1", "name": "Accès spécifique API partenaires", "status": "partially-applied", "justification": ""}], "nist-csf": [{"id": "nist-pr.at-1", "name": "PR.AT-1: Security awareness training", "status": "applied", "justification": "Totallement appliqué"}], "67ef2e2f8fc56749148e9345": [{"id": "custom-fyf1s4xyb", "name": "CIS Control 1: Inventory and Control of Enterprise Assets", "status": "partially-applied", "justification": ""}]}, "ruleBusinessValues": {"projx-rule-2": [1743038319915], "nist-pr.at-1": ["1743302217013"], "projx-rule-1": ["1743038319915"], "custom-fyf1s4xyb": ["1742901375427"]}, "ruleDreadedEvents": {"projx-rule-2": [1743594810701, 1743604490888, 1743604491654], "nist-pr.at-1": [1743688238877], "projx-rule-1": [1743604490888], "custom-fyf1s4xyb": [1743630980686]}, "summary": [{"id": "projx-rule-2", "name": "Chiffrement données sensibles", "status": "partially-applied", "justification": "abcdef", "frameworkName": "custom-projx", "frameworkId": "custom-projx", "dreadedEvents": [{"id": 1743604490888, "name": "Attaque par déni de service", "description": "Saturation volontaire des ressources rendant le service inaccessible", "securityPillar": "disponibilite", "severity": "critical", "businessValue": "1743038319915", "linkedBusinessValue": {"id": 1743038319915, "name": "thaleth", "shortId": "VM3", "supportAssets": [{"id": "support-1743117215303", "name": "ta thaleth", "shortId": "BS3-1"}, {"id": "support-1743693419520", "name": "BIEN SUPPORT  2 ta thaLeth", "shortId": "BS3-2"}], "securityPillars": ["disponibilite", "confidentialite", "tracabilite"]}}, {"id": 1743604491654, "name": "Panne d'infrastructure", "description": "Défaillance technique des composants d'infrastructure", "securityPillar": "disponibilite", "severity": "moderate", "businessValue": "1743038319915", "linkedBusinessValue": {"id": 1743038319915, "name": "thaleth", "shortId": "VM3", "supportAssets": [{"id": "support-1743117215303", "name": "ta thaleth", "shortId": "BS3-1"}, {"id": "support-1743693419520", "name": "BIEN SUPPORT  2 ta thaLeth", "shortId": "BS3-2"}], "securityPillars": ["disponibilite", "confidentialite", "tracabilite"]}}]}, {"id": "projx-rule-1", "name": "Accès spécifique API partenaires", "status": "partially-applied", "justification": "", "frameworkName": "custom-projx", "frameworkId": "custom-projx", "dreadedEvents": [{"id": 1743604490888, "name": "Attaque par déni de service", "description": "Saturation volontaire des ressources rendant le service inaccessible", "securityPillar": "disponibilite", "severity": "critical", "businessValue": "1743038319915", "linkedBusinessValue": {"id": 1743038319915, "name": "thaleth", "shortId": "VM3", "supportAssets": [{"id": "support-1743117215303", "name": "ta thaleth", "shortId": "BS3-1"}, {"id": "support-1743693419520", "name": "BIEN SUPPORT  2 ta thaLeth", "shortId": "BS3-2"}], "securityPillars": ["disponibilite", "confidentialite", "tracabilite"]}}]}, {"id": "nist-pr.at-1", "name": "PR.AT-1: Security awareness training", "status": "applied", "justification": "Totallement appliqué", "frameworkName": "nist-csf", "frameworkId": "nist-csf", "dreadedEvents": [{"id": 1743688238877, "name": "Nom de l'événement 1", "description": "Pour chaque valeur mé<PERSON>, identifiez les événements redoutés qui pourraient impacter chacun des piliers de sécurité associés et évaluez leur gravité.\n\n", "securityPillar": "confidentialite", "severity": "catastrophic", "businessValue": "1743302217013", "linkedBusinessValue": {"id": 1743302217013, "name": "une nouvelle valeur métier métier et technique", "shortId": "VM5", "supportAssets": [{"id": "support-1743302244716", "name": "Biens supports associé a une nouvelle valeur métier", "shortId": "BS5-1"}], "securityPillars": ["confidentialite"]}}]}, {"id": "custom-fyf1s4xyb", "name": "CIS Control 1: Inventory and Control of Enterprise Assets", "status": "partially-applied", "justification": "", "frameworkName": "CIS Controls V8", "frameworkId": "67ef2e2f8fc56749148e9345", "dreadedEvents": [{"id": 1743630980686, "name": "Panne d'infrastructure", "description": "Défaillance technique des composants d'infrastructure", "securityPillar": "disponibilite", "severity": "moderate", "businessValue": "1742901375427", "linkedBusinessValue": {"id": 1742901375427, "name": "VM11115", "shortId": "VM1", "supportAssets": [{"id": "support-1742901386671", "name": "BSSSS111", "shortId": "BS1-1"}], "securityPillars": ["confidentialite", "integrite", "auditabilite", "disponibilite"]}}]}]}, "updatedAt": {"$date": "2025-04-04T01:04:10.553Z"}, "updatedBy": {"$oid": "67e02839208a45ab0a3168b5"}, "version": 23}, {"_id": {"$oid": "67ef32ed1c9cf3b70eda2351"}, "analysisId": {"$oid": "67ef307f8fc56749148e9ad6"}, "componentType": "business-values", "__v": 0, "createdAt": {"$date": "2025-04-04T01:16:29.285Z"}, "createdBy": {"$oid": "67e4a836b8ec79118b11dea3"}, "data": {"businessValues": [{"id": 1743729358922, "name": "valeur metier 1 test 3", "shortId": "VM1", "supportAssets": [{"id": "support-1743729377248", "name": "bien support 1 test 3", "shortId": "BS1-1"}, {"id": "support-1743729386928", "name": "bien support 2 test 3", "shortId": "BS1-2"}], "securityPillars": ["preuve", "tracabilite", "auditabilite"]}]}, "updatedAt": {"$date": "2025-04-04T01:16:29.285Z"}, "updatedBy": {"$oid": "67e4a836b8ec79118b11dea3"}, "version": 1}, {"_id": {"$oid": "67ef33271c9cf3b70eda2372"}, "analysisId": {"$oid": "67ef307f8fc56749148e9ad6"}, "componentType": "context", "__v": 0, "createdAt": {"$date": "2025-04-04T01:17:27.726Z"}, "createdBy": {"$oid": "67e4a836b8ec79118b11dea3"}, "data": {"organizationName": "ACGNY", "scope": "Analyse de testing num 3 ", "analysisDate": "2025-04-04", "participants": [{"id": 1, "name": "<PERSON><PERSON>", "position": "RSSI", "workshopRoles": {"Atelier 1": "R", "Atelier 2": "R", "Atelier 3": "A", "Atelier 4": "A", "Atelier 5": "I"}}, {"id": 1743904309365, "name": "alex", "position": "COMM", "workshopRoles": {"Atelier 1": "I", "Atelier 2": "I", "Atelier 3": "I", "Atelier 4": "I", "Atelier 5": "I"}}, {"id": 1743904320769, "name": "sarah", "position": "CONS", "workshopRoles": {"Atelier 1": "C", "Atelier 2": "C", "Atelier 3": "C", "Atelier 4": "C", "Atelier 5": "C"}}], "analysisId": "67ef307f8fc56749148e9ad6"}, "updatedAt": {"$date": "2025-04-06T01:52:24.724Z"}, "updatedBy": {"$oid": "67e4a836b8ec79118b11dea3"}, "version": 2}, {"_id": {"$oid": "67ef33611c9cf3b70eda2395"}, "analysisId": {"$oid": "67ef307f8fc56749148e9ad6"}, "componentType": "dreaded-events", "__v": 0, "createdAt": {"$date": "2025-04-04T01:18:25.657Z"}, "createdBy": {"$oid": "67e4a836b8ec79118b11dea3"}, "data": {"dreadedEvents": [{"id": 1743729502461, "name": "Défaut de conservation", "description": "Non-respect des durées légales de conservation des preuves", "securityPillar": "preuve", "severity": "moderate", "businessValue": "1743729358922"}, {"id": 1743729502868, "name": "Falsification de preuve", "description": "Modification ou suppression d'éléments probants", "securityPillar": "preuve", "severity": "critical", "businessValue": "1743729358922"}]}, "updatedAt": {"$date": "2025-04-04T01:18:25.657Z"}, "updatedBy": {"$oid": "67e4a836b8ec79118b11dea3"}, "version": 1}, {"_id": {"$oid": "67ef33841c9cf3b70eda23b9"}, "analysisId": {"$oid": "67ef307f8fc56749148e9ad6"}, "componentType": "security-framework", "__v": 0, "createdAt": {"$date": "2025-04-04T01:19:00.955Z"}, "createdBy": {"$oid": "67e4a836b8ec79118b11dea3"}, "data": {"selectedRules": {"67ef2e2f8fc56749148e9345": [{"id": "custom-bcvcz7tr4", "name": "CIS Safeguard 1,1: Establish and Maintain Detailed Enterprise Asset Inventory", "status": "partially-applied", "justification": "la justification c'est la justification waw"}, {"id": "custom-g23shxos1", "name": "CIS Safeguard 1,2: Address Unauthorized Assets", "status": "partially-applied", "justification": ""}], "67ef3af78fc56749148ea598": [{"id": "custom-8oz6liwcd", "name": "PR.AT-01: Provide General Security Training", "status": "partially-applied", "justification": ""}, {"id": "custom-oo6au3jce", "name": "PR.AA-06: Manage Physical Access", "status": "applied", "justification": ""}]}, "ruleBusinessValues": {"custom-bcvcz7tr4": ["1743729358922"], "custom-g23shxos1": ["1743729358922"], "custom-8oz6liwcd": ["1743729358922"], "custom-oo6au3jce": ["1743729358922"]}, "ruleDreadedEvents": {"custom-bcvcz7tr4": [1743729502461], "custom-g23shxos1": [1743729502868], "custom-8oz6liwcd": [1743729502868], "custom-oo6au3jce": [1743729502461]}, "summary": [{"id": "custom-bcvcz7tr4", "name": "CIS Safeguard 1,1: Establish and Maintain Detailed Enterprise Asset Inventory", "status": "partially-applied", "justification": "la justification c'est la justification waw", "frameworkName": "CIS Controls V8", "frameworkId": "67ef2e2f8fc56749148e9345", "dreadedEvents": [{"id": 1743729502461, "name": "Défaut de conservation", "description": "Non-respect des durées légales de conservation des preuves", "securityPillar": "preuve", "severity": "moderate", "businessValue": "1743729358922", "linkedBusinessValue": {"id": 1743729358922, "name": "valeur metier 1 test 3", "shortId": "VM1", "supportAssets": [{"id": "support-1743729377248", "name": "bien support 1 test 3", "shortId": "BS1-1"}, {"id": "support-1743729386928", "name": "bien support 2 test 3", "shortId": "BS1-2"}], "securityPillars": ["preuve", "tracabilite", "auditabilite"]}}]}, {"id": "custom-g23shxos1", "name": "CIS Safeguard 1,2: Address Unauthorized Assets", "status": "partially-applied", "justification": "", "frameworkName": "CIS Controls V8", "frameworkId": "67ef2e2f8fc56749148e9345", "dreadedEvents": [{"id": 1743729502868, "name": "Falsification de preuve", "description": "Modification ou suppression d'éléments probants", "securityPillar": "preuve", "severity": "critical", "businessValue": "1743729358922", "linkedBusinessValue": {"id": 1743729358922, "name": "valeur metier 1 test 3", "shortId": "VM1", "supportAssets": [{"id": "support-1743729377248", "name": "bien support 1 test 3", "shortId": "BS1-1"}, {"id": "support-1743729386928", "name": "bien support 2 test 3", "shortId": "BS1-2"}], "securityPillars": ["preuve", "tracabilite", "auditabilite"]}}]}, {"id": "custom-8oz6liwcd", "name": "PR.AT-01: Provide General Security Training", "status": "partially-applied", "justification": "", "frameworkName": "NIST CSF 2.0", "frameworkId": "67ef3af78fc56749148ea598", "dreadedEvents": [{"id": 1743729502868, "name": "Falsification de preuve", "description": "Modification ou suppression d'éléments probants", "securityPillar": "preuve", "severity": "critical", "businessValue": "1743729358922", "linkedBusinessValue": {"id": 1743729358922, "name": "valeur metier 1 test 3", "shortId": "VM1", "supportAssets": [{"id": "support-1743729377248", "name": "bien support 1 test 3", "shortId": "BS1-1"}, {"id": "support-1743729386928", "name": "bien support 2 test 3", "shortId": "BS1-2"}], "securityPillars": ["preuve", "tracabilite", "auditabilite"]}}]}, {"id": "custom-oo6au3jce", "name": "PR.AA-06: Manage Physical Access", "status": "applied", "justification": "", "frameworkName": "NIST CSF 2.0", "frameworkId": "67ef3af78fc56749148ea598", "dreadedEvents": [{"id": 1743729502461, "name": "Défaut de conservation", "description": "Non-respect des durées légales de conservation des preuves", "securityPillar": "preuve", "severity": "moderate", "businessValue": "1743729358922", "linkedBusinessValue": {"id": 1743729358922, "name": "valeur metier 1 test 3", "shortId": "VM1", "supportAssets": [{"id": "support-1743729377248", "name": "bien support 1 test 3", "shortId": "BS1-1"}, {"id": "support-1743729386928", "name": "bien support 2 test 3", "shortId": "BS1-2"}], "securityPillars": ["preuve", "tracabilite", "auditabilite"]}}]}]}, "updatedAt": {"$date": "2025-04-05T02:37:55.477Z"}, "updatedBy": {"$oid": "67e4a836b8ec79118b11dea3"}, "version": 6}, {"_id": {"$oid": "67f2e3eb9767c60e9bdb6acc"}, "analysisId": {"$oid": "67f2e2a0a5ad300283807d49"}, "componentType": "context", "__v": 0, "createdAt": {"$date": "2025-04-06T20:28:27.750Z"}, "createdBy": {"$oid": "67e4a836b8ec79118b11dea3"}, "data": {"organizationName": "InnovateRetail Inc", "scope": "Assessment of the primary e-commerce platform <www.innovateretail.com>, including customer database, order processing system, and payment gateway integration.", "analysisDate": "2025-04-01", "participants": [{"id": 1, "name": "<PERSON>", "position": "CTO", "workshopRoles": {"Atelier 1": "R", "Atelier 2": "R", "Atelier 3": "R", "Atelier 4": "R", "Atelier 5": "R"}}, {"id": 1743971176347, "name": "<PERSON>", "position": "COMM", "workshopRoles": {"Atelier 1": "R", "Atelier 2": "A", "Atelier 3": "A", "Atelier 4": "I", "Atelier 5": "C"}}, {"id": 1743971176542, "name": "<PERSON><PERSON><PERSON>", "position": "DSI", "workshopRoles": {"Atelier 1": "R", "Atelier 2": "C", "Atelier 3": "I", "Atelier 4": "R", "Atelier 5": "C"}}, {"id": 1743971176759, "name": "<PERSON>", "position": "RSSI", "workshopRoles": {"Atelier 1": "C", "Atelier 2": "I", "Atelier 3": "R", "Atelier 4": "A", "Atelier 5": "R"}}, {"id": 1744486321498, "name": "<PERSON>", "position": "JURIST", "workshopRoles": {"Atelier 1": "C", "Atelier 2": "C", "Atelier 3": "C", "Atelier 4": "C", "Atelier 5": "C"}}], "analysisId": "67f2e2a0a5ad300283807d49"}, "updatedAt": {"$date": "2025-05-02T19:55:51.807Z"}, "updatedBy": {"$oid": "67e4a836b8ec79118b11dea3"}, "version": 13}, {"_id": {"$oid": "67f2e5539767c60e9bdb6b21"}, "analysisId": {"$oid": "67f2e2a0a5ad300283807d49"}, "componentType": "business-values", "__v": 0, "createdAt": {"$date": "2025-04-06T20:34:27.774Z"}, "createdBy": {"$oid": "67e4a836b8ec79118b11dea3"}, "data": {"businessValues": [{"id": 1743971400871, "name": "Customer Trust & Data Privacy", "shortId": "VM1", "supportAssets": [{"id": "support-1743971511128", "name": "Customer Database", "shortId": "BS1-1", "type": "logiciels", "owner": "Alex <PERSON>", "location": "serveur N11", "description": "test test Descrep de BS"}, {"id": "support-1743971523010", "name": "Payment Gateway Integration API Keys", "shortId": "BS1-2"}], "securityPillars": ["confidentialite", "integrite", "auditabilite", "tracabilite", "disponibilite", "preuve"]}, {"id": 1743971410153, "name": "Platform Availability & Performance", "shortId": "VM2", "supportAssets": [{"id": "support-1743971573409", "name": "E-commerce Web Servers", "shortId": "BS2-1", "owner": "DSI"}, {"id": "support-1743971581218", "name": "Order Management System (OMS)", "shortId": "BS2-2"}], "securityPillars": ["disponibilite", "auditabilite"]}, {"id": 1743971420282, "name": "Order & Payment Processing Integrity", "shortId": "VM3", "supportAssets": [{"id": "support-1743971608145", "name": "Order Management System (OMS)", "shortId": "BS3-1"}, {"id": "support-1743971617909", "name": "Payment Gateway Integration API Keys", "shortId": "BS3-2"}], "securityPillars": ["integrite", "disponibilite", "auditabilite"]}, {"id": 1743971433594, "name": "Brand Reputation & Image", "shortId": "VM4", "supportAssets": [{"id": "support-1743971651665", "name": "Customer Database", "shortId": "BS4-1"}, {"id": "support-1743971661004", "name": "E-commerce Web Servers", "shortId": "BS4-2"}], "securityPillars": ["integrite", "confidentialite", "disponibilite", "auditabilite"]}, {"id": 1744482109653, "name": "New Clients Integration", "description": "", "shortId": "VM5", "supportAssets": [{"id": "support-1744482155757-cb246409cf54e", "name": "Application ", "description": "", "type": "logiciels", "owner": "CTO Ahmed", "location": "Bureau 5 etage 2", "shortId": "BS5-1"}], "securityPillars": ["confidentialite", "integrite"]}]}, "updatedAt": {"$date": "2025-05-02T19:56:36.933Z"}, "updatedBy": {"$oid": "67e4a836b8ec79118b11dea3"}, "version": 9}, {"_id": {"$oid": "67f2fc5d9767c60e9bdb6d86"}, "analysisId": {"$oid": "67f2e2a0a5ad300283807d49"}, "componentType": "dreaded-events", "__v": 0, "createdAt": {"$date": "2025-04-06T22:12:45.782Z"}, "createdBy": {"$oid": "67e4a836b8ec79118b11dea3"}, "data": {"dreadedEvents": [{"id": 1744498112806, "name": "Actions non attribuables", "description": "Impossibilité d'identifier l'auteur d'une action dans le système", "securityPillar": "tracabilite", "severity": "moderate", "businessValue": "1743971400871"}, {"id": 1744916099189, "name": "Falsification de preuve", "description": "Modification ou suppression d'éléments probants", "securityPillar": "preuve", "severity": "critical", "businessValue": "1743971400871"}, {"id": 1744476313359, "name": "Attaque par déni de service", "description": "Saturation volontaire des ressources rendant le service inaccessible", "securityPillar": "disponibilite", "severity": "critical", "businessValue": "1743971410153"}, {"id": 1744925300411, "name": "Bug applicatif", "description": "Erreur empêchant l'utilisation.", "securityPillar": "disponibilite", "severity": "moderate", "businessValue": "1743971410153"}, {"id": 1745351902911, "name": "Bug applicatif", "description": "Erreur empêchant l'utilisation.", "securityPillar": "disponibilite", "severity": "moderate", "businessValue": "1743971433594", "impacts": ["missions_services"]}, {"id": 1745351903260, "name": "Crash matériel", "description": "Serveur grillé suite à défaut.", "securityPillar": "disponibilite", "severity": "catastrophic", "businessValue": "1743971433594"}, {"id": 1745351907073, "name": "Divulgation de données sensibles", "description": "Fuite accidentelle de données personnelles ou professionnelles.", "securityPillar": "confidentialite", "severity": "catastrophic", "businessValue": "1743971433594", "impact": "image_confiance"}, {"id": 1745351914514, "name": "<PERSON><PERSON><PERSON> co<PERSON><PERSON>", "description": "Transfert endommagé de fi<PERSON>.", "securityPillar": "integrite", "severity": "minor", "businessValue": "1743971433594", "impacts": ["missions_services"]}, {"id": 1745351914241, "name": "Injection code", "description": "Code malveillant ajouté à une application.", "securityPillar": "integrite", "severity": "catastrophic", "businessValue": "1743971433594", "impacts": ["financier", "humain_materiel_environnemental", "missions_services"]}, {"id": 1745351925080, "name": "Application bloquée", "description": "Logiciel essentiel inutilisable.", "securityPillar": "disponibilite", "severity": "moderate", "businessValue": "1743971433594"}, {"id": 1745351925118, "name": "Crash matériel", "description": "Serveur grillé suite à défaut.", "securityPillar": "disponibilite", "severity": "catastrophic", "businessValue": "1743971433594"}, {"id": 1745353317145, "name": "Non-conformité processus", "description": "Audit révè<PERSON> procé<PERSON> absente.", "securityPillar": "Auditabilite", "severity": "major", "businessValue": "1743971420282", "impacts": ["gouvernance"]}, {"id": 1745355323387, "name": "Absence indicateurs", "description": "Pas de KPIs disponibles.", "securityPillar": "Auditabilite", "severity": "moderate", "businessValue": "1743971410153"}, {"id": 1745355631711, "name": "Anomalies ignorées", "description": "Défauts non signalés.", "securityPillar": "Auditabilite", "severity": "moderate", "businessValue": "1743971410153"}, {"id": 1745355647245, "name": "Falsification des journaux d'audit", "description": "Modification frauduleuse des journaux d'audit, rendant impossible la vérification de l'intégrité des données et compromettant gravement l'auditabilité du système.", "securityPillar": "Auditabilite", "businessValue": "1743971410153", "severity": "critical", "isSuggestion": false, "isAiGenerated": true}, {"id": 1745355665149, "name": "Virus destructeur", "description": "Supprime les fichiers critiques.", "securityPillar": "disponibilite", "severity": "catastrophic", "businessValue": "1743971410153"}, {"id": 1745355665357, "name": "Surcharge disque", "description": "Plus d'espace pour stocker.", "securityPillar": "disponibilite", "severity": "catastrophic", "businessValue": "1743971410153"}, {"id": 1745355678739, "name": "Falsification rapport", "description": "Résultat d'audit falsifié.", "securityPillar": "integrite", "severity": "major", "businessValue": "1743971400871", "impacts": ["gouvernance"]}, {"id": 1745355691557, "name": "Modification frauduleuse de données clients", "description": "Des données clients sensibles (adresses, informations de paiement) sont altéré<PERSON>, impactant la confiance client et la confidentialité des données.", "securityPillar": "integrite", "businessValue": "1743971400871", "severity": "critical", "isSuggestion": false, "isAiGenerated": true}, {"id": 1745572106629, "name": "Espionnage industriel", "description": "Collecte illégale d'informations concurrentielles.", "securityPillar": "confidentialite", "severity": "minor", "businessValue": "1743971433594", "impacts": ["financier", "juridique"]}, {"id": 1745572126131, "name": "Vol malware", "description": "Un logiciel malveillant exporte les données.", "securityPillar": "confidentialite", "severity": "catastrophic", "businessValue": "1743971433594", "impacts": ["humain_materiel_environnemental", "financier", "juridique"]}, {"id": 1746216349990, "name": "Intégrité des données de commande compromise", "description": "Modification non détectée des données de commande dû à un manque de contrôle d'intégrité, impactant la performance et la fiabilité du système de traitement des commandes.", "securityPillar": "Auditabilite", "businessValue": "1743971410153", "severity": "moderate", "impacts": ["missions_services", "financier"], "isSuggestion": false, "isAiGenerated": true}]}, "updatedAt": {"$date": "2025-05-02T20:06:03.760Z"}, "updatedBy": {"$oid": "67e4a836b8ec79118b11dea3"}, "version": 32}, {"_id": {"$oid": "67f31f759767c60e9bdb7a22"}, "analysisId": {"$oid": "67f2e2a0a5ad300283807d49"}, "componentType": "security-framework", "__v": 0, "createdAt": {"$date": "2025-04-07T00:42:29.068Z"}, "createdBy": {"$oid": "67e4a836b8ec79118b11dea3"}, "data": {"selectedRules": {"67ef2e2f8fc56749148e9345": [{"id": "custom-kaczivfnw", "name": "CIS Safeguard 2,5: Allowlist Authorized Software", "description": "(Asset Type: Applications, Security Function: Protect) Use technical controls, such as application allowlisting, to ensure that only authorized software can execute or be accessed. Reassess bi-annually, or more frequently.", "status": "applied", "justification": ""}, {"id": "custom-75za880n4", "name": "CIS Safeguard 3,10: Encrypt Sensitive Data in Transit", "description": "(Asset Type: Data, Security Function: Protect) Encrypt sensitive data in transit. Example implementations can include: Transport Layer Security (TLS) and Open Secure Shell (OpenSSH).", "status": "partially-applied", "justification": ""}], "67ef3af78fc56749148ea598": [{"id": "custom-z2h4ebult", "name": "GV.SC-08: Include Suppliers in IR/Recovery", "description": "Relevant suppliers and other third parties are included in incident planning, response, and recovery activities", "status": "partially-applied", "justification": "lorem espsum..."}, {"id": "custom-45js3lhrr", "name": "GV.OV-01: Review Strategy Outcomes", "description": "Cybersecurity risk management strategy outcomes are reviewed to inform and adjust strategy and direction", "status": "applied", "justification": ""}, {"id": "custom-n88hy4592", "name": "GV.OV-02: Review/Adjust Strategy Coverage", "description": "The cybersecurity risk management strategy is reviewed and adjusted to ensure coverage of organizational requirements and risks", "status": "applied", "justification": ""}]}, "ruleBusinessValues": {"custom-z2h4ebult": ["1743971410153"], "custom-kaczivfnw": ["1743971400871", "1743971410153"], "custom-45js3lhrr": ["1743971410153", "1743971433594"], "custom-n88hy4592": ["1743971433594"], "custom-75za880n4": ["1743971400871"]}, "ruleDreadedEvents": {"custom-z2h4ebult": [1744328365350, 1744925300411], "custom-kaczivfnw": [1744498112806, 1746216349990], "custom-45js3lhrr": [1744476313359, 1745351903260], "custom-n88hy4592": [1745351927759, 1745351927933, 1745351925118, 1745351925080], "custom-75za880n4": [1745355691557, 1745355678739]}, "summary": [{"id": "custom-kaczivfnw", "name": "CIS Safeguard 2,5: Allowlist Authorized Software", "description": "(Asset Type: Applications, Security Function: Protect) Use technical controls, such as application allowlisting, to ensure that only authorized software can execute or be accessed. Reassess bi-annually, or more frequently.", "status": "applied", "justification": "", "frameworkName": "CIS Controls V8", "frameworkId": "67ef2e2f8fc56749148e9345", "dreadedEvents": [{"id": 1744498112806, "name": "Actions non attribuables", "description": "Impossibilité d'identifier l'auteur d'une action dans le système", "securityPillar": "tracabilite", "severity": "moderate", "businessValue": "1743971400871", "linkedBusinessValue": {"id": 1743971400871, "name": "Customer Trust & Data Privacy", "shortId": "VM1", "supportAssets": [{"id": "support-1743971511128", "name": "Customer Database", "shortId": "BS1-1", "type": "logiciels", "owner": "Alex <PERSON>", "location": "serveur N11", "description": "test test Descrep de BS"}, {"id": "support-1743971523010", "name": "Payment Gateway Integration API Keys", "shortId": "BS1-2"}], "securityPillars": ["confidentialite", "integrite", "auditabilite", "tracabilite", "disponibilite", "preuve"]}}, {"id": 1746216349990, "name": "Intégrité des données de commande compromise", "description": "Modification non détectée des données de commande dû à un manque de contrôle d'intégrité, impactant la performance et la fiabilité du système de traitement des commandes.", "securityPillar": "Auditabilite", "businessValue": "1743971410153", "severity": "moderate", "impacts": ["missions_services", "financier"], "isSuggestion": false, "isAiGenerated": true, "linkedBusinessValue": {"id": 1743971410153, "name": "Platform Availability & Performance", "shortId": "VM2", "supportAssets": [{"id": "support-1743971573409", "name": "E-commerce Web Servers", "shortId": "BS2-1", "owner": "DSI"}, {"id": "support-1743971581218", "name": "Order Management System (OMS)", "shortId": "BS2-2"}], "securityPillars": ["disponibilite", "auditabilite"]}}]}, {"id": "custom-75za880n4", "name": "CIS Safeguard 3,10: Encrypt Sensitive Data in Transit", "description": "(Asset Type: Data, Security Function: Protect) Encrypt sensitive data in transit. Example implementations can include: Transport Layer Security (TLS) and Open Secure Shell (OpenSSH).", "status": "partially-applied", "justification": "", "frameworkName": "CIS Controls V8", "frameworkId": "67ef2e2f8fc56749148e9345", "dreadedEvents": [{"id": 1745355691557, "name": "Modification frauduleuse de données clients", "description": "Des données clients sensibles (adresses, informations de paiement) sont altéré<PERSON>, impactant la confiance client et la confidentialité des données.", "securityPillar": "integrite", "businessValue": "1743971400871", "severity": "critical", "isSuggestion": false, "isAiGenerated": true, "linkedBusinessValue": {"id": 1743971400871, "name": "Customer Trust & Data Privacy", "shortId": "VM1", "supportAssets": [{"id": "support-1743971511128", "name": "Customer Database", "shortId": "BS1-1", "type": "logiciels", "owner": "Alex <PERSON>", "location": "serveur N11", "description": "test test Descrep de BS"}, {"id": "support-1743971523010", "name": "Payment Gateway Integration API Keys", "shortId": "BS1-2"}], "securityPillars": ["confidentialite", "integrite", "auditabilite", "tracabilite", "disponibilite", "preuve"]}}, {"id": 1745355678739, "name": "Falsification rapport", "description": "Résultat d'audit falsifié.", "securityPillar": "integrite", "severity": "major", "businessValue": "1743971400871", "impacts": ["gouvernance"], "linkedBusinessValue": {"id": 1743971400871, "name": "Customer Trust & Data Privacy", "shortId": "VM1", "supportAssets": [{"id": "support-1743971511128", "name": "Customer Database", "shortId": "BS1-1", "type": "logiciels", "owner": "Alex <PERSON>", "location": "serveur N11", "description": "test test Descrep de BS"}, {"id": "support-1743971523010", "name": "Payment Gateway Integration API Keys", "shortId": "BS1-2"}], "securityPillars": ["confidentialite", "integrite", "auditabilite", "tracabilite", "disponibilite", "preuve"]}}]}, {"id": "custom-z2h4ebult", "name": "GV.SC-08: Include Suppliers in IR/Recovery", "description": "Relevant suppliers and other third parties are included in incident planning, response, and recovery activities", "status": "partially-applied", "justification": "lorem espsum...", "frameworkName": "NIST CSF 2.0", "frameworkId": "67ef3af78fc56749148ea598", "dreadedEvents": [{"id": 1744925300411, "name": "Bug applicatif", "description": "Erreur empêchant l'utilisation.", "securityPillar": "disponibilite", "severity": "moderate", "businessValue": "1743971410153", "linkedBusinessValue": {"id": 1743971410153, "name": "Platform Availability & Performance", "shortId": "VM2", "supportAssets": [{"id": "support-1743971573409", "name": "E-commerce Web Servers", "shortId": "BS2-1", "owner": "DSI"}, {"id": "support-1743971581218", "name": "Order Management System (OMS)", "shortId": "BS2-2"}], "securityPillars": ["disponibilite", "auditabilite"]}}]}, {"id": "custom-45js3lhrr", "name": "GV.OV-01: Review Strategy Outcomes", "description": "Cybersecurity risk management strategy outcomes are reviewed to inform and adjust strategy and direction", "status": "applied", "justification": "", "frameworkName": "NIST CSF 2.0", "frameworkId": "67ef3af78fc56749148ea598", "dreadedEvents": [{"id": 1744476313359, "name": "Attaque par déni de service", "description": "Saturation volontaire des ressources rendant le service inaccessible", "securityPillar": "disponibilite", "severity": "critical", "businessValue": "1743971410153", "linkedBusinessValue": {"id": 1743971410153, "name": "Platform Availability & Performance", "shortId": "VM2", "supportAssets": [{"id": "support-1743971573409", "name": "E-commerce Web Servers", "shortId": "BS2-1", "owner": "DSI"}, {"id": "support-1743971581218", "name": "Order Management System (OMS)", "shortId": "BS2-2"}], "securityPillars": ["disponibilite", "auditabilite"]}}, {"id": 1745351903260, "name": "Crash matériel", "description": "Serveur grillé suite à défaut.", "securityPillar": "disponibilite", "severity": "catastrophic", "businessValue": "1743971433594", "linkedBusinessValue": {"id": 1743971433594, "name": "Brand Reputation & Image", "shortId": "VM4", "supportAssets": [{"id": "support-1743971651665", "name": "Customer Database", "shortId": "BS4-1"}, {"id": "support-1743971661004", "name": "E-commerce Web Servers", "shortId": "BS4-2"}], "securityPillars": ["integrite", "confidentialite", "disponibilite", "auditabilite"]}}]}, {"id": "custom-n88hy4592", "name": "GV.OV-02: Review/Adjust Strategy Coverage", "description": "The cybersecurity risk management strategy is reviewed and adjusted to ensure coverage of organizational requirements and risks", "status": "applied", "justification": "", "frameworkName": "NIST CSF 2.0", "frameworkId": "67ef3af78fc56749148ea598", "dreadedEvents": [{"id": 1745351925118, "name": "Crash matériel", "description": "Serveur grillé suite à défaut.", "securityPillar": "disponibilite", "severity": "catastrophic", "businessValue": "1743971433594", "linkedBusinessValue": {"id": 1743971433594, "name": "Brand Reputation & Image", "shortId": "VM4", "supportAssets": [{"id": "support-1743971651665", "name": "Customer Database", "shortId": "BS4-1"}, {"id": "support-1743971661004", "name": "E-commerce Web Servers", "shortId": "BS4-2"}], "securityPillars": ["integrite", "confidentialite", "disponibilite", "auditabilite"]}}, {"id": 1745351925080, "name": "Application bloquée", "description": "Logiciel essentiel inutilisable.", "securityPillar": "disponibilite", "severity": "moderate", "businessValue": "1743971433594", "linkedBusinessValue": {"id": 1743971433594, "name": "Brand Reputation & Image", "shortId": "VM4", "supportAssets": [{"id": "support-1743971651665", "name": "Customer Database", "shortId": "BS4-1"}, {"id": "support-1743971661004", "name": "E-commerce Web Servers", "shortId": "BS4-2"}], "securityPillars": ["integrite", "confidentialite", "disponibilite", "auditabilite"]}}]}]}, "updatedAt": {"$date": "2025-05-02T20:06:21.898Z"}, "updatedBy": {"$oid": "67e4a836b8ec79118b11dea3"}, "version": 35}, {"_id": {"$oid": "680d1d64a97fb241cd3d7ceb"}, "analysisId": {"$oid": "67f2e2a0a5ad300283807d49"}, "componentType": "sources-risque", "data": {"sources": [{"id": "source-1746094140711-k8guvl7xq", "name": "Groupe cybercriminel organisé", "description": "Organisation criminelle spécialisée dans les attaques informatiques à but lucratif", "category": "criminel", "objectifVise": "Vol de données financières", "objectifViseCategory": "luc<PERSON><PERSON>", "motivation": "eleve", "activite": "eleve", "ressources": "importantes", "createdAt": "2025-05-01T10:09:00.711Z", "selected": true}, {"name": "Concurrent malveillant", "description": "Un concurrent cherchant à obtenir un avantage concurrentiel en accédant à des informations confidentielles ou en perturbant les opérations d'InnovateRetail.", "category": "malveillant", "objectifVise": "Vol de données clients ou dégradation de la réputation", "objectifViseCategory": "luc<PERSON><PERSON>", "motivation": "eleve", "activite": "moyen", "ressources": "moy<PERSON>s", "id": "source-1746094140711-g1ycd4hop", "createdAt": "2025-05-01T10:09:00.711Z", "selected": true}, {"name": "Insider malveillant", "description": "Un employé ou un contractuel ayant accès aux systèmes d'InnovateRetail et utilisant ses privilèges à des fins malveillantes (vol de données, espionnage industriel).", "category": "malveillant", "objectifVise": "Vol de données clients ou propriété intellectuelle", "objectifViseCategory": "luc<PERSON><PERSON>", "motivation": "moyen", "activite": "faible", "ressources": "faibles", "id": "source-1746094140711-4xxjg<PERSON>ff", "createdAt": "2025-05-01T10:09:00.711Z", "selected": false}, {"id": "source-1746094140711-hj1c1ggi2", "name": "Hacktiviste idéologique", "description": "Individu motivé par des convictions idéologiques", "category": "<PERSON>e", "objectifVise": "Perturbation des services en ligne", "objectifViseCategory": "entrave", "motivation": "eleve", "activite": "faible", "ressources": "faibles", "createdAt": "2025-05-01T10:09:00.711Z", "selected": true}, {"name": "<PERSON><PERSON><PERSON> kiddie", "description": "Un individu avec des compétences informatiques limitées, utilisant des outils et des scripts accessibles publiquement pour mener des attaques opportunistes contre des systèmes vulnérables.", "category": "amateur", "objectifVise": "Vandalisme numérique et perturbation des services", "objectifViseCategory": "defi", "motivation": "faible", "activite": "faible", "ressources": "faibles", "id": "source-1746097051035-btihv2h2d", "createdAt": "2025-05-01T10:57:31.035Z", "selected": true}]}, "version": 1, "createdBy": {"$oid": "67e4a836b8ec79118b11dea3"}, "updatedBy": {"$oid": "67e4a836b8ec79118b11dea3"}, "createdAt": {"$date": "2025-04-26T17:52:36.733Z"}, "updatedAt": {"$date": "2025-05-01T10:57:51.156Z"}, "__v": 0}, {"_id": {"$oid": "681218080e7924030d3d3c8c"}, "analysisId": {"$oid": "67e4a998b8ec79118b11e01a"}, "componentType": "sources-risque", "data": {"sources": [{"id": "source-1746016227009-7o24443ra", "name": "Hacktiviste idéologique", "description": "Individu motivé par des convictions idéologiques", "category": "<PERSON>e", "objectifVise": "Perturbation des services en ligne", "objectifViseCategory": "entrave", "motivation": "eleve", "activite": "faible", "ressources": "faibles", "createdAt": "2025-04-30T12:30:27.009Z"}]}, "version": 1, "createdBy": {"$oid": "67e4a836b8ec79118b11dea3"}, "updatedBy": {"$oid": "67e4a836b8ec79118b11dea3"}, "createdAt": {"$date": "2025-04-30T12:31:04.781Z"}, "updatedAt": {"$date": "2025-04-30T12:31:04.781Z"}, "__v": 0}, {"_id": {"$oid": "681247bbac1689d0936bf2b8"}, "analysisId": {"$oid": "67f2e2a0a5ad300283807d49"}, "componentType": "selected-couples-srov", "data": {"mappings": [{"id": "source-1745689377807-1745572106629-1746028440326", "sourceId": "source-1745689377807", "dreadedEventId": 1745572106629, "createdAt": "2025-04-30T15:54:00.326Z"}, {"id": "source-1745689377807-1745572126131-1746028441503", "sourceId": "source-1745689377807", "dreadedEventId": 1745572126131, "createdAt": "2025-04-30T15:54:01.503Z"}, {"id": "source-1745702627755-1745351907073-1746028464398", "sourceId": "source-1745702627755", "dreadedEventId": 1745351907073, "createdAt": "2025-04-30T15:54:24.398Z"}, {"id": "source-1745702627755-1744925300411-1746028466094", "sourceId": "source-1745702627755", "dreadedEventId": 1744925300411, "createdAt": "2025-04-30T15:54:26.094Z"}, {"id": "source-1745708254126-1745351907073-1746088347220", "sourceId": "source-1745708254126", "dreadedEventId": 1745351907073, "createdAt": "2025-05-01T08:32:27.220Z"}, {"id": "source-1745708254126-1745572106629-1746088348844", "sourceId": "source-1745708254126", "dreadedEventId": 1745572106629, "createdAt": "2025-05-01T08:32:28.844Z"}, {"id": "source-1746094140711-k8guvl7xq-1744498112806-1746096653045", "sourceId": "source-1746094140711-k8guvl7xq", "dreadedEventId": 1744498112806, "createdAt": "2025-05-01T10:50:53.045Z"}, {"id": "source-1746094140711-k8guvl7xq-1745351907073-1746096657450", "sourceId": "source-1746094140711-k8guvl7xq", "dreadedEventId": 1745351907073, "createdAt": "2025-05-01T10:50:57.450Z"}, {"id": "source-1746094140711-g1ycd4hop-1745351914241-1746096667315", "sourceId": "source-1746094140711-g1ycd4hop", "dreadedEventId": 1745351914241, "createdAt": "2025-05-01T10:51:07.315Z"}, {"id": "source-1746094140711-g1ycd4hop-1745351914514-1746096669251", "sourceId": "source-1746094140711-g1ycd4hop", "dreadedEventId": 1745351914514, "createdAt": "2025-05-01T10:51:09.251Z"}, {"id": "source-1746094140711-g1ycd4hop-1745355678739-1746096670828", "sourceId": "source-1746094140711-g1ycd4hop", "dreadedEventId": 1745355678739, "createdAt": "2025-05-01T10:51:10.828Z"}, {"id": "source-1746094140711-hj1c1ggi2-1745353317145-1746096678556", "sourceId": "source-1746094140711-hj1c1ggi2", "dreadedEventId": 1745353317145, "createdAt": "2025-05-01T10:51:18.556Z"}, {"id": "source-1746094140711-hj1c1ggi2-1744916099189-1746096681131", "sourceId": "source-1746094140711-hj1c1ggi2", "dreadedEventId": 1744916099189, "createdAt": "2025-05-01T10:51:21.131Z"}, {"id": "source-1746094140711-hj1c1ggi2-1745572106629-1746096683340", "sourceId": "source-1746094140711-hj1c1ggi2", "dreadedEventId": 1745572106629, "createdAt": "2025-05-01T10:51:23.340Z"}]}, "version": 1, "createdBy": {"$oid": "67e4a836b8ec79118b11dea3"}, "updatedBy": {"$oid": "67e4a836b8ec79118b11dea3"}, "createdAt": {"$date": "2025-04-30T15:54:35.440Z"}, "updatedAt": {"$date": "2025-05-01T10:56:03.599Z"}, "__v": 0}]