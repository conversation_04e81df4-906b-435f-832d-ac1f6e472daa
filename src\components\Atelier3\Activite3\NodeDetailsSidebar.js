// src/components/Atelier3/Activite3/NodeDetailsSidebar.js
import React, { useState, useEffect } from 'react';
import { X, Save, Plus, Trash2, Edit2 } from 'lucide-react';

const NodeDetailsSidebar = ({
  selectedNode,
  onClose,
  onUpdateNode,
  onAddNode,
  onDeleteNode,
  attackPath
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const [nodeData, setNodeData] = useState(selectedNode?.data || {});
  const [showAddForm, setShowAddForm] = useState(selectedNode === null);
  const [newNodeType, setNewNodeType] = useState('custom');
  const [newNodeData, setNewNodeData] = useState({
    label: '',
    description: '',
    nodeType: 'custom',
    color: '#6366f1', // Default indigo color
    inputs: 1,
    outputs: 1,
  });

  // Update nodeData when selectedNode changes
  useEffect(() => {
    if (selectedNode) {
      setNodeData(selectedNode.data || {});
      setShowAddForm(false);
      setIsEditing(false);
    }
  }, [selectedNode]);

  // Always show the sidebar, either with node details or add form
  if (!selectedNode && !showAddForm) {
    setShowAddForm(true);
  }

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setNodeData({
      ...nodeData,
      [name]: value
    });
  };

  const handleNewNodeInputChange = (e) => {
    const { name, value } = e.target;
    setNewNodeData({
      ...newNodeData,
      [name]: value
    });
  };

  const handleSave = () => {
    onUpdateNode(selectedNode.id, nodeData);
    setIsEditing(false);
  };

  const handleAddNode = () => {
    onAddNode(newNodeType, newNodeData);
    setShowAddForm(false);
    // Reset form with default values
    setNewNodeData({
      label: '',
      description: '',
      nodeType: 'custom',
      color: '#6366f1',
      inputs: 1,
      outputs: 1,
    });
    setNewNodeType('custom');
  };

  const renderNodeTypeDetails = () => {
    if (showAddForm) {
      return (
        <div className="p-4">
          <h3 className="text-lg font-semibold text-slate-800 mb-4">Ajouter un nouveau nœud</h3>

          <div className="mb-4">
            <label className="block text-sm font-medium text-slate-700 mb-1">Type de nœud</label>
            <select
              className="w-full p-2 border border-slate-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
              value={newNodeType}
              onChange={(e) => {
                setNewNodeType(e.target.value);
                setNewNodeData(prev => ({
                  ...prev,
                  nodeType: e.target.value
                }));
              }}
            >
              <option value="sourceRisk">Source de Risque</option>
              <option value="stakeholder">Partie Prenante</option>
              <option value="businessValue">Objectif Visé</option>
              <option value="businessValueStandalone">Valeur Métier</option>
              <option value="custom">Autre (personnalisé)</option>
            </select>
          </div>

          <div className="mb-4">
            <label className="block text-sm font-medium text-slate-700 mb-1">Nom *</label>
            <input
              type="text"
              name="label"
              value={newNodeData.label}
              onChange={handleNewNodeInputChange}
              className="w-full p-2 border border-slate-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
              placeholder="Entrez le nom du nœud"
              autoFocus
              onFocus={(e) => e.target.select()}
            />
          </div>

          <div className="mb-4">
            <label className="block text-sm font-medium text-slate-700 mb-1">Description</label>
            <textarea
              name="description"
              value={newNodeData.description}
              onChange={handleNewNodeInputChange}
              className="w-full p-2 border border-slate-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
              rows="3"
              placeholder="Entrez une description pour ce nœud (optionnel)"
            ></textarea>
          </div>

          {newNodeType === 'custom' && (
            <>
              <div className="mb-4">
                <label className="block text-sm font-medium text-slate-700 mb-1">Couleur</label>
                <div className="flex items-center space-x-2 mb-2">
                  <input
                    type="color"
                    name="color"
                    value={newNodeData.color || '#6366f1'}
                    onChange={handleNewNodeInputChange}
                    className="p-1 border border-slate-300 rounded-md h-10 w-10"
                  />
                  <input
                    type="text"
                    name="color"
                    value={newNodeData.color || '#6366f1'}
                    onChange={handleNewNodeInputChange}
                    className="w-full p-2 border border-slate-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                    placeholder="Code couleur (ex: #6366f1)"
                  />
                </div>
                {/* Couleurs prédéfinies */}
                <div className="flex space-x-2">
                  {['#6366f1', '#8b5cf6', '#06b6d4', '#10b981', '#f59e0b', '#ef4444', '#6b7280'].map(color => (
                    <button
                      key={color}
                      type="button"
                      onClick={() => setNewNodeData(prev => ({ ...prev, color }))}
                      className={`w-6 h-6 rounded border-2 ${newNodeData.color === color ? 'border-slate-800' : 'border-slate-300'}`}
                      style={{ backgroundColor: color }}
                      title={color}
                    />
                  ))}
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4 mb-4">
                <div>
                  <label className="block text-sm font-medium text-slate-700 mb-1">Nombre d'entrées</label>
                  <select
                    name="inputs"
                    value={newNodeData.inputs || 1}
                    onChange={handleNewNodeInputChange}
                    className="w-full p-2 border border-slate-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value={0}>0</option>
                    <option value={1}>1</option>
                    <option value={2}>2</option>
                    <option value={3}>3</option>
                    <option value={4}>4</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-slate-700 mb-1">Nombre de sorties</label>
                  <select
                    name="outputs"
                    value={newNodeData.outputs || 1}
                    onChange={handleNewNodeInputChange}
                    className="w-full p-2 border border-slate-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value={0}>0</option>
                    <option value={1}>1</option>
                    <option value={2}>2</option>
                    <option value={3}>3</option>
                    <option value={4}>4</option>
                  </select>
                </div>
              </div>
            </>
          )}

          <div className="flex justify-end mt-6">
            <button
              onClick={handleAddNode}
              disabled={!newNodeData.label}
              className={`px-4 py-2 rounded-lg text-white flex items-center ${!newNodeData.label ? 'bg-blue-400 cursor-not-allowed' : 'bg-blue-600 hover:bg-blue-700'}`}
            >
              <Plus size={16} className="mr-2" />
              Ajouter
            </button>
          </div>
        </div>
      );
    }

    if (!selectedNode) return null;

    switch (selectedNode.type) {
      case 'sourceRisk':
        return (
          <div className="p-4">
            <div className="flex items-center mb-4">
              <div className="w-3 h-3 bg-red-500 rounded-full mr-2"></div>
              <h3 className="text-lg font-semibold text-slate-800">Source de Risque</h3>
            </div>

            {isEditing ? (
              <>
                <div className="mb-4">
                  <label className="block text-sm font-medium text-slate-700 mb-1">Nom</label>
                  <input
                    type="text"
                    name="label"
                    value={nodeData.label}
                    onChange={handleInputChange}
                    className="w-full p-2 border border-slate-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>

                <div className="mb-4">
                  <label className="block text-sm font-medium text-slate-700 mb-1">Description</label>
                  <textarea
                    name="description"
                    value={nodeData.description || ''}
                    onChange={handleInputChange}
                    className="w-full p-2 border border-slate-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                    rows="3"
                  ></textarea>
                </div>

                <div className="mb-4">
                  <label className="block text-sm font-medium text-slate-700 mb-1">Niveau de risque</label>
                  <select
                    name="riskLevel"
                    value={nodeData.riskLevel || 'medium'}
                    onChange={handleInputChange}
                    className="w-full p-2 border border-slate-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="high">Élevé</option>
                    <option value="medium">Moyen</option>
                    <option value="low">Faible</option>
                  </select>
                </div>
              </>
            ) : (
              <>
                <div className="mb-4">
                  <h4 className="text-sm font-medium text-slate-700 mb-1">Nom</h4>
                  <p className="text-slate-900">{nodeData.label}</p>
                </div>

                {nodeData.description && (
                  <div className="mb-4">
                    <h4 className="text-sm font-medium text-slate-700 mb-1">Description</h4>
                    <p className="text-slate-900">{nodeData.description}</p>
                  </div>
                )}

                {nodeData.riskLevel && (
                  <div className="mb-4">
                    <h4 className="text-sm font-medium text-slate-700 mb-1">Niveau de risque</h4>
                    <div className={`
                      inline-block px-2 py-1 rounded-full text-xs font-bold
                      ${nodeData.riskLevel === 'high' ? 'bg-red-100 text-red-800' :
                        nodeData.riskLevel === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                        'bg-green-100 text-green-800'}
                    `}>
                      {nodeData.riskLevel === 'high' ? 'Élevé' :
                       nodeData.riskLevel === 'medium' ? 'Moyen' :
                       'Faible'}
                    </div>
                  </div>
                )}
              </>
            )}
          </div>
        );

      case 'dreadedEvent':
        return (
          <div className="p-4">
            <div className="flex items-center mb-4">
              <div className="w-3 h-3 bg-yellow-500 rounded-full mr-2"></div>
              <h3 className="text-lg font-semibold text-slate-800">Événement Redouté</h3>
            </div>

            {isEditing ? (
              <>
                <div className="mb-4">
                  <label className="block text-sm font-medium text-slate-700 mb-1">Nom</label>
                  <input
                    type="text"
                    name="label"
                    value={nodeData.label}
                    onChange={handleInputChange}
                    className="w-full p-2 border border-slate-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>

                <div className="mb-4">
                  <label className="block text-sm font-medium text-slate-700 mb-1">Description</label>
                  <textarea
                    name="description"
                    value={nodeData.description || ''}
                    onChange={handleInputChange}
                    className="w-full p-2 border border-slate-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                    rows="3"
                  ></textarea>
                </div>

                <div className="mb-4">
                  <label className="block text-sm font-medium text-slate-700 mb-1">Sévérité</label>
                  <select
                    name="severity"
                    value={nodeData.severity || 'moderate'}
                    onChange={handleInputChange}
                    className="w-full p-2 border border-slate-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="critical">Critique</option>
                    <option value="major">Majeur</option>
                    <option value="moderate">Modéré</option>
                    <option value="minor">Mineur</option>
                  </select>
                </div>
              </>
            ) : (
              <>
                <div className="mb-4">
                  <h4 className="text-sm font-medium text-slate-700 mb-1">Nom</h4>
                  <p className="text-slate-900">{nodeData.label}</p>
                </div>

                {nodeData.description && (
                  <div className="mb-4">
                    <h4 className="text-sm font-medium text-slate-700 mb-1">Description</h4>
                    <p className="text-slate-900">{nodeData.description}</p>
                  </div>
                )}

                {nodeData.severity && (
                  <div className="mb-4">
                    <h4 className="text-sm font-medium text-slate-700 mb-1">Sévérité</h4>
                    <div className={`
                      inline-block px-2 py-1 rounded-full text-xs font-bold
                      ${nodeData.severity === 'critical' ? 'bg-red-100 text-red-800' :
                        nodeData.severity === 'major' ? 'bg-orange-100 text-orange-800' :
                        nodeData.severity === 'moderate' ? 'bg-yellow-100 text-yellow-800' :
                        'bg-green-100 text-green-800'}
                    `}>
                      {nodeData.severity === 'critical' ? 'Critique' :
                       nodeData.severity === 'major' ? 'Majeur' :
                       nodeData.severity === 'moderate' ? 'Modéré' :
                       'Mineur'}
                    </div>
                  </div>
                )}
              </>
            )}
          </div>
        );

      case 'businessValue':
        return (
          <div className="p-4">
            <div className="flex items-center mb-4">
              <div className="w-3 h-3 bg-green-500 rounded-full mr-2"></div>
              <h3 className="text-lg font-semibold text-slate-800">Valeur Métier</h3>
            </div>

            {isEditing ? (
              <>
                <div className="mb-4">
                  <label className="block text-sm font-medium text-slate-700 mb-1">Nom</label>
                  <input
                    type="text"
                    name="label"
                    value={nodeData.label}
                    onChange={handleInputChange}
                    className="w-full p-2 border border-slate-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>

                <div className="mb-4">
                  <label className="block text-sm font-medium text-slate-700 mb-1">Description</label>
                  <textarea
                    name="description"
                    value={nodeData.description || ''}
                    onChange={handleInputChange}
                    className="w-full p-2 border border-slate-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                    rows="3"
                  ></textarea>
                </div>
              </>
            ) : (
              <>
                <div className="mb-4">
                  <h4 className="text-sm font-medium text-slate-700 mb-1">Nom</h4>
                  <p className="text-slate-900">{nodeData.label}</p>
                </div>

                {nodeData.description && (
                  <div className="mb-4">
                    <h4 className="text-sm font-medium text-slate-700 mb-1">Description</h4>
                    <p className="text-slate-900">{nodeData.description}</p>
                  </div>
                )}
              </>
            )}
          </div>
        );

      case 'stakeholder':
        return (
          <div className="p-4">
            <div className="flex items-center mb-4">
              <div className="w-3 h-3 bg-blue-500 rounded-full mr-2"></div>
              <h3 className="text-lg font-semibold text-slate-800">Partie Prenante</h3>
            </div>

            {isEditing ? (
              <>
                <div className="mb-4">
                  <label className="block text-sm font-medium text-slate-700 mb-1">Nom</label>
                  <input
                    type="text"
                    name="label"
                    value={nodeData.label}
                    onChange={handleInputChange}
                    className="w-full p-2 border border-slate-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>

                <div className="mb-4">
                  <label className="block text-sm font-medium text-slate-700 mb-1">Description</label>
                  <textarea
                    name="description"
                    value={nodeData.description || ''}
                    onChange={handleInputChange}
                    className="w-full p-2 border border-slate-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                    rows="3"
                  ></textarea>
                </div>
              </>
            ) : (
              <>
                <div className="mb-4">
                  <h4 className="text-sm font-medium text-slate-700 mb-1">Nom</h4>
                  <p className="text-slate-900">{nodeData.label}</p>
                </div>

                {nodeData.description && (
                  <div className="mb-4">
                    <h4 className="text-sm font-medium text-slate-700 mb-1">Description</h4>
                    <p className="text-slate-900">{nodeData.description}</p>
                  </div>
                )}
              </>
            )}
          </div>
        );

      case 'custom':
        return (
          <div className="p-4">
            <div className="flex items-center mb-4">
              <div className="w-3 h-3 bg-purple-500 rounded-full mr-2"></div>
              <h3 className="text-lg font-semibold text-slate-800">Nœud Personnalisé</h3>
            </div>

            {isEditing ? (
              <>
                <div className="mb-4">
                  <label className="block text-sm font-medium text-slate-700 mb-1">Nom</label>
                  <input
                    type="text"
                    name="label"
                    value={nodeData.label}
                    onChange={handleInputChange}
                    className="w-full p-2 border border-slate-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>

                <div className="mb-4">
                  <label className="block text-sm font-medium text-slate-700 mb-1">Description</label>
                  <textarea
                    name="description"
                    value={nodeData.description || ''}
                    onChange={handleInputChange}
                    className="w-full p-2 border border-slate-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                    rows="3"
                  ></textarea>
                </div>

                <div className="mb-4">
                  <label className="block text-sm font-medium text-slate-700 mb-1">Couleur</label>
                  <div className="flex items-center space-x-2">
                    <input
                      type="color"
                      name="color"
                      value={nodeData.color || '#3b82f6'}
                      onChange={handleInputChange}
                      className="p-1 border border-slate-300 rounded-md h-10 w-10"
                    />
                    <input
                      type="text"
                      name="color"
                      value={nodeData.color || '#3b82f6'}
                      onChange={handleInputChange}
                      className="w-full p-2 border border-slate-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                      placeholder="Code couleur (ex: #3b82f6)"
                    />
                  </div>
                </div>
              </>
            ) : (
              <>
                <div className="mb-4">
                  <h4 className="text-sm font-medium text-slate-700 mb-1">Nom</h4>
                  <p className="text-slate-900">{nodeData.label}</p>
                </div>

                {nodeData.description && (
                  <div className="mb-4">
                    <h4 className="text-sm font-medium text-slate-700 mb-1">Description</h4>
                    <p className="text-slate-900">{nodeData.description}</p>
                  </div>
                )}

                {nodeData.color && (
                  <div className="mb-4">
                    <h4 className="text-sm font-medium text-slate-700 mb-1">Couleur</h4>
                    <div className="flex items-center space-x-2">
                      <div
                        className="w-6 h-6 rounded border border-slate-300"
                        style={{ backgroundColor: nodeData.color }}
                      ></div>
                      <span className="text-slate-900 text-sm">{nodeData.color}</span>
                    </div>
                  </div>
                )}
              </>
            )}
          </div>
        );

      default:
        return (
          <div className="p-4">
            <div className="flex items-center mb-4">
              <div className="w-3 h-3 bg-gray-500 rounded-full mr-2"></div>
              <h3 className="text-lg font-semibold text-slate-800">Nœud Inconnu</h3>
            </div>

            {isEditing ? (
              <>
                <div className="mb-4">
                  <label className="block text-sm font-medium text-slate-700 mb-1">Nom</label>
                  <input
                    type="text"
                    name="label"
                    value={nodeData.label}
                    onChange={handleInputChange}
                    className="w-full p-2 border border-slate-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>

                <div className="mb-4">
                  <label className="block text-sm font-medium text-slate-700 mb-1">Description</label>
                  <textarea
                    name="description"
                    value={nodeData.description || ''}
                    onChange={handleInputChange}
                    className="w-full p-2 border border-slate-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                    rows="3"
                  ></textarea>
                </div>
              </>
            ) : (
              <>
                <div className="mb-4">
                  <h4 className="text-sm font-medium text-slate-700 mb-1">Nom</h4>
                  <p className="text-slate-900">{nodeData.label}</p>
                </div>

                {nodeData.description && (
                  <div className="mb-4">
                    <h4 className="text-sm font-medium text-slate-700 mb-1">Description</h4>
                    <p className="text-slate-900">{nodeData.description}</p>
                  </div>
                )}

                <div className="mb-4">
                  <h4 className="text-sm font-medium text-slate-700 mb-1">Type</h4>
                  <p className="text-slate-900">{selectedNode.type}</p>
                </div>
              </>
            )}
          </div>
        );
    }
  };

  return (
    <div className="bg-white border-l border-slate-200 shadow-lg w-full md:w-80 h-full overflow-y-auto">
      <div className="p-4 border-b border-slate-200 flex justify-between items-center bg-slate-50">
        <h2 className="text-lg font-semibold text-slate-800">
          {showAddForm ? 'Ajouter un nœud' : 'Détails du nœud'}
        </h2>
        <div className="flex space-x-2">
          {!showAddForm && selectedNode && (
            <>
              {isEditing ? (
                <button
                  onClick={handleSave}
                  className="p-2 text-blue-600 hover:text-blue-800 hover:bg-blue-50 rounded-full transition-colors"
                  title="Enregistrer"
                >
                  <Save size={18} />
                </button>
              ) : (
                <>
                  <button
                    onClick={() => setIsEditing(true)}
                    className="p-2 text-blue-600 hover:text-blue-800 hover:bg-blue-50 rounded-full transition-colors"
                    title="Modifier"
                  >
                    <Edit2 size={18} />
                  </button>
                  {selectedNode.type !== 'sourceRisk' && selectedNode.type !== 'businessValue' && (
                    <button
                      onClick={() => onDeleteNode(selectedNode.id)}
                      className="p-2 text-red-600 hover:text-red-800 hover:bg-red-50 rounded-full transition-colors"
                      title="Supprimer"
                    >
                      <Trash2 size={18} />
                    </button>
                  )}
                </>
              )}
            </>
          )}
          <button
            onClick={onClose}
            className="p-2 text-slate-500 hover:text-slate-700 hover:bg-slate-100 rounded-full transition-colors"
            title="Fermer"
          >
            <X size={18} />
          </button>
        </div>
      </div>

      {renderNodeTypeDetails()}

      {!showAddForm && !isEditing && selectedNode && (
        <div className="p-4 border-t border-slate-200">
          <button
            onClick={() => {
              // Store the current node selection before showing add form
              const currentNode = selectedNode;
              setShowAddForm(true);
              // Keep the node selected in the background
              setTimeout(() => {
                if (!selectedNode) {
                  // This prevents the node from being deselected
                  onUpdateNode(currentNode.id, currentNode.data);
                }
              }, 50);
            }}
            className="w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition duration-200 flex items-center justify-center add-node-button"
          >
            <Plus size={16} className="mr-2" />
            Ajouter un nœud
          </button>
        </div>
      )}
    </div>
  );
};

export default NodeDetailsSidebar;
