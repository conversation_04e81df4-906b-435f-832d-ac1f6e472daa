// src/components/Atelier 2/Activite2/RadarVisualization.js
import React from 'react';
import EnhancedRadarVisualization from './EnhancedRadarVisualization';

const RadarVisualization = ({ couples, onCoupleUpdate }) => {
  // Handle couple selection change
  const handleCoupleSelectionChange = (id, field, value) => {
    if (onCoupleUpdate) {
      onCoupleUpdate(id, field, value);
    }
  };

  return (
    <div className="space-y-6">
      <EnhancedRadarVisualization
        couples={couples}
        onCoupleSelectionChange={handleCoupleSelectionChange}
      />
    </div>
  );
};

export default RadarVisualization;
