// backend/controllers/activityLogController.js
const ActivityLog = require('../models/ActivityLog');
const User = require('../models/User');
const Company = require('../models/Company');
const mongoose = require('mongoose');

/**
 * Helper function to transform MongoDB document for frontend
 * Handles ObjectId and Date serialization
 */
const transformLogForResponse = (log) => {
  if (!log) return null;
  
  // Convert to plain object if it's a Mongoose document
  const plainLog = log.toObject ? log.toObject() : log;
  
  return {
    id: plainLog._id.toString(),
    userId: plainLog.userId ? plainLog.userId.toString() : null,
    userName: plainLog.userName || 'Utilisateur inconnu',
    companyId: plainLog.companyId ? plainLog.companyId.toString() : null,
    companyName: plainLog.companyName || null,
    actionType: plainLog.actionType || 'UNKNOWN',
    resourceType: plainLog.resourceType || '',
    resourceId: plainLog.resourceId ? plainLog.resourceId.toString() : null,
    ipAddress: plainLog.ipAddress || '',
    details: plainLog.details || {},
    timestamp: plainLog.timestamp ? plainLog.timestamp.toISOString() : new Date().toISOString()
  };
};

/**
 * Apply permission-based filtering to the query filter
 * @param {Object} filter - The current query filter
 * @param {Object} user - The authenticated user
 * @returns {Object} Updated filter with permission restrictions
 */
const applyPermissionFilters = (filter, user) => {
  // Clone the filter to avoid modifying the original
  const updatedFilter = { ...filter };
  
  // Only SuperAdmin can see all logs
  // Admin can only see logs from their company
  if (user.role === 'admin' && user.companyId) {
    updatedFilter.companyId = user.companyId;
  }
  
  // Regular users can only see their own logs
  if (user.role === 'simpleuser') {
    updatedFilter.userId = user._id;
  }
  
  return updatedFilter;
};

/**
 * @desc    Get all activity logs
 * @route   GET /api/logs
 * @access  Private (SuperAdmin, Admin for their company, Users for their own)
 */
exports.getLogs = async (req, res) => {
  try {
    const { 
      userId, 
      companyId, 
      actionType, 
      resourceType,
      startDate,
      endDate,
      page = 1, 
      limit = 20
    } = req.query;
    
    const skip = (page - 1) * limit;
    
    // Build filter
    let filter = {};
    
    // Add user-provided filters
    if (userId) {
      // Make sure userId is a valid ObjectId
      if (mongoose.Types.ObjectId.isValid(userId)) {
        filter.userId = new mongoose.Types.ObjectId(userId);
      }
    }
    
    if (companyId) {
      // Make sure companyId is a valid ObjectId
      if (mongoose.Types.ObjectId.isValid(companyId)) {
        filter.companyId = new mongoose.Types.ObjectId(companyId);
      }
    }
    
    if (actionType) {
      filter.actionType = actionType;
    }
    
    if (resourceType) {
      filter.resourceType = resourceType;
    }
    
    // Date range filter
    if (startDate || endDate) {
      filter.timestamp = {};
      
      if (startDate) {
        const startDateTime = new Date(startDate);
        startDateTime.setHours(0, 0, 0, 0);
        filter.timestamp.$gte = startDateTime;
      }
      
      if (endDate) {
        const endDateTime = new Date(endDate);
        endDateTime.setHours(23, 59, 59, 999);
        filter.timestamp.$lte = endDateTime;
      }
    }
    
    console.log('Before permission filters:', JSON.stringify(filter, null, 2));
    
    // Apply permission-based filtering
    filter = applyPermissionFilters(filter, req.user);
    
    console.log('After permission filters:', JSON.stringify(filter, null, 2));
    
    // Execute query with pagination
    const logs = await ActivityLog.find(filter)
      .sort({ timestamp: -1 })
      .skip(skip)
      .limit(parseInt(limit));
    
    // Get total count for pagination
    const total = await ActivityLog.countDocuments(filter);
    
    // Transform logs for frontend
    const transformedLogs = logs.map(transformLogForResponse);
    
    // Return logs
    res.status(200).json({
      success: true,
      data: transformedLogs,
      pagination: {
        currentPage: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Get logs error:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération des logs d\'activité',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * @desc    Get logs for a specific company
 * @route   GET /api/companies/:companyId/logs
 * @access  Private (SuperAdmin, Admin of the company)
 */
exports.getCompanyLogs = async (req, res) => {
  try {
    const companyId = req.params.companyId;
    const { page = 1, limit = 20, actionType, resourceType, userId, startDate, endDate } = req.query;
    const skip = (page - 1) * limit;
    
    // Validate companyId
    if (!mongoose.Types.ObjectId.isValid(companyId)) {
      return res.status(400).json({
        success: false,
        message: 'ID d\'entreprise invalide'
      });
    }
    
    // Check permissions - Admin can only see their own company logs
    if (req.user.role === 'admin' && req.user.companyId.toString() !== companyId) {
      return res.status(403).json({
        success: false,
        message: 'Vous n\'avez pas la permission de voir les logs de cette entreprise'
      });
    }
    
    // Check if company exists
    const company = await Company.findById(companyId);
    
    if (!company) {
      return res.status(404).json({
        success: false,
        message: 'Entreprise non trouvée'
      });
    }
    
    // Build filter
    const filter = { companyId: new mongoose.Types.ObjectId(companyId) };
    
    if (actionType) {
      filter.actionType = actionType;
    }
    
    if (resourceType) {
      filter.resourceType = resourceType;
    }
    
    if (userId && mongoose.Types.ObjectId.isValid(userId)) {
      filter.userId = new mongoose.Types.ObjectId(userId);
    }
    
    // Date range filter
    if (startDate || endDate) {
      filter.timestamp = {};
      
      if (startDate) {
        const startDateTime = new Date(startDate);
        startDateTime.setHours(0, 0, 0, 0);
        filter.timestamp.$gte = startDateTime;
      }
      
      if (endDate) {
        const endDateTime = new Date(endDate);
        endDateTime.setHours(23, 59, 59, 999);
        filter.timestamp.$lte = endDateTime;
      }
    }
    
    console.log('Company logs filter:', JSON.stringify(filter, null, 2));
    
    // Get logs
    const logs = await ActivityLog.find(filter)
      .sort({ timestamp: -1 })
      .skip(skip)
      .limit(parseInt(limit));
    
    // Get total count for pagination
    const total = await ActivityLog.countDocuments(filter);
    
    // Transform logs for frontend
    const transformedLogs = logs.map(transformLogForResponse);
    
    res.status(200).json({
      success: true,
      data: transformedLogs,
      pagination: {
        currentPage: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Get company logs error:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération des logs de l\'entreprise',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * @desc    Get logs for a specific user
 * @route   GET /api/users/:userId/logs
 * @access  Private (SuperAdmin, Admin of the user's company, or the user themselves)
 */
exports.getUserLogs = async (req, res) => {
  try {
    const userId = req.params.userId;
    const { page = 1, limit = 20, actionType, resourceType, startDate, endDate } = req.query;
    const skip = (page - 1) * limit;
    
    // Validate userId
    if (!mongoose.Types.ObjectId.isValid(userId)) {
      return res.status(400).json({
        success: false,
        message: 'ID d\'utilisateur invalide'
      });
    }
    
    // Check if user exists
    const user = await User.findById(userId);
    
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'Utilisateur non trouvé'
      });
    }
    
    // Check permissions
    if (req.user.role === 'admin' && req.user.companyId.toString() !== user.companyId.toString()) {
      return res.status(403).json({
        success: false,
        message: 'Vous n\'avez pas la permission de voir les logs de cet utilisateur'
      });
    }
    
    if (req.user.role === 'simpleuser' && req.user._id.toString() !== userId) {
      return res.status(403).json({
        success: false,
        message: 'Vous n\'avez pas la permission de voir les logs d\'autres utilisateurs'
      });
    }
    
    // Build filter
    const filter = { userId: new mongoose.Types.ObjectId(userId) };
    
    if (actionType) {
      filter.actionType = actionType;
    }
    
    if (resourceType) {
      filter.resourceType = resourceType;
    }
    
    // Date range filter
    if (startDate || endDate) {
      filter.timestamp = {};
      
      if (startDate) {
        const startDateTime = new Date(startDate);
        startDateTime.setHours(0, 0, 0, 0);
        filter.timestamp.$gte = startDateTime;
      }
      
      if (endDate) {
        const endDateTime = new Date(endDate);
        endDateTime.setHours(23, 59, 59, 999);
        filter.timestamp.$lte = endDateTime;
      }
    }
    
    console.log('User logs filter:', JSON.stringify(filter, null, 2));
    
    // Get logs
    const logs = await ActivityLog.find(filter)
      .sort({ timestamp: -1 })
      .skip(skip)
      .limit(parseInt(limit));
    
    // Get total count for pagination
    const total = await ActivityLog.countDocuments(filter);
    
    // Transform logs for frontend
    const transformedLogs = logs.map(transformLogForResponse);
    
    res.status(200).json({
      success: true,
      data: transformedLogs,
      pagination: {
        currentPage: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Get user logs error:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération des logs de l\'utilisateur',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * @desc    Get activity stats and summary
 * @route   GET /api/logs/stats
 * @access  Private (SuperAdmin, Admin)
 */
exports.getActivityStats = async (req, res) => {
  try {
    // Initialize filter based on user role and permissions
    let filter = {};
    
    // Admin can only see stats from their company
    if (req.user.role === 'admin' && req.user.companyId) {
      filter.companyId = req.user.companyId;
    }
    
    console.log('Activity stats filter:', JSON.stringify(filter, null, 2));
    
    // Get total count
    const totalLogs = await ActivityLog.countDocuments(filter);
    
    // Get counts by action type
    const actionTypeCounts = await ActivityLog.aggregate([
      { $match: filter },
      { $group: { _id: '$actionType', count: { $sum: 1 } } },
      { $sort: { count: -1 } }
    ]);
    
    // Get counts by resource type
    const resourceTypeCounts = await ActivityLog.aggregate([
      { $match: filter },
      { $group: { _id: '$resourceType', count: { $sum: 1 } } },
      { $sort: { count: -1 } }
    ]);
    
    // Get most active users (filtered by company for admin)
    const mostActiveUsers = await ActivityLog.aggregate([
      { $match: filter },
      { $group: { _id: { userId: '$userId', userName: '$userName' }, count: { $sum: 1 } } },
      { $sort: { count: -1 } },
      { $limit: 5 }
    ]);
    
    // Get recent activity (filtered by company for admin)
    const recentActivity = await ActivityLog.find(filter)
      .sort({ timestamp: -1 })
      .limit(10);
    
    // Transform recent activity logs
    const transformedRecentActivity = recentActivity.map(transformLogForResponse);
    
    // Get activity counts by day for the last 30 days (filtered by company for admin)
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
    
    const activityByDay = await ActivityLog.aggregate([
      { 
        $match: { 
          ...filter,
          timestamp: { $gte: thirtyDaysAgo } 
        } 
      },
      {
        $group: {
          _id: { 
            $dateToString: { format: '%Y-%m-%d', date: '$timestamp' } 
          },
          count: { $sum: 1 }
        }
      },
      { $sort: { _id: 1 } }
    ]);
    
    // Transform aggregation results
    const formattedActionTypeCounts = actionTypeCounts.map(item => ({
      type: item._id || 'UNKNOWN',
      count: item.count
    }));
    
    const formattedResourceTypeCounts = resourceTypeCounts.map(item => ({
      type: item._id || 'unknown',
      count: item.count
    }));
    
    const formattedMostActiveUsers = mostActiveUsers.map(item => ({
      userId: item._id.userId ? item._id.userId.toString() : null,
      userName: item._id.userName || 'Utilisateur inconnu',
      count: item.count
    }));
    
    res.status(200).json({
      success: true,
      data: {
        totalLogs,
        actionTypeCounts: formattedActionTypeCounts,
        resourceTypeCounts: formattedResourceTypeCounts,
        mostActiveUsers: formattedMostActiveUsers,
        recentActivity: transformedRecentActivity,
        activityByDay
      }
    });
  } catch (error) {
    console.error('Get activity stats error:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération des statistiques d\'activité',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};