// src/components/Atelier4/Activite2/GuideModal.js
import React from 'react';
import './GuideModal.css';

const GuideModal = ({ onClose }) => {
  return (
    <div className="guide-modal-overlay">
      <div className="guide-modal">
        <div className="guide-header">
          <h2>
            <i className="fas fa-book-open"></i>
            Guide - Activité 2 : Scénarios opérationnels
          </h2>
          <button className="close-btn" onClick={onClose}>
            <i className="fas fa-times"></i>
          </button>
        </div>

        <div className="guide-content">
          <div className="guide-section">
            <h3>
              <i className="fas fa-bullseye"></i>
              Objectif de l'activité
            </h3>
            <p>
              L'activité 2 de l'Atelier 4 vise à décomposer les chemins d'attaque identifiés en 
              <strong> scénarios opérationnels détaillés</strong>. Ces scénarios décrivent concrètement 
              comment un attaquant pourrait exploiter chaque chemin d'attaque, en détaillant les étapes, 
              techniques, et indicateurs de compromission.
            </p>
          </div>

          <div className="guide-section">
            <h3>
              <i className="fas fa-cogs"></i>
              Fonctionnalités principales
            </h3>
            
            <div className="feature-grid">
              <div className="feature-card">
                <div className="feature-icon">
                  <i className="fas fa-robot"></i>
                </div>
                <h4>Génération IA</h4>
                <p>Générez automatiquement des scénarios opérationnels détaillés à partir des chemins d'attaque avec l'intelligence artificielle.</p>
              </div>

              <div className="feature-card">
                <div className="feature-icon">
                  <i className="fas fa-table"></i>
                </div>
                <h4>Vue tableau</h4>
                <p>Consultez et gérez tous vos scénarios dans un tableau interactif avec tri, filtrage et édition en ligne.</p>
              </div>

              <div className="feature-card">
                <div className="feature-icon">
                  <i className="fas fa-project-diagram"></i>
                </div>
                <h4>Visualisation</h4>
                <p>Explorez vos scénarios dans une vue graphique interactive montrant les relations entre chemins et étapes.</p>
              </div>

              <div className="feature-card">
                <div className="feature-icon">
                  <i className="fas fa-tasks"></i>
                </div>
                <h4>Étapes détaillées</h4>
                <p>Chaque scénario inclut des étapes opérationnelles avec techniques, indicateurs et durées estimées.</p>
              </div>
            </div>
          </div>

          <div className="guide-section">
            <h3>
              <i className="fas fa-play-circle"></i>
              Comment utiliser cette activité
            </h3>
            
            <div className="steps-container">
              <div className="step">
                <div className="step-number">1</div>
                <div className="step-content">
                  <h4>Génération avec IA</h4>
                  <p>Cliquez sur <strong>"Générer avec IA"</strong> pour créer automatiquement des scénarios opérationnels :</p>
                  <ul>
                    <li>Sélectionnez les chemins d'attaque à analyser</li>
                    <li>Configurez le nombre de scénarios par chemin (1-4)</li>
                    <li>Choisissez le niveau de détail (basique, détaillé, exhaustif)</li>
                    <li>Activez l'inclusion des étapes opérationnelles détaillées</li>
                  </ul>
                </div>
              </div>

              <div className="step">
                <div className="step-number">2</div>
                <div className="step-content">
                  <h4>Consultation et édition</h4>
                  <p>Dans la <strong>vue tableau</strong> :</p>
                  <ul>
                    <li>Cliquez sur la flèche pour développer un scénario et voir ses étapes</li>
                    <li>Utilisez les boutons d'édition pour modifier les détails</li>
                    <li>Triez par gravité, probabilité ou nom</li>
                    <li>Sélectionnez plusieurs scénarios pour des actions groupées</li>
                  </ul>
                </div>
              </div>

              <div className="step">
                <div className="step-number">3</div>
                <div className="step-content">
                  <h4>Visualisation interactive</h4>
                  <p>Dans la <strong>vue visualisation</strong> :</p>
                  <ul>
                    <li><strong>Vue scénarios</strong> : Voir tous les scénarios groupés par chemin d'attaque</li>
                    <li><strong>Vue étapes</strong> : Explorer en détail les étapes d'un scénario spécifique</li>
                    <li><strong>Vue chemins</strong> : Analyser les relations entre chemins d'attaque</li>
                    <li>Utilisez les contrôles pour zoomer, déplacer et ajuster la vue</li>
                  </ul>
                </div>
              </div>

              <div className="step">
                <div className="step-number">4</div>
                <div className="step-content">
                  <h4>Export et sauvegarde</h4>
                  <p>Vos données sont automatiquement sauvegardées. Vous pouvez :</p>
                  <ul>
                    <li>Exporter tous les scénarios au format JSON</li>
                    <li>Les modifications sont sauvegardées en temps réel</li>
                    <li>Accéder aux données depuis d'autres ateliers</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>

          <div className="guide-section">
            <h3>
              <i className="fas fa-lightbulb"></i>
              Bonnes pratiques
            </h3>
            
            <div className="tips-grid">
              <div className="tip-card success">
                <i className="fas fa-check-circle"></i>
                <h4>À faire</h4>
                <ul>
                  <li>Commencez par générer 2-3 scénarios par chemin d'attaque</li>
                  <li>Utilisez le niveau "détaillé" pour un bon équilibre</li>
                  <li>Vérifiez et ajustez les scénarios générés selon votre contexte</li>
                  <li>Documentez les indicateurs de compromission spécifiques</li>
                  <li>Validez la cohérence avec les chemins d'attaque source</li>
                </ul>
              </div>

              <div className="tip-card warning">
                <i className="fas fa-exclamation-triangle"></i>
                <h4>À éviter</h4>
                <ul>
                  <li>Ne pas générer trop de scénarios d'un coup (max 20-30)</li>
                  <li>Ne pas ignorer les scénarios existants lors de nouvelles générations</li>
                  <li>Ne pas oublier de personnaliser les scénarios générés</li>
                  <li>Ne pas négliger la validation des étapes techniques</li>
                </ul>
              </div>
            </div>
          </div>

          <div className="guide-section">
            <h3>
              <i className="fas fa-info-circle"></i>
              Informations sur les scénarios
            </h3>
            
            <div className="info-cards">
              <div className="info-card">
                <h4>Structure d'un scénario</h4>
                <ul>
                  <li><strong>Nom et description</strong> : Identification claire du scénario</li>
                  <li><strong>Gravité</strong> : Impact potentiel (faible, moyenne, élevée, critique)</li>
                  <li><strong>Probabilité</strong> : Chance de réussite (faible, moyenne, élevée)</li>
                  <li><strong>Compétences requises</strong> : Niveau technique nécessaire</li>
                  <li><strong>Durée estimée</strong> : Temps nécessaire pour l'exécution</li>
                  <li><strong>Étapes opérationnelles</strong> : Séquence détaillée d'actions</li>
                </ul>
              </div>

              <div className="info-card">
                <h4>Étapes opérationnelles</h4>
                <ul>
                  <li><strong>Nom et description</strong> : Action spécifique à réaliser</li>
                  <li><strong>Techniques</strong> : Outils et méthodes utilisés</li>
                  <li><strong>Indicateurs</strong> : Signes de compromission détectables</li>
                  <li><strong>Durée</strong> : Temps estimé pour cette étape</li>
                </ul>
              </div>
            </div>
          </div>

          <div className="guide-section">
            <h3>
              <i className="fas fa-question-circle"></i>
              Questions fréquentes
            </h3>
            
            <div className="faq-container">
              <div className="faq-item">
                <h4>Combien de scénarios générer par chemin d'attaque ?</h4>
                <p>Nous recommandons 2-3 scénarios par chemin pour couvrir différentes approches et niveaux de sophistication, sans surcharger l'analyse.</p>
              </div>

              <div className="faq-item">
                <h4>Comment personnaliser les scénarios générés ?</h4>
                <p>Utilisez le bouton d'édition dans le tableau pour ajuster les descriptions, gravités, et étapes selon votre environnement spécifique.</p>
              </div>

              <div className="faq-item">
                <h4>Que faire si la génération IA échoue ?</h4>
                <p>Vérifiez votre connexion, réduisez le nombre de chemins sélectionnés, ou contactez l'administrateur si le problème persiste.</p>
              </div>

              <div className="faq-item">
                <h4>Comment utiliser les visualisations ?</h4>
                <p>Chaque mode de visualisation offre une perspective différente : scénarios pour la vue d'ensemble, étapes pour le détail, chemins pour les relations.</p>
              </div>
            </div>
          </div>
        </div>

        <div className="guide-footer">
          <button className="btn btn-primary" onClick={onClose}>
            <i className="fas fa-check"></i>
            Compris, commencer l'activité
          </button>
        </div>
      </div>
    </div>
  );
};

export default GuideModal;