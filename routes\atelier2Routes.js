// routes/atelier2Routes.js
const express = require('express');
const {
  getThreatCategories,
  saveThreatCategories,
  getSourcesRisque,
  saveSourcesRisque,
  getObjectifsVises,
  saveObjectifsVises,
  getCouplesSROV,
  saveCouplesSROV,
  getSelectedCouplesSROV,
  saveSelectedCouplesSROV
} = require('../controllers/atelier2Controller');

const { protect } = require('../middleware/authMiddleware');

const router = express.Router();

// All routes need authentication
router.use(protect);

// Threat categories routes
router.route('/analyses/:analysisId/atelier2/threat-categories')
  .get(protect, getThreatCategories)
  .post(protect, saveThreatCategories);

// Sources de risque routes
router.route('/analyses/:analysisId/atelier2/sources-risque')
  .get(protect, getSourcesRisque)
  .post(protect, saveSourcesRisque);

// Objectifs visés routes
router.route('/analyses/:analysisId/atelier2/objectifs-vises')
  .get(protect, getObjectifsVises)
  .post(protect, saveObjectifsVises);

// Couples SR/OV routes
router.route('/analyses/:analysisId/atelier2/couples-srov')
  .get(protect, getCouplesSROV)
  .post(protect, saveCouplesSROV);

// Selected couples SR/OV routes
router.route('/analyses/:analysisId/atelier2/selected-couples-srov')
  .get(protect, getSelectedCouplesSROV)
  .post(protect, saveSelectedCouplesSROV);

module.exports = router;
