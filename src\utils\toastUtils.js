// src/utils/toastUtils.js
import toast from 'react-hot-toast';

/**
 * Toast utility functions for consistent toast notifications across the application
 */

// Success toast with custom message
export const showSuccessToast = (message) => {
  toast.success(message);
};

// Error toast with custom message
export const showErrorToast = (message) => {
  toast.error(message);
};

// Info toast with custom message
export const showInfoToast = (message) => {
  toast(message);
};

// Warning toast with custom message and icon
export const showWarningToast = (message) => {
  toast(message, {
    icon: '⚠️',
    style: {
      background: '#FEF3C7',
      color: '#92400E',
      borderLeft: '4px solid #F59E0B',
    },
  });
};

// Loading toast that can be updated
export const showLoadingToast = (message) => {
  return toast.loading(message);
};

// Update an existing toast (useful for loading -> success/error transitions)
export const updateToast = (toastId, message, type) => {
  // Dismiss the specific toast first
  if (toastId) {
    toast.dismiss(toastId);
  }

  // Small delay to prevent race conditions
  setTimeout(() => {
    if (type === 'success') {
      toast.success(message);
    } else if (type === 'error') {
      toast.error(message);
    } else {
      toast(message);
    }
  }, 100);
};

// Dismiss a specific toast by ID
export const dismissToast = (toastId) => {
  toast.dismiss(toastId);
};

// Dismiss all toasts
export const dismissAllToasts = () => {
  toast.dismiss();
};

// Track recent toasts to prevent duplicates
const recentToasts = new Set();

// Success toast with duplicate prevention
export const showSuccessToastOnce = (message) => {
  const key = `success-${message}`;
  if (!recentToasts.has(key)) {
    recentToasts.add(key);
    toast.success(message);
    // Remove from set after 2 seconds
    setTimeout(() => recentToasts.delete(key), 2000);
  }
};

// Error toast with duplicate prevention
export const showErrorToastOnce = (message) => {
  const key = `error-${message}`;
  if (!recentToasts.has(key)) {
    recentToasts.add(key);
    toast.error(message);
    // Remove from set after 2 seconds
    setTimeout(() => recentToasts.delete(key), 2000);
  }
};

export default {
  showSuccessToast,
  showErrorToast,
  showInfoToast,
  showWarningToast,
  showLoadingToast,
  updateToast,
  dismissToast,
  dismissAllToasts,
  showSuccessToastOnce,
  showErrorToastOnce,
};
