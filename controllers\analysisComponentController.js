// controllers/analysisComponentController.js
const AnalysisComponent = require('../models/AnalysisComponent');
const Analysis = require('../models/Analysis');
const ActivityLog = require('../models/ActivityLog');
const mongoose = require('mongoose');

// Helper function to get IP address
const getIpAddress = (req) => {
  return req.ip ||
         req.headers['x-forwarded-for'] ||
         req.connection.remoteAddress ||
         'unknown';
};

// Helper function to create activity log
const logActivity = async (req, actionType, resourceType, resourceId, details = {}) => {
  try {
    if (!req.user) return;

    await ActivityLog.create({
      userId: req.user.id,
      userName: req.user.name,
      companyId: req.user.companyId,
      companyName: req.user.companyName,
      actionType,
      resourceType,
      resourceId,
      ipAddress: getIpAddress(req),
      userAgent: req.headers['user-agent'] || '',
      details: {
        ...details,
        timestamp: new Date()
      }
    });
  } catch (error) {
    console.error('Activity log error:', error);
  }
};

/**
 * Helper to check if user has access to the analysis
 */
const checkAnalysisAccess = async (analysisId, userId, userRole, userCompanyId) => {
  try {
    const analysis = await Analysis.findById(analysisId);

    if (!analysis) {
      return {
        success: false,
        status: 404,
        message: 'Analysis not found'
      };
    }

    // SuperAdmin has access to all analyses
    if (userRole === 'superadmin') {
      return { success: true, analysis };
    }

    // Other users can only access analyses from their company
    if (analysis.companyId.toString() !== userCompanyId.toString()) {
      return {
        success: false,
        status: 403,
        message: 'You do not have permission to access this analysis'
      };
    }

    return { success: true, analysis };
  } catch (error) {
    return {
      success: false,
      status: 500,
      message: 'Error checking analysis access',
      error
    };
  }
};

/**
 * Transform component for response
 */
const transformComponentForResponse = (component) => {
  if (!component) return null;

  const plainComponent = component.toObject ? component.toObject() : { ...component };

  return {
    id: plainComponent._id.toString(),
    analysisId: plainComponent.analysisId.toString(),
    componentType: plainComponent.componentType,
    data: plainComponent.data,
    version: plainComponent.version,
    createdBy: plainComponent.createdBy.toString(),
    updatedBy: plainComponent.updatedBy ? plainComponent.updatedBy.toString() : null,
    createdAt: plainComponent.createdAt.toISOString(),
    updatedAt: plainComponent.updatedAt.toISOString()
  };
};

/**
 * @desc    Get a specific component for an analysis
 * @route   GET /api/analyses/:analysisId/components/:componentType
 * @access  Private
 */
exports.getComponent = async (req, res) => {
  try {
    const { analysisId, componentType } = req.params;

    // Validate analysisId
    if (!mongoose.Types.ObjectId.isValid(analysisId)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid analysis ID'
      });
    }

    // Validate componentType
    const validComponentTypes = ['context', 'business-values', 'dreaded-events', 'security-framework', 'security-controls', 'risk-treatment', 'sources', 'objectives', 'threat-categories', 'sources-risque', 'objectifs-vises', 'couples-srov', 'selected-couples-srov', 'stakeholders', 'attack-paths', 'attack-path-diagrams', 'ecosystem-measures', 'cti-results'];
    if (!validComponentTypes.includes(componentType)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid component type'
      });
    }

    // Check if user has access to this analysis
    const accessCheck = await checkAnalysisAccess(
      analysisId,
      req.user.id,
      req.user.role,
      req.user.companyId
    );

    if (!accessCheck.success) {
      return res.status(accessCheck.status).json({
        success: false,
        message: accessCheck.message
      });
    }

    // Get the component
    const component = await AnalysisComponent.findOne({
      analysisId,
      componentType
    }).sort({ version: -1 }); // Get the latest version

    if (!component) {
      return res.status(404).json({
        success: false,
        message: `Component '${componentType}' not found for this analysis`
      });
    }

    res.status(200).json({
      success: true,
      data: transformComponentForResponse(component)
    });
  } catch (error) {
    console.error(`Get component error:`, error);
    res.status(500).json({
      success: false,
      message: 'Error retrieving component',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * @desc    Save/update a component for an analysis
 * @route   POST /api/analyses/:analysisId/components/:componentType
 * @access  Private
 */
exports.saveComponent = async (req, res) => {
    try {
      const { analysisId, componentType } = req.params;
      const { data } = req.body;

      // Validate analysisId
      if (!mongoose.Types.ObjectId.isValid(analysisId)) {
        return res.status(400).json({
          success: false,
          message: 'Invalid analysis ID'
        });
      }

      // Validate componentType
      const validComponentTypes = ['context', 'business-values', 'dreaded-events', 'security-framework', 'security-controls', 'risk-treatment', 'sources', 'objectives', 'threat-categories', 'sources-risque', 'objectifs-vises', 'couples-srov', 'selected-couples-srov', 'stakeholders', 'attack-paths', 'attack-path-diagrams', 'ecosystem-measures', 'cti-results'];
      if (!validComponentTypes.includes(componentType)) {
        return res.status(400).json({
          success: false,
          message: 'Invalid component type'
        });
      }

      // Validate data
      if (!data) {
        return res.status(400).json({
          success: false,
          message: 'Component data is required'
        });
      }

      // Check if user has access to this analysis
      const accessCheck = await checkAnalysisAccess(
        analysisId,
        req.user.id,
        req.user.role,
        req.user.companyId
      );

      if (!accessCheck.success) {
        return res.status(accessCheck.status).json({
          success: false,
          message: accessCheck.message
        });
      }

      // Find existing component to determine next version
      const existingComponent = await AnalysisComponent.findOne({
        analysisId,
        componentType
      }).sort({ version: -1 });

      // Create or update component using findOneAndUpdate with upsert
      const component = await AnalysisComponent.findOneAndUpdate(
        {
          analysisId,
          componentType
        },
        {
          data: componentType === 'dreaded-events' ? data : data, // Pass all data for dreaded-events
          analysisId,
          componentType,
          version: existingComponent ? existingComponent.version + 1 : 1,
          createdBy: req.user.id,
          updatedBy: req.user.id,
          createdAt: existingComponent ? existingComponent.createdAt : new Date(),
          updatedAt: new Date()
        },
        {
          upsert: true,
          new: true,
          setDefaultsOnInsert: true
        }
      );

      // Update the analysis's lastUpdated fields
      await Analysis.findByIdAndUpdate(
        analysisId,
        {
          updatedAt: Date.now(),
          lastUpdatedBy: req.user.id,
          lastUpdatedByName: req.user.name
        }
      );

      // Log activity
      await logActivity(
        req,
        'COMPONENT_UPDATE',
        'analysis_component',
        component._id,
        {
          analysisId,
          componentType,
          version: component.version
        }
      );

      res.status(200).json({
        success: true,
        data: transformComponentForResponse(component)
      });
    } catch (error) {
      console.error(`Save component error:`, error);
      res.status(500).json({
        success: false,
        message: 'Error saving component',
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      });
    }
  };

/**
 * @desc    Get all components for an analysis
 * @route   GET /api/analyses/:analysisId/components
 * @access  Private
 */
exports.getAllComponents = async (req, res) => {
  try {
    const { analysisId } = req.params;

    // Validate analysisId
    if (!mongoose.Types.ObjectId.isValid(analysisId)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid analysis ID'
      });
    }

    // Check if user has access to this analysis
    const accessCheck = await checkAnalysisAccess(
      analysisId,
      req.user.id,
      req.user.role,
      req.user.companyId
    );

    if (!accessCheck.success) {
      return res.status(accessCheck.status).json({
        success: false,
        message: accessCheck.message
      });
    }

    // Get all components for this analysis
    const components = await AnalysisComponent.aggregate([
      { $match: { analysisId: new mongoose.Types.ObjectId(analysisId) } },
      { $sort: { componentType: 1, version: -1 } },
      {
        $group: {
          _id: '$componentType',
          latestComponent: { $first: '$$ROOT' }
        }
      },
      { $replaceRoot: { newRoot: '$latestComponent' } },
      { $sort: { componentType: 1 } }
    ]);

    // Map for response
    const transformedComponents = components.map(component => ({
      id: component._id.toString(),
      analysisId: component.analysisId.toString(),
      componentType: component.componentType,
      data: component.data,
      version: component.version,
      createdBy: component.createdBy.toString(),
      updatedBy: component.updatedBy ? component.updatedBy.toString() : null,
      createdAt: component.createdAt.toISOString(),
      updatedAt: component.updatedAt.toISOString()
    }));

    res.status(200).json({
      success: true,
      data: transformedComponents
    });
  } catch (error) {
    console.error(`Get all components error:`, error);
    res.status(500).json({
      success: false,
      message: 'Error retrieving components',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * @desc    Get component history for an analysis
 * @route   GET /api/analyses/:analysisId/components/:componentType/history
 * @access  Private
 */
exports.getComponentHistory = async (req, res) => {
  try {
    const { analysisId, componentType } = req.params;

    // Validate analysisId
    if (!mongoose.Types.ObjectId.isValid(analysisId)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid analysis ID'
      });
    }

    // Validate componentType
    const validComponentTypes = ['context', 'business-values', 'dreaded-events', 'security-framework', 'security-controls', 'risk-treatment', 'sources', 'objectives', 'threat-categories', 'sources-risque', 'objectifs-vises', 'couples-srov', 'selected-couples-srov', 'stakeholders', 'attack-paths', 'attack-path-diagrams', 'ecosystem-measures', 'cti-results'];
    if (!validComponentTypes.includes(componentType)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid component type'
      });
    }

    // Check if user has access to this analysis
    const accessCheck = await checkAnalysisAccess(
      analysisId,
      req.user.id,
      req.user.role,
      req.user.companyId
    );

    if (!accessCheck.success) {
      return res.status(accessCheck.status).json({
        success: false,
        message: accessCheck.message
      });
    }

    // Get all versions of this component, sorted by version in descending order
    const componentVersions = await AnalysisComponent.find({
      analysisId,
      componentType
    }).sort({ version: -1 });

    if (!componentVersions || componentVersions.length === 0) {
      return res.status(404).json({
        success: false,
        message: `No versions found for component '${componentType}'`
      });
    }

    // Transform for response
    const transformedVersions = componentVersions.map(transformComponentForResponse);

    res.status(200).json({
      success: true,
      data: transformedVersions
    });
  } catch (error) {
    console.error(`Get component history error:`, error);
    res.status(500).json({
      success: false,
      message: 'Error retrieving component history',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * @desc    Get a specific version of a component
 * @route   GET /api/analyses/:analysisId/components/:componentType/versions/:version
 * @access  Private
 */
exports.getComponentVersion = async (req, res) => {
  try {
    const { analysisId, componentType, version } = req.params;

    // Validate analysisId
    if (!mongoose.Types.ObjectId.isValid(analysisId)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid analysis ID'
      });
    }

    // Validate componentType
    const validComponentTypes = ['context', 'business-values', 'dreaded-events', 'security-framework', 'security-controls', 'risk-treatment', 'sources', 'objectives', 'threat-categories', 'sources-risque', 'objectifs-vises', 'couples-srov', 'selected-couples-srov', 'stakeholders', 'attack-paths', 'attack-path-diagrams', 'ecosystem-measures', 'cti-results'];
    if (!validComponentTypes.includes(componentType)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid component type'
      });
    }

    // Validate version
    const versionNumber = parseInt(version);
    if (isNaN(versionNumber) || versionNumber < 1) {
      return res.status(400).json({
        success: false,
        message: 'Invalid version number'
      });
    }

    // Check if user has access to this analysis
    const accessCheck = await checkAnalysisAccess(
      analysisId,
      req.user.id,
      req.user.role,
      req.user.companyId
    );

    if (!accessCheck.success) {
      return res.status(accessCheck.status).json({
        success: false,
        message: accessCheck.message
      });
    }

    // Get the specific version
    const component = await AnalysisComponent.findOne({
      analysisId,
      componentType,
      version: versionNumber
    });

    if (!component) {
      return res.status(404).json({
        success: false,
        message: `Version ${versionNumber} of component '${componentType}' not found`
      });
    }

    res.status(200).json({
      success: true,
      data: transformComponentForResponse(component)
    });
  } catch (error) {
    console.error(`Get component version error:`, error);
    res.status(500).json({
      success: false,
      message: 'Error retrieving component version',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * @desc    Get context component for an analysis
 * @route   GET /api/analyses/:analysisId/context
 * @access  Private
 */
exports.getContext = async (req, res) => {
  req.params.componentType = 'context';
  return this.getComponent(req, res);
};

/**
 * @desc    Save context component for an analysis
 * @route   POST /api/analyses/:analysisId/context
 * @access  Private
 */
exports.saveContext = async (req, res) => {
  req.params.componentType = 'context';
  return this.saveComponent(req, res);
};

/**
 * @desc    Get business values component for an analysis
 * @route   GET /api/analyses/:analysisId/business-values
 * @access  Private
 */
exports.getBusinessValues = async (req, res) => {
  req.params.componentType = 'business-values';
  return this.getComponent(req, res);
};

/**
 * @desc    Save business values component for an analysis
 * @route   POST /api/analyses/:analysisId/business-values
 * @access  Private
 */
exports.saveBusinessValues = async (req, res) => {
  req.params.componentType = 'business-values';
  return this.saveComponent(req, res);
};

/**
 * @desc    Get dreaded events component for an analysis
 * @route   GET /api/analyses/:analysisId/dreaded-events
 * @access  Private
 */
exports.getDreadedEvents = async (req, res) => {
  req.params.componentType = 'dreaded-events';
  return this.getComponent(req, res);
};

/**
 * @desc    Save dreaded events component for an analysis
 * @route   POST /api/analyses/:analysisId/dreaded-events
 * @access  Private
 */
exports.saveDreadedEvents = async (req, res) => {
  req.params.componentType = 'dreaded-events';
  return this.saveComponent(req, res);
};

/**
 * @desc    Get security framework component for an analysis
 * @route   GET /api/analyses/:analysisId/security-framework
 * @access  Private
 */
exports.getSecurityFramework = async (req, res) => {
  req.params.componentType = 'security-framework';
  return this.getComponent(req, res);
};

/**
 * @desc    Save security framework component for an analysis
 * @route   POST /api/analyses/:analysisId/security-framework
 * @access  Private
 */
exports.saveSecurityFramework = async (req, res) => {
  req.params.componentType = 'security-framework';
  return this.saveComponent(req, res);
};

/**
 * @desc    Get security controls component for an analysis
 * @route   GET /api/analyses/:analysisId/security-controls
 * @access  Private
 */
exports.getSecurityControls = async (req, res) => {
  req.params.componentType = 'security-controls';
  return this.getComponent(req, res);
};

/**
 * @desc    Save security controls component for an analysis
 * @route   POST /api/analyses/:analysisId/security-controls
 * @access  Private
 */
exports.saveSecurityControls = async (req, res) => {
  req.params.componentType = 'security-controls';
  return this.saveComponent(req, res);
};

/**
 * @desc    Get risk treatment component for an analysis
 * @route   GET /api/analyses/:analysisId/risk-treatment
 * @access  Private
 */
exports.getRiskTreatment = async (req, res) => {
  req.params.componentType = 'risk-treatment';
  return this.getComponent(req, res);
};

/**
 * @desc    Save risk treatment component for an analysis
 * @route   POST /api/analyses/:analysisId/risk-treatment
 * @access  Private
 */
exports.saveRiskTreatment = async (req, res) => {
  req.params.componentType = 'risk-treatment';
  return this.saveComponent(req, res);
};

/**
 * @desc    Get sources component for an analysis
 * @route   GET /api/analyses/:analysisId/sources
 * @access  Private
 */
exports.getSources = async (req, res) => {
  req.params.componentType = 'sources';
  return this.getComponent(req, res);
};

/**
 * @desc    Save sources component for an analysis
 * @route   POST /api/analyses/:analysisId/sources
 * @access  Private
 */
exports.saveSources = async (req, res) => {
  req.params.componentType = 'sources';
  return this.saveComponent(req, res);
};

/**
 * @desc    Get objectives component for an analysis
 * @route   GET /api/analyses/:analysisId/objectives
 * @access  Private
 */
exports.getObjectives = async (req, res) => {
  req.params.componentType = 'objectives';
  return this.getComponent(req, res);
};

/**
 * @desc    Save objectives component for an analysis
 * @route   POST /api/analyses/:analysisId/objectives
 * @access  Private
 */
exports.saveObjectives = async (req, res) => {
  req.params.componentType = 'objectives';
  return this.saveComponent(req, res);
};

/**
 * @desc    Get CTI results component for an analysis
 * @route   GET /api/analyses/:analysisId/cti-results
 * @access  Private
 */
exports.getCTIResults = async (req, res) => {
  req.params.componentType = 'cti-results';
  return this.getComponent(req, res);
};

/**
 * @desc    Save CTI results component for an analysis
 * @route   POST /api/analyses/:analysisId/cti-results
 * @access  Private
 */
exports.saveCTIResults = async (req, res) => {
  req.params.componentType = 'cti-results';
  return this.saveComponent(req, res);
};

/**
 * @desc    Delete CTI results component for an analysis
 * @route   DELETE /api/analyses/:analysisId/cti-results
 * @access  Private
 */
exports.deleteCTIResults = async (req, res) => {
  try {
    const { analysisId } = req.params;

    // Validate analysisId
    if (!mongoose.Types.ObjectId.isValid(analysisId)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid analysis ID'
      });
    }

    // Check if user has access to this analysis
    const accessCheck = await checkAnalysisAccess(
      analysisId,
      req.user.id,
      req.user.role,
      req.user.companyId
    );

    if (!accessCheck.success) {
      return res.status(accessCheck.status || 403).json({
        success: false,
        message: accessCheck.message
      });
    }

    // Find and delete the CTI results component
    const deletedComponent = await AnalysisComponent.findOneAndDelete({
      analysisId,
      componentType: 'cti-results'
    });

    if (!deletedComponent) {
      return res.status(404).json({
        success: false,
        message: 'CTI results not found'
      });
    }

    // Also delete all versions of this component
    await AnalysisComponent.deleteMany({
      analysisId,
      componentType: 'cti-results'
    });

    // Log activity
    await logActivity(
      req,
      'COMPONENT_DELETE',
      'analysis_component',
      deletedComponent._id,
      {
        analysisId,
        componentType: 'cti-results'
      }
    );

    res.status(200).json({
      success: true,
      message: 'CTI results deleted successfully'
    });

  } catch (error) {
    console.error('Error deleting CTI results:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while deleting CTI results'
    });
  }
};