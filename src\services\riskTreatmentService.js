// src/services/riskTreatmentService.js
import api from '../api/apiClient';

/**
 * Service for handling risk treatment operations
 * API-first approach without localStorage fallback for loading
 */
const riskTreatmentService = {
  /**
   * Get risk treatment data for a specific analysis
   * @param {string} analysisId - Analysis ID
   * @returns {Promise<Object>} Risk treatment data
   */
  getRiskTreatment: async (analysisId) => {
    try {
      // Try to get from API
      const response = await api.get(`/analyses/${analysisId}/risk-treatment`);

      if (response.success) {
        return response.data;
      }

      throw new Error(response.message || 'Failed to fetch risk treatment data');
    } catch (error) {
      // If it's a 404 error, return empty data instead of throwing
      if (error.status === 404) {
        console.log('No risk treatment data found, returning empty data');
        return {
          data: {
            riskTreatments: []
          }
        };
      }

      // Log without revealing sensitive data
      console.error('Error fetching risk treatment data');
      // No localStorage fallback - API is the source of truth
      throw error;
    }
  },

  /**
   * Save risk treatment data for a specific analysis
   * @param {string} analysisId - Analysis ID
   * @param {Array} riskTreatmentData - Risk treatment data to save
   * @returns {Promise<Object>} Saved risk treatment data
   */
  saveRiskTreatment: async (analysisId, riskTreatmentData) => {
    // Format data for API
    const formattedData = {
      data: {
        riskTreatments: riskTreatmentData
      }
    };

    try {
      // Save to API using POST method to match backend route configuration
      const response = await api.post(`/analyses/${analysisId}/risk-treatment`, formattedData);

      // Also save to local storage as backup
      localStorage.setItem('riskTreatmentData', JSON.stringify(riskTreatmentData));

      return response;
    } catch (error) {
      // Log without revealing sensitive data
      console.error('Error saving risk treatment data');

      // If API fails, at least try to save locally
      localStorage.setItem('riskTreatmentData', JSON.stringify(riskTreatmentData));

      // Return a success response with a local flag
      return {
        success: true,
        data: riskTreatmentData,
        savedLocally: true,
        message: 'Data saved locally only. Will sync when connection is restored.'
      };
    }
  },

  /**
   * Format risk treatment data for component consumption
   * @param {Object} rawData - Raw data from API
   * @returns {Array} Formatted risk treatment array
   */
  formatRiskTreatmentData: (rawData) => {
    // If data is already in array format, return it
    if (Array.isArray(rawData)) {
      return rawData;
    }

    // Extract from API response
    const data = rawData.data || {};

    return data.riskTreatments || [];
  },

  /**
   * Get risk treatment history
   * @param {string} analysisId - Analysis ID
   * @returns {Promise<Array>} Risk treatment history
   */
  getRiskTreatmentHistory: async (analysisId) => {
    try {
      const response = await api.get(`/analyses/${analysisId}/components/risk-treatment/history`);
      return response.data || [];
    } catch (error) {
      // Log without revealing sensitive data
      console.error('Error fetching risk treatment history');
      throw error;
    }
  }
};

export default riskTreatmentService;