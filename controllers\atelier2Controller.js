// controllers/atelier2Controller.js
const mongoose = require('mongoose');
const AnalysisComponent = require('../models/AnalysisComponent');
const Analysis = require('../models/Analysis');
const { checkAnalysisAccess } = require('../utils/accessControl');

/**
 * @desc    Get threat categories for an analysis
 * @route   GET /api/analyses/:analysisId/atelier2/threat-categories
 * @access  Private
 */
exports.getThreatCategories = async (req, res) => {
  try {
    const { analysisId } = req.params;

    // Validate analysisId
    if (!mongoose.Types.ObjectId.isValid(analysisId)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid analysis ID'
      });
    }

    // Check if user has access to this analysis
    const accessCheck = await checkAnalysisAccess(
      analysisId,
      req.user.id,
      req.user.role,
      req.user.companyId
    );

    if (!accessCheck.success) {
      return res.status(accessCheck.status).json({
        success: false,
        message: accessCheck.message
      });
    }

    // Get the component
    const component = await AnalysisComponent.findOne({
      analysisId,
      componentType: 'threat-categories'
    }).sort({ version: -1 }); // Get the latest version

    if (!component) {
      return res.status(200).json([]);
    }

    res.status(200).json(component.data.categories || []);
  } catch (error) {
    console.error('Error getting threat categories:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

/**
 * @desc    Save threat categories for an analysis
 * @route   POST /api/analyses/:analysisId/atelier2/threat-categories
 * @access  Private
 */
exports.saveThreatCategories = async (req, res) => {
  try {
    const { analysisId } = req.params;
    const { categories } = req.body;

    // Validate analysisId
    if (!mongoose.Types.ObjectId.isValid(analysisId)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid analysis ID'
      });
    }

    // Check if user has access to this analysis
    const accessCheck = await checkAnalysisAccess(
      analysisId,
      req.user.id,
      req.user.role,
      req.user.companyId
    );

    if (!accessCheck.success) {
      return res.status(accessCheck.status).json({
        success: false,
        message: accessCheck.message
      });
    }

    // Create or update the component
    const component = await AnalysisComponent.createNewVersion(
      analysisId,
      'threat-categories',
      { categories },
      req.user.id
    );

    // Update the analysis lastUpdatedBy fields
    await Analysis.findByIdAndUpdate(analysisId, {
      lastUpdatedBy: req.user.id,
      lastUpdatedByName: req.user.name
    });

    res.status(200).json({
      success: true,
      data: categories
    });
  } catch (error) {
    console.error('Error saving threat categories:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

/**
 * @desc    Get sources de risque for an analysis
 * @route   GET /api/analyses/:analysisId/atelier2/sources-risque
 * @access  Private
 */
exports.getSourcesRisque = async (req, res) => {
  try {
    const { analysisId } = req.params;

    // Validate analysisId
    if (!mongoose.Types.ObjectId.isValid(analysisId)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid analysis ID'
      });
    }

    // Check if user has access to this analysis
    const accessCheck = await checkAnalysisAccess(
      analysisId,
      req.user.id,
      req.user.role,
      req.user.companyId
    );

    if (!accessCheck.success) {
      return res.status(accessCheck.status).json({
        success: false,
        message: accessCheck.message
      });
    }

    // Get the component
    const component = await AnalysisComponent.findOne({
      analysisId,
      componentType: 'sources-risque'
    }).sort({ version: -1 }); // Get the latest version

    console.log('Backend - Found sources-risque component:', component ? 'Yes' : 'No');

    if (!component) {
      console.log('Backend - No component found, returning empty array');
      return res.status(200).json([]);
    }

    console.log('Backend - Component data:', component.data);
    console.log('Backend - Component data.sources:', component.data.sources);

    // Return the sources array
    const sourcesArray = component.data.sources || [];
    console.log('Backend - Returning sources array:', sourcesArray);

    res.status(200).json(sourcesArray);
  } catch (error) {
    console.error('Error getting sources de risque:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

/**
 * @desc    Save sources de risque for an analysis
 * @route   POST /api/analyses/:analysisId/atelier2/sources-risque
 * @access  Private
 */
exports.saveSourcesRisque = async (req, res) => {
  try {
    const { analysisId } = req.params;
    const { sources } = req.body;

    // Validate analysisId
    if (!mongoose.Types.ObjectId.isValid(analysisId)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid analysis ID'
      });
    }

    // Check if user has access to this analysis
    const accessCheck = await checkAnalysisAccess(
      analysisId,
      req.user.id,
      req.user.role,
      req.user.companyId
    );

    if (!accessCheck.success) {
      return res.status(accessCheck.status).json({
        success: false,
        message: accessCheck.message
      });
    }

    // Create or update the component
    const component = await AnalysisComponent.createNewVersion(
      analysisId,
      'sources-risque',
      { sources },
      req.user.id
    );

    // Update the analysis lastUpdatedBy fields
    await Analysis.findByIdAndUpdate(analysisId, {
      lastUpdatedBy: req.user.id,
      lastUpdatedByName: req.user.name
    });

    res.status(200).json({
      success: true,
      data: sources
    });
  } catch (error) {
    console.error('Error saving sources de risque:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

/**
 * @desc    Get objectifs visés for an analysis
 * @route   GET /api/analyses/:analysisId/atelier2/objectifs-vises
 * @access  Private
 */
exports.getObjectifsVises = async (req, res) => {
  try {
    const { analysisId } = req.params;

    // Validate analysisId
    if (!mongoose.Types.ObjectId.isValid(analysisId)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid analysis ID'
      });
    }

    // Check if user has access to this analysis
    const accessCheck = await checkAnalysisAccess(
      analysisId,
      req.user.id,
      req.user.role,
      req.user.companyId
    );

    if (!accessCheck.success) {
      return res.status(accessCheck.status).json({
        success: false,
        message: accessCheck.message
      });
    }

    // Get the component
    const component = await AnalysisComponent.findOne({
      analysisId,
      componentType: 'objectifs-vises'
    }).sort({ version: -1 }); // Get the latest version

    if (!component) {
      return res.status(200).json([]);
    }

    res.status(200).json(component.data.objectifs || []);
  } catch (error) {
    console.error('Error getting objectifs visés:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

/**
 * @desc    Save objectifs visés for an analysis
 * @route   POST /api/analyses/:analysisId/atelier2/objectifs-vises
 * @access  Private
 */
exports.saveObjectifsVises = async (req, res) => {
  try {
    const { analysisId } = req.params;
    const { objectifs } = req.body;

    // Validate analysisId
    if (!mongoose.Types.ObjectId.isValid(analysisId)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid analysis ID'
      });
    }

    // Check if user has access to this analysis
    const accessCheck = await checkAnalysisAccess(
      analysisId,
      req.user.id,
      req.user.role,
      req.user.companyId
    );

    if (!accessCheck.success) {
      return res.status(accessCheck.status).json({
        success: false,
        message: accessCheck.message
      });
    }

    // Create or update the component
    const component = await AnalysisComponent.createNewVersion(
      analysisId,
      'objectifs-vises',
      { objectifs },
      req.user.id
    );

    // Update the analysis lastUpdatedBy fields
    await Analysis.findByIdAndUpdate(analysisId, {
      lastUpdatedBy: req.user.id,
      lastUpdatedByName: req.user.name
    });

    res.status(200).json({
      success: true,
      data: objectifs
    });
  } catch (error) {
    console.error('Error saving objectifs visés:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

/**
 * @desc    Get couples SR/OV for an analysis
 * @route   GET /api/analyses/:analysisId/atelier2/couples-srov
 * @access  Private
 */
exports.getCouplesSROV = async (req, res) => {
  try {
    const { analysisId } = req.params;

    // Validate analysisId
    if (!mongoose.Types.ObjectId.isValid(analysisId)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid analysis ID'
      });
    }

    // Check if user has access to this analysis
    const accessCheck = await checkAnalysisAccess(
      analysisId,
      req.user.id,
      req.user.role,
      req.user.companyId
    );

    if (!accessCheck.success) {
      return res.status(accessCheck.status).json({
        success: false,
        message: accessCheck.message
      });
    }

    // Get the component
    const component = await AnalysisComponent.findOne({
      analysisId,
      componentType: 'couples-srov'
    }).sort({ version: -1 }); // Get the latest version

    if (!component) {
      return res.status(200).json([]);
    }

    res.status(200).json(component.data.couples || []);
  } catch (error) {
    console.error('Error getting couples SR/OV:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

/**
 * @desc    Save couples SR/OV for an analysis
 * @route   POST /api/analyses/:analysisId/atelier2/couples-srov
 * @access  Private
 */
exports.saveCouplesSROV = async (req, res) => {
  try {
    const { analysisId } = req.params;
    const { couples } = req.body;

    // Validate analysisId
    if (!mongoose.Types.ObjectId.isValid(analysisId)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid analysis ID'
      });
    }

    // Check if user has access to this analysis
    const accessCheck = await checkAnalysisAccess(
      analysisId,
      req.user.id,
      req.user.role,
      req.user.companyId
    );

    if (!accessCheck.success) {
      return res.status(accessCheck.status).json({
        success: false,
        message: accessCheck.message
      });
    }

    // Create or update the component
    const component = await AnalysisComponent.createNewVersion(
      analysisId,
      'couples-srov',
      { couples },
      req.user.id
    );

    // Update the analysis lastUpdatedBy fields
    await Analysis.findByIdAndUpdate(analysisId, {
      lastUpdatedBy: req.user.id,
      lastUpdatedByName: req.user.name
    });

    res.status(200).json({
      success: true,
      data: couples
    });
  } catch (error) {
    console.error('Error saving couples SR/OV:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

/**
 * @desc    Get selected couples SR/OV for an analysis
 * @route   GET /api/analyses/:analysisId/atelier2/selected-couples-srov
 * @access  Private
 */
exports.getSelectedCouplesSROV = async (req, res) => {
  try {
    const { analysisId } = req.params;

    // Validate analysisId
    if (!mongoose.Types.ObjectId.isValid(analysisId)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid analysis ID'
      });
    }

    // Check if user has access to this analysis
    const accessCheck = await checkAnalysisAccess(
      analysisId,
      req.user.id,
      req.user.role,
      req.user.companyId
    );

    if (!accessCheck.success) {
      return res.status(accessCheck.status).json({
        success: false,
        message: accessCheck.message
      });
    }

    // Get the component
    const component = await AnalysisComponent.findOne({
      analysisId,
      componentType: 'selected-couples-srov'
    }).sort({ version: -1 }); // Get the latest version

    if (!component) {
      return res.status(200).json([]);
    }

    // Check if we have the new format (mappings) or old format (selectedCouples)
    if (component.data.mappings) {
      res.status(200).json(component.data.mappings);
    } else {
      res.status(200).json(component.data.selectedCouples || []);
    }
  } catch (error) {
    console.error('Error getting selected couples SR/OV:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

/**
 * @desc    Save selected couples SR/OV for an analysis
 * @route   POST /api/analyses/:analysisId/atelier2/selected-couples-srov
 * @access  Private
 */
exports.saveSelectedCouplesSROV = async (req, res) => {
  try {
    const { analysisId } = req.params;
    const { selectedCouples, mappings } = req.body;

    // Validate analysisId
    if (!mongoose.Types.ObjectId.isValid(analysisId)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid analysis ID'
      });
    }

    // Check if user has access to this analysis
    const accessCheck = await checkAnalysisAccess(
      analysisId,
      req.user.id,
      req.user.role,
      req.user.companyId
    );

    if (!accessCheck.success) {
      return res.status(accessCheck.status).json({
        success: false,
        message: accessCheck.message
      });
    }

    // Determine which format we're using (new or old or combined)
    let dataToSave;

    if (mappings && selectedCouples) {
      // Combined format - save both for maximum compatibility
      dataToSave = { mappings, selectedCouples };
    } else if (mappings) {
      // New format only
      dataToSave = { mappings };
    } else {
      // Old format only
      dataToSave = { selectedCouples };
    }

    // Create or update the component
    const component = await AnalysisComponent.createNewVersion(
      analysisId,
      'selected-couples-srov',
      dataToSave,
      req.user.id
    );

    // Update the analysis lastUpdatedBy fields
    await Analysis.findByIdAndUpdate(analysisId, {
      lastUpdatedBy: req.user.id,
      lastUpdatedByName: req.user.name
    });

    res.status(200).json({
      success: true,
      data: mappings || selectedCouples
    });
  } catch (error) {
    console.error('Error saving selected couples SR/OV:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};
