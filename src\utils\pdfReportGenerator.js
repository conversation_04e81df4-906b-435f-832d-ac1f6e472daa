// src/utils/pdfReportGenerator.js
import jsPDF from 'jspdf';

// PDF styling constants
const COLORS = {
  primary: '#1e40af',
  secondary: '#64748b',
  success: '#059669',
  warning: '#d97706',
  danger: '#dc2626',
  light: '#f8fafc',
  dark: '#0f172a'
};

const FONTS = {
  title: 18,
  subtitle: 14,
  heading: 12,
  body: 10,
  small: 8
};

export class PDFReportGenerator {
  constructor() {
    this.pdf = new jsPDF('p', 'mm', 'a4');
    this.pageWidth = this.pdf.internal.pageSize.getWidth();
    this.pageHeight = this.pdf.internal.pageSize.getHeight();
    this.margin = 20;
    this.currentY = this.margin;
    this.lineHeight = 6;
  }

  // Add header with logo and title
  addHeader(title, subtitle = '') {
    // Add title
    this.pdf.setFontSize(FONTS.title);
    this.pdf.setTextColor(COLORS.primary);
    this.pdf.setFont('helvetica', 'bold');
    this.pdf.text(title, this.margin, this.currentY);
    this.currentY += 10;

    if (subtitle) {
      this.pdf.setFontSize(FONTS.subtitle);
      this.pdf.setTextColor(COLORS.secondary);
      this.pdf.setFont('helvetica', 'normal');
      this.pdf.text(subtitle, this.margin, this.currentY);
      this.currentY += 8;
    }

    // Add date and time
    const currentDate = new Date().toLocaleDateString('fr-FR', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });

    this.pdf.setFontSize(FONTS.small);
    this.pdf.setTextColor(COLORS.secondary);
    this.pdf.text(`Généré le ${currentDate}`, this.margin, this.currentY);
    this.currentY += 15;

    // Add separator line
    this.addSeparatorLine();
  }

  // Add footer with page number
  addFooter() {
    const pageCount = this.pdf.internal.getNumberOfPages();
    for (let i = 1; i <= pageCount; i++) {
      this.pdf.setPage(i);
      this.pdf.setFontSize(FONTS.small);
      this.pdf.setTextColor(COLORS.secondary);
      this.pdf.text(
        `Page ${i} sur ${pageCount}`,
        this.pageWidth - this.margin - 20,
        this.pageHeight - 10
      );
      this.pdf.text(
        'Rapport EBIOS RM - Progression des PTR',
        this.margin,
        this.pageHeight - 10
      );
    }
  }

  // Add section heading
  addSectionHeading(text, level = 1) {
    this.checkPageBreak(15);

    const fontSize = level === 1 ? FONTS.subtitle : FONTS.heading;
    const color = level === 1 ? COLORS.primary : COLORS.dark;

    this.pdf.setFontSize(fontSize);
    this.pdf.setTextColor(color);
    this.pdf.setFont('helvetica', 'bold');
    this.pdf.text(text, this.margin, this.currentY);
    this.currentY += level === 1 ? 10 : 8;
  }

  // Add paragraph text with better spacing
  addParagraph(text, options = {}) {
    const {
      fontSize = FONTS.body,
      color = COLORS.dark,
      fontStyle = 'normal',
      indent = 0,
      lineSpacing = 1.2
    } = options;

    this.pdf.setFontSize(fontSize);
    this.pdf.setTextColor(color);
    this.pdf.setFont('helvetica', fontStyle);

    const maxWidth = this.pageWidth - 2 * this.margin - indent;
    const lines = this.pdf.splitTextToSize(text, maxWidth);

    const adjustedLineHeight = this.lineHeight * lineSpacing;

    for (const line of lines) {
      this.checkPageBreak(adjustedLineHeight + 2);
      this.pdf.text(line, this.margin + indent, this.currentY);
      this.currentY += adjustedLineHeight;
    }
    this.currentY += 4; // Extra spacing after paragraph
  }

  // Add bullet point with better spacing
  addBulletPoint(text, level = 0) {
    const indent = 5 + (level * 10);
    const bullet = level === 0 ? '•' : '◦';
    const bulletSpacing = 6; // Space between bullet and text

    this.checkPageBreak(this.lineHeight + 4);
    this.pdf.setFontSize(FONTS.body);
    this.pdf.setTextColor(COLORS.dark);
    this.pdf.setFont('helvetica', 'normal');

    // Add bullet
    this.pdf.text(bullet, this.margin + indent, this.currentY);

    // Add text with proper wrapping
    const maxWidth = this.pageWidth - 2 * this.margin - indent - bulletSpacing;
    const lines = this.pdf.splitTextToSize(text, maxWidth);

    for (let i = 0; i < lines.length; i++) {
      if (i > 0) {
        this.checkPageBreak(this.lineHeight + 2);
        this.currentY += this.lineHeight;
      }
      this.pdf.text(lines[i], this.margin + indent + bulletSpacing, this.currentY);
    }
    this.currentY += this.lineHeight + 3; // Extra spacing after bullet point
  }

  // Add statistics box
  addStatsBox(stats) {
    this.checkPageBreak(40);

    const boxHeight = 35;
    const boxWidth = this.pageWidth - 2 * this.margin;

    // Draw background
    this.pdf.setFillColor(245, 247, 250); // Light blue background
    this.pdf.rect(this.margin, this.currentY, boxWidth, boxHeight, 'F');

    // Draw border
    this.pdf.setDrawColor(COLORS.primary);
    this.pdf.setLineWidth(0.5);
    this.pdf.rect(this.margin, this.currentY, boxWidth, boxHeight);

    // Add title
    this.pdf.setFontSize(FONTS.heading);
    this.pdf.setTextColor(COLORS.primary);
    this.pdf.setFont('helvetica', 'bold');
    this.pdf.text('STATISTIQUES GLOBALES', this.margin + 5, this.currentY + 8);

    // Add stats in columns
    const colWidth = boxWidth / 3;
    let col = 0;

    Object.entries(stats).forEach(([key, value]) => {
      const x = this.margin + 5 + (col * colWidth);
      const y = this.currentY + 18;

      this.pdf.setFontSize(FONTS.body);
      this.pdf.setTextColor(COLORS.secondary);
      this.pdf.setFont('helvetica', 'normal');
      this.pdf.text(key, x, y);

      this.pdf.setFontSize(FONTS.subtitle);
      this.pdf.setTextColor(COLORS.primary);
      this.pdf.setFont('helvetica', 'bold');
      this.pdf.text(String(value), x, y + 8);

      col = (col + 1) % 3;
    });

    this.currentY += boxHeight + 10;
  }

  // Add table with better formatting
  addTable(headers, rows, options = {}) {
    const {
      headerColor = COLORS.primary,
      headerBgColor = [240, 242, 247],
      rowHeight = 10, // Increased for better readability
      fontSize = FONTS.small,
      columnWidths = null // Allow custom column widths
    } = options;

    this.checkPageBreak(40);

    const tableWidth = this.pageWidth - 2 * this.margin;
    const colWidths = columnWidths || headers.map(() => tableWidth / headers.length);

    // Ensure column widths sum to table width
    const totalWidth = colWidths.reduce((sum, width) => sum + width, 0);
    const scaleFactor = tableWidth / totalWidth;
    const adjustedColWidths = colWidths.map(width => width * scaleFactor);

    // Draw header with borders
    this.pdf.setFillColor(...headerBgColor);
    this.pdf.rect(this.margin, this.currentY, tableWidth, rowHeight, 'F');

    this.pdf.setDrawColor(150, 150, 150);
    this.pdf.setLineWidth(0.2);

    // Header borders
    let currentX = this.margin;
    adjustedColWidths.forEach((width, i) => {
      this.pdf.rect(currentX, this.currentY, width, rowHeight);
      currentX += width;
    });

    this.pdf.setFontSize(fontSize);
    this.pdf.setTextColor(headerColor);
    this.pdf.setFont('helvetica', 'bold');

    // Header text
    currentX = this.margin;
    headers.forEach((header, i) => {
      const x = currentX + 2;
      const maxWidth = adjustedColWidths[i] - 4;
      const text = this.pdf.splitTextToSize(String(header), maxWidth)[0];
      this.pdf.text(text, x, this.currentY + 6);
      currentX += adjustedColWidths[i];
    });

    this.currentY += rowHeight;

    // Draw rows with proper spacing
    rows.forEach((row, rowIndex) => {
      this.checkPageBreak(rowHeight + 3);

      // Alternate row colors
      if (rowIndex % 2 === 1) {
        this.pdf.setFillColor(248, 250, 252);
        this.pdf.rect(this.margin, this.currentY, tableWidth, rowHeight, 'F');
      }

      // Row borders
      currentX = this.margin;
      adjustedColWidths.forEach((width, i) => {
        this.pdf.setDrawColor(200, 200, 200);
        this.pdf.setLineWidth(0.1);
        this.pdf.rect(currentX, this.currentY, width, rowHeight);
        currentX += width;
      });

      this.pdf.setFontSize(fontSize);
      this.pdf.setTextColor(COLORS.dark);
      this.pdf.setFont('helvetica', 'normal');

      // Row text with proper clipping
      currentX = this.margin;
      row.forEach((cell, i) => {
        const x = currentX + 2;
        const maxWidth = adjustedColWidths[i] - 4;
        const cellText = String(cell || '');
        const text = this.pdf.splitTextToSize(cellText, maxWidth)[0] || '';

        // Ensure text doesn't overflow
        if (text.length > 0) {
          this.pdf.text(text, x, this.currentY + 6);
        }
        currentX += adjustedColWidths[i];
      });

      this.currentY += rowHeight;
    });

    this.currentY += 8; // Extra spacing after table
  }

  // Add separator line
  addSeparatorLine() {
    this.pdf.setDrawColor(COLORS.secondary);
    this.pdf.setLineWidth(0.5);
    this.pdf.line(this.margin, this.currentY, this.pageWidth - this.margin, this.currentY);
    this.currentY += 8;
  }

  // Check if we need a page break
  checkPageBreak(requiredSpace = 20) {
    if (this.currentY + requiredSpace > this.pageHeight - this.margin) {
      this.pdf.addPage();
      this.currentY = this.margin;
    }
  }

  // Add space
  addSpace(space = 5) {
    this.currentY += space;
  }

  // Add progress bar
  addProgressBar(label, value, maxValue = 100, options = {}) {
    const {
      width = 100,
      height = 8,
      color = COLORS.primary,
      backgroundColor = '#e5e7eb'
    } = options;

    this.checkPageBreak(20);

    // Add label
    this.pdf.setFontSize(FONTS.body);
    this.pdf.setTextColor(COLORS.dark);
    this.pdf.setFont('helvetica', 'normal');
    this.pdf.text(label, this.margin, this.currentY);

    // Add percentage
    this.pdf.setFont('helvetica', 'bold');
    this.pdf.text(`${value}%`, this.margin + width + 10, this.currentY);

    this.currentY += 5;

    // Draw background bar
    this.pdf.setFillColor(backgroundColor);
    this.pdf.rect(this.margin, this.currentY, width, height, 'F');

    // Draw progress bar
    const progressWidth = (value / maxValue) * width;
    this.pdf.setFillColor(color);
    this.pdf.rect(this.margin, this.currentY, progressWidth, height, 'F');

    // Draw border
    this.pdf.setDrawColor(200, 200, 200);
    this.pdf.setLineWidth(0.1);
    this.pdf.rect(this.margin, this.currentY, width, height);

    this.currentY += height + 8;
  }

  // Add status badge
  addStatusBadge(text, status) {
    const colors = {
      enriched: { bg: [34, 197, 94], text: [255, 255, 255] },
      'partially-treated': { bg: [251, 191, 36], text: [0, 0, 0] },
      untreated: { bg: [239, 68, 68], text: [255, 255, 255] },
      default: { bg: [156, 163, 175], text: [255, 255, 255] }
    };

    const color = colors[status] || colors.default;
    const padding = 2;
    const textWidth = this.pdf.getTextWidth(text);
    const badgeWidth = textWidth + (padding * 2);
    const badgeHeight = 6;

    // Draw background
    this.pdf.setFillColor(...color.bg);
    this.pdf.roundedRect(this.margin, this.currentY, badgeWidth, badgeHeight, 1, 1, 'F');

    // Add text
    this.pdf.setFontSize(FONTS.small);
    this.pdf.setTextColor(...color.text);
    this.pdf.setFont('helvetica', 'bold');
    this.pdf.text(text, this.margin + padding, this.currentY + 4);

    return badgeWidth + 5; // Return width for positioning next element
  }

  // Add donut chart with better visualization
  addDonutChart(data, title, options = {}) {
    const {
      centerX = this.margin + 60,
      centerY = this.currentY + 45,
      outerRadius = 30,
      innerRadius = 15
    } = options;

    this.checkPageBreak(100);

    // Add title
    this.pdf.setFontSize(FONTS.heading);
    this.pdf.setTextColor(COLORS.primary);
    this.pdf.setFont('helvetica', 'bold');
    this.pdf.text(title, this.margin, this.currentY);
    this.currentY += 15;

    const total = data.reduce((sum, item) => sum + item.value, 0);
    if (total === 0) {
      this.addParagraph('Aucune donnée à afficher');
      return;
    }

    const colors = [
      [34, 197, 94],   // Green - Enriched
      [251, 191, 36],  // Yellow - Partial
      [239, 68, 68],   // Red - Untreated
      [59, 130, 246],  // Blue
      [168, 85, 247],  // Purple
      [236, 72, 153]   // Pink
    ];

    let currentAngle = -90; // Start from top

    // Draw donut segments
    data.forEach((item, index) => {
      if (item.value > 0) {
        const angle = (item.value / total) * 360;
        const color = colors[index % colors.length];

        this.pdf.setFillColor(...color);

        // Draw arc segment (approximated with lines)
        const steps = Math.max(8, Math.floor(angle / 10));
        const stepAngle = angle / steps;

        for (let i = 0; i < steps; i++) {
          const startAngle = (currentAngle + i * stepAngle) * Math.PI / 180;
          const endAngle = (currentAngle + (i + 1) * stepAngle) * Math.PI / 180;

          // Calculate points for the segment
          const x1 = centerX + innerRadius * Math.cos(startAngle);
          const y1 = centerY + innerRadius * Math.sin(startAngle);
          const x2 = centerX + outerRadius * Math.cos(startAngle);
          const y2 = centerY + outerRadius * Math.sin(startAngle);
          const x3 = centerX + outerRadius * Math.cos(endAngle);
          const y3 = centerY + outerRadius * Math.sin(endAngle);
          const x4 = centerX + innerRadius * Math.cos(endAngle);
          const y4 = centerY + innerRadius * Math.sin(endAngle);

          // Draw the segment as a polygon
          this.pdf.setFillColor(...color);
          this.pdf.triangle(x1, y1, x2, y2, x3, y3, 'F');
          this.pdf.triangle(x1, y1, x3, y3, x4, y4, 'F');
        }

        currentAngle += angle;
      }
    });

    // Add legend
    const legendX = centerX + outerRadius + 20;
    let legendY = centerY - (data.length * 8) / 2;

    data.forEach((item, index) => {
      if (item.value > 0) {
        const color = colors[index % colors.length];
        const percentage = ((item.value / total) * 100).toFixed(1);

        // Legend color box
        this.pdf.setFillColor(...color);
        this.pdf.rect(legendX, legendY - 3, 8, 6, 'F');

        // Legend text
        this.pdf.setFontSize(FONTS.small);
        this.pdf.setTextColor(COLORS.dark);
        this.pdf.setFont('helvetica', 'normal');
        this.pdf.text(`${item.label}: ${item.value} (${percentage}%)`, legendX + 12, legendY);

        legendY += 10;
      }
    });

    this.currentY += 80;
  }

  // Add bar chart
  addBarChart(data, title, options = {}) {
    const {
      chartWidth = 120,
      chartHeight = 60,
      showValues = true
    } = options;

    this.checkPageBreak(chartHeight + 40);

    // Add title
    this.pdf.setFontSize(FONTS.heading);
    this.pdf.setTextColor(COLORS.primary);
    this.pdf.setFont('helvetica', 'bold');
    this.pdf.text(title, this.margin, this.currentY);
    this.currentY += 15;

    const maxValue = Math.max(...data.map(item => item.value));
    if (maxValue === 0) {
      this.addParagraph('Aucune donnée à afficher');
      return;
    }

    const barWidth = chartWidth / data.length * 0.8;
    const barSpacing = chartWidth / data.length * 0.2;
    const chartStartX = this.margin;
    const chartStartY = this.currentY;

    const colors = [
      [34, 197, 94],   // Green
      [251, 191, 36],  // Yellow
      [239, 68, 68],   // Red
      [59, 130, 246],  // Blue
      [168, 85, 247],  // Purple
    ];

    // Draw bars
    data.forEach((item, index) => {
      const barHeight = (item.value / maxValue) * chartHeight;
      const x = chartStartX + index * (barWidth + barSpacing);
      const y = chartStartY + chartHeight - barHeight;

      const color = colors[index % colors.length];
      this.pdf.setFillColor(...color);
      this.pdf.rect(x, y, barWidth, barHeight, 'F');

      // Add value on top of bar
      if (showValues) {
        this.pdf.setFontSize(FONTS.small);
        this.pdf.setTextColor(COLORS.dark);
        this.pdf.setFont('helvetica', 'bold');
        this.pdf.text(String(item.value), x + barWidth/2 - 3, y - 2);
      }

      // Add label below bar
      this.pdf.setFontSize(FONTS.small);
      this.pdf.setTextColor(COLORS.dark);
      this.pdf.setFont('helvetica', 'normal');
      const labelText = item.label.length > 8 ? item.label.substring(0, 8) + '...' : item.label;
      this.pdf.text(labelText, x, chartStartY + chartHeight + 8);
    });

    // Draw axes
    this.pdf.setDrawColor(COLORS.secondary);
    this.pdf.setLineWidth(0.5);
    // Y-axis
    this.pdf.line(chartStartX, chartStartY, chartStartX, chartStartY + chartHeight);
    // X-axis
    this.pdf.line(chartStartX, chartStartY + chartHeight, chartStartX + chartWidth, chartStartY + chartHeight);

    this.currentY += chartHeight + 25;
  }

  // Generate and download PDF
  save(filename) {
    this.addFooter();
    this.pdf.save(filename);
  }

  // Get PDF as blob
  getBlob() {
    this.addFooter();
    return this.pdf.output('blob');
  }
}
