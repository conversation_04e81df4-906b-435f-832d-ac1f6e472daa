import React, { useState, useEffect } from 'react';
import { useAnalysis } from '../../context/AnalysisContext';
import EventProgressionTable from './EventProgressionTable';
import { AlertTriangle, TrendingUp } from 'lucide-react';
import api from '../../api/apiClient';
import { processEventProgressionData } from './dataProcessor';

const EventProgression = () => {
  const { 
    currentAnalysis,
    currentDreadedEvents,
    currentAnalysisControlPlan,
    isWorkshopDataLoading
  } = useAnalysis();
  
  const [ecosystemMeasures, setEcosystemMeasures] = useState([]);
  const [attackPaths, setAttackPaths] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [progressionData, setProgressionData] = useState(null);
  
  // Fetch ecosystem measures and attack paths
  useEffect(() => {
    const fetchData = async () => {
      if (!currentAnalysis?.id) return;
      
      setIsLoading(true);
      try {
        // Fetch ecosystem measures from Atelier 3
        const measuresResponse = await api.get(`/analyses/${currentAnalysis.id}/ecosystem-measures`);
        if (measuresResponse.success) {
          setEcosystemMeasures(measuresResponse.data || []);
        }
        
        // Fetch attack paths
        const pathsResponse = await api.get(`/analyses/${currentAnalysis.id}/attack-paths`);
        if (pathsResponse.success) {
          setAttackPaths(pathsResponse.data || []);
        }
        
        setIsLoading(false);
      } catch (error) {
        console.error('Error fetching data for event progression:', error);
        setIsLoading(false);
      }
    };
    
    fetchData();
  }, [currentAnalysis]);
  
  // Process data to calculate progression metrics
  useEffect(() => {
    if (isLoading || isWorkshopDataLoading) return;
    
    // Process the data to calculate progression metrics
    // The processEventProgressionData function will use demo data if real data is missing
    const processedData = processEventProgressionData(
      currentDreadedEvents,
      currentAnalysisControlPlan,
      ecosystemMeasures,
      attackPaths
    );
    
    setProgressionData(processedData);
  }, [
    isLoading, 
    isWorkshopDataLoading, 
    currentDreadedEvents, 
    currentAnalysisControlPlan, 
    ecosystemMeasures, 
    attackPaths
  ]);
  
  if (isLoading || isWorkshopDataLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
        <span className="ml-3 text-gray-600">Chargement des données...</span>
      </div>
    );
  }
  
  return (
    <div className="bg-gray-50 min-h-screen p-6">
      <div className="max-w-7xl mx-auto">
        <div className="mb-6">
          <div className="flex items-center mb-4">
            <TrendingUp className="h-8 w-8 text-blue-600 mr-3" />
            <h1 className="text-3xl font-bold text-gray-900">
              Progression des Événements Redoutés
            </h1>
          </div>
          <p className="text-gray-600 text-lg">
            Suivi de l'évolution des événements redoutés depuis leur définition dans l'Atelier 1 
            jusqu'à leur traitement détaillé dans l'Atelier 3
          </p>
        </div>
        
        {/* Note about demo data if no real data is available */}
        {(!currentDreadedEvents?.length || !Object.keys(currentAnalysisControlPlan || {}).length) && (
          <div className="bg-blue-50 border-l-4 border-blue-400 p-4 mb-6 rounded-r-lg">
            <div className="flex">
              <AlertTriangle className="text-blue-400 mr-3" size={24} />
              <p className="text-blue-700">
                <strong>Mode Démonstration:</strong> Les données affichées sont des exemples. 
                Pour voir vos données réelles, définissez des événements redoutés dans l'Atelier 1 
                et des mesures de sécurité dans l'Atelier 3.
              </p>
            </div>
          </div>
        )}
        
        {/* Main Content */}
        <EventProgressionTable data={progressionData} />
      </div>
    </div>
  );
};

export default EventProgression;
