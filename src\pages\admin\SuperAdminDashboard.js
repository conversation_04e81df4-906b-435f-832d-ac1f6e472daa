// src/pages/admin/SuperAdminDashboard.jsx
import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { companyService, userService, logService } from '../../services/apiServices';
import { dashboardService } from '../../services/dashboardServices';
import LoadingSpinner from '../../components/common/LoadingSpinner';
import { 
  ActivityChart, 
  UserDistributionChart, 
  CompanyStatusChart,
  MetricsCard
} from '../../components/dashboard/DashboardCharts';

// Icons for metric cards
const ICONS = {
  company: "M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4",
  user: "M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z",
  analysis: "M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
};

const SuperAdminDashboard = () => {
  // State for dashboard data
  const [dashboardData, setDashboardData] = useState({
    metrics: {
      totalCompanies: 0,
      totalUsers: 0,
      activeAnalyses: 0
    },
    recentActivities: [],
    userDistribution: [],
    companyStatuses: [],
    activityTrends: []
  });
  
  // Loading and error states
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  
  // Fetch dashboard data on component mount
  useEffect(() => {
    const fetchDashboardData = async () => {
      setIsLoading(true);
      setError(null);
      
      try {
        // Fetch metrics data
        let metricsData;
        
        try {
          // Try the new dedicated endpoint
          const response = await dashboardService.getSuperAdminMetrics();
          if (response.success) {
            metricsData = response.data;
          }
        } catch (err) {
          console.warn('Dashboard API not available, falling back to individual calls');
          
          // Fallback to multiple API calls if the dedicated endpoint isn't available
          const [companyMetricsResponse, logsResponse] = await Promise.all([
            companyService.getCompanyMetrics(),
            logService.getLogs({ limit: 5, sort: '-timestamp' })
          ]);
          
          metricsData = {
            ...companyMetricsResponse.data,
            recentActivities: logsResponse.data || []
          };
        }
        
        // Prepare user distribution data for charts
        const userDistribution = [
          { name: 'Admins', value: metricsData.usersByRole?.admin || 0 },
          { name: 'Analystes', value: metricsData.usersByRole?.analyst || 0 },
        ];
        
        // Prepare company status data for charts
        const companyStatuses = [
          { name: 'Actives', count: metricsData.companyStatuses?.active || 0 },
          { name: 'Inactives', count: metricsData.companyStatuses?.inactive || 0 }
        ];
        
        // Sample activity trend data (replace with actual data when available)
        const activityTrends = [
          { name: 'Lun', login: 20, create: 5, update: 8 },
          { name: 'Mar', login: 15, create: 3, update: 10 },
          { name: 'Mer', login: 22, create: 7, update: 12 },
          { name: 'Jeu', login: 19, create: 4, update: 9 },
          { name: 'Ven', login: 25, create: 6, update: 15 },
          { name: 'Sam', login: 10, create: 2, update: 5 },
          { name: 'Dim', login: 8, create: 1, update: 3 }
        ];
        
        setDashboardData({
          metrics: {
            totalCompanies: metricsData.totalCompanies || 0,
            totalUsers: metricsData.totalUsers || 0,
            activeAnalyses: metricsData.activeAnalyses || 0
          },
          recentActivities: metricsData.recentActivities || [],
          userDistribution,
          companyStatuses,
          activityTrends
        });
      } catch (error) {
        console.error('Dashboard data fetch error:', error);
        setError('Erreur lors du chargement des données. Veuillez réessayer plus tard.');
      } finally {
        setIsLoading(false);
      }
    };
    
    fetchDashboardData();
  }, []);
  
  // Helper function to determine activity badge color
  const getActivityBadgeColor = (actionType) => {
    if (actionType.includes('LOGIN')) return 'bg-blue-100 text-blue-800';
    if (actionType.includes('CREATE')) return 'bg-green-100 text-green-800';
    if (actionType.includes('UPDATE')) return 'bg-yellow-100 text-yellow-800';
    if (actionType.includes('DELETE')) return 'bg-red-100 text-red-800';
    return 'bg-gray-100 text-gray-800';
  };

  // Show loading spinner while fetching data
  if (isLoading) {
    return (
      <div className="p-6 flex justify-center items-center h-96">
        <LoadingSpinner />
      </div>
    );
  }
  
  // Show error message if something went wrong
  if (error) {
    return (
      <div className="p-6">
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
          <p className="font-medium">Erreur</p>
          <p>{error}</p>
          <button 
            onClick={() => window.location.reload()} 
            className="mt-2 bg-red-100 px-3 py-1 rounded-md hover:bg-red-200"
          >
            Réessayer
          </button>
        </div>
      </div>
    );
  }
  
  const { metrics, recentActivities, userDistribution, companyStatuses, activityTrends } = dashboardData;

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Dashboard Super Administrateur</h1>
        <div className="text-sm text-gray-500">
          <span>Dernière mise à jour : {new Date().toLocaleString()}</span>
        </div>
      </div>
      
      {/* Main metrics cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <MetricsCard 
          title="Entreprises" 
          value={metrics.totalCompanies} 
          icon={ICONS.company} 
          color="blue" 
          linkText="Gérer les entreprises" 
          linkUrl="/superadmin/companies"
        />
        
        <MetricsCard 
          title="Utilisateurs" 
          value={metrics.totalUsers} 
          icon={ICONS.user} 
          color="green" 
          linkText="Gérer les utilisateurs" 
          linkUrl="/superadmin/users"
        />
        
        <MetricsCard 
          title="Analyses actives" 
          value={metrics.activeAnalyses} 
          icon={ICONS.analysis} 
          color="purple" 
          linkText="Voir toutes les analyses" 
          linkUrl="/superadmin/analyses"
        />
      </div>
      
      {/* Charts grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        <ActivityChart data={activityTrends} />
        <UserDistributionChart data={userDistribution} />
      </div>
      
      {/* Recent Activities Card */}
      <div className="bg-white p-6 rounded-lg shadow-md mb-8">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-lg font-semibold">Activités récentes</h2>
          <span className="bg-blue-50 text-blue-700 text-xs font-medium px-2.5 py-0.5 rounded-full">
            Dernières {recentActivities.length} activités
          </span>
        </div>
        
        {recentActivities.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            Aucune activité récente à afficher
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Action</th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Utilisateur</th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Entreprise</th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {recentActivities.map((activity) => (
                  <tr key={activity.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${getActivityBadgeColor(activity.actionType)}`}>
                        {activity.actionType}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {activity.userName || activity.userId}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {activity.companyName || '-'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {new Date(activity.timestamp).toLocaleString()}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
        
        <div className="mt-4 text-right">
          <Link 
            to="/admin/activities" 
            className="text-blue-500 hover:underline inline-flex items-center"
          >
            Voir tous les logs d'activité
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
            </svg>
          </Link>
        </div>
      </div>
      
      {/* Company Status Chart */}
      <div className="bg-white p-6 rounded-lg shadow-md">
        <h2 className="text-lg font-semibold mb-4">Statut des Entreprises</h2>
        <CompanyStatusChart data={companyStatuses} />
      </div>
    </div>
  );
};

export default SuperAdminDashboard;