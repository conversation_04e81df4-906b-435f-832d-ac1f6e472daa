import React from 'react';
import { ChevronDown, ChevronRight, AlertCircle, Target, Link, CheckSquare } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { useAnalysis } from '../../context/AnalysisContext';
import { getUnsavedChanges, setNavigationWarningDialogOpen } from '../../utils/navigationUtils';

// Define tab keys directly
const ATELIER2_TABS = {
    ACTIVITE1: 'atelier2-activite1',
    ACTIVITE2: 'atelier2-activite2',
    ACTIVITE3: 'atelier2-activite3'
};

const Atelier2Navigation = ({
  activeTab,
  setActiveTab,
  expanded,
  toggleExpand,
  baseNavItemClass,
  workshopTitleClass,
  activeStyle,
  inactiveStyle
}) => {
  const { t } = useTranslation();
  // Get analysis context
  const {
    currentAnalysis,
    getSourcesDeRisque,
    loadThreatCategories,
    loadObjectifsVises
  } = useAnalysis();

  // Check if any tab within this workshop is active
  const isAtelierActive = Object.values(ATELIER2_TABS).includes(activeTab);

  // Sub-item class with added padding
  const subItemClass = `${baseNavItemClass} pl-8`; // Indent sub-items

  // Handle click on Identifier SR/OV tab
  const handleActivite1Click = async () => {
    // Check for unsaved changes before navigation
    if (getUnsavedChanges()) {
      // Show the warning dialog
      setNavigationWarningDialogOpen(true);
      // Store the target tab for later use
      localStorage.setItem('ebiosrm_pending_tab', ATELIER2_TABS.ACTIVITE1);
      return;
    }

    // Set the active tab first for immediate UI feedback
    setActiveTab(ATELIER2_TABS.ACTIVITE1);

    // Load data if we have a current analysis
    if (currentAnalysis?.id) {
      try {
        console.log('Atelier2Navigation - Clicking on Identifier SR/OV tab');
        // We'll let the component handle the data loading directly
      } catch (error) {
        console.error('Error loading Atelier 2 Activite 1 data:', error);
      }
    }
  };

  return (
    <li className="w-full">
      <button
        className={`${workshopTitleClass} ${isAtelierActive ? 'text-white' : 'text-gray-300 hover:bg-gray-700 hover:text-white'} group`}
        onClick={toggleExpand}
      >
        <div className="flex items-center w-full justify-between">
          <div className="flex items-center">
            <div className={`${isAtelierActive ? 'bg-blue-500' : 'bg-gray-700 group-hover:bg-gray-600'} p-1.5 rounded-md transition-colors duration-200 mr-3`}>
              <AlertCircle size={16} className="flex-shrink-0 text-white" />
            </div>
            <span className="truncate">{t('workshop2.navigationTitle')}</span>
          </div>
          <div className="text-gray-400 transition-transform duration-200">
            {expanded ? <ChevronDown size={16} /> : <ChevronRight size={16} />}
          </div>
        </div>
      </button>

      {expanded && (
        <ul className="mt-1 space-y-1 w-full">
          <li>
            <button
              className={`${subItemClass} ${activeTab === ATELIER2_TABS.ACTIVITE1 ? activeStyle : inactiveStyle} group`}
              onClick={handleActivite1Click}
            >
              <div className="flex items-center">
                <div className={`${activeTab === ATELIER2_TABS.ACTIVITE1 ? 'bg-blue-500' : 'bg-gray-700 group-hover:bg-gray-600'} p-1 rounded transition-colors duration-200 mr-2`}>
                  <AlertCircle size={14} className="flex-shrink-0 text-white" />
                </div>
                <span className="truncate">{t('workshop2.navigation.identifySROV')}</span>
              </div>
            </button>
          </li>
          <li>
            <button
              className={`${subItemClass} ${activeTab === ATELIER2_TABS.ACTIVITE2 ? activeStyle : inactiveStyle} group`}
              onClick={() => {
                // Check for unsaved changes before navigation
                if (getUnsavedChanges()) {
                  // Show the warning dialog
                  setNavigationWarningDialogOpen(true);
                  // Store the target tab for later use
                  localStorage.setItem('ebiosrm_pending_tab', ATELIER2_TABS.ACTIVITE2);
                  return;
                }
                // If no unsaved changes, navigate directly
                setActiveTab(ATELIER2_TABS.ACTIVITE2);
              }}
            >
              <div className="flex items-center">
                <div className={`${activeTab === ATELIER2_TABS.ACTIVITE2 ? 'bg-blue-500' : 'bg-gray-700 group-hover:bg-gray-600'} p-1 rounded transition-colors duration-200 mr-2`}>
                  <Link size={14} className="flex-shrink-0 text-white" />
                </div>
                <span className="truncate">{t('workshop2.navigation.evaluateSROV')}</span>
              </div>
            </button>
          </li>
          <li>
            <button
              className={`${subItemClass} ${activeTab === ATELIER2_TABS.ACTIVITE3 ? activeStyle : inactiveStyle} group`}
              onClick={() => {
                // Check for unsaved changes before navigation
                if (getUnsavedChanges()) {
                  // Show the warning dialog
                  setNavigationWarningDialogOpen(true);
                  // Store the target tab for later use
                  localStorage.setItem('ebiosrm_pending_tab', ATELIER2_TABS.ACTIVITE3);
                  return;
                }
                // If no unsaved changes, navigate directly
                setActiveTab(ATELIER2_TABS.ACTIVITE3);
              }}
            >
              <div className="flex items-center">
                <div className={`${activeTab === ATELIER2_TABS.ACTIVITE3 ? 'bg-blue-500' : 'bg-gray-700 group-hover:bg-gray-600'} p-1 rounded transition-colors duration-200 mr-2`}>
                  <CheckSquare size={14} className="flex-shrink-0 text-white" />
                </div>
                <span className="truncate">{t('workshop2.navigation.associateSRER')}</span>
              </div>
            </button>
          </li>
        </ul>
      )}
    </li>
  );
};

export default Atelier2Navigation;
