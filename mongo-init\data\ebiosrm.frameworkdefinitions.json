[{"_id": {"$oid": "67ef2a8c8fc56749148e916b"}, "name": "Référentiel Personnalisé 1", "description": "Importé le 04/04/2025", "rules": [{"id": "custom-jhx6c4prk", "name": "Politique de mot de passe robuste", "description": "Obligation d'utiliser des mots de passe complexes avec au moins 12 caractères"}, {"id": "custom-ql484zxvg", "name": "Authentification à deux facteurs", "description": "L'authentification à deux facteurs doit être activée pour tous les accès critiques"}, {"id": "custom-7gigw863k", "name": "Sauvegarde quotidienne des données", "description": "Les données critiques doivent être sauvegardées quotidiennement"}, {"id": "custom-sixsk2s8r", "name": "Chiffrement du stockage de données", "description": "Toutes les données sensibles doivent être chiffrées au repos"}, {"id": "custom-3u2ied6i1", "name": "Contrôle d'accès basé sur les rôles", "description": "Les droits d'accès doivent être définis par rôle et fonction"}, {"id": "custom-iobumnlp1", "name": "Journalisation des événements", "description": "Tous les événements de sécurité doivent être journalisés et conservés 6 mois"}, {"id": "custom-i5gmeo46z", "name": "Analyse des vulnérabilités trimestrielle", "description": "Les systèmes critiques doivent être analysés tous les trimestres"}, {"id": "custom-qoalflvfg", "name": "Plan de reprise d'activité", "description": "Un PRA doit être testé annuellement"}, {"id": "custom-eqtedj0ai", "name": "Formation de sensibilisation annuelle", "description": "Tous les employés doivent suivre une formation de sensibilisation annuelle"}, {"id": "custom-d699qqbdt", "name": "Gestion des correctifs de sécurité", "description": "Les correctifs critiques doivent être appliqués sous 15 jours"}], "is_predefined": false, "analysisId": {"$oid": "67e4a998b8ec79118b11e01a"}, "createdBy": {"$oid": "67e4a836b8ec79118b11dea3"}, "createdAt": {"$date": "2025-04-04T00:40:44.430Z"}, "updatedAt": {"$date": "2025-04-04T00:40:44.430Z"}, "__v": 0}, {"_id": {"$oid": "67ef2e2f8fc56749148e9345"}, "name": "CIS Controls V8", "description": "Importé le 04/04/2025", "rules": [{"id": "custom-fyf1s4xyb", "name": "CIS Control 1: Inventory and Control of Enterprise Assets", "description": "Actively manage (inventory, track, and correct) all enterprise assets (end-user devices, including portable and mobile"}, {"id": "custom-bcvcz7tr4", "name": "CIS Safeguard 1,1: Establish and Maintain Detailed Enterprise Asset Inventory", "description": "(Asset Type: Devices, Security Function: Identify) Establish and maintain an accurate, detailed, and up-to-date inventory of all enterprise assets with the potential to store or process data, to include: end-user devices (including portable and mobile), network devices, non-computing/IoT devices, and servers. Ensure the inventory records the network address (if static), hardware address, machine name, enterprise asset owner, department for each asset, and whether the asset has been approved to connect to the network. For mobile end-user devices, MDM type tools can support this process, where appropriate. This inventory includes assets connected to the infrastructure physically, virtually, remotely, and those within cloud environments. Additionally, it includes assets that are regularly connected to the enterprise’s network infrastructure, even if they are not under control of the enterprise. Review and update the inventory of all enterprise assets bi-annually, or more frequently."}, {"id": "custom-g23shxos1", "name": "CIS Safeguard 1,2: Address Unauthorized Assets", "description": "(Asset Type: Devices, Security Function: Respond) Ensure that a process exists to address unauthorized assets on a weekly basis. The enterprise may choose to remove the asset from the network, deny the asset from connecting remotely to the network, or quarantine the asset."}, {"id": "custom-22z1mijmb", "name": "CIS Safeguard 1,3: Utilize an Active Discovery Tool", "description": "(Asset Type: Devices, Security Function: Detect) Utilize an active discovery tool to identify assets connected to the enterprise’s network. Configure the active discovery tool to execute daily, or more frequently."}, {"id": "custom-0z9bmu5vl", "name": "CIS Safeguard 1,4: Use Dynamic Host Configuration Protocol (DHCP) Logging to Update Enterprise Asset Inventory", "description": "(Asset Type: Devices, Security Function: Identify) Use DHCP logging on all DHCP servers or Internet Protocol (IP) address management tools to update the enterprise’s asset inventory. Review and use logs to update the enterprise’s asset inventory weekly, or more frequently."}, {"id": "custom-kf6wdbkbp", "name": "CIS Safeguard 1,5: Use a Passive Asset Discovery Tool", "description": "(Asset Type: Devices, Security Function: Detect) Use a passive discovery tool to identify assets connected to the enterprise’s network. Review and use scans to update the enterprise’s asset inventory at least weekly, or more frequently."}, {"id": "custom-auxmt6s17", "name": "CIS Control 2: Inventory and Control of Software Assets", "description": "Actively manage (inventory, track, and correct) all software (operating systems and applications) on the network so that only authorized software is installed and can execute, and that unauthorized and unmanaged software is found and prevented from installation or execution."}, {"id": "custom-lxz1djtxd", "name": "CIS Safeguard 2,1: Establish and Maintain a Software Inventory", "description": "(Asset Type: Applications, Security Function: Identify) Establish and maintain a detailed inventory of all licensed software installed on enterprise assets. The software inventory must document the title, publisher, initial install/use date, and business purpose for each entry"}, {"id": "custom-15j7vphh1", "name": "CIS Safeguard 2,2: Ensure Authorized Software is Currently Supported", "description": "(Asset Type: Applications, Security Function: Identify) Ensure that only currently supported software is designated as authorized in the software inventory for enterprise assets. If software is unsupported, yet necessary for the fulfillment of the enterprise’s mission, document an exception detailing mitigating controls and residual risk acceptance. For any unsupported software without an exception documentation, designate as unauthorized. Review the software list to verify software support at least monthly, or more frequently."}, {"id": "custom-lidx5iaif", "name": "CIS Safeguard 2,3: Address Unauthorized Software", "description": "(Asset Type: Applications, Security Function: Respond) Ensure that unauthorized software is either removed from use on enterprise assets or receives a documented exception. Review monthly, or more frequently."}, {"id": "custom-namphpt9k", "name": "CIS Safeguard 2,4: Utilize Automated Software Inventory Tools", "description": "(Asset Type: Applications, Security Function: Detect) Utilize software inventory tools, when possible, throughout the enterprise to automate the discovery and documentation of installed software."}, {"id": "custom-kaczivfnw", "name": "CIS Safeguard 2,5: Allowlist Authorized Software", "description": "(Asset Type: Applications, Security Function: Protect) Use technical controls, such as application allowlisting, to ensure that only authorized software can execute or be accessed. Reassess bi-annually, or more frequently."}, {"id": "custom-jfv3re918", "name": "CIS Safeguard 2,6: Allowlist Authorized Libraries", "description": "(Asset Type: Applications, Security Function: Protect) Use technical controls to ensure that only authorized software libraries, such as specific .dll, .ocx, .so, etc., files, are allowed to load into a system process. Block unauthorized libraries from loading into a system process. Reassess bi-annually, or more frequently."}, {"id": "custom-66to5fpas", "name": "CIS Safeguard 2,7: <PERSON>owlist Author<PERSON> Scripts", "description": "(Asset Type: Applications, Security Function: Protect) Use technical controls, such as digital signatures and version control, to ensure that only authorized scripts, such as specific .ps1, .py, etc., files, are allowed to execute. Block unauthorized scripts from executing. Reassess bi-annually, or more frequently."}, {"id": "custom-mh100vc1p", "name": "CIS Control 3: Data Protection", "description": "Develop processes and technical controls to identify, classify, securely handle, retain, and dispose of data."}, {"id": "custom-dtckcps5c", "name": "CIS Safeguard 3,1: Establish and Maintain a Data Management Process", "description": "(Asset Type: Data, Security Function: Identify) Establish and maintain a data management process. In the process, address data sensitivity, data owner, handling of data, data retention limits, and disposal requirements, based on sensitivity and retention standards for the enterprise. Review and update documentation annually, or when significant enterprise changes occur that could impact this Safeguard."}, {"id": "custom-4cwtxud95", "name": "CIS Safeguard 3,2: Establish and Maintain a Data Inventory", "description": "(Asset Type: Data, Security Function: Identify) Establish and maintain a data inventory, based on the enterprise’s data management process. Inventory sensitive data, at a minimum. Review and update inventory annually, at a minimum, with a priority on sensitive data."}, {"id": "custom-3p7r0632v", "name": "CIS Safeguard 3,3: Configure Data Access Control Lists", "description": "(Asset Type: Data, Security Function: Protect) Configure data access control lists based on a user’s need to know. Apply data access control lists, also known as access permissions, to local and remote file systems, databases, and applications."}, {"id": "custom-9571g5svg", "name": "CIS Safeguard 3,4: Enforce Data Retention", "description": "(Asset Type: Data, Security Function: Protect) Retain data according to the enterprise’s data management process. Data retention must include both minimum and maximum timelines."}, {"id": "custom-65vttllj8", "name": "CIS Safeguard 3,5: Securely Dispose of Data", "description": "(Asset Type: Data, Security Function: Protect) Securely dispose of data as outlined in the enterprise’s data management process. Ensure the disposal process and method are commensurate with the data sensitivity."}, {"id": "custom-uk1vbfg24", "name": "CIS Safeguard 3,6: Encrypt Data on End-User Devices", "description": "(Asset Type: Devices, Security Function: Protect) Encrypt data on end-user devices containing sensitive data. Example implementations can include: Windows BitLocker®, Apple FileVault®, Linux® dm-crypt."}, {"id": "custom-vdoqj4tez", "name": "CIS Safeguard 3,7: Establish and Maintain a Data Classification Scheme", "description": "(Asset Type: Data, Security Function: Identify) Establish and maintain an overall data classification scheme for the enterprise. Enterprises may use labels, such as “Sensitive,” “Confidential,” and “Public,” and classify their data according to those labels. Review and update the classification scheme annually, or when significant enterprise changes occur that could impact this Safeguard."}, {"id": "custom-dwaydyhlm", "name": "CIS Safeguard 3,8: Document Data Flows", "description": "(Asset Type: Data, Security Function: Identify) Document data flows. Data flow documentation includes service provider data flows and should be based on the enterprise’s data management process. Review and update documentation annually, or when significant enterprise changes occur that could impact this Safeguard."}, {"id": "custom-ywuz8r5e5", "name": "CIS Safeguard 3,9: Encrypt Data on Removable Media", "description": "(Asset Type: Data, Security Function: Protect) Encrypt data on removable media."}, {"id": "custom-75za880n4", "name": "CIS Safeguard 3,10: Encrypt Sensitive Data in Transit", "description": "(Asset Type: Data, Security Function: Protect) Encrypt sensitive data in transit. Example implementations can include: Transport Layer Security (TLS) and Open Secure Shell (OpenSSH)."}, {"id": "custom-12oophwaw", "name": "CIS Safeguard 3,11: Encrypt Sensitive Data at Rest", "description": "(Asset Type: Data, Security Function: Protect) Encrypt sensitive data at rest on servers, applications, and databases containing sensitive data. Storage-layer encryption, also known as server-side encryption, meets the minimum requirement of this Safeguard. Additional encryption methods may include application-layer encryption, also known as client-side encryption, where access to the data storage device(s) does not permit access to the plain-text data."}, {"id": "custom-ujg9hg2bq", "name": "CIS Safeguard 3,12: Segment Data Processing and Storage Based on Sensitivity", "description": "(Asset Type: Network, Security Function: Protect) Segment data processing and storage based on the sensitivity of the data. Do not process sensitive data on enterprise assets intended for lower sensitivity data."}, {"id": "custom-g4xriadlh", "name": "CIS Safeguard 3,13: Deploy a Data Loss Prevention Solution", "description": "(Asset Type: Data, Security Function: Protect) Implement an automated tool, such as a host-based Data Loss Prevention (DLP) tool to identify all sensitive data stored, processed, or transmitted through enterprise assets, including those located onsite or at a remote service provider, and update the enterprise's sensitive data inventory."}, {"id": "custom-de0m63is4", "name": "CIS Safeguard 3,14: Log Sensitive Data Access", "description": "(Asset Type: Data, Security Function: Detect) Log sensitive data access, including modification and disposal."}, {"id": "custom-p14fiorfb", "name": "CIS Control 4: Secure Configuration of Enterprise Assets and Software", "description": "Establish and maintain the secure configuration of enterprise assets (end-user devices, including portable and mobile"}, {"id": "custom-jef2dmxqw", "name": "CIS Safeguard 4,1: Establish and Maintain a Secure Configuration Process", "description": "(Asset Type: Applications, Security Function: Protect) Establish and maintain a secure configuration process for enterprise assets (end-user devices, including portable and mobile, non-computing/IoT devices, and servers) and software (operating systems and applications). Review and update documentation annually, or when significant enterprise changes occur that could impact this Safeguard."}, {"id": "custom-03qybnrze", "name": "CIS Safeguard 4,2: Establish and Maintain a Secure Configuration Process for Network Infrastructure", "description": "(Asset Type: Network, Security Function: Protect) Establish and maintain a secure configuration process for network devices. Review and update documentation annually, or when significant enterprise changes occur that could impact this Safeguard."}, {"id": "custom-lsesesm4p", "name": "CIS Safeguard 4,3: Configure Automatic Session Locking on Enterprise Assets", "description": "(Asset Type: Users, Security Function: Protect) Configure automatic session locking on enterprise assets after a defined period of inactivity. For general purpose operating systems, the period must not exceed 15 minutes. For mobile end-user devices, the period must not exceed 2 minutes."}, {"id": "custom-7dxm9cfs7", "name": "CIS Safeguard 4,4: Implement and Manage a Firewall on Servers", "description": "(Asset Type: Devices, Security Function: Protect) Implement and manage a firewall on servers, where supported. Example implementations include a virtual firewall, operating system firewall, or a third-party firewall agent."}, {"id": "custom-9521s0kdb", "name": "CIS Safeguard 4,5: Implement and Manage a Firewall on End-User Devices", "description": "(Asset Type: Devices, Security Function: Protect) Implement and manage a host-based firewall or port-filtering tool on end-user devices, with a default-deny rule that drops all traffic except those services and ports that are explicitly allowed."}, {"id": "custom-0w8o7sr3h", "name": "CIS Safeguard 4,6: Securely Manage Enterprise Assets and Software", "description": "(Asset Type: Network, Security Function: Protect) Securely manage enterprise assets and software. Example implementations include managing configuration through version-controlled-infrastructure-as-code and accessing administrative interfaces over secure network protocols, such as Secure Shell (SSH) and Hypertext Transfer Protocol Secure (HTTPS). Do not use insecure management protocols, such as Telnet (Teletype Network) and HTTP, unless operationally essential."}, {"id": "custom-7j3xdlo4e", "name": "CIS Safeguard 4,7: <PERSON><PERSON> Default Accounts on Enterprise Assets and Software", "description": "(Asset Type: Users, Security Function: Protect) Manage default accounts on enterprise assets and software, such as root, administrator, and other pre-configured vendor accounts. Example implementations can include: disabling default accounts or making them unusable."}, {"id": "custom-ti58qtmuy", "name": "CIS Safeguard 4,8: Uninstall or Disable Unnecessary Services on Enterprise Assets and Software", "description": "(Asset Type: Devices, Security Function: Protect) Uninstall or disable unnecessary services on enterprise assets and software, such as an unused file sharing service, web application module, or service function."}, {"id": "custom-983ldamq6", "name": "CIS Safeguard 4,9: Configure Trusted DNS Servers on Enterprise Assets", "description": "(Asset Type: Devices, Security Function: Protect) Configure trusted DNS servers on enterprise assets. Example implementations include: configuring assets to use enterprise-controlled DNS servers and/or reputable externally accessible DNS servers."}, {"id": "custom-c537mc9g9", "name": "CIS Safeguard 4,10: Enforce Automatic Device Lockout on Portable End-User Devices", "description": "(Asset Type: Devices, Security Function: Respond) Enforce automatic device lockout following a predetermined threshold of local failed authentication attempts on portable end-user devices, where supported. For laptops, do not allow more than 20 failed authentication attempts"}, {"id": "custom-hvfd2evxv", "name": "CIS Safeguard 4,11: Enforce Remote Wipe Capability on Portable End-User Devices", "description": "(Asset Type: Devices, Security Function: Protect) Remotely wipe enterprise data from enterprise-owned portable end-user devices when deemed appropriate such as lost or stolen devices, or when an individual no longer supports the enterprise."}, {"id": "custom-jer7iobcb", "name": "CIS Safeguard 4,12: Separate Enterprise Workspaces on Mobile End-User Devices", "description": "(Asset Type: Devices, Security Function: Protect) Ensure separate enterprise workspaces are used on mobile end-user devices, where supported. Example implementations include using an Apple® Configuration Profile or Android™ Work Profile to separate enterprise applications and data from personal applications and data."}, {"id": "custom-nmhjg248d", "name": "CIS Control 5: Account Management", "description": "Use processes and tools to assign and manage authorization to credentials for user accounts, including administrator accounts, as well as service accounts, to enterprise assets and software."}, {"id": "custom-4epl8c90b", "name": "CIS Safeguard 5,1: <PERSON><PERSON><PERSON>lish and Maintain an Inventory of Accounts", "description": "(Asset Type: Users, Security Function: Identify) Establish and maintain an inventory of all accounts managed in the enterprise. The inventory must include both user and administrator accounts. The inventory, at a minimum, should contain the person’s name, username, start/stop dates, and department. Validate that all active accounts are authorized, on a recurring schedule at a minimum quarterly, or more frequently."}, {"id": "custom-gx97bewgr", "name": "CIS Safeguard 5,2: Use Unique Passwords", "description": "(Asset Type: Users, Security Function: Protect) Use unique passwords for all enterprise assets. Best practice implementation includes, at a minimum, an 8-character password for accounts using MFA and a 14-character password for accounts not using MFA."}, {"id": "custom-98bksl63x", "name": "CIS Safeguard 5,3: <PERSON><PERSON> <PERSON><PERSON><PERSON> Accounts", "description": "(Asset Type: Users, Security Function: Respond) Delete or disable any dormant accounts after a period of 45 days of inactivity, where supported."}, {"id": "custom-j2afx2akb", "name": "CIS Safeguard 5,4: Restrict Administrator Privileges to Dedicated Administrator Accounts", "description": "(Asset Type: Users, Security Function: Protect) Restrict administrator privileges to dedicated administrator accounts on enterprise assets. Conduct general computing activities, such as internet browsing, email, and productivity suite use, from the user’s primary, non-privileged account."}, {"id": "custom-a25824ndi", "name": "CIS Safeguard 5,5: <PERSON><PERSON>blish and Maintain an Inventory of Service Accounts", "description": "(Asset Type: Users, Security Function: Identify) Establish and maintain an inventory of service accounts. The inventory, at a minimum, must contain department owner, review date, and purpose. Perform service account reviews to validate that all active accounts are authorized, on a recurring schedule at a minimum quarterly, or more frequently."}, {"id": "custom-nuhcr02sg", "name": "CIS Safeguard 5,6: Centralize Account Management", "description": "(Asset Type: Users, Security Function: Protect) Centralize account management through a directory or identity service."}, {"id": "custom-l5jcgdlgp", "name": "CIS Control 6: Access Control Management", "description": "Use processes and tools to create, assign, manage, and revoke access credentials and privileges for user, administrator, and service accounts for enterprise assets and software."}, {"id": "custom-7h61gifoe", "name": "CIS Safeguard 6,1: Establish an Access Granting Process", "description": "(Asset Type: Users, Security Function: Protect) Establish and follow a process, preferably automated, for granting access to enterprise assets upon new hire, rights grant, or role change of a user."}, {"id": "custom-la5gqvi98", "name": "CIS Safeguard 6,2: Establish an Access Revoking Process", "description": "(Asset Type: Users, Security Function: Protect) Establish and follow a process, preferably automated, for revoking access to enterprise assets, through disabling accounts immediately upon termination, rights revocation, or role change of a user. Disabling accounts, instead of deleting accounts, may be necessary to preserve audit trails."}, {"id": "custom-mq03wg28a", "name": "CIS Safeguard 6,3: Require MFA for Externally-Exposed Applications", "description": "(Asset Type: Users, Security Function: Protect) Require all externally-exposed enterprise or third-party applications to enforce MFA, where supported. Enforcing MFA through a directory service or SSO provider is a satisfactory implementation of this Safeguard."}, {"id": "custom-gteaz6x02", "name": "CIS Safeguard 6,4: Require MFA for Remote Network Access", "description": "(Asset Type: Users, Security Function: Protect) Require MFA for remote network access."}, {"id": "custom-pffqzxnlt", "name": "CIS Safeguard 6,5: Require M<PERSON> for Administrative Access", "description": "(Asset Type: Users, Security Function: Protect) Require MFA for all administrative access accounts, where supported, on all enterprise assets, whether managed on-site or through a third-party provider."}, {"id": "custom-fst5sa97m", "name": "CIS Safeguard 6,6: <PERSON><PERSON>blish and Maintain an Inventory of Authentication and Authorization Systems", "description": "(Asset Type: Users, Security Function: Identify) Establish and maintain an inventory of the enterprise’s authentication and authorization systems, including those hosted on-site or at a remote service provider. Review and update the inventory, at a minimum, annually, or more frequently."}, {"id": "custom-rq44z2lvv", "name": "CIS Safeguard 6,7: Centralize Access Control", "description": "(Asset Type: Users, Security Function: Protect) Centralize access control for all enterprise assets through a directory service or SSO provider, where supported."}, {"id": "custom-3shcmx648", "name": "CIS Safeguard 6,8: Define and Maintain Role-Based Access Control", "description": "(Asset Type: Data, Security Function: Protect) Define and maintain role-based access control, through determining and documenting the access rights necessary for each role within the enterprise to successfully carry out its assigned duties. Perform access control reviews of enterprise assets to validate that all privileges are authorized, on a recurring schedule at a minimum annually, or more frequently."}, {"id": "custom-436uwkjry", "name": "CIS Control 7: Continuous Vulnerability Management", "description": "Develop a plan to continuously assess and track vulnerabilities on all enterprise assets within the enterprise’s infrastructure, in order to remediate, and minimize, the window of opportunity for attackers. Monitor public and private industry sources for new threat and vulnerability information."}, {"id": "custom-cwb1d1ial", "name": "CIS Safeguard 7,1: Establish and Maintain a Vulnerability Management Process", "description": "(Asset Type: Applications, Security Function: Protect) Establish and maintain a documented vulnerability management process for enterprise assets. Review and update documentation annually, or when significant enterprise changes occur that could impact this Safeguard."}, {"id": "custom-8eftdqy0v", "name": "CIS Safeguard 7,2: Establish and Maintain a Remediation Process", "description": "(Asset Type: Applications, Security Function: Respond) Establish and maintain a risk-based remediation strategy documented in a remediation process, with monthly, or more frequent, reviews."}, {"id": "custom-efe6tgj3q", "name": "CIS Safeguard 7,3: Perform Automated Operating System Patch Management", "description": "(Asset Type: Applications, Security Function: Protect) Perform operating system updates on enterprise assets through automated patch management on a monthly, or more frequent, basis."}, {"id": "custom-9se6iui2c", "name": "CIS Safeguard 7,4: Perform Automated Application Patch Management", "description": "(Asset Type: Applications, Security Function: Protect) Perform application updates on enterprise assets through automated patch management on a monthly, or more frequent, basis."}, {"id": "custom-9jmvz7byf", "name": "CIS Safeguard 7,5: Perform Automated Vulnerability Scans of Internal Enterprise Assets", "description": "(Asset Type: Applications, Security Function: Identify) Perform automated vulnerability scans of internal enterprise assets on a quarterly, or more frequent, basis. Conduct both authenticated and unauthenticated scans, using a SCAP-compliant vulnerability scanning tool."}, {"id": "custom-t2t63xfb5", "name": "CIS Safeguard 7,6: Perform Automated Vulnerability Scans of Externally-Exposed Enterprise Assets", "description": "(Asset Type: Applications, Security Function: Identify) Perform automated vulnerability scans of externally-exposed enterprise assets using a SCAP-compliant vulnerability scanning tool. Perform scans on a monthly, or more frequent, basis."}, {"id": "custom-3na5tckew", "name": "CIS Safeguard 7,7: Remediate Detected Vulnerabilities", "description": "(Asset Type: Applications, Security Function: Respond) Remediate detected vulnerabilities in software through processes and tooling on a monthly, or more frequent, basis, based on the remediation process."}, {"id": "custom-x5aeb7pti", "name": "CIS Control 8: Audit Log Management", "description": "Collect, alert, review, and retain audit logs of events that could help detect, understand, or recover from an attack."}, {"id": "custom-39kl8jqi6", "name": "CIS Safeguard 8,1: Establish and Maintain an Audit Log Management Process", "description": "(Asset Type: Network, Security Function: Protect) Establish and maintain an audit log management process that defines the enterprise’s logging requirements. At a minimum, address the collection, review, and retention of audit logs for enterprise assets. Review and update documentation annually, or when significant enterprise changes occur that could impact this Safeguard."}, {"id": "custom-icsbl7kq0", "name": "CIS Safeguard 8,2: Collect <PERSON>t Logs", "description": "(Asset Type: Network, Security Function: Detect) Collect audit logs. Ensure that logging, per the enterprise’s audit log management process, has been enabled across enterprise assets."}, {"id": "custom-j8jax80ai", "name": "CIS Safeguard 8,3: Ensure Adequate Audit Log Storage", "description": "(Asset Type: Network, Security Function: Protect) Ensure that logging destinations maintain adequate storage to comply with the enterprise’s audit log management process."}, {"id": "custom-0y6k6apwe", "name": "CIS Safeguard 8,4: Standardize Time Synchronization", "description": "(Asset Type: Network, Security Function: Protect) Standardize time synchronization. Configure at least two synchronized time sources across enterprise assets, where supported."}, {"id": "custom-xfh719tez", "name": "CIS Safeguard 8,5: Collect Detailed Audit Logs", "description": "(Asset Type: Network, Security Function: Detect) Configure detailed audit logging for enterprise assets containing sensitive data. Include event source, date, username, timestamp, source addresses, destination addresses, and other useful elements that could assist in a forensic investigation."}, {"id": "custom-219622bod", "name": "CIS Safeguard 8,6: Collect DNS Query Audit Logs", "description": "(Asset Type: Network, Security Function: Detect) Collect DNS query audit logs on enterprise assets, where appropriate and supported."}, {"id": "custom-q5gg0vx1p", "name": "CIS Safeguard 8,7: Collect URL Request Audit Logs", "description": "(Asset Type: Network, Security Function: Detect) Collect URL request audit logs on enterprise assets, where appropriate and supported."}, {"id": "custom-ixj<PERSON><PERSON><PERSON><PERSON>", "name": "CIS Safeguard 8,8: Collect Command-Line Audit Logs", "description": "(Asset Type: Devices, Security Function: Detect) Collect command-line audit logs. Example implementations include collecting audit logs from PowerShell®, BASH™, and remote administrative terminals."}, {"id": "custom-5uq7g0pqp", "name": "CIS Safeguard 8,9: Centralize Audit Logs", "description": "(Asset Type: Network, Security Function: Detect) Centralize, to the extent possible, audit log collection and retention across enterprise assets."}, {"id": "custom-4815rnhua", "name": "CIS Safeguard 8,10: <PERSON><PERSON>t Logs", "description": "(Asset Type: Network, Security Function: Protect) Retain audit logs across enterprise assets for a minimum of 90 days."}, {"id": "custom-ty4li9ope", "name": "CIS Safeguard 8,11: Conduct Audit Log Reviews", "description": "(Asset Type: Network, Security Function: Detect) Conduct reviews of audit logs to detect anomalies or abnormal events that could indicate a potential threat. Conduct reviews on a weekly, or more frequent, basis."}, {"id": "custom-m9ztd4rda", "name": "CIS Safeguard 8,12: Collect Service Provider Logs", "description": "(Asset Type: Data, Security Function: Detect) Collect service provider logs, where supported. Example implementations include collecting authentication and authorization events, data creation and disposal events, and user management events."}, {"id": "custom-pskhiids1", "name": "CIS Control 9: Email and Web Browser Protections", "description": "Improve protections and detections of threats from email and web vectors, as these are opportunities for attackers to manipulate human behavior through direct engagement."}, {"id": "custom-z1ovt92aw", "name": "CIS Safeguard 9,1: Ensure Use of Only Fully Supported Browsers and Email Clients", "description": "(Asset Type: Applications, Security Function: Protect) Ensure only fully supported browsers and email clients are allowed to execute in the enterprise, only using the latest version of browsers and email clients provided through the vendor."}, {"id": "custom-l7t693e2k", "name": "CIS Safeguard 9,2: Use DNS Filtering Services", "description": "(Asset Type: Network, Security Function: Protect) Use DNS filtering services on all enterprise assets to block access to known malicious domains."}, {"id": "custom-ahyj3pe45", "name": "CIS Safeguard 9,3: Maintain and Enforce Network-Based URL Filters", "description": "(Asset Type: Network, Security Function: Protect) Enforce and update network-based URL filters to limit an enterprise asset from connecting to potentially malicious or unapproved websites. Example implementations include category-based filtering, reputation-based filtering, or through the use of block lists. Enforce filters for all enterprise assets."}, {"id": "custom-j2a68anlk", "name": "CIS Safeguard 9,4: Restrict Unnecessary or Unauthorized <PERSON><PERSON><PERSON> and Email Client Extensions", "description": "(Asset Type: Applications, Security Function: Protect) Restrict, either through uninstalling or disabling, any unauthorized or unnecessary browser or email client plugins, extensions, and add-on applications."}, {"id": "custom-l499k6yjd", "name": "CIS Safeguard 9,5: Implement DMARC", "description": "(Asset Type: Network, Security Function: Protect) To lower the chance of spoofed or modified emails from valid domains, implement DMARC policy and verification, starting with implementing the Sender Policy Framework (SPF) and the DomainKeys Identified Mail (DKIM) standards."}, {"id": "custom-yroreohms", "name": "CIS Safeguard 9,6: Block Unnecessary File Types", "description": "(Asset Type: Network, Security Function: Protect) Block unnecessary file types attempting to enter the enterprise’s email gateway."}, {"id": "custom-4hdslifey", "name": "CIS Safeguard 9,7: Deploy and Maintain Email Server Anti-Malware Protections", "description": "(Asset Type: Network, Security Function: Protect) Deploy and maintain email server anti-malware protections, such as attachment scanning and/or sandboxing."}, {"id": "custom-p4hszuybs", "name": "CIS Control 10: Malware Defenses", "description": "Prevent or control the installation, spread, and execution of malicious applications, code, or scripts on enterprise assets."}, {"id": "custom-y28wbc1wb", "name": "CIS Safeguard 10,1: Deploy and Maintain Anti-Malware Software", "description": "(Asset Type: Devices, Security Function: Protect) Deploy and maintain anti-malware software on all enterprise assets."}, {"id": "custom-msguh7uom", "name": "CIS Safeguard 10,2: Configure Automatic Anti-Malware Signature Updates", "description": "(Asset Type: Devices, Security Function: Protect) Configure automatic updates for anti-malware signature files on all enterprise assets."}, {"id": "custom-m400wak5u", "name": "CIS Safeguard 10,3: Disable Autorun and Autoplay for Removable Media", "description": "(Asset Type: Devices, Security Function: Protect) Disable autorun and autoplay auto-execute functionality for removable media."}, {"id": "custom-zf62wk1tb", "name": "CIS Safeguard 10,4: Configure Automatic Anti-Malware Scanning of Removable Media", "description": "(Asset Type: Devices, Security Function: Detect) Configure anti-malware software to automatically scan removable media."}, {"id": "custom-00f2oxckg", "name": "CIS Safeguard 10,5: Enable Anti-Exploitation Features", "description": "(Asset Type: Devices, Security Function: Protect) Enable anti-exploitation features on enterprise assets and software, where possible, such as Microsoft® Data Execution Prevention (DEP), Windows® Defender Exploit Guard (WDEG), or Apple® System Integrity Protection (SIP) and Gatekeeper™."}, {"id": "custom-dqzfnidnw", "name": "CIS Safeguard 10,6: Centrally Manage Anti-Malware Software", "description": "(Asset Type: Devices, Security Function: Protect) Centrally manage anti-malware software."}, {"id": "custom-xvhn9yt01", "name": "CIS Safeguard 10,7: Use Behavior-Based Anti-Malware Software", "description": "(Asset Type: Devices, Security Function: Detect) Use behavior-based anti-malware software."}, {"id": "custom-z491h1onq", "name": "CIS Control 11: Data Recovery", "description": "Establish and maintain data recovery practices sufficient to restore in-scope enterprise assets to a pre-incident and trusted state."}, {"id": "custom-smvm0kvo3", "name": "CIS Safeguard 11,1: Establish and Maintain a Data Recovery Process", "description": "(Asset Type: Data, Security Function: Recover) Establish and maintain a data recovery process. In the process, address the scope of data recovery activities, recovery prioritization, and the security of backup data. Review and update documentation annually, or when significant enterprise changes occur that could impact this Safeguard."}, {"id": "custom-96blj4hys", "name": "CIS Safeguard 11,2: Perform Automated Backups", "description": "(Asset Type: Data, Security Function: Recover) Perform automated backups of in-scope enterprise assets. Run backups weekly, or more frequently, based on the sensitivity of the data."}, {"id": "custom-3rad2eh8k", "name": "CIS Safeguard 11,3: Protect Recovery Data", "description": "(Asset Type: Data, Security Function: Protect) Protect recovery data with equivalent controls to the original data. Reference encryption or data separation, based on requirements."}, {"id": "custom-hy3kne78k", "name": "CIS Safeguard 11,4: Establish and Maintain an Isolated Instance of Recovery Data", "description": "(Asset Type: Data, Security Function: Recover) Establish and maintain an isolated instance of recovery data. Example implementations include, version controlling backup destinations through offline, cloud, or off-site systems or services."}, {"id": "custom-is8xgslpl", "name": "CIS Safeguard 11,5: Test Data Recovery", "description": "(Asset Type: Data, Security Function: Recover) Test backup recovery quarterly, or more frequently, for a sampling of in-scope enterprise assets."}, {"id": "custom-8wuoybttu", "name": "CIS Control 12: Network Infrastructure Management", "description": "Establish, implement, and actively manage (track, report, correct) network devices, in order to prevent attackers from exploiting vulnerable network services and access points."}, {"id": "custom-4bqwdze0d", "name": "CIS Safeguard 12,1: Ensure Network Infrastructure is Up-to-Date", "description": "(Asset Type: Network, Security Function: Protect) Ensure network infrastructure is kept up-to-date. Example implementations include running the latest stable release of software and/or using currently supported network-as-a-service (NaaS) offerings. Review software versions monthly, or more frequently, to verify software support."}, {"id": "custom-gd4dpgel0", "name": "CIS Safeguard 12,2: Establish and Maintain a Secure Network Architecture", "description": "(Asset Type: Network, Security Function: Protect) Establish and maintain a secure network architecture. A secure network architecture must address segmentation, least privilege, and availability, at a minimum."}, {"id": "custom-c6g2e581c", "name": "CIS Safeguard 12,3: Securely Manage Network Infrastructure", "description": "(Asset Type: Network, Security Function: Protect) Securely manage network infrastructure. Example implementations include version-controlled-infrastructure-as-code, and the use of secure network protocols, such as SSH and HTTPS."}, {"id": "custom-fncb42zpq", "name": "CIS Safeguard 12,4: Establish and Maintain Architecture Diagram(s)", "description": "(Asset Type: Network, Security Function: Identify) Establish and maintain architecture diagram(s) and/or other network system documentation. Review and update documentation annually, or when significant enterprise changes occur that could impact this Safeguard."}, {"id": "custom-pzmw59qs2", "name": "CIS Safeguard 12,5: Centralize Network Authentication, Authorization, and Auditing (AAA)", "description": "(Asset Type: Network, Security Function: Protect) Centralize network AAA."}, {"id": "custom-mxa4feq4y", "name": "CIS Safeguard 12,6: Use of Secure Network Management and Communication Protocols", "description": "(Asset Type: Network, Security Function: Protect) Use secure network management and communication protocols (e.g., 802.1X, Wi-Fi Protected Access 2 (WPA2) Enterprise or greater)."}, {"id": "custom-me6zqo5ht", "name": "CIS Safeguard 12,7: Ensure Remote Devices Utilize a VPN and are Connecting to an Enterprise’s AAA Infrastructure", "description": "(Asset Type: Devices, Security Function: Protect) Require users to authenticate to enterprise-managed VPN and authentication services prior to accessing enterprise resources on end-user devices."}, {"id": "custom-jzrqg4kn3", "name": "CIS Safeguard 12,8: Establish and Maintain Dedicated Computing Resources for All Administrative Work", "description": "(Asset Type: Devices, Security Function: Protect) Establish and maintain dedicated computing resources, either physically or logically separated, for all administrative tasks or tasks requiring administrative access. The computing resources should be segmented from the enterprise's primary network and not be allowed internet access."}, {"id": "custom-5v2wsc45u", "name": "CIS Control 13: Network Monitoring and Defense", "description": "Operate processes and tooling to establish and maintain comprehensive network monitoring and defense against security threats across the enterprise’s network infrastructure and user base."}, {"id": "custom-9hdqzijjw", "name": "CIS Safeguard 13,1: Centralize Security Event Alerting", "description": "(Asset Type: Network, Security Function: Detect) Centralize security event alerting across enterprise assets for log correlation and analysis. Best practice implementation requires the use of a SIEM, which includes vendor-defined event correlation alerts. A log analytics platform configured with security-relevant correlation alerts also satisfies this Safeguard."}, {"id": "custom-deuk1adg9", "name": "CIS Safeguard 13,2: Deploy a Host-Based Intrusion Detection Solution", "description": "(Asset Type: Devices, Security Function: Detect) Deploy a host-based intrusion detection solution on enterprise assets, where appropriate and/or supported."}, {"id": "custom-7bptiii0b", "name": "CIS Safeguard 13,3: Deploy a Network Intrusion Detection Solution", "description": "(Asset Type: Network, Security Function: Detect) Deploy a network intrusion detection solution on enterprise assets, where appropriate. Example implementations include the use of a Network Intrusion Detection System (NIDS) or equivalent cloud service provider (CSP) service."}, {"id": "custom-pkswxrq9n", "name": "CIS Safeguard 13,4: Perform Traffic Filtering Between Network Segments", "description": "(Asset Type: Network, Security Function: Protect) Perform traffic filtering between network segments, where appropriate."}, {"id": "custom-xd5numf9p", "name": "CIS Safeguard 13,5: Manage Access Control for Remote Assets", "description": "(Asset Type: Devices, Security Function: Protect) Manage access control for assets remotely connecting to enterprise resources. Determine amount of access to enterprise resources based on: up-to-date anti-malware software installed, configuration compliance with the enterprise’s secure configuration process, and ensuring the operating system and applications are up-to-date."}, {"id": "custom-nxpy6vxgx", "name": "CIS Safeguard 13,6: Collect Network Traffic Flow Logs", "description": "(Asset Type: Network, Security Function: Detect) Collect network traffic flow logs and/or network traffic to review and alert upon from network devices."}, {"id": "custom-wg8frmxp2", "name": "CIS Safeguard 13,7: Deploy a Host-Based Intrusion Prevention Solution", "description": "(Asset Type: Devices, Security Function: Protect) Deploy a host-based intrusion prevention solution on enterprise assets, where appropriate and/or supported. Example implementations include use of an Endpoint Detection and Response (EDR) client or host-based IPS agent."}, {"id": "custom-kbth3bv5c", "name": "CIS Safeguard 13,8: Deploy a Network Intrusion Prevention Solution", "description": "(Asset Type: Network, Security Function: Protect) Deploy a network intrusion prevention solution, where appropriate. Example implementations include the use of a Network Intrusion Prevention System (NIPS) or equivalent CSP service."}, {"id": "custom-ef2h5fanb", "name": "CIS Safeguard 13,9: Deploy Port-Level Access Control", "description": "(Asset Type: Devices, Security Function: Protect) Deploy port-level access control. Port-level access control utilizes 802.1x, or similar network access control protocols, such as certificates, and may incorporate user and/or device authentication."}, {"id": "custom-00jhs7t5b", "name": "CIS Safeguard 13,10: Perform Application Layer Filtering", "description": "(Asset Type: Network, Security Function: Protect) Perform application layer filtering. Example implementations include a filtering proxy, application layer firewall, or gateway."}, {"id": "custom-vq3ax<PERSON>rye", "name": "CIS Safeguard 13,11: Tune Security Event Alerting Thresholds", "description": "(Asset Type: Network, Security Function: Detect) Tune security event alerting thresholds monthly, or more frequently."}, {"id": "custom-0oeafvd62", "name": "CIS Control 14: Security Awareness and Skills Training", "description": "Establish and maintain a security awareness program to influence behavior among the workforce to be security conscious and properly skilled to reduce cybersecurity risks to the enterprise."}, {"id": "custom-zaddrh4iz", "name": "CIS Safeguard 14,1: Establish and Maintain a Security Awareness Program", "description": "(Security Function: Protect) Establish and maintain a security awareness program. The purpose of a security awareness program is to educate the enterprise’s workforce on how to interact with enterprise assets and data in a secure manner. Conduct training at hire and, at a minimum, annually. Review and update content annually, or when significant enterprise changes occur that could impact this Safeguard."}, {"id": "custom-ng4nk1u8x", "name": "CIS Safeguard 14,2: Train Workforce Members to Recognize Social Engineering Attacks", "description": "(Security Function: Protect) Train workforce members to recognize social engineering attacks, such as phishing, pre-texting, and tailgating."}, {"id": "custom-olnvdzkr8", "name": "CIS Safeguard 14,3: Train Workforce Members on Authentication Best Practices", "description": "(Security Function: Protect) Train workforce members on authentication best practices. Example topics include MFA, password composition, and credential management."}, {"id": "custom-v8l90gxya", "name": "CIS Safeguard 14,4: Train Workforce on Data Handling Best Practices", "description": "(Security Function: Protect) Train workforce members on how to identify and properly store, transfer, archive, and destroy sensitive data. This also includes training workforce members on clear screen and desk best practices, such as locking their screen when they step away from their enterprise asset, erasing physical and virtual whiteboards at the end of meetings, and storing data and assets securely."}, {"id": "custom-o7zv6zuht", "name": "CIS Safeguard 14,5: Train Workforce Members on Causes of Unintentional Data Exposure", "description": "(Security Function: Protect) Train workforce members to be aware of causes for unintentional data exposure. Example topics include mis-delivery of sensitive data, losing a portable end-user device, or publishing data to unintended audiences."}, {"id": "custom-f8u6h5d0d", "name": "CIS Safeguard 14,6: Train Workforce Members on Recognizing and Reporting Security Incidents", "description": "(Security Function: Protect) Train workforce members to be able to recognize a potential incident and be able to report such an incident."}, {"id": "custom-9dljkv3iu", "name": "CIS Safeguard 14,7: Train Workforce on How to Identify and Report if Their Enterprise Assets are Missing Security Updates", "description": "(Security Function: Protect) Train workforce to understand how to verify and report out-of-date software patches or any failures in automated processes and tools. Part of this training should include notifying IT personnel of any failures in automated processes and tools."}, {"id": "custom-ter8m4hi1", "name": "CIS Safeguard 14,8: Train Workforce on the Dangers of Connecting to and Transmitting Enterprise Data Over Insecure Networks", "description": "(Security Function: Protect) Train workforce members on the dangers of connecting to, and transmitting data over, insecure networks for enterprise activities. If the enterprise has remote workers, training must include guidance to ensure that all users securely configure their home network infrastructure."}, {"id": "custom-1nm8nwjp2", "name": "CIS Safeguard 14,9: Conduct Role-Specific Security Awareness and Skills Training", "description": "(Security Function: Protect) Conduct role-specific security awareness and skills training. Example implementations include secure system administration courses for IT professionals, OWASP® Top 10 vulnerability awareness and prevention training for web application developers, and advanced social engineering awareness training for high-profile roles."}, {"id": "custom-un4q2wgk9", "name": "CIS Control 15: Service Provider Management", "description": "Develop a process to evaluate service providers who hold sensitive data, or are responsible for an enterprise’s critical IT platforms or processes, to ensure these providers are protecting those platforms and data appropriately."}, {"id": "custom-lp8u843p5", "name": "CIS Safeguard 15,1: Establish and Maintain an Inventory of Service Providers", "description": "(Security Function: Identify) Establish and maintain an inventory of service providers. The inventory is to list all known service providers, include classification(s), and designate an enterprise contact for each service provider. Review and update the inventory annually, or when significant enterprise changes occur that could impact this Safeguard."}, {"id": "custom-4jwkgmwnr", "name": "CIS Safeguard 15,2: Establish and Maintain a Service Provider Management Policy", "description": "(Security Function: Identify) Establish and maintain a service provider management policy. Ensure the policy addresses the classification, inventory, assessment, monitoring, and decommissioning of service providers. Review and update the policy annually, or when significant enterprise changes occur that could impact this Safeguard."}, {"id": "custom-gq03ivrsg", "name": "CIS Safeguard 15,3: Classify Service Providers", "description": "(Security Function: Identify) Classify service providers. Classification consideration may include one or more characteristics, such as data sensitivity, data volume, availability requirements, applicable regulations, inherent risk, and mitigated risk. Update and review classifications annually, or when significant enterprise changes occur that could impact this Safeguard."}, {"id": "custom-0wzvaldpd", "name": "CIS Safeguard 15,4: Ensure Service Provider Contracts Include Security Requirements", "description": "(Security Function: Protect) Ensure service provider contracts include security requirements. Example requirements may include minimum security program requirements, security incident and/or data breach notification and response, data encryption requirements, and data disposal commitments. These security requirements must be consistent with the enterprise’s service provider management policy. Review service provider contracts annually to ensure contracts are not missing security requirements."}, {"id": "custom-snhdw7gbn", "name": "CIS Safeguard 15,5: Assess Service Providers", "description": "(Security Function: Identify) Assess service providers consistent with the enterprise’s service provider management policy. Assessment scope may vary based on classification(s), and may include review of standardized assessment reports, such as Service Organization Control 2 (SOC 2) and Payment Card Industry (PCI) Attestation of Compliance (AoC), customized questionnaires, or other appropriately rigorous processes. Reassess service providers annually, at a minimum, or with new and renewed contracts."}, {"id": "custom-tpt5e7x5k", "name": "CIS Safeguard 15,6: Monitor Service Providers", "description": "(Asset Type: Data, Security Function: Detect) Monitor service providers consistent with the enterprise’s service provider management policy. Monitoring may include periodic reassessment of service provider compliance, monitoring service provider release notes, and dark web monitoring."}, {"id": "custom-o67ljvj76", "name": "CIS Safeguard 15,7: Securely Decommission Service Providers", "description": "(Asset Type: Data, Security Function: Protect) Securely decommission service providers. Example considerations include user and service account deactivation, termination of data flows, and secure disposal of enterprise data within service provider systems."}, {"id": "custom-voi98cm8k", "name": "CIS Control 16: Application Software Security", "description": "Manage the security life cycle of in-house developed, hosted, or acquired software to prevent, detect, and remediate security weaknesses before they can impact the enterprise."}, {"id": "custom-bfgwofl76", "name": "CIS Safeguard 16,1: Establish and Maintain a Secure Application Development Process", "description": "(Asset Type: Applications, Security Function: Protect) Establish and maintain a secure application development process. In the process, address such items as: secure application design standards, secure coding practices, developer training, vulnerability management, security of third-party code, and application security testing procedures. Review and update documentation annually, or when significant enterprise changes occur that could impact this Safeguard."}, {"id": "custom-upnr22eew", "name": "CIS Safeguard 16,2: Establish and Maintain a Process to Accept and Address Software Vulnerabilities", "description": "(Asset Type: Applications, Security Function: Protect) Establish and maintain a process to accept and address reports of software vulnerabilities, including providing a means for external entities to report. The process is to include such items as: a vulnerability handling policy that identifies reporting process, responsible party for handling vulnerability reports, and a process for intake, assignment, remediation, and remediation testing. As part of the process, use a vulnerability tracking system that includes severity ratings, and metrics for measuring timing for identification, analysis, and remediation of vulnerabilities. Review and update documentation annually, or when significant enterprise changes occur that could impact this Safeguard. Third-party application developers need to consider this an externally-facing policy that helps to set expectations for outside stakeholders."}, {"id": "custom-noq09xxy3", "name": "CIS Safeguard 16,3: Perform Root Cause Analysis on Security Vulnerabilities", "description": "(Asset Type: Applications, Security Function: Protect) Perform root cause analysis on security vulnerabilities. When reviewing vulnerabilities, root cause analysis is the task of evaluating underlying issues that create vulnerabilities in code, and allows development teams to move beyond just fixing individual vulnerabilities as they arise."}, {"id": "custom-jegr5vw19", "name": "CIS Safeguard 16,4: <PERSON><PERSON><PERSON>lish and <PERSON><PERSON> an Inventory of Third-Party Software Components", "description": "(Asset Type: Applications, Security Function: Protect) Establish and manage an updated inventory of third-party components used in development, often referred to as a “bill of materials,” as well as components slated for future use. This inventory is to include any risks that each third-party component could pose. Evaluate the list at least monthly to identify any changes or updates to these components, and validate that the component is still supported."}, {"id": "custom-xo7b2mvw1", "name": "CIS Safeguard 16,5: Use Up-to-Date and Trusted Third-Party Software Components", "description": "(Asset Type: Applications, Security Function: Protect) Use up-to-date and trusted third-party software components. When possible, choose established and proven frameworks and libraries that provide adequate security. Acquire these components from trusted sources or evaluate the software for vulnerabilities before use."}, {"id": "custom-836ybi361", "name": "CIS Safeguard 16,6: Establish and Maintain a Severity Rating System and Process for Application Vulnerabilities", "description": "(Asset Type: Applications, Security Function: Protect) Establish and maintain a severity rating system and process for application vulnerabilities that facilitates prioritizing the order in which discovered vulnerabilities are fixed. This process includes setting a minimum level of security acceptability for releasing code or applications. Severity ratings bring a systematic way of triaging vulnerabilities that improves risk management and helps ensure the most severe bugs are fixed first. Review and update the system and process annually."}, {"id": "custom-wsx28cajc", "name": "CIS Safeguard 16,7: Use Standard Hardening Configuration Templates for Application Infrastructure", "description": "(Asset Type: Applications, Security Function: Protect) Use standard, industry-recommended hardening configuration templates for application infrastructure components. This includes underlying servers, databases, and web servers, and applies to cloud containers, Platform as a Service (PaaS) components, and SaaS components. Do not allow in-house developed software to weaken configuration hardening."}, {"id": "custom-4ryrcskg6", "name": "CIS Safeguard 16,8: Separate Production and Non-Production Systems", "description": "(Asset Type: Applications, Security Function: Protect) Maintain separate environments for production and non-production systems."}, {"id": "custom-wm6kz1jnd", "name": "CIS Safeguard 16,9: Train Developers in Application Security Concepts and Secure Coding", "description": "(Asset Type: Applications, Security Function: Protect) Ensure that all software development personnel receive training in writing secure code for their specific development environment and responsibilities. Training can include general security principles and application security standard practices. Conduct training at least annually and design in a way to promote security within the development team, and build a culture of security among the developers."}, {"id": "custom-ovgsf6d1r", "name": "CIS Safeguard 16,10: Apply Secure Design Principles in Application Architectures", "description": "(Asset Type: Applications, Security Function: Protect) Apply secure design principles in application architectures. Secure design principles include the concept of least privilege and enforcing mediation to validate every operation that the user makes, promoting the concept of \"\"never trust user input.\"\" Examples include ensuring that explicit error checking is performed and documented for all input, including for size, data type, and acceptable ranges or formats. Secure design also means minimizing the application infrastructure attack surface, such as turning off unprotected ports and services, removing unnecessary programs and files, and renaming or removing default accounts."}, {"id": "custom-i2xini3gi", "name": "CIS Safeguard 16,11: Leverage Vetted Modules or Services for Application Security Components", "description": "(Asset Type: Applications, Security Function: Protect) Leverage vetted modules or services for application security components, such as identity management, encryption, and auditing and logging. Using platform features in critical security functions will reduce developers’ workload and minimize the likelihood of design or implementation errors. Modern operating systems provide effective mechanisms for identification, authentication, and authorization and make those mechanisms available to applications. Use only standardized, currently accepted, and extensively reviewed encryption algorithms. Operating systems also provide mechanisms to create and maintain secure audit logs."}, {"id": "custom-jq6l1hvt7", "name": "CIS Safeguard 16,12: Implement Code-Level Security Checks", "description": "(Asset Type: Applications, Security Function: Protect) Apply static and dynamic analysis tools within the application life cycle to verify that secure coding practices are being followed."}, {"id": "custom-2jgoio6kb", "name": "CIS Safeguard 16,13: Conduct Application Penetration Testing", "description": "(Asset Type: Applications, Security Function: Protect) Conduct application penetration testing. For critical applications, authenticated penetration testing is better suited to finding business logic vulnerabilities than code scanning and automated security testing. Penetration testing relies on the skill of the tester to manually manipulate an application as an authenticated and unauthenticated user."}, {"id": "custom-l5ae7c42q", "name": "CIS Safeguard 16,14: Conduct Threat Modeling", "description": "(Asset Type: Applications, Security Function: Protect) Conduct threat modeling. Threat modeling is the process of identifying and addressing application security design flaws within a design, before code is created. It is conducted through specially trained individuals who evaluate the application design and gauge security risks for each entry point and access level. The goal is to map out the application, architecture, and infrastructure in a structured way to understand its weaknesses."}, {"id": "custom-j2zi6ukpr", "name": "CIS Control 17: Incident Response Management", "description": "Establish a program to develop and maintain an incident response capability (e.g., policies, plans, procedures, defined roles, training, and communications) to prepare, detect, and quickly respond to an attack."}, {"id": "custom-w1oudcjiv", "name": "CIS Safeguard 17,1: Designate Personnel to Manage Incident Handling", "description": "(Security Function: Respond) Designate one key person, and at least one backup, who will manage the enterprise’s incident handling process. Management personnel are responsible for the coordination and documentation of incident response and recovery efforts and can consist of employees internal to the enterprise, third-party vendors, or a hybrid approach. If using a third-party vendor, designate at least one person internal to the enterprise to oversee any third-party work. Review annually, or when significant enterprise changes occur that could impact this Safeguard."}, {"id": "custom-j8n7ud8ko", "name": "CIS Safeguard 17,2: Establish and Maintain Contact Information for Reporting Security Incidents", "description": "(Security Function: Respond) Establish and maintain contact information for parties that need to be informed of security incidents. Contacts may include internal staff, third-party vendors, law enforcement, cyber insurance providers, relevant government agencies, Information Sharing and Analysis Center (ISAC) partners, or other stakeholders. Verify contacts annually to ensure that information is up-to-date."}, {"id": "custom-gt0fzi0ik", "name": "CIS Safeguard 17,3: Establish and Maintain an Enterprise Process for Reporting Incidents", "description": "(Security Function: Respond) Establish and maintain an enterprise process for the workforce to report security incidents. The process includes reporting timeframe, personnel to report to, mechanism for reporting, and the minimum information to be reported. Ensure the process is publicly available to all of the workforce. Review annually, or when significant enterprise changes occur that could impact this Safeguard."}, {"id": "custom-tf8g5dz0s", "name": "CIS Safeguard 17,4: Establish and Maintain an Incident Response Process", "description": "(Security Function: Respond) Establish and maintain an incident response process that addresses roles and responsibilities, compliance requirements, and a communication plan. Review annually, or when significant enterprise changes occur that could impact this Safeguard."}, {"id": "custom-7arbecp29", "name": "CIS Safeguard 17,5: Assign Key Roles and Responsibilities", "description": "(Security Function: Respond) Assign key roles and responsibilities for incident response, including staff from legal, IT, information security, facilities, public relations, human resources, incident responders, and analysts, as applicable. Review annually, or when significant enterprise changes occur that could impact this Safeguard."}, {"id": "custom-zunit1j5w", "name": "CIS Safeguard 17,6: Define Mechanisms for Communicating During Incident Response", "description": "(Security Function: Respond) Determine which primary and secondary mechanisms will be used to communicate and report during a security incident. Mechanisms can include phone calls, emails, or letters. Keep in mind that certain mechanisms, such as emails, can be affected during a security incident. Review annually, or when significant enterprise changes occur that could impact this Safeguard."}, {"id": "custom-zoetwdnhh", "name": "CIS Safeguard 17,7: Conduct Routine Incident Response Exercises", "description": "(Security Function: Recover) Plan and conduct routine incident response exercises and scenarios for key personnel involved in the incident response process to prepare for responding to real-world incidents. Exercises need to test communication channels, decision making, and workflows. Conduct testing on an annual basis, at a minimum."}, {"id": "custom-wggiqykdu", "name": "CIS Safeguard 17,8: Conduct Post-Incident Reviews", "description": "(Security Function: Recover) Conduct post-incident reviews. Post-incident reviews help prevent incident recurrence through identifying lessons learned and follow-up action."}, {"id": "custom-4jnp9qcwg", "name": "CIS Safeguard 17,9: Establish and Maintain Security Incident Thresholds", "description": "(Security Function: Recover) Establish and maintain security incident thresholds, including, at a minimum, differentiating between an incident and an event. Examples can include: abnormal activity, security vulnerability, security weakness, data breach, privacy incident, etc. Review annually, or when significant enterprise changes occur that could impact this Safeguard."}, {"id": "custom-6j39mckfz", "name": "CIS Control 18: Penetration Testing", "description": "Test the effectiveness and resiliency of enterprise assets through identifying and exploiting weaknesses in controls (people, processes, and technology), and simulating the objectives and actions of an attacker."}, {"id": "custom-x2d1kcna0", "name": "CIS Safeguard 18,1: Establish and Maintain a Penetration Testing Program", "description": "(Security Function: Identify) Establish and maintain a penetration testing program appropriate to the size, complexity, and maturity of the enterprise. Penetration testing program characteristics include scope, such as network, web application, Application Programming Interface (API), hosted services, and physical premise controls"}, {"id": "custom-jbuqiqj8b", "name": "CIS Safeguard 18,2: Perform Periodic External Penetration Tests", "description": "(Asset Type: Network, Security Function: Identify) Perform periodic external penetration tests based on program requirements, no less than annually. External penetration testing must include enterprise and environmental reconnaissance to detect exploitable information. Penetration testing requires specialized skills and experience and must be conducted through a qualified party. The testing may be clear box or opaque box."}, {"id": "custom-3kgzesab3", "name": "CIS Safeguard 18,3: Remediate Penetration Test Findings", "description": "(Asset Type: Network, Security Function: Protect) Remediate penetration test findings based on the enterprise’s policy for remediation scope and prioritization."}, {"id": "custom-swgbbzogk", "name": "CIS Safeguard 18,4: Validate Security Measures", "description": "(Asset Type: Network, Security Function: Protect) Validate security measures after each penetration test. If deemed necessary, modify rulesets and capabilities to detect the techniques used during testing."}, {"id": "custom-iuezbp34t", "name": "CIS Safeguard 18,5: Perform Periodic Internal Penetration Tests", "description": "(Security Function: Identify) Perform periodic internal penetration tests based on program requirements, no less than annually. The testing may be clear box or opaque box."}], "is_predefined": true, "analysisId": {"$oid": "67e28e111ed3f1537e02fe15"}, "createdBy": {"$oid": "67e02839208a45ab0a3168b5"}, "createdAt": {"$date": "2025-04-04T00:56:15.915Z"}, "updatedAt": {"$date": "2025-04-04T00:56:15.915Z"}, "__v": 0}, {"_id": {"$oid": "67ef3af78fc56749148ea598"}, "name": "NIST CSF 2.0", "description": "Importé le 04/04/2025", "rules": [{"id": "custom-i3rualhwy", "name": "DE.AE-02: <PERSON><PERSON>ze Adverse Events", "description": "Potentially adverse events are analyzed to better understand associated activities"}, {"id": "custom-bczqhpc4p", "name": "DE.AE-03: Correlate Information Sources", "description": "Information is correlated from multiple sources"}, {"id": "custom-hbxacbhac", "name": "DE.AE-04: Understand Event Impact/Scope", "description": "The estimated impact and scope of adverse events are understood"}, {"id": "custom-qusyx7dyr", "name": "DE.AE-06: Provide Event Information", "description": "Information on adverse events is provided to authorized staff and tools"}, {"id": "custom-j3o68nr0n", "name": "DE.AE-07: Integrate Threat Intelligence", "description": "Cyber threat intelligence and other contextual information are integrated into the analysis"}, {"id": "custom-i6df24tzb", "name": "DE.AE-08: Declare Incidents", "description": "Incidents are declared when adverse events meet the defined incident criteria"}, {"id": "custom-mjpyg1yc9", "name": "DE.CM-01: Monitor Networks for Events", "description": "Networks and network services are monitored to find potentially adverse events"}, {"id": "custom-8dx5chspg", "name": "DE.CM-02: Monitor Physical Environment", "description": "The physical environment is monitored to find potentially adverse events"}, {"id": "custom-xjfqmxlgi", "name": "DE.CM-03: Monitor Personnel/Tech Usage", "description": "Personnel activity and technology usage are monitored to find potentially adverse events"}, {"id": "custom-53ov3ef3c", "name": "DE.CM-06: Monitor Service Providers", "description": "External service provider activities and services are monitored to find potentially adverse events"}, {"id": "custom-5w4l90dma", "name": "DE.CM-09: Monitor Assets/Data for Events", "description": "Computing hardware and software, runtime environments, and their data are monitored to find potentially adverse events"}, {"id": "custom-5vkqhtmep", "name": "GV.OC-01: Understand Mission for Risk Mgmt", "description": "The organizational mission is understood and informs cybersecurity risk management"}, {"id": "custom-co3o61bp1", "name": "GV.OC-02: Understand Stakeholder Needs", "description": "Internal and external stakeholders are understood, and their needs and expectations regarding cybersecurity risk management are understood and considered"}, {"id": "custom-qbd053qxv", "name": "GV.OC-03: Manage Legal/Regulatory Req.", "description": "Legal, regulatory, and contractual requirements regarding cybersecurity - including privacy and civil liberties obligations - are understood and managed"}, {"id": "custom-5gkpp218c", "name": "GV.OC-04: Understand Critical External Svcs", "description": "Critical objectives, capabilities, and services that external stakeholders depend on or expect from the organization are understood and communicated"}, {"id": "custom-9m2qft2st", "name": "GV.OC-05: Understand Internal Dependencies", "description": "Outcomes, capabilities, and services that the organization depends on are understood and communicated"}, {"id": "custom-45js3lhrr", "name": "GV.OV-01: Review Strategy Outcomes", "description": "Cybersecurity risk management strategy outcomes are reviewed to inform and adjust strategy and direction"}, {"id": "custom-n88hy4592", "name": "GV.OV-02: Review/Adjust Strategy Coverage", "description": "The cybersecurity risk management strategy is reviewed and adjusted to ensure coverage of organizational requirements and risks"}, {"id": "custom-98t6o8t6k", "name": "GV.OV-03: Evaluate Risk Mgmt Performance", "description": "Organizational cybersecurity risk management performance is evaluated and reviewed for adjustments needed"}, {"id": "custom-gz2bqdaqw", "name": "GV.PO-01: Establish Risk Policy", "description": "Policy for managing cybersecurity risks is established based on organizational context, cybersecurity strategy, and priorities and is communicated and enforced"}, {"id": "custom-wm8e6mohk", "name": "GV.PO-02: Maintain Risk Policy", "description": "Policy for managing cybersecurity risks is reviewed, updated, communicated, and enforced to reflect changes in requirements, threats, technology, and organizational mission"}, {"id": "custom-nr85r3cej", "name": "GV.RM-01: Establish Risk Objectives", "description": "Risk management objectives are established and agreed to by organizational stakeholders"}, {"id": "custom-pjnqvzgdw", "name": "GV.RM-02: Define Risk Appetite/Tolerance", "description": "Risk appetite and risk tolerance statements are established, communicated, and maintained"}, {"id": "custom-hvvynz95a", "name": "GV.RM-03: Integrate Cyber Risk into ERM", "description": "Cybersecurity risk management activities and outcomes are included in enterprise risk management processes"}, {"id": "custom-ebxjhlurf", "name": "GV.RM-04: Establish Risk Response Strategy", "description": "Strategic direction that describes appropriate risk response options is established and communicated"}, {"id": "custom-q1cao0ayt", "name": "GV.RM-05: Establish Risk Communication Lines", "description": "Lines of communication across the organization are established for cybersecurity risks, including risks from suppliers and other third parties"}, {"id": "custom-wvnwu4s32", "name": "GV.RM-06: Standardize Risk Calculation", "description": "A standardized method for calculating, documenting, categorizing, and prioritizing cybersecurity risks is established and communicated"}, {"id": "custom-zmv64xlaf", "name": "GV.RR-02: Define Risk Roles/Responsibilities", "description": "Roles, responsibilities, and authorities related to cybersecurity risk management are established, communicated, understood, and enforced"}, {"id": "custom-okd28rsii", "name": "GV.RR-03: Allocate Risk Resources", "description": "Adequate resources are allocated commensurate with the cybersecurity risk strategy, roles, responsibilities, and policies"}, {"id": "custom-p1gggmmax", "name": "GV.RR-04: Integrate Cyber into HR", "description": "Cybersecurity is included in human resources practices"}, {"id": "custom-nzhoxkadx", "name": "GV.SC-01: Establish C-SCRM Program", "description": "A cybersecurity supply chain risk management program, strategy, objectives, policies, and processes are established and agreed to by organizational stakeholders"}, {"id": "custom-4x7021hf4", "name": "GV.SC-02: Define C-SCRM Roles", "description": "Cybersecurity roles and responsibilities for suppliers, customers, and partners are established, communicated, and coordinated internally and externally"}, {"id": "custom-78itk58vn", "name": "GV.SC-03: Integrate C-SCRM into Risk Mgmt", "description": "Cybersecurity supply chain risk management is integrated into cybersecurity and enterprise risk management, risk assessment, and improvement processes"}, {"id": "custom-c2froniue", "name": "GV.SC-04: Know/Prioritize Suppliers", "description": "Suppliers are known and prioritized by criticality"}, {"id": "custom-b5laumlxw", "name": "GV.SC-05: Define C-SCRM Requirements", "description": "Requirements to address cybersecurity risks in supply chains are established, prioritized, and integrated into contracts and other types of agreements with suppliers and other relevant third parties"}, {"id": "custom-avs0caw9s", "name": "GV.SC-06: Perform Supplier Due Diligence", "description": "Planning and due diligence are performed to reduce risks before entering into formal supplier or other third-party relationships"}, {"id": "custom-plikjoi5z", "name": "GV.SC-07: Manage Ongoing Supplier Risk", "description": "The risks posed by a supplier, their products and services, and other third parties are understood, recorded, prioritized, assessed, responded to, and monitored over the course of the relationship"}, {"id": "custom-z2h4ebult", "name": "GV.SC-08: Include Suppliers in IR/Recovery", "description": "Relevant suppliers and other third parties are included in incident planning, response, and recovery activities"}, {"id": "custom-i904x7ltu", "name": "GV.SC-09: Integrate/Monitor C-SCRM Practices", "description": "Supply chain security practices are integrated into cybersecurity and enterprise risk management programs, and their performance is monitored throughout the technology product and service life cycle"}, {"id": "custom-mfn8jegsq", "name": "GV.SC-10: Plan for Supplier Offboarding", "description": "Cybersecurity supply chain risk management plans include provisions for activities that occur after the conclusion of a partnership or service agreement"}, {"id": "custom-ibkifm5yx", "name": "ID.AM-01: Maintain Hardware Inventory", "description": "Inventories of hardware managed by the organization are maintained"}, {"id": "custom-2ukiqj2b1", "name": "ID.AM-02: Maintain Software/System Inventory", "description": "Inventories of software, services, and systems managed by the organization are maintained"}, {"id": "custom-vcj89mhr9", "name": "ID.AM-03: Maintain Network Flow Diagrams", "description": "Representations of the organization's authorized network communication and internal and external network data flows are maintained"}, {"id": "custom-vlrmqn23x", "name": "ID.AM-04: Maintain Supplier Service Inventory", "description": "Inventories of services provided by suppliers are maintained"}, {"id": "custom-zwryn0h6m", "name": "ID.AM-05: Prioritize Assets", "description": "Assets are prioritized based on classification, criticality, resources, and impact on the mission"}, {"id": "custom-zr365dy6v", "name": "ID.AM-07: Maintain Data Inventory", "description": "Inventories of data and corresponding metadata for designated data types are maintained"}, {"id": "custom-3y10mo536", "name": "ID.AM-08: Manage Asset Lifecycles", "description": "Systems, hardware, software, services, and data are managed throughout their life cycles"}, {"id": "custom-urwvb8wwy", "name": "ID.IM-01: Identify Improvements (Evaluations)", "description": "Improvements are identified from evaluations"}, {"id": "custom-rv06pzf78", "name": "ID.IM-02: Identify Improvements (Tests)", "description": "Improvements are identified from security tests and exercises, including those done in coordination with suppliers and relevant third parties"}, {"id": "custom-e9h5np8gv", "name": "ID.IM-03: Identify Improvements (Operations)", "description": "Improvements are identified from execution of operational processes, procedures, and activities"}, {"id": "custom-cwhac6efm", "name": "ID.IM-04: Maintain/Improve IR Plans", "description": "Incident response plans and other cybersecurity plans that affect operations are established, communicated, maintained, and improved"}, {"id": "custom-1e98i5yz9", "name": "ID.RA-01: Identify/Record Vulnerabilities", "description": "Vulnerabilities in assets are identified, validated, and recorded"}, {"id": "custom-6l8zhpdhs", "name": "ID.RA-02: Receive Threat Intelligence", "description": "Cyber threat intelligence is received from information sharing forums and sources"}, {"id": "custom-h0f89r7ca", "name": "ID.RA-03: Identify/Record Threats", "description": "Internal and external threats to the organization are identified and recorded"}, {"id": "custom-f3k4dt3og", "name": "ID.RA-04: Assess Threat Impact/Likelihood", "description": "Potential impacts and likelihoods of threats exploiting vulnerabilities are identified and recorded"}, {"id": "custom-e3btvbkec", "name": "ID.RA-05: Analyze Risks", "description": "Threats, vulnerabilities, likelihoods, and impacts are used to understand inherent risk and inform risk response prioritization"}, {"id": "custom-ay53zav0q", "name": "ID.RA-06: Plan/Track Risk Responses", "description": "Risk responses are chosen, prioritized, planned, tracked, and communicated"}, {"id": "custom-a6oqvg58k", "name": "ID.RA-07: Manage Changes/Exceptions", "description": "Changes and exceptions are managed, assessed for risk impact, recorded, and tracked"}, {"id": "custom-qpwil5xob", "name": "ID.RA-08: Manage Vulnerability Disclosures", "description": "Processes for receiving, analyzing, and responding to vulnerability disclosures are established"}, {"id": "custom-jzbbjq9c3", "name": "ID.RA-09: <PERSON><PERSON><PERSON> Asset Integrity (Pre-Acq.)", "description": "The authenticity and integrity of hardware and software are assessed prior to acquisition and use"}, {"id": "custom-zt2cp6ad5", "name": "ID.RA-10: <PERSON><PERSON><PERSON> (Pre-Acq.)", "description": "Critical suppliers are assessed prior to acquisition"}, {"id": "custom-u5fu5crdn", "name": "PR.AA-01: Manage Identities/Credentials", "description": "Identities and credentials for authorized users, services, and hardware are managed by the organization"}, {"id": "custom-l93nx5jgk", "name": "PR.AA-02: Proof/Bind Identities", "description": "Identities are proofed and bound to credentials based on the context of interactions"}, {"id": "custom-54av2tam7", "name": "PR.AA-03: Authenticate Users/Services/HW", "description": "Users, services, and hardware are authenticated"}, {"id": "custom-duycp6iym", "name": "PR.AA-04: Protect Identity Assertions", "description": "Identity assertions are protected, conveyed, and verified"}, {"id": "custom-49c2044r5", "name": "PR.AA-05: Manage Access Permissions", "description": "Access permissions, entitlements, and authorizations are defined in a policy, managed, enforced, and reviewed, and incorporate the principles of least privilege and separation of duties"}, {"id": "custom-oo6au3jce", "name": "PR.AA-06: Manage Physical Access", "description": "Physical access to assets is managed, monitored, and enforced commensurate with risk"}, {"id": "custom-8oz6liwcd", "name": "PR.AT-01: Provide General Security Training", "description": "Personnel are provided with awareness and training so that they possess the knowledge and skills to perform general tasks with cybersecurity risks in mind"}, {"id": "custom-vnt2z9ro4", "name": "PR.AT-02: Provide Role-Specific Training", "description": "Individuals in specialized roles are provided with awareness and training so that they possess the knowledge and skills to perform relevant tasks with cybersecurity risks in mind"}, {"id": "custom-gkgizrdv3", "name": "PR.DS-01: Protect Data-at-Rest", "description": "The confidentiality, integrity, and availability of data-at-rest are protected"}, {"id": "custom-8v2w55j1x", "name": "PR.DS-02: Protect Data-in-Transit", "description": "The confidentiality, integrity, and availability of data-in-transit are protected"}, {"id": "custom-hgbhvi1r3", "name": "PR.DS-10: Protect Data-in-Use", "description": "The confidentiality, integrity, and availability of data-in-use are protected"}, {"id": "custom-efquk1epb", "name": "PR.DS-11: Manage Data Backups", "description": "Backups of data are created, protected, maintained, and tested"}, {"id": "custom-ltvzgfvih", "name": "PR.IR-01: Protect Networks (Logical Access)", "description": "Networks and environments are protected from unauthorized logical access and usage"}, {"id": "custom-do3jfpo67", "name": "PR.IR-02: Protect Assets (Environmental)", "description": "The organization's technology assets are protected from environmental threats"}, {"id": "custom-t1jlcc8dh", "name": "PR.IR-03: Implement Resilience Mechanisms", "description": "Mechanisms are implemented to achieve resilience requirements in normal and adverse situations"}, {"id": "custom-xiaw4z7df", "name": "PR.IR-04: Maintain Resource Capacity", "description": "Adequate resource capacity to ensure availability is maintained"}, {"id": "custom-1fccolgii", "name": "PR.PS-01: Apply Configuration Management", "description": "Configuration management practices are established and applied"}, {"id": "custom-ns9xwj06m", "name": "PR.PS-02: Manage Software Maintenance", "description": "Software is maintained, replaced, and removed commensurate with risk"}, {"id": "custom-kz2hjqis9", "name": "PR.PS-03: Manage Hardware Maintenance", "description": "Hardware is maintained, replaced, and removed commensurate with risk"}, {"id": "custom-m3lu34844", "name": "PR.PS-04: Generate Logs for Monitoring", "description": "Log records are generated and made available for continuous monitoring"}, {"id": "custom-hdkybkgv2", "name": "PR.PS-05: Prevent Unauthorized Software", "description": "Installation and execution of unauthorized software are prevented"}, {"id": "custom-l8a69emwc", "name": "PR.PS-06: Integrate Secure SDLC", "description": "Secure software development practices are integrated, and their performance is monitored throughout the software development life cycle"}, {"id": "custom-4dosor0ag", "name": "RC.CO-03: Communicate Recovery Progress", "description": "Recovery activities and progress in restoring operational capabilities are communicated to designated internal and external stakeholders"}, {"id": "custom-pz3ew5ntp", "name": "RC.CO-04: Share Public Recovery Updates", "description": "Public updates on incident recovery are shared using approved methods and messaging"}, {"id": "custom-eagjjv0q9", "name": "RC.RP-01: Execute Recovery Plan", "description": "The recovery portion of the incident response plan is executed once initiated from the incident response process"}, {"id": "custom-7z2s7i5zp", "name": "RC.RP-02: Manage Recovery Actions", "description": "Recovery actions are selected, scoped, prioritized, and performed"}, {"id": "custom-c4umysk5f", "name": "RC.RP-03: Verify Backup Integrity", "description": "The integrity of backups and other restoration assets is verified before using them for restoration"}, {"id": "custom-on4dvrrwu", "name": "RC.RP-04: Establish Post-Incident Norms", "description": "Critical mission functions and cybersecurity risk management are considered to establish post-incident operational norms"}, {"id": "custom-h7tsbl014", "name": "RC.RP-05: <PERSON><PERSON><PERSON> Restored Assets", "description": "The integrity of restored assets is verified, systems and services are restored, and normal operating status is confirmed"}, {"id": "custom-k46h0w3e7", "name": "RC.RP-06: Declare End of Recovery", "description": "The end of incident recovery is declared based on criteria, and incident-related documentation is completed"}, {"id": "custom-776y0vv6j", "name": "RS.AN-03: Analyze Incident/Root Cause", "description": "Analysis is performed to establish what has taken place during an incident and the root cause of the incident"}, {"id": "custom-74m57o1qk", "name": "RS.AN-06: Record Investigation Actions", "description": "Actions performed during an investigation are recorded, and the records' integrity and provenance are preserved"}, {"id": "custom-8fcdg9h19", "name": "RS.AN-07: Collect/Preserve Incident Data", "description": "Incident data and metadata are collected, and their integrity and provenance are preserved"}, {"id": "custom-y1chx5ptf", "name": "RS.AN-08: Estimate Incident Magnitude", "description": "An incident's magnitude is estimated and validated"}, {"id": "custom-10benafr3", "name": "RS.CO-02: Notify Stakeholders of Incidents", "description": "Internal and external stakeholders are notified of incidents"}, {"id": "custom-o19x9tmpa", "name": "RS.CO-03: Share Incident Information", "description": "Information is shared with designated internal and external stakeholders"}, {"id": "custom-qumy6cpcd", "name": "RS.MA-01: Execute Incident Response Plan", "description": "The incident response plan is executed in coordination with relevant third parties once an incident is declared"}, {"id": "custom-pwao3jsvw", "name": "RS.MA-02: Triage/Validate Incident Reports", "description": "Incident reports are triaged and validated"}, {"id": "custom-tphwhbxik", "name": "RS.MA-03: Categorize/Prioritize Incidents", "description": "Incidents are categorized and prioritized"}, {"id": "custom-0vluvwwq9", "name": "RS.MA-04: Escalate Incidents", "description": "Incidents are escalated or elevated as needed"}, {"id": "custom-0j379qpa8", "name": "RS.MA-05: Apply Recovery Initiation Criteria", "description": "The criteria for initiating incident recovery are applied"}, {"id": "custom-x1cjw3ezv", "name": "RS.MI-01: Contain Incidents", "description": "Incidents are contained"}, {"id": "custom-rhd2ev7tr", "name": "RS.MI-02: Eradicate Incidents", "description": "Incidents are eradicated"}], "is_predefined": true, "analysisId": {"$oid": "67ef307f8fc56749148e9ad6"}, "createdBy": {"$oid": "67e02839208a45ab0a3168b5"}, "createdAt": {"$date": "2025-04-04T01:50:47.491Z"}, "updatedAt": {"$date": "2025-04-04T01:50:47.491Z"}, "__v": 0}]