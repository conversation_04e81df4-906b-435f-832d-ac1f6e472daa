// controllers/operationalScenariosController.js
const mongoose = require('mongoose');
const OperationalScenario = require('../models/OperationalScenario');
const Analysis = require('../models/Analysis');
const ActivityLog = require('../models/ActivityLog');

// Helper function to transform enum values
const transformEnumValue = (value, mapping) => {
  if (!value) return value;
  const lowerValue = value.toLowerCase();
  return mapping[lowerValue] || value;
};

// Helper function to get IP address
const getIpAddress = (req) => {
  return req.headers['x-forwarded-for'] ||
         req.connection.remoteAddress ||
         req.socket.remoteAddress ||
         (req.connection.socket ? req.connection.socket.remoteAddress : null) ||
         '127.0.0.1';
};

// Helper function to create activity log
const logActivity = async (userId, companyId, actionType, resourceType, resourceId, details = {}) => {
  try {
    if (!userId) return;

    await ActivityLog.create({
      userId,
      userName: 'System User', // Since we don't have user info when auth is disabled
      companyId,
      companyName: null,
      actionType,
      resourceType,
      resourceId,
      ipAddress: '127.0.0.1',
      userAgent: '',
      details: {
        ...details,
        timestamp: new Date()
      }
    });
  } catch (error) {
    console.error('Activity log error:', error);
  }
};

// Get all operational scenarios for an analysis
exports.getOperationalScenarios = async (req, res) => {
  try {
    const { analysisId } = req.params;

    // Verify analysis exists and user has access
    const analysis = await Analysis.findById(analysisId);
    if (!analysis) {
      return res.status(404).json({
        success: false,
        message: 'Analysis not found'
      });
    }

    const scenarios = await OperationalScenario.find({ analysisId })
      .populate('createdBy', 'name email')
      .populate('updatedBy', 'name email')
      .sort({ createdAt: -1 });

    res.json({
      success: true,
      data: scenarios
    });
  } catch (error) {
    console.error('Error fetching operational scenarios:', error);
    res.status(500).json({
      success: false,
      message: 'Error fetching operational scenarios',
      error: error.message
    });
  }
};

// Get operational scenarios by attack path
exports.getOperationalScenariosByAttackPath = async (req, res) => {
  try {
    const { analysisId, attackPathId } = req.params;

    const scenarios = await OperationalScenario.find({
      analysisId,
      attackPathId
    })
      .populate('createdBy', 'name email')
      .populate('updatedBy', 'name email')
      .sort({ createdAt: -1 });

    res.json({
      success: true,
      data: scenarios
    });
  } catch (error) {
    console.error('Error fetching scenarios by attack path:', error);
    res.status(500).json({
      success: false,
      message: 'Error fetching scenarios by attack path',
      error: error.message
    });
  }
};

// Create a new operational scenario
exports.createOperationalScenario = async (req, res) => {
  try {
    const { analysisId } = req.params;
    const scenarioData = req.body;

    // Verify analysis exists
    const analysis = await Analysis.findById(analysisId);
    if (!analysis) {
      return res.status(404).json({
        success: false,
        message: 'Analysis not found'
      });
    }

    const scenario = new OperationalScenario({
      ...scenarioData,
      analysisId,
      createdBy: req.user?.id || null
    });

    await scenario.save();

    // Log activity (if user is authenticated)
    if (req.user?.id) {
      await logActivity(
        req.user.id,
        req.user.companyId,
        'create',
        'operational-scenario',
        scenario._id,
        { scenarioName: scenario.name }
      );
    }

    const populatedScenario = await OperationalScenario.findById(scenario._id)
      .populate('createdBy', 'name email');

    res.status(201).json({
      success: true,
      data: populatedScenario
    });
  } catch (error) {
    console.error('Error creating operational scenario:', error);
    res.status(500).json({
      success: false,
      message: 'Error creating operational scenario',
      error: error.message
    });
  }
};

// Update an operational scenario
exports.updateOperationalScenario = async (req, res) => {
  try {
    const { analysisId, scenarioId } = req.params;
    const updateData = req.body;

    const scenario = await OperationalScenario.findOne({
      _id: scenarioId,
      analysisId
    });

    if (!scenario) {
      return res.status(404).json({
        success: false,
        message: 'Operational scenario not found'
      });
    }

    // Update scenario
    Object.assign(scenario, updateData);
    scenario.updatedBy = req.user?.id || null;
    scenario.metadata.lastModified = new Date();

    await scenario.save();

    // Log activity (if user is authenticated)
    if (req.user?.id) {
      await logActivity(
        req.user.id,
        req.user.companyId,
        'update',
        'operational-scenario',
        scenario._id,
        { scenarioName: scenario.name }
      );
    }

    const populatedScenario = await OperationalScenario.findById(scenario._id)
      .populate('createdBy', 'name email')
      .populate('updatedBy', 'name email');

    res.json({
      success: true,
      data: populatedScenario
    });
  } catch (error) {
    console.error('Error updating operational scenario:', error);
    res.status(500).json({
      success: false,
      message: 'Error updating operational scenario',
      error: error.message
    });
  }
};

// Delete an operational scenario
exports.deleteOperationalScenario = async (req, res) => {
  try {
    const { analysisId, scenarioId } = req.params;

    const scenario = await OperationalScenario.findOne({
      _id: scenarioId,
      analysisId
    });

    if (!scenario) {
      return res.status(404).json({
        success: false,
        message: 'Operational scenario not found'
      });
    }

    await OperationalScenario.findByIdAndDelete(scenarioId);

    // Log activity (if user is authenticated)
    if (req.user?.id) {
      await logActivity(
        req.user.id,
        req.user.companyId,
        'delete',
        'operational-scenario',
        scenarioId,
        { scenarioName: scenario.name }
      );
    }

    res.json({
      success: true,
      message: 'Operational scenario deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting operational scenario:', error);
    res.status(500).json({
      success: false,
      message: 'Error deleting operational scenario',
      error: error.message
    });
  }
};

// Bulk create operational scenarios
exports.bulkCreateOperationalScenarios = async (req, res) => {
  try {
    const { analysisId } = req.params;
    const { scenarios } = req.body;

    if (!scenarios || !Array.isArray(scenarios)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid scenarios data'
      });
    }

    // Verify analysis exists
    const analysis = await Analysis.findById(analysisId);
    if (!analysis) {
      return res.status(404).json({
        success: false,
        message: 'Analysis not found'
      });
    }

    // Transform and add common fields to all scenarios
    console.log('=== TRANSFORMATION DEBUG ===');
    console.log('Original scenarios:', JSON.stringify(scenarios, null, 2));

    const scenariosWithMetadata = scenarios.map(scenario => {
      console.log('Processing scenario:', scenario.name);
      console.log('Original severity:', scenario.severity);
      console.log('Original attackPathId:', scenario.attackPathId);

      // Remove any custom _id field to let MongoDB generate it, but preserve attackPathId
      const { _id, id, ...scenarioData } = scenario;

      // Ensure attackPathId is preserved
      if (!scenarioData.attackPathId) {
        console.error('Missing attackPathId for scenario:', scenario.name);
        throw new Error(`Missing attackPathId for scenario: ${scenario.name}`);
      }

      // Transform enum values from English to French
      const transformedScenario = {
        ...scenarioData,
        analysisId,
        attackPathId: scenarioData.attackPathId, // Explicitly preserve attackPathId
        createdBy: req.user?.id || null,
        // Transform severity
        severity: transformEnumValue(scenario.severity, {
          'low': 'Très faible',
          'medium': 'Moyen',
          'high': 'Élevé',
          'critical': 'Très élevé'
        }),
        // Transform likelihood
        likelihood: transformEnumValue(scenario.likelihood, {
          'low': 'Très faible',
          'medium': 'Moyen',
          'high': 'Élevé'
        }),
        // Transform detection difficulty
        detectionDifficulty: transformEnumValue(scenario.detectionDifficulty, {
          'easy': 'Très faible',
          'medium': 'Moyen',
          'hard': 'Élevé'
        }),
        // Transform required skills
        requiredSkills: transformEnumValue(scenario.requiredSkills, {
          'basic': 'Débutant',
          'intermediate': 'Intermédiaire',
          'advanced': 'Avancé',
          'expert': 'Expert'
        }),
        // Transform steps to operationalSteps
        operationalSteps: (scenario.steps || []).map((step, index) => {
          // Remove custom _id and transform step structure
          const { _id, id, techniques, indicators, ...stepData } = step;

          // Use the phase from the AI response if available, otherwise map based on patterns
          const getPhase = (stepPhase, stepName, stepIndex) => {
            // If phase is explicitly provided by AI, use it
            if (stepPhase && ['CONNAITRE', 'RENTRER', 'TROUVER', 'EXPLOITER'].includes(stepPhase)) {
              return stepPhase;
            }

            // Fallback to pattern matching for backward compatibility
            const name = stepName?.toLowerCase() || '';
            if (name.includes('reconnaissance') || name.includes('collecte') || name.includes('information')) {
              return 'CONNAITRE';
            } else if (name.includes('accès') || name.includes('connexion') || name.includes('authentification') || name.includes('intrusion')) {
              return 'RENTRER';
            } else if (name.includes('recherche') || name.includes('découverte') || name.includes('exploration') || name.includes('latéralisation')) {
              return 'TROUVER';
            } else {
              return 'EXPLOITER';
            }
          };

          return {
            phase: getPhase(step.phase, step.name, index),
            stepNumber: index + 1,
            technique: Array.isArray(techniques) ? techniques.join(', ') : (techniques || step.name || 'Non spécifié'),
            description: step.description || 'Description non fournie',
            indicators: Array.isArray(indicators) ? indicators : (indicators ? [indicators] : []),
            tools: Array.isArray(techniques) ? techniques : (techniques ? [techniques] : []),
            duration: step.duration || 'Non spécifié',
            difficulty: transformEnumValue(step.difficulty, {
              'easy': 'Très faible',
              'medium': 'Moyen',
              'hard': 'Élevé'
            }) || 'Moyen'
          };
        }),
        // Add metadata for AI-generated scenarios
        metadata: {
          generatedByAI: true,
          aiModel: 'gemini-1.5-flash',
          generationDate: new Date(),
          lastModified: new Date()
        },
        // Add resources and timeline if missing
        resources: scenario.resources || 'Non spécifié',
        timeline: scenario.timeline || '1-7 jours'
      };

      console.log('Transformed scenario:', transformedScenario.name);
      console.log('Transformed severity:', transformedScenario.severity);
      console.log('Transformed likelihood:', transformedScenario.likelihood);
      console.log('Transformed detectionDifficulty:', transformedScenario.detectionDifficulty);
      console.log('Transformed requiredSkills:', transformedScenario.requiredSkills);

      return transformedScenario;
    });

    console.log('=== END TRANSFORMATION DEBUG ===');

    const createdScenarios = await OperationalScenario.insertMany(scenariosWithMetadata);

    // Log activity (if user is authenticated)
    if (req.user?.id) {
      await logActivity(
        req.user.id,
        req.user.companyId,
        'bulk_create',
        'operational-scenario',
        analysisId,
        { count: createdScenarios.length }
      );
    }

    res.status(201).json({
      success: true,
      data: createdScenarios,
      count: createdScenarios.length
    });
  } catch (error) {
    console.error('Error bulk creating operational scenarios:', error);
    res.status(500).json({
      success: false,
      message: 'Error bulk creating operational scenarios',
      error: error.message
    });
  }
};

// Get operational scenario statistics
exports.getOperationalScenarioStats = async (req, res) => {
  try {
    const { analysisId } = req.params;

    const stats = await OperationalScenario.aggregate([
      { $match: { analysisId: mongoose.Types.ObjectId(analysisId) } },
      {
        $group: {
          _id: null,
          totalScenarios: { $sum: 1 },
          avgStepsPerScenario: { $avg: { $size: '$operationalSteps' } },
          severityDistribution: {
            $push: '$severity'
          },
          likelihoodDistribution: {
            $push: '$likelihood'
          },
          aiGeneratedCount: {
            $sum: { $cond: ['$metadata.generatedByAI', 1, 0] }
          }
        }
      }
    ]);

    res.json({
      success: true,
      data: stats[0] || {
        totalScenarios: 0,
        avgStepsPerScenario: 0,
        severityDistribution: [],
        likelihoodDistribution: [],
        aiGeneratedCount: 0
      }
    });
  } catch (error) {
    console.error('Error fetching operational scenario stats:', error);
    res.status(500).json({
      success: false,
      message: 'Error fetching operational scenario statistics',
      error: error.message
    });
  }
};
