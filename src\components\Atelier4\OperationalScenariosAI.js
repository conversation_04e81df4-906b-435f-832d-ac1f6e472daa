// src/components/Atelier4/OperationalScenariosAI.js
import React, { useState, useEffect } from 'react';
import { Target, Brain, Zap, AlertTriangle, CheckCircle, Eye, Shield, ArrowRight, Save, RefreshCw, Plus, Edit2, X } from 'lucide-react';
import { ReactFlowProvider } from 'reactflow';
import { useAnalysis } from '../../context/AnalysisContext';
import { api } from '../../api/apiClient';
import { showSuccessToast, showErrorToast, showLoadingToast, updateToast } from '../../utils/toastUtils';
import ctiResultsService from '../../services/ctiResultsService';
import { generateScenariosFromCTI, saveCTIScenarios } from '../../services/operationalScenariosService';
import businessValueService from '../../services/businessValuesService';
import dreadedEventService from '../../services/dreadedEventsService';
import ScenarioVisualization from './ScenarioVisualization';

const OperationalScenariosAI = () => {
  const { currentAnalysis } = useAnalysis();
  const [ctiData, setCtiData] = useState(null);
  const [isLoadingCTI, setIsLoadingCTI] = useState(false);
  const [selectedAssets, setSelectedAssets] = useState([]);
  const [generatedScenarios, setGeneratedScenarios] = useState([]);
  const [isGenerating, setIsGenerating] = useState(false);
  const [businessValues, setBusinessValues] = useState([]);
  const [dreadedEvents, setDreadedEvents] = useState([]);
  const [selectedScenario, setSelectedScenario] = useState(null);
  const [editingScenario, setEditingScenario] = useState(null);
  const [showManualForm, setShowManualForm] = useState(false);
  const [manualScenario, setManualScenario] = useState({
    name: '',
    description: '',
    severity: '',
    likelihood: '',
    timeline: '',
    detectionDifficulty: '',
    attackPathId: '',
    attackPathReference: '',
    steps: [
      { phase: 'CONNAITRE', name: '', description: '', duration: '', difficulty: '', techniques: [], indicators: [], tools: [] },
      { phase: 'RENTRER', name: '', description: '', duration: '', difficulty: '', techniques: [], indicators: [], tools: [] },
      { phase: 'TROUVER', name: '', description: '', duration: '', difficulty: '', techniques: [], indicators: [], tools: [] },
      { phase: 'EXPLOITER', name: '', description: '', duration: '', difficulty: '', techniques: [], indicators: [], tools: [] }
    ]
  });

  // Load attack paths, CTI data and EBIOS RM data on component mount
  useEffect(() => {
    if (currentAnalysis) {
      loadAttackPathsAndCTI();
      loadEBIOSRMData();
    }
  }, [currentAnalysis]);

  const loadEBIOSRMData = async () => {
    try {
      // Load business values
      const businessValuesResponse = await businessValueService.getBusinessValues(currentAnalysis.id);
      if (businessValuesResponse?.businessValues) {
        setBusinessValues(businessValuesResponse.businessValues);
        console.log('[OperationalScenariosAI] Loaded business values:', businessValuesResponse.businessValues);
      }

      // Load dreaded events
      const dreadedEventsResponse = await dreadedEventService.getDreadedEvents(currentAnalysis.id);
      if (dreadedEventsResponse?.dreadedEvents) {
        setDreadedEvents(dreadedEventsResponse.dreadedEvents);
        console.log('[OperationalScenariosAI] Loaded dreaded events:', dreadedEventsResponse.dreadedEvents);
      }
    } catch (error) {
      console.error('Error loading EBIOS RM data:', error);
    }
  };

  // Function to intelligently map CTI assets to real EBIOS RM data
  const mapAssetToEBIOSRM = (assetName, businessValueName) => {
    // Find matching business value
    let matchedBusinessValue = null;
    if (businessValueName && businessValues.length > 0) {
      matchedBusinessValue = businessValues.find(bv =>
        bv.name.toLowerCase().includes(businessValueName.toLowerCase()) ||
        businessValueName.toLowerCase().includes(bv.name.toLowerCase())
      );
    }

    // If no direct match, try to match by asset name
    if (!matchedBusinessValue && assetName && businessValues.length > 0) {
      matchedBusinessValue = businessValues.find(bv =>
        bv.supportAssets?.some(asset =>
          asset.name.toLowerCase().includes(assetName.toLowerCase()) ||
          assetName.toLowerCase().includes(asset.name.toLowerCase())
        )
      );
    }

    // Find relevant dreaded events based on business value or asset
    let relevantDreadedEvents = [];
    if (dreadedEvents.length > 0) {
      relevantDreadedEvents = dreadedEvents.filter(event => {
        if (matchedBusinessValue) {
          // Match by security pillars
          return matchedBusinessValue.securityPillars?.some(pillar =>
            event.securityPillar === pillar
          );
        }
        // Default to confidentiality/integrity events for data assets
        return ['confidentialite', 'integrite', 'disponibilite'].includes(event.securityPillar);
      });
    }

    // Select the most relevant dreaded event (highest severity first)
    const selectedDreadedEvent = relevantDreadedEvents.length > 0
      ? relevantDreadedEvents.sort((a, b) => {
          const severityOrder = { 'catastrophic': 5, 'critical': 4, 'major': 3, 'moderate': 2, 'minor': 1 };
          return (severityOrder[b.severity] || 0) - (severityOrder[a.severity] || 0);
        })[0]
      : null;

    return {
      businessValue: matchedBusinessValue,
      dreadedEvent: selectedDreadedEvent,
      // Generate realistic threat sources based on asset type
      threatSource: assetName?.toLowerCase().includes('serveur') || assetName?.toLowerCase().includes('server')
        ? 'Cybercriminels ciblant les infrastructures'
        : assetName?.toLowerCase().includes('base') || assetName?.toLowerCase().includes('database')
        ? 'Attaquants spécialisés en exfiltration de données'
        : 'Menaces cybernétiques avancées',
      // Generate realistic objectives
      objective: selectedDreadedEvent
        ? `Causer l'événement redouté: ${selectedDreadedEvent.name}`
        : matchedBusinessValue
        ? `Compromettre la valeur métier: ${matchedBusinessValue.name}`
        : 'Exploitation des vulnérabilités identifiées'
    };
  };

  const loadAttackPathsAndCTI = async () => {
    try {
      setIsLoadingCTI(true);

      // Step 1: Load all attack paths from the analysis
      console.log('[OperationalScenariosAI] Loading attack paths from analysis...');
      const attackPathsResponse = await api.get(`/analyses/${currentAnalysis.id}/attack-paths`);

      let allAttackPaths = [];
      if (attackPathsResponse.data?.attackPaths) {
        allAttackPaths = attackPathsResponse.data.attackPaths;
      } else if (Array.isArray(attackPathsResponse.data)) {
        allAttackPaths = attackPathsResponse.data;
      }

      console.log('[OperationalScenariosAI] Loaded attack paths:', allAttackPaths);
      console.log('[OperationalScenariosAI] Attack paths structure:', allAttackPaths.map(ap => ({
        id: ap.id,
        pathId: ap.pathId,
        name: ap.name,
        pathName: ap.pathName,
        referenceCode: ap.referenceCode,
        objectifVise: ap.objectifVise
      })));

      // Step 2: Load CTI analysis results
      console.log('[OperationalScenariosAI] Loading CTI analysis results...');
      const ctiResponse = await ctiResultsService.getCTIResults(currentAnalysis.id);

      // Handle the nested data structure
      let ctiDataToUse = null;
      if (ctiResponse?.data?.data) {
        ctiDataToUse = ctiResponse.data.data;
      } else if (ctiResponse?.data) {
        ctiDataToUse = ctiResponse.data;
      }

      console.log('[OperationalScenariosAI] CTI data:', ctiDataToUse);

      // Step 3: Match attack paths with their CTI analysis
      const attackPathsWithCTI = [];

      if (allAttackPaths.length > 0 && ctiDataToUse && (ctiDataToUse.vulnerabilities?.length > 0 || ctiDataToUse.techniques?.length > 0)) {
        console.log('[OperationalScenariosAI] Matching attack paths with CTI data...');

        allAttackPaths.forEach(attackPath => {
          console.log(`[OperationalScenariosAI] Checking attack path:`, {
            id: attackPath.id,
            pathId: attackPath.pathId,
            name: attackPath.name || attackPath.pathName,
            referenceCode: attackPath.referenceCode
          });

          // Find CTI data for this attack path - match by attackPathId field
          const pathVulns = ctiDataToUse.vulnerabilities?.filter(v => {
            const matches = v.attackPathId === attackPath.id;
            if (matches) {
              console.log(`[OperationalScenariosAI] Found vulnerability match:`, v.id, 'for path', attackPath.id);
            }
            return matches;
          }) || [];

          const pathTechs = ctiDataToUse.techniques?.filter(t => {
            const matches = t.attackPathId === attackPath.id;
            if (matches) {
              console.log(`[OperationalScenariosAI] Found technique match:`, t.id, 'for path', attackPath.id);
            }
            return matches;
          }) || [];

          // Only include attack paths that have CTI analysis
          if (pathVulns.length > 0 || pathTechs.length > 0) {
            console.log(`[OperationalScenariosAI] Attack path "${attackPath.name || attackPath.pathName}" has ${pathVulns.length} vulnerabilities and ${pathTechs.length} techniques`);

            attackPathsWithCTI.push({
              // Use attack path data as primary source
              id: attackPath.id || attackPath.pathId,
              pathId: attackPath.id || attackPath.pathId,
              pathName: attackPath.name || attackPath.pathName || attackPath.referenceCode || attackPath.objectifVise || `Attack Path ${attackPath.id}`,
              referenceCode: attackPath.referenceCode,
              businessValueName: attackPath.businessValueName,
              sourceRiskName: attackPath.sourceRiskName,
              dreadedEventName: attackPath.dreadedEventName,
              objectifVise: attackPath.objectifVise,
              stakeholders: attackPath.stakeholders,
              description: attackPath.description,

              // Add CTI analysis data
              vulnerabilities: pathVulns,
              attackTechniques: pathTechs,
              vulnerabilitiesCount: pathVulns.length,
              techniquesCount: pathTechs.length,
              assetsCount: attackPath.assetsCount || 1,

              // Additional EBIOS RM context
              businessValueType: attackPath.businessValueType,
              businessValueDescription: attackPath.businessValueDescription,
              dreadedEventDescription: attackPath.dreadedEventDescription,
              dreadedEventSeverity: attackPath.dreadedEventSeverity,
              sourceRiskType: attackPath.sourceRiskType,
              sourceRiskDescription: attackPath.sourceRiskDescription,
              sourceRiskCapabilities: attackPath.sourceRiskCapabilities
            });
          } else {
            console.log(`[OperationalScenariosAI] Attack path "${attackPath.name || attackPath.pathName}" has no CTI analysis - skipping`);
          }
        });
      }

      const processedCTIData = {
        attackPathsWithCTI: attackPathsWithCTI,
        vulnerabilities: ctiDataToUse?.vulnerabilities || [],
        techniques: ctiDataToUse?.techniques || [],
        totalAttackPaths: allAttackPaths.length,
        attackPathsWithCTICount: attackPathsWithCTI.length
      };

      console.log('[OperationalScenariosAI] Final results:');
      console.log(`- Total attack paths: ${allAttackPaths.length}`);
      console.log(`- Attack paths with CTI: ${attackPathsWithCTI.length}`);
      console.log(`- Total vulnerabilities: ${ctiDataToUse?.vulnerabilities?.length || 0}`);
      console.log(`- Total techniques: ${ctiDataToUse?.techniques?.length || 0}`);

      setCtiData(processedCTIData);

      // Auto-select attack paths that have CTI data
      if (attackPathsWithCTI.length > 0) {
        setSelectedAssets(attackPathsWithCTI.map(path => path.id));
      }

    } catch (error) {
      console.error('[OperationalScenariosAI] Error loading attack paths and CTI data:', error);
      setCtiData({ attackPathsWithCTI: [], vulnerabilities: [], techniques: [], totalAttackPaths: 0, attackPathsWithCTICount: 0 });
    } finally {
      setIsLoadingCTI(false);
    }
  };

  const handleAssetToggle = (assetId) => {
    setSelectedAssets(prev =>
      prev.includes(assetId)
        ? prev.filter(id => id !== assetId)
        : [...prev, assetId]
    );
  };

  const generateOperationalScenarios = async () => {
    if (!ctiData || selectedAssets.length === 0) {
      showErrorToast('Veuillez sélectionner au moins un chemin d\'attaque avec des données CTI');
      return;
    }

    const toastId = showLoadingToast('Génération des scénarios opérationnels avec IA...');

    try {
      setIsGenerating(true);

      // Prepare CTI data for selected attack paths
      const selectedAttackPathsData = ctiData.attackPathsWithCTI?.filter(attackPath =>
        selectedAssets.includes(attackPath.id)
      ) || [];

      // Prepare data for existing AI endpoint
      const aiPayload = {
        analysisId: currentAnalysis.id,
        attackPaths: selectedAttackPathsData.map(attackPath => ({
          // Attack Path Core Info
          pathId: attackPath.pathId,
          pathName: attackPath.pathName,
          referenceCode: attackPath.referenceCode,

          // EBIOS RM Context
          businessValue: attackPath.businessValueName,
          threatSource: attackPath.sourceRiskName,
          threatObjective: attackPath.objectifVise,
          dreadedEvent: attackPath.dreadedEventName,
          stakeholders: attackPath.stakeholders || [],

          // CTI Analysis Data
          businessAssets: attackPath.businessAssets || [],
          supportAssets: attackPath.supportAssets || [],
          vulnerabilities: attackPath.vulnerabilities || [],
          attackTechniques: attackPath.attackTechniques || [],

          // Metrics
          assetsCount: attackPath.assetsCount,
          vulnerabilitiesCount: attackPath.vulnerabilities?.length || 0,
          techniquesCount: attackPath.attackTechniques?.length || 0
        })),

        // Options for scenario generation
        options: {
          scenariosPerPath: 2,
          detailLevel: 'comprehensive',
          includeIndicators: true,
          includeTools: true,
          includeEBIOSPhases: true,
          phases: ['CONNAÎTRE', 'RENTRER', 'TROUVER', 'EXPLOITER'],
          attackerProfile: 'cybercrime',
          attackGoal: 'data-theft',
          complexity: 'medium'
        }
      };

      // Validate data before sending to AI
      const totalVulns = selectedAttackPathsData.reduce((sum, path) => sum + (path.vulnerabilities?.length || 0), 0);
      const totalTechs = selectedAttackPathsData.reduce((sum, path) => sum + (path.attackTechniques?.length || 0), 0);

      console.log('[OperationalScenariosAI] Data validation:', {
        attackPaths: selectedAttackPathsData.length,
        totalVulnerabilities: totalVulns,
        totalTechniques: totalTechs,
        hasData: totalVulns > 0 || totalTechs > 0
      });

      if (totalVulns === 0 && totalTechs === 0) {
        throw new Error('Aucune donnée CTI (vulnérabilités ou techniques) trouvée pour les chemins d\'attaque sélectionnés. Veuillez d\'abord effectuer une analyse CTI.');
      }

      console.log('[OperationalScenariosAI] Sending data to existing AI endpoint:', aiPayload);

      // Log the specific techniques being sent to AI
      console.log('[OperationalScenariosAI] CTI Data being sent to AI:');
      selectedAttackPathsData.forEach((path, index) => {
        console.log(`Attack Path ${index + 1}: ${path.pathName}`);
        console.log('- Vulnerabilities:', path.vulnerabilities?.length || 0);
        if (path.vulnerabilities?.length > 0) {
          console.log('  Vulnerability details:', path.vulnerabilities.map(v => ({
            id: v.cveId || v.id,
            severity: v.severity?.severity || v.severity,
            description: v.description?.substring(0, 100) + '...'
          })));
        }
        console.log('- Attack Techniques from CTI:', path.attackTechniques?.map(t => `${t.id} (${t.name})`));
        console.log('- Business Assets:', path.businessAssets?.length || 0);
        console.log('- Support Assets:', path.supportAssets?.length || 0);
      });

      // Use existing AI endpoint
      const response = await api.post('/ai/generate-operational-scenarios', aiPayload);

      console.log('[OperationalScenariosAI] Full AI Response:', JSON.stringify(response, null, 2));
      console.log('[OperationalScenariosAI] Response data:', response.data);
      console.log('[OperationalScenariosAI] Response success:', response.data?.success);
      console.log('[OperationalScenariosAI] Response scenarios:', response.data?.scenarios);
      console.log('[OperationalScenariosAI] Response data field:', response.data?.data);
      console.log('[OperationalScenariosAI] Response message:', response.data?.message);
      console.log('[OperationalScenariosAI] Response error:', response.data?.error);

      // Handle different response formats
      if (response.data && (response.data.success || response.data.scenarios)) {
        // Handle nested data structure: response.data.data.scenarios
        let scenarios = response.data.scenarios || response.data.data?.scenarios || response.data.data || [];

        console.log('[OperationalScenariosAI] Extracted scenarios:', scenarios);
        console.log('[OperationalScenariosAI] Scenarios type:', typeof scenarios);
        console.log('[OperationalScenariosAI] Scenarios is array:', Array.isArray(scenarios));
        console.log('[OperationalScenariosAI] Scenarios length:', scenarios.length);

        if (Array.isArray(scenarios) && scenarios.length > 0) {
          console.log('[OperationalScenariosAI] Detailed scenario analysis:');
          scenarios.forEach((scenario, index) => {
            console.log(`Scenario ${index + 1}:`, {
              name: scenario?.name,
              description: scenario?.description,
              steps: scenario?.steps,
              stepsCount: scenario?.steps?.length || 0,
              hasSteps: !!scenario?.steps,
              severity: scenario?.severity,
              timeline: scenario?.timeline,
              fullScenario: scenario
            });
          });

          setGeneratedScenarios(scenarios);
          updateToast(toastId, `${scenarios.length} scénarios opérationnels générés avec succès !`, 'success');
        } else {
          console.error('[OperationalScenariosAI] Empty or invalid scenarios array:', scenarios);
          console.error('[OperationalScenariosAI] AI Input Data Summary:', {
            attackPathsCount: selectedAttackPathsData.length,
            totalVulnerabilities: selectedAttackPathsData.reduce((sum, path) => sum + (path.vulnerabilities?.length || 0), 0),
            totalTechniques: selectedAttackPathsData.reduce((sum, path) => sum + (path.attackTechniques?.length || 0), 0),
            pathsWithData: selectedAttackPathsData.filter(path =>
              (path.vulnerabilities?.length > 0) || (path.attackTechniques?.length > 0)
            ).length
          });

          // More helpful error message
          const hasData = selectedAttackPathsData.some(path =>
            (path.vulnerabilities?.length > 0) || (path.attackTechniques?.length > 0)
          );

          if (!hasData) {
            throw new Error('Aucune donnée CTI trouvée pour générer des scénarios. Veuillez d\'abord effectuer une analyse CTI.');
          } else {
            throw new Error('L\'IA n\'a pas pu générer de scénarios avec les données fournies. Essayez avec d\'autres chemins d\'attaque ou vérifiez la qualité des données CTI.');
          }
        }
      } else {
        console.error('[OperationalScenariosAI] AI generation failed - no success flag or scenarios:', response.data);
        throw new Error(response.data?.message || response.data?.error || 'Erreur lors de la génération des scénarios');
      }

    } catch (error) {
      console.error('[OperationalScenariosAI] Error generating operational scenarios:', error);
      console.error('[OperationalScenariosAI] Error details:', {
        message: error.message,
        status: error.status,
        data: error.data,
        response: error.response?.data
      });

      let errorMessage = 'Erreur lors de la génération des scénarios';
      if (error.response?.data?.message) {
        errorMessage = error.response.data.message;
      } else if (error.data?.message) {
        errorMessage = error.data.message;
      } else if (error.message) {
        errorMessage = error.message;
      }

      updateToast(toastId, `Erreur: ${errorMessage}`, 'error');
    } finally {
      setIsGenerating(false);
    }
  };

  // Handle editing a scenario
  const handleEditScenario = (scenario) => {
    // Ensure steps exist with proper structure
    const defaultSteps = [
      { phase: 'CONNAITRE', name: '', description: '', duration: '', difficulty: '', techniques: [], indicators: [], tools: [] },
      { phase: 'RENTRER', name: '', description: '', duration: '', difficulty: '', techniques: [], indicators: [], tools: [] },
      { phase: 'TROUVER', name: '', description: '', duration: '', difficulty: '', techniques: [], indicators: [], tools: [] },
      { phase: 'EXPLOITER', name: '', description: '', duration: '', difficulty: '', techniques: [], indicators: [], tools: [] }
    ];

    const editingData = {
      ...scenario,
      steps: scenario.steps && scenario.steps.length > 0 ? scenario.steps : defaultSteps
    };

    setEditingScenario(editingData);
  };

  // Save edited scenario
  const saveEditedScenario = () => {
    if (!editingScenario) return;

    const updatedScenarios = generatedScenarios.map(scenario =>
      scenario === selectedScenario ? editingScenario : scenario
    );
    setGeneratedScenarios(updatedScenarios);
    setSelectedScenario(editingScenario);
    setEditingScenario(null);
    showSuccessToast('Scénario modifié avec succès');
  };

  // Cancel editing
  const cancelEdit = () => {
    setEditingScenario(null);
  };

  // Handle manual scenario creation
  const handleAddManualScenario = () => {
    setShowManualForm(true);
  };

  // Save manual scenario
  const saveManualScenario = () => {
    if (!manualScenario.name.trim()) {
      showErrorToast('Veuillez saisir un nom pour le scénario');
      return;
    }

    const newScenario = {
      ...manualScenario,
      id: Date.now().toString(),
      isManual: true
    };

    setGeneratedScenarios([...generatedScenarios, newScenario]);
    setManualScenario({
      name: '',
      description: '',
      severity: '',
      likelihood: '',
      timeline: '',
      detectionDifficulty: '',
      steps: [
        { phase: 'CONNAITRE', name: '', description: '', duration: '', difficulty: '', techniques: [], indicators: [], tools: [] },
        { phase: 'RENTRER', name: '', description: '', duration: '', difficulty: '', techniques: [], indicators: [], tools: [] },
        { phase: 'TROUVER', name: '', description: '', duration: '', difficulty: '', techniques: [], indicators: [], tools: [] },
        { phase: 'EXPLOITER', name: '', description: '', duration: '', difficulty: '', techniques: [], indicators: [], tools: [] }
      ]
    });
    setShowManualForm(false);
    showSuccessToast('Scénario manuel ajouté avec succès');
  };

  // Cancel manual scenario creation
  const cancelManualScenario = () => {
    setShowManualForm(false);
    setManualScenario({
      name: '',
      description: '',
      severity: '',
      likelihood: '',
      timeline: '',
      detectionDifficulty: '',
      attackPathId: '',
      attackPathReference: '',
      steps: [
        { phase: 'CONNAITRE', name: '', description: '', duration: '', difficulty: '', techniques: [], indicators: [], tools: [] },
        { phase: 'RENTRER', name: '', description: '', duration: '', difficulty: '', techniques: [], indicators: [], tools: [] },
        { phase: 'TROUVER', name: '', description: '', duration: '', difficulty: '', techniques: [], indicators: [], tools: [] },
        { phase: 'EXPLOITER', name: '', description: '', duration: '', difficulty: '', techniques: [], indicators: [], tools: [] }
      ]
    });
  };

  const saveScenarios = async () => {
    if (generatedScenarios.length === 0) {
      showErrorToast('Aucun scénario à sauvegarder');
      return;
    }

    const toastId = showLoadingToast('Sauvegarde des scénarios...');

    try {
      const result = await saveCTIScenarios(currentAnalysis.id, generatedScenarios);

      if (result.success) {
        updateToast(toastId, 'Scénarios sauvegardés avec succès !', 'success');
      } else {
        throw new Error(result.message || 'Erreur lors de la sauvegarde');
      }

    } catch (error) {
      console.error('Error saving scenarios:', error);
      updateToast(toastId, `Erreur lors de la sauvegarde: ${error.message}`, 'error');
    }
  };

  if (isLoadingCTI) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span className="ml-3 text-gray-600">Chargement des données CTI...</span>
      </div>
    );
  }

  if (!ctiData) {
    return (
      <div className="text-center py-12">
        <AlertTriangle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">
          Aucune donnée CTI disponible
        </h3>
        <p className="text-gray-600 mb-4">
          Vous devez d'abord effectuer une analyse CTI dans l'onglet "Intelligence des menaces"
        </p>
        <button
          onClick={loadAttackPathsAndCTI}
          className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
        >
          <RefreshCw className="h-4 w-4 mr-2" />
          Recharger
        </button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* === MAIN HEADER === */}
      <div className="bg-white rounded-xl shadow-sm border border-slate-100 p-6">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div>
            <div className="flex items-center text-sm text-slate-500 mb-2">
              <span className="hover:text-blue-600 transition-colors">Atelier 4</span>
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mx-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
              <span className="text-blue-600 font-medium">Scénarios opérationnels IA</span>
            </div>
            <h1 className="text-2xl sm:text-3xl font-bold text-slate-800 flex items-center">
              <Brain size={28} className="mr-3 text-blue-600" />
              Génération IA de Scénarios Opérationnels
            </h1>
            <p className="text-slate-600 mt-1">
              Génération automatique de scénarios d'attaque basés sur vos données CTI
            </p>
          </div>

          <div className="flex items-center space-x-3">
            {ctiData && (
              <div className="text-right">
                <div className="text-sm font-medium text-slate-700">
                  {ctiData.attackPathsWithCTI?.length || 0} chemins d'attaque • {ctiData.vulnerabilities?.length || 0} vulnérabilités
                </div>
                <div className="text-xs text-slate-500">
                  {ctiData.techniques?.length || 0} techniques d'attaque disponibles
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* CTI Data Summary */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div className="flex items-center mb-2">
          <Shield className="h-5 w-5 text-blue-600 mr-2" />
          <h3 className="font-semibold text-blue-900">Données CTI Disponibles</h3>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-blue-800">
          <div className="flex items-center">
            <Target className="h-4 w-4 mr-2" />
            {ctiData.attackPathsWithCTI?.length || 0} Chemins d'attaque analysés
          </div>
          <div className="flex items-center">
            <AlertTriangle className="h-4 w-4 mr-2" />
            {ctiData.vulnerabilities?.length || 0} Vulnérabilités
          </div>
          <div className="flex items-center">
            <Zap className="h-4 w-4 mr-2" />
            {ctiData.techniques?.length || 0} Techniques d'attaque
          </div>
        </div>
      </div>

      {/* Attack Path Selection */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
          <Eye className="h-5 w-5 mr-2" />
          Sélection des Chemins d'Attaque pour les Scénarios
        </h3>

        {!ctiData.attackPathsWithCTI || ctiData.attackPathsWithCTI.length === 0 ? (
          <div className="text-center py-8 bg-gray-50 rounded-lg">
            <Target className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h4 className="text-lg font-medium text-gray-900 mb-2">
              Aucun chemin d'attaque avec analyse CTI trouvé
            </h4>
            <p className="text-gray-600 mb-4">
              Pour générer des scénarios opérationnels, vous devez :
            </p>
            <div className="text-left max-w-md mx-auto space-y-2 text-sm text-gray-600">
              <div className="flex items-center">
                <span className="w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-xs font-semibold mr-3">1</span>
                Avoir créé des <strong>chemins d'attaque</strong> dans l'Atelier 3
              </div>
              <div className="flex items-center">
                <span className="w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-xs font-semibold mr-3">2</span>
                Effectuer une <strong>analyse CTI</strong> sur ces chemins dans l'onglet "Intelligence des menaces"
              </div>
              <div className="flex items-center">
                <span className="w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-xs font-semibold mr-3">3</span>
                Revenir ici pour sélectionner les chemins analysés
              </div>
            </div>
          </div>
        ) : (
          <div className="grid gap-6 md:grid-cols-1 lg:grid-cols-2">
            {ctiData.attackPathsWithCTI.map((attackPath) => (
            <div
              key={attackPath.id}
              className={`
                relative p-6 border-2 rounded-xl cursor-pointer transition-all duration-200
                ${selectedAssets.includes(attackPath.id)
                  ? 'border-blue-500 bg-blue-50 shadow-lg'
                  : 'border-gray-200 bg-white hover:border-gray-300 hover:shadow-md'
                }
              `}
              onClick={() => handleAssetToggle(attackPath.id)}
            >
              {/* Header with selection indicator */}
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center space-x-2">
                  {attackPath.referenceCode && (
                    <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-semibold bg-indigo-100 text-indigo-800">
                      {attackPath.referenceCode}
                    </span>
                  )}
                  <h4 className="font-semibold text-gray-900">
                    {attackPath.pathName}
                  </h4>
                </div>
                {selectedAssets.includes(attackPath.id) && (
                  <CheckCircle className="h-6 w-6 text-blue-600" />
                )}
              </div>

              {/* EBIOS RM Attack Sequence */}
              <div className="space-y-3 mb-4">
                {/* Business Value (Target) */}
                <div className="flex items-center bg-blue-100 rounded-lg px-3 py-2">
                  <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center mr-3">
                    <Target className="h-3 w-3 text-white" />
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-xs text-blue-600 font-medium">Valeur Métier Ciblée</p>
                    <p className="text-sm font-semibold text-blue-900 truncate">{attackPath.businessValueName}</p>
                  </div>
                </div>

                {/* Threat Source */}
                {attackPath.sourceRiskName && (
                  <div className="flex items-center bg-red-100 rounded-lg px-3 py-2">
                    <div className="w-6 h-6 bg-red-500 rounded-full flex items-center justify-center mr-3">
                      <AlertTriangle className="h-3 w-3 text-white" />
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="text-xs text-red-600 font-medium">Source de Menace</p>
                      <p className="text-sm font-semibold text-red-900 truncate">{attackPath.sourceRiskName}</p>
                    </div>
                  </div>
                )}

                {/* Dreaded Event */}
                {attackPath.dreadedEventName && (
                  <div className="flex items-center bg-orange-100 rounded-lg px-3 py-2">
                    <div className="w-6 h-6 bg-orange-500 rounded-full flex items-center justify-center mr-3">
                      <Zap className="h-3 w-3 text-white" />
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="text-xs text-orange-600 font-medium">Événement Redouté</p>
                      <p className="text-sm font-semibold text-orange-900 truncate">{attackPath.dreadedEventName}</p>
                    </div>
                  </div>
                )}
              </div>

              {/* CTI Analysis Summary */}
              <div className="bg-gray-50 rounded-lg p-3">
                <h5 className="text-xs font-semibold text-gray-700 mb-2">Analyse CTI Disponible</h5>
                <div className="grid grid-cols-3 gap-2 text-xs">
                  <div className="text-center">
                    <div className="font-bold text-gray-900">{attackPath.assetsCount || 0}</div>
                    <div className="text-gray-600">Actifs</div>
                  </div>
                  <div className="text-center">
                    <div className="font-bold text-red-600">{attackPath.vulnerabilities?.length || 0}</div>
                    <div className="text-gray-600">CVE</div>
                  </div>
                  <div className="text-center">
                    <div className="font-bold text-purple-600">{attackPath.attackTechniques?.length || 0}</div>
                    <div className="text-gray-600">Techniques</div>
                  </div>
                </div>
              </div>

              {/* Selection Status */}
              {selectedAssets.includes(attackPath.id) && (
                <div className="absolute top-2 right-2">
                  <div className="bg-blue-500 text-white rounded-full p-1">
                    <CheckCircle className="h-4 w-4" />
                  </div>
                </div>
              )}
              </div>
            ))}
          </div>
        )}
      </div>



      {/* Selected Attack Paths Summary */}
      {selectedAssets.length > 0 && (
        <div className="bg-green-50 border border-green-200 rounded-lg p-4">
          <div className="flex items-center mb-3">
            <CheckCircle className="h-5 w-5 text-green-600 mr-2" />
            <h3 className="font-semibold text-green-900">Contexte EBIOS RM pour l'IA</h3>
          </div>
          <p className="text-sm text-green-800 mb-3">
            Les informations suivantes seront transmises à l'IA pour générer des scénarios opérationnels réalistes :
          </p>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            {ctiData.attackPathsWithCTI?.filter(path => selectedAssets.includes(path.id)).map((path, index) => (
              <div key={path.id} className="bg-white rounded-lg p-3 border border-green-200">
                <div className="font-medium text-green-900 mb-2">{path.pathName}</div>
                <div className="space-y-1 text-xs text-green-700">
                  <div>🎯 <strong>Cible:</strong> {path.businessValueName}</div>
                  {path.sourceRiskName && <div>⚡ <strong>Menace:</strong> {path.sourceRiskName}</div>}
                  {path.dreadedEventName && <div>💥 <strong>Impact:</strong> {path.dreadedEventName}</div>}
                  {path.objectifVise && <div>🎪 <strong>Objectif:</strong> {path.objectifVise}</div>}
                  <div>🔍 <strong>CTI:</strong> {path.vulnerabilities?.length || 0} CVE, {path.attackTechniques?.length || 0} techniques</div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Generation Controls */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              Génération IA des Scénarios Opérationnels
            </h3>
            <p className="text-sm text-gray-600">
              {selectedAssets.length} chemin{selectedAssets.length > 1 ? 's' : ''} d'attaque sélectionné{selectedAssets.length > 1 ? 's' : ''} pour la génération IA
            </p>
          </div>

          <div className="flex space-x-3">
            <button
              onClick={generateOperationalScenarios}
              disabled={isGenerating || selectedAssets.length === 0}
              className={`
                inline-flex items-center px-6 py-3 rounded-lg font-medium transition-all duration-200
                ${isGenerating || selectedAssets.length === 0
                  ? 'bg-gray-100 text-gray-500 cursor-not-allowed'
                  : 'bg-blue-600 text-white hover:bg-blue-700 shadow-md hover:shadow-lg'
                }
              `}
            >
              {isGenerating ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Génération...
                </>
              ) : (
                <>
                  <Brain className="h-4 w-4 mr-2" />
                  Générer les Scénarios Opérationnels
                </>
              )}
            </button>

            {/* Manual Scenario Button */}
            <button
              onClick={handleAddManualScenario}
              className="flex items-center px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
            >
              <Plus className="h-4 w-4 mr-2" />
              Ajouter un Scénario Manuel
            </button>

            {generatedScenarios.length > 0 && (
              <button
                onClick={saveScenarios}
                className="inline-flex items-center px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 font-medium transition-all duration-200 shadow-md hover:shadow-lg"
              >
                <Save className="h-4 w-4 mr-2" />
                Sauvegarder
              </button>
            )}
          </div>
        </div>
      </div>

      {/* Manual Scenario Form */}
      {showManualForm && (
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900">Créer un Scénario Manuel</h3>
            <button
              onClick={cancelManualScenario}
              className="text-gray-400 hover:text-gray-600"
            >
              <X size={20} />
            </button>
          </div>

          <div className="space-y-4">
            {/* Basic Info */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Nom du scénario *</label>
                <input
                  type="text"
                  value={manualScenario.name}
                  onChange={(e) => setManualScenario({...manualScenario, name: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Nom du scénario"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Sévérité</label>
                <select
                  value={manualScenario.severity}
                  onChange={(e) => setManualScenario({...manualScenario, severity: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="">Sélectionner</option>
                  <option value="Faible">Faible</option>
                  <option value="Modérée">Modérée</option>
                  <option value="Élevée">Élevée</option>
                  <option value="Critique">Critique</option>
                </select>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Description</label>
              <textarea
                value={manualScenario.description}
                onChange={(e) => setManualScenario({...manualScenario, description: e.target.value})}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                rows="3"
                placeholder="Description du scénario"
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Probabilité</label>
                <input
                  type="text"
                  value={manualScenario.likelihood}
                  onChange={(e) => setManualScenario({...manualScenario, likelihood: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Ex: Élevée"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Durée</label>
                <input
                  type="text"
                  value={manualScenario.timeline}
                  onChange={(e) => setManualScenario({...manualScenario, timeline: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Ex: 2-4 semaines"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Difficulté de détection</label>
                <input
                  type="text"
                  value={manualScenario.detectionDifficulty}
                  onChange={(e) => setManualScenario({...manualScenario, detectionDifficulty: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Ex: Élevée"
                />
              </div>
            </div>

            {/* Attack Path Association */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Chemin d'attaque associé</label>
              <select
                value={manualScenario.attackPathId || ''}
                onChange={(e) => setManualScenario({...manualScenario, attackPathId: e.target.value, attackPathReference: ctiData?.attackPathsWithCTI?.find(path => path.id === e.target.value)?.referenceCode})}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="">Aucun chemin d'attaque associé</option>
                {ctiData?.attackPathsWithCTI?.map(path => (
                  <option key={path.id} value={path.id}>
                    {path.referenceCode} - {path.pathName}
                  </option>
                ))}
              </select>
            </div>

            {/* Phase Details */}
            <div className="border-t border-gray-200 pt-4">
              <h4 className="text-md font-semibold text-gray-900 mb-4">Détails des Phases EBIOS RM</h4>
              <div className="space-y-6">
                {manualScenario.steps.map((step, stepIndex) => (
                  <div key={stepIndex} className="border border-gray-200 rounded-lg p-4">
                    <div className="flex items-center mb-3">
                      <div className={`w-8 h-8 rounded-full flex items-center justify-center text-white font-bold mr-3 ${
                        step.phase === 'CONNAITRE' ? 'bg-blue-500' :
                        step.phase === 'RENTRER' ? 'bg-red-500' :
                        step.phase === 'TROUVER' ? 'bg-amber-500' : 'bg-green-500'
                      }`}>
                        {stepIndex + 1}
                      </div>
                      <h5 className="font-semibold text-gray-900">{step.phase}</h5>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">Nom de l'étape</label>
                        <input
                          type="text"
                          value={step.name}
                          onChange={(e) => {
                            const newSteps = [...manualScenario.steps];
                            newSteps[stepIndex].name = e.target.value;
                            setManualScenario({...manualScenario, steps: newSteps});
                          }}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                          placeholder={`Nom de la phase ${step.phase}`}
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">Durée</label>
                        <input
                          type="text"
                          value={step.duration}
                          onChange={(e) => {
                            const newSteps = [...manualScenario.steps];
                            newSteps[stepIndex].duration = e.target.value;
                            setManualScenario({...manualScenario, steps: newSteps});
                          }}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                          placeholder="Ex: 1-2 jours"
                        />
                      </div>
                    </div>

                    <div className="mb-4">
                      <label className="block text-sm font-medium text-gray-700 mb-1">Description</label>
                      <textarea
                        value={step.description}
                        onChange={(e) => {
                          const newSteps = [...manualScenario.steps];
                          newSteps[stepIndex].description = e.target.value;
                          setManualScenario({...manualScenario, steps: newSteps});
                        }}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        rows="2"
                        placeholder={`Description de la phase ${step.phase}`}
                      />
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">Techniques (une par ligne)</label>
                        <textarea
                          value={step.techniques.map(t => typeof t === 'string' ? t : t.name || t.technique || '').join('\n')}
                          onChange={(e) => {
                            const newSteps = [...manualScenario.steps];
                            newSteps[stepIndex].techniques = e.target.value.split('\n').filter(t => t.trim()).map(t => ({ name: t.trim() }));
                            setManualScenario({...manualScenario, steps: newSteps});
                          }}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                          rows="3"
                          placeholder="Ex: Reconnaissance passive&#10;Scan de ports&#10;Énumération des services"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">Indicateurs (un par ligne)</label>
                        <textarea
                          value={step.indicators.map(i => typeof i === 'string' ? i : i.name || i.indicator || '').join('\n')}
                          onChange={(e) => {
                            const newSteps = [...manualScenario.steps];
                            newSteps[stepIndex].indicators = e.target.value.split('\n').filter(i => i.trim()).map(i => ({ name: i.trim() }));
                            setManualScenario({...manualScenario, steps: newSteps});
                          }}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                          rows="3"
                          placeholder="Ex: Trafic DNS anormal&#10;Connexions suspectes&#10;Logs d'authentification"
                        />
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            <div className="flex justify-end space-x-3">
              <button
                onClick={cancelManualScenario}
                className="px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50"
              >
                Annuler
              </button>
              <button
                onClick={saveManualScenario}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
              >
                <Save size={16} className="inline mr-2" />
                Sauvegarder
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Edit Scenario Form */}
      {editingScenario && (
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900">Modifier le Scénario</h3>
            <button
              onClick={cancelEdit}
              className="text-gray-400 hover:text-gray-600"
            >
              <X size={20} />
            </button>
          </div>

          <div className="space-y-4">
            {/* Basic Info */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Nom du scénario *</label>
                <input
                  type="text"
                  value={editingScenario.name || ''}
                  onChange={(e) => setEditingScenario({...editingScenario, name: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Sévérité</label>
                <select
                  value={editingScenario.severity || ''}
                  onChange={(e) => setEditingScenario({...editingScenario, severity: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="">Sélectionner</option>
                  <option value="Faible">Faible</option>
                  <option value="Modérée">Modérée</option>
                  <option value="Élevée">Élevée</option>
                  <option value="Critique">Critique</option>
                </select>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Description</label>
              <textarea
                value={editingScenario.description || ''}
                onChange={(e) => setEditingScenario({...editingScenario, description: e.target.value})}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                rows="3"
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Probabilité</label>
                <input
                  type="text"
                  value={editingScenario.likelihood || ''}
                  onChange={(e) => setEditingScenario({...editingScenario, likelihood: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Durée</label>
                <input
                  type="text"
                  value={editingScenario.timeline || ''}
                  onChange={(e) => setEditingScenario({...editingScenario, timeline: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Difficulté de détection</label>
                <input
                  type="text"
                  value={editingScenario.detectionDifficulty || ''}
                  onChange={(e) => setEditingScenario({...editingScenario, detectionDifficulty: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
            </div>

            {/* Attack Path Association */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Chemin d'attaque associé</label>
              <select
                value={editingScenario.attackPathId || ''}
                onChange={(e) => setEditingScenario({...editingScenario, attackPathId: e.target.value, attackPathReference: ctiData?.attackPathsWithCTI?.find(path => path.id === e.target.value)?.referenceCode})}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="">Aucun chemin d'attaque associé</option>
                {ctiData?.attackPathsWithCTI?.map(path => (
                  <option key={path.id} value={path.id}>
                    {path.referenceCode} - {path.pathName}
                  </option>
                ))}
              </select>
            </div>

            {/* Phase Details */}
            <div className="border-t border-gray-200 pt-4">
              <h4 className="text-md font-semibold text-gray-900 mb-4">Détails des Phases EBIOS RM</h4>
              <div className="space-y-6">
                {(editingScenario.steps || []).map((step, stepIndex) => (
                  <div key={stepIndex} className="border border-gray-200 rounded-lg p-4">
                    <div className="flex items-center mb-3">
                      <div className={`w-8 h-8 rounded-full flex items-center justify-center text-white font-bold mr-3 ${
                        step.phase === 'CONNAITRE' ? 'bg-blue-500' :
                        step.phase === 'RENTRER' ? 'bg-red-500' :
                        step.phase === 'TROUVER' ? 'bg-amber-500' : 'bg-green-500'
                      }`}>
                        {stepIndex + 1}
                      </div>
                      <h5 className="font-semibold text-gray-900">{step.phase}</h5>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">Nom de l'étape</label>
                        <input
                          type="text"
                          value={step.name || ''}
                          onChange={(e) => {
                            const newSteps = [...(editingScenario.steps || [])];
                            newSteps[stepIndex] = {...newSteps[stepIndex], name: e.target.value};
                            setEditingScenario({...editingScenario, steps: newSteps});
                          }}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                          placeholder={`Nom de la phase ${step.phase}`}
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">Durée</label>
                        <input
                          type="text"
                          value={step.duration || ''}
                          onChange={(e) => {
                            const newSteps = [...(editingScenario.steps || [])];
                            newSteps[stepIndex] = {...newSteps[stepIndex], duration: e.target.value};
                            setEditingScenario({...editingScenario, steps: newSteps});
                          }}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                          placeholder="Ex: 1-2 jours"
                        />
                      </div>
                    </div>

                    <div className="mb-4">
                      <label className="block text-sm font-medium text-gray-700 mb-1">Description</label>
                      <textarea
                        value={step.description || ''}
                        onChange={(e) => {
                          const newSteps = [...(editingScenario.steps || [])];
                          newSteps[stepIndex] = {...newSteps[stepIndex], description: e.target.value};
                          setEditingScenario({...editingScenario, steps: newSteps});
                        }}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        rows="2"
                        placeholder={`Description de la phase ${step.phase}`}
                      />
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">Techniques (une par ligne)</label>
                        <textarea
                          value={(step.techniques || []).map(t => typeof t === 'string' ? t : t.name || t.technique || '').join('\n')}
                          onChange={(e) => {
                            const newSteps = [...(editingScenario.steps || [])];
                            newSteps[stepIndex] = {...newSteps[stepIndex], techniques: e.target.value.split('\n').filter(t => t.trim()).map(t => ({ name: t.trim() }))};
                            setEditingScenario({...editingScenario, steps: newSteps});
                          }}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                          rows="3"
                          placeholder="Ex: Reconnaissance passive&#10;Scan de ports&#10;Énumération des services"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">Indicateurs (un par ligne)</label>
                        <textarea
                          value={(step.indicators || []).map(i => typeof i === 'string' ? i : i.name || i.indicator || '').join('\n')}
                          onChange={(e) => {
                            const newSteps = [...(editingScenario.steps || [])];
                            newSteps[stepIndex] = {...newSteps[stepIndex], indicators: e.target.value.split('\n').filter(i => i.trim()).map(i => ({ name: i.trim() }))};
                            setEditingScenario({...editingScenario, steps: newSteps});
                          }}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                          rows="3"
                          placeholder="Ex: Trafic DNS anormal&#10;Connexions suspectes&#10;Logs d'authentification"
                        />
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            <div className="flex justify-end space-x-3">
              <button
                onClick={cancelEdit}
                className="px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50"
              >
                Annuler
              </button>
              <button
                onClick={saveEditedScenario}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
              >
                <Save size={16} className="inline mr-2" />
                Sauvegarder
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Generated Scenarios Display */}
      {generatedScenarios.length > 0 && (
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <Zap className="h-5 w-5 mr-2" />
            Scénarios Opérationnels Générés ({generatedScenarios.length})
          </h3>

          <div className="space-y-6">
            {generatedScenarios.filter(scenario => scenario != null).map((scenario, index) => (
              <div key={index} className="border border-gray-200 rounded-lg p-6">
                <div className="flex items-center justify-between mb-4">
                  <h4 className="text-lg font-semibold text-gray-900">
                    Scénario {index + 1}: {scenario?.name || scenario?.title || `Scénario ${index + 1}`}
                  </h4>
                  <div className="flex items-center space-x-2">
                    <div className="flex items-center space-x-2 text-sm text-gray-600">
                      {scenario?.attackPathReference && (
                        <span className="px-2 py-1 bg-indigo-100 text-indigo-800 rounded">
                          {scenario?.attackPathReference}
                        </span>
                      )}
                      {scenario?.severity && (
                        <span className="px-2 py-1 bg-red-100 text-red-800 rounded">
                          {scenario?.severity}
                        </span>
                      )}
                      {scenario?.requiredSkills && (
                        <span className="px-2 py-1 bg-orange-100 text-orange-800 rounded">
                          {scenario?.requiredSkills}
                        </span>
                      )}
                    </div>
                    <div className="flex items-center space-x-2">
                      <button
                        onClick={() => handleEditScenario(scenario)}
                        className="flex items-center px-3 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition duration-200 shadow-sm text-sm"
                        title="Modifier le scénario"
                      >
                        <Edit2 size={16} className="mr-2" />
                        Modifier
                      </button>
                      <button
                        onClick={() => {
                          console.log('[OperationalScenariosAI] Button clicked for scenario index:', index);
                          console.log('[OperationalScenariosAI] Current selectedScenario:', selectedScenario);
                          console.log('[OperationalScenariosAI] Scenario:', scenario);
                          const isCurrentlySelected = selectedScenario && selectedScenario === scenario;
                          setSelectedScenario(isCurrentlySelected ? null : scenario);
                        }}
                        className={`flex items-center px-3 py-2 rounded-lg transition duration-200 shadow-sm text-sm ${
                          selectedScenario === scenario
                            ? 'bg-red-600 text-white hover:bg-red-700'
                            : 'bg-blue-600 text-white hover:bg-blue-700'
                        }`}
                        title={selectedScenario === scenario ? "Masquer la visualisation" : "Visualiser le scénario"}
                      >
                        <Eye size={16} className="mr-2" />
                        {selectedScenario === scenario ? 'Masquer' : 'Visualiser'}
                      </button>
                    </div>
                  </div>
                </div>

                <p className="text-gray-700 mb-4">{scenario?.description || 'Aucune description disponible'}</p>

                {/* Scenario Metadata */}
                {(scenario?.likelihood || scenario?.detectionDifficulty || scenario?.timeline) && (
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4 p-4 bg-gray-50 rounded-lg">
                    {scenario?.likelihood && (
                      <div className="text-center">
                        <div className="text-xs text-gray-600">Probabilité</div>
                        <div className="font-semibold text-gray-900">{scenario?.likelihood}</div>
                      </div>
                    )}
                    {scenario?.detectionDifficulty && (
                      <div className="text-center">
                        <div className="text-xs text-gray-600">Difficulté de détection</div>
                        <div className="font-semibold text-gray-900">{scenario?.detectionDifficulty}</div>
                      </div>
                    )}
                    {scenario?.timeline && (
                      <div className="text-center">
                        <div className="text-xs text-gray-600">Durée estimée</div>
                        <div className="font-semibold text-gray-900">{scenario?.timeline}</div>
                      </div>
                    )}
                  </div>
                )}

                {/* EBIOS RM Steps */}
                {scenario?.steps && scenario.steps.length > 0 ? (
                  <div className="space-y-4">
                    <h5 className="font-semibold text-gray-900 mb-3">Étapes détaillées (EBIOS RM):</h5>
                    {scenario.steps.map((step, stepIndex) => (
                      <div key={stepIndex} className="border-l-4 border-blue-500 pl-4 bg-gray-50 p-4 rounded-r-lg">
                        <div className="flex items-center justify-between mb-2">
                          <h6 className="font-semibold text-gray-900 flex items-center">
                            <span className="w-6 h-6 bg-blue-500 text-white rounded-full flex items-center justify-center text-xs font-bold mr-2">
                              {stepIndex + 1}
                            </span>
                            {step.phase && (
                              <span className="px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs font-semibold mr-2">
                                {step.phase}
                              </span>
                            )}
                            {step.name}
                          </h6>
                          {step.duration && (
                            <span className="text-xs text-gray-600 bg-white px-2 py-1 rounded">
                              {step.duration}
                            </span>
                          )}
                        </div>

                        <p className="text-gray-700 mb-3">{step.description}</p>

                        {/* Techniques */}
                        {step.techniques && step.techniques.length > 0 && (
                          <div className="mb-3">
                            <div className="text-xs font-semibold text-gray-600 mb-1">Techniques:</div>
                            <div className="flex flex-wrap gap-1">
                              {step.techniques.map((technique, techIndex) => (
                                <span key={techIndex} className="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-purple-100 text-purple-800">
                                  {technique}
                                </span>
                              ))}
                            </div>
                          </div>
                        )}

                        {/* Indicators */}
                        {step.indicators && step.indicators.length > 0 && (
                          <div className="mb-3">
                            <div className="text-xs font-semibold text-gray-600 mb-1">Indicateurs de compromission:</div>
                            <div className="flex flex-wrap gap-1">
                              {step.indicators.map((indicator, indIndex) => (
                                <span key={indIndex} className="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-yellow-100 text-yellow-800">
                                  {indicator}
                                </span>
                              ))}
                            </div>
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                ) : (
                  // Fallback for old format or simple scenarios
                  <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                    <div className="flex items-center mb-2">
                      <AlertTriangle className="h-5 w-5 text-yellow-600 mr-2" />
                      <span className="font-semibold text-yellow-800">Scénario simplifié</span>
                    </div>
                    <p className="text-yellow-700 text-sm">
                      Ce scénario ne contient pas les étapes détaillées EBIOS RM.
                      Régénérez les scénarios pour obtenir des détails techniques complets.
                    </p>
                  </div>
                )}

                {/* Resources and Assets */}
                {(scenario?.resources || scenario?.assetsUsed) && (
                  <div className="mt-4 pt-4 border-t border-gray-200">
                    {scenario?.resources && (
                      <div className="mb-2">
                        <span className="text-sm font-semibold text-gray-700">Ressources nécessaires: </span>
                        <span className="text-sm text-gray-600">{scenario?.resources}</span>
                      </div>
                    )}
                    {scenario?.assetsUsed && (
                      <div>
                        <h6 className="font-medium text-gray-900 mb-2">Actifs impliqués:</h6>
                        <div className="flex flex-wrap gap-2">
                          {scenario.assetsUsed.map((asset, assetIndex) => (
                            <span key={assetIndex} className="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-green-100 text-green-800">
                              {asset}
                            </span>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                )}

                {/* Inline Visualization */}
                {selectedScenario === scenario && selectedScenario && (
                  <div className="mt-6 border-t border-gray-200 pt-6">
                    <div className="border border-gray-300 rounded-lg bg-white shadow-sm overflow-x-auto" style={{ minHeight: '500px', minWidth: '100%' }}>
                      <ReactFlowProvider>
                        <ScenarioVisualization
                          scenario={selectedScenario}
                          title={`Visualisation: ${selectedScenario?.name || selectedScenario?.title || 'Scénario Opérationnel'}`}
                          onClose={() => setSelectedScenario(null)}
                          isInline={true}
                        />
                      </ReactFlowProvider>
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      )}


    </div>
  );
};

export default OperationalScenariosAI;
