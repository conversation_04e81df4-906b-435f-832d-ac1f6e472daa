// src/components/Atelier3/Atelier3.js
import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { AlertCircle, Users, CheckSquare, Target, Route, Shield, TrendingUp } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import Activite1 from './Activite1/Activite1';
import Activite2 from './Activite2/Activite2';
import Activite3 from './Activite3/Activite3';
import Activite4 from './Activite4/Activite4';
import Activite5 from './Activite5/Activite5';
import { useAnalysis } from '../../context/AnalysisContext';

const Atelier3 = () => {
  const { t } = useTranslation();
  const [activeActivity, setActiveActivity] = useState('activite1');
  const { currentAnalysis } = useAnalysis();

  // Load data when component mounts
  useEffect(() => {
    const loadInitialData = async () => {
      if (currentAnalysis?.id) {
        try {
          console.log('Atelier3 - Component mounted, letting Activite1 handle data loading');
          // We'll let the Activite1 component handle the data loading directly
        } catch (error) {
          console.error('Error loading initial Atelier 3 data:', error);
        }
      }
    };

    loadInitialData();
  }, [currentAnalysis?.id]);

  // Helper function to get progress status for each activity
  const getProgressStatus = (activity) => {
    // This will be implemented later when we have progress tracking
    return 'pending';
  };

  // Helper function to get status icon based on progress
  const getStatusIcon = (status) => {
    switch (status) {
      case 'completed':
        return <span className="w-2 h-2 bg-green-500 rounded-full mr-2"></span>;
      case 'in-progress':
        return <span className="w-2 h-2 bg-yellow-500 rounded-full mr-2"></span>;
      case 'pending':
      default:
        return <span className="w-2 h-2 bg-gray-300 rounded-full mr-2"></span>;
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-100">
        <h1 className="text-2xl font-bold text-gray-800 mb-2">{t('workshop3.title')}</h1>
        <p className="text-gray-600 mb-4">
          {t('workshop3.subtitle')}
        </p>

        {/* Activity Tabs */}
        <div className="border-b border-gray-200">
          <div className="flex space-x-1">
            <button
              className={`flex items-center px-4 py-2 rounded-md ${
                activeActivity === 'activite1'
                  ? 'bg-blue-50 text-blue-700 font-medium'
                  : 'text-gray-600 hover:bg-gray-50'
              }`}
              onClick={() => setActiveActivity('activite1')}
            >
              {getStatusIcon(getProgressStatus('activite1'))}
              <div className="ml-2 flex items-center">
                <Users size={16} className="mr-1.5" />
                <span>{t('workshop3.activities.mapEcosystem')}</span>
              </div>
            </button>
            <button
              className={`flex items-center px-4 py-2 rounded-md ${
                activeActivity === 'activite2'
                  ? 'bg-blue-50 text-blue-700 font-medium'
                  : 'text-gray-600 hover:bg-gray-50'
              }`}
              onClick={() => setActiveActivity('activite2')}
            >
              {getStatusIcon(getProgressStatus('activite2'))}
              <div className="ml-2 flex items-center">
                <Target size={16} className="mr-1.5" />
                <span>{t('workshop3.activities.strategicScenarios')}</span>
              </div>
            </button>
            <button
              className={`flex items-center px-4 py-2 rounded-md ${
                activeActivity === 'activite3'
                  ? 'bg-blue-50 text-blue-700 font-medium'
                  : 'text-gray-600 hover:bg-gray-50'
              }`}
              onClick={() => setActiveActivity('activite3')}
            >
              {getStatusIcon(getProgressStatus('activite3'))}
              <div className="ml-2 flex items-center">
                <Route size={16} className="mr-1.5" />
                <span>{t('workshop3.activities.attackPaths')}</span>
              </div>
            </button>
            <button
              className={`flex items-center px-4 py-2 rounded-md ${
                activeActivity === 'activite4'
                  ? 'bg-blue-50 text-blue-700 font-medium'
                  : 'text-gray-600 hover:bg-gray-50'
              }`}
              onClick={() => setActiveActivity('activite4')}
            >
              {getStatusIcon(getProgressStatus('activite4'))}
              <div className="ml-2 flex items-center">
                <Shield size={16} className="mr-1.5" />
                <span>{t('workshop3.activities.securityMeasures')}</span>
              </div>
            </button>
            <button
              className={`flex items-center px-4 py-2 rounded-md ${
                activeActivity === 'activite5'
                  ? 'bg-blue-50 text-blue-700 font-medium'
                  : 'text-gray-600 hover:bg-gray-50'
              }`}
              onClick={() => setActiveActivity('activite5')}
            >
              {getStatusIcon(getProgressStatus('activite5'))}
              <div className="ml-2 flex items-center">
                <TrendingUp size={16} className="mr-1.5" />
                <span>{t('workshop3.activities.eventProgression')}</span>
              </div>
            </button>
          </div>
        </div>

        {/* Activity Content */}
        <div>
          {activeActivity === 'activite1' && <Activite1 />}
          {activeActivity === 'activite2' && <Activite2 />}
          {activeActivity === 'activite3' && <Activite3 />}
          {activeActivity === 'activite4' && <Activite4 />}
          {activeActivity === 'activite5' && <Activite5 />}
        </div>
      </div>
    </div>
  );
};

export default Atelier3;
