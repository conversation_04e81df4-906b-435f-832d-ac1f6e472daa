/* src/components/Atelier3/Activite3/FlowStyles.css */

/* Animation for flowing edges */
@keyframes flowAnimation {
  from {
    stroke-dashoffset: 100;
  }
  to {
    stroke-dashoffset: 0;
  }
}

/* Styles for regular edge dots */
.edge-dot-1, .edge-dot-2, .edge-dot-3 {
  opacity: 0.8;
  filter: drop-shadow(0 0 2px rgba(255, 255, 255, 0.8));
}

/* Styles for direct attack edge dots */
.edge-dot-direct-1, .edge-dot-direct-2, .edge-dot-direct-3, .edge-dot-direct-4 {
  opacity: 0.9;
  filter: drop-shadow(0 0 4px rgba(255, 69, 0, 0.9));
  animation: pulse 2s infinite;
}

/* Pulse animation for direct attack dots */
@keyframes pulse {
  0% {
    r: 3;
    opacity: 0.7;
  }
  50% {
    r: 5;
    opacity: 1;
  }
  100% {
    r: 3;
    opacity: 0.7;
  }
}

/* Hover styles for nodes */
.react-flow__node:hover {
  filter: drop-shadow(0 0 8px rgba(59, 130, 246, 0.5));
}

/* Hover styles for edges */
.react-flow__edge:hover .react-flow__edge-path {
  stroke-width: 3;
  filter: drop-shadow(0 0 3px rgba(59, 130, 246, 0.5));
}

/* Selected node styles */
.react-flow__node.selected {
  filter: drop-shadow(0 0 12px rgba(59, 130, 246, 0.8));
}

/* Selected edge styles */
.react-flow__edge.selected .react-flow__edge-path {
  stroke-width: 3;
  filter: drop-shadow(0 0 5px rgba(59, 130, 246, 0.8));
}

/* Custom handle styles */
.react-flow__handle {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background-color: #fff;
  border: 2px solid #3b82f6;
}

.react-flow__handle:hover {
  background-color: #3b82f6;
}

/* Edge label styles - ensure solid background and visibility */
.react-flow__edge-label {
  background-color: white !important;
  opacity: 1 !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2) !important;
  z-index: 10 !important;
  border: 1px solid #e2e8f0 !important;
  padding: 2px 6px !important;
  font-weight: 500 !important;
}
