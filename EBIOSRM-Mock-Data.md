# EBIOS RM Mock Data for Testing

This document provides mock data that can be used for testing the EBIOS RM application. You can use these examples to create test scenarios or verify functionality.

## User Accounts

### Admin User
```json
{
  "email": "<EMAIL>",
  "password": "Admin123!",
  "firstName": "Admin",
  "lastName": "User",
  "role": "admin",
  "companyName": "EBIOS RM",
  "companyId": "60d5ec9af682fbd12a8952a1"
}
```

### Regular User
```json
{
  "email": "<EMAIL>",
  "password": "User123!",
  "firstName": "Test",
  "lastName": "User",
  "role": "simpleuser",
  "companyName": "Test Company",
  "companyId": "60d5ec9af682fbd12a8952a2"
}
```

## Companies

### Example Company
```json
{
  "_id": "60d5ec9af682fbd12a8952a1",
  "name": "EBIOS RM",
  "address": "123 Security Street",
  "city": "Paris",
  "country": "France",
  "postalCode": "75001",
  "phone": "+***********",
  "website": "https://ebiosrm.fr",
  "industry": "Cybersecurity",
  "createdAt": "2023-01-01T00:00:00.000Z"
}
```

## Analysis

### Example Analysis
```json
{
  "_id": "61f2a3b4c5d6e7f8a9b0c1d2",
  "name": "Test Analysis",
  "description": "This is a test analysis for demonstration purposes",
  "status": "in-progress",
  "companyId": "60d5ec9af682fbd12a8952a1",
  "createdBy": "<EMAIL>",
  "createdAt": "2023-01-15T00:00:00.000Z",
  "updatedAt": "2023-01-20T00:00:00.000Z"
}
```

## Business Values

### Example Business Values
```json
[
  {
    "id": "bv1",
    "name": "Système d'information critique",
    "description": "Système d'information essentiel aux opérations",
    "analysisId": "61f2a3b4c5d6e7f8a9b0c1d2"
  },
  {
    "id": "bv2",
    "name": "Base de données clients",
    "description": "Stockage des informations clients sensibles",
    "analysisId": "61f2a3b4c5d6e7f8a9b0c1d2"
  },
  {
    "id": "bv3",
    "name": "Infrastructure réseau",
    "description": "Infrastructure réseau de l'entreprise",
    "analysisId": "61f2a3b4c5d6e7f8a9b0c1d2"
  }
]
```

## Sources de Risque (Risk Sources)

### Example Risk Sources
```json
[
  {
    "id": "sr1",
    "name": "Cybercriminel",
    "description": "Individu ou groupe cherchant à exploiter des vulnérabilités pour un gain financier",
    "motivation": "elevee",
    "resources": "moyen",
    "activity": "elevee",
    "capacite": "elevee",
    "retenu": true,
    "analysisId": "61f2a3b4c5d6e7f8a9b0c1d2"
  },
  {
    "id": "sr2",
    "name": "Employé malveillant",
    "description": "Employé interne avec des intentions malveillantes",
    "motivation": "moyen",
    "resources": "faible",
    "activity": "moyen",
    "capacite": "elevee",
    "retenu": true,
    "analysisId": "61f2a3b4c5d6e7f8a9b0c1d2"
  },
  {
    "id": "sr3",
    "name": "Acteur étatique",
    "description": "Entité gouvernementale avec des capacités avancées",
    "motivation": "moyen",
    "resources": "elevee",
    "activity": "moyen",
    "capacite": "elevee",
    "retenu": false,
    "analysisId": "61f2a3b4c5d6e7f8a9b0c1d2"
  }
]
```

## Objectifs Visés (Targeted Objectives)

### Example Targeted Objectives
```json
[
  {
    "id": "ov1",
    "name": "Vol de données",
    "description": "Extraction non autorisée de données sensibles",
    "businessValueId": "bv2",
    "retenu": true,
    "analysisId": "61f2a3b4c5d6e7f8a9b0c1d2"
  },
  {
    "id": "ov2",
    "name": "Interruption de service",
    "description": "Rendre indisponible un service critique",
    "businessValueId": "bv1",
    "retenu": true,
    "analysisId": "61f2a3b4c5d6e7f8a9b0c1d2"
  },
  {
    "id": "ov3",
    "name": "Altération de données",
    "description": "Modification non autorisée de données",
    "businessValueId": "bv2",
    "retenu": false,
    "analysisId": "61f2a3b4c5d6e7f8a9b0c1d2"
  }
]
```

## Couples SR/OV (Risk Source/Targeted Objective Pairs)

### Example SR/OV Pairs
```json
[
  {
    "id": "srov1",
    "sourceRisqueId": "sr1",
    "objectifViseId": "ov1",
    "vraisemblance": "elevee",
    "retenu": true,
    "analysisId": "61f2a3b4c5d6e7f8a9b0c1d2"
  },
  {
    "id": "srov2",
    "sourceRisqueId": "sr2",
    "objectifViseId": "ov1",
    "vraisemblance": "moyen",
    "retenu": true,
    "analysisId": "61f2a3b4c5d6e7f8a9b0c1d2"
  },
  {
    "id": "srov3",
    "sourceRisqueId": "sr1",
    "objectifViseId": "ov2",
    "vraisemblance": "faible",
    "retenu": false,
    "analysisId": "61f2a3b4c5d6e7f8a9b0c1d2"
  }
]
```

## Dreaded Events

### Example Dreaded Events
```json
[
  {
    "id": "de1",
    "name": "Fuite de données clients",
    "description": "Exfiltration de données clients sensibles",
    "businessValueId": "bv2",
    "pilier": "confidentialite",
    "gravite": "elevee",
    "impacts": {
      "missions": "elevee",
      "humain": "moyen",
      "financier": "elevee",
      "juridique": "elevee",
      "image": "elevee",
      "gouvernance": "moyen"
    },
    "retenu": true,
    "analysisId": "61f2a3b4c5d6e7f8a9b0c1d2"
  },
  {
    "id": "de2",
    "name": "Indisponibilité du système critique",
    "description": "Interruption prolongée du système d'information critique",
    "businessValueId": "bv1",
    "pilier": "disponibilite",
    "gravite": "elevee",
    "impacts": {
      "missions": "elevee",
      "humain": "faible",
      "financier": "elevee",
      "juridique": "moyen",
      "image": "moyen",
      "gouvernance": "faible"
    },
    "retenu": true,
    "analysisId": "61f2a3b4c5d6e7f8a9b0c1d2"
  }
]
```

## Security Controls

### Example Security Controls
```json
[
  {
    "id": "sc1",
    "name": "Chiffrement des données sensibles",
    "description": "Mise en place d'un chiffrement fort pour toutes les données sensibles",
    "pilier": "confidentialite",
    "type": "preventif",
    "status": "implemented",
    "dreadedEventId": "de1",
    "analysisId": "61f2a3b4c5d6e7f8a9b0c1d2"
  },
  {
    "id": "sc2",
    "name": "Authentification multi-facteurs",
    "description": "Mise en place d'une authentification à deux facteurs pour tous les accès",
    "pilier": "confidentialite",
    "type": "preventif",
    "status": "planned",
    "dreadedEventId": "de1",
    "analysisId": "61f2a3b4c5d6e7f8a9b0c1d2"
  },
  {
    "id": "sc3",
    "name": "Système de haute disponibilité",
    "description": "Mise en place d'une architecture redondante pour les systèmes critiques",
    "pilier": "disponibilite",
    "type": "preventif",
    "status": "implemented",
    "dreadedEventId": "de2",
    "analysisId": "61f2a3b4c5d6e7f8a9b0c1d2"
  }
]
```

## Testing Scenarios

### Scenario 1: New Analysis Creation
1. Log <NAME_EMAIL>
2. Create a new analysis with name "Test Analysis"
3. Add business values:
   - "Système de gestion des commandes"
   - "Base de données clients"
4. Verify the analysis is created successfully

### Scenario 2: Risk Source Evaluation
1. Log <NAME_EMAIL>
2. Open "Test Analysis"
3. Add risk sources:
   - "Cybercriminel" with high motivation
   - "Employé malveillant" with medium motivation
4. Evaluate and mark "Cybercriminel" as retained
5. Verify the risk sources are saved correctly

### Scenario 3: Dreaded Event Creation
1. Log <NAME_EMAIL>
2. Open "Test Analysis"
3. Create a dreaded event:
   - Name: "Fuite de données clients"
   - Business Value: "Base de données clients"
   - Pillar: "Confidentialité"
   - Severity: "Élevée"
4. Verify the dreaded event is created successfully

### Scenario 4: Security Control Implementation
1. Log <NAME_EMAIL>
2. Open "Test Analysis"
3. Add security controls for "Fuite de données clients":
   - "Chiffrement des données sensibles"
   - "Authentification multi-facteurs"
4. Mark "Chiffrement des données sensibles" as implemented
5. Verify the security controls are saved correctly

## API Testing

### Authentication Endpoints
```
POST /api/auth/login
Body: { "email": "<EMAIL>", "password": "Admin123!" }
Expected: 200 OK with token

POST /api/auth/refresh
Body: { "refreshToken": "<valid-refresh-token>" }
Expected: 200 OK with new access token
```

### Analysis Endpoints
```
GET /api/analyses
Headers: { "Authorization": "Bearer <token>" }
Expected: 200 OK with list of analyses

POST /api/analyses
Headers: { "Authorization": "Bearer <token>" }
Body: { "name": "New Test Analysis", "description": "API test" }
Expected: 201 Created with analysis data
```

### Business Values Endpoints
```
GET /api/business-values/:analysisId
Headers: { "Authorization": "Bearer <token>" }
Expected: 200 OK with list of business values

POST /api/business-values
Headers: { "Authorization": "Bearer <token>" }
Body: { "name": "New Business Value", "description": "API test", "analysisId": "<valid-analysis-id>" }
Expected: 201 Created with business value data
```
