// src/components/dashboard/Dashboard.js
import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useTranslation } from 'react-i18next';
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>hart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer, Cell, RadarChart, PolarGrid, PolarAngleAxis, PolarRadiusAxis, Radar, ScatterChart, Scatter, ZAxis, <PERSON>hart as RechartsLineChart, Line, Area, AreaChart, ComposedChart } from 'recharts';
import {
    <PERSON><PERSON>hart as BarChartLucide,
    <PERSON><PERSON><PERSON> as <PERSON><PERSON>hartLucide,
    Users,
    Target,
    Calendar,
    Shield,
    AlertTriangle,
    ChevronDown,
    ChevronRight,
    ChevronUp,
    Download,
    RefreshCw,
    Info,
    FileText,
    XCircle,
    Settings,
    ExternalLink,
    TrendingUp,
    Filter,
    Clock,
    CheckCircle,
    HelpCircle,
    Eye,
    LineChart,
    Zap,
    Award
} from 'lucide-react';
import AnalysisSelector from './AnalysisSelector';
import { useAnalysis } from '../context/AnalysisContext';
import { authService } from '../services/authService';
import { SECURITY_PILLARS, DREADED_EVENT_IMPACTS } from '../constants';
import { getTranslatedDreadedEventImpacts } from '../utils/translatedConstants';

// --- Enhanced Helper Components ---

// Modern Card with hover effects and optional gradient
const InfoCard = ({ title, message, icon = FileText, value = null, trend = null, color = 'blue' }) => {
    const colorMap = {
        blue: 'from-blue-500 to-indigo-600 shadow-blue-200/50',
        green: 'from-emerald-500 to-green-600 shadow-emerald-200/50',
        purple: 'from-violet-500 to-purple-600 shadow-violet-200/50',
        amber: 'from-amber-500 to-orange-600 shadow-amber-200/50',
        red: 'from-red-500 to-rose-600 shadow-red-200/50',
    };

    const bgColorMap = {
        blue: 'bg-gradient-to-br from-blue-50 to-indigo-50 border-blue-100',
        green: 'bg-gradient-to-br from-emerald-50 to-green-50 border-emerald-100',
        purple: 'bg-gradient-to-br from-violet-50 to-purple-50 border-violet-100',
        amber: 'bg-gradient-to-br from-amber-50 to-orange-50 border-amber-100',
        red: 'bg-gradient-to-br from-red-50 to-rose-50 border-red-100',
    };

    const iconColorMap = {
        blue: 'text-blue-600',
        green: 'text-emerald-600',
        purple: 'text-violet-600',
        amber: 'text-amber-600',
        red: 'text-red-600',
    };

    const textColorMap = {
        blue: 'text-white',
        green: 'text-white',
        purple: 'text-white',
        amber: 'text-white',
        red: 'text-white',
    };

    return (
        <motion.div
            whileHover={{ y: -5, scale: 1.02, boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)' }}
            transition={{ duration: 0.2 }}
            className={`${bgColorMap[color]} p-5 rounded-xl border shadow-lg relative overflow-hidden flex flex-col justify-between min-h-[150px]`}
        >
            {/* Decorative elements */}
            <div className="absolute -right-6 -top-6 w-24 h-24 rounded-full bg-white/10"></div>
            <div className="absolute -right-4 -bottom-4 w-16 h-16 rounded-full bg-white/10"></div>

            <div className="relative z-10">
                <div className="flex items-center mb-2">
                    <div className={`p-2 rounded-lg bg-white/20 mr-3`}>
                        {React.createElement(icon, { size: 18, className: `${textColorMap[color]}` })}
                    </div>
                    <h3 className={`text-sm font-medium ${textColorMap[color]}`}>{title}</h3>
                </div>
            </div>

            {value !== null ? (
                <div className="mt-3 relative z-10">
                    <span className={`text-4xl font-bold ${textColorMap[color]}`}>{value}</span>

                </div>
            ) : (
                <div className="flex items-center justify-center text-center text-xs text-white/80 mt-1 flex-grow bg-white/10 p-3 rounded-lg backdrop-blur-sm relative z-10">
                    {React.createElement(icon, { size: 16, className: "mr-1 flex-shrink-0" })}
                    <span>{message}</span>
                </div>
            )}

            {/* Gradient overlay */}
            <div className={`absolute inset-0 bg-gradient-to-br ${colorMap[color]} opacity-90 z-0`}></div>
        </motion.div>
    );
};

// Enhanced Section with collapsible functionality
const InfoSection = ({ title, message, icon = FileText, children = null, collapsible = false }) => {
    const { t } = useTranslation();
    const [isCollapsed, setIsCollapsed] = useState(false);

    return (
        <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-white rounded-xl border border-gray-200 shadow-md overflow-hidden"
        >
            <div className="p-4 border-b border-gray-200 bg-gray-50 flex justify-between items-center">
                <h3 className="font-medium text-gray-700 flex items-center">
                    {React.createElement(icon, { size: 18, className: "mr-2 text-gray-500" })}
                    {title}
                </h3>
                {collapsible && (
                    <button
                        onClick={() => setIsCollapsed(!isCollapsed)}
                        className="p-1 rounded-full hover:bg-gray-200 transition-colors"
                    >
                        {isCollapsed ? <ChevronDown size={18} /> : <ChevronUp size={18} />}
                    </button>
                )}
            </div>

            <AnimatePresence>
                {(!collapsible || !isCollapsed) && (
                    <motion.div
                        initial={{ height: 0, opacity: 0 }}
                        animate={{ height: "auto", opacity: 1 }}
                        exit={{ height: 0, opacity: 0 }}
                        transition={{ duration: 0.2 }}
                    >
                        {children || (
                            <div className="p-8 text-center">
                                {React.createElement(icon, { size: 48, className: `mx-auto mb-3 text-gray-300` })}
                                <p className={`text-gray-500`}>
                                    {message || t('dashboard.cards.toDefine')}
                                </p>
                            </div>
                        )}
                    </motion.div>
                )}
            </AnimatePresence>
        </motion.div>
    );
};

// Enhanced Empty Chart
const EmptyChartPlaceholder = ({ title, icon = BarChartLucide }) => {
    const { t } = useTranslation();
    return (
    <motion.div
        whileHover={{ y: -3, boxShadow: '0 15px 30px -5px rgba(0, 0, 0, 0.1), 0 10px 15px -6px rgba(0, 0, 0, 0.05)' }}
        transition={{ duration: 0.2 }}
        className="bg-white p-5 rounded-xl border border-gray-100 shadow-lg overflow-hidden relative"
        style={{
            backgroundImage: 'radial-gradient(circle at 100% 100%, rgba(243, 244, 246, 0.4) 0%, transparent 40%)',
            boxShadow: '0 10px 25px -5px rgba(0, 0, 0, 0.05), 0 8px 10px -6px rgba(0, 0, 0, 0.01)',
        }}
    >
        {/* Decorative elements */}
        <div className="absolute top-0 right-0 w-24 h-24 bg-gradient-to-br from-blue-50 to-indigo-50 rounded-full -mr-12 -mt-12 opacity-50"></div>
        <div className="absolute bottom-0 left-0 w-16 h-16 bg-gradient-to-tr from-green-50 to-emerald-50 rounded-full -ml-8 -mb-8 opacity-50"></div>

        <div className="relative">
            <div className="flex items-center mb-4">
                <div className="p-2 rounded-lg bg-gradient-to-br from-blue-50 to-indigo-50 mr-3">
                    {React.createElement(icon, { size: 20, className: "text-indigo-600" })}
                </div>
                <h3 className="font-semibold text-gray-800">{title}</h3>
            </div>
            <div className="h-[300px] flex flex-col items-center justify-center text-center bg-gradient-to-b from-gray-50 to-white rounded-lg border border-gray-100">
                {React.createElement(icon, { size: 48, className: `text-gray-300 mb-3 opacity-50` })}
                <p className="text-sm text-gray-400">{t('dashboard.empty.noSecurityMeasures')}</p>
                <button className="mt-4 px-4 py-2 bg-indigo-50 text-indigo-600 rounded-lg text-sm hover:bg-indigo-100 transition-colors">
                    {t('common.add')}
                </button>
            </div>
        </div>
    </motion.div>
    );
};

// Enhanced chart container with a modern look
const ChartContainer = ({ title, icon, children }) => (
    <motion.div
        whileHover={{ y: -3, boxShadow: '0 15px 30px -5px rgba(0, 0, 0, 0.1), 0 10px 15px -6px rgba(0, 0, 0, 0.05)' }}
        transition={{ duration: 0.2 }}
        className="bg-white p-5 rounded-xl border border-gray-100 shadow-lg overflow-hidden relative"
        style={{
            backgroundImage: 'radial-gradient(circle at 100% 100%, rgba(243, 244, 246, 0.4) 0%, transparent 40%)',
            boxShadow: '0 10px 25px -5px rgba(0, 0, 0, 0.05), 0 8px 10px -6px rgba(0, 0, 0, 0.01)',
        }}
    >
        {/* Decorative elements */}
        <div className="absolute top-0 right-0 w-24 h-24 bg-gradient-to-br from-blue-50 to-indigo-50 rounded-full -mr-12 -mt-12 opacity-50"></div>
        <div className="absolute bottom-0 left-0 w-16 h-16 bg-gradient-to-tr from-green-50 to-emerald-50 rounded-full -ml-8 -mb-8 opacity-50"></div>

        <div className="relative">
            <div className="flex items-center justify-between mb-4">
                <div className="flex items-center">
                    <div className="p-2 rounded-lg bg-gradient-to-br from-blue-50 to-indigo-50 mr-3">
                        {React.createElement(icon, { size: 20, className: "text-indigo-600" })}
                    </div>
                    <h3 className="font-semibold text-gray-800">{title}</h3>
                </div>

            </div>
            {children}
        </div>
    </motion.div>
);

// Custom hook to handle tab switching
const useTabSwitcher = () => {
    // This is a workaround since we don't have direct access to setActiveTab
    // We'll use localStorage to communicate with the parent component
    const switchToTab = (tabName) => {
        // Store the tab name in localStorage
        localStorage.setItem('ebiosrm_switch_to_tab', tabName);
        // Dispatch a custom event to notify the parent component
        window.dispatchEvent(new CustomEvent('ebiosrm_switch_tab'));
    };

    return { switchToTab };
};

// Color palettes for charts
const STATUS_COLORS = {
    implemented: '#10B981', // green
    partial: '#F59E0B',    // amber
    planned: '#3B82F6',    // blue
    apply_before: '#8B5CF6', // purple
    abandoned: '#EF4444',  // red
    undefined: '#94A3B8'   // gray
};

const RISK_LEVEL_COLORS = {
    high: '#DC2626',       // red
    medium: '#F59E0B',     // amber
    low: '#10B981',        // green
    undefined: '#94A3B8'   // gray
};

const COVERAGE_COLORS = {
    high: '#10B981',       // green
    medium: '#F59E0B',     // amber
    low: '#DC2626',        // red
    undefined: '#94A3B8'   // gray
};

const SEVERITY_COLORS = {
    catastrophic: '#1E293B', // Slate 800
    critical: '#EF4444',     // Red 600
    major: '#F97316',        // Orange 600
    moderate: '#FBBF24',     // Yellow 500
    minor: '#10B981',        // Emerald 500
    undefined: '#94A3B8'     // Gray 400
};

const ROLE_COLORS = ['#6366F1', '#10B981', '#8B5CF6', '#F43F5E', '#F59E0B', '#3B82F6'];
const PILLAR_COLORS = ['#6366F1', '#10B981', '#F43F5E', '#F97316', '#8B5CF6', '#EC4899'];

// --- Main Dashboard Component ---
const Dashboard = ({ preferences, setPreferences }) => {
    // Translation hook
    const { t, i18n } = useTranslation();

    // Use our custom hook
    const { switchToTab } = useTabSwitcher();
    // --- Get Data SOLELY from Context ---
    const {
        currentAnalysis,
        currentContextData,
        currentBusinessValues,
        currentDreadedEvents,
        currentSecurityFramework,
        currentAnalysisControlPlan,
        currentRiskTreatments,
        isWorkshopDataLoading,
        workshopDataError
    } = useAnalysis();

    // --- Local UI State ---
    const [activeSection, setActiveSection] = useState(preferences?.activeSection || 'overview');
    const [securityFilter, setSecurityFilter] = useState('all');
    const [securityView, setSecurityView] = useState('grid');

    // Update activeSection in preferences
    useEffect(() => {
        if (setPreferences && activeSection !== preferences?.activeSection) {
            setPreferences({ ...preferences, activeSection });
        }
    }, [activeSection, preferences, setPreferences]);

    // --- Use Context Data Directly ---
    // Assign defaults, assume data exists unless explicitly null/undefined
    const context = currentContextData || {};
    const bValues = currentBusinessValues || [];
    // Removed sensitive data logging for security
    const events = currentDreadedEvents || [];
    const frameworkData = currentSecurityFramework || null; // Contains rules, frameworks etc.
    // Extract controls from the control plan
    const controlsData = [];

    // Check if we have a control plan with data
    // Removed sensitive data logging for security
    if (currentAnalysisControlPlan?.planData?.plan) {
        // Extract controls from the plan
        Object.entries(currentAnalysisControlPlan.planData.plan).forEach(([key, ruleEventData]) => {
            if (ruleEventData?.controls && Array.isArray(ruleEventData.controls)) {
                ruleEventData.controls.forEach(control => {
                    // Add each control to the controlsData array
                    controlsData.push({
                        id: control.id,
                        name: control.name,
                        description: control.description,
                        type: control.type || 'Standard',
                        status: control.decision || 'planned',
                        responsiblePerson: control.responsiblePerson
                    });
                });
            }
        });
        // Removed sensitive data logging for security
    }
    const treatmentsData = currentRiskTreatments || []; // Not used yet, but available

    // --- Prepare data for charts (only if source data exists) ---
    const participantRolesData = context?.participants?.length > 0 ? (context.participants).reduce((acc, p) => {
        let role = p.position === "OTHER" && p.customPosition ? p.customPosition : (p.position || 'Undefined');
        const existing = acc.find(item => item.name === role);
        if (existing) existing.value += 1; else acc.push({ name: role, value: 1 });
        return acc;
    }, []) : [];

    // Create data for the radar chart with each security pillar as a separate point
    const securityPillarEventsData = SECURITY_PILLARS.map(pillar => {
        // Initialize with the pillar name and 0 events
        const pillarData = {
            subject: pillar.name,  // Use the French name for display
            value: 0,             // Initialize count to 0
            fullMark: 10          // Just for scale reference
        };

        // Count events for this pillar
        if (events?.length > 0) {
            events.forEach(event => {
                // Normalize the security pillar to handle case inconsistencies
                let eventPillar = event.securityPillar || '';
                if (eventPillar.toLowerCase() === 'auditabilite') {
                    eventPillar = 'Auditabilite';
                }

                // If this event belongs to the current pillar, increment the count
                if (eventPillar.toLowerCase() === pillar.id.toLowerCase()) {
                    pillarData.value += 1;
                }
            });
        }

        return pillarData;
    });

    const businessValuePillarsData = bValues?.length > 0 ? (bValues).reduce((acc, bv) => {
        if (Array.isArray(bv.securityPillars)) {
            bv.securityPillars.forEach(pillar => {
                const pillarName = pillar || 'Undefined';
                const existing = acc.find(item => item.name === pillarName);
                if (existing) existing.value += 1; else acc.push({ name: pillarName, value: 1 });
            });
        }
        return acc;
    }, []) : [];

    // New data processing for enhanced charts

    // 1. Security Control Status Distribution
    const securityControlStatusData = controlsData.length > 0 ?
        Object.keys(STATUS_COLORS).reduce((acc, status) => {
            const count = controlsData.filter(control =>
                control.status === status ||
                (status === 'undefined' && !control.status)
            ).length;

            if (count > 0) {
                acc.push({
                    name: status === 'implemented' ? t('dashboard.statusLabels.implemented') :
                          status === 'partial' ? t('dashboard.statusLabels.partial') :
                          status === 'planned' ? t('dashboard.statusLabels.planned') :
                          status === 'apply_before' ? t('dashboard.statusLabels.toApply') :
                          status === 'abandoned' ? t('dashboard.statusLabels.toAbandon') : 'Non défini',
                    value: count,
                    color: STATUS_COLORS[status]
                });
            }
            return acc;
        }, []) : [];

    // 2. Security Controls by Type (real data)
    const securityControlsByTypeData = controlsData.length > 0 ?
        controlsData.reduce((acc, control) => {
            const type = control.type || 'Non défini';
            const existing = acc.find(item => item.name === type);
            if (existing) {
                existing.value += 1;
            } else {
                acc.push({ name: type, value: 1 });
            }
            return acc;
        }, []) : [];

    // 3. Events by Severity (real data)
    const eventsBySeverityData = events.length > 0 ?
        events.reduce((acc, event) => {
            const severity = event.severity || 'Non défini';
            const severityLabel =
                severity === 'catastrophic' ? 'Catastrophique' :
                severity === 'critical' ? 'Critique' :
                severity === 'major' ? 'Majeur' :
                severity === 'moderate' ? 'Modéré' :
                severity === 'minor' ? 'Mineur' : 'Non défini';

            const existing = acc.find(item => item.name === severityLabel);
            if (existing) {
                existing.value += 1;
            } else {
                const color = SEVERITY_COLORS[severity] || '#94A3B8';
                acc.push({ name: severityLabel, value: 1, color });
            }
            return acc;
        }, []) : [];

    // 4. Events by Impact Type (new chart)
    const eventsByImpactData = events.length > 0 ?
        events.reduce((acc, event) => {
            // Handle both old impact field and new impacts array
            const impactArray = event.impacts || (event.impact ? [event.impact] : []);

            // Count each impact type
            impactArray.forEach(impactValue => {
                const translatedImpacts = getTranslatedDreadedEventImpacts();
                const impact = translatedImpacts.find(imp => imp.value === impactValue);
                const impactLabel = impact ? impact.label : (impactValue || 'Non spécifié');

                const existing = acc.find(item => item.name === impactLabel);
                if (existing) {
                    existing.value += 1;
                } else {
                    // Generate a color based on the impact type
                    const impactColors = {
                        'missions_services': '#3B82F6', // blue
                        'humain_materiel_environnemental': '#EF4444', // red
                        'gouvernance': '#8B5CF6', // purple
                        'financier': '#10B981', // green
                        'juridique': '#F59E0B', // amber
                        'image_confiance': '#EC4899', // pink
                    };
                    const color = impactColors[impactValue] || '#94A3B8'; // gray fallback
                    acc.push({ name: impactLabel, value: 1, color });
                }
            });

            return acc;
        }, []) : [];

    // 4. Events by Business Value (real data)
    const eventsByBusinessValueData = [];
    if (events.length > 0 && bValues.length > 0) {
        // Create a map of business value IDs to names for quick lookup
        const bValueMap = bValues.reduce((map, bv) => {
            map[bv.id] = bv.name;
            return map;
        }, {});

        // Group events by business value
        events.forEach(event => {
            if (event.businessValue) {
                const bvName = bValueMap[event.businessValue] || `ID: ${event.businessValue}`;
                const existing = eventsByBusinessValueData.find(item => item.name === bvName);
                if (existing) {
                    existing.value += 1;
                } else {
                    eventsByBusinessValueData.push({ name: bvName, value: 1 });
                }
            }
        });
    }

    // Filter security controls based on selected filter
    const filteredControls = controlsData.filter(control => {
        if (securityFilter === 'all') return true;
        return control.status === securityFilter;
    });

    // Using color palettes defined at the top of the file

    // Tab variants for animation
    const tabVariants = {
        active: {
            backgroundColor: '#EFF6FF',
            color: '#1D4ED8',
            borderColor: '#93C5FD',
            boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)'
        },
        inactive: {
            backgroundColor: '#F3F4F6',
            color: '#4B5563',
            borderColor: '#E5E7EB',
            boxShadow: 'none'
        }
    };

    // Animation for container transitions
    const containerVariants = {
        hidden: { opacity: 0, y: 10 },
        visible: {
            opacity: 1,
            y: 0,
            transition: {
                duration: 0.4,
                staggerChildren: 0.1
            }
        }
    };

    const itemVariants = {
        hidden: { opacity: 0, y: 20 },
        visible: { opacity: 1, y: 0 }
    };

    // Button group variants
    const buttonGroupVariants = {
        active: {
            backgroundColor: '#4F46E5',
            color: 'white',
            borderColor: '#4338CA',
        },
        inactive: {
            backgroundColor: '#F9FAFB',
            color: '#6B7280',
            borderColor: '#E5E7EB',
        }
    };

    // --- Main Render ---
    return (
        <motion.div
            initial={{ opacity: 0, y: 15 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="modern-card modern-fade-in p-8"
        >
            {/* Modern Enhanced Header */}
            <div className="modern-header mb-8">
                <div className="modern-header-content">
                    <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center gap-6">
                        <div className="flex items-center space-x-4">
                            <div className="w-16 h-16 bg-white/20 rounded-2xl flex items-center justify-center shadow-lg backdrop-blur-sm">
                                <BarChartLucide size={32} className="text-white" />
                            </div>
                            <div>
                                <h2 className="text-3xl font-bold text-white flex items-center">
                                    {t('dashboard.title')}
                                    <span className="ml-3 px-3 py-1 bg-white/20 text-white text-sm font-medium rounded-full backdrop-blur-sm">
                                        {t('dashboard.version')}
                                    </span>
                                </h2>
                                <p className="text-blue-100 text-lg mt-2">{t('dashboard.subtitle')}</p>
                            </div>
                        </div>
                        <div className="flex flex-wrap gap-3">
                            <div className="modern-glass-effect rounded-xl px-4 py-2 text-sm text-white flex items-center">
                                <Calendar size={16} className="mr-2 text-blue-200" />
                                {new Date().toLocaleDateString(i18n.language === 'en' ? 'en-US' : 'fr-FR', {weekday: 'long', year: 'numeric', month: 'long', day: 'numeric'})}
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            {/* Modern Analysis Selector */}
            <div className="mb-8 modern-card p-6">
                <div className="flex items-center mb-4">
                    <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-lg flex items-center justify-center mr-3">
                        <Target size={16} className="text-white" />
                    </div>
                    <h3 className="text-lg font-semibold text-gray-900">{t('dashboard.analysisSelector.title')}</h3>
                    <div className="ml-3 modern-badge modern-badge-primary">
                        <Info size={12} />
                        {t('dashboard.analysisSelector.info')}
                    </div>
                </div>
                <AnalysisSelector />
            </div>

            {/* Modern Loading State */}
            {isWorkshopDataLoading && (
                <motion.div
                    initial={{ opacity: 0, y: 5 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="modern-card p-8 my-8 flex items-center justify-center bg-gradient-to-r from-blue-50 to-indigo-50"
                >
                    <RefreshCw size={24} className="animate-spin text-blue-600 mr-4" />
                    <div>
                        <p className="text-blue-800 font-semibold text-lg">{t('dashboard.status.loading')}</p>
                        <p className="text-blue-600 text-sm mt-1">Chargement des données d'analyse...</p>
                    </div>
                </motion.div>
            )}

            {/* Modern Error state */}
            {workshopDataError && !isWorkshopDataLoading && (
                <motion.div
                    initial={{ opacity: 0, y: 5 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="modern-card p-6 my-8 bg-gradient-to-r from-red-50 to-rose-50 border-red-200"
                >
                    <div className="flex items-start">
                        <div className="w-12 h-12 bg-red-100 rounded-xl flex items-center justify-center mr-4 flex-shrink-0">
                            <XCircle size={24} className="text-red-600" />
                        </div>
                        <div className="flex-1">
                            <h3 className="text-red-800 font-semibold text-lg">{t('dashboard.status.error')}</h3>
                            <p className="text-red-700 text-sm mt-1">{t('dashboard.status.errorDetails')}</p>
                        </div>
                        <button className="modern-btn modern-btn-error text-sm">
                            {t('dashboard.status.retry')}
                        </button>
                    </div>
                </motion.div>
            )}

            {/* Content Area: Render if NOT loading AND an analysis IS selected */}
            {!isWorkshopDataLoading && currentAnalysis && (
                <AnimatePresence mode="wait">
                    <motion.div
                        key={activeSection}
                        initial="hidden"
                        animate="visible"
                        variants={containerVariants}
                    >
                        {/* Modern Navigation Tabs */}
                        <div className="modern-nav mb-8">
                            <div className="flex flex-wrap">
                                {[
                                    { id: 'overview', label: t('dashboard.tabs.overview'), icon: BarChartLucide },
                                    { id: 'participants', label: t('dashboard.tabs.participants'), icon: Users },
                                    { id: 'events', label: t('dashboard.tabs.events'), icon: AlertTriangle },
                                    { id: 'security', label: t('dashboard.tabs.security'), icon: Shield },
                                    { id: 'analysis', label: t('dashboard.tabs.analysis'), icon: LineChart },
                                ].map(section => (
                                    <motion.button
                                        key={section.id}
                                        variants={tabVariants}
                                        animate={activeSection === section.id ? 'active' : 'inactive'}
                                        whileHover={{ scale: 1.02 }}
                                        whileTap={{ scale: 0.98 }}
                                        transition={{ duration: 0.2 }}
                                        className={`modern-nav-item ${activeSection === section.id ? 'active' : ''}`}
                                        onClick={() => setActiveSection(section.id)}
                                    >
                                        <div className={`w-8 h-8 rounded-lg flex items-center justify-center mr-3 transition-all ${
                                            activeSection === section.id
                                                ? 'bg-white/20'
                                                : 'bg-gray-100'
                                        }`}>
                                            {React.createElement(section.icon, {
                                                size: 16,
                                                className: activeSection === section.id ? 'text-white' : 'text-gray-600'
                                            })}
                                        </div>
                                        <div>
                                            <div className="font-medium">{section.label}</div>
                                            <div className="text-xs opacity-75">
                                                {section.id === 'overview' && 'Vue d\'ensemble'}
                                                {section.id === 'participants' && 'Parties prenantes'}
                                                {section.id === 'events' && 'Événements redoutés'}
                                                {section.id === 'security' && 'Mesures de sécurité'}
                                                {section.id === 'analysis' && 'Analyse avancée'}
                                            </div>
                                        </div>
                                    </motion.button>
                                ))}
                            </div>
                        </div>

                        {/* Main Content based on activeSection */}
                        <div className="space-y-8">
                            {/* Overview Section */}
                            {activeSection === 'overview' && (
                                <motion.div variants={containerVariants}>
                                    {/* Analytics Overview Banner */}

                                    {/* Summary Cards - Enhanced with real data trends */}
                                    <motion.div variants={itemVariants} className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-5 mb-8">
                                        <InfoCard
                                            title={t('dashboard.cards.participants')}
                                            value={context?.participants?.length ?? null}
                                            icon={Users}
                                            trend={context?.participants?.length > 0 ? Math.round(100 / context.participants.length) : 0}
                                            message={`${participantRolesData.length} ${t('dashboard.cards.differentRoles')}`}
                                            color="blue"
                                        />
                                        <InfoCard
                                            title={t('dashboard.cards.businessValues')}
                                            value={bValues?.length ?? null}
                                            icon={Target}
                                            trend={bValues?.length > 0 ? Math.round(100 / bValues.length) : 0}
                                            message={`${businessValuePillarsData.length} ${t('dashboard.cards.pillarsCovered')}`}
                                            color="green"
                                        />
                                        <InfoCard
                                            title={t('dashboard.cards.dreadedEvents')}
                                            value={events?.length ?? null}
                                            icon={AlertTriangle}
                                            trend={events?.filter(e => e.severity === 'critical' || e.severity === 'catastrophic').length > 0 ?
                                                -Math.round((events.filter(e => e.severity === 'critical' || e.severity === 'catastrophic').length / events.length) * 100) : 0}
                                            message={`${events?.filter(e => e.severity === 'critical' || e.severity === 'catastrophic').length || 0} ${t('dashboard.cards.critical')}`}
                                            color="amber"
                                        />
                                        <InfoCard
                                            title={t('dashboard.cards.securityMeasures')}
                                            value={controlsData?.length ?? null}
                                            icon={Shield}
                                            trend={controlsData?.filter(c => c.status === 'implemented').length > 0 ?
                                                Math.round((controlsData.filter(c => c.status === 'implemented').length / controlsData.length) * 100) : 0}
                                            message={`${controlsData?.filter(c => c.status === 'implemented').length || 0} ${t('dashboard.cards.implemented')}`}
                                            color="purple"
                                        />
                                    </motion.div>

                                    {/* Main Dashboard Layout - Rearranged for better visual flow */}
                                    <div className="grid grid-cols-12 gap-8">
                                        {/* Left Column - 7/12 width for main charts */}
                                        <div className="col-span-12 lg:col-span-7 space-y-6">
                                            {/* Top Row - Security Status Distribution */}
                                            <motion.div variants={itemVariants}>
                                                {(securityControlStatusData.length > 0) ? (
                                                    <ChartContainer title={t('dashboard.charts.securityDistribution')} icon={Shield}>
                                                        <ResponsiveContainer width="100%" height={350}>
                                                            <PieChart>
                                                                <Pie
                                                                    data={securityControlStatusData}
                                                                    dataKey="value"
                                                                    label={({ name, percent }) => `${name} (${(percent * 100).toFixed(0)}%)`}
                                                                    outerRadius={120}
                                                                    innerRadius={60}
                                                                    paddingAngle={2}
                                                                    fill="#8884d8"
                                                                    labelLine={true}
                                                                    animationDuration={1500}
                                                                    animationBegin={300}
                                                                >
                                                                    {securityControlStatusData.map((entry, index) => (
                                                                        <Cell key={`cell-status-${index}`} fill={entry.color} strokeWidth={1} stroke="#fff" />
                                                                    ))}
                                                                </Pie>
                                                                <Tooltip
                                                                    contentStyle={{
                                                                        borderRadius: '12px',
                                                                        boxShadow: '0 10px 25px -5px rgba(0, 0, 0, 0.1)',
                                                                        backgroundColor: 'rgba(255, 255, 255, 0.95)',
                                                                        border: 'none',
                                                                        padding: '12px'
                                                                    }}
                                                                />
                                                                <Legend
                                                                    layout="horizontal"
                                                                    verticalAlign="bottom"
                                                                    align="center"
                                                                    wrapperStyle={{
                                                                        paddingTop: '20px',
                                                                        fontSize: '12px'
                                                                    }}
                                                                />
                                                            </PieChart>
                                                        </ResponsiveContainer>
                                                    </ChartContainer>
                                                ) : (
                                                    <EmptyChartPlaceholder title={t('dashboard.charts.securityDistribution')} icon={PieChartLucide}/>
                                                )}
                                            </motion.div>

                                            {/* Middle Row - Events by Pillar (Radar Chart) */}
                                            <motion.div variants={itemVariants}>
                                                {(securityPillarEventsData.length > 0) ? (
                                                    <ChartContainer title={t('dashboard.charts.eventsByPillar')} icon={AlertTriangle}>
                                                        <ResponsiveContainer width="100%" height={350}>
                                                            <RadarChart
                                                                outerRadius={150}
                                                                width={500}
                                                                height={350}
                                                                data={securityPillarEventsData}
                                                                margin={{ top: 20, right: 30, left: 20, bottom: 20 }}
                                                            >
                                                                <PolarGrid strokeDasharray="3 3" stroke="#333" strokeWidth={1} />
                                                                <PolarAngleAxis
                                                                    dataKey="subject"
                                                                    tick={(props) => {
                                                                        // Find the corresponding pillar to get its color
                                                                        const pillar = SECURITY_PILLARS.find(p => p.name === props.payload.value);
                                                                        const color = pillar ? pillar.color : '#4B5563';

                                                                        return (
                                                                            <text
                                                                                x={props.x}
                                                                                y={props.y}
                                                                                textAnchor={props.textAnchor}
                                                                                fill={color}
                                                                                fontSize={12}
                                                                                fontWeight="600"
                                                                            >
                                                                                {props.payload.value}
                                                                            </text>
                                                                        );
                                                                    }}
                                                                />
                                                                <PolarRadiusAxis
                                                                    angle={30}
                                                                    domain={[0, Math.ceil(Math.max(...securityPillarEventsData.map(d => d.value)))]}
                                                                    stroke="#333"
                                                                    strokeWidth={1}
                                                                    tickCount={Math.ceil(Math.max(...securityPillarEventsData.map(d => d.value))) + 1}
                                                                    allowDecimals={false}
                                                                />
                                                                <Tooltip
                                                                    contentStyle={{
                                                                        borderRadius: '12px',
                                                                        boxShadow: '0 10px 25px -5px rgba(0, 0, 0, 0.1)',
                                                                        backgroundColor: 'rgba(255, 255, 255, 0.95)',
                                                                        border: 'none',
                                                                        padding: '12px'
                                                                    }}
                                                                    formatter={(value) => [`${Math.round(value)} événement(s)`, t('dashboard.charts.numberOfEvents')]}
                                                                />
                                                                {/* Single radar line showing event counts for all pillars */}
                                                                <Radar
                                                                    name={t('dashboard.charts.numberOfEvents')}
                                                                    dataKey="value"
                                                                    stroke="#000"
                                                                    strokeWidth={2}
                                                                    fill="#000"
                                                                    fillOpacity={0.1}
                                                                    animationDuration={1500}
                                                                    animationBegin={300}
                                                                />
                                                                {/* Legend removed as requested */}
                                                            </RadarChart>
                                                        </ResponsiveContainer>
                                                    </ChartContainer>
                                                ) : (
                                                    <EmptyChartPlaceholder title={t('dashboard.charts.eventsByPillar')} icon={BarChartLucide}/>
                                                )}
                                            </motion.div>

                                            {/* NEW: Events by Impact Type Chart */}
                                            <motion.div variants={itemVariants}>
                                                {(eventsByImpactData.length > 0) ? (
                                                    <ChartContainer title={t('dashboard.charts.impactTypes')} icon={Target}>
                                                        <ResponsiveContainer width="100%" height={350}>
                                                            <RadarChart
                                                                outerRadius={150}
                                                                width={500}
                                                                height={350}
                                                                data={eventsByImpactData.map(item => ({
                                                                    subject: item.name,
                                                                    value: item.value,
                                                                    fullMark: Math.max(...eventsByImpactData.map(d => d.value)) + 2,
                                                                    color: item.color
                                                                }))}
                                                                margin={{ top: 20, right: 30, left: 20, bottom: 20 }}
                                                            >
                                                                <PolarGrid strokeDasharray="3 3" stroke="#333" strokeWidth={1} />
                                                                <PolarAngleAxis
                                                                    dataKey="subject"
                                                                    tick={(props) => {
                                                                        // Find the corresponding impact to get its color
                                                                        const impact = eventsByImpactData.find(imp => imp.name === props.payload.value);
                                                                        const color = impact ? impact.color : '#4B5563';

                                                                        return (
                                                                            <text
                                                                                x={props.x}
                                                                                y={props.y}
                                                                                textAnchor={props.textAnchor}
                                                                                fill={color}
                                                                                fontSize={11}
                                                                                fontWeight="600"
                                                                            >
                                                                                {props.payload.value}
                                                                            </text>
                                                                        );
                                                                    }}
                                                                />
                                                                <PolarRadiusAxis
                                                                    angle={30}
                                                                    domain={[0, Math.ceil(Math.max(...eventsByImpactData.map(d => d.value)))]}
                                                                    stroke="#333"
                                                                    strokeWidth={1}
                                                                    tickCount={Math.ceil(Math.max(...eventsByImpactData.map(d => d.value))) + 1}
                                                                    allowDecimals={false}
                                                                />
                                                                <Tooltip
                                                                    contentStyle={{
                                                                        borderRadius: '12px',
                                                                        boxShadow: '0 10px 25px -5px rgba(0, 0, 0, 0.1)',
                                                                        backgroundColor: 'rgba(255, 255, 255, 0.95)',
                                                                        border: 'none',
                                                                        padding: '12px'
                                                                    }}
                                                                    formatter={(value) => [`${Math.round(value)} événement(s)`, t('dashboard.charts.numberOfEvents')]}
                                                                />
                                                                <Radar
                                                                    name={t('dashboard.charts.numberOfEvents')}
                                                                    dataKey="value"
                                                                    stroke="#000"
                                                                    strokeWidth={2}
                                                                    fill="#000"
                                                                    fillOpacity={0.1}
                                                                    animationDuration={1500}
                                                                    animationBegin={300}
                                                                />
                                                            </RadarChart>
                                                        </ResponsiveContainer>
                                                        <div className="mt-2 px-4">
                                                            <ul className="grid grid-cols-2 md:grid-cols-3 gap-2 text-xs">
                                                                {eventsByImpactData.sort((a, b) => b.value - a.value).map((impact, index) => (
                                                                    <li key={index} className="flex items-center">
                                                                        <span className="w-3 h-3 rounded-full mr-2" style={{ backgroundColor: impact.color }}></span>
                                                                        <span className="truncate">{impact.name}</span>
                                                                        <span className="ml-1 font-semibold">({impact.value})</span>
                                                                    </li>
                                                                ))}
                                                            </ul>
                                                        </div>
                                                    </ChartContainer>
                                                ) : (
                                                    <EmptyChartPlaceholder title={t('dashboard.charts.impactTypes')} icon={Target}/>
                                                )}
                                            </motion.div>

                                            {/* Bottom Row - Implementation Timeline */}
                                            <motion.div variants={itemVariants}>
                                                <ChartContainer title={t('dashboard.charts.implementationTimeline')} icon={Calendar}>
                                                    {controlsData.length > 0 ? (
                                                        <ResponsiveContainer width="100%" height={300}>
                                                            <BarChart
                                                                data={[
                                                                    { name: t('dashboard.statusLabels.implemented'), value: controlsData.filter(c => c.status === 'implemented').length, color: STATUS_COLORS.implemented },
                                                                    { name: t('dashboard.statusLabels.partial'), value: controlsData.filter(c => c.status === 'partial').length, color: STATUS_COLORS.partial },
                                                                    { name: t('dashboard.statusLabels.planned'), value: controlsData.filter(c => c.status === 'planned').length, color: STATUS_COLORS.planned },
                                                                    { name: t('dashboard.statusLabels.toApply'), value: controlsData.filter(c => c.status === 'apply_before').length, color: STATUS_COLORS.apply_before },
                                                                    { name: t('dashboard.statusLabels.toAbandon'), value: controlsData.filter(c => c.status === 'abandoned').length, color: STATUS_COLORS.abandoned }
                                                                ]}
                                                                margin={{ top: 20, right: 30, left: 20, bottom: 20 }}
                                                                barCategoryGap={30}
                                                            >
                                                                <CartesianGrid strokeDasharray="3 3" opacity={0.6} />
                                                                <XAxis
                                                                    dataKey="name"
                                                                    axisLine={false}
                                                                    tickLine={false}
                                                                    tick={{ fontSize: 12, fill: '#4B5563' }}
                                                                />
                                                                <YAxis
                                                                    axisLine={false}
                                                                    tickLine={false}
                                                                />
                                                                <Tooltip
                                                                    contentStyle={{
                                                                        borderRadius: '12px',
                                                                        boxShadow: '0 10px 25px -5px rgba(0, 0, 0, 0.1)',
                                                                        backgroundColor: 'rgba(255, 255, 255, 0.95)',
                                                                        border: 'none',
                                                                        padding: '12px'
                                                                    }}
                                                                    cursor={{ fill: 'rgba(236, 240, 243, 0.5)' }}
                                                                />
                                                                <Legend
                                                                    layout="horizontal"
                                                                    verticalAlign="bottom"
                                                                    align="center"
                                                                    wrapperStyle={{
                                                                        paddingTop: '10px',
                                                                        fontSize: '12px'
                                                                    }}
                                                                />
                                                                <Bar
                                                                    dataKey="value"
                                                                    name={t('dashboard.charts.numberOfMeasures')}
                                                                    radius={[4, 4, 0, 0]}
                                                                    animationDuration={1500}
                                                                >
                                                                    {[
                                                                        { name: 'En place', value: controlsData.filter(c => c.status === 'implemented').length, color: STATUS_COLORS.implemented },
                                                                        { name: 'Partiel', value: controlsData.filter(c => c.status === 'partial').length, color: STATUS_COLORS.partial },
                                                                        { name: 'Planifié', value: controlsData.filter(c => c.status === 'planned').length, color: STATUS_COLORS.planned },
                                                                        { name: 'À appliquer', value: controlsData.filter(c => c.status === 'apply_before').length, color: STATUS_COLORS.apply_before },
                                                                        { name: 'À abandonner', value: controlsData.filter(c => c.status === 'abandoned').length, color: STATUS_COLORS.abandoned }
                                                                    ].map((entry, index) => (
                                                                        <Cell key={`cell-${index}`} fill={entry.color} />
                                                                    ))}
                                                                </Bar>
                                                            </BarChart>
                                                        </ResponsiveContainer>
                                                    ) : (
                                                        <div className="flex flex-col items-center justify-center h-[300px] bg-gradient-to-b from-gray-50 to-white rounded-lg border border-gray-100">
                                                            <Shield size={40} className="text-gray-300 mb-2" />
                                                            <p className="text-gray-500 text-sm">{t('dashboard.empty.noSecurityMeasures')}</p>
                                                            <p className="text-gray-400 text-xs mt-1">{t('dashboard.empty.defineSecurityMeasures')}</p>
                                                        </div>
                                                    )}
                                                </ChartContainer>
                                            </motion.div>
                                        </div>

                                        {/* Right Column - 5/12 width for secondary charts */}
                                        <div className="col-span-12 lg:col-span-5 space-y-6">
                                            {/* Security Status Summary */}
                                            <motion.div variants={itemVariants}>
                                                <ChartContainer title={t('dashboard.charts.globalSecurityStatus')} icon={Shield}>
                                                    <div className="p-4">
                                                        <div className="mb-6 flex justify-center">
                                                            <motion.div
                                                                initial={{ opacity: 0, scale: 0.8 }}
                                                                animate={{ opacity: 1, scale: 1 }}
                                                                transition={{ delay: 0.5, duration: 0.5 }}
                                                                className="relative w-40 h-40"
                                                            >
                                                                {/* Circular progress indicator with gradient */}
                                                                <svg className="w-full h-full" viewBox="0 0 100 100">
                                                                    {/* Background circle */}
                                                                    <circle
                                                                        cx="50" cy="50" r="45"
                                                                        fill="none"
                                                                        stroke="#f0f0f0"
                                                                        strokeWidth="10"
                                                                    />

                                                                    {/* Gradient definition */}
                                                                    <defs>
                                                                        <linearGradient id="progressGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                                                                            <stop offset="0%" stopColor="#4F46E5" />
                                                                            <stop offset="100%" stopColor="#818CF8" />
                                                                        </linearGradient>
                                                                    </defs>

                                                                    {/* Progress circle with gradient */}
                                                                    <motion.circle
                                                                        cx="50" cy="50" r="45"
                                                                        fill="none"
                                                                        stroke="url(#progressGradient)"
                                                                        strokeWidth="10"
                                                                        strokeLinecap="round"
                                                                        strokeDasharray="283"  // 2πr
                                                                        initial={{ strokeDashoffset: 283 }}
                                                                        animate={{ strokeDashoffset: 283 * (1 - (controlsData?.length ? (controlsData.filter(c => c.status === 'implemented').length / controlsData.length) : 0)) }}
                                                                        transition={{ duration: 1.5, delay: 0.5 }}
                                                                        transform="rotate(-90 50 50)"
                                                                    />
                                                                </svg>
                                                                <div className="absolute top-0 left-0 w-full h-full flex items-center justify-center">
                                                                    <div className="text-center">
                                                                        <span className="text-3xl font-bold text-indigo-600">
                                                                            {controlsData?.length ? Math.round((controlsData.filter(c => c.status === 'implemented').length / controlsData.length) * 100) : 0}%
                                                                        </span>
                                                                        <span className="block text-sm text-gray-500 mt-1">{t('dashboard.charts.security')}</span>
                                                                    </div>
                                                                </div>
                                                            </motion.div>
                                                        </div>

                                                        <div className="space-y-4">
                                                            {/* Security statistics with enhanced styling */}
                                                            <div className="flex justify-between items-center bg-indigo-50 p-3 rounded-lg">
                                                                <span className="text-sm font-medium text-indigo-700">{t('dashboard.statusLabels.implementedMeasures')}</span>
                                                                <div className="flex items-center bg-white px-3 py-1 rounded-full shadow-sm">
                                                                    <span className="text-sm font-medium text-indigo-600">
                                                                        {controlsData?.filter(c => c.status === 'implemented')?.length || 0}
                                                                    </span>
                                                                    <span className="text-xs text-gray-500 ml-1">
                                                                        /{controlsData?.length || 0}
                                                                    </span>
                                                                </div>
                                                            </div>

                                                            <div className="flex justify-between items-center bg-amber-50 p-3 rounded-lg">
                                                                <span className="text-sm font-medium text-amber-700">{t('dashboard.statusLabels.partialMeasures')}</span>
                                                                <div className="flex items-center bg-white px-3 py-1 rounded-full shadow-sm">
                                                                    <span className="text-sm font-medium text-amber-600">
                                                                        {controlsData?.filter(c => c.status === 'partial')?.length || 0}
                                                                    </span>
                                                                    <span className="text-xs text-gray-500 ml-1">
                                                                        /{controlsData?.length || 0}
                                                                    </span>
                                                                </div>
                                                            </div>

                                                            <div className="flex justify-between items-center bg-emerald-50 p-3 rounded-lg">
                                                                <span className="text-sm font-medium text-emerald-700">{t('dashboard.statusLabels.plannedMeasures')}</span>
                                                                <div className="flex items-center bg-white px-3 py-1 rounded-full shadow-sm">
                                                                    <span className="text-sm font-medium text-emerald-600">
                                                                        {controlsData?.filter(c => c.status === 'planned')?.length || 0}
                                                                    </span>
                                                                    <span className="text-xs text-gray-500 ml-1">
                                                                        /{controlsData?.length || 0}
                                                                    </span>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </ChartContainer>
                                            </motion.div>

                                            {/* Events by Severity Chart */}
                                            <motion.div variants={itemVariants}>
                                                {(eventsBySeverityData.length > 0) ? (
                                                    <ChartContainer title={t('dashboard.charts.eventsBySeverity')} icon={AlertTriangle}>
                                                        <ResponsiveContainer width="100%" height={300}>
                                                            <PieChart>
                                                                <Pie
                                                                    data={eventsBySeverityData}
                                                                    dataKey="value"
                                                                    label={({ name, percent }) => `${name} (${(percent * 100).toFixed(0)}%)`}
                                                                    outerRadius={90}
                                                                    innerRadius={40}
                                                                    paddingAngle={2}
                                                                    fill="#8884d8"
                                                                    labelLine={true}
                                                                    animationDuration={1500}
                                                                    animationBegin={300}
                                                                >
                                                                    {eventsBySeverityData.map((entry, index) => (
                                                                        <Cell key={`cell-severity-${index}`} fill={entry.color || ROLE_COLORS[index % ROLE_COLORS.length]} strokeWidth={1} stroke="#fff" />
                                                                    ))}
                                                                </Pie>
                                                                <Tooltip
                                                                    contentStyle={{
                                                                        borderRadius: '12px',
                                                                        boxShadow: '0 10px 25px -5px rgba(0, 0, 0, 0.1)',
                                                                        backgroundColor: 'rgba(255, 255, 255, 0.95)',
                                                                        border: 'none',
                                                                        padding: '12px'
                                                                    }}
                                                                />
                                                                <Legend
                                                                    layout="horizontal"
                                                                    verticalAlign="bottom"
                                                                    align="center"
                                                                    wrapperStyle={{
                                                                        paddingTop: '20px',
                                                                        fontSize: '12px'
                                                                    }}
                                                                />
                                                            </PieChart>
                                                        </ResponsiveContainer>
                                                    </ChartContainer>
                                                ) : (
                                                    <EmptyChartPlaceholder title={t('dashboard.charts.eventsBySeverity')} icon={PieChartLucide}/>
                                                )}
                                            </motion.div>

                                            {/* Business Value Pillars Chart */}
                                            <motion.div variants={itemVariants}>
                                                {(businessValuePillarsData.length > 0) ? (
                                                    <ChartContainer title={t('dashboard.charts.businessValuePillars')} icon={Target}>
                                                        <ResponsiveContainer width="100%" height={300}>
                                                            <PieChart>
                                                                <Pie
                                                                    data={businessValuePillarsData}
                                                                    dataKey="value"
                                                                    label={({ name, percent }) => `${name} (${(percent * 100).toFixed(0)}%)`}
                                                                    outerRadius={90}
                                                                    innerRadius={40}
                                                                    paddingAngle={2}
                                                                    fill="#8884d8"
                                                                    labelLine={true}
                                                                    animationDuration={1500}
                                                                    animationBegin={300}
                                                                >
                                                                    {businessValuePillarsData.map((entry, index) => (
                                                                        <Cell key={`cell-bv-${index}`} fill={PILLAR_COLORS[index % PILLAR_COLORS.length]} strokeWidth={1} stroke="#fff" />
                                                                    ))}
                                                                </Pie>
                                                                <Tooltip
                                                                    contentStyle={{
                                                                        borderRadius: '12px',
                                                                        boxShadow: '0 10px 25px -5px rgba(0, 0, 0, 0.1)',
                                                                        backgroundColor: 'rgba(255, 255, 255, 0.95)',
                                                                        border: 'none',
                                                                        padding: '12px'
                                                                    }}
                                                                />
                                                                <Legend
                                                                    layout="horizontal"
                                                                    verticalAlign="bottom"
                                                                    align="center"
                                                                    wrapperStyle={{
                                                                        paddingTop: '20px',
                                                                        fontSize: '12px'
                                                                    }}
                                                                />
                                                            </PieChart>
                                                        </ResponsiveContainer>
                                                    </ChartContainer>
                                                ) : (
                                                    <EmptyChartPlaceholder title={t('dashboard.charts.businessValuePillars')} icon={PieChartLucide}/>
                                                )}
                                            </motion.div>


                                        </div>
                                    </div>
                                </motion.div>
                            )}

                            {/* Participants Section */}
                            {activeSection === 'participants' && (
                                <motion.div variants={containerVariants}>
                                    {(context?.participants?.length > 0) ? (
                                        <InfoSection title="Liste des participants" icon={Users}>
                                            <div className="p-4">
                                                <div className="mb-4 flex flex-wrap gap-2 justify-between items-center">
                                                    <div className="flex items-center gap-2">
                                                        <div className="relative">
                                                            <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                                                                <svg className="w-4 h-4 text-gray-500" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 20 20">
                                                                    <path stroke="currentColor" strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="m19 19-4-4m0-7A7 7 0 1 1 1 8a7 7 0 0 1 14 0Z"/>
                                                                </svg>
                                                            </div>
                                                            <input type="search" className="block w-full p-2 pl-10 text-sm border border-gray-300 rounded-lg bg-gray-50 focus:ring-blue-500 focus:border-blue-500" placeholder="Rechercher un participant..." />
                                                        </div>
                                                        <button className="p-2 text-gray-500 bg-gray-100 hover:bg-gray-200 rounded-lg">
                                                            <Filter size={18} />
                                                        </button>
                                                    </div>
                                                    <button
                                                        onClick={() => switchToTab('context')}
                                                        className="px-4 py-2 text-white bg-blue-600 rounded-lg hover:bg-blue-700 transition-colors flex items-center"
                                                    >
                                                        <Users size={16} className="mr-2" /> Ajouter un participant
                                                    </button>
                                                </div>

                                                <div className="overflow-x-auto">
                                                    <table className="min-w-full divide-y divide-gray-200 rounded-lg overflow-hidden">
                                                        <thead className="bg-gray-50">
                                                            <tr>
                                                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Nom</th>
                                                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Fonction</th>
                                                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Rôles RACI</th>
                                                                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                                                            </tr>
                                                        </thead>
                                                        <tbody className="bg-white divide-y divide-gray-200">
                                                            {context.participants.map((p) => (
                                                                <motion.tr
                                                                    key={p.id}
                                                                    whileHover={{ backgroundColor: 'rgba(249, 250, 251, 0.5)' }}
                                                                >
                                                                    <td className="px-6 py-4 whitespace-nowrap">
                                                                        <div className="flex items-center">
                                                                            <div className="flex-shrink-0 h-8 w-8 rounded-full bg-blue-100 flex items-center justify-center text-blue-800 font-medium">
                                                                                {(p.name || 'U').charAt(0).toUpperCase()}
                                                                            </div>
                                                                            <div className="ml-3">
                                                                                <div className="text-sm font-medium text-gray-900">{p.name || 'Sans nom'}</div>
                                                                                <div className="text-xs text-gray-500">{p.email || ''}</div>
                                                                            </div>
                                                                        </div>
                                                                    </td>
                                                                    <td className="px-6 py-4 whitespace-nowrap">
                                                                        <div className="text-sm text-gray-500">
                                                                            {(p.position === "OTHER" && p.customPosition) ?
                                                                                p.customPosition :
                                                                                (p.position || 'Non défini')}
                                                                        </div>
                                                                    </td>
                                                                    <td className="px-6 py-4 whitespace-nowrap">
                                                                        {/* Check if workshopRoles exists and has entries */}
                                                                        {p.workshopRoles && Object.keys(p.workshopRoles).length > 0 ? (
                                                                            <div className="flex space-x-1">
                                                                                {/* Iterate over the workshopRoles object directly */}
                                                                                {Object.entries(p.workshopRoles).map(([w, r]) => r && (
                                                                                    <span
                                                                                        key={`${p.id}-${w}`} // Key remains participant id + workshop name
                                                                                        className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium"
                                                                                        style={{
                                                                                            backgroundColor: r === 'R' ? '#d1fae5' : r === 'A' ? '#dbeafe' : r === 'C' ? '#fef3c7' : r === 'I' ? '#fee2e2' : '#f3f4f6',
                                                                                            color: r === 'R' ? '#065f46' : r === 'A' ? '#1e40af' : r === 'C' ? '#92400e' : r === 'I' ? '#991b1b' : '#374151'
                                                                                        }}
                                                                                    >
                                                                                        {r} {/* Display the role */}
                                                                                    </span>
                                                                                ))}
                                                                            </div>
                                                                        ) : ('Aucun rôle')} {/* Fallback if no roles */}
                                                                    </td>
                                                                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm">
                                                                        <button className="text-blue-600 hover:text-blue-800 px-2 py-1 rounded hover:bg-blue-50 transition-colors">
                                                                            Détails
                                                                        </button>
                                                                    </td>
                                                                </motion.tr>
                                                            ))}
                                                        </tbody>
                                                    </table>
                                                </div>
                                                <div className="flex justify-end mt-4">
                                                    <button className="px-4 py-2 text-sm text-blue-600 bg-blue-50 rounded-lg hover:bg-blue-100 transition-colors flex items-center">
                                                        <ExternalLink size={16} className="mr-1" /> Voir tous les participants
                                                    </button>
                                                </div>
                                            </div>
                                        </InfoSection>
                                    ) : (
                                        <InfoSection title="Liste des participants" icon={Users}>
                                            <div className="p-8 text-center">
                                                <Users size={48} className="mx-auto mb-3 text-gray-300" />
                                                <h3 className="text-gray-600 font-medium mb-1">Aucun participant trouvé</h3>
                                                <p className="text-gray-500 text-sm mb-4">Ajoutez des participants pour commencer votre analyse</p>
                                                <button
                                                    onClick={() => switchToTab('context')}
                                                    className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                                                >
                                                    Ajouter des participants
                                                </button>
                                            </div>
                                        </InfoSection>
                                    )}
                                </motion.div>
                            )}

                            {/* Dreaded Events Section */}
                            {activeSection === 'events' && (
                                <motion.div variants={containerVariants}>
                                    {(events?.length > 0) ? (
                                        <InfoSection title="Événements redoutés" icon={AlertTriangle}>
                                            <div className="p-4">
                                                <div className="mb-4 flex flex-wrap gap-2 justify-between items-center">
                                                    <div className="flex items-center gap-2">
                                                        <div className="relative">
                                                            <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                                                                <svg className="w-4 h-4 text-gray-500" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 20 20">
                                                                    <path stroke="currentColor" strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="m19 19-4-4m0-7A7 7 0 1 1 1 8a7 7 0 0 1 14 0Z"/>
                                                                </svg>
                                                            </div>
                                                            <input type="search" className="block w-full p-2 pl-10 text-sm border border-gray-300 rounded-lg bg-gray-50 focus:ring-blue-500 focus:border-blue-500" placeholder="Rechercher un événement..." />
                                                        </div>
                                                        <select className="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block p-2">
                                                            <option value="">Tous les piliers</option>
                                                            <option value="confidentiality">Confidentialité</option>
                                                            <option value="integrity">Intégrité</option>
                                                            <option value="availability">Disponibilité</option>
                                                        </select>
                                                        <select className="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block p-2">
                                                            <option value="">Toutes les sévérités</option>
                                                            <option value="catastrophic">Catastrophique</option>
                                                            <option value="critical">Critique</option>
                                                            <option value="major">Majeur</option>
                                                            <option value="moderate">Modéré</option>
                                                            <option value="minor">Mineur</option>
                                                        </select>
                                                    </div>
                                                    <button
                                                        onClick={() => switchToTab('events')}
                                                        className="px-4 py-2 text-white bg-amber-600 rounded-lg hover:bg-amber-700 transition-colors flex items-center"
                                                    >
                                                        <AlertTriangle size={16} className="mr-2" /> Ajouter un événement
                                                    </button>
                                                </div>

                                                <div className="space-y-4">
                                                    {events.map((event) => (
                                                        <motion.div
                                                            key={event.id}
                                                            className="border rounded-xl p-5 hover:shadow-md transition-shadow duration-200 bg-white"
                                                            whileHover={{ y: -2, boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1)' }}
                                                        >
                                                            <div className="flex justify-between items-start">
                                                                <h4 className="font-medium text-gray-800 flex items-center">
                                                                    <AlertTriangle size={16} className="mr-2 text-amber-500" />
                                                                    {event.name}
                                                                </h4>
                                                                <span className={`inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium border ${
                                                                    {
                                                                        minor: 'bg-emerald-50 text-emerald-700 border-emerald-200',
                                                                        moderate: 'bg-amber-50 text-amber-700 border-amber-200',
                                                                        major: 'bg-orange-50 text-orange-800 border-orange-200',
                                                                        critical: 'bg-red-50 text-red-700 border-red-200',
                                                                        catastrophic: 'bg-slate-900 text-slate-50 border-slate-700'
                                                                    }[event.severity] || 'bg-gray-100 text-gray-800 border-gray-200'
                                                                }`}>
                                                                    {event.severity?.charAt(0).toUpperCase() + event.severity?.slice(1) || 'Non défini'}
                                                                </span>
                                                            </div>
                                                            <p className="text-sm text-gray-600 mt-3 bg-gray-50 p-3 rounded-lg">
                                                                {event.description}
                                                            </p>
                                                            <div className="mt-4 flex flex-wrap gap-2">
                                                                {event.securityPillar && (
                                                                    <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                                                                        <Shield size={12} className="mr-1" />
                                                                        {event.securityPillar}
                                                                    </span>
                                                                )}
                                                                {event.businessValue && (
                                                                    <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                                                        <Target size={12} className="mr-1" />
                                                                        {/* Find the business value name using its ID - comparing as strings */}
                                                                        {bValues.find(bv => String(bv.id) === String(event.businessValue))?.name || `ID: ${event.businessValue}`}
                                                                    </span>
                                                                )}
                                                                <div className="flex-grow"></div>
                                                                <button
                                                                    onClick={() => switchToTab('events')}
                                                                    className="text-xs text-blue-600 hover:text-blue-800 hover:underline"
                                                                >
                                                                    Modifier
                                                                </button>
                                                                <button
                                                                    onClick={() => switchToTab('events')}
                                                                    className="text-xs text-gray-500 hover:text-gray-700 hover:underline"
                                                                >
                                                                    Détails
                                                                </button>
                                                            </div>
                                                        </motion.div>
                                                    ))}
                                                </div>

                                                {events.length > 5 && (
                                                    <div className="mt-6 flex justify-center">
                                                        <button className="px-4 py-2 bg-white border border-gray-300 rounded-lg shadow-sm text-sm text-gray-700 hover:bg-gray-50 flex items-center">
                                                            <ChevronDown size={16} className="mr-1" /> Voir plus d'événements
                                                        </button>
                                                    </div>
                                                )}
                                            </div>
                                        </InfoSection>
                                    ) : (
                                        <InfoSection title="Événements redoutés" icon={AlertTriangle}>
                                            <div className="p-8 text-center">
                                                <AlertTriangle size={48} className="mx-auto mb-3 text-gray-300" />
                                                <h3 className="text-gray-600 font-medium mb-1">Aucun événement redouté défini</h3>
                                                <p className="text-gray-500 text-sm mb-4">
                                                    Identifiez les événements redoutés pour continuer l'analyse des risques
                                                </p>
                                                <button
                                                    onClick={() => switchToTab('events')}
                                                    className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                                                >
                                                    Définir des événements
                                                </button>
                                            </div>
                                        </InfoSection>
                                    )}
                                </motion.div>
                            )}

                            {/* Security Measures Section */}
                            {activeSection === 'security' && (
                                <motion.div variants={containerVariants}>
                                    {(controlsData?.length > 0) ? (
                                        <InfoSection title="Mesures de sécurité" icon={Shield}>
                                            <div className="p-4">
                                                <div className="mb-4 flex flex-col md:flex-row flex-wrap md:items-center gap-3 justify-between">
                                                    <div className="flex items-center gap-2 flex-wrap">
                                                        <div className="relative">
                                                            <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                                                                <svg className="w-4 h-4 text-gray-500" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 20 20">
                                                                    <path stroke="currentColor" strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="m19 19-4-4m0-7A7 7 0 1 1 1 8a7 7 0 0 1 14 0Z"/>
                                                                </svg>
                                                            </div>
                                                            <input type="search" className="block w-full p-2 pl-10 text-sm border border-gray-300 rounded-lg bg-gray-50 focus:ring-blue-500 focus:border-blue-500" placeholder="Rechercher une mesure..." />
                                                        </div>

                                                        <div className="flex">
                                                            <motion.button
                                                                variants={buttonGroupVariants}
                                                                animate={securityFilter === 'all' ? 'active' : 'inactive'}
                                                                whileHover={{ backgroundColor: securityFilter === 'all' ? '#4338CA' : '#F3F4F6' }}
                                                                onClick={() => setSecurityFilter('all')}
                                                                className="px-3 py-2 text-sm font-medium rounded-l-lg border border-r-0 flex items-center"
                                                            >
                                                                Tous
                                                            </motion.button>
                                                            <motion.button
                                                                variants={buttonGroupVariants}
                                                                animate={securityFilter === 'implemented' ? 'active' : 'inactive'}
                                                                whileHover={{ backgroundColor: securityFilter === 'implemented' ? '#4338CA' : '#F3F4F6' }}
                                                                onClick={() => setSecurityFilter('implemented')}
                                                                className="px-3 py-2 text-sm font-medium border border-r-0 flex items-center"
                                                            >
                                                                <CheckCircle size={14} className="mr-1" /> En place
                                                            </motion.button>
                                                            <motion.button
                                                                variants={buttonGroupVariants}
                                                                animate={securityFilter === 'partial' ? 'active' : 'inactive'}
                                                                whileHover={{ backgroundColor: securityFilter === 'partial' ? '#4338CA' : '#F3F4F6' }}
                                                                onClick={() => setSecurityFilter('partial')}
                                                                className="px-3 py-2 text-sm font-medium border border-r-0 flex items-center"
                                                            >
                                                                <Clock size={14} className="mr-1" /> Partiel
                                                            </motion.button>
                                                            <motion.button
                                                                variants={buttonGroupVariants}
                                                                animate={securityFilter === 'planned' ? 'active' : 'inactive'}
                                                                whileHover={{ backgroundColor: securityFilter === 'planned' ? '#4338CA' : '#F3F4F6' }}
                                                                onClick={() => setSecurityFilter('planned')}
                                                                className="px-3 py-2 text-sm font-medium border border-r-0 flex items-center"
                                                            >
                                                                <Calendar size={14} className="mr-1" /> Planifié
                                                            </motion.button>
                                                            <motion.button
                                                                variants={buttonGroupVariants}
                                                                animate={securityFilter === 'apply_before' ? 'active' : 'inactive'}
                                                                whileHover={{ backgroundColor: securityFilter === 'apply_before' ? '#4338CA' : '#F3F4F6' }}
                                                                onClick={() => setSecurityFilter('apply_before')}
                                                                className="px-3 py-2 text-sm font-medium border border-r-0 flex items-center"
                                                            >
                                                                <Calendar size={14} className="mr-1" /> À appliquer
                                                            </motion.button>
                                                            <motion.button
                                                                variants={buttonGroupVariants}
                                                                animate={securityFilter === 'abandoned' ? 'active' : 'inactive'}
                                                                whileHover={{ backgroundColor: securityFilter === 'abandoned' ? '#4338CA' : '#F3F4F6' }}
                                                                onClick={() => setSecurityFilter('abandoned')}
                                                                className="px-3 py-2 text-sm font-medium rounded-r-lg border flex items-center"
                                                            >
                                                                <XCircle size={14} className="mr-1" /> À abandonner
                                                            </motion.button>
                                                        </div>
                                                    </div>

                                                    <div className="flex items-center gap-2">
                                                        <div className="flex">
                                                            <motion.button
                                                                variants={buttonGroupVariants}
                                                                animate={securityView === 'grid' ? 'active' : 'inactive'}
                                                                whileHover={{ backgroundColor: securityView === 'grid' ? '#4338CA' : '#F3F4F6' }}
                                                                onClick={() => setSecurityView('grid')}
                                                                className="px-3 py-2 text-sm font-medium rounded-l-lg border border-r-0 flex items-center"
                                                            >
                                                                <svg xmlns="http://www.w3.org/2000/svg" className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zm10 0a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zm10 0a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z" />
                                                                </svg>
                                                            </motion.button>
                                                            <motion.button
                                                                variants={buttonGroupVariants}
                                                                animate={securityView === 'list' ? 'active' : 'inactive'}
                                                                whileHover={{ backgroundColor: securityView === 'list' ? '#4338CA' : '#F3F4F6' }}
                                                                onClick={() => setSecurityView('list')}
                                                                className="px-3 py-2 text-sm font-medium rounded-r-lg border flex items-center"
                                                            >
                                                                <svg xmlns="http://www.w3.org/2000/svg" className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                                                                </svg>
                                                            </motion.button>
                                                        </div>

                                                        <button
                                                            onClick={() => switchToTab('security-controls')}
                                                            className="px-4 py-2 text-white bg-purple-600 rounded-lg hover:bg-purple-700 transition-colors flex items-center"
                                                        >
                                                            <Shield size={16} className="mr-2" /> Ajouter une mesure
                                                        </button>
                                                    </div>
                                                </div>

                                                <div className="bg-blue-50 p-3 rounded-lg mb-4 flex items-center justify-between">
                                                    <p className="text-blue-700 text-sm font-medium flex items-center">
                                                        <Info size={16} className="mr-2" />
                                                        {filteredControls.length} contrôles de sécurité affichés sur {controlsData.length} au total
                                                    </p>
                                                    <span className="text-xs text-blue-600 bg-blue-100 px-2 py-1 rounded-full">
                                                        {securityFilter === 'all' ? 'Tous les statuts' :
                                                         securityFilter === 'implemented' ? 'En place uniquement' :
                                                         securityFilter === 'partial' ? 'Partiels uniquement' :
                                                         securityFilter === 'planned' ? 'Planifiés uniquement' :
                                                         securityFilter === 'apply_before' ? 'À appliquer uniquement' :
                                                         securityFilter === 'abandoned' ? 'À abandonner uniquement' :
                                                         'Statut filtré'}
                                                    </span>
                                                </div>

                                                {securityView === 'grid' ? (
                                                    <div className="grid gap-3 md:grid-cols-2">
                                                        {filteredControls.map(control => (
                                                            <motion.div
                                                                key={control.id}
                                                                className="border rounded-lg p-3 bg-white hover:shadow-sm transition-all"
                                                                whileHover={{ backgroundColor: 'rgba(249, 250, 251, 0.8)' }}
                                                            >
                                                                <div className="flex justify-between items-start">
                                                                    <h4 className="font-medium text-gray-800 text-sm">{control.name}</h4>
                                                                    <span className={`px-2 py-0.5 rounded-full text-xs ${
                                                                        control.status === 'implemented' ? 'bg-green-100 text-green-800' :
                                                                        control.status === 'partial' ? 'bg-amber-100 text-amber-800' :
                                                                        control.status === 'planned' ? 'bg-blue-100 text-blue-800' :
                                                                        control.status === 'apply_before' ? 'bg-purple-100 text-purple-800' :
                                                                        control.status === 'abandoned' ? 'bg-red-100 text-red-800' :
                                                                        'bg-gray-100 text-gray-800'
                                                                    }`}>
                                                                        {control.status === 'implemented' ? 'En place' :
                                                                         control.status === 'partial' ? 'Partiel' :
                                                                         control.status === 'planned' ? 'Planifié' :
                                                                         control.status === 'apply_before' ? 'À appliquer avant le' :
                                                                         control.status === 'abandoned' ? 'À abandonner' :
                                                                         control.status || 'Non défini'}
                                                                    </span>
                                                                </div>

                                                                <div className="flex justify-between items-center mt-2">
                                                                    <div className="flex items-center">
                                                                        {control.type && control.type !== 'Standard' && (
                                                                            <span className="bg-purple-100 text-purple-800 text-xs px-2 py-0.5 rounded-full">
                                                                                {control.type}
                                                                            </span>
                                                                        )}
                                                                    </div>
                                                                    <button
                                                                        onClick={() => switchToTab('security-controls')}
                                                                        className="text-xs text-blue-600 hover:text-blue-800"
                                                                    >
                                                                        Détails
                                                                    </button>
                                                                </div>
                                                            </motion.div>
                                                        ))}
                                                    </div>
                                                ) : (
                                                    <div className="overflow-x-auto rounded-lg border">
                                                        <table className="min-w-full divide-y divide-gray-200">
                                                            <thead className="bg-gray-50">
                                                                <tr>
                                                                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Mesure</th>
                                                                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Statut</th>
                                                                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                                                                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Priorité</th>
                                                                    <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                                                                </tr>
                                                            </thead>
                                                            <tbody className="bg-white divide-y divide-gray-200">
                                                                {filteredControls.map(control => (
                                                                    <tr key={control.id} className="hover:bg-gray-50">
                                                                        <td className="px-6 py-4 whitespace-nowrap">
                                                                            <div className="flex items-center">
                                                                                <div className="text-sm font-medium text-gray-900">{control.name}</div>
                                                                            </div>
                                                                        </td>
                                                                        <td className="px-6 py-4 whitespace-nowrap">
                                                                            <span className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${
                                                                                control.status === 'implemented' ? 'bg-green-100 text-green-800' :
                                                                                control.status === 'partial' ? 'bg-amber-100 text-amber-800' :
                                                                                control.status === 'planned' ? 'bg-blue-100 text-blue-800' :
                                                                                control.status === 'apply_before' ? 'bg-purple-100 text-purple-800' :
                                                                                control.status === 'abandoned' ? 'bg-red-100 text-red-800' :
                                                                                'bg-gray-100 text-gray-800'
                                                                            }`}>
                                                                                {control.status === 'implemented' ? 'En place' :
                                                                                control.status === 'partial' ? 'Partiel' :
                                                                                control.status === 'planned' ? 'Planifié' :
                                                                                control.status === 'apply_before' ? 'À appliquer avant le' :
                                                                                control.status === 'abandoned' ? 'À abandonner' :
                                                                                control.status || 'Non défini'}
                                                                            </span>
                                                                        </td>
                                                                        <td className="px-6 py-4 whitespace-nowrap">
                                                                            <div className="text-sm text-gray-500">
                                                                                {control.type && control.type !== 'Standard' ? control.type : ''}
                                                                            </div>
                                                                        </td>
                                                                        <td className="px-6 py-4 whitespace-nowrap">
                                                                            <div className="text-sm text-gray-500">{control.priority || 'Moyenne'}</div>
                                                                        </td>
                                                                        <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                                                            <button
                                                                                onClick={() => switchToTab('security-controls')}
                                                                                className="text-blue-600 hover:text-blue-900 mr-2"
                                                                            >
                                                                                Modifier
                                                                            </button>
                                                                            <button
                                                                                onClick={() => switchToTab('security-controls')}
                                                                                className="text-gray-600 hover:text-gray-900"
                                                                            >
                                                                                Détails
                                                                            </button>
                                                                        </td>
                                                                    </tr>
                                                                ))}
                                                            </tbody>
                                                        </table>
                                                    </div>
                                                )}

                                                {filteredControls.length > 10 && (
                                                    <div className="flex justify-center mt-6">
                                                        <button className="px-4 py-2 text-sm text-blue-600 bg-blue-50 hover:bg-blue-100 rounded-lg transition-colors flex items-center">
                                                            <ChevronDown size={16} className="mr-1" />
                                                            Voir plus de mesures
                                                        </button>
                                                    </div>
                                                )}
                                            </div>
                                        </InfoSection>
                                    ) : (
                                        <InfoSection title="Mesures de sécurité" icon={Shield}>
                                            <div className="p-8 text-center">
                                                <Shield size={48} className="mx-auto mb-3 text-gray-300" />
                                                <h3 className="text-gray-600 font-medium mb-1">Aucune mesure de sécurité définie</h3>
                                                <p className="text-gray-500 text-sm mb-4">
                                                    Définissez des mesures de sécurité pour vous protéger contre les événements redoutés
                                                </p>
                                                <button className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                                                    Ajouter des mesures
                                                </button>
                                            </div>
                                        </InfoSection>
                                    )}
                                </motion.div>
                            )}

                            {/* Analysis Section */}
                            {activeSection === 'analysis' && (
                                <motion.div variants={containerVariants}>
                                    <motion.div variants={itemVariants} className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                                        <InfoCard
                                            title="Score de risque global"
                                            value="62.4"
                                            icon={LineChart}
                                            trend={-8}
                                            color="red"
                                        />
                                        <InfoCard
                                            title="Niveau de conformité"
                                            value="71%"
                                            icon={Award}
                                            trend={12}
                                            color="green"
                                        />
                                    </motion.div>

                                    <motion.div variants={itemVariants} className="bg-white rounded-xl shadow-md border border-gray-200 mb-6">
                                        <div className="p-4 border-b border-gray-200">
                                            <h3 className="font-medium text-gray-700 flex items-center">
                                                <LineChart size={18} className="mr-2 text-indigo-600" />
                                                Évolution du niveau de risque
                                            </h3>
                                        </div>
                                        <div className="p-4">
                                            <div className="h-[300px] flex flex-col items-center justify-center bg-gray-50 rounded-xl">
                                                <LineChart size={48} className="text-gray-300 mb-3" />
                                                <p className="text-gray-500 font-medium">Graphique d'évolution du risque</p>
                                                <p className="text-xs text-gray-400 max-w-xs mt-1 mb-4 text-center">
                                                    Visualisez l'évolution du niveau de risque au fil du temps pour suivre l'efficacité de vos mesures de sécurité.
                                                </p>
                                                <button className="px-4 py-2 bg-indigo-600 text-white rounded-lg text-sm">
                                                    Générer le rapport d'analyse
                                                </button>
                                            </div>
                                        </div>
                                    </motion.div>

                                    <motion.div variants={itemVariants} className="grid grid-cols-1 md:grid-cols-3 gap-6">
                                        <ChartContainer title="Impact des mesures" icon={Shield}>
                                            <div className="p-4 text-center">
                                                <div className="flex items-center justify-center h-[240px]">
                                                    <div className="relative inline-flex">
                                                        <div className="w-40 h-40 rounded-full overflow-hidden">
                                                            <div className="w-full h-full bg-emerald-100 flex items-center justify-center">
                                                                <div className="w-32 h-32 rounded-full bg-white flex items-center justify-center text-2xl font-bold text-emerald-700">
                                                                    42%
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div className="absolute -top-1 -right-1 bg-emerald-500 text-white w-8 h-8 rounded-full flex items-center justify-center">
                                                            <Zap size={16} />
                                                        </div>
                                                    </div>
                                                </div>
                                                <p className="text-sm text-gray-500 mt-2">Efficacité des mesures de sécurité</p>
                                            </div>
                                        </ChartContainer>

                                        <ChartContainer title="Répartition des risques" icon={PieChartLucide}>
                                            <div className="p-4 text-center">
                                                <div className="flex flex-col items-center justify-center h-[240px]">
                                                    <div className="grid grid-cols-3 gap-2 w-full max-w-xs mb-4">
                                                        <div className="bg-red-100 rounded-lg p-2 text-center">
                                                            <div className="text-2xl font-bold text-red-700">12</div>
                                                            <div className="text-xs text-red-600">Critiques</div>
                                                        </div>
                                                        <div className="bg-orange-100 rounded-lg p-2 text-center">
                                                            <div className="text-2xl font-bold text-orange-700">24</div>
                                                            <div className="text-xs text-orange-600">Majeurs</div>
                                                        </div>
                                                        <div className="bg-amber-100 rounded-lg p-2 text-center">
                                                            <div className="text-2xl font-bold text-amber-700">38</div>
                                                            <div className="text-xs text-amber-600">Modérés</div>
                                                        </div>
                                                    </div>
                                                    <p className="text-sm text-gray-500">Répartition par niveau de sévérité</p>
                                                </div>
                                            </div>
                                        </ChartContainer>

                                        <ChartContainer title="Actions requises" icon={CheckCircle}>
                                            <div className="p-4">
                                                <div className="space-y-3 h-[240px] overflow-y-auto">
                                                    <div className="bg-blue-50 p-2 rounded-lg border-l-4 border-blue-500">
                                                        <div className="flex items-center">
                                                            <div className="font-medium text-blue-800 text-sm">Mettre à jour les contrôles d'accès</div>
                                                            <span className="ml-auto text-xs text-blue-700 bg-blue-100 px-2 py-0.5 rounded-full">Haute</span>
                                                        </div>
                                                    </div>
                                                    <div className="bg-amber-50 p-2 rounded-lg border-l-4 border-amber-500">
                                                        <div className="flex items-center">
                                                            <div className="font-medium text-amber-800 text-sm">Revoir le plan de continuité</div>
                                                            <span className="ml-auto text-xs text-amber-700 bg-amber-100 px-2 py-0.5 rounded-full">Moyenne</span>
                                                        </div>
                                                    </div>
                                                    <div className="bg-green-50 p-2 rounded-lg border-l-4 border-green-500">
                                                        <div className="flex items-center">
                                                            <div className="font-medium text-green-800 text-sm">Former les utilisateurs</div>
                                                            <span className="ml-auto text-xs text-green-700 bg-green-100 px-2 py-0.5 rounded-full">Normale</span>
                                                        </div>
                                                    </div>
                                                    <div className="bg-purple-50 p-2 rounded-lg border-l-4 border-purple-500">
                                                        <div className="flex items-center">
                                                            <div className="font-medium text-purple-800 text-sm">Mettre à jour les antivirus</div>
<span className="ml-auto text-xs text-purple-700 bg-purple-100 px-2 py-0.5 rounded-full">Moyenne</span>
                                                        </div>
                                                    </div>
                                                    <div className="bg-blue-50 p-2 rounded-lg border-l-4 border-blue-500">
                                                        <div className="flex items-center">
                                                            <div className="font-medium text-blue-800 text-sm">Configurer la surveillance réseau</div>
                                                            <span className="ml-auto text-xs text-blue-700 bg-blue-100 px-2 py-0.5 rounded-full">Haute</span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </ChartContainer>
                                    </motion.div>

                                    <motion.div variants={itemVariants} className="mt-6">
                                        <InfoSection title="Recommandations d'amélioration" icon={HelpCircle} collapsible={true}>
                                            <div className="p-4">
                                                <div className="space-y-4">
                                                    <div className="bg-white rounded-lg border border-gray-200 p-4">
                                                        <div className="flex items-start">
                                                            <div className="bg-red-100 p-2 rounded-full mr-3">
                                                                <AlertTriangle size={16} className="text-red-600" />
                                                            </div>
                                                            <div>
                                                                <h4 className="font-medium text-gray-800 mb-1">Risque élevé: Authentification insuffisante</h4>
                                                                <p className="text-sm text-gray-600 mb-2">
                                                                    Les mécanismes d'authentification actuels ne sont pas suffisamment robustes pour protéger les accès critiques.
                                                                </p>
                                                                <div className="bg-gray-50 p-3 rounded-lg text-sm">
                                                                    <h5 className="font-medium text-gray-700 mb-1">Recommandations:</h5>
                                                                    <ul className="list-disc list-inside text-gray-600 space-y-1">
                                                                        <li>Mettre en place l'authentification multi-facteurs pour tous les accès administrateurs</li>
                                                                        <li>Renforcer la politique de mots de passe</li>
                                                                        <li>Implémenter un système de détection des tentatives d'intrusion</li>
                                                                    </ul>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>

                                                    <div className="bg-white rounded-lg border border-gray-200 p-4">
                                                        <div className="flex items-start">
                                                            <div className="bg-amber-100 p-2 rounded-full mr-3">
                                                                <Shield size={16} className="text-amber-600" />
                                                            </div>
                                                            <div>
                                                                <h4 className="font-medium text-gray-800 mb-1">Risque moyen: Protection des données insuffisante</h4>
                                                                <p className="text-sm text-gray-600 mb-2">
                                                                    La protection des données sensibles ne répond pas aux exigences réglementaires et aux bonnes pratiques.
                                                                </p>
                                                                <div className="bg-gray-50 p-3 rounded-lg text-sm">
                                                                    <h5 className="font-medium text-gray-700 mb-1">Recommandations:</h5>
                                                                    <ul className="list-disc list-inside text-gray-600 space-y-1">
                                                                        <li>Mettre en œuvre le chiffrement des données sensibles au repos et en transit</li>
                                                                        <li>Effectuer un audit complet des accès aux données sensibles</li>
                                                                        <li>Établir une procédure de gestion des incidents liés aux données</li>
                                                                    </ul>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>

                                                    <div className="bg-white rounded-lg border border-gray-200 p-4">
                                                        <div className="flex items-start">
                                                            <div className="bg-green-100 p-2 rounded-full mr-3">
                                                                <Eye size={16} className="text-green-600" />
                                                            </div>
                                                            <div>
                                                                <h4 className="font-medium text-gray-800 mb-1">Risque faible: Visibilité du système insuffisante</h4>
                                                                <p className="text-sm text-gray-600 mb-2">
                                                                    La surveillance et la journalisation des événements système sont insuffisantes pour une détection efficace des incidents.
                                                                </p>
                                                                <div className="bg-gray-50 p-3 rounded-lg text-sm">
                                                                    <h5 className="font-medium text-gray-700 mb-1">Recommandations:</h5>
                                                                    <ul className="list-disc list-inside text-gray-600 space-y-1">
                                                                        <li>Améliorer les mécanismes de journalisation des événements système</li>
                                                                        <li>Mettre en place un système centralisé de gestion des journaux</li>
                                                                        <li>Établir des alertes automatiques pour les comportements anormaux</li>
                                                                    </ul>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>

                                                <div className="flex justify-end mt-4">
                                                    <button className="px-4 py-2 bg-indigo-600 text-white rounded-lg text-sm flex items-center">
                                                        <Download size={16} className="mr-2" />
                                                        Télécharger le rapport complet
                                                    </button>
                                                </div>
                                            </div>
                                        </InfoSection>
                                    </motion.div>
                                </motion.div>
                            )}
                        </div>

                        {/* Enhanced Footer Information */}
                        <motion.div
                            initial={{ opacity: 0 }}
                            animate={{ opacity: 1 }}
                            transition={{ delay: 0.5 }}
                            className="mt-8 text-xs text-gray-500 flex flex-col sm:flex-row items-center gap-4 border-t border-gray-200 pt-4"
                        >
                            <div className="flex items-center gap-x-4 gap-y-1 flex-wrap">
                                <span className="flex items-center bg-gray-100 px-3 py-1.5 rounded-full">
                                    <Calendar size={14} className="mr-1.5 text-blue-500" />
                                    <span className="font-medium text-gray-600">{t('dashboard.footer.organization')} :</span>
                                    <span className="ml-1.5">{context?.organizationName || currentAnalysis?.companyName || 'N/A'}</span>
                                </span>

                                <span className="flex items-center bg-gray-100 px-3 py-1.5 rounded-full">
                                    <Calendar size={14} className="mr-1.5 text-green-500" />
                                    <span className="font-medium text-gray-600">{t('dashboard.footer.analysisDate')} :</span>
                                    <span className="ml-1.5">{context?.analysisDate || 'N/A'}</span>
                                </span>

                                <span className="flex items-center bg-gray-100 px-3 py-1.5 rounded-full">
                                    <RefreshCw size={14} className="mr-1.5 text-purple-500" />
                                    <span className="font-medium text-gray-600">{t('dashboard.footer.lastModified')}:</span>
                                    <span className="ml-1.5">{currentAnalysis?.updatedAt ? new Date(currentAnalysis.updatedAt).toLocaleString(i18n.language === 'en' ? 'en-US' : 'fr-FR') : 'N/A'}</span>
                                </span>
                            </div>
                            <div className="flex-grow"></div>
                        </motion.div>
                    </motion.div>
                </AnimatePresence>
            )}

            {/* Enhanced Message when no analysis is selected */}
            {!isWorkshopDataLoading && !currentAnalysis && (
                <motion.div
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="text-center py-16 bg-gradient-to-b from-blue-50 to-gray-50 rounded-xl border border-blue-100 mt-6 px-4"
                >
                    <motion.div
                        initial={{ scale: 0.9, opacity: 0 }}
                        animate={{ scale: 1, opacity: 1 }}
                        transition={{ delay: 0.2, type: "spring", stiffness: 100 }}
                        className="bg-white p-6 rounded-xl shadow-lg inline-block mb-6"
                    >
                        <Info size={64} className="mx-auto text-blue-400" />
                    </motion.div>
                    <h3 className="text-xl text-gray-700 font-medium mb-2">Aucune analyse sélectionnée</h3>
                    <p className="text-gray-500 max-w-md mx-auto mb-6">
                        Veuillez choisir une analyse existante dans le sélecteur ci-dessus ou créez-en une nouvelle pour commencer.
                    </p>
                    <div className="flex flex-col sm:flex-row gap-3 justify-center">
                        <motion.button
                            whileHover={{ scale: 1.03 }}
                            whileTap={{ scale: 0.97 }}
                            className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors shadow-md flex items-center justify-center"
                        >
                            Créer une nouvelle analyse
                        </motion.button>
                        <motion.button
                            whileHover={{ scale: 1.03 }}
                            whileTap={{ scale: 0.97 }}
                            className="px-6 py-3 bg-white text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors shadow-sm flex items-center justify-center"
                        >
                            Voir un exemple
                        </motion.button>
                    </div>
                </motion.div>
            )}
        </motion.div>
    );
};

// Custom hook for dashboard analytics
const useDashboardAnalytics = () => {
    const [isInitialized, setIsInitialized] = useState(false);

    useEffect(() => {
        // Mock analytics initialization
        if (!isInitialized) {
            console.log("Dashboard analytics initialized");
            setIsInitialized(true);
        }

        // Cleanup
        return () => {
            console.log("Dashboard analytics cleanup");
        };
    }, [isInitialized]);

    return {
        trackEvent: (eventName, data) => {
            // Mock analytics tracking
            console.log(`Analytics event: ${eventName}`, data);
        }
    };
};

export default Dashboard;