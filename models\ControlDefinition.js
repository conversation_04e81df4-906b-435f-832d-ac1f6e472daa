const mongoose = require('mongoose');

const ControlDefinitionSchema = new mongoose.Schema({
    name: {
        type: String,
        required: [true, 'Please provide a control name'],
        trim: true,
        unique: true // Assuming control names should be unique
    },
    description: {
        type: String,
        trim: true,
        default: ''
    },
    category: { // Example: Technical, Organizational, Physical, Legal
        type: String,
        trim: true,
        index: true // Index if you often filter/sort by category
    },
    // Add other relevant fields as needed, e.g.:
    // type: { 
    //     type: String, 
    //     enum: ['Preventive', 'Detective', 'Corrective'], 
    //     default: 'Preventive' 
    // },
    // frameworkSource: { // e.g., 'ISO27001', 'NIST', 'Custom'
    //     type: String,
    //     trim: true
    // },
    is_predefined: {
        type: Boolean,
        default: true // Useful to distinguish between standard and custom/AI controls?
    }
}, {
    timestamps: true // Automatically add createdAt and updatedAt fields
});

// Optional: Add index for searching by name
ControlDefinitionSchema.index({ name: 'text', description: 'text' });

module.exports = mongoose.model('ControlDefinition', ControlDefinitionSchema); 