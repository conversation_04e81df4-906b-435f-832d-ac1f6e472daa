// src/controllers/authController.js
import { 
    hashPassword, 
    verifyPassword, 
    generateTokenPair,
    verifyRefreshToken,
    generateAccessToken
  } from '../services/authService';
  
  // Import du modèle utilisateur (à adapter selon votre structure)
  import { findUserByEmail, updateUserLastLogin } from '../models/userModel';
  
  /**
   * Gère l'authentification des utilisateurs (login)
   * @param {Object} req - Requête Express
   * @param {Object} res - Réponse Express
   */
  export const login = async (req, res) => {
    try {
      const { email, password } = req.body;
      
      // Validation des champs
      if (!email || !password) {
        return res.status(400).json({
          success: false,
          message: 'Email et mot de passe requis'
        });
      }
      
      // Chercher l'utilisateur par email
      const user = await findUserByEmail(email);
      
      if (!user) {
        return res.status(401).json({
          success: false,
          message: 'Identifiants invalides'
        });
      }
      
      // Vérifier si l'utilisateur est actif
      if (user.status === 'inactive') {
        return res.status(403).json({
          success: false,
          message: 'Compte désactivé. Contactez votre administrateur'
        });
      }
      
      // Vérifier le mot de passe
      const isPasswordValid = await verifyPassword(password, user.password);
      
      if (!isPasswordValid) {
        return res.status(401).json({
          success: false,
          message: 'Identifiants invalides'
        });
      }
      
      // Générer tokens JWT
      const { accessToken, refreshToken } = generateTokenPair(user);
      
      // Mettre à jour la date de dernière connexion de l'utilisateur
      await updateUserLastLogin(user.id);
      
      // Renvoyer les tokens et informations utilisateur
      res.status(200).json({
        success: true,
        data: {
          user: {
            id: user.id,
            name: user.name,
            email: user.email,
            role: user.role,
            companyId: user.companyId,
            companyName: user.companyName
          },
          tokens: {
            accessToken,
            refreshToken
          }
        }
      });
    } catch (error) {
      console.error('Login error:', error);
      res.status(500).json({
        success: false,
        message: 'Erreur lors de la connexion',
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      });
    }
  };
  
  /**
   * Rafraîchit l'access token en utilisant un refresh token
   * @param {Object} req - Requête Express
   * @param {Object} res - Réponse Express
   */
  export const refreshToken = async (req, res) => {
    try {
      const { refreshToken } = req.body;
      
      if (!refreshToken) {
        return res.status(400).json({
          success: false,
          message: 'Refresh token requis'
        });
      }
      
      // Vérifier le refresh token
      const decoded = verifyRefreshToken(refreshToken);
      
      if (!decoded) {
        return res.status(401).json({
          success: false,
          message: 'Refresh token invalide ou expiré'
        });
      }
      
      // Récupérer l'utilisateur associé au token
      // Dans une application réelle, vous devriez vérifier contre une liste blanche/noire de refresh tokens
      const userId = decoded.userId;
      const user = await getUserById(userId);
      
      if (!user || user.status === 'inactive') {
        return res.status(401).json({
          success: false,
          message: 'Utilisateur introuvable ou inactif'
        });
      }
      
      // Générer un nouveau access token
      const newAccessToken = generateAccessToken(user);
      
      // Répondre avec le nouveau token
      res.status(200).json({
        success: true,
        data: {
          accessToken: newAccessToken
        }
      });
    } catch (error) {
      console.error('Refresh token error:', error);
      res.status(500).json({
        success: false,
        message: 'Erreur lors du rafraîchissement du token',
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      });
    }
  };
  
  /**
   * Déconnecte l'utilisateur en invalidant son refresh token
   * @param {Object} req - Requête Express
   * @param {Object} res - Réponse Express
   */
  export const logout = async (req, res) => {
    try {
      const { refreshToken } = req.body;
      
      // Dans une application réelle, vous devriez ajouter le refresh token à une liste noire
      // ou le supprimer de la base de données
      
      res.status(200).json({
        success: true,
        message: 'Déconnexion réussie'
      });
    } catch (error) {
      console.error('Logout error:', error);
      res.status(500).json({
        success: false,
        message: 'Erreur lors de la déconnexion',
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      });
    }
  };
  
  /**
   * Récupère l'utilisateur par ID (fonction utilitaire)
   * @param {string} id - ID de l'utilisateur
   * @returns {Promise<Object>} - Objet utilisateur
   */
  const getUserById = async (id) => {
    // À implémenter selon votre système de stockage
    // Exemple simplifié:
    return {
      id,
      // autres propriétés...
    };
  };