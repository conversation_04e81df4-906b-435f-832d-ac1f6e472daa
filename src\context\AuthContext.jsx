// src/context/EnhancedAuthContext.jsx
import React, { createContext, useContext, useState, useEffect, useCallback, useRef } from 'react';
import { authService } from '../services/apiServices';
import {
  saveAuthData,
  getUserInfo,
  clearAuthData,
  isAuthenticated as checkIsAuthenticated,
  getRefreshToken,
  getAccessToken,
  decodeToken
} from '../utils/tokenStorage';
import { initTokenRefreshMonitor, getTokenExpiry } from '../utils/tokenRefresh';

// Create Authentication Context
const AuthContext = createContext(null);

/**
 * Authentication Provider Component
 * Enhanced with better state management and session handling
 */
export const AuthProvider = ({ children }) => {
  // State for authenticated user
  const [user, setUser] = useState(null);
  // State to track loading status
  const [isLoading, setIsLoading] = useState(true);
  // State for any authentication errors
  const [error, setError] = useState(null);
  // State to track token refresh process
  const [isRefreshing, setIsRefreshing] = useState(false);
  // State to track authentication check completion
  const [authCheckComplete, setAuthCheckComplete] = useState(false);

  /**
   * Check if token needs refresh - DISABLED
   * @returns {boolean} Always returns false as refresh is disabled
   */
  const shouldRefreshToken = useCallback(() => {
    // Token refresh is disabled, so always return false
    return false;
  }, []);

  /**
   * Refresh the access token - DISABLED
   * @returns {Promise<boolean>} Always returns false as refresh is disabled
   */
  const refreshToken = useCallback(async () => {
    console.log('Token refresh has been disabled. Please log in again.');
    clearAuthData();
    setUser(null);
    return false;
  }, []);

  // Listen for silent refresh events
  useEffect(() => {
    const handleSilentRefresh = (event) => {
      // This event listener handles silent refresh events without triggering UI changes
      // No need to reload the page or redirect
      console.log('Silent token refresh event received:', event.detail.success ? 'success' : 'failure');
    };

    window.addEventListener('auth:silentRefresh', handleSilentRefresh);

    return () => {
      window.removeEventListener('auth:silentRefresh', handleSilentRefresh);
    };
  }, []);

  // Check authentication status on initial load
  useEffect(() => {
    const checkAuth = async () => {
      setIsLoading(true);
      try {
        // Check if user info exists locally
        const storedUser = getUserInfo();

        if (storedUser && checkIsAuthenticated()) {
          // If token needs refresh, refresh it silently before proceeding
          if (shouldRefreshToken()) {
            try {
              // Use a completely silent refresh approach
              const refreshSuccess = await refreshToken();
              if (!refreshSuccess) {
                // Handle silently - just update state without redirects
                setUser(null);
                setAuthCheckComplete(true);
                setIsLoading(false);
                return;
              }
            } catch (refreshError) {
              // Handle refresh error silently without page reload
              console.error('Silent token refresh error during auth check');
              clearAuthData();
              setUser(null);
              setAuthCheckComplete(true);
              setIsLoading(false);
              return;
            }
          }

          try {
            // Always use silentAuth=true to prevent page reloads
            const response = await authService.getCurrentUser(true);

            if (response.success) {
              setUser(response.data.user);
            } else {
              // Invalid or expired token - handle silently
              clearAuthData();
              setUser(null);
            }
          } catch (err) {
            // API call error - handle silently without page reloads
            console.error('Silent auth check API error occurred');
            clearAuthData();
            setUser(null);
          }
        } else {
          // No stored user - handle silently
          clearAuthData();
          setUser(null);
        }
      } catch (error) {
        // Handle any errors silently without page reloads
        console.error('Auth check error occurred');
        clearAuthData();
        setUser(null);
        setError('Authentication error occurred');
      } finally {
        setIsLoading(false);
        setAuthCheckComplete(true);
      }
    };

    checkAuth();
  }, [shouldRefreshToken, refreshToken]);

  // Set up token refresh monitor using our utility
  const tokenRefreshMonitorRef = useRef(null);

  // Token refresh monitoring has been disabled
  useEffect(() => {
    // No automatic token refresh
    console.log('Automatic token refresh has been disabled');
  }, [user]);

  /**
   * Login function - Enhanced with better error handling
   * @param {string} email - User email
   * @param {string} password - User password
   * @returns {Promise<Object>} - Login result
   */
  const login = async (email, password) => {
    setIsLoading(true);
    setError(null);

    try {
      // Removed login attempt logging for security
      const response = await authService.login(email, password);

      // Removed login response logging for security

      if (response.success) {
        // Save authentication data
        const authData = {
          tokens: response.data.tokens,
          user: response.data.user
        };

        saveAuthData(authData);
        setUser(response.data.user);
        return { success: true, user: response.data.user };
      } else {
        throw new Error(response.message || 'Login failed');
      }
    } catch (error) {
      // Log without revealing credentials
      console.error('Login error occurred');
      const errorMessage = error.data?.message || error.message || 'Login error';
      setError(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Logout function - Enhanced to handle API errors gracefully
   */
  const logout = async () => {
    setIsLoading(true);

    try {
      // Only call API if authenticated (prevents unnecessary calls)
      if (checkIsAuthenticated()) {
        try {
          await authService.logout();
        } catch (error) {
          // Log without revealing token details
          console.error('Logout API error occurred');
        }
      }
    } finally {
      // Always clear local auth data
      clearAuthData();
      setUser(null);
      setIsLoading(false);
    }
  };

  /**
   * Check if user has specific role(s)
   * @param {string|string[]} roles - Role(s) to check
   * @returns {boolean} - True if user has at least one of the specified roles
   */
  const checkRole = useCallback((roles) => {
    if (!user) return false;

    if (Array.isArray(roles)) {
      return roles.includes(user.role);
    }

    return user.role === roles;
  }, [user]);

  // Context value - computed properties for common role checks
  const contextValue = {
    user,
    isLoading,
    error,
    login,
    logout,
    refreshToken,
    isAuthenticated: !!user,
    authCheckComplete,
    checkRole,
    isSuperAdmin: user?.role === 'superadmin',
    isAdmin: ['superadmin', 'admin'].includes(user?.role),
    isProvider: user?.role === 'admin',
    isAnalyst: user?.role === 'simpleuser',
    isSimpleUser: user?.role === 'simpleuser',
    companyId: user?.companyId || null,
    companyName: user?.companyName || null
  };

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  );
};

/**
 * Hook to use the auth context
 * @returns {Object} - Auth context value
 */
export const useAuth = () => {
  const context = useContext(AuthContext);

  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }

  return context;
};