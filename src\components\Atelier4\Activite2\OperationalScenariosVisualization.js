// src/components/Atelier4/Activite2/OperationalScenariosVisualization.js
import React, { useState, useCallback, useMemo } from 'react';
import ReactFlow, {
  MiniMap,
  Controls,
  Background,
  useNodesState,
  useEdgesState,
  addEdge,
  Panel,
} from 'reactflow';
import 'reactflow/dist/style.css';
import './OperationalScenariosVisualization.css';

// Custom node types
const ScenarioNode = ({ data, selected }) => {
  const getSeverityColor = (severity) => {
    const colors = {
      low: '#28a745',
      medium: '#ffc107',
      high: '#fd7e14',
      critical: '#dc3545'
    };
    return colors[severity] || '#6c757d';
  };

  return (
    <div className={`scenario-node ${selected ? 'selected' : ''}`}>
      <div className="node-header">
        <div className="node-title">{data.name}</div>
        <div 
          className="severity-indicator"
          style={{ backgroundColor: getSeverityColor(data.severity) }}
        >
          {data.severity}
        </div>
      </div>
      
      <div className="node-content">
        <div className="node-info">
          <span className="info-item">
            <i className="fas fa-clock"></i>
            {data.timeline}
          </span>
          <span className="info-item">
            <i className="fas fa-user-graduate"></i>
            {data.requiredSkills}
          </span>
        </div>
        
        <div className="node-stats">
          <div className="stat">
            <span className="stat-label">Probabilité</span>
            <span className="stat-value">{data.likelihood}</span>
          </div>
          <div className="stat">
            <span className="stat-label">Étapes</span>
            <span className="stat-value">{data.steps?.length || 0}</span>
          </div>
        </div>
      </div>
      
      {data.source === 'ai_generated' && (
        <div className="ai-badge">
          <i className="fas fa-robot"></i>
          IA
        </div>
      )}
    </div>
  );
};

const StepNode = ({ data, selected }) => {
  return (
    <div className={`step-node ${selected ? 'selected' : ''}`}>
      <div className="step-header">
        <span className="step-number">{data.stepNumber}</span>
        <span className="step-name">{data.name}</span>
      </div>
      
      <div className="step-content">
        <div className="step-duration">{data.duration}</div>
        
        {data.techniques && data.techniques.length > 0 && (
          <div className="techniques">
            {data.techniques.slice(0, 2).map((technique, i) => (
              <span key={i} className="technique-tag">{technique}</span>
            ))}
            {data.techniques.length > 2 && (
              <span className="more-count">+{data.techniques.length - 2}</span>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

const AttackPathNode = ({ data, selected }) => {
  return (
    <div className={`attack-path-node ${selected ? 'selected' : ''}`}>
      <div className="path-header">
        <i className="fas fa-route"></i>
        <span className="path-name">{data.name}</span>
      </div>
      
      <div className="path-info">
        <span className="scenario-count">
          {data.scenarioCount} scénario{data.scenarioCount !== 1 ? 's' : ''}
        </span>
      </div>
    </div>
  );
};

const nodeTypes = {
  scenario: ScenarioNode,
  step: StepNode,
  attackPath: AttackPathNode,
};

const OperationalScenariosVisualization = ({
  scenarios,
  attackPaths,
  selectedScenarios,
  onScenarioSelect
}) => {
  const [nodes, setNodes, onNodesChange] = useNodesState([]);
  const [edges, setEdges, onEdgesChange] = useEdgesState([]);
  const [viewMode, setViewMode] = useState('scenarios'); // 'scenarios', 'steps', 'paths'
  const [selectedScenario, setSelectedScenario] = useState(null);

  // Generate nodes and edges based on view mode
  const { generatedNodes, generatedEdges } = useMemo(() => {
    let newNodes = [];
    let newEdges = [];

    if (viewMode === 'scenarios') {
      // Group scenarios by attack path
      const pathGroups = {};
      scenarios.forEach(scenario => {
        const pathId = scenario.attackPathId;
        if (!pathGroups[pathId]) {
          pathGroups[pathId] = [];
        }
        pathGroups[pathId].push(scenario);
      });

      let yOffset = 0;
      Object.entries(pathGroups).forEach(([pathId, pathScenarios]) => {
        const attackPath = attackPaths.find(p => p.id === pathId);
        
        // Add attack path node
        newNodes.push({
          id: `path-${pathId}`,
          type: 'attackPath',
          position: { x: 50, y: yOffset },
          data: {
            name: attackPath?.name || `Chemin ${pathId}`,
            scenarioCount: pathScenarios.length
          }
        });

        // Add scenario nodes for this path
        pathScenarios.forEach((scenario, index) => {
          newNodes.push({
            id: scenario.id,
            type: 'scenario',
            position: { x: 350 + (index % 3) * 300, y: yOffset + Math.floor(index / 3) * 200 },
            data: scenario,
            selected: selectedScenarios.includes(scenario.id)
          });

          // Connect scenario to attack path
          newEdges.push({
            id: `path-${pathId}-${scenario.id}`,
            source: `path-${pathId}`,
            target: scenario.id,
            type: 'smoothstep',
            style: { stroke: '#007bff', strokeWidth: 2 }
          });
        });

        yOffset += Math.max(200, Math.ceil(pathScenarios.length / 3) * 200) + 100;
      });

    } else if (viewMode === 'steps' && selectedScenario) {
      // Show detailed steps for selected scenario
      const scenario = scenarios.find(s => s.id === selectedScenario);
      if (scenario && scenario.steps) {
        // Add scenario node
        newNodes.push({
          id: scenario.id,
          type: 'scenario',
          position: { x: 50, y: 100 },
          data: scenario
        });

        // Add step nodes
        scenario.steps.forEach((step, index) => {
          const stepId = `${scenario.id}-step-${index}`;
          newNodes.push({
            id: stepId,
            type: 'step',
            position: { x: 400 + (index % 4) * 250, y: 50 + Math.floor(index / 4) * 150 },
            data: {
              ...step,
              stepNumber: index + 1
            }
          });

          // Connect scenario to step
          newEdges.push({
            id: `${scenario.id}-${stepId}`,
            source: scenario.id,
            target: stepId,
            type: 'smoothstep',
            style: { stroke: '#28a745', strokeWidth: 2 }
          });

          // Connect steps in sequence
          if (index > 0) {
            const prevStepId = `${scenario.id}-step-${index - 1}`;
            newEdges.push({
              id: `${prevStepId}-${stepId}`,
              source: prevStepId,
              target: stepId,
              type: 'smoothstep',
              style: { stroke: '#6c757d', strokeWidth: 1, strokeDasharray: '5,5' }
            });
          }
        });
      }

    } else if (viewMode === 'paths') {
      // Show attack paths and their relationships
      const pathPositions = {};
      attackPaths.forEach((path, index) => {
        const x = 100 + (index % 3) * 300;
        const y = 100 + Math.floor(index / 3) * 200;
        pathPositions[path.id] = { x, y };

        newNodes.push({
          id: path.id,
          type: 'attackPath',
          position: { x, y },
          data: {
            name: path.name,
            scenarioCount: scenarios.filter(s => s.attackPathId === path.id).length
          }
        });
      });

      // Add edges between related paths (if they share sources or targets)
      attackPaths.forEach(path1 => {
        attackPaths.forEach(path2 => {
          if (path1.id !== path2.id && 
              path1.sourceRiskName === path2.sourceRiskName) {
            newEdges.push({
              id: `${path1.id}-${path2.id}`,
              source: path1.id,
              target: path2.id,
              type: 'smoothstep',
              style: { stroke: '#ffc107', strokeWidth: 1, strokeDasharray: '3,3' }
            });
          }
        });
      });
    }

    return { generatedNodes: newNodes, generatedEdges: newEdges };
  }, [scenarios, attackPaths, viewMode, selectedScenario, selectedScenarios]);

  // Update nodes and edges when generated data changes
  React.useEffect(() => {
    setNodes(generatedNodes);
    setEdges(generatedEdges);
  }, [generatedNodes, generatedEdges, setNodes, setEdges]);

  const onConnect = useCallback(
    (params) => setEdges((eds) => addEdge(params, eds)),
    [setEdges]
  );

  const onNodeClick = useCallback((event, node) => {
    if (node.type === 'scenario') {
      if (viewMode === 'scenarios') {
        // Toggle selection
        const newSelection = selectedScenarios.includes(node.id)
          ? selectedScenarios.filter(id => id !== node.id)
          : [...selectedScenarios, node.id];
        onScenarioSelect(newSelection);
      } else if (viewMode === 'steps') {
        // Already showing steps for this scenario
      }
    }
  }, [viewMode, selectedScenarios, onScenarioSelect]);

  const handleViewModeChange = (mode) => {
    setViewMode(mode);
    if (mode === 'steps' && scenarios.length > 0) {
      setSelectedScenario(scenarios[0].id);
    }
  };

  const handleScenarioChange = (scenarioId) => {
    setSelectedScenario(scenarioId);
  };

  if (scenarios.length === 0) {
    return (
      <div className="visualization-container">
        <div className="empty-visualization">
          <i className="fas fa-project-diagram"></i>
          <h3>Aucun scénario à visualiser</h3>
          <p>Générez des scénarios opérationnels pour voir leur visualisation.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="visualization-container">
      <div className="visualization-controls">
        <div className="view-mode-selector">
          <button
            className={`mode-btn ${viewMode === 'scenarios' ? 'active' : ''}`}
            onClick={() => handleViewModeChange('scenarios')}
          >
            <i className="fas fa-list"></i>
            Vue scénarios
          </button>
          <button
            className={`mode-btn ${viewMode === 'steps' ? 'active' : ''}`}
            onClick={() => handleViewModeChange('steps')}
            disabled={scenarios.length === 0}
          >
            <i className="fas fa-tasks"></i>
            Vue étapes
          </button>
          <button
            className={`mode-btn ${viewMode === 'paths' ? 'active' : ''}`}
            onClick={() => handleViewModeChange('paths')}
          >
            <i className="fas fa-route"></i>
            Vue chemins
          </button>
        </div>

        {viewMode === 'steps' && (
          <div className="scenario-selector">
            <label>Scénario à détailler:</label>
            <select
              value={selectedScenario || ''}
              onChange={(e) => handleScenarioChange(e.target.value)}
            >
              {scenarios.map(scenario => (
                <option key={scenario.id} value={scenario.id}>
                  {scenario.name}
                </option>
              ))}
            </select>
          </div>
        )}

        <div className="visualization-stats">
          <span className="stat">
            <i className="fas fa-eye"></i>
            Mode: {viewMode === 'scenarios' ? 'Scénarios' : viewMode === 'steps' ? 'Étapes' : 'Chemins'}
          </span>
          <span className="stat">
            <i className="fas fa-nodes"></i>
            {nodes.length} nœud{nodes.length !== 1 ? 's' : ''}
          </span>
        </div>
      </div>

      <div className="flow-container">
        <ReactFlow
          nodes={nodes}
          edges={edges}
          onNodesChange={onNodesChange}
          onEdgesChange={onEdgesChange}
          onConnect={onConnect}
          onNodeClick={onNodeClick}
          nodeTypes={nodeTypes}
          fitView
          attributionPosition="bottom-left"
        >
          <Controls />
          <MiniMap 
            nodeColor={(node) => {
              if (node.type === 'scenario') return '#007bff';
              if (node.type === 'step') return '#28a745';
              if (node.type === 'attackPath') return '#ffc107';
              return '#6c757d';
            }}
            nodeStrokeWidth={3}
            zoomable
            pannable
          />
          <Background color="#aaa" gap={16} />
          
          <Panel position="top-right">
            <div className="legend">
              <h4>Légende</h4>
              <div className="legend-items">
                <div className="legend-item">
                  <div className="legend-color scenario-color"></div>
                  <span>Scénarios</span>
                </div>
                <div className="legend-item">
                  <div className="legend-color step-color"></div>
                  <span>Étapes</span>
                </div>
                <div className="legend-item">
                  <div className="legend-color path-color"></div>
                  <span>Chemins d'attaque</span>
                </div>
              </div>
            </div>
          </Panel>
        </ReactFlow>
      </div>
    </div>
  );
};

export default OperationalScenariosVisualization;