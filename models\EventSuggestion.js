const mongoose = require('mongoose');

const EventSuggestionSchema = new mongoose.Schema({
  name: {
    type: String,
    required: [true, 'Please provide a suggestion name'],
    trim: true
  },
  description: {
    type: String,
    trim: true,
    default: ''
  },
  securityPillar: {
    type: String,
    required: [true, 'Please specify a security pillar'],
    enum: ['confidentialite', 'integrite', 'disponibilite', 'tracabilite', 'preuve', 'Auditabilite']
  },
  severity: {
    type: String,
    required: [true, 'Please specify a severity level'],
    enum: ['minor', 'moderate', 'major', 'critical', 'catastrophic'],
    default: 'moderate'
  },
  isDefault: {
    type: Boolean,
    default: false
  },
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    default: null
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  },
  impacts: {
    type: [String],
    enum: ['missions_services', 'humain_materiel_environnemental', 'gouvernance', 'financier', 'juridique', 'image_confiance'],
    default: []
  }
}, {
  timestamps: true
});

// Create indexes for faster queries
EventSuggestionSchema.index({ securityPillar: 1 });
EventSuggestionSchema.index({ name: 'text', description: 'text' });

module.exports = mongoose.model('EventSuggestion', EventSuggestionSchema);
