// routes/atelier3Routes.js
const express = require('express');
const {
  getStakeholders,
  saveStakeholders,
  getStakeholder,
  createStakeholder,
  updateStakeholder,
  deleteStakeholder,
  updateThresholds,
  getThresholds
} = require('../controllers/atelier3Controller');

const {
  getEcosystemMeasures,
  saveEcosystemMeasures
} = require('../controllers/ecosystemMeasuresController');

const { protect } = require('../middleware/authMiddleware');

const router = express.Router();

// All routes need authentication
router.use(protect);

// Stakeholders routes
router.route('/analyses/:analysisId/atelier3/stakeholders')
  .get(protect, getStakeholders)
  .post(protect, createStakeholder);

// Batch save stakeholders
router.route('/analyses/:analysisId/atelier3/stakeholders/batch')
  .post(protect, saveStakeholders);

// Thresholds routes - IMPORTANT: This must come before the :stakeholderId route to avoid conflicts
router.route('/analyses/:analysisId/atelier3/stakeholders/thresholds')
  .get(protect, getThresholds)
  .post(protect, updateThresholds);

// Individual stakeholder routes
router.route('/analyses/:analysisId/atelier3/stakeholders/:stakeholderId')
  .get(protect, getStakeholder)
  .put(protect, updateStakeholder)
  .delete(protect, deleteStakeholder);

// Ecosystem measures routes
router.route('/analyses/:analysisId/ecosystem-measures')
  .get(protect, getEcosystemMeasures)
  .post(protect, saveEcosystemMeasures);

module.exports = router;
