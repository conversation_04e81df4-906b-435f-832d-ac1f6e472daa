// src/components/Atelier3/Activite3/Activite3.js
import React, { useState, useEffect } from 'react';
import { Target, Shield, AlertTriangle, Info, ChevronDown, ChevronUp, Search, Download, Eye, X, Route } from 'lucide-react';
import { ReactFlowProvider } from 'reactflow';
import { useTranslation } from 'react-i18next';
import AttackPathVisualization from './AttackPathVisualization';
import { useAnalysis } from '../../../context/AnalysisContext';
import { showLoadingToast, dismissToast, showErrorToast } from '../../../utils/toastUtils';
import attackPathsService from '../../../services/attackPathsService';

const Activite3 = () => {
  const { t } = useTranslation();
  // State for data
  const [attackPaths, setAttackPaths] = useState([]);
  const [stakeholders, setStakeholders] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [sortField, setSortField] = useState('sourceRiskName');
  const [sortDirection, setSortDirection] = useState('asc');
  const [expandedRows, setExpandedRows] = useState({});
  const [selectedAttackPath, setSelectedAttackPath] = useState(null);
  const [showVisualization, setShowVisualization] = useState(false);
  const [selectedAttackPathId, setSelectedAttackPathId] = useState(null);

  // Get analysis context
  const {
    currentAnalysis,
    getStakeholders
  } = useAnalysis();

  // Load data on component mount
  useEffect(() => {
    const fetchData = async () => {
      if (currentAnalysis?.id) {
        setIsLoading(true);
        setError(null);
        const toastId = showLoadingToast(t('workshop3.activity3.loading'));

        try {
          // Load attack paths
          const response = await attackPathsService.getAttackPaths(currentAnalysis.id);
          if (response.success && Array.isArray(response.data)) {
            setAttackPaths(response.data);
          } else {
            console.error('Invalid attack paths data:', response);
            setAttackPaths([]);
          }

          // Load stakeholders
          try {
            const stakeholdersResponse = await getStakeholders(currentAnalysis.id);
            if (stakeholdersResponse && stakeholdersResponse.success && Array.isArray(stakeholdersResponse.data)) {
              setStakeholders(stakeholdersResponse.data);
            } else {
              console.log('Stakeholders data is not valid:', stakeholdersResponse);
              setStakeholders([]);
            }
          } catch (stakeholderError) {
            console.error('Error loading stakeholders:', stakeholderError);
            setStakeholders([]);
          }

          // Dismiss the loading toast without showing a success toast
          dismissToast(toastId);
        } catch (error) {
          console.error('Error loading data:', error);
          setError(t('workshop3.activity3.errors.loadingError'));
          // Dismiss the loading toast and show error toast
          dismissToast(toastId);
          showErrorToast(t('workshop3.activity3.errors.loadingAttackPathsError'));
        } finally {
          setIsLoading(false);
        }
      }
    };

    fetchData();
  }, [currentAnalysis?.id]); // Only depend on analysis ID

  // Handle sort
  const handleSort = (field) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  // Toggle row expansion
  const toggleRowExpansion = (id) => {
    setExpandedRows(prev => ({
      ...prev,
      [id]: !prev[id]
    }));
  };

  // Filter and sort attack paths
  const filteredAttackPaths = attackPaths
    .filter(path => {
      if (!searchTerm) return true;

      const searchLower = searchTerm.toLowerCase();
      return (
        (path.sourceRiskName && path.sourceRiskName.toLowerCase().includes(searchLower)) ||
        (path.objectifVise && path.objectifVise.toLowerCase().includes(searchLower)) ||
        (path.dreadedEventName && path.dreadedEventName.toLowerCase().includes(searchLower)) ||
        (path.businessValueName && path.businessValueName.toLowerCase().includes(searchLower))
      );
    })
    .sort((a, b) => {
      let aValue = a[sortField] || '';
      let bValue = b[sortField] || '';

      if (typeof aValue === 'string') aValue = aValue.toLowerCase();
      if (typeof bValue === 'string') bValue = bValue.toLowerCase();

      if (aValue < bValue) return sortDirection === 'asc' ? -1 : 1;
      if (aValue > bValue) return sortDirection === 'asc' ? 1 : -1;
      return 0;
    });

  // Export to CSV
  const exportToCSV = () => {
    // Create CSV header
    const header = [
      t('workshop3.activity3.table.riskSource'),
      t('workshop3.activity3.table.targetObjective'),
      t('workshop3.activity3.table.dreadedEvent'),
      t('workshop3.activity3.table.businessValue'),
      t('workshop3.activity3.table.stakeholders')
    ].join(',');

    // Create CSV rows
    const rows = filteredAttackPaths.map(path => {
      const stakeholderNames = path.stakeholders
        ? path.stakeholders.map(s => s.name).join('; ')
        : '';

      return [
        `"${path.sourceRiskName || ''}"`,
        `"${path.objectifVise || ''}"`,
        `"${path.dreadedEventName || ''}"`,
        `"${path.businessValueName || ''}"`,
        `"${stakeholderNames}"`
      ].join(',');
    });

    // Combine header and rows
    const csv = [header, ...rows].join('\n');

    // Create download link
    const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.setAttribute('href', url);
    link.setAttribute('download', `${t('workshop3.activity3.export.filename')}-${new Date().toISOString().split('T')[0]}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  // Render sort indicator
  const renderSortIndicator = (field) => {
    if (sortField !== field) return null;
    return sortDirection === 'asc' ? <ChevronUp size={14} /> : <ChevronDown size={14} />;
  };

  return (
    <div className="space-y-6">
      {/* Enhanced Header - Matching Scénarios Stratégiques style */}
      <div className="bg-white rounded-xl shadow-sm border border-slate-100 p-4 mb-6">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          {/* Left Side: Breadcrumb & Title */}
          <div>
            <div className="flex items-center text-sm text-slate-500 mb-2">
              <span className="hover:text-blue-600 transition-colors">{t('workshop3.title')}</span>
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mx-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
              <span className="text-blue-600 font-medium">{t('workshop3.activity3.breadcrumb')}</span>
            </div>
            <h1 className="text-2xl sm:text-3xl font-bold text-slate-800 flex items-center">
              <Route size={28} className="mr-3 text-blue-600" />
              {t('workshop3.activity3.title')}
            </h1>
          </div>

          {/* Right Side: Action Buttons */}
          <div className="flex items-center space-x-3">
            {/* Help Button */}
            <button
              className="text-sm font-medium bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 flex items-center shadow-sm transition duration-200"
              onClick={() => {/* TODO: Add help modal */}}
            >
              <Info size={16} className="mr-2" />
              {t('common.help')}
            </button>

            {/* Export button */}
            <button
              onClick={exportToCSV}
              className="text-sm font-medium bg-green-100 text-green-700 px-4 py-2 rounded-lg hover:bg-green-200 flex items-center shadow-sm transition duration-200"
            >
              <Download size={16} className="mr-2" />
              {t('workshop3.activity3.export.button')}
            </button>
          </div>
        </div>

        {/* Description */}
        <div className="mt-4 p-4 bg-blue-50 rounded-lg border border-blue-100">
          <p className="text-sm text-blue-700">
            {t('workshop3.activity3.description')}
          </p>
        </div>
      </div>

      {/* Loading state */}
      {isLoading ? (
        <div className="flex justify-center items-center h-64 bg-slate-50 rounded-xl shadow-sm">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-slate-600">{t('workshop3.activity3.loadingMessage')}</p>
          </div>
        </div>
      ) : error ? (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-center">
            <AlertTriangle className="h-5 w-5 text-red-400 mr-2" />
            <div>
              <h3 className="font-bold text-red-800">{t('workshop3.activity3.errors.loadingErrorTitle')}</h3>
              <p className="text-red-700 mt-1">{error}</p>
              <button
                className="mt-2 px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition duration-200 text-sm"
                onClick={() => window.location.reload()}
              >
                {t('common.retry')}
              </button>
            </div>
          </div>
        </div>
      ) : (
        <div className="bg-white rounded-xl shadow-md overflow-hidden">
          {/* Header */}
          <div className="p-5 border-b border-slate-200 bg-gradient-to-r from-blue-50 to-white">
            <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
              <div>
                <h2 className="text-xl font-semibold text-slate-800 flex items-center">
                  <Target size={20} className="mr-2 text-blue-600" />
                  {t('workshop3.activity3.title')}
                </h2>
                <p className="text-sm text-slate-500 mt-1">
                  {t('workshop3.activity3.subtitle')}
                </p>
              </div>
              <div className="flex items-center space-x-3">
                {/* Search input */}
                <div className="relative">
                  <input
                    type="text"
                    placeholder={t('workshop3.activity3.searchPlaceholder')}
                    className="pl-9 pr-4 py-2 border border-slate-300 rounded-lg focus:ring-blue-500 focus:border-blue-500 w-full md:w-64 shadow-sm"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                  <Search size={16} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400" />
                </div>

                {/* Export button */}
                <button
                  onClick={exportToCSV}
                  className="flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition duration-200 shadow-sm"
                >
                  <Download size={16} className="mr-2" />
                  {t('workshop3.activity3.export.csv')}
                </button>

                {/* Visualize selected button */}
                <button
                  onClick={() => {
                    if (selectedAttackPathId) {
                      // Find the selected attack path
                      const selectedPath = attackPaths.find(path => path.id === selectedAttackPathId);
                      if (selectedPath) {
                        // Create a copy of the path with the first stakeholder
                        const pathWithSelectedStakeholder = {
                          ...selectedPath,
                          selectedStakeholder: selectedPath.stakeholders && selectedPath.stakeholders.length > 0
                            ? selectedPath.stakeholders[0]
                            : null
                        };
                        setSelectedAttackPath(pathWithSelectedStakeholder);
                        setShowVisualization(true);
                      }
                    } else {
                      showErrorToast(t('workshop3.activity3.errors.selectAttackPath'));
                    }
                  }}
                  disabled={!selectedAttackPathId}
                  className={`flex items-center px-4 py-2 rounded-lg text-white shadow-sm transition duration-200 ${!selectedAttackPathId ? 'bg-blue-400 cursor-not-allowed' : 'bg-blue-600 hover:bg-blue-700'}`}
                >
                  <Eye size={16} className="mr-2" />
                  {t('workshop3.activity3.visualizeSelection')}
                </button>
              </div>
            </div>
          </div>

          {/* Table */}
          {filteredAttackPaths.length === 0 ? (
            <div className="p-8 text-center">
              <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-blue-50 text-blue-500 mb-4">
                <Info size={24} />
              </div>
              <h3 className="text-lg font-medium text-slate-900 mb-2">{t('workshop3.activity3.empty.noAttackPaths')}</h3>
              <p className="text-slate-500 max-w-md mx-auto">
                {t('workshop3.activity3.empty.noAttackPathsMessage')}
              </p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-slate-200">
                <thead className="bg-slate-50">
                  <tr>
                    <th
                      scope="col"
                      className="px-4 py-3 text-left text-xs font-medium text-slate-700 uppercase tracking-wider"
                    >
                      <div className="flex items-center">
                        {t('workshop3.activity3.table.selection')}
                      </div>
                    </th>
                    <th
                      scope="col"
                      className="px-4 py-3 text-left text-xs font-medium text-slate-700 uppercase tracking-wider"
                    >
                      <div className="flex items-center">
                        {t('workshop3.activity3.table.reference')}
                      </div>
                    </th>
                    <th
                      scope="col"
                      className="px-4 py-3 text-left text-xs font-medium text-slate-700 uppercase tracking-wider cursor-pointer"
                      onClick={() => handleSort('sourceRiskName')}
                    >
                      <div className="flex items-center">
                        {t('workshop3.activity3.table.riskSource')}
                        {renderSortIndicator('sourceRiskName')}
                      </div>
                    </th>
                    <th
                      scope="col"
                      className="px-4 py-3 text-left text-xs font-medium text-slate-700 uppercase tracking-wider cursor-pointer"
                      onClick={() => handleSort('objectifVise')}
                    >
                      <div className="flex items-center">
                        {t('workshop3.activity3.table.targetObjective')}
                        {renderSortIndicator('objectifVise')}
                      </div>
                    </th>
                    <th
                      scope="col"
                      className="px-4 py-3 text-left text-xs font-medium text-slate-700 uppercase tracking-wider cursor-pointer"
                      onClick={() => handleSort('dreadedEventName')}
                    >
                      <div className="flex items-center">
                        {t('workshop3.activity3.table.dreadedEvent')}
                        {renderSortIndicator('dreadedEventName')}
                      </div>
                    </th>
                    <th
                      scope="col"
                      className="px-4 py-3 text-left text-xs font-medium text-slate-700 uppercase tracking-wider cursor-pointer"
                      onClick={() => handleSort('businessValueName')}
                    >
                      <div className="flex items-center">
                        {t('workshop3.activity3.table.businessValue')}
                        {renderSortIndicator('businessValueName')}
                      </div>
                    </th>
                    <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-slate-700 uppercase tracking-wider">
                      {t('workshop3.activity3.table.stakeholders')}
                    </th>
                    <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-slate-700 uppercase tracking-wider">
                      {t('workshop3.activity3.table.actions')}
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-slate-200">
                  {filteredAttackPaths.flatMap((path) => {
                    // If there are no stakeholders, create one row
                    if (!path.stakeholders || path.stakeholders.length === 0) {
                      return (
                        <tr
                          key={`${path.id}-none`}
                          className={`hover:bg-slate-50 ${expandedRows[path.id] ? 'bg-slate-50' : ''} ${selectedAttackPathId === path.id ? 'border-l-4 border-l-blue-500 bg-blue-50 shadow-lg ring-2 ring-blue-200' : ''}`}
                        >
                          <td className="px-4 py-4 whitespace-nowrap">
                            <input
                              type="radio"
                              name="selectedAttackPath"
                              className="h-5 w-5 text-blue-600 border-slate-300 focus:ring-blue-500 focus:ring-2"
                              checked={selectedAttackPathId === path.id}
                              onChange={() => {
                                setSelectedAttackPathId(path.id);
                                // Automatically show visualization when a path is selected
                                const pathWithSelectedStakeholder = {
                                  ...path,
                                  selectedStakeholder: path.stakeholders && path.stakeholders.length > 0
                                    ? path.stakeholders[0]
                                    : null
                                };
                                setSelectedAttackPath(pathWithSelectedStakeholder);
                                setShowVisualization(true);
                              }}
                            />
                          </td>
                          <td className="px-4 py-4 whitespace-nowrap">
                            <div className={`inline-flex items-center px-2.5 py-1 rounded-md text-xs font-medium ${selectedAttackPathId === path.id ? 'bg-blue-600 text-white shadow-lg' : 'bg-blue-100 text-blue-800'}`}>
                              {path.referenceCode || `CA${String(filteredAttackPaths.indexOf(path) + 1).padStart(2, '0')}`}
                              {selectedAttackPathId === path.id && (
                                <span className="ml-1 text-white">●</span>
                              )}
                            </div>
                          </td>
                          <td className="px-4 py-4 whitespace-nowrap">
                            <div className="text-sm font-medium text-slate-900">{path.sourceRiskName}</div>
                          </td>
                          <td className="px-4 py-4 whitespace-nowrap">
                            <div className="text-sm text-slate-700">{path.objectifVise}</div>
                          </td>
                          <td className="px-4 py-4">
                            <div className="text-sm text-slate-700 flex items-start">
                              <AlertTriangle size={16} className="text-amber-500 mr-2 mt-0.5 flex-shrink-0" />
                              <span>{path.dreadedEventName}</span>
                            </div>
                          </td>
                          <td className="px-4 py-4">
                            <div className="text-sm text-slate-700">{path.businessValueName}</div>
                          </td>
                          <td className="px-4 py-4">
                            <span className="text-slate-400 italic text-xs">{t('workshop3.activity3.table.none')}</span>
                          </td>
                          <td className="px-4 py-4 whitespace-nowrap text-sm font-medium">
                            <div className="flex justify-end space-x-2">
                              <button
                                onClick={() => {
                                  // Create a copy of the path with null selectedStakeholder
                                  const pathWithSelectedStakeholder = {
                                    ...path,
                                    selectedStakeholder: null
                                  };
                                  setSelectedAttackPath(pathWithSelectedStakeholder);
                                  setShowVisualization(true);
                                }}
                                className="text-blue-600 hover:text-blue-900 flex items-center"
                                title={t('workshop3.activity3.visualizeAttackPath')}
                              >
                                <Eye size={16} className="mr-1" />
                                {t('workshop3.activity3.visualize')}
                              </button>
                              <button
                                onClick={() => toggleRowExpansion(path.id)}
                                className="text-blue-600 hover:text-blue-900 flex items-center"
                              >
                                {expandedRows[path.id] ? (
                                  <>
                                    <ChevronUp size={16} className="mr-1" />
                                    {t('workshop3.activity3.collapse')}
                                  </>
                                ) : (
                                  <>
                                    <ChevronDown size={16} className="mr-1" />
                                    {t('workshop3.activity3.details')}
                                  </>
                                )}
                              </button>
                            </div>
                          </td>
                        </tr>
                      );
                    }

                    // If there are stakeholders, create one row per stakeholder
                    return path.stakeholders.map((stakeholder, index) => (
                      <tr
                        key={`${path.id}-${stakeholder.id}`}
                        className={`hover:bg-slate-50 ${expandedRows[path.id] ? 'bg-slate-50' : ''} ${index > 0 ? 'border-t-0' : ''} ${selectedAttackPathId === path.id ? 'border-l-4 border-l-blue-500 bg-blue-50 shadow-lg ring-2 ring-blue-200' : ''}`}
                      >
                        <td className="px-4 py-4 whitespace-nowrap">
                          {index === 0 && (
                            <input
                              type="radio"
                              name="selectedAttackPath"
                              className="h-5 w-5 text-blue-600 border-slate-300 focus:ring-blue-500 focus:ring-2"
                              checked={selectedAttackPathId === path.id}
                              onChange={() => {
                                setSelectedAttackPathId(path.id);
                                // Automatically show visualization when a path is selected
                                const pathWithSelectedStakeholder = {
                                  ...path,
                                  selectedStakeholder: path.stakeholders && path.stakeholders.length > 0
                                    ? path.stakeholders[0]
                                    : null
                                };
                                setSelectedAttackPath(pathWithSelectedStakeholder);
                                setShowVisualization(true);
                              }}
                            />
                          )}
                        </td>
                        <td className="px-4 py-4 whitespace-nowrap">
                          <div className={`inline-flex items-center px-2.5 py-1 rounded-md text-xs font-medium ${selectedAttackPathId === path.id ? 'bg-blue-600 text-white shadow-lg' : 'bg-blue-100 text-blue-800'}`}>
                            {path.referenceCode || `CA${String(filteredAttackPaths.indexOf(path) + 1).padStart(2, '0')}`}
                            {selectedAttackPathId === path.id && (
                              <span className="ml-1 text-white">●</span>
                            )}
                          </div>
                        </td>
                        <td className="px-4 py-4 whitespace-nowrap">
                          <div className="text-sm font-medium text-slate-900">{path.sourceRiskName}</div>
                        </td>
                        <td className="px-4 py-4 whitespace-nowrap">
                          <div className="text-sm text-slate-700">{path.objectifVise}</div>
                        </td>
                        <td className="px-4 py-4">
                          <div className="text-sm text-slate-700 flex items-start">
                            <AlertTriangle size={16} className="text-amber-500 mr-2 mt-0.5 flex-shrink-0" />
                            <span>{path.dreadedEventName}</span>
                          </div>
                        </td>
                        <td className="px-4 py-4">
                          <div className="text-sm text-slate-700">{path.businessValueName}</div>
                        </td>
                        <td className="px-4 py-4">
                          <div
                            className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-teal-100 text-teal-800"
                          >
                            {stakeholder.name}
                          </div>
                        </td>
                        <td className="px-4 py-4 whitespace-nowrap text-sm font-medium">
                          <div className="flex justify-end space-x-2">
                            <button
                              onClick={() => {
                                setSelectedAttackPath(path);
                                setShowVisualization(true);
                              }}
                              className="text-blue-600 hover:text-blue-900 flex items-center"
                              title={t('workshop3.activity3.visualizeAttackPath')}
                            >
                              <Eye size={16} className="mr-1" />
                              {t('workshop3.activity3.visualize')}
                            </button>
                            <button
                              onClick={() => toggleRowExpansion(path.id)}
                              className="text-blue-600 hover:text-blue-900 flex items-center"
                            >
                              {expandedRows[path.id] ? (
                                <>
                                  <ChevronUp size={16} className="mr-1" />
                                  {t('workshop3.activity3.collapse')}
                                </>
                              ) : (
                                <>
                                  <ChevronDown size={16} className="mr-1" />
                                  {t('workshop3.activity3.details')}
                                </>
                              )}
                            </button>
                          </div>
                        </td>
                      </tr>
                    ));
                  })}
                </tbody>
              </table>
            </div>
          )}
        </div>
      )}

      {/* Expanded details for selected rows */}
      {Object.keys(expandedRows).filter(id => expandedRows[id]).map(id => {
        const path = filteredAttackPaths.find(p => p.id === id);
        if (!path) return null;
        return (
          <div key={id} className="bg-slate-50 p-6 mt-4 rounded-lg shadow-md">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="bg-white p-4 rounded-lg shadow-sm">
                <h4 className="font-medium text-slate-800 mb-2">{t('workshop3.activity3.detailsModal.attackPathDetails')}</h4>
                <div className="space-y-2 text-sm">
                  <p><span className="font-medium">{t('workshop3.activity3.table.riskSource')}:</span> {path.sourceRiskName}</p>
                  <p><span className="font-medium">{t('workshop3.activity3.table.targetObjective')}:</span> {path.objectifVise}</p>
                  <p><span className="font-medium">{t('workshop3.activity3.table.dreadedEvent')}:</span> {path.dreadedEventName}</p>
                  <p><span className="font-medium">{t('workshop3.activity3.table.businessValue')}:</span> {path.businessValueName}</p>
                </div>
              </div>

              <div className="bg-white p-4 rounded-lg shadow-sm">
                <h4 className="font-medium text-slate-800 mb-2">{t('workshop3.activity3.detailsModal.concernedStakeholder')}</h4>
                {path.stakeholders && path.stakeholders.length > 0 ? (
                  <ul className="space-y-2">
                    {/* Only show the stakeholder that was clicked */}
                    <li className="flex items-center">
                      <span className="inline-flex items-center justify-center w-6 h-6 rounded-full bg-teal-100 text-teal-800 mr-2">
                        {path.stakeholders[0].name.charAt(0)}
                      </span>
                      <span>{path.stakeholders[0].name}</span>
                    </li>
                  </ul>
                ) : (
                  <p className="text-slate-500 italic">{t('workshop3.activity3.detailsModal.noAssociatedStakeholder')}</p>
                )}

                <div className="mt-4">
                  <button
                    onClick={() => {
                      // Create a copy of the path with the selected stakeholder
                      const pathWithSelectedStakeholder = {
                        ...path,
                        selectedStakeholder: path.stakeholders && path.stakeholders.length > 0 ? path.stakeholders[0] : null
                      };
                      setSelectedAttackPath(pathWithSelectedStakeholder);
                      setShowVisualization(true);
                    }}
                    className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition duration-200 shadow-sm text-sm"
                  >
                    <Eye size={16} className="mr-2" />
                    {t('workshop3.activity3.visualizeAttackPath')}
                  </button>
                </div>
              </div>
            </div>
          </div>
        );
      })}

      {/* Attack Path Visualization Section */}
      {showVisualization && selectedAttackPath && (
        <div className="mt-8">
          <ReactFlowProvider>
            <AttackPathVisualization
              attackPath={selectedAttackPath}
              title={t('workshop3.activity3.visualization.title', {
                sourceRisk: selectedAttackPath.sourceRiskName,
                dreadedEvent: selectedAttackPath.dreadedEventName
              })}
              onSave={(updatedAttackPath) => {
                // Update the attack path in the state with the saved diagram state
                setAttackPaths(prevPaths =>
                  prevPaths.map(path =>
                    path.id === updatedAttackPath.id ? updatedAttackPath : path
                  )
                );
                setSelectedAttackPath(updatedAttackPath);
              }}
            />
          </ReactFlowProvider>
          <div className="flex justify-center mt-4">
            <button
              onClick={() => setShowVisualization(false)}
              className="px-4 py-2 bg-slate-200 text-slate-800 rounded-lg hover:bg-slate-300 transition duration-200 flex items-center"
            >
              <X size={16} className="mr-2" />
              {t('workshop3.activity3.closeVisualization')}
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default Activite3;
