// controllers/ecosystemMeasuresController.js
const AnalysisComponent = require('../models/AnalysisComponent');
const { userHasAnalysisAccess } = require('../utils/accessControl');
const ActivityLog = require('../models/ActivityLog');
const { v4: uuidv4 } = require('uuid');

// Helper function to get IP address from request
const getIpAddress = (req) => {
  return req.ip ||
    (req.headers['x-forwarded-for'] || '').split(',')[0].trim() ||
    req.socket.remoteAddress ||
    'unknown';
};

// Helper function to create activity log
const logActivity = async (req, actionType, resourceType, resourceId, details = {}) => {
  try {
    if (!req.user) return;

    await ActivityLog.create({
      userId: req.user.id,
      userName: req.user.name,
      companyId: req.user.companyId,
      companyName: req.user.companyName,
      actionType,
      resourceType,
      resourceId,
      ipAddress: getIpAddress(req),
      userAgent: req.headers['user-agent'] || '',
      details: {
        ...details,
        timestamp: new Date()
      }
    });
  } catch (error) {
    console.error('Activity log error:', error);
  }
};

/**
 * Get ecosystem measures for an analysis
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getEcosystemMeasures = async (req, res) => {
  try {
    const { analysisId } = req.params;

    // Check if user has access to this analysis
    if (!await userHasAnalysisAccess(req.user.id, analysisId)) {
      return res.status(403).json({ success: false, message: 'Accès non autorisé à cette analyse' });
    }

    // Find the ecosystem measures component for this analysis
    const component = await AnalysisComponent.findOne({
      analysisId,
      componentType: 'ecosystem-measures'
    });

    if (!component) {
      return res.status(200).json({ success: true, data: [] });
    }

    return res.status(200).json({
      success: true,
      data: component.data.measures || []
    });
  } catch (error) {
    console.error('Error getting ecosystem measures:', error);
    return res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération des mesures de sécurité',
      error: error.message
    });
  }
};

/**
 * Save ecosystem measures for an analysis
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.saveEcosystemMeasures = async (req, res) => {
  try {
    const { analysisId } = req.params;
    const { measures } = req.body;

    // Check if user has access to this analysis
    if (!await userHasAnalysisAccess(req.user.id, analysisId)) {
      return res.status(403).json({ success: false, message: 'Accès non autorisé à cette analyse' });
    }

    // Validate measures
    if (!Array.isArray(measures)) {
      return res.status(400).json({
        success: false,
        message: 'Les mesures doivent être un tableau'
      });
    }

    // Process measures to ensure they have IDs and timestamps
    const processedMeasures = measures.map(measure => ({
      ...measure,
      id: measure.id || `measure-${uuidv4()}`,
      updatedAt: new Date().toISOString()
    }));

    // Save to database using AnalysisComponent model
    const component = await AnalysisComponent.createNewVersion(
      analysisId,
      'ecosystem-measures',
      { measures: processedMeasures },
      req.user.id
    );

    // Log the activity
    try {
      await logActivity(
        req,
        'COMPONENT_UPDATE',
        'analysis_component',
        component._id,
        { analysisId, componentType: 'ecosystem-measures', action: 'save_measures' }
      );
    } catch (logError) {
      console.error('Activity log error (non-critical):', logError);
      // Continue execution even if logging fails
    }

    return res.status(200).json({
      success: true,
      message: 'Mesures de sécurité sauvegardées avec succès',
      data: processedMeasures
    });
  } catch (error) {
    console.error('Error saving ecosystem measures:', error);
    return res.status(500).json({
      success: false,
      message: 'Erreur lors de la sauvegarde des mesures de sécurité',
      error: error.message
    });
  }
};
