// src/components/Atelier4/Activite1/GuideModal.js
import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { X, ChevronLeft, ChevronRight, Search, LogIn, MapPin, Target, BookOpen, AlertCircle } from 'lucide-react';

const GuideModal = ({ onClose }) => {
  const [currentStep, setCurrentStep] = useState(0);

  const guideSteps = [
    {
      title: "Introduction aux graphes d'attaque",
      icon: BookOpen,
      content: (
        <div className="space-y-4">
          <p className="text-gray-700">
            Un scénario opérationnel peut être représenté sous la forme d'un <strong>graphe d'attaque</strong> 
            permettant de visualiser les modes opératoires planifiés par l'attaquant pour atteindre son objectif.
          </p>
          <p className="text-gray-700">
            Le graphe d'attaque se présente sous la forme d'un <strong>enchaînement d'actions élémentaires</strong> 
            sur des biens supports. Plusieurs modes opératoires peuvent être réalisés par la source de risque 
            pour atteindre son objectif visé.
          </p>
          <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
            <div className="flex items-start">
              <AlertCircle size={20} className="text-blue-600 mr-3 mt-0.5 flex-shrink-0" />
              <div>
                <h4 className="font-medium text-blue-800 mb-1">Objectif</h4>
                <p className="text-blue-700 text-sm">
                  Décomposer chaque chemin d'attaque identifié en Atelier 3 en actions élémentaires 
                  organisées selon le modèle de séquence d'attaque à 4 phases.
                </p>
              </div>
            </div>
          </div>
        </div>
      )
    },
    {
      title: "Modèle de séquence d'attaque",
      icon: Target,
      content: (
        <div className="space-y-4">
          <p className="text-gray-700">
            Les scénarios opérationnels sont structurés selon une séquence d'attaque type 
            articulée autour de <strong>4 phases principales</strong> :
          </p>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
              <div className="flex items-center mb-2">
                <Search size={20} className="text-blue-600 mr-2" />
                <h4 className="font-medium text-blue-800">CONNAITRE</h4>
              </div>
              <p className="text-blue-700 text-sm">
                Activités de ciblage, reconnaissance et découverte externes pour préparer l'attaque.
              </p>
            </div>
            <div className="bg-orange-50 p-4 rounded-lg border border-orange-200">
              <div className="flex items-center mb-2">
                <LogIn size={20} className="text-orange-600 mr-2" />
                <h4 className="font-medium text-orange-800">RENTRER</h4>
              </div>
              <p className="text-orange-700 text-sm">
                Activités d'intrusion numérique ou physique dans le système d'information cible.
              </p>
            </div>
            <div className="bg-purple-50 p-4 rounded-lg border border-purple-200">
              <div className="flex items-center mb-2">
                <MapPin size={20} className="text-purple-600 mr-2" />
                <h4 className="font-medium text-purple-800">TROUVER</h4>
              </div>
              <p className="text-purple-700 text-sm">
                Reconnaissance interne, latéralisation et élévation de privilèges pour localiser les cibles.
              </p>
            </div>
            <div className="bg-red-50 p-4 rounded-lg border border-red-200">
              <div className="flex items-center mb-2">
                <Target size={20} className="text-red-600 mr-2" />
                <h4 className="font-medium text-red-800">EXPLOITER</h4>
              </div>
              <p className="text-red-700 text-sm">
                Exploitation des données et biens supports trouvés pour atteindre l'objectif visé.
              </p>
            </div>
          </div>
        </div>
      )
    },
    {
      title: "Phase CONNAITRE - Reconnaissance",
      icon: Search,
      content: (
        <div className="space-y-4">
          <p className="text-gray-700">
            La phase <strong>CONNAITRE</strong> regroupe toutes les activités de reconnaissance 
            et de collecte d'informations menées par l'attaquant avant l'attaque.
          </p>
          <div className="bg-gray-50 p-4 rounded-lg">
            <h4 className="font-medium text-gray-800 mb-3">Catégories d'actions élémentaires :</h4>
            <ul className="space-y-2 text-sm text-gray-700">
              <li className="flex items-start">
                <span className="w-2 h-2 bg-blue-500 rounded-full mr-3 mt-2 flex-shrink-0"></span>
                <div>
                  <strong>Recrutement d'une source :</strong> Corruption de personnel interne ou externe
                </div>
              </li>
              <li className="flex items-start">
                <span className="w-2 h-2 bg-blue-500 rounded-full mr-3 mt-2 flex-shrink-0"></span>
                <div>
                  <strong>Reconnaissance externe :</strong> Collecte d'informations via sources ouvertes, 
                  réseaux sociaux, ingénierie sociale
                </div>
              </li>
            </ul>
          </div>
          <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
            <h4 className="font-medium text-blue-800 mb-2">Exemples d'actions :</h4>
            <ul className="text-sm text-blue-700 space-y-1">
              <li>• Reconnaissance externe sources ouvertes</li>
              <li>• Reconnaissance externe avancée</li>
              <li>• Recrutement d'une source interne</li>
              <li>• Collecte d'informations via réseaux sociaux</li>
            </ul>
          </div>
        </div>
      )
    },
    {
      title: "Phase RENTRER - Intrusion",
      icon: LogIn,
      content: (
        <div className="space-y-4">
          <p className="text-gray-700">
            La phase <strong>RENTRER</strong> correspond aux activités d'intrusion initiale 
            dans le système d'information cible ou de son écosystème.
          </p>
          <div className="bg-gray-50 p-4 rounded-lg">
            <h4 className="font-medium text-gray-800 mb-3">Catégories d'actions élémentaires :</h4>
            <ul className="space-y-2 text-sm text-gray-700">
              <li className="flex items-start">
                <span className="w-2 h-2 bg-orange-500 rounded-full mr-3 mt-2 flex-shrink-0"></span>
                <div>
                  <strong>Intrusion depuis Internet :</strong> Attaques directes, hameçonnage, points d'eau
                </div>
              </li>
              <li className="flex items-start">
                <span className="w-2 h-2 bg-orange-500 rounded-full mr-3 mt-2 flex-shrink-0"></span>
                <div>
                  <strong>Intrusion physique :</strong> Accès physique, clés USB piégées, corruption matériel
                </div>
              </li>
            </ul>
          </div>
          <div className="bg-orange-50 p-4 rounded-lg border border-orange-200">
            <h4 className="font-medium text-orange-800 mb-2">Exemples d'actions :</h4>
            <ul className="text-sm text-orange-700 space-y-1">
              <li>• Intrusion via mail de hameçonnage</li>
              <li>• Intrusion via canal d'accès préexistant</li>
              <li>• Intrusion via site web compromis (point d'eau)</li>
              <li>• Clé USB piégée connectée sur poste</li>
            </ul>
          </div>
        </div>
      )
    },
    {
      title: "Phase TROUVER - Reconnaissance interne",
      icon: MapPin,
      content: (
        <div className="space-y-4">
          <p className="text-gray-700">
            La phase <strong>TROUVER</strong> regroupe les activités de reconnaissance interne, 
            de latéralisation et d'élévation de privilèges.
          </p>
          <div className="bg-gray-50 p-4 rounded-lg">
            <h4 className="font-medium text-gray-800 mb-3">Catégories d'actions élémentaires :</h4>
            <ul className="space-y-2 text-sm text-gray-700">
              <li className="flex items-start">
                <span className="w-2 h-2 bg-purple-500 rounded-full mr-3 mt-2 flex-shrink-0"></span>
                <div>
                  <strong>Reconnaissance interne :</strong> Cartographie réseaux, identification vulnérabilités
                </div>
              </li>
              <li className="flex items-start">
                <span className="w-2 h-2 bg-purple-500 rounded-full mr-3 mt-2 flex-shrink-0"></span>
                <div>
                  <strong>Latéralisation :</strong> Progression dans le SI, élévation de privilèges
                </div>
              </li>
            </ul>
          </div>
          <div className="bg-purple-50 p-4 rounded-lg border border-purple-200">
            <h4 className="font-medium text-purple-800 mb-2">Exemples d'actions :</h4>
            <ul className="text-sm text-purple-700 space-y-1">
              <li>• Reconnaissance interne réseaux bureautique</li>
              <li>• Latéralisation vers réseaux LAN et R&D</li>
              <li>• Élévation de privilèges administrateur</li>
              <li>• Cartographie des services critiques</li>
            </ul>
          </div>
        </div>
      )
    },
    {
      title: "Phase EXPLOITER - Réalisation objectif",
      icon: Target,
      content: (
        <div className="space-y-4">
          <p className="text-gray-700">
            La phase <strong>EXPLOITER</strong> correspond à la réalisation de l'objectif visé 
            par la source de risque selon différents modes opératoires.
          </p>
          <div className="bg-gray-50 p-4 rounded-lg">
            <h4 className="font-medium text-gray-800 mb-3">Types d'exploitation selon l'objectif :</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm">
              <div>
                <strong className="text-blue-600">Espionnage :</strong>
                <ul className="text-gray-700 mt-1 space-y-1">
                  <li>• Exfiltration de données</li>
                  <li>• Écoute passive</li>
                  <li>• Interception signaux</li>
                </ul>
              </div>
              <div>
                <strong className="text-red-600">Sabotage :</strong>
                <ul className="text-gray-700 mt-1 space-y-1">
                  <li>• Attaque DDoS</li>
                  <li>• Altération données</li>
                  <li>• Brouillage systèmes</li>
                </ul>
              </div>
              <div>
                <strong className="text-green-600">Lucratif :</strong>
                <ul className="text-gray-700 mt-1 space-y-1">
                  <li>• Rançongiciel</li>
                  <li>• Fraude financière</li>
                  <li>• Détournement usage</li>
                </ul>
              </div>
              <div>
                <strong className="text-purple-600">Influence :</strong>
                <ul className="text-gray-700 mt-1 space-y-1">
                  <li>• Défiguration sites</li>
                  <li>• Propagande</li>
                  <li>• Atteinte réputation</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      )
    },
    {
      title: "Évaluation des actions élémentaires",
      icon: AlertCircle,
      content: (
        <div className="space-y-4">
          <p className="text-gray-700">
            Chaque action élémentaire doit être évaluée selon deux critères principaux 
            pour estimer la vraisemblance du scénario.
          </p>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="bg-yellow-50 p-4 rounded-lg border border-yellow-200">
              <h4 className="font-medium text-yellow-800 mb-2">Difficulté technique</h4>
              <p className="text-yellow-700 text-sm mb-3">
                Ressources que l'attaquant devra engager pour mener son action.
              </p>
              <ul className="text-xs text-yellow-700 space-y-1">
                <li>• <strong>Négligeable :</strong> Ressources déjà disponibles</li>
                <li>• <strong>Faible :</strong> Ressources limitées</li>
                <li>• <strong>Modérée :</strong> Ressources significatives</li>
                <li>• <strong>Élevée :</strong> Ressources importantes</li>
                <li>• <strong>Très élevée :</strong> Ressources très importantes</li>
              </ul>
            </div>
            <div className="bg-green-50 p-4 rounded-lg border border-green-200">
              <h4 className="font-medium text-green-800 mb-2">Probabilité de succès</h4>
              <p className="text-green-700 text-sm mb-3">
                Chances que l'attaquant réussisse son action.
              </p>
              <ul className="text-xs text-green-700 space-y-1">
                <li>• <strong>Très faible :</strong> &lt; 3%</li>
                <li>• <strong>Faible :</strong> &lt; 20%</li>
                <li>• <strong>Significative :</strong> &gt; 20%</li>
                <li>• <strong>Très élevée :</strong> &gt; 60%</li>
                <li>• <strong>Quasi-certaine :</strong> &gt; 90%</li>
              </ul>
            </div>
          </div>
          <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
            <div className="flex items-start">
              <AlertCircle size={20} className="text-blue-600 mr-3 mt-0.5 flex-shrink-0" />
              <div>
                <h4 className="font-medium text-blue-800 mb-1">Note importante</h4>
                <p className="text-blue-700 text-sm">
                  Ces évaluations permettront de calculer la vraisemblance globale du scénario 
                  et d'identifier les actions les plus critiques à protéger.
                </p>
              </div>
            </div>
          </div>
        </div>
      )
    }
  ];

  const nextStep = () => {
    if (currentStep < guideSteps.length - 1) {
      setCurrentStep(currentStep + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const currentGuide = guideSteps[currentStep];
  const IconComponent = currentGuide.icon;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        className="bg-white rounded-xl shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden"
      >
        {/* Header */}
        <div className="flex justify-between items-center p-6 border-b border-gray-200">
          <div className="flex items-center">
            <div className="bg-blue-100 p-2 rounded-lg mr-3">
              <IconComponent size={24} className="text-blue-600" />
            </div>
            <div>
              <h2 className="text-xl font-semibold text-gray-800">{currentGuide.title}</h2>
              <p className="text-sm text-gray-500">
                Étape {currentStep + 1} sur {guideSteps.length}
              </p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 p-1 rounded-full hover:bg-gray-100"
          >
            <X size={20} />
          </button>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[calc(90vh-200px)]">
          {currentGuide.content}
        </div>

        {/* Footer */}
        <div className="flex justify-between items-center p-6 border-t border-gray-200 bg-gray-50">
          <button
            onClick={prevStep}
            disabled={currentStep === 0}
            className={`flex items-center px-4 py-2 rounded-lg ${
              currentStep === 0
                ? 'text-gray-400 cursor-not-allowed'
                : 'text-gray-700 bg-white border border-gray-300 hover:bg-gray-50'
            }`}
          >
            <ChevronLeft size={16} className="mr-1" />
            Précédent
          </button>

          <div className="flex space-x-2">
            {guideSteps.map((_, index) => (
              <button
                key={index}
                onClick={() => setCurrentStep(index)}
                className={`w-3 h-3 rounded-full ${
                  index === currentStep ? 'bg-blue-600' : 'bg-gray-300'
                }`}
              />
            ))}
          </div>

          <button
            onClick={currentStep === guideSteps.length - 1 ? onClose : nextStep}
            className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
          >
            {currentStep === guideSteps.length - 1 ? 'Terminer' : 'Suivant'}
            {currentStep < guideSteps.length - 1 && <ChevronRight size={16} className="ml-1" />}
          </button>
        </div>
      </motion.div>
    </div>
  );
};

export default GuideModal;