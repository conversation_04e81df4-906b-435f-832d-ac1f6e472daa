const express = require('express');
const {
    getAllControlDefinitions,
    // Import other controller functions if you add them (e.g., createControlDefinition)
} = require('../controllers/controlDefinitionController');

const { protect } = require('../middleware/authMiddleware'); // Assuming you want to protect this route

const router = express.Router();

// All routes in this file can use the protect middleware if needed
router.use(protect);

// Route to get all control definitions
router.route('/definitions')
    .get(getAllControlDefinitions);

// Add routes for other operations if needed
// router.route('/')
//    .post(createControlDefinition); // Example for creating

module.exports = router; 