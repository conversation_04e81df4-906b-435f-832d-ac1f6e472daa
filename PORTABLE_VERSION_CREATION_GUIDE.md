# EBIOS RM - Portable Version Creation Guide

## 📋 Overview

This guide documents the complete process for creating a portable Docker-based deployment package for the EBIOS RM application. This process can be replicated for future versions of the project.

## 🎯 Goal

Create a self-contained, portable package that includes:
- ✅ Complete frontend application (latest build)
- ✅ Complete backend application (with all dependencies)
- ✅ Database with all data exported
- ✅ Docker configuration for easy deployment
- ✅ Cross-platform installation scripts

## 📁 Project Structure

```
EBIOS RM Project/
├── APP/
│   └── Atelier 1/
│       ├── backend/                 # Backend source code
│       └── my-ebios-app/            # Frontend source code
│           ├── src/                 # React components
│           ├── build/               # Production build (generated)
│           └── ebiosrm-portable/    # Generated portable package
```

## 🔧 Prerequisites

### Development Environment
- Node.js 18+ installed
- Docker Desktop installed and running
- MongoDB running (for data export)
- Git (for version control)

### Required Dependencies
Ensure backend has these dependencies in `package.json`:
```json
{
  "dependencies": {
    "@google/generative-ai": "^0.24.0",
    "axios": "^1.6.0",
    "bcryptjs": "^2.4.3",
    "cookie-parser": "^1.4.7",
    "cors": "^2.8.5",
    "dotenv": "^16.3.1",
    "express": "^4.18.2",
    "express-async-handler": "^1.2.0",
    "jsonwebtoken": "^9.0.2",
    "mongoose": "^7.5.0",
    "uuid": "^9.0.0"
  }
}
```

## 📝 Step-by-Step Process

### Step 1: Prepare Frontend Build

#### 1.1 Create Fresh Build
```bash
cd "path/to/my-ebios-app"
npm install
npm run build
```

#### 1.2 Verify Build Contents
Ensure the build includes all latest features:
- Check `build/asset-manifest.json` for recent timestamps
- Verify all Atelier components are included
- Test locally before packaging

### Step 2: Export Database

#### 2.1 Create Database Export Script
```bash
# Create mongo-init directory structure
mkdir -p mongo-init/data

# Export all collections
mongoexport --db ebiosrm --collection users --out mongo-init/data/ebiosrm.users.json --jsonArray
mongoexport --db ebiosrm --collection companies --out mongo-init/data/ebiosrm.companies.json --jsonArray
mongoexport --db ebiosrm --collection analyses --out mongo-init/data/ebiosrm.analyses.json --jsonArray
mongoexport --db ebiosrm --collection analysiscomponents --out mongo-init/data/ebiosrm.analysiscomponents.json --jsonArray
mongoexport --db ebiosrm --collection eventsuggestions --out mongo-init/data/ebiosrm.eventsuggestions.json --jsonArray
# ... (export all other collections)
```

#### 2.2 Create Import Script
```bash
# mongo-init/import-data.sh
#!/bin/bash
echo "Importing EBIOS RM database..."

until mongosh --eval "print('MongoDB ready')" > /dev/null 2>&1; do
  sleep 2
done

for file in /docker-entrypoint-initdb.d/data/*.json; do
  if [ -f "$file" ]; then
    collection=$(basename "$file" .json | cut -d. -f2)
    echo "Importing $collection..."
    mongoimport --db ebiosrm --collection "$collection" --file "$file" --jsonArray --drop
  fi
done

echo "Database import completed!"
```

### Step 3: Create Docker Configuration

#### 3.1 Docker Compose File
```yaml
# docker-compose.yml
version: '3.8'

services:
  mongodb:
    image: mongo:7.0
    container_name: ebiosrm-db
    restart: unless-stopped
    environment:
      MONGO_INITDB_ROOT_USERNAME: root
      MONGO_INITDB_ROOT_PASSWORD: rootpass
    ports:
      - "27017:27017"
    volumes:
      - mongodb_data:/data/db
      - ./mongo-init:/docker-entrypoint-initdb.d
    networks:
      - ebiosrm-net

  backend:
    build:
      context: ./backend
      dockerfile_inline: |
        FROM node:18-alpine
        WORKDIR /app
        COPY package*.json ./
        RUN npm install
        COPY . .
        EXPOSE 5050
        CMD ["npm", "start"]
    container_name: ebiosrm-backend
    restart: unless-stopped
    environment:
      NODE_ENV: production
      PORT: 5050
      MONGO_URI: **************************************************************
    ports:
      - "5050:5050"
    depends_on:
      - mongodb
    networks:
      - ebiosrm-net

  frontend:
    build:
      context: .
      dockerfile_inline: |
        FROM nginx:alpine
        COPY build /usr/share/nginx/html
        COPY default.conf /etc/nginx/conf.d/default.conf
        EXPOSE 80
    container_name: ebiosrm-frontend
    restart: unless-stopped
    ports:
      - "8090:80"
    depends_on:
      - backend
    networks:
      - ebiosrm-net

volumes:
  mongodb_data:

networks:
  ebiosrm-net:
    driver: bridge
```

#### 3.2 Nginx Configuration
```nginx
# default.conf
server {
    listen 80;
    server_name localhost;

    location / {
        root /usr/share/nginx/html;
        index index.html;
        try_files $uri $uri/ /index.html;
    }

    location /api/ {
        proxy_pass http://backend:5050;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }
}
```

### Step 4: Create Portable Package Script

#### 4.1 Package Creation Script (Windows)
```batch
@echo off
echo Creating EBIOS RM Portable Package...

REM Create package directory
if exist ebiosrm-portable rmdir /s /q ebiosrm-portable
mkdir ebiosrm-portable

REM Copy Docker configuration
copy docker-compose.yml ebiosrm-portable\
copy default.conf ebiosrm-portable\

REM Copy backend source
xcopy "..\backend" "ebiosrm-portable\backend\" /E /I /Y

REM Copy fresh frontend build
xcopy "build" "ebiosrm-portable\build\" /E /I /Y

REM Copy database files
xcopy "mongo-init" "ebiosrm-portable\mongo-init\" /E /I /Y

REM Create launch scripts
echo @echo off > ebiosrm-portable\launch.bat
echo docker-compose down 2^>nul >> ebiosrm-portable\launch.bat
echo docker-compose up -d --build >> ebiosrm-portable\launch.bat
echo echo Frontend: http://localhost:8090 >> ebiosrm-portable\launch.bat
echo pause >> ebiosrm-portable\launch.bat

echo #!/bin/bash > ebiosrm-portable\launch.sh
echo docker-compose down 2^>/dev/null ^|^| true >> ebiosrm-portable\launch.sh
echo docker-compose up -d --build >> ebiosrm-portable\launch.sh
echo echo "Frontend: http://localhost:8090" >> ebiosrm-portable\launch.sh

echo Package created successfully!
```

### Step 5: Create Installation Scripts

#### 5.1 Cross-Platform Launch Scripts
```bash
# launch.sh (Linux/macOS)
#!/bin/bash
echo "Starting EBIOS RM Application..."

if ! command -v docker &> /dev/null; then
    echo "Docker not found. Please install Docker first."
    exit 1
fi

docker-compose down 2>/dev/null || true
docker-compose up -d --build

echo "EBIOS RM is starting..."
echo "Frontend: http://localhost:8090"
echo "Backend: http://localhost:5050/api"
```

```batch
REM launch.bat (Windows)
@echo off
echo Starting EBIOS RM Application...

docker --version >nul 2>&1
if errorlevel 1 (
    echo Docker not found. Please install Docker Desktop first.
    pause
    exit /b 1
)

docker-compose down 2>nul
docker-compose up -d --build

echo Frontend: http://localhost:8090
echo Backend: http://localhost:5050/api
pause
```

## ⚠️ Common Issues and Solutions

### Issue 1: Missing Dependencies
**Problem**: Backend fails with "Cannot find module 'axios'" or "Cannot find module 'uuid'"
**Solution**: Ensure all required dependencies are in backend/package.json

### Issue 2: Old Frontend Build
**Problem**: Latest features (Atelier 3, 4) not visible in portable version
**Solution**: Always create fresh build before packaging:
```bash
npm run build
# Then copy fresh build to portable package
```

### Issue 3: Database Import Fails
**Problem**: MongoDB import script doesn't work
**Solution**: Ensure proper file permissions and MongoDB is ready:
```bash
chmod +x mongo-init/import-data.sh
# Add proper wait logic in import script
```

### Issue 4: Docker Build Fails
**Problem**: Frontend or backend container fails to build
**Solution**: Check Dockerfile syntax and ensure all files are copied correctly

## 🔍 Verification Checklist

Before distributing the portable package, verify:

- [ ] Fresh frontend build includes all latest features
- [ ] Backend has all required dependencies
- [ ] Database export includes all collections
- [ ] Docker Compose file has correct paths
- [ ] Launch scripts work on target platforms
- [ ] All containers start successfully
- [ ] Application is accessible on expected ports
- [ ] Database data is properly imported

## 📦 Final Package Structure

```
ebiosrm-portable/
├── docker-compose.yml          # Container orchestration
├── default.conf               # Nginx configuration
├── launch.sh                  # Linux/macOS launcher
├── launch.bat                 # Windows launcher
├── README.md                  # User instructions
├── backend/                   # Complete backend source
│   ├── package.json          # With all dependencies
│   ├── server.js             # Main application
│   ├── controllers/          # API controllers
│   ├── models/               # Database models
│   ├── routes/               # API routes
│   └── ...                   # Other backend files
├── build/                     # Fresh frontend build
│   ├── index.html            # Main HTML file
│   ├── static/               # CSS/JS assets
│   └── ...                   # Other build files
└── mongo-init/               # Database setup
    ├── import-data.sh        # Import script
    └── data/                 # Exported collections
        ├── ebiosrm.users.json
        ├── ebiosrm.companies.json
        └── ...               # All collections
```

## 🚀 Distribution

1. **Compress the package**: Create `ebiosrm-portable.zip` or `ebiosrm-portable.tar.gz`
2. **Test on clean system**: Verify installation on system without development tools
3. **Document requirements**: Ensure Docker requirements are clearly stated
4. **Provide support**: Include troubleshooting guide and contact information

## 📝 Version Control

For future versions:
1. Tag the source code version used for the portable package
2. Document any configuration changes
3. Update this guide with new requirements or issues
4. Maintain compatibility with previous deployment methods

---

**Created**: January 2025  
**Version**: 1.0  
**Compatible with**: EBIOS RM v1.0+  
**Docker**: 20.10+  
**Node.js**: 18+
