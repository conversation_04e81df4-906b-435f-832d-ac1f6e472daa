import React, { useState, useEffect } from 'react';
import { companyService } from '../../services/apiServices';
import { authService } from '../../services/authService';
import {
  ChartBarIcon,
  UsersIcon,
  BuildingOfficeIcon,
  ClipboardDocumentListIcon,
  ArrowTrendingUpIcon,
  ArrowTrendingDownIcon
} from '@heroicons/react/24/outline';

const ProviderDashboard = () => {
  const [companyData, setCompanyData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [metrics, setMetrics] = useState(null);

  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        const user = authService.getCurrentUser();
        if (!user || !user.companyId) {
          throw new Error('No company ID found');
        }

        // Get company data
        const companyResponse = await companyService.getCompanyById(user.companyId);
        if (!companyResponse.success) {
          throw new Error(companyResponse.message || 'Failed to fetch company data');
        }

        // Get company metrics
        const metricsResponse = await companyService.getCompanyMetrics();
        if (metricsResponse.success) {
          const companyMetrics = metricsResponse.data.find(
            m => m.companyId === user.companyId
          );
          setMetrics(companyMetrics);
        }

        setCompanyData(companyResponse.data);
      } catch (error) {
        console.error('Error fetching provider dashboard data:', error);
        setError(error.message || 'Failed to load dashboard data');
      } finally {
        setLoading(false);
      }
    };

    fetchDashboardData();
  }, []);

  if (loading) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-700"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-lg p-4">
        <p className="text-red-700">{error}</p>
      </div>
    );
  }

  if (!companyData) {
    return (
      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
        <p className="text-yellow-700">No company data available</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Welcome Section */}
      <div className="bg-white rounded-lg shadow-sm p-6">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">Welcome, {authService.getCurrentUser()?.name}</h1>
        <p className="text-gray-600">Here's an overview of your company</p>
      </div>

      {/* Stats Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white rounded-lg shadow-sm p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Users</p>
              <p className="text-2xl font-semibold text-gray-900">{metrics?.totalUsers || 0}</p>
            </div>
            <div className="bg-blue-100 rounded-full p-3">
              <UsersIcon className="h-6 w-6 text-blue-600" />
            </div>
          </div>
          <div className="mt-4 flex items-center">
            <ArrowTrendingUpIcon className="h-4 w-4 text-green-500" />
            <span className="text-sm text-green-600 ml-1">+12% from last month</span>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Active Users</p>
              <p className="text-2xl font-semibold text-gray-900">{metrics?.activeUsers || 0}</p>
            </div>
            <div className="bg-green-100 rounded-full p-3">
              <ChartBarIcon className="h-6 w-6 text-green-600" />
            </div>
          </div>
          <div className="mt-4 flex items-center">
            <ArrowTrendingUpIcon className="h-4 w-4 text-green-500" />
            <span className="text-sm text-green-600 ml-1">+8% from last month</span>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Company Status</p>
              <p className="text-2xl font-semibold text-gray-900 capitalize">{companyData.status}</p>
            </div>
            <div className="bg-purple-100 rounded-full p-3">
              <BuildingOfficeIcon className="h-6 w-6 text-purple-600" />
            </div>
          </div>
          <div className="mt-4 flex items-center">
            <span className="text-sm text-gray-600">Last updated: {new Date(companyData.updatedAt).toLocaleDateString()}</span>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Activity Logs</p>
              <p className="text-2xl font-semibold text-gray-900">24</p>
            </div>
            <div className="bg-orange-100 rounded-full p-3">
              <ClipboardDocumentListIcon className="h-6 w-6 text-orange-600" />
            </div>
          </div>
          <div className="mt-4 flex items-center">
            <ArrowTrendingDownIcon className="h-4 w-4 text-red-500" />
            <span className="text-sm text-red-600 ml-1">-5% from last week</span>
          </div>
        </div>
      </div>

      {/* Company Overview */}
      <div className="bg-white rounded-lg shadow-sm p-6">
        <h2 className="text-2xl font-semibold text-gray-800 mb-4">Company Overview</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <div className="bg-blue-50 rounded-lg p-4">
            <h3 className="text-sm font-medium text-blue-700">Company Name</h3>
            <p className="text-lg font-semibold text-blue-900">{companyData.name}</p>
          </div>
          <div className="bg-green-50 rounded-lg p-4">
            <h3 className="text-sm font-medium text-green-700">Domain</h3>
            <p className="text-lg font-semibold text-green-900">{companyData.domain}</p>
          </div>
          <div className="bg-purple-50 rounded-lg p-4">
            <h3 className="text-sm font-medium text-purple-700">Status</h3>
            <p className="text-lg font-semibold text-purple-900 capitalize">{companyData.status}</p>
          </div>
          <div className="bg-orange-50 rounded-lg p-4">
            <h3 className="text-sm font-medium text-orange-700">Total Users</h3>
            <p className="text-lg font-semibold text-orange-900">{metrics?.totalUsers || 0}</p>
          </div>
          <div className="bg-indigo-50 rounded-lg p-4">
            <h3 className="text-sm font-medium text-indigo-700">Created At</h3>
            <p className="text-lg font-semibold text-indigo-900">
              {new Date(companyData.createdAt).toLocaleDateString()}
            </p>
          </div>
          <div className="bg-pink-50 rounded-lg p-4">
            <h3 className="text-sm font-medium text-pink-700">Last Updated</h3>
            <p className="text-lg font-semibold text-pink-900">
              {new Date(companyData.updatedAt).toLocaleDateString()}
            </p>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="bg-white rounded-lg shadow-sm p-6">
        <h2 className="text-2xl font-semibold text-gray-800 mb-4">Quick Actions</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <button className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
            Manage Users
          </button>
          <button className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors">
            View Reports
          </button>
          <button className="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 transition-colors">
            Company Settings
          </button>
        </div>
      </div>
    </div>
  );
};

export default ProviderDashboard; 