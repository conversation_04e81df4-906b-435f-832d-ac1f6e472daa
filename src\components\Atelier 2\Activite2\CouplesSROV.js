// src/components/Atelier 2/Activite2/CouplesSROV.js
import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Save, Plus, RefreshCw, AlertCircle, Target, Trash2, Edit, X, Check, Search, Filter, Download, Link } from 'lucide-react';
import { useAnalysis } from '../../../context/AnalysisContext';
import { showSuccessToast, showErrorToast, showLoadingToast, updateToast } from '../../../utils/toastUtils';
import SliderSelector from '../common/SliderSelector';

const CouplesSROV = () => {
  // State for couples SR/OV
  const [couples, setCouples] = useState([]);
  const [newCouple, setNewCouple] = useState({
    sourceId: '',
    objectifId: '',
    vraisemblance: 'faible', // faible, moyenne, forte
    justification: '',
  });
  const [editingCouple, setEditingCouple] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterVraisemblance, setFilterVraisemblance] = useState('');
  const [isSaving, setIsSaving] = useState(false);

  // Get analysis context
  const {
    currentAnalysis,
    saveCouplesSROV,
    loadCouplesSROV,
    getSourcesDeRisque,
    loadObjectifsVises,
  } = useAnalysis();

  // State for sources and objectifs
  const [sourcesRisque, setSourcesRisque] = useState([]);
  const [objectifsVises, setObjectifsVises] = useState([]);

  // Load data on component mount
  useEffect(() => {
    const fetchData = async () => {
      if (currentAnalysis?.id) {
        try {
          // Load couples
          const couplesData = await loadCouplesSROV(currentAnalysis.id);
          if (couplesData) {
            setCouples(couplesData);
          }

          // Load sources de risque
          const sourcesData = await getSourcesDeRisque(currentAnalysis.id);
          if (sourcesData) {
            setSourcesRisque(sourcesData);
          }

          // Load objectifs visés
          const objectifsData = await loadObjectifsVises(currentAnalysis.id);
          if (objectifsData) {
            setObjectifsVises(objectifsData);
          }
        } catch (error) {
          console.error('Error loading data:', error);
          showErrorToast('Erreur lors du chargement des données');
        }
      }
    };

    fetchData();
  }, [currentAnalysis?.id, loadCouplesSROV, getSourcesDeRisque, loadObjectifsVises]);

  // Handle adding a new couple SR/OV
  const handleAddCouple = () => {
    if (!newCouple.sourceId || !newCouple.objectifId) {
      showErrorToast('Veuillez sélectionner une source de risque et un objectif visé');
      return;
    }

    // Check if couple already exists
    const coupleExists = couples.some(
      couple => couple.sourceId === newCouple.sourceId && couple.objectifId === newCouple.objectifId
    );

    if (coupleExists) {
      showErrorToast('Ce couple source/objectif existe déjà');
      return;
    }

    const coupleToAdd = {
      ...newCouple,
      id: Date.now().toString(), // Temporary ID until saved to backend
      createdAt: new Date().toISOString(),
    };

    setCouples([...couples, coupleToAdd]);
    setNewCouple({
      sourceId: '',
      objectifId: '',
      vraisemblance: 'faible',
      justification: '',
    });

    showSuccessToast('Couple SR/OV ajouté');
  };

  // Handle editing a couple SR/OV
  const handleEditCouple = (couple) => {
    setEditingCouple({
      ...couple,
    });
  };

  // Handle saving edited couple SR/OV
  const handleSaveEdit = () => {
    if (!editingCouple.sourceId || !editingCouple.objectifId) {
      showErrorToast('Veuillez sélectionner une source de risque et un objectif visé');
      return;
    }

    // Check if couple already exists (excluding the current one being edited)
    const coupleExists = couples.some(
      couple =>
        couple.id !== editingCouple.id &&
        couple.sourceId === editingCouple.sourceId &&
        couple.objectifId === editingCouple.objectifId
    );

    if (coupleExists) {
      showErrorToast('Ce couple source/objectif existe déjà');
      return;
    }

    const updatedCouples = couples.map(couple =>
      couple.id === editingCouple.id ? editingCouple : couple
    );

    setCouples(updatedCouples);
    setEditingCouple(null);
    showSuccessToast('Couple SR/OV mis à jour');
  };

  // Handle canceling edit
  const handleCancelEdit = () => {
    setEditingCouple(null);
  };

  // Handle deleting a couple SR/OV
  const handleDeleteCouple = (id) => {
    if (window.confirm('Êtes-vous sûr de vouloir supprimer ce couple SR/OV ?')) {
      const updatedCouples = couples.filter(couple => couple.id !== id);
      setCouples(updatedCouples);
      showSuccessToast('Couple SR/OV supprimé');
    }
  };

  // Handle saving all couples SR/OV
  const handleSaveAll = async () => {
    if (!currentAnalysis?.id) {
      showErrorToast('Aucune analyse sélectionnée');
      return;
    }

    setIsSaving(true);
    const toastId = showLoadingToast('Sauvegarde des couples SR/OV...');

    try {
      await saveCouplesSROV(currentAnalysis.id, couples);
      updateToast(toastId, 'Couples SR/OV sauvegardés avec succès', 'success');
    } catch (error) {
      console.error('Error saving couples SR/OV:', error);
      updateToast(toastId, 'Erreur lors de la sauvegarde des couples SR/OV', 'error');
    } finally {
      setIsSaving(false);
    }
  };

  // Filter couples based on search term and vraisemblance filter
  const filteredCouples = couples.filter(couple => {
    const source = sourcesRisque.find(s => s.id === couple.sourceId);
    const objectif = objectifsVises.find(o => o.id === couple.objectifId);

    const sourceName = source ? source.name : '';
    const objectifName = objectif ? objectif.name : '';

    const matchesSearch =
      sourceName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      objectifName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      couple.justification.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesVraisemblance = filterVraisemblance ? couple.vraisemblance === filterVraisemblance : true;

    return matchesSearch && matchesVraisemblance;
  });

  // Get color for vraisemblance
  const getVraisemblanceColor = (level) => {
    switch (level) {
      case 'faible': return 'bg-green-100 text-green-800';
      case 'moyenne': return 'bg-yellow-100 text-yellow-800';
      case 'forte': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  // Color map for vraisemblance slider
  const vraisemblanceColorMap = {
    'faible': 'bg-green-600',
    'moyenne': 'bg-yellow-600',
    'forte': 'bg-red-600'
  };

  // Get source name by ID
  const getSourceName = (id) => {
    const source = sourcesRisque.find(s => s.id === id);
    return source ? source.name : `ID: ${id}`;
  };

  // Get objectif name by ID
  const getObjectifName = (id) => {
    const objectif = objectifsVises.find(o => o.id === id);
    return objectif ? objectif.name : `ID: ${id}`;
  };

  // Get source type by ID
  const getSourceType = (id) => {
    const source = sourcesRisque.find(s => s.id === id);
    return source ? source.type : '';
  };

  // Get objectif type by ID
  const getObjectifType = (id) => {
    const objectif = objectifsVises.find(o => o.id === id);
    return objectif ? objectif.type : '';
  };

  // Get type badge color for source
  const getSourceTypeBadgeColor = (id) => {
    const type = getSourceType(id);
    switch (type) {
      case 'humain': return 'bg-blue-100 text-blue-800';
      case 'technique': return 'bg-purple-100 text-purple-800';
      case 'physique': return 'bg-orange-100 text-orange-800';
      case 'organisationnel': return 'bg-indigo-100 text-indigo-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  // Get type badge color for objectif
  const getObjectifTypeBadgeColor = (id) => {
    const type = getObjectifType(id);
    switch (type) {
      case 'confidentialite': return 'bg-blue-100 text-blue-800';
      case 'integrite': return 'bg-purple-100 text-purple-800';
      case 'disponibilite': return 'bg-orange-100 text-orange-800';
      case 'auditabilite': return 'bg-red-100 text-red-800';
      case 'preuve': return 'bg-indigo-100 text-indigo-800';
      case 'controle': return 'bg-teal-100 text-teal-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="bg-white p-6 rounded-lg shadow-sm border border-gray-200"
    >
      {/* Header */}
      <div className="flex justify-between items-center mb-6">
        <div>
          <h2 className="text-2xl font-bold text-gray-800 flex items-center">
            <Link size={24} className="mr-2 text-blue-600" />
            Couples SR/OV
          </h2>
          <p className="text-sm text-gray-500 mt-1">
            Évaluez les couples Source de Risque / Objectif Visé
          </p>
        </div>
        <button
          onClick={handleSaveAll}
          disabled={isSaving}
          className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 flex items-center disabled:opacity-50"
        >
          {isSaving ? (
            <RefreshCw size={16} className="mr-2 animate-spin" />
          ) : (
            <Save size={16} className="mr-2" />
          )}
          Enregistrer
        </button>
      </div>

      {/* Add new couple form */}
      <div className="bg-gray-50 p-4 rounded-lg border border-gray-200 mb-6">
        <h3 className="text-lg font-semibold text-gray-700 mb-3">Ajouter un couple SR/OV</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Source de Risque</label>
            <select
              value={newCouple.sourceId}
              onChange={(e) => setNewCouple({ ...newCouple, sourceId: e.target.value })}
              className="w-full p-2 border border-gray-300 rounded-md"
            >
              <option value="">Sélectionner une source de risque</option>
              {sourcesRisque.map(source => (
                <option key={source.id} value={source.id}>{source.name}</option>
              ))}
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Objectif Visé</label>
            <select
              value={newCouple.objectifId}
              onChange={(e) => setNewCouple({ ...newCouple, objectifId: e.target.value })}
              className="w-full p-2 border border-gray-300 rounded-md"
            >
              <option value="">Sélectionner un objectif visé</option>
              {objectifsVises.map(objectif => (
                <option key={objectif.id} value={objectif.id}>{objectif.name}</option>
              ))}
            </select>
          </div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
          <div>
            <SliderSelector
              label="Vraisemblance"
              value={newCouple.vraisemblance}
              onChange={(value) => setNewCouple({ ...newCouple, vraisemblance: value })}
              options={[
                { value: 'faible', label: 'Faible' },
                { value: 'moyenne', label: 'Moyenne' },
                { value: 'forte', label: 'Forte' }
              ]}
              colorMap={vraisemblanceColorMap}
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Justification</label>
            <input
              type="text"
              value={newCouple.justification}
              onChange={(e) => setNewCouple({ ...newCouple, justification: e.target.value })}
              className="w-full p-2 border border-gray-300 rounded-md"
              placeholder="Justification de la vraisemblance"
            />
          </div>
        </div>
        <div className="flex justify-end">
          <button
            onClick={handleAddCouple}
            className="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 flex items-center"
          >
            <Plus size={16} className="mr-2" />
            Ajouter
          </button>
        </div>
      </div>

      {/* Search and filter */}
      <div className="flex flex-col md:flex-row gap-4 mb-6">
        <div className="flex-1">
          <div className="relative">
            <input
              type="text"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full p-2 pl-10 border border-gray-300 rounded-md"
              placeholder="Rechercher un couple SR/OV..."
            />
            <Search size={18} className="absolute left-3 top-2.5 text-gray-400" />
          </div>
        </div>
        <div className="w-full md:w-64">
          <select
            value={filterVraisemblance}
            onChange={(e) => setFilterVraisemblance(e.target.value)}
            className="w-full p-2 border border-gray-300 rounded-md"
          >
            <option value="">Toutes les vraisemblances</option>
            <option value="faible">Faible</option>
            <option value="moyenne">Moyenne</option>
            <option value="forte">Forte</option>
          </select>
        </div>
      </div>

      {/* Couples list */}
      {filteredCouples.length === 0 ? (
        <div className="text-center py-8 bg-gray-50 rounded-lg border border-gray-200">
          <Link size={48} className="mx-auto text-gray-400 mb-4" />
          <p className="text-gray-600 font-medium">Aucun couple SR/OV trouvé</p>
          <p className="text-gray-500 text-sm mt-1">
            {couples.length > 0
              ? 'Essayez de modifier vos critères de recherche ou de filtre'
              : 'Commencez par ajouter un couple SR/OV en utilisant le formulaire ci-dessus'}
          </p>
        </div>
      ) : (
        <div className="grid grid-cols-1 gap-4">
          {filteredCouples.map((couple) => (
            <div
              key={couple.id}
              className="bg-white p-4 rounded-lg border border-gray-200 shadow-sm hover:shadow-md transition-shadow"
            >
              {editingCouple && editingCouple.id === couple.id ? (
                // Edit mode
                <div>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Source de Risque</label>
                      <select
                        value={editingCouple.sourceId}
                        onChange={(e) => setEditingCouple({ ...editingCouple, sourceId: e.target.value })}
                        className="w-full p-2 border border-gray-300 rounded-md"
                      >
                        <option value="">Sélectionner une source de risque</option>
                        {sourcesRisque.map(source => (
                          <option key={source.id} value={source.id}>{source.name}</option>
                        ))}
                      </select>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Objectif Visé</label>
                      <select
                        value={editingCouple.objectifId}
                        onChange={(e) => setEditingCouple({ ...editingCouple, objectifId: e.target.value })}
                        className="w-full p-2 border border-gray-300 rounded-md"
                      >
                        <option value="">Sélectionner un objectif visé</option>
                        {objectifsVises.map(objectif => (
                          <option key={objectif.id} value={objectif.id}>{objectif.name}</option>
                        ))}
                      </select>
                    </div>
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div>
                      <SliderSelector
                        label="Vraisemblance"
                        value={editingCouple.vraisemblance}
                        onChange={(value) => setEditingCouple({ ...editingCouple, vraisemblance: value })}
                        options={[
                          { value: 'faible', label: 'Faible' },
                          { value: 'moyenne', label: 'Moyenne' },
                          { value: 'forte', label: 'Forte' }
                        ]}
                        colorMap={vraisemblanceColorMap}
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Justification</label>
                      <input
                        type="text"
                        value={editingCouple.justification}
                        onChange={(e) => setEditingCouple({ ...editingCouple, justification: e.target.value })}
                        className="w-full p-2 border border-gray-300 rounded-md"
                        placeholder="Justification de la vraisemblance"
                      />
                    </div>
                  </div>
                  <div className="flex justify-end space-x-2">
                    <button
                      onClick={handleCancelEdit}
                      className="bg-gray-200 text-gray-800 px-4 py-2 rounded-md hover:bg-gray-300 flex items-center"
                    >
                      <X size={16} className="mr-2" />
                      Annuler
                    </button>
                    <button
                      onClick={handleSaveEdit}
                      className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 flex items-center"
                    >
                      <Check size={16} className="mr-2" />
                      Enregistrer
                    </button>
                  </div>
                </div>
              ) : (
                // View mode
                <div>
                  <div className="flex justify-between items-start">
                    <div className="flex-1">
                      <div className="flex flex-col md:flex-row md:items-center md:space-x-4">
                        <div className="flex-1">
                          <div className="flex items-center">
                            <AlertCircle size={16} className="text-blue-600 mr-2" />
                            <h3 className="text-lg font-semibold text-gray-800">{getSourceName(couple.sourceId)}</h3>
                            <span className={`ml-2 px-2 py-0.5 rounded-full text-xs ${getSourceTypeBadgeColor(couple.sourceId)}`}>
                              {getSourceType(couple.sourceId).charAt(0).toUpperCase() + getSourceType(couple.sourceId).slice(1)}
                            </span>
                          </div>
                        </div>
                        <div className="flex items-center my-2 md:my-0">
                          <div className="w-6 h-0.5 bg-gray-300"></div>
                          <div className="mx-2 p-1 bg-blue-100 rounded-full">
                            <Link size={14} className="text-blue-600" />
                          </div>
                          <div className="w-6 h-0.5 bg-gray-300"></div>
                        </div>
                        <div className="flex-1">
                          <div className="flex items-center">
                            <Target size={16} className="text-blue-600 mr-2" />
                            <h3 className="text-lg font-semibold text-gray-800">{getObjectifName(couple.objectifId)}:</h3>
                            <span className={`ml-2 px-2 py-0.5 rounded-full text-xs ${getObjectifTypeBadgeColor(couple.objectifId)}`}>
                              {getObjectifType(couple.objectifId).charAt(0).toUpperCase() + getObjectifType(couple.objectifId).slice(1)}
                            </span>
                            <span className={`ml-2 px-2 py-0.5 rounded-full text-xs ${getObjectifTypeBadgeColor(couple.objectifId)}`}>
                              {getObjectifType(couple.objectifId).charAt(0).toUpperCase() + getObjectifType(couple.objectifId).slice(1)}
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div className="flex space-x-1 ml-4">
                      <button
                        onClick={() => handleEditCouple(couple)}
                        className="p-1.5 text-blue-600 hover:bg-blue-50 rounded-full"
                        title="Modifier"
                      >
                        <Edit size={16} />
                      </button>
                      <button
                        onClick={() => handleDeleteCouple(couple.id)}
                        className="p-1.5 text-red-600 hover:bg-red-50 rounded-full"
                        title="Supprimer"
                      >
                        <Trash2 size={16} />
                      </button>
                    </div>
                  </div>
                  <div className="mt-4 grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <p className="text-xs text-gray-500">Vraisemblance</p>
                      <span className={`inline-block px-2 py-0.5 rounded-full text-xs ${getVraisemblanceColor(couple.vraisemblance)}`}>
                        {couple.vraisemblance.charAt(0).toUpperCase() + couple.vraisemblance.slice(1)}
                      </span>
                    </div>
                    <div>
                      <p className="text-xs text-gray-500">Justification</p>
                      <p className="text-sm font-medium">{couple.justification || 'Non spécifiée'}</p>
                    </div>
                  </div>
                </div>
              )}
            </div>
          ))}
        </div>
      )}
    </motion.div>
  );
};

export default CouplesSROV;
