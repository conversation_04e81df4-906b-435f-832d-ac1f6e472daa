// src/services/stakeholderService.js
import api from '../api/apiClient';

/**
 * Service for managing stakeholders in Atelier 3
 */
const stakeholderService = {
  /**
   * Get all stakeholders for an analysis
   * @param {string} analysisId - The ID of the analysis
   * @returns {Promise<Object>} - Response with stakeholders data
   */
  getStakeholders: async (analysisId) => {
    try {
      const response = await api.get(`/analyses/${analysisId}/atelier3/stakeholders`);
      return response;
    } catch (error) {
      console.error('Error getting stakeholders:', error);
      return { success: false, message: 'Erreur lors de la récupération des parties prenantes', error: error.message };
    }
  },

  /**
   * Create a new stakeholder
   * @param {string} analysisId - The ID of the analysis
   * @param {Object} stakeholderData - The stakeholder data to create
   * @returns {Promise<Object>} - Response with created stakeholder
   */
  createStakeholder: async (analysisId, stakeholderData) => {
    try {
      const response = await api.post(`/analyses/${analysisId}/atelier3/stakeholders`, stakeholderData);
      return response;
    } catch (error) {
      console.error('Error creating stakeholder:', error);
      return { success: false, message: 'Erreur lors de la création de la partie prenante', error: error.message };
    }
  },

  /**
   * Update an existing stakeholder
   * @param {string} analysisId - The ID of the analysis
   * @param {string} stakeholderId - The ID of the stakeholder to update
   * @param {Object} stakeholderData - The updated stakeholder data
   * @returns {Promise<Object>} - Response with updated stakeholder
   */
  updateStakeholder: async (analysisId, stakeholderId, stakeholderData) => {
    try {
      const response = await api.put(`/analyses/${analysisId}/atelier3/stakeholders/${stakeholderId}`, stakeholderData);
      return response;
    } catch (error) {
      console.error('Error updating stakeholder:', error);
      return { success: false, message: 'Erreur lors de la mise à jour de la partie prenante', error: error.message };
    }
  },

  /**
   * Delete a stakeholder
   * @param {string} analysisId - The ID of the analysis
   * @param {string} stakeholderId - The ID of the stakeholder to delete
   * @returns {Promise<Object>} - Response with deletion status
   */
  deleteStakeholder: async (analysisId, stakeholderId) => {
    try {
      const response = await api.delete(`/analyses/${analysisId}/atelier3/stakeholders/${stakeholderId}`);
      return response;
    } catch (error) {
      console.error('Error deleting stakeholder:', error);
      return { success: false, message: 'Erreur lors de la suppression de la partie prenante', error: error.message };
    }
  },

  /**
   * Save all stakeholders for an analysis
   * @param {string} analysisId - The ID of the analysis
   * @param {Array} stakeholders - Array of stakeholders to save
   * @returns {Promise<Object>} - Response with saved stakeholders
   */
  saveStakeholders: async (analysisId, stakeholders) => {
    try {
      console.log('Saving stakeholders to:', `/analyses/${analysisId}/atelier3/stakeholders/batch`);
      console.log('Stakeholders data:', stakeholders);

      const response = await api.post(`/analyses/${analysisId}/atelier3/stakeholders/batch`, { stakeholders });

      console.log('Save response:', response);
      return response;
    } catch (error) {
      console.error('Error saving stakeholders:', error);
      return { success: false, message: 'Erreur lors de l\'enregistrement des parties prenantes', error: error.message };
    }
  },

  /**
   * Get threshold values for stakeholder threat zones
   * @param {string} analysisId - The ID of the analysis
   * @returns {Promise<Object>} - Response with threshold values
   */
  getThresholds: async (analysisId) => {
    try {
      const response = await api.get(`/analyses/${analysisId}/atelier3/stakeholders/thresholds`);
      return response;
    } catch (error) {
      console.error('Error getting thresholds:', error);
      return {
        success: false,
        message: 'Erreur lors de la récupération des seuils',
        error: error.message,
        data: {
          danger: 3,
          control: 1.5,
          watch: 0.5
        }
      };
    }
  },

  /**
   * Update threshold values for stakeholder threat zones
   * @param {string} analysisId - The ID of the analysis
   * @param {Object} thresholds - The threshold values (danger, control, watch)
   * @returns {Promise<Object>} - Response with updated thresholds
   */
  updateThresholds: async (analysisId, thresholds) => {
    try {
      const response = await api.post(`/analyses/${analysisId}/atelier3/stakeholders/thresholds`, thresholds);
      return response;
    } catch (error) {
      console.error('Error updating thresholds:', error);
      return { success: false, message: 'Erreur lors de la mise à jour des seuils', error: error.message };
    }
  }
};

export default stakeholderService;
