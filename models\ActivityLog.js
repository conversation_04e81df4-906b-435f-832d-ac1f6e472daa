// backend/models/ActivityLog.js
const mongoose = require('mongoose');

const ActivityLogSchema = new mongoose.Schema({
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  userName: {
    type: String,
    required: true
  },
  companyId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Company',
    default: null
  },
  companyName: {
    type: String,
    default: null
  },
  actionType: {
    type: String,
    required: true,
    enum: [
      'USER_LOGIN', 'USER_LOGOUT', 'USER_CREATE', 'USER_UPDATE', 'USER_DELETE',
      'COMPANY_CREATE', 'COMPANY_UPDATE', 'COMPANY_DELETE',
      'ANALYSIS_CREATE', 'ANALYSIS_UPDATE', 'ANALYSIS_DELETE',
      'COMPONENT_CREATE', 'COMPONENT_UPDATE', 'COMPONENT_DELETE',
      'create', 'update', 'delete', // Added for Atelier 3 stakeholders
      'bulk_create' // Added for bulk operations
    ]
  },
  resourceType: {
    type: String,
    required: true,
    enum: [
      'user', 'company', 'analysis', 'analysis_component',
      'stakeholder', 'stakeholders', 'stakeholder-thresholds', // Added for Atelier 3
      'ecosystem-measures', // Added for Atelier 3 Activity 4
      'operational-scenario' // Added for Atelier 4 Activity 1
    ]
  },
  resourceId: {
    type: mongoose.Schema.Types.ObjectId,
    required: true
  },
  details: {
    type: Object,
    default: {}
  },
  ipAddress: {
    type: String,
    default: ''
  },
  userAgent: {
    type: String,
    default: ''
  },
  timestamp: {
    type: Date,
    default: Date.now
  }
});

// Index for performance
ActivityLogSchema.index({ timestamp: -1 });
ActivityLogSchema.index({ userId: 1, timestamp: -1 });
ActivityLogSchema.index({ companyId: 1, timestamp: -1 });

module.exports = mongoose.model('ActivityLog', ActivityLogSchema);