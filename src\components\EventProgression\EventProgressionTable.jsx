import React, { useState } from 'react';
import {
  CheckCircle, AlertCircle, XCircle, Info, ChevronDown, ChevronRight,
  TrendingUp, Target, Shield, AlertTriangle, Search, Grid, List,
  ChevronLeft, ChevronRight as ChevronRightIcon, Bar<PERSON>hart3, Filter
} from 'lucide-react';

const EventProgressionTable = ({ data }) => {
  const [expandedEvents, setExpandedEvents] = useState(new Set());
  const [filterStatus, setFilterStatus] = useState('all');

  const [searchTerm, setSearchTerm] = useState('');
  const [viewMode, setViewMode] = useState('table'); // 'table', 'cards', 'summary'
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [sortField] = useState('name');
  const [sortDirection] = useState('asc');

  // Reset page when filters change
  React.useEffect(() => {
    setCurrentPage(1);
  }, [filterStatus, searchTerm]);

  // Handle null or undefined data
  if (!data) {
    return (
      <div className="bg-gray-50 rounded-lg p-8 text-center">
        <Info className="mx-auto h-12 w-12 text-gray-400 mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">Chargement des données...</h3>
        <p className="text-gray-500">Veuillez patienter pendant le chargement des données de progression.</p>
      </div>
    );
  }

  const { eventProgression = [], globalStats = {}, recommendations = [] } = data;

  // Toggle event expansion
  const toggleEventExpansion = (eventId) => {
    const newExpanded = new Set(expandedEvents);
    if (newExpanded.has(eventId)) {
      newExpanded.delete(eventId);
    } else {
      newExpanded.add(eventId);
    }
    setExpandedEvents(newExpanded);
  };

  // Filter and search events
  const filteredEvents = eventProgression.filter(event => {
    // Status filter
    if (filterStatus !== 'all' && event.status !== filterStatus) return false;

    // Search filter
    if (searchTerm) {
      const searchLower = searchTerm.toLowerCase();
      const matchesName = event.name.toLowerCase().includes(searchLower);
      const matchesPillar = event.securityPillar.toLowerCase().includes(searchLower);
      const matchesGravity = event.gravity.toLowerCase().includes(searchLower);
      if (!matchesName && !matchesPillar && !matchesGravity) return false;
    }

    return true;
  });

  // Sort events
  const sortedEvents = [...filteredEvents].sort((a, b) => {
    let aValue, bValue;

    switch (sortField) {
      case 'name':
        aValue = a.name;
        bValue = b.name;
        break;
      case 'gravity':
        aValue = a.gravity;
        bValue = b.gravity;
        break;
      case 'pillar':
        aValue = a.securityPillar;
        bValue = b.securityPillar;
        break;
      case 'progression':
        aValue = a.progressionRate;
        bValue = b.progressionRate;
        break;
      case 'status':
        aValue = a.status;
        bValue = b.status;
        break;
      default:
        aValue = a.name;
        bValue = b.name;
    }

    if (typeof aValue === 'string') {
      aValue = aValue.toLowerCase();
      bValue = bValue.toLowerCase();
    }

    if (sortDirection === 'asc') {
      return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
    } else {
      return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;
    }
  });

  // Pagination
  const totalPages = Math.ceil(sortedEvents.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const paginatedEvents = sortedEvents.slice(startIndex, startIndex + itemsPerPage);

  // Status icon component
  const StatusIcon = ({ status }) => {
    switch (status) {
      case 'enriched':
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case 'partially-treated':
        return <AlertCircle className="h-5 w-5 text-yellow-500" />;
      case 'not-treated':
        return <XCircle className="h-5 w-5 text-red-500" />;
      default:
        return <Info className="h-5 w-5 text-gray-500" />;
    }
  };

  // Progress bar component
  const ProgressBar = ({ value, color = 'blue' }) => {
    const colorClasses = {
      blue: 'bg-blue-500',
      green: 'bg-green-500',
      yellow: 'bg-yellow-500',
      red: 'bg-red-500'
    };

    const bgColor = value >= 100 ? colorClasses.green :
                   value >= 50 ? colorClasses.yellow :
                   value > 0 ? colorClasses.blue : colorClasses.red;

    return (
      <div className="w-full bg-gray-200 rounded-full h-2">
        <div
          className={`h-2 rounded-full ${bgColor}`}
          style={{ width: `${Math.min(value, 100)}%` }}
        ></div>
      </div>
    );
  };

  return (
    <div className="space-y-8">
      {/* Enhanced Global Statistics */}
      <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl p-6 border border-blue-100">
        <h2 className="text-xl font-bold text-gray-900 mb-6 flex items-center">
          <BarChart3 className="h-6 w-6 text-blue-600 mr-3" />
          Vue d'ensemble de la progression
        </h2>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
          <div className="bg-white rounded-xl shadow-sm p-5 border border-gray-100">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 mb-1">Total Événements</p>
                <p className="text-3xl font-bold text-gray-900">{globalStats.totalEvents || 0}</p>
              </div>
              <Target className="h-10 w-10 text-blue-500 bg-blue-50 rounded-lg p-2" />
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-sm p-5 border border-gray-100">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 mb-1">Enrichis</p>
                <p className="text-3xl font-bold text-green-600">{globalStats.enrichedEvents || 0}</p>
                <p className="text-xs text-gray-500">A1 + A3</p>
              </div>
              <CheckCircle className="h-10 w-10 text-green-500 bg-green-50 rounded-lg p-2" />
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-sm p-5 border border-gray-100">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 mb-1">Partiels</p>
                <p className="text-3xl font-bold text-yellow-600">{globalStats.partiallyTreatedEvents || 0}</p>
                <p className="text-xs text-gray-500">A1 ou A3</p>
              </div>
              <AlertCircle className="h-10 w-10 text-yellow-500 bg-yellow-50 rounded-lg p-2" />
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-sm p-5 border border-gray-100">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 mb-1">Non traités</p>
                <p className="text-3xl font-bold text-red-600">{globalStats.notTreatedEvents || 0}</p>
                <p className="text-xs text-gray-500">Aucun atelier</p>
              </div>
              <XCircle className="h-10 w-10 text-red-500 bg-red-50 rounded-lg p-2" />
            </div>
          </div>
        </div>

        {/* Progress Bars */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="bg-white rounded-lg p-4 border border-gray-100">
            <div className="flex justify-between items-center mb-3">
              <span className="text-sm font-semibold text-gray-700">Taux d'enrichissement</span>
              <span className="text-lg font-bold text-green-600">{globalStats.enrichmentRate || 0}%</span>
            </div>
            <ProgressBar value={globalStats.enrichmentRate || 0} color="green" />
            <p className="text-xs text-gray-500 mt-2">Événements traités par les deux ateliers</p>
          </div>
          <div className="bg-white rounded-lg p-4 border border-gray-100">
            <div className="flex justify-between items-center mb-3">
              <span className="text-sm font-semibold text-gray-700">Taux de couverture global</span>
              <span className="text-lg font-bold text-blue-600">{globalStats.treatmentRate || 0}%</span>
            </div>
            <ProgressBar value={globalStats.treatmentRate || 0} color="blue" />
            <p className="text-xs text-gray-500 mt-2">Événements traités par au moins un atelier</p>
          </div>
        </div>
      </div>

      {/* Additional Statistics */}
      {globalStats.pillarStats && (
        <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <BarChart3 className="h-5 w-5 mr-2 text-indigo-600" />
            Statistiques Détaillées
          </h3>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Pillar Statistics */}
            <div>
              <h4 className="text-md font-medium text-gray-800 mb-3">Répartition par Pilier de Sécurité</h4>
              <div className="space-y-3">
                {Object.entries(globalStats.pillarStats).filter(([_, stats]) => stats.total > 0).map(([pillar, stats]) => (
                  <div key={pillar} className="bg-gray-50 rounded-lg p-3">
                    <div className="flex justify-between items-center mb-2">
                      <span className="text-sm font-medium text-gray-700">{pillar}</span>
                      <span className="text-sm text-gray-600">{stats.total} événement{stats.total > 1 ? 's' : ''}</span>
                    </div>
                    <div className="flex items-center space-x-2 mb-1">
                      <div className="flex-1">
                        <ProgressBar value={stats.enrichmentRate} color="green" />
                      </div>
                      <span className="text-xs font-medium text-gray-600 w-12">{stats.enrichmentRate}%</span>
                    </div>
                    <div className="flex justify-between text-xs text-gray-500">
                      <span>🟢 {stats.enriched} enrichis</span>
                      <span>🟡 {stats.partial} partiels</span>
                      <span>🔴 {stats.notTreated} non traités</span>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Gravity Statistics */}
            <div>
              <h4 className="text-md font-medium text-gray-800 mb-3">Répartition par Niveau de Gravité</h4>
              <div className="space-y-3">
                {Object.entries(globalStats.gravityStats).filter(([_, stats]) => stats.total > 0).map(([gravity, stats]) => (
                  <div key={gravity} className="bg-gray-50 rounded-lg p-3">
                    <div className="flex justify-between items-center mb-2">
                      <span className="text-sm font-medium text-gray-700">{gravity}</span>
                      <span className="text-sm text-gray-600">{stats.total} événement{stats.total > 1 ? 's' : ''}</span>
                    </div>
                    <div className="flex items-center space-x-2 mb-1">
                      <div className="flex-1">
                        <ProgressBar value={stats.enrichmentRate} color={
                          gravity === 'Catastrophique' || gravity === 'Critique' ? 'red' :
                          gravity === 'Majeur' ? 'yellow' : 'blue'
                        } />
                      </div>
                      <span className="text-xs font-medium text-gray-600 w-12">{stats.enrichmentRate}%</span>
                    </div>
                    <div className="flex justify-between text-xs text-gray-500">
                      <span>🟢 {stats.enriched}</span>
                      <span>🟡 {stats.partial}</span>
                      <span>🔴 {stats.notTreated}</span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Summary Metrics */}
          <div className="mt-6 pt-4 border-t border-gray-200">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="text-center p-3 bg-blue-50 rounded-lg">
                <p className="text-2xl font-bold text-blue-600">{globalStats.totalA1Measures || 0}</p>
                <p className="text-sm text-blue-700">Mesures Atelier 1</p>
              </div>
              <div className="text-center p-3 bg-indigo-50 rounded-lg">
                <p className="text-2xl font-bold text-indigo-600">{globalStats.totalA3Measures || 0}</p>
                <p className="text-sm text-indigo-700">Mesures Atelier 3</p>
              </div>
              <div className="text-center p-3 bg-purple-50 rounded-lg">
                <p className="text-2xl font-bold text-purple-600">{globalStats.averageProgressionRate || 0}%</p>
                <p className="text-sm text-purple-700">Progression Moyenne</p>
              </div>
            </div>
          </div>
        </div>
      )}


      {/* Enhanced Filters and Controls */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-lg font-semibold text-gray-900 flex items-center">
            <Filter className="h-5 w-5 mr-2 text-blue-600" />
            Filtres et Options d'Affichage
          </h3>
          <div className="flex items-center space-x-2">
            <span className="text-sm text-gray-500">Vue:</span>
            <div className="flex rounded-md shadow-sm">
              <button
                onClick={() => setViewMode('table')}
                className={`px-3 py-2 text-sm font-medium rounded-l-md border ${
                  viewMode === 'table'
                    ? 'bg-blue-50 border-blue-500 text-blue-700'
                    : 'bg-white border-gray-300 text-gray-700 hover:bg-gray-50'
                }`}
              >
                <List className="h-4 w-4" />
              </button>
              <button
                onClick={() => setViewMode('cards')}
                className={`px-3 py-2 text-sm font-medium border-t border-b ${
                  viewMode === 'cards'
                    ? 'bg-blue-50 border-blue-500 text-blue-700'
                    : 'bg-white border-gray-300 text-gray-700 hover:bg-gray-50'
                }`}
              >
                <Grid className="h-4 w-4" />
              </button>
              <button
                onClick={() => setViewMode('summary')}
                className={`px-3 py-2 text-sm font-medium rounded-r-md border ${
                  viewMode === 'summary'
                    ? 'bg-blue-50 border-blue-500 text-blue-700'
                    : 'bg-white border-gray-300 text-gray-700 hover:bg-gray-50'
                }`}
              >
                <BarChart3 className="h-4 w-4" />
              </button>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {/* Search */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Recherche</label>
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="text"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                placeholder="Rechercher un événement..."
                className="pl-10 border border-gray-300 rounded-md px-3 py-2 text-sm w-full focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
          </div>

          {/* Status Filter */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Statut</label>
            <select
              value={filterStatus}
              onChange={(e) => setFilterStatus(e.target.value)}
              className="border border-gray-300 rounded-md px-3 py-2 text-sm w-full focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="all">Tous les statuts</option>
              <option value="enriched">Enrichis</option>
              <option value="partially-treated">Partiellement traités</option>
              <option value="not-treated">Non traités</option>
            </select>
          </div>

          {/* Items per page */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Éléments par page</label>
            <select
              value={itemsPerPage}
              onChange={(e) => setItemsPerPage(Number(e.target.value))}
              className="border border-gray-300 rounded-md px-3 py-2 text-sm w-full focus:ring-blue-500 focus:border-blue-500"
            >
              <option value={5}>5</option>
              <option value={10}>10</option>
              <option value={20}>20</option>
              <option value={50}>50</option>
              <option value={100}>Tous</option>
            </select>
          </div>
        </div>

        {/* Results summary */}
        <div className="mt-4 flex items-center justify-between text-sm text-gray-600">
          <span>
            Affichage de {startIndex + 1} à {Math.min(startIndex + itemsPerPage, sortedEvents.length)} sur {sortedEvents.length} événements
            {filteredEvents.length !== eventProgression.length && (
              <span className="text-blue-600"> (filtré sur {eventProgression.length} total)</span>
            )}
          </span>
          {(searchTerm || filterStatus !== 'all') && (
            <button
              onClick={() => {
                setSearchTerm('');
                setFilterStatus('all');
              }}
              className="text-blue-600 hover:text-blue-800 font-medium"
            >
              Réinitialiser les filtres
            </button>
          )}
        </div>
      </div>

      {/* Main Progression Table */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
        <div className="px-6 py-5 border-b border-gray-200 bg-gradient-to-r from-gray-50 to-gray-100">
          <h3 className="text-xl font-semibold text-gray-900 flex items-center">
            <TrendingUp className="h-5 w-5 mr-2 text-blue-600" />
            Progression Détaillée des Événements Redoutés
          </h3>
          <p className="text-sm text-gray-600 mt-2">
            Évolution des événements depuis l'Atelier 1 jusqu'à l'Atelier 3 • {sortedEvents.length} événement(s) affiché(s)
          </p>
        </div>

        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gradient-to-r from-gray-50 to-gray-100">
              <tr>
                <th className="px-6 py-4 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider">
                  Événement Redouté
                </th>
                <th className="px-6 py-4 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider">
                  Valeur Métier
                </th>
                <th className="px-6 py-4 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider">
                  Biens Supports
                </th>
                <th className="px-6 py-4 text-center text-xs font-semibold text-gray-700 uppercase tracking-wider">
                  Atelier 1
                </th>
                <th className="px-6 py-4 text-center text-xs font-semibold text-gray-700 uppercase tracking-wider">
                  Atelier 3
                </th>
                <th className="px-6 py-4 text-center text-xs font-semibold text-gray-700 uppercase tracking-wider">
                  Progression
                </th>
                <th className="px-6 py-4 text-center text-xs font-semibold text-gray-700 uppercase tracking-wider">
                  Statut
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {paginatedEvents.map((event) => (
                <React.Fragment key={event.id}>
                  <tr className={`hover:bg-gray-50 ${
                    event.status === 'not-treated' ? 'bg-red-50' :
                    event.status === 'partially-treated' ? 'bg-yellow-50' :
                    'bg-white'
                  }`}>
                    <td className="px-6 py-4">
                      <div className="flex items-center">
                        <button
                          onClick={() => toggleEventExpansion(event.id)}
                          className="mr-2 p-1 hover:bg-gray-200 rounded"
                        >
                          {expandedEvents.has(event.id) ?
                            <ChevronDown className="h-4 w-4" /> :
                            <ChevronRight className="h-4 w-4" />
                          }
                        </button>
                        <div>
                          <div className="text-sm font-medium text-gray-900">
                            {event.name}
                          </div>
                          <div className="text-xs text-gray-500">
                            {event.gravity} • {event.securityPillar}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">
                        {event.businessValueDetails?.name || 'Non définie'}
                      </div>
                      {event.businessValueDetails?.shortId && (
                        <div className="text-xs text-gray-500">
                          {event.businessValueDetails.shortId}
                        </div>
                      )}
                    </td>
                    <td className="px-6 py-4">
                      <div className="text-sm text-gray-900 max-w-xs">
                        {event.supportAssetsFormatted || 'Aucun'}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-center">
                      <div className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-50 text-blue-700">
                        {event.a1Measures.length} mesure{event.a1Measures.length > 1 ? 's' : ''}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-center">
                      <div className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-indigo-50 text-indigo-700">
                        {event.a3Measures.length} mesure{event.a3Measures.length > 1 ? 's' : ''}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-center">
                      <div className="flex items-center justify-center space-x-3">
                        <span className="text-sm font-bold text-gray-900">
                          {event.progressionRate}%
                        </span>
                        <div className="w-20">
                          <ProgressBar value={event.progressionRate} />
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-center">
                      <div className="flex items-center justify-center space-x-2">
                        <StatusIcon status={event.status} />
                        <span className={`text-sm font-semibold ${
                          event.status === 'enriched' ? 'text-green-700' :
                          event.status === 'partially-treated' ? 'text-yellow-700' :
                          'text-red-700'
                        }`}>
                          {event.statusLabel}
                        </span>
                      </div>
                    </td>
                  </tr>

                  {/* Expanded details */}
                  {expandedEvents.has(event.id) && (
                    <tr>
                      <td colSpan="7" className="px-6 py-4 bg-gray-50">
                        {/* Business Value and Support Assets Details */}
                        <div className="mb-6 p-4 bg-blue-50 rounded-lg border border-blue-200">
                          <h4 className="font-medium text-blue-900 mb-3">Contexte Business</h4>
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                              <h5 className="text-sm font-medium text-blue-800 mb-2">Valeur Métier</h5>
                              <p className="text-sm text-blue-700">
                                {event.businessValueDetails?.name || 'Non définie'}
                                {event.businessValueDetails?.shortId && (
                                  <span className="ml-2 text-xs bg-blue-200 text-blue-800 px-2 py-1 rounded">
                                    {event.businessValueDetails.shortId}
                                  </span>
                                )}
                              </p>
                            </div>
                            <div>
                              <h5 className="text-sm font-medium text-blue-800 mb-2">Biens Supports</h5>
                              {event.businessValueDetails?.supportAssets?.length > 0 ? (
                                <ul className="text-sm text-blue-700 space-y-1">
                                  {event.businessValueDetails.supportAssets.slice(0, 3).map((asset, index) => (
                                    <li key={index} className="flex items-center">
                                      <span className="w-2 h-2 bg-blue-400 rounded-full mr-2"></span>
                                      {asset.name}
                                      {asset.type && (
                                        <span className="ml-2 text-xs bg-blue-200 text-blue-800 px-1 py-0.5 rounded">
                                          {asset.type}
                                        </span>
                                      )}
                                    </li>
                                  ))}
                                  {event.businessValueDetails.supportAssets.length > 3 && (
                                    <li className="text-xs text-blue-600">
                                      ... et {event.businessValueDetails.supportAssets.length - 3} autre(s)
                                    </li>
                                  )}
                                </ul>
                              ) : (
                                <p className="text-sm text-blue-600 italic">Aucun bien support défini</p>
                              )}
                            </div>
                          </div>
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                          <div>
                            <h4 className="font-medium text-gray-900 mb-2">Mesures Atelier 1</h4>
                            {event.a1Measures.length > 0 ? (
                              <ul className="space-y-1">
                                {event.a1Measures.map((measure, index) => {
                                  // Debug: log measure structure in development
                                  if (process.env.NODE_ENV === 'development') {
                                    console.log('A1 Measure structure:', measure);
                                  }

                                  // Try different property names for the measure name
                                  const measureName = measure.controlName ||
                                                    measure.name ||
                                                    measure.title ||
                                                    measure.description ||
                                                    measure.control ||
                                                    measure.measure ||
                                                    `Mesure ${index + 1}`;

                                  // Try different property names for the strategy/type
                                  const measureType = measure.strategy ||
                                                    measure.type ||
                                                    measure.category ||
                                                    measure.controlType;

                                  // Get decision status and responsible person
                                  const decision = measure.decision;
                                  const responsiblePerson = measure.responsiblePerson;
                                  const decisionDate = measure.decisionDate;

                                  // Determine status display
                                  let statusDisplay = '';
                                  let statusClass = 'bg-gray-100 text-gray-800';

                                  if (decision === 'planned') {
                                    statusDisplay = 'Planifié';
                                    statusClass = 'bg-blue-100 text-blue-800';
                                  } else if (decision === 'apply_before') {
                                    statusDisplay = decisionDate ? `À appliquer avant ${new Date(decisionDate).toLocaleDateString('fr-FR')}` : 'À appliquer';
                                    statusClass = 'bg-orange-100 text-orange-800';
                                  } else if (decision === 'applied') {
                                    statusDisplay = 'Appliqué';
                                    statusClass = 'bg-green-100 text-green-800';
                                  } else if (decision === 'rejected') {
                                    statusDisplay = 'Rejeté';
                                    statusClass = 'bg-red-100 text-red-800';
                                  }

                                  return (
                                    <li key={index} className="text-sm text-gray-600 mb-2">
                                      <div className="flex flex-col">
                                        <div className="flex items-center">
                                          • <span className="font-medium">{measureName}</span>
                                          {statusDisplay && (
                                            <span className={`ml-2 px-2 py-1 text-xs rounded ${statusClass}`}>
                                              {statusDisplay}
                                            </span>
                                          )}
                                        </div>
                                        {responsiblePerson && (
                                          <div className="text-xs text-gray-500 ml-2 mt-1">
                                            Responsable: {responsiblePerson}
                                          </div>
                                        )}
                                        {measureType && (
                                          <div className="text-xs text-gray-500 ml-2">
                                            Stratégie: {measureType}
                                          </div>
                                        )}
                                      </div>
                                    </li>
                                  );
                                })}
                              </ul>
                            ) : (
                              <p className="text-sm text-gray-500 italic">Aucune mesure définie</p>
                            )}
                          </div>
                          <div>
                            <h4 className="font-medium text-gray-900 mb-2">Mesures Atelier 3</h4>
                            {event.a3Measures.length > 0 ? (
                              <ul className="space-y-1">
                                {event.a3Measures.map((measure, index) => (
                                  <li key={index} className="text-sm text-gray-600">
                                    • {measure.title}
                                    <span className={`ml-2 px-2 py-1 text-xs rounded ${
                                      measure.implementation === 'applied' ? 'bg-green-100 text-green-800' :
                                      measure.implementation === 'in-progress' ? 'bg-yellow-100 text-yellow-800' :
                                      'bg-gray-100 text-gray-800'
                                    }`}>
                                      {measure.implementation === 'applied' ? 'Appliqué' :
                                       measure.implementation === 'in-progress' ? 'En cours' : 'Planifié'}
                                    </span>
                                  </li>
                                ))}
                              </ul>
                            ) : (
                              <p className="text-sm text-gray-500 italic">Aucune mesure définie</p>
                            )}
                          </div>
                        </div>
                      </td>
                    </tr>
                  )}
                </React.Fragment>
              ))}
            </tbody>
          </table>
        </div>

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="px-6 py-4 border-t border-gray-200 bg-gray-50">
            <div className="flex items-center justify-between">
              <div className="text-sm text-gray-700">
                Page {currentPage} sur {totalPages}
              </div>
              <div className="flex items-center space-x-2">
                <button
                  onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                  disabled={currentPage === 1}
                  className={`px-3 py-2 text-sm font-medium rounded-md ${
                    currentPage === 1
                      ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                      : 'bg-white text-gray-700 border border-gray-300 hover:bg-gray-50'
                  }`}
                >
                  <ChevronLeft className="h-4 w-4" />
                </button>

                {/* Page numbers */}
                {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                  let pageNum;
                  if (totalPages <= 5) {
                    pageNum = i + 1;
                  } else if (currentPage <= 3) {
                    pageNum = i + 1;
                  } else if (currentPage >= totalPages - 2) {
                    pageNum = totalPages - 4 + i;
                  } else {
                    pageNum = currentPage - 2 + i;
                  }

                  return (
                    <button
                      key={pageNum}
                      onClick={() => setCurrentPage(pageNum)}
                      className={`px-3 py-2 text-sm font-medium rounded-md ${
                        currentPage === pageNum
                          ? 'bg-blue-600 text-white'
                          : 'bg-white text-gray-700 border border-gray-300 hover:bg-gray-50'
                      }`}
                    >
                      {pageNum}
                    </button>
                  );
                })}

                <button
                  onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                  disabled={currentPage === totalPages}
                  className={`px-3 py-2 text-sm font-medium rounded-md ${
                    currentPage === totalPages
                      ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                      : 'bg-white text-gray-700 border border-gray-300 hover:bg-gray-50'
                  }`}
                >
                  <ChevronRightIcon className="h-4 w-4" />
                </button>
              </div>
            </div>
          </div>
        )}
      </div>


    </div>
  );
};

export default EventProgressionTable;
