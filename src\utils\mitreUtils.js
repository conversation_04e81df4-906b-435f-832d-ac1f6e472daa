// src/utils/mitreUtils.js
// Utility functions for MITRE ATT&CK technique handling

/**
 * Generate correct MITRE ATT&CK URL for techniques and sub-techniques
 * @param {string} techniqueId - The technique ID (e.g., T1011, T1011.001)
 * @returns {string} - Correct MITRE ATT&CK URL
 */
export function generateMitreUrl(techniqueId) {
  if (!techniqueId || !techniqueId.startsWith('T')) {
    return 'https://attack.mitre.org/techniques/';
  }

  // Handle sub-techniques (e.g., T1011.001)
  if (techniqueId.includes('.')) {
    const [mainTechnique, subTechnique] = techniqueId.split('.');
    return `https://attack.mitre.org/techniques/${mainTechnique}/${subTechnique}/`;
  }
  
  // Handle main techniques (e.g., T1011)
  return `https://attack.mitre.org/techniques/${techniqueId}/`;
}

/**
 * Parse technique ID to get main technique and sub-technique
 * @param {string} techniqueId - The technique ID
 * @returns {object} - Object with mainTechnique and subTechnique
 */
export function parseTechniqueId(techniqueId) {
  if (!techniqueId || !techniqueId.startsWith('T')) {
    return { mainTechnique: null, subTechnique: null, isSubTechnique: false };
  }

  if (techniqueId.includes('.')) {
    const [mainTechnique, subTechnique] = techniqueId.split('.');
    return { 
      mainTechnique, 
      subTechnique, 
      isSubTechnique: true,
      fullId: techniqueId
    };
  }

  return { 
    mainTechnique: techniqueId, 
    subTechnique: null, 
    isSubTechnique: false,
    fullId: techniqueId
  };
}

/**
 * Format technique display name with proper hierarchy
 * @param {object} technique - Technique object with id and name
 * @returns {string} - Formatted display name
 */
export function formatTechniqueDisplay(technique) {
  if (!technique || !technique.id) {
    return 'Unknown Technique';
  }

  const parsed = parseTechniqueId(technique.id);
  
  if (parsed.isSubTechnique) {
    return `${technique.id} (${technique.name})`;
  }
  
  return `${technique.id} (${technique.name})`;
}

/**
 * Get technique color based on tactic
 * @param {string} tactic - MITRE ATT&CK tactic
 * @returns {string} - CSS color class or hex color
 */
export function getTechniqueColor(tactic) {
  const tacticColors = {
    'Reconnaissance': '#8B5A3C',
    'Resource Development': '#8B5A3C',
    'Initial Access': '#FF6B6B',
    'Execution': '#4ECDC4',
    'Persistence': '#45B7D1',
    'Privilege Escalation': '#96CEB4',
    'Defense Evasion': '#FFEAA7',
    'Credential Access': '#DDA0DD',
    'Discovery': '#98D8C8',
    'Lateral Movement': '#F7DC6F',
    'Collection': '#BB8FCE',
    'Command and Control': '#85C1E9',
    'Exfiltration': '#F8C471',
    'Impact': '#EC7063'
  };

  return tacticColors[tactic] || '#95A5A6';
}

/**
 * Get technique icon based on tactic
 * @param {string} tactic - MITRE ATT&CK tactic
 * @returns {string} - Icon name or emoji
 */
export function getTechniqueIcon(tactic) {
  const tacticIcons = {
    'Reconnaissance': '🔍',
    'Resource Development': '🛠️',
    'Initial Access': '🚪',
    'Execution': '⚡',
    'Persistence': '🔒',
    'Privilege Escalation': '⬆️',
    'Defense Evasion': '🥷',
    'Credential Access': '🔑',
    'Discovery': '🗺️',
    'Lateral Movement': '↔️',
    'Collection': '📦',
    'Command and Control': '📡',
    'Exfiltration': '📤',
    'Impact': '💥'
  };

  return tacticIcons[tactic] || '🎯';
}

/**
 * Validate technique ID format
 * @param {string} techniqueId - The technique ID to validate
 * @returns {boolean} - True if valid format
 */
export function isValidTechniqueId(techniqueId) {
  if (!techniqueId || typeof techniqueId !== 'string') {
    return false;
  }

  // Main technique pattern: T followed by 4 digits
  const mainPattern = /^T\d{4}$/;
  
  // Sub-technique pattern: T followed by 4 digits, dot, then 3 digits
  const subPattern = /^T\d{4}\.\d{3}$/;

  return mainPattern.test(techniqueId) || subPattern.test(techniqueId);
}

/**
 * Sort techniques by tactic order (following MITRE ATT&CK framework order)
 * @param {array} techniques - Array of technique objects
 * @returns {array} - Sorted techniques
 */
export function sortTechniquesByTactic(techniques) {
  const tacticOrder = [
    'Reconnaissance',
    'Resource Development', 
    'Initial Access',
    'Execution',
    'Persistence',
    'Privilege Escalation',
    'Defense Evasion',
    'Credential Access',
    'Discovery',
    'Lateral Movement',
    'Collection',
    'Command and Control',
    'Exfiltration',
    'Impact'
  ];

  return techniques.sort((a, b) => {
    const tacticA = tacticOrder.indexOf(a.tactic);
    const tacticB = tacticOrder.indexOf(b.tactic);
    
    // If tactic not found, put at end
    const indexA = tacticA === -1 ? tacticOrder.length : tacticA;
    const indexB = tacticB === -1 ? tacticOrder.length : tacticB;
    
    if (indexA !== indexB) {
      return indexA - indexB;
    }
    
    // If same tactic, sort by technique ID
    return a.id.localeCompare(b.id);
  });
}

/**
 * Group techniques by tactic
 * @param {array} techniques - Array of technique objects
 * @returns {object} - Object with tactics as keys and technique arrays as values
 */
export function groupTechniquesByTactic(techniques) {
  const grouped = {};
  
  techniques.forEach(technique => {
    const tactic = technique.tactic || 'Unknown';
    if (!grouped[tactic]) {
      grouped[tactic] = [];
    }
    grouped[tactic].push(technique);
  });
  
  // Sort techniques within each tactic
  Object.keys(grouped).forEach(tactic => {
    grouped[tactic].sort((a, b) => a.id.localeCompare(b.id));
  });
  
  return grouped;
}

/**
 * Create technique badge component data
 * @param {object} technique - Technique object
 * @returns {object} - Badge component data
 */
export function createTechniqueBadge(technique) {
  if (!technique || !technique.id) {
    return {
      id: 'unknown',
      name: 'Unknown',
      displayText: 'Unknown Technique',
      color: '#95A5A6',
      icon: '❓',
      url: 'https://attack.mitre.org/techniques/',
      isValid: false
    };
  }

  return {
    id: technique.id,
    name: technique.name || 'Unknown',
    displayText: formatTechniqueDisplay(technique),
    color: getTechniqueColor(technique.tactic),
    icon: getTechniqueIcon(technique.tactic),
    url: generateMitreUrl(technique.id),
    isValid: isValidTechniqueId(technique.id),
    tactic: technique.tactic || 'Unknown',
    description: technique.description || 'No description available'
  };
}

/**
 * Extract technique IDs from text (useful for parsing scenario descriptions)
 * @param {string} text - Text to search for technique IDs
 * @returns {array} - Array of found technique IDs
 */
export function extractTechniqueIds(text) {
  if (!text || typeof text !== 'string') {
    return [];
  }

  // Pattern to match technique IDs in text
  const pattern = /T\d{4}(?:\.\d{3})?/g;
  const matches = text.match(pattern) || [];
  
  // Remove duplicates and validate
  return [...new Set(matches)].filter(id => isValidTechniqueId(id));
}

/**
 * Create clickable technique links for text
 * @param {string} text - Text containing technique IDs
 * @returns {string} - HTML string with clickable links
 */
export function createTechniqueLinks(text) {
  if (!text || typeof text !== 'string') {
    return text;
  }

  return text.replace(/T\d{4}(?:\.\d{3})?/g, (match) => {
    if (isValidTechniqueId(match)) {
      const url = generateMitreUrl(match);
      return `<a href="${url}" target="_blank" rel="noopener noreferrer" class="technique-link">${match}</a>`;
    }
    return match;
  });
}

export default {
  generateMitreUrl,
  parseTechniqueId,
  formatTechniqueDisplay,
  getTechniqueColor,
  getTechniqueIcon,
  isValidTechniqueId,
  sortTechniquesByTactic,
  groupTechniquesByTactic,
  createTechniqueBadge,
  extractTechniqueIds,
  createTechniqueLinks
};
