import React from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { authService } from '../../services/authService';

const ProtectedRoute = ({ children, requiredRole }) => {
  const location = useLocation();
  const isAuthenticated = authService.isAuthenticated();
  const hasRequiredRole = authService.hasRole(requiredRole);

  if (!isAuthenticated) {
    // Redirect to login if not authenticated
    return <Navigate to="/login" state={{ from: location }} replace />;
  }

  if (!hasRequiredRole) {
    // Redirect to appropriate dashboard if user doesn't have required role
    return <Navigate to={authService.getDashboardPath()} replace />;
  }

  return children;
};

export default ProtectedRoute; 