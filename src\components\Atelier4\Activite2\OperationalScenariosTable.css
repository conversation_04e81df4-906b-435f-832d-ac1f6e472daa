/* src/components/Atelier4/Activite2/OperationalScenariosTable.css */

.scenarios-table-container {
  padding: 20px;
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: #6c757d;
}

.empty-state i {
  font-size: 3rem;
  margin-bottom: 20px;
  color: #dee2e6;
}

.empty-state h3 {
  margin: 0 0 10px 0;
  color: #495057;
}

.empty-state p {
  margin: 0;
  font-size: 1rem;
}

.table-header {
  margin-bottom: 20px;
}

.table-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 15px;
}

.select-all {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
  color: #495057;
  cursor: pointer;
}

.select-all input[type="checkbox"] {
  margin: 0;
}

.bulk-actions {
  display: flex;
  align-items: center;
  gap: 15px;
}

.selection-count {
  color: #007bff;
  font-weight: 500;
  font-size: 0.9rem;
}

.btn-sm {
  padding: 6px 12px;
  font-size: 0.8rem;
}

.btn-danger {
  background: #dc3545;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 6px;
  transition: all 0.2s ease;
}

.btn-danger:hover {
  background: #c82333;
  transform: translateY(-1px);
}

.table-wrapper {
  overflow-x: auto;
  border-radius: 8px;
  border: 1px solid #dee2e6;
}

.scenarios-table {
  width: 100%;
  border-collapse: collapse;
  background: white;
}

.scenarios-table th,
.scenarios-table td {
  padding: 12px;
  text-align: left;
  border-bottom: 1px solid #dee2e6;
  vertical-align: top;
}

.scenarios-table th {
  background: #f8f9fa;
  font-weight: 600;
  color: #495057;
  position: sticky;
  top: 0;
  z-index: 10;
}

.scenarios-table th.sortable {
  cursor: pointer;
  user-select: none;
  transition: background-color 0.2s ease;
}

.scenarios-table th.sortable:hover {
  background: #e9ecef;
}

.scenarios-table th.sortable i {
  margin-left: 5px;
  color: #007bff;
}

.select-column {
  width: 40px;
  text-align: center;
}

.actions-column {
  width: 100px;
  text-align: center;
}

.scenarios-table tr.selected {
  background: #e3f2fd;
}

.scenarios-table tr:hover:not(.expanded-row) {
  background: #f8f9fa;
}

.scenario-name {
  min-width: 250px;
}

.name-content {
  display: flex;
  align-items: center;
  gap: 8px;
}

.expand-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  color: #6c757d;
  transition: all 0.2s ease;
}

.expand-btn:hover {
  background: #e9ecef;
  color: #495057;
}

.name {
  font-weight: 500;
  color: #495057;
}

.ai-badge {
  background: #17a2b8;
  color: white;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 0.7rem;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 3px;
}

.severity-badge,
.likelihood-badge {
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.8rem;
  font-weight: 500;
  text-transform: capitalize;
}

.skills-badge {
  background: #6f42c1;
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.8rem;
  font-weight: 500;
  text-transform: capitalize;
}

.attack-path {
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.action-buttons {
  display: flex;
  gap: 5px;
  justify-content: center;
}

.btn-primary {
  background: #007bff;
  color: white;
}

.btn-primary:hover {
  background: #0056b3;
}

.expanded-row {
  background: #f8f9fa !important;
}

.expanded-row:hover {
  background: #f8f9fa !important;
}

.scenario-details {
  padding: 20px;
  border-left: 4px solid #007bff;
}

.description-section,
.steps-section,
.metadata-section {
  margin-bottom: 25px;
}

.description-section h4,
.steps-section h4 {
  margin: 0 0 10px 0;
  color: #495057;
  font-size: 1.1rem;
  font-weight: 600;
}

.description-section p {
  margin: 0;
  color: #6c757d;
  line-height: 1.5;
}

.steps-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.step-item {
  background: white;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  padding: 15px;
}

.step-header {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 10px;
}

.step-number {
  background: #007bff;
  color: white;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.8rem;
  font-weight: 600;
  flex-shrink: 0;
}

.step-header h5 {
  margin: 0;
  flex: 1;
  color: #495057;
  font-size: 1rem;
}

.step-duration {
  background: #e9ecef;
  color: #6c757d;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 0.8rem;
  font-weight: 500;
}

.step-description {
  margin: 0 0 15px 0;
  color: #6c757d;
  line-height: 1.5;
}

.step-details {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.detail-group {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.detail-group strong {
  color: #495057;
  font-size: 0.9rem;
}

.techniques,
.indicators {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
}

.technique-tag,
.indicator-tag {
  background: #e9ecef;
  color: #495057;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 0.8rem;
}

.technique-tag {
  background: #d1ecf1;
  color: #0c5460;
}

.indicator-tag {
  background: #f8d7da;
  color: #721c24;
}

.no-steps {
  color: #6c757d;
  font-style: italic;
  margin: 0;
}

.metadata-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 15px;
}

.metadata-item {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.metadata-item strong {
  color: #495057;
  font-size: 0.9rem;
}

.metadata-item span {
  color: #6c757d;
  text-transform: capitalize;
}

/* Edit Modal */
.edit-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.edit-modal {
  background: white;
  border-radius: 12px;
  width: 90%;
  max-width: 600px;
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.modal-header {
  padding: 20px;
  border-bottom: 1px solid #dee2e6;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-header h3 {
  margin: 0;
  color: #495057;
}

.close-btn {
  background: none;
  border: none;
  font-size: 1.2rem;
  cursor: pointer;
  color: #6c757d;
  padding: 5px;
  border-radius: 4px;
}

.close-btn:hover {
  background: #f8f9fa;
  color: #495057;
}

.modal-body {
  padding: 20px;
  overflow-y: auto;
  flex: 1;
}

.form-group {
  margin-bottom: 20px;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
  color: #495057;
}

.form-group input,
.form-group textarea,
.form-group select {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ced4da;
  border-radius: 6px;
  font-size: 0.9rem;
  transition: border-color 0.2s ease;
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.modal-footer {
  padding: 20px;
  border-top: 1px solid #dee2e6;
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.btn-secondary {
  background: #6c757d;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-secondary:hover {
  background: #545b62;
}

/* Responsive design */
@media (max-width: 768px) {
  .scenarios-table-container {
    padding: 15px;
  }
  
  .table-controls {
    flex-direction: column;
    align-items: stretch;
  }
  
  .bulk-actions {
    justify-content: space-between;
  }
  
  .scenarios-table th,
  .scenarios-table td {
    padding: 8px;
    font-size: 0.9rem;
  }
  
  .scenario-name {
    min-width: 200px;
  }
  
  .attack-path {
    max-width: 150px;
  }
  
  .step-details {
    gap: 15px;
  }
  
  .metadata-grid {
    grid-template-columns: 1fr;
  }
  
  .form-row {
    grid-template-columns: 1fr;
  }
  
  .edit-modal {
    width: 95%;
    margin: 10px;
  }
}

@media (max-width: 480px) {
  .name-content {
    flex-direction: column;
    align-items: flex-start;
    gap: 5px;
  }
  
  .action-buttons {
    flex-direction: column;
  }
  
  .step-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .techniques,
  .indicators {
    flex-direction: column;
  }
}