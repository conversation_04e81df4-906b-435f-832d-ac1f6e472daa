// BusinessValuesAdd.js
import React from 'react';
import { useTranslation } from 'react-i18next';

const BusinessValuesAdd = ({
  newBusinessValue,
  setNewBusinessValue,
  newBusinessValueDescription,
  setNewBusinessValueDescription,
  handleAddBusinessValue,
  onClose
}) => {
  const { t } = useTranslation();

  const handleAddClick = () => {
    if (newBusinessValue.trim()) {
      handleAddBusinessValue();
    }
  };

  const handleKeyPress = (e) => {
    if (e.key === 'Enter') {
      handleAddClick();
    }
  };

  return (
    <div className="flex flex-col space-y-3">
      <div>
        <label htmlFor="businessValueName" className="block text-sm font-medium text-gray-700 mb-1">{t('businessValues.fields.name')}</label>
        <input
          id="businessValueName"
          type="text"
          className="w-full p-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
          placeholder={t('businessValues.placeholders.businessValueName')}
          value={newBusinessValue}
          onChange={(e) => setNewBusinessValue(e.target.value)}
          onKeyPress={handleKeyPress}
        />
      </div>

      <div>
        <label htmlFor="businessValueDescription" className="block text-sm font-medium text-gray-700 mb-1">{t('businessValues.fields.description')}</label>
        <textarea
          id="businessValueDescription"
          className="w-full p-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
          placeholder={t('businessValues.placeholders.businessValueDescription')}
          rows="3"
          value={newBusinessValueDescription}
          onChange={(e) => setNewBusinessValueDescription(e.target.value)}
          onKeyPress={handleKeyPress}
        />
      </div>

      <div className="flex justify-end space-x-2 pt-2">
          <button
              type="button"
              className="bg-white text-gray-700 px-4 py-2 rounded-md border border-gray-300 hover:bg-gray-50"
              onClick={onClose}
          >
              {t('businessValues.buttons.cancel')}
          </button>
          <button
            type="button"
            className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 disabled:opacity-50"
            onClick={handleAddClick}
            disabled={!newBusinessValue.trim()}
          >
            {t('businessValues.buttons.addValue')}
          </button>
      </div>
    </div>
  );
};

export default BusinessValuesAdd;