// src/components/Atelier4/Activite1/OperationalScenarios.js
import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useAnalysis } from '../../../context/AnalysisContext';
import { api } from '../../../api/apiClient';
import OperationalScenariosTable from './OperationalScenariosTable';
import OperationalScenariosVisualization from './OperationalScenariosVisualization';
import AIGenerationModal from './AIGenerationModal';
import { Bot, Download, Table, BarChart3, AlertCircle, Target, Info, Save, Play } from 'lucide-react';

const cardVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: { opacity: 1, y: 0, transition: { duration: 0.5 } },
};

const OperationalScenarios = ({ attackPaths = [] }) => {
  const { currentAnalysis } = useAnalysis();
  const [operationalScenarios, setOperationalScenarios] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [showAIModal, setShowAIModal] = useState(false);
  const [activeView, setActiveView] = useState('table'); // 'table' or 'visualization'
  const [selectedScenarios, setSelectedScenarios] = useState([]);

  // Load operational scenarios on component mount
  useEffect(() => {
    if (currentAnalysis?.id) {
      loadOperationalScenarios();
    }
  }, [currentAnalysis?.id]);

  const loadOperationalScenarios = async () => {
    try {
      setLoading(true);
      setError(null);

      // Load existing operational scenarios
      const scenariosResponse = await api.get(`/analyses/${currentAnalysis.id}/operational-scenarios`);
      if (scenariosResponse?.success && scenariosResponse?.data) {
        setOperationalScenarios(scenariosResponse.data);
      }

    } catch (err) {
      console.error('Error loading operational scenarios:', err);
      setError('Erreur lors du chargement des scénarios opérationnels');
    } finally {
      setLoading(false);
    }
  };

  const handleGenerateScenarios = async (selectedAttackPaths, options = {}) => {
    try {
      setLoading(true);

      // Prepare attack paths data for AI with complete details
      const attackPathsData = attackPaths.filter(path =>
        selectedAttackPaths.includes(path.id)
      ).map((path, index) => ({
        ...path,
        // Ensure reference code is included for AI context
        referenceCode: path.referenceCode || `CA${String(index + 1).padStart(2, '0')}`,
        // Include all relevant details for AI context
        sourceRiskName: path.sourceRiskName || path.name,
        targetObjective: path.objectifVise || path.targetObjective,
        dreadedEventName: path.dreadedEventName,
        description: path.description
      }));

      console.log('=== AI GENERATION REQUEST ===');
      console.log('Attack Paths Data for AI:', attackPathsData);
      console.log('Options:', options);

      // Call AI service to generate operational scenarios
      const response = await api.post('/ai/generate-operational-scenarios', {
        analysisId: currentAnalysis.id,
        attackPaths: attackPathsData, // Send complete attack path details
        existingScenarios: operationalScenarios,
        options: {
          ...options,
          // Ensure AI includes attack path reference in response
          includeAttackPathReference: true
        }
      });

      console.log('=== AI GENERATION RESPONSE ===');
      console.log('Response:', response);

      if (response?.data?.scenarios) {
        // Process generated scenarios to ensure attack path association
        const newScenarios = response.data.scenarios.map(scenario => {
          // Find the corresponding attack path for reference code
          const associatedPath = attackPathsData.find(path =>
            path.id === scenario.attackPathId ||
            path.referenceCode === scenario.attackPathReference
          );

          return {
            ...scenario,
            id: `scenario_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
            createdAt: new Date().toISOString(),
            source: 'ai_generated',
            // Ensure attack path association is maintained
            attackPathId: scenario.attackPathId || associatedPath?.id,
            attackPathReference: scenario.attackPathReference || associatedPath?.referenceCode,
            // Include attack path context in scenario
            attackPathContext: {
              referenceCode: associatedPath?.referenceCode,
              sourceRiskName: associatedPath?.sourceRiskName,
              targetObjective: associatedPath?.targetObjective
            }
          };
        });

        console.log('=== PROCESSED SCENARIOS ===');
        console.log('New Scenarios:', newScenarios);

        setOperationalScenarios(prev => [...prev, ...newScenarios]);

        // Save to backend
        await saveScenarios([...operationalScenarios, ...newScenarios]);
      }

      setShowAIModal(false);
    } catch (err) {
      console.error('Error generating scenarios:', err);
      setError('Erreur lors de la génération des scénarios opérationnels');
    } finally {
      setLoading(false);
    }
  };

  const saveScenarios = async (scenarios) => {
    try {
      await api.post(`/analyses/${currentAnalysis.id}/operational-scenarios/bulk`, {
        scenarios
      });
    } catch (err) {
      console.error('Error saving scenarios:', err);
      throw err;
    }
  };

  const handleScenarioUpdate = async (scenarioId, updates) => {
    try {
      const updatedScenarios = operationalScenarios.map(scenario =>
        scenario.id === scenarioId ? { ...scenario, ...updates } : scenario
      );

      setOperationalScenarios(updatedScenarios);
      await saveScenarios(updatedScenarios);
    } catch (err) {
      console.error('Error updating scenario:', err);
      setError('Erreur lors de la mise à jour du scénario');
    }
  };

  const handleScenarioDelete = async (scenarioId) => {
    try {
      const updatedScenarios = operationalScenarios.filter(scenario => scenario.id !== scenarioId);
      setOperationalScenarios(updatedScenarios);
      await saveScenarios(updatedScenarios);
    } catch (err) {
      console.error('Error deleting scenario:', err);
      setError('Erreur lors de la suppression du scénario');
    }
  };

  const handleExportScenarios = () => {
    const dataStr = JSON.stringify(operationalScenarios, null, 2);
    const dataUri = 'data:application/json;charset=utf-8,'+ encodeURIComponent(dataStr);

    const exportFileDefaultName = `scenarios_operationnels_${currentAnalysis?.name || 'analyse'}_${new Date().toISOString().split('T')[0]}.json`;

    const linkElement = document.createElement('a');
    linkElement.setAttribute('href', dataUri);
    linkElement.setAttribute('download', exportFileDefaultName);
    linkElement.click();
  };

  // Show loading state
  if (loading && operationalScenarios.length === 0) {
    return (
      <div className="flex justify-center items-center h-64 bg-slate-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-slate-600">Chargement des scénarios opérationnels...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Action Buttons */}
      <div className="bg-white rounded-xl shadow-sm border border-slate-100 p-4">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div>
            <h3 className="text-lg font-semibold text-gray-900">Actions disponibles</h3>
            <p className="text-sm text-gray-600 mt-1">
              Générez et gérez vos scénarios opérationnels
            </p>
          </div>

          <div className="flex items-center gap-2 flex-wrap">
            <button
              onClick={() => setShowAIModal(true)}
              disabled={attackPaths.length === 0}
              className="text-sm font-medium bg-purple-600 text-white px-4 py-2 rounded-md hover:bg-purple-700 disabled:bg-gray-300 disabled:cursor-not-allowed flex items-center shadow-sm transition duration-200"
              title="Générer des scénarios avec l'IA"
            >
              <Bot size={16} className="mr-2" />
              Générer avec IA
            </button>

            <button
              onClick={handleExportScenarios}
              disabled={operationalScenarios.length === 0}
              className="text-sm font-medium bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 disabled:bg-gray-300 disabled:cursor-not-allowed flex items-center shadow-sm transition duration-200"
              title="Exporter les scénarios"
            >
              <Download size={16} className="mr-2" />
              Exporter
            </button>

            <button
              className="text-sm font-medium bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 flex items-center shadow-sm transition duration-200"
              title="Aide sur les scénarios opérationnels"
            >
              <Info size={16} className="mr-2" />
              Aide
            </button>
          </div>
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-red-100 border border-red-300 text-red-800 p-4 rounded-lg mb-4 shadow-sm"
        >
          <div className="flex items-center">
            <AlertCircle size={20} className="text-red-600 mr-3 flex-shrink-0" />
            <span className="text-red-700 flex-1">{error}</span>
            <button
              onClick={() => setError(null)}
              className="ml-3 text-red-600 hover:text-red-800 font-bold text-lg leading-none"
              title="Fermer"
            >
              ×
            </button>
          </div>
        </motion.div>
      )}

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 mb-6">
        <motion.div
          variants={cardVariants}
          initial="hidden"
          animate="visible"
          className="bg-white p-4 rounded-xl shadow-sm border border-slate-100"
        >
          <div className="flex items-center">
            <div className="p-2 bg-blue-100 rounded-lg">
              <Play size={20} className="text-blue-600" />
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-slate-600">Scénarios</p>
              <p className="text-2xl font-bold text-slate-900">{operationalScenarios.length}</p>
            </div>
          </div>
        </motion.div>

        <motion.div
          variants={cardVariants}
          initial="hidden"
          animate="visible"
          className="bg-white p-4 rounded-xl shadow-sm border border-slate-100"
        >
          <div className="flex items-center">
            <div className="p-2 bg-green-100 rounded-lg">
              <Target size={20} className="text-green-600" />
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-slate-600">Chemins d'attaque</p>
              <p className="text-2xl font-bold text-slate-900">{attackPaths.length}</p>
            </div>
          </div>
        </motion.div>

        <motion.div
          variants={cardVariants}
          initial="hidden"
          animate="visible"
          className="bg-white p-4 rounded-xl shadow-sm border border-slate-100"
        >
          <div className="flex items-center">
            <div className="p-2 bg-purple-100 rounded-lg">
              <Bot size={20} className="text-purple-600" />
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-slate-600">Générés par IA</p>
              <p className="text-2xl font-bold text-slate-900">
                {operationalScenarios.filter(s => s.source === 'ai_generated').length}
              </p>
            </div>
          </div>
        </motion.div>
      </div>

      {/* View Controls */}
      <motion.div
        variants={cardVariants}
        initial="hidden"
        animate="visible"
        className="bg-white rounded-xl shadow-sm border border-slate-100 p-4 mb-6"
      >
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div className="flex space-x-1 bg-slate-100 rounded-lg p-1">
            <button
              onClick={() => setActiveView('table')}
              className={`flex items-center px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                activeView === 'table'
                  ? 'bg-white text-slate-900 shadow-sm'
                  : 'text-slate-600 hover:text-slate-900'
              }`}
            >
              <Table size={16} className="mr-2" />
              Vue tableau
            </button>
            <button
              onClick={() => setActiveView('visualization')}
              className={`flex items-center px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                activeView === 'visualization'
                  ? 'bg-white text-slate-900 shadow-sm'
                  : 'text-slate-600 hover:text-slate-900'
              }`}
            >
              <BarChart3 size={16} className="mr-2" />
              Visualisation
            </button>
          </div>

          <div className="flex items-center space-x-4 text-sm text-slate-600">
            <span className="flex items-center">
              <span className="w-3 h-3 bg-blue-500 rounded-full mr-2"></span>
              Vue {activeView === 'table' ? 'tableau' : 'visualisation'}
            </span>
            {loading && (
              <span className="flex items-center">
                <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-blue-600 mr-2"></div>
                Chargement...
              </span>
            )}
          </div>
        </div>
      </motion.div>

      {/* Content */}
      <motion.div
        variants={cardVariants}
        initial="hidden"
        animate="visible"
        className="bg-white rounded-xl shadow-sm border border-slate-100 overflow-hidden"
      >
        {operationalScenarios.length === 0 && !loading ? (
          <div className="p-12 text-center">
            <div className="mx-auto w-24 h-24 bg-slate-100 rounded-full flex items-center justify-center mb-4">
              <Play size={32} className="text-slate-400" />
            </div>
            <h3 className="text-lg font-semibold text-slate-800 mb-2">Aucun scénario opérationnel</h3>
            <p className="text-slate-600 mb-6 max-w-md mx-auto">
              Commencez par générer des scénarios opérationnels à partir des chemins d'attaque disponibles.
            </p>
            <button
              onClick={() => setShowAIModal(true)}
              disabled={attackPaths.length === 0}
              className="inline-flex items-center px-6 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors font-medium"
            >
              <Bot size={20} className="mr-2" />
              Générer des scénarios avec l'IA
            </button>
            {attackPaths.length === 0 && (
              <p className="text-sm text-slate-500 mt-3">
                Vous devez d'abord créer des chemins d'attaque dans les ateliers précédents.
              </p>
            )}
          </div>
        ) : (
          <>
            {activeView === 'table' ? (
              <OperationalScenariosTable
                scenarios={operationalScenarios}
                attackPaths={attackPaths}
                onScenarioUpdate={handleScenarioUpdate}
                onScenarioDelete={handleScenarioDelete}
                selectedScenarios={selectedScenarios}
                onSelectionChange={setSelectedScenarios}
              />
            ) : (
              <div className="h-[600px]">
                <OperationalScenariosVisualization
                  scenarios={operationalScenarios}
                  attackPaths={attackPaths}
                  selectedScenarios={selectedScenarios}
                  onScenarioSelect={setSelectedScenarios}
                />
              </div>
            )}
          </>
        )}
      </motion.div>

      {/* AI Generation Modal */}
      {showAIModal && (
        <AIGenerationModal
          attackPaths={attackPaths}
          existingScenarios={operationalScenarios}
          onGenerate={handleGenerateScenarios}
          onClose={() => setShowAIModal(false)}
        />
      )}
    </div>
  );
};

export default OperationalScenarios;
