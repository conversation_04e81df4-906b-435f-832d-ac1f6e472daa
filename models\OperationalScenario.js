// models/OperationalScenario.js
const mongoose = require('mongoose');

// Schema for individual operational steps
const OperationalStepSchema = new mongoose.Schema({
  phase: {
    type: String,
    enum: ['CONNAITRE', 'RENTRER', 'TROUVER', 'EXPLOITER'],
    required: true
  },
  stepNumber: {
    type: Number,
    required: true
  },
  technique: {
    type: String,
    required: true,
    trim: true
  },
  description: {
    type: String,
    required: true,
    trim: true
  },
  indicators: [{
    type: String,
    trim: true
  }],
  tools: [{
    type: String,
    trim: true
  }],
  duration: {
    type: String,
    trim: true
  },
  difficulty: {
    type: String,
    enum: ['Très faible', 'Faible', 'Moyen', 'Élevé', 'Très élevé'],
    default: 'Moyen'
  }
}, {
  _id: true
});

// Main operational scenario schema
const OperationalScenarioSchema = new mongoose.Schema({
  analysisId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Analysis',
    required: true,
    index: true
  },
  attackPathId: {
    type: String,
    required: true,
    index: true
  },
  name: {
    type: String,
    required: true,
    trim: true
  },
  description: {
    type: String,
    required: true,
    trim: true
  },
  severity: {
    type: String,
    enum: ['Très faible', 'Faible', 'Moyen', 'Élevé', 'Très élevé'],
    required: true,
    default: 'Moyen'
  },
  likelihood: {
    type: String,
    enum: ['Très faible', 'Faible', 'Moyen', 'Élevé', 'Très élevé'],
    required: true,
    default: 'Moyen'
  },
  requiredSkills: {
    type: String,
    enum: ['Débutant', 'Intermédiaire', 'Avancé', 'Expert'],
    required: true,
    default: 'Intermédiaire'
  },
  timeline: {
    type: String,
    required: true,
    trim: true,
    default: '1-7 jours'
  },
  detectionDifficulty: {
    type: String,
    enum: ['Très faible', 'Faible', 'Moyen', 'Élevé', 'Très élevé'],
    required: true,
    default: 'Moyen'
  },
  operationalSteps: [OperationalStepSchema],
  attackPathDetails: {
    sourceRisque: {
      type: String,
      trim: true
    },
    partiesPrenantes: [{
      type: String,
      trim: true
    }],
    objectifVise: {
      type: String,
      trim: true
    }
  },
  metadata: {
    generatedByAI: {
      type: Boolean,
      default: false
    },
    aiModel: {
      type: String,
      trim: true
    },
    generationDate: {
      type: Date
    },
    lastModified: {
      type: Date,
      default: Date.now
    }
  },
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: false
  },
  updatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }
}, {
  timestamps: true
});

// Indexes for performance
OperationalScenarioSchema.index({ analysisId: 1, attackPathId: 1 });
OperationalScenarioSchema.index({ severity: 1, likelihood: 1 });
OperationalScenarioSchema.index({ 'metadata.generatedByAI': 1 });

// Virtual for risk score calculation
OperationalScenarioSchema.virtual('riskScore').get(function() {
  const severityMap = { 'Très faible': 1, 'Faible': 2, 'Moyen': 3, 'Élevé': 4, 'Très élevé': 5 };
  const likelihoodMap = { 'Très faible': 1, 'Faible': 2, 'Moyen': 3, 'Élevé': 4, 'Très élevé': 5 };

  return severityMap[this.severity] * likelihoodMap[this.likelihood];
});

// Method to get scenario summary
OperationalScenarioSchema.methods.getSummary = function() {
  return {
    id: this._id,
    name: this.name,
    severity: this.severity,
    likelihood: this.likelihood,
    riskScore: this.riskScore,
    stepsCount: this.operationalSteps.length,
    timeline: this.timeline
  };
};

module.exports = mongoose.model('OperationalScenario', OperationalScenarioSchema);
