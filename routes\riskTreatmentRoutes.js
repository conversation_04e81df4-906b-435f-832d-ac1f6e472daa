// routes/riskTreatmentRoutes.js
const express = require('express');
const { protect } = require('../middleware/authMiddleware');
const router = express.Router();

// Import the dedicated controller functions
const { getRiskTreatment, saveRiskTreatment } = require('../controllers/riskTreatmentController');

// Risk treatment routes
router.route('/analyses/:analysisId/risk-treatment')
  .get(protect, getRiskTreatment)
  .post(protect, saveRiskTreatment);

module.exports = router;
