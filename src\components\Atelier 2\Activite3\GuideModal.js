// src/components/Atelier 2/Activite3/GuideModal.js
import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { X, HelpCircle, MousePointer, Link, Shield, Target, Activity, Database } from 'lucide-react';
import { useTranslation } from 'react-i18next';

const GuideModal = ({ isOpen, onClose }) => {
  const { t } = useTranslation();

  // If the modal is not open, don't render anything
  if (!isOpen) return null;

  const steps = [
    {
      title: t('workshop2.activity3.guide.selectRiskSources'),
      description: t('workshop2.activity3.guide.selectRiskSourcesDesc'),
      icon: <MousePointer size={20} className="text-blue-500" />
    },
    {
      title: t('workshop2.activity3.guide.associateDreadedEvents'),
      description: t('workshop2.activity3.guide.associateDreadedEventsDesc'),
      icon: <Link size={20} className="text-purple-500" />
    },
    {
      title: t('workshop2.activity3.guide.understandSecurityPillars'),
      description: t('workshop2.activity3.guide.understandSecurityPillarsDesc'),
      icon: <Shield size={20} className="text-orange-500" />
    },
    {
      title: t('workshop2.activity3.guide.evaluateGravity'),
      description: t('workshop2.activity3.guide.evaluateGravityDesc'),
      icon: <Target size={20} className="text-green-500" />
    },
    {
      title: t('workshop2.activity3.guide.consultSummaryTable'),
      description: t('workshop2.activity3.guide.consultSummaryTableDesc'),
      icon: <Activity size={20} className="text-red-500" />
    }
  ];

  return (
    <AnimatePresence>
      {isOpen && (
        <>
          {/* Backdrop */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black bg-opacity-50 z-40 backdrop-blur-sm"
            onClick={onClose}
          />

          {/* Modal */}
          <motion.div
            initial={{ opacity: 0, scale: 0.9, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.9, y: 20 }}
            transition={{ type: "spring", damping: 25, stiffness: 300 }}
            className="fixed inset-0 z-50 flex items-center justify-center p-4 sm:p-0"
            onClick={(e) => e.stopPropagation()}
          >
            <div className="bg-white rounded-2xl shadow-xl w-full max-w-2xl max-h-[90vh] overflow-hidden">
              {/* Header */}
              <div className="bg-gradient-to-r from-blue-600 to-indigo-700 p-6 flex justify-between items-center">
                <div className="flex items-center">
                  <HelpCircle size={24} className="text-white mr-3" />
                  <h2 className="text-xl font-bold text-white">{t('workshop2.activity3.guide.title')}</h2>
                </div>
                <button
                  onClick={onClose}
                  className="text-white hover:bg-white hover:bg-opacity-20 rounded-full p-1 transition-colors duration-200"
                >
                  <X size={24} />
                </button>
              </div>

              {/* Content */}
              <div className="p-6 overflow-y-auto max-h-[calc(90vh-80px)]">
                <div className="space-y-8">
                  {steps.map((step, index) => (
                    <motion.div
                      key={index}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: index * 0.1 }}
                      className="flex"
                    >
                      <div className="flex-shrink-0 mr-4">
                        <div className="flex items-center justify-center w-10 h-10 rounded-full bg-blue-100 text-blue-600 font-bold">
                          {index + 1}
                        </div>
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center mb-2">
                          {step.icon}
                          <h3 className="text-lg font-semibold text-gray-800 ml-2">{step.title}</h3>
                        </div>
                        <p className="text-gray-600">{step.description}</p>
                      </div>
                    </motion.div>
                  ))}

                  <div className="bg-blue-50 border-l-4 border-blue-500 p-4 rounded-md mt-6">
                    <div className="flex">
                      <div className="flex-shrink-0">
                        <Activity className="h-5 w-5 text-blue-500" />
                      </div>
                      <div className="ml-3">
                        <h3 className="text-sm font-medium text-blue-800">{t('workshop2.activity3.guide.tip')}</h3>
                        <div className="mt-2 text-sm text-blue-700">
                          <p>
                            {t('workshop2.activity3.guide.tipMessage')}
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Footer */}
              <div className="border-t border-gray-200 p-4 flex justify-end">
                <button
                  onClick={onClose}
                  className="px-4 py-2 bg-gray-100 hover:bg-gray-200 text-gray-800 rounded-md transition-colors duration-200 mr-2"
                >
{t('common.close')}
                </button>
                <button
                  onClick={onClose}
                  className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md transition-colors duration-200 flex items-center"
                >
                  <Database size={16} className="mr-2" />
{t('workshop2.activity3.guide.start')}
                </button>
              </div>
            </div>
          </motion.div>
        </>
      )}
    </AnimatePresence>
  );
};

export default GuideModal;
