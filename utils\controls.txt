"Measure Description","Pillar","Risk Treatment Strategy"
"Encrypt sensitive data at rest using strong algorithms.","Confidentialité","Réduire le risque"
"Implement strong role-based access control (RBAC) policies.","Confidentialité","Réduire le risque"
"Enforce multi-factor authentication (MFA) for remote access.","Confidentialité","Réduire le risque"
"Use TLS/SSL encryption for all data transmission over networks.","Confidentialité","Réduire le risque"
"Deploy Data Loss Prevention (DLP) solutions on endpoints and network.","Confidentialité","Réduire le risque"
"Regularly train employees on data handling and confidentiality policies.","Confidentialité","Réduire le risque"
"Implement data masking or anonymization in non-production environments.","Confidentialité","Réduire le risque"
"Conduct periodic security audits of access permissions.","Confidentialité","Réduire le risque"
"Enforce strong password complexity and rotation policies.","Confidentialité","Ré<PERSON><PERSON> le risque"
"Secure physical documents containing sensitive information.","Confidentialité","Réduire le risque"
"Avoid collecting personally identifiable information (PII) unless strictly necessary.","Confidentialité","Éviter le risque"
"Prohibit use of unencrypted portable storage devices (USB drives).","Confidentialité","Éviter le risque"
"Do not store sensitive data on publicly accessible servers.","Confidentialité","Éviter le risque"
"Restrict access to sensitive data repositories entirely for non-essential personnel.","Confidentialité","Éviter le risque"
"Avoid transmitting sensitive data over unsecured public Wi-Fi networks.","Confidentialité","Éviter le risque"
"Destroy sensitive data securely once its retention period expires.","Confidentialité","Éviter le risque"
"Forbid sharing of account credentials among users.","Confidentialité","Éviter le risque"
"Avoid using third-party services with inadequate security postures for sensitive data.","Confidentialité","Éviter le risque"
"Disable unnecessary network services handling sensitive information.","Confidentialité","Éviter le risque"
"Prohibit installation of unauthorized software on systems accessing sensitive data.","Confidentialité","Éviter le risque"
"Purchase cyber insurance covering data breach incidents and liability.","Confidentialité","Transférer le risque"
"Outsource secure data destruction services to a certified vendor.","Confidentialité","Transférer le risque"
"Use a cloud provider with robust security controls and SLAs for confidentiality.","Confidentialité","Transférer le risque"
"Employ a third-party Managed Security Service Provider (MSSP) for monitoring.","Confidentialité","Transférer le risque"
"Utilize third-party penetration testing services to identify vulnerabilities.","Confidentialité","Transférer le risque"
"Implement secure email gateway services from a specialized vendor.","Confidentialité","Transférer le risque"
"Rely on vendor contracts for data protection responsibilities in SaaS applications.","Confidentialité","Transférer le risque"
"Use a trusted third-party escrow service for sensitive key management.","Confidentialité","Transférer le risque"
"Outsource background checks for personnel handling highly sensitive data.","Confidentialité","Transférer le risque"
"Engage legal counsel specialized in data privacy regulations.","Confidentialité","Transférer le risque"
"Formally accept risk of incidental data exposure in open office environments.","Confidentialité","Accepter le risque"
"Document acceptance of residual risk after implementing encryption controls.","Confidentialité","Accepter le risque"
"Accept risk for specific low-sensitivity data sets deemed non-critical.","Confidentialité","Accepter le risque"
"Acknowledge risk associated with legacy systems where encryption is infeasible.","Confidentialité","Accepter le risque"
"Accept the risk of using anonymized data for analysis (potential re-identification).","Confidentialité","Accepter le risque"
"Decide not to implement costly controls for data with very low impact if compromised.","Confidentialité","Accepter le risque"
"Accept risk of internal data misuse after providing adequate training.","Confidentialité","Accepter le risque"
"Formally accept risk of data exposure during necessary system maintenance windows.","Confidentialité","Accepter le risque"
"Acknowledge and accept limitations of DLP effectiveness against determined insiders.","Confidentialité","Accepter le risque"
"Accept risk of metadata exposure even when content is encrypted.","Confidentialité","Accepter le risque"
"Implement File Integrity Monitoring (FIM) on critical system files.","Intégrité","Réduire le risque"
"Use digital signatures to verify software and document authenticity.","Intégrité","Réduire le risque"
"Employ version control systems for configuration files and source code.","Intégrité","Réduire le risque"
"Perform regular data backups and test restoration procedures frequently.","Intégrité","Réduire le risque"
"Validate all user inputs to prevent injection attacks (SQLi, XSS).","Intégrité","Réduire le risque"
"Apply security patches promptly to operating systems and applications.","Intégrité","Réduire le risque"
"Use cryptographic hashes (checksums) to verify data integrity during transfer.","Intégrité","Réduire le risque"
"Deploy Intrusion Detection/Prevention Systems (IDPS) to block malicious modifications.","Intégrité","Réduire le risque"
"Enforce secure coding practices throughout the software development lifecycle.","Intégrité","Réduire le risque"
"Implement change management processes with approval workflows.","Intégrité","Réduire le risque"
"Restrict write access to critical configuration files and directories.","Intégrité","Éviter le risque"
"Use immutable infrastructure deployments where possible.","Intégrité","Éviter le risque"
"Prohibit direct database modification access for developers in production.","Intégrité","Éviter le risque"
"Disable installation of unauthorized software or browser extensions.","Intégrité","Éviter le risque"
"Avoid using software from untrusted or unverified sources.","Intégrité","Éviter le risque"
"Use Write-Once-Read-Many (WORM) storage for critical logs and archives.","Intégrité","Éviter le risque"
"Eliminate default passwords and configurations on all systems.","Intégrité","Éviter le risque"
"Avoid running systems with known, unpatchable critical vulnerabilities.","Intégrité","Éviter le risque"
"Prevent users from disabling endpoint security software.","Intégrité","Éviter le risque"
"Do not allow code deployment without prior testing and approval.","Intégrité","Éviter le risque"
"Use a trusted third-party Certificate Authority (CA) for SSL/TLS certificates.","Intégrité","Transférer le risque"
"Host critical applications with a provider offering integrity guarantees and monitoring.","Intégrité","Transférer le risque"
"Utilize a Content Delivery Network (CDN) with built-in integrity checks.","Intégrité","Transférer le risque"
"Purchase insurance covering data corruption or malicious system alteration.","Intégrité","Transférer le risque"
"Rely on SaaS vendors to maintain the integrity of their platforms.","Intégrité","Transférer le risque"
"Outsource patch management to a specialized service provider.","Intégrité","Transférer le risque"
"Use third-party code signing services to assure software integrity.","Intégrité","Transférer le risque"
"Employ external security auditors to verify integrity controls.","Intégrité","Transférer le risque"
"Use cloud provider's managed database services with integrity features.","Intégrité","Transférer le risque"
"Transfer risk of malware infections through managed endpoint detection response (EDR).","Intégrité","Transférer le risque"
"Accept minor data inconsistencies in non-critical internal reporting tools.","Intégrité","Accepter le risque"
"Formally accept risk of configuration drift on development/testing environments.","Intégrité","Accepter le risque"
"Accept potential for minor user data entry errors after providing training.","Intégrité","Accepter le risque"
"Document acceptance of using legacy software where integrity verification is limited.","Intégrité","Accepter le risque"
"Accept risk of undetected, low-impact changes in non-critical systems.","Intégrité","Accepter le risque"
"Decide against implementing costly real-time FIM on low-risk assets.","Intégrité","Accepter le risque"
"Accept the residual risk of zero-day exploits bypassing existing controls.","Intégrité","Accepter le risque"
"Acknowledge potential integrity issues from third-party data feeds (after validation).","Intégrité","Accepter le risque"
"Accept risk that backups might have minor inconsistencies between backup cycles.","Intégrité","Accepter le risque"
"Formally accept risks associated with user-managed spreadsheet data integrity.","Intégrité","Accepter le risque"
"Implement server load balancing across multiple systems.","Disponibilité","Réduire le risque"
"Use redundant hardware components (power supplies, NICs, disks - RAID).","Disponibilité","Réduire le risque"
"Maintain and regularly test a comprehensive Disaster Recovery (DR) plan.","Disponibilité","Réduire le risque"
"Perform regular system backups and ensure recoverability.","Disponibilité","Réduire le risque"
"Deploy DDoS mitigation solutions at the network edge.","Disponibilité","Réduire le risque"
"Monitor system performance and resource utilization proactively.","Disponibilité","Réduire le risque"
"Implement automated failover mechanisms for critical services.","Disponibilité","Réduire le risque"
"Ensure adequate network bandwidth and redundancy (multiple ISPs).","Disponibilité","Réduire le risque"
"Apply patches and updates during scheduled maintenance windows.","Disponibilité","Réduire le risque"
"Maintain environmental controls (cooling, fire suppression) in data centers.","Disponibilité","Réduire le risque"
"Design system architecture to eliminate single points of failure.","Disponibilité","Éviter le risque"
"Avoid scheduling critical system maintenance during peak business hours.","Disponibilité","Éviter le risque"
"Do not rely on a single geographic region for critical infrastructure.","Disponibilité","Éviter le risque"
"Avoid overly complex system interdependencies that increase fragility.","Disponibilité","Éviter le risque"
"Prohibit untested changes from being deployed to production environments.","Disponibilité","Éviter le risque"
"Avoid deploying critical services on hardware nearing end-of-life.","Disponibilité","Éviter le risque"
"Do not run critical production workloads on development or test infrastructure.","Disponibilité","Éviter le risque"
"Eliminate reliance on unsupported software or operating systems.","Disponibilité","Éviter le risque"
"Avoid resource exhaustion by implementing capacity planning.","Disponibilité","Éviter le risque"
"Prevent accidental shutdowns by restricting physical access to critical hardware.","Disponibilité","Éviter le risque"
"Utilize cloud hosting providers with high availability Service Level Agreements (SLAs).","Disponibilité","Transférer le risque"
"Outsource DDoS protection services to a specialized third-party provider.","Disponibilité","Transférer le risque"
"Purchase business interruption insurance to cover financial losses from downtime.","Disponibilité","Transférer le risque"
"Use a Content Delivery Network (CDN) to improve global availability.","Disponibilité","Transférer le risque"
"Contract with a third party for managed backup and disaster recovery services.","Disponibilité","Transférer le risque"
"Outsource data center operations including power and cooling management.","Disponibilité","Transférer le risque"
"Rely on SaaS vendors' commitments for application uptime.","Disponibilité","Transférer le risque"
"Engage a third party for 24/7 infrastructure monitoring and alerting.","Disponibilité","Transférer le risque"
"Transfer risk of hardware failure through comprehensive warranty/support contracts.","Disponibilité","Transférer le risque"
"Use managed DNS services with global distribution and redundancy.","Disponibilité","Transférer le risque"
"Accept occasional brief downtime for non-critical internal applications.","Disponibilité","Accepter le risque"
"Define and accept lower availability targets for development/test environments.","Disponibilité","Accepter le risque"
"Accept the risk of hardware failure for standard user workstations (replace on fail).","Disponibilité","Accepter le risque"
"Formally accept longer Recovery Time Objectives (RTO) for archived data.","Disponibilité","Accepter le risque"
"Accept risk of minor service degradation during peak load times.","Disponibilité","Accepter le risque"
"Decide against costly active-active redundancy for services with low financial impact.","Disponibilité","Accepter le risque"
"Accept the risk of relying on a single ISP for non-critical branch offices.","Disponibilité","Accepter le risque"
"Acknowledge and accept potential downtime related to force majeure events.","Disponibilité","Accepter le risque"
"Accept risk of scheduled downtime announced by third-party service providers.","Disponibilité","Accepter le risque"
"Formally accept limitations in DR capabilities for non-essential legacy systems.","Disponibilité","Accepter le risque"
"Enable detailed system, application, and security event logging.","Traçabilité","Réduire le risque"
"Centralize logs from disparate systems into a SIEM solution.","Traçabilité","Réduire le risque"
"Assign unique user IDs for all personnel accessing systems.","Traçabilité","Réduire le risque"
"Implement accurate time synchronization across all systems (NTP).","Traçabilité","Réduire le risque"
"Log all administrative actions and changes to critical systems.","Traçabilité","Réduire le risque"
"Monitor and log network traffic flow patterns.","Traçabilité","Réduire le risque"
"Implement user activity monitoring on sensitive systems where appropriate.","Traçabilité","Réduire le risque"
"Retain logs according to regulatory requirements and internal policy.","Traçabilité","Réduire le risque"
"Correlate security events across different log sources for context.","Traçabilité","Réduire le risque"
"Log access to sensitive data files and databases.","Traçabilité","Réduire le risque"
"Prohibit the use of shared or generic user accounts.","Traçabilité","Éviter le risque"
"Avoid disabling essential operating system or application audit logs.","Traçabilité","Éviter le risque"
"Do not permit anonymous access to systems requiring accountability.","Traçabilité","Éviter le risque"
"Prevent unauthorized modification or deletion of log files.","Traçabilité","Éviter le risque"
"Avoid using network address translation (NAT) excessively where source IP is critical.","Traçabilité","Éviter le risque"
"Restrict direct administrative access to production systems whenever possible.","Traçabilité","Éviter le risque"
"Avoid system configurations that obscure user actions (e.g., disabling command history).","Traçabilité","Éviter le risque"
"Do not allow unmonitored remote access sessions to critical infrastructure.","Traçabilité","Éviter le risque"
"Eliminate systems or applications that lack adequate logging capabilities.","Traçabilité","Éviter le risque"
"Prevent changes outside of established change management (which includes logging).","Traçabilité","Éviter le risque"
"Utilize a managed Security Information and Event Management (SIEM) service.","Traçabilité","Transférer le risque"
"Outsource log storage and archiving to a specialized cloud provider.","Traçabilité","Transférer le risque"
"Employ a third-party Security Operations Center (SOC) for log analysis.","Traçabilité","Transférer le risque"
"Rely on cloud provider logging services (e.g., AWS CloudTrail, Azure Monitor logs).","Traçabilité","Transférer le risque"
"Use SaaS applications where the vendor manages activity logging.","Traçabilité","Transférer le risque"
"Contract with forensic investigators who rely on logs for evidence.","Traçabilité","Transférer le risque"
"Utilize third-party identity providers (IdP) managing authentication logs.","Traçabilité","Transférer le risque"
"Implement managed endpoint detection and response (EDR) with logging features.","Traçabilité","Transférer le risque"
"Use external services for monitoring employee compliance with policies.","Traçabilité","Transférer le risque"
"Rely on external auditors to validate the effectiveness of logging mechanisms.","Traçabilité","Transférer le risque"
"Accept limited traceability on standard user workstations due to privacy concerns.","Traçabilité","Accepter le risque"
"Accept lack of detailed logging for low-risk public information websites.","Traçabilité","Accepter le risque"
"Accept reduced log retention periods for non-critical systems to manage costs.","Traçabilité","Accepter le risque"
"Formally accept lack of traceability for specific end-of-life legacy systems.","Traçabilité","Accepter le risque"
"Accept that logs may not capture user intent, only actions performed.","Traçabilité","Accepter le risque"
"Decide against costly, high-volume network packet capture for routine traffic.","Traçabilité","Accepter le risque"
"Accept limitations in correlating events across systems with unsynchronized clocks.","Traçabilité","Accepter le risque"
"Acknowledge that determined attackers may attempt to evade or tamper with logs.","Traçabilité","Accepter le risque"
"Accept gaps in tracing user activity across personal and corporate devices.","Traçabilité","Accepter le risque"
"Formally accept that logs from certain third-party services may be insufficient.","Traçabilité","Accepter le risque"
"Implement tamper-evident logging mechanisms (e.g., secure append-only, hashing).","Preuve","Réduire le risque"
"Use digital signatures for verifying the origin and integrity of communications.","Preuve","Réduire le risque"
"Maintain a strict chain of custody for all collected digital evidence.","Preuve","Réduire le risque"
"Employ cryptographic hashing to ensure the integrity of stored evidence files.","Preuve","Réduire le risque"
"Record all incident response actions, decisions, and findings meticulously.","Preuve","Réduire le risque"
"Utilize secure timestamping services for critical log entries and transactions.","Preuve","Réduire le risque"
"Implement non-repudiation controls for critical transactions.","Preuve","Réduire le risque"
"Capture forensic images of systems involved in security incidents correctly.","Preuve","Réduire le risque"
"Ensure log data includes sufficient detail for forensic analysis.","Preuve","Réduire le risque"
"Corroborate digital evidence with other sources (physical logs, witness statements).","Preuve","Réduire le risque"
"Avoid deleting critical logs before the mandated retention period expires.","Preuve","Éviter le risque"
"Do not allow modification of original evidence logs (use WORM or secure copies).","Preuve","Éviter le risque"
"Prevent actions that could contaminate evidence during incident response (e.g., premature reboot).","Preuve","Éviter le risque"
"Avoid using systems or tools that cannot produce verifiable evidence trails.","Preuve","Éviter le risque"
"Do not rely solely on volatile memory for critical evidence.","Preuve","Éviter le risque"
"Prohibit undocumented or unauthorized changes to system configurations relevant to evidence.","Preuve","Éviter le risque"
"Avoid commingling sensitive investigation data with normal operational data.","Preuve","Éviter le risque"
"Do not store critical evidence on easily accessible or modifiable network shares.","Preuve","Éviter le risque"
"Avoid interrupting legally required data preservation holds (litigation holds).","Preuve","Éviter le risque"
"Prevent untrained personnel from handling potential digital evidence.","Preuve","Éviter le risque"
"Use a trusted third-party timestamping authority for verifiable time records.","Preuve","Transférer le risque"
"Outsource digital forensics investigations to specialized certified firms.","Preuve","Transférer le risque"
"Rely on cloud provider logs and attestations as evidence (verify contractual terms).","Preuve","Transférer le risque"
"Engage third-party e-discovery services for legal proceedings.","Preuve","Transférer le risque"
"Store critical evidence backups or archives with a secure third-party custodian.","Preuve","Transférer le risque"
"Use externally managed Public Key Infrastructure (PKI) for digital signatures.","Preuve","Transférer le risque"
"Rely on independent auditors' reports as proof of control implementation.","Preuve","Transférer le risque"
"Utilize third-party attestation services (e.g., SOC reports) as evidence of compliance.","Preuve","Transférer le risque"
"Employ external legal counsel to validate evidence collection procedures.","Preuve","Transférer le risque"
"Use blockchain-based services for immutable transaction proof where applicable.","Preuve","Transférer le risque"
"Accept weaker non-repudiation for low-risk internal email communications.","Preuve","Accepter le risque"
"Accept lack of definitive proof for minor policy violations handled informally.","Preuve","Accepter le risque"
"Acknowledge that evidence retrieved from certain legacy systems may be less reliable.","Preuve","Accepter le risque"
"Accept limitations in definitively proving user intent versus accidental action.","Preuve","Accepter le risque"
"Accept that volatile data (RAM) might be lost if not captured immediately.","Preuve","Accepter le risque"
"Decide against costly forensic tooling for all minor security events.","Preuve","Accepter le risque"
"Accept that perfect chain of custody may be difficult for user-managed devices.","Preuve","Accepter le risque"
"Formally accept that log timestamps might have minor drifts if NTP fails briefly.","Preuve","Accepter le risque"
"Acknowledge that digitally signed documents might face legal challenges.","Preuve","Accepter le risque"
"Accept risk that encrypted data may be unrecoverable without keys, limiting evidence.","Preuve","Accepter le risque"
"Ensure audit logs are complete, accurate, and protected from tampering.","Auditabilité","Réduire le risque"
"Implement specific, read-only access roles for internal and external auditors.","Auditabilité","Réduire le risque"
"Maintain up-to-date documentation of system architecture, data flows, and policies.","Auditabilité","Réduire le risque"
"Conduct regular internal security audits based on established frameworks (ISO 27001, NIST).","Auditabilité","Réduire le risque"
"Facilitate periodic external audits by independent third parties.","Auditabilité","Réduire le risque"
"Use Governance, Risk, and Compliance (GRC) tools to manage audit evidence.","Auditabilité","Réduire le risque"
"Ensure audit trails capture sufficient detail (who, what, when, where, success/failure).","Auditabilité","Réduire le risque"
"Implement processes for tracking and remediating audit findings promptly.","Auditabilité","Réduire le risque"
"Standardize system configurations to simplify auditing processes.","Auditabilité","Réduire le risque"
"Provide auditors with necessary tools and training for accessing audit data.","Auditabilité","Réduire le risque"
"Avoid system designs that inherently lack audit capabilities or transparency.","Auditabilité","Éviter le risque"
"Do not disable necessary audit trails or logging required for compliance.","Auditabilité","Éviter le risque"
"Avoid using technologies or third-party services without audit rights or reports.","Auditabilité","Éviter le risque"
"Prohibit undocumented exceptions to security policies that hinder audits.","Auditabilité","Éviter le risque"
"Avoid granting excessive system permissions that bypass audit controls.","Auditabilité","Éviter le risque"
"Do not allow destruction of audit records before the required retention period.","Auditabilité","Éviter le risque"
"Avoid manual processes where automated, auditable workflows can be used.","Auditabilité","Éviter le risque"
"Prevent deployment of systems without defined audit requirements and testing.","Auditabilité","Éviter le risque"
"Eliminate shared administrative accounts that obscure individual accountability for audits.","Auditabilité","Éviter le risque"
"Do not obstruct auditors' access to necessary personnel or documentation.","Auditabilité","Éviter le risque"
"Hire independent external audit firms to perform periodic assessments.","Auditabilité","Transférer le risque"
"Utilize cloud providers certified against recognized standards (e.g., SOC 2, ISO 27001).","Auditabilité","Transférer le risque"
"Rely on audit reports and certifications provided by SaaS vendors.","Auditabilité","Transférer le risque"
"Outsource the internal audit function (fully or partially) to a specialized firm.","Auditabilité","Transférer le risque"
"Engage third-party consultants to assess compliance with specific regulations (PCI DSS, GDPR).","Auditabilité","Transférer le risque"
"Use managed services where the provider assumes responsibility for auditable operations.","Auditabilité","Transférer le risque"
"Rely on external legal counsel to interpret audit requirements in regulations.","Auditabilité","Transférer le risque"
"Utilize third-party GRC platforms that facilitate audit evidence collection from vendors.","Auditabilité","Transférer le risque"
"Contractually require service providers to grant audit rights.","Auditabilité","Transférer le risque"
"Purchase cyber insurance that may require audits as a condition.","Auditabilité","Transférer le risque"
"Accept reduced auditability for temporary development or sandbox environments.","Auditabilité","Accepter le risque"
"Acknowledge limitations in auditing third-party ""black-box"" software components.","Auditabilité","Accepter le risque"
"Accept longer lead times for retrieving audit data from offline archives.","Auditabilité","Accepter le risque"
"Formally accept that the cost of full auditability is not justified for certain low-risk assets.","Auditabilité","Accepter le risque"
"Accept limited visibility into the internal controls of certain upstream suppliers.","Auditabilité","Accepter le risque"
"Decide against intrusive audits on systems where it could cause instability.","Auditabilité","Accepter le risque"
"Accept that audit evidence for legacy systems may be incomplete or non-standard.","Auditabilité","Accepter le risque"
"Acknowledge difficulty in auditing informal communication channels (e.g., hallway conversations).","Auditabilité","Accepter le risque"
"Accept residual risk that audits may not detect all non-compliance or fraud.","Auditabilité","Accepter le risque"
"Formally accept limitations on auditing employee adherence to acceptable use policies.","Auditabilité","Accepter le risque"