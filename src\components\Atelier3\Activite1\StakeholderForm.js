// src/components/Atelier3/Activite1/StakeholderForm.js
import React, { useState, useEffect } from 'react';
import { X, Save, AlertCircle } from 'lucide-react';
import { useTranslation } from 'react-i18next';

const StakeholderForm = ({ stakeholder, onSubmit, onCancel }) => {
  const { t } = useTranslation();
  const [formData, setFormData] = useState({
    name: '',
    category: 'client',
    type: 'external',
    description: '',
    rank: 1,
    dependency: 1,
    penetration: 1,
    cyberMaturity: 1,
    trust: 1,
    notes: '',
    retained: false
  });

  const [threatLevel, setThreatLevel] = useState(0);

  // Initialize form with stakeholder data if editing
  useEffect(() => {
    if (stakeholder) {
      setFormData({
        name: stakeholder.name || '',
        category: stakeholder.category || 'client',
        type: stakeholder.type || 'external',
        description: stakeholder.description || '',
        rank: stakeholder.rank || 1,
        dependency: stakeholder.dependency || 1,
        penetration: stakeholder.penetration || 1,
        cyberMaturity: stakeholder.cyberMaturity || 1,
        trust: stakeholder.trust || 1,
        notes: stakeholder.notes || '',
        retained: stakeholder.retained || false
      });
    }
  }, [stakeholder]);

  // Calculate threat level when rating values change
  useEffect(() => {
    const { dependency, penetration, cyberMaturity, trust } = formData;

    // Prevent division by zero
    if (cyberMaturity === 0 || trust === 0) {
      setThreatLevel(Infinity);
      return;
    }

    // Formula: (Dependency × Penetration) / (Cyber Maturity × Trust)
    const calculatedThreatLevel = (dependency * penetration) / (cyberMaturity * trust);
    setThreatLevel(calculatedThreatLevel);
  }, [formData.dependency, formData.penetration, formData.cyberMaturity, formData.trust]);

  // Handle form input changes
  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: name === 'rank' || name === 'dependency' || name === 'penetration' ||
              name === 'cyberMaturity' || name === 'trust'
                ? parseInt(value, 10)
                : value
    }));
  };

  // Handle form submission
  const handleSubmit = (e) => {
    e.preventDefault();

    // Calculate threat level
    const calculatedThreatLevel = (formData.dependency * formData.penetration) /
                                 (formData.cyberMaturity * formData.trust);

    // Submit form data with calculated threat level
    onSubmit({
      ...formData,
      threatLevel: calculatedThreatLevel
    });
  };

  // Helper function to render rating options with descriptions
  const renderRatingOptions = (field, descriptions) => {
    // Get field label
    const getFieldLabel = () => {
      switch(field) {
        case 'dependency': return t('workshop3.activity1.stakeholderForm.dependency');
        case 'penetration': return t('workshop3.activity1.stakeholderForm.penetration');
        case 'cyberMaturity': return t('workshop3.activity1.stakeholderForm.cyberMaturity');
        case 'trust': return t('workshop3.activity1.stakeholderForm.trust');
        default: return field;
      }
    };

    // Get color based on value and field
    const getValueColor = (value) => {
      // For dependency and penetration, higher values are more dangerous (red)
      if (field === 'dependency' || field === 'penetration') {
        switch(value) {
          case 1: return 'bg-green-50 border-green-200';
          case 2: return 'bg-yellow-50 border-yellow-200';
          case 3: return 'bg-orange-50 border-orange-200';
          case 4: return 'bg-red-50 border-red-200';
        }
      }
      // For cyberMaturity and trust, higher values are safer (green)
      else {
        switch(value) {
          case 1: return 'bg-red-50 border-red-200';
          case 2: return 'bg-orange-50 border-orange-200';
          case 3: return 'bg-yellow-50 border-yellow-200';
          case 4: return 'bg-green-50 border-green-200';
        }
      }
    };

    return (
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          {getFieldLabel()}
        </label>
        <div className="flex flex-col space-y-2">
          {[1, 2, 3, 4].map(value => (
            <div
              key={value}
              className={`p-2 border rounded-md cursor-pointer transition-colors flex items-center
                ${formData[field] === value
                  ? `${getValueColor(value)} ring-2 ring-blue-500`
                  : 'border-gray-200 hover:bg-gray-50'}`}
              onClick={() => setFormData(prev => ({ ...prev, [field]: value }))}
            >
              <div className={`h-6 w-6 rounded-full flex items-center justify-center mr-2 font-medium
                ${formData[field] === value ? 'bg-blue-600 text-white' : 'bg-gray-100 text-gray-700'}`}>
                {value}
              </div>
              <div className="text-xs text-gray-700 flex-1">{descriptions[value-1]}</div>
            </div>
          ))}
        </div>
      </div>
    );
  };

  return (
    <div className="p-4">
      <form onSubmit={handleSubmit} className="space-y-4">
        {/* Basic information section */}
        <div className="grid grid-cols-2 gap-4">
          {/* Name */}
          <div className="col-span-2 sm:col-span-1">
            <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
              {t('workshop3.activity1.stakeholderForm.name')} *
            </label>
            <input
              type="text"
              id="name"
              name="name"
              value={formData.name}
              onChange={handleChange}
              required
              className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder={t('workshop3.activity1.stakeholderForm.namePlaceholder')}
            />
          </div>

          {/* Category */}
          <div className="col-span-2 sm:col-span-1">
            <label htmlFor="category" className="block text-sm font-medium text-gray-700 mb-1">
              {t('workshop3.activity1.stakeholderForm.category')} *
            </label>
            <select
              id="category"
              name="category"
              value={formData.category}
              onChange={handleChange}
              required
              className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="client">{t('workshop3.activity1.categories.client')}</option>
              <option value="partner">{t('workshop3.activity1.categories.partner')}</option>
              <option value="provider">{t('workshop3.activity1.categories.provider')}</option>
              <option value="technical">{t('workshop3.activity1.categories.technical')}</option>
              <option value="business">{t('workshop3.activity1.categories.business')}</option>
              <option value="subsidiary">{t('workshop3.activity1.categories.subsidiary')}</option>
            </select>
          </div>
        </div>

        <div className="grid grid-cols-2 gap-4">
          {/* Type */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              {t('workshop3.activity1.stakeholderForm.type')} *
            </label>
            <div className="flex space-x-4 mt-1">
              <label className="inline-flex items-center">
                <input
                  type="radio"
                  name="type"
                  value="internal"
                  checked={formData.type === 'internal'}
                  onChange={handleChange}
                  className="h-4 w-4 text-blue-600"
                />
                <span className="ml-2 text-sm text-gray-700">{t('workshop3.activity1.filter.internal')}</span>
              </label>
              <label className="inline-flex items-center">
                <input
                  type="radio"
                  name="type"
                  value="external"
                  checked={formData.type === 'external'}
                  onChange={handleChange}
                  className="h-4 w-4 text-blue-600"
                />
                <span className="ml-2 text-sm text-gray-700">{t('workshop3.activity1.filter.external')}</span>
              </label>
            </div>
          </div>

          {/* Rank */}
          <div>
            <label htmlFor="rank" className="block text-sm font-medium text-gray-700 mb-1">
              {t('workshop3.activity1.stakeholderForm.rank')} *
            </label>
            <select
              id="rank"
              name="rank"
              value={formData.rank}
              onChange={handleChange}
              required
              className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value={1}>{t('workshop3.activity1.stakeholderForm.rank1')}</option>
              <option value={2}>{t('workshop3.activity1.stakeholderForm.rank2')}</option>
              <option value={3}>{t('workshop3.activity1.stakeholderForm.rank3')}</option>
            </select>
          </div>
        </div>

        {/* Description */}
        <div>
          <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-1">
            {t('workshop3.activity1.stakeholderForm.description')}
          </label>
          <textarea
            id="description"
            name="description"
            value={formData.description}
            onChange={handleChange}
            rows={2}
            className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            placeholder={t('workshop3.activity1.stakeholderForm.descriptionPlaceholder')}
          />
        </div>

        {/* Ratings section */}
        <div className="bg-gray-50 p-4 rounded-lg border border-gray-200">
          <h3 className="text-sm font-medium text-gray-700 mb-3">{t('workshop3.activity1.stakeholderForm.criteriaEvaluation')}</h3>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Dependency Rating */}
            <div>
              {renderRatingOptions('dependency', [
                t('workshop3.activity1.stakeholderForm.dependency1'),
                t('workshop3.activity1.stakeholderForm.dependency2'),
                t('workshop3.activity1.stakeholderForm.dependency3'),
                t('workshop3.activity1.stakeholderForm.dependency4')
              ])}
            </div>

            {/* Penetration Rating */}
            <div>
              {renderRatingOptions('penetration', [
                t('workshop3.activity1.stakeholderForm.penetration1'),
                t('workshop3.activity1.stakeholderForm.penetration2'),
                t('workshop3.activity1.stakeholderForm.penetration3'),
                t('workshop3.activity1.stakeholderForm.penetration4')
              ])}
            </div>

            {/* Cyber Maturity Rating */}
            <div>
              {renderRatingOptions('cyberMaturity', [
                t('workshop3.activity1.stakeholderForm.cyberMaturity1'),
                t('workshop3.activity1.stakeholderForm.cyberMaturity2'),
                t('workshop3.activity1.stakeholderForm.cyberMaturity3'),
                t('workshop3.activity1.stakeholderForm.cyberMaturity4')
              ])}
            </div>

            {/* Trust Rating */}
            <div>
              {renderRatingOptions('trust', [
                t('workshop3.activity1.stakeholderForm.trust1'),
                t('workshop3.activity1.stakeholderForm.trust2'),
                t('workshop3.activity1.stakeholderForm.trust3'),
                t('workshop3.activity1.stakeholderForm.trust4')
              ])}
            </div>
          </div>

          {/* Calculated Threat Level */}
          <div className="mt-4 p-3 bg-white rounded-lg border border-gray-200 flex justify-between items-center">
            <div>
              <span className="text-sm font-medium text-gray-700">{t('workshop3.activity1.stakeholderForm.calculatedThreatLevel')}:</span>
              <div className="text-xs text-gray-500 mt-1">
                {t('workshop3.activity1.stakeholderForm.threatLevelFormula')}
              </div>
            </div>
            <div className={`text-lg font-bold px-3 py-1 rounded-lg ${
              threatLevel >= 3 ? 'bg-red-100 text-red-700' :
              threatLevel >= 1.5 ? 'bg-orange-100 text-orange-700' :
              'bg-green-100 text-green-700'
            }`}>
              {threatLevel.toFixed(2)}
            </div>
          </div>
        </div>

        {/* Notes */}
        <div>
          <label htmlFor="notes" className="block text-sm font-medium text-gray-700 mb-1">
            {t('workshop3.activity1.stakeholderForm.notes')}
          </label>
          <textarea
            id="notes"
            name="notes"
            value={formData.notes}
            onChange={handleChange}
            rows={2}
            className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            placeholder={t('workshop3.activity1.stakeholderForm.notesPlaceholder')}
          />
        </div>

        {/* Retained Status - Simplified */}
        <div className="mt-4 p-3 bg-gray-50 rounded-lg border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <label htmlFor="retained" className="text-sm font-medium text-gray-700">{t('workshop3.activity1.retained')}</label>
              <p className="text-xs text-gray-500 mt-0.5">{t('workshop3.activity1.stakeholderForm.retainedDescription')}</p>
            </div>
            <div className="flex items-center space-x-3">
              <span className={`text-xs px-2 py-0.5 rounded-full ${formData.retained ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-800'}`}>
                {formData.retained ? t('workshop3.activity1.retained') : t('workshop3.activity1.notRetained')}
              </span>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  name="retained"
                  id="retained"
                  className="sr-only peer"
                  checked={formData.retained}
                  onChange={(e) => setFormData({ ...formData, retained: e.target.checked })}
                />
                <div className="w-8 h-4 bg-gray-200 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-3 after:w-3 after:transition-all peer-checked:bg-blue-600"></div>
              </label>
            </div>
          </div>
        </div>

        {/* Form actions */}
        <div className="flex justify-end space-x-3 pt-2 border-t border-gray-200">
          <button
            type="button"
            onClick={onCancel}
            className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
          >
            {t('common.cancel')}
          </button>
          <button
            type="submit"
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center"
          >
            <Save size={18} className="mr-1.5" />
            {stakeholder ? t('workshop3.activity1.stakeholderForm.update') : t('workshop3.activity1.stakeholderForm.add')}
          </button>
        </div>
      </form>
    </div>
  );
};

export default StakeholderForm;
