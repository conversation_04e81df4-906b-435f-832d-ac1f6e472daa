// backend/controllers/userController.js
const User = require('../models/User');
const Company = require('../models/Company');
const ActivityLog = require('../models/ActivityLog');

// Helper function to get IP address
const getIpAddress = (req) => {
  return req.ip || 
         req.headers['x-forwarded-for'] || 
         req.connection.remoteAddress || 
         'unknown';
};

// Helper function to create activity log
const logActivity = async (req, actionType, resourceType, resourceId, details = {}) => {
  try {
    if (!req.user) return;
    
    await ActivityLog.create({
      userId: req.user.id,
      userName: req.user.name,
      companyId: req.user.companyId,
      companyName: req.user.companyName,
      actionType,
      resourceType,
      resourceId,
      ipAddress: getIpAddress(req),
      userAgent: req.headers['user-agent'] || '',
      details: {
        ...details,
        timestamp: new Date()
      }
    });
  } catch (error) {
    console.error('Activity log error:', error);
  }
};

/**
 * @desc    Get all users
 * @route   GET /api/users
 * @access  Private (SuperAdmin, Admin)
 */
exports.getUsers = async (req, res) => {
  try {
    const { role, status, search, companyId, page = 1, limit = 10 } = req.query;
    const skip = (page - 1) * limit;
    
    // Build filter
    const filter = {};
    
    // Role filter
    if (role) {
      filter.role = role;
    }
    
    // Status filter
    if (status) {
      filter.status = status;
    }
    
    // Search filter
    if (search) {
      filter.$or = [
        { name: { $regex: search, $options: 'i' } },
        { email: { $regex: search, $options: 'i' } }
      ];
    }
    
    // Company filter
    if (companyId) {
      filter.companyId = companyId;
    }
    
    // Add company filter for admin users (they can only see users from their company)
    if (req.user.role === 'admin') {
      filter.companyId = req.user.companyId;
    }
    
    // Execute query with pagination
    const users = await User.find(filter)
      .select('-password')
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(parseInt(limit));
    
    // Get total count for pagination
    const total = await User.countDocuments(filter);
    
    // Return users
    res.status(200).json({
      success: true,
      data: users,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Get users error:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération des utilisateurs',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * @desc    Get user by ID
 * @route   GET /api/users/:id
 * @access  Private (SuperAdmin, Admin of same company, or user themselves)
 */
exports.getUserById = async (req, res) => {
  try {
    const user = await User.findById(req.params.id).select('-password');
    
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'Utilisateur non trouvé'
      });
    }
    
    // Check permission
    if (req.user.role !== 'superadmin') {
      // Admin can only see users from their company
      if (req.user.role === 'admin' && user.companyId.toString() !== req.user.companyId.toString()) {
        return res.status(403).json({
          success: false,
          message: 'Accès non autorisé'
        });
      }
      
      // Regular users can only see themselves
      if (req.user.role === 'simpleuser' && user._id.toString() !== req.user.id.toString()) {
        return res.status(403).json({
          success: false,
          message: 'Accès non autorisé'
        });
      }
    }
    
    res.status(200).json({
      success: true,
      data: user
    });
  } catch (error) {
    console.error('Get user error:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération de l\'utilisateur',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * @desc    Create new user
 * @route   POST /api/users
 * @access  Private (SuperAdmin, Admin)
 */
exports.createUser = async (req, res) => {
  try {
    const { name, email, password, role, companyId, status = 'active' } = req.body;
    
    // Validation
    if (!name || !email || !password) {
      return res.status(400).json({
        success: false,
        message: 'Nom, email et mot de passe sont requis'
      });
    }
    
    // Check if email exists
    const existingUser = await User.findOne({ email });
    
    if (existingUser) {
      return res.status(400).json({
        success: false,
        message: 'Un utilisateur avec cet email existe déjà'
      });
    }
    
    // Role permission check
    if (req.user.role === 'admin') {
      // Admin can only create simpleuser
      if (role === 'superadmin' || role === 'admin') {
        return res.status(403).json({
          success: false,
          message: 'Vous n\'avez pas la permission de créer ce type d\'utilisateur'
        });
      }
      
      // Admin can only create users in their own company
      if (companyId !== req.user.companyId.toString()) {
        return res.status(403).json({
          success: false,
          message: 'Vous ne pouvez créer des utilisateurs que dans votre entreprise'
        });
      }
    }
    
    // Verify company exists
    let company = null;
    if (companyId) {
      company = await Company.findById(companyId);
      
      if (!company) {
        return res.status(400).json({
          success: false,
          message: 'Entreprise non trouvée'
        });
      }
    }
    
    // Create user
    const user = await User.create({
      name,
      email,
      password,
      role: role || 'simpleuser',
      companyId: companyId || null,
      companyName: company ? company.name : null,
      status,
      createdBy: req.user.id
    });
    
    // Log activity
    await logActivity(
      req, 
      'USER_CREATE', 
      'user', 
      user._id, 
      { 
        userName: user.name, 
        userEmail: user.email, 
        userRole: user.role 
      }
    );
    
    // Return user without password
    const userResponse = await User.findById(user._id).select('-password');
    
    res.status(201).json({
      success: true,
      data: userResponse
    });
  } catch (error) {
    console.error('Create user error:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la création de l\'utilisateur',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * @desc    Update user
 * @route   PUT /api/users/:id
 * @access  Private (SuperAdmin, Admin of same company, or user themselves)
 */
exports.updateUser = async (req, res) => {
  try {
    const { name, email, role, status, companyId } = req.body;
    const userId = req.params.id;
    
    // Find user
    const user = await User.findById(userId);
    
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'Utilisateur non trouvé'
      });
    }
    
    // Check permission
    if (req.user.role === 'admin') {
      // Admin can't update superadmin or other admin
      if (user.role === 'superadmin' || user.role === 'admin') {
        return res.status(403).json({
          success: false,
          message: 'Vous n\'avez pas la permission de modifier ce type d\'utilisateur'
        });
      }
      
      // Admin can only update users from their company
      if (user.companyId.toString() !== req.user.companyId.toString()) {
        return res.status(403).json({
          success: false,
          message: 'Vous ne pouvez modifier que les utilisateurs de votre entreprise'
        });
      }
      
      // Admin can't change user's company
      if (companyId && companyId !== user.companyId.toString()) {
        return res.status(403).json({
          success: false,
          message: 'Vous ne pouvez pas changer l\'entreprise d\'un utilisateur'
        });
      }
    }
    
    // Simple users can only update their own name and email
    if (req.user.role === 'simpleuser') {
      if (req.user.id !== userId) {
        return res.status(403).json({
          success: false,
          message: 'Vous ne pouvez modifier que votre propre profil'
        });
      }
      
      // Simple users can't change their role or status
      if (role && role !== user.role) {
        return res.status(403).json({
          success: false,
          message: 'Vous ne pouvez pas modifier votre rôle'
        });
      }
      
      if (status && status !== user.status) {
        return res.status(403).json({
          success: false,
          message: 'Vous ne pouvez pas modifier votre statut'
        });
      }
    }
    
    // Verify company exists if changing
    let company = null;
    if (companyId && companyId !== user.companyId.toString()) {
      company = await Company.findById(companyId);
      
      if (!company) {
        return res.status(400).json({
          success: false,
          message: 'Entreprise non trouvée'
        });
      }
    }
    
    // Check if email exists for another user
    if (email && email !== user.email) {
      const existingUser = await User.findOne({ email });
      
      if (existingUser) {
        return res.status(400).json({
          success: false,
          message: 'Un utilisateur avec cet email existe déjà'
        });
      }
    }
    
    // Update user fields
    if (name) user.name = name;
    if (email) user.email = email;
    if (req.user.role !== 'simpleuser' && role) user.role = role;
    if (req.user.role !== 'simpleuser' && status) user.status = status;
    
    // Only superadmin can change company
    if (req.user.role === 'superadmin' && companyId) {
      user.companyId = companyId;
      user.companyName = company ? company.name : null;
    }
    
    // Save changes
    await user.save();
    
    // Log activity
    await logActivity(
      req, 
      'USER_UPDATE', 
      'user', 
      user._id, 
      { 
        userName: user.name, 
        userEmail: user.email,
        changedFields: Object.keys(req.body) 
      }
    );
    
    // Return updated user without password
    const updatedUser = await User.findById(userId).select('-password');
    
    res.status(200).json({
      success: true,
      data: updatedUser
    });
  } catch (error) {
    console.error('Update user error:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la mise à jour de l\'utilisateur',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * @desc    Update user password
 * @route   PUT /api/users/:id/password
 * @access  Private (SuperAdmin, Admin of same company, or user themselves)
 */
exports.updatePassword = async (req, res) => {
  try {
    const { currentPassword, newPassword } = req.body;
    const userId = req.params.id;
    
    if (!newPassword) {
      return res.status(400).json({
        success: false,
        message: 'Le nouveau mot de passe est requis'
      });
    }
    
    // Find user with password
    const user = await User.findById(userId).select('+password');
    
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'Utilisateur non trouvé'
      });
    }
    
    // Check permission
    if (req.user.role !== 'superadmin') {
      // Admin can only update users from their company
      if (req.user.role === 'admin' && user.companyId.toString() !== req.user.companyId.toString()) {
        return res.status(403).json({
          success: false,
          message: 'Vous ne pouvez modifier que les utilisateurs de votre entreprise'
        });
      }
      
      // Simple users can only update their own password
      if (req.user.role === 'simpleuser' && user._id.toString() !== req.user.id) {
        return res.status(403).json({
          success: false,
          message: 'Vous ne pouvez modifier que votre propre mot de passe'
        });
      }
    }
    
    // If user is changing their own password, verify current password
    if (req.user.id === userId && req.user.role === 'simpleuser') {
      if (!currentPassword) {
        return res.status(400).json({
          success: false,
          message: 'Le mot de passe actuel est requis'
        });
      }
      
      const isMatch = await user.comparePassword(currentPassword);
      
      if (!isMatch) {
        return res.status(401).json({
          success: false,
          message: 'Mot de passe actuel incorrect'
        });
      }
    }
    
    // Update password
    user.password = newPassword;
    await user.save();
    
    // Log activity
    await logActivity(
      req, 
      'USER_UPDATE', 
      'user', 
      user._id, 
      { 
        userName: user.name,
        action: 'password_change'
      }
    );
    
    res.status(200).json({
      success: true,
      message: 'Mot de passe mis à jour avec succès'
    });
  } catch (error) {
    console.error('Update password error:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la mise à jour du mot de passe',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * @desc    Delete user
 * @route   DELETE /api/users/:id
 * @access  Private (SuperAdmin, Admin)
 */
exports.deleteUser = async (req, res) => {
  try {
    const userId = req.params.id;
    
    // Find user
    const user = await User.findById(userId);
    
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'Utilisateur non trouvé'
      });
    }
    
    // Check permission
    if (req.user.role === 'admin') {
      // Admin can't delete superadmin or other admin
      if (user.role === 'superadmin' || user.role === 'admin') {
        return res.status(403).json({
          success: false,
          message: 'Vous n\'avez pas la permission de supprimer ce type d\'utilisateur'
        });
      }
      
      // Admin can only delete users from their company
      if (user.companyId.toString() !== req.user.companyId.toString()) {
        return res.status(403).json({
          success: false,
          message: 'Vous ne pouvez supprimer que les utilisateurs de votre entreprise'
        });
      }
    }
    
    // Store user information for logging
    const userInfo = {
      id: user._id,
      name: user.name,
      email: user.email,
      role: user.role,
      companyId: user.companyId,
      companyName: user.companyName
    };
    
    // Delete user
    await User.findByIdAndDelete(userId);
    
    // Log activity
    await logActivity(
      req, 
      'USER_DELETE', 
      'user', 
      userInfo.id, 
      userInfo
    );
    
    res.status(200).json({
      success: true,
      message: 'Utilisateur supprimé avec succès',
      data: userInfo
    });
  } catch (error) {
    console.error('Delete user error:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la suppression de l\'utilisateur',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};