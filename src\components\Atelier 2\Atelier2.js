// src/components/Atelier 2/Atelier2.js
import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { AlertCircle, Link, CheckSquare } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import Activite1 from './Activite1/Activite1';
import Activite2 from './Activite2/Activite2';
import Activite3 from './Activite3/Activite3';
import { useAnalysis } from '../../context/AnalysisContext';

const Atelier2 = () => {
  const { t } = useTranslation();
  const [activeActivity, setActiveActivity] = useState('activite1');
  const {
    currentAnalysis,
    getSourcesDeRisque
  } = useAnalysis();

  // Load data when component mounts
  useEffect(() => {
    const loadInitialData = async () => {
      if (currentAnalysis?.id) {
        try {
          console.log('Atelier2 - Component mounted, letting Activite1 handle data loading');
          // We'll let the Activite1 component handle the data loading directly
        } catch (error) {
          console.error('Error loading initial Atelier 2 data:', error);
        }
      }
    };

    loadInitialData();
  }, [currentAnalysis?.id]);

  // Progress indicators
  const getProgressStatus = (activity) => {
    // This would ideally be based on actual data from the context
    // For now, we'll just use a placeholder implementation
    if (activity === 'activite1') {
      return 'in-progress'; // Placeholder
    } else if (activity === 'activite2') {
      return 'not-started'; // Placeholder
    } else if (activity === 'activite3') {
      return 'not-started'; // Placeholder
    }
    return 'not-started';
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'completed':
        return <div className="w-6 h-6 rounded-full bg-green-500 flex items-center justify-center text-white">✓</div>;
      case 'in-progress':
        return <div className="w-6 h-6 rounded-full bg-blue-500 flex items-center justify-center text-white">⋯</div>;
      case 'not-started':
      default:
        return <div className="w-6 h-6 rounded-full bg-gray-300 flex items-center justify-center text-white">○</div>;
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-white p-6 rounded-lg shadow-sm border border-gray-200"
      >
        <h1 className="text-2xl font-bold text-gray-800">{t('workshop2.title')}</h1>
        <p className="text-gray-600 mt-1">
          {currentAnalysis ? t('workshop2.currentAnalysis', { name: currentAnalysis.name }) : t('workshop2.noAnalysisSelected')}
        </p>
      </motion.div>

      {/* Activity Navigation */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
        <div className="p-4 border-b border-gray-200">
          <div className="flex flex-col md:flex-row space-y-2 md:space-y-0">
            <button
              className={`flex items-center px-4 py-2 rounded-md ${
                activeActivity === 'activite1'
                  ? 'bg-blue-50 text-blue-700 font-medium'
                  : 'text-gray-600 hover:bg-gray-50'
              }`}
              onClick={async () => {
                setActiveActivity('activite1');

                // Load data if we have a current analysis
                if (currentAnalysis?.id) {
                  try {
                    console.log('Atelier2 - Clicking on Activite1 tab, letting component handle data loading');
                    // We'll let the Activite1 component handle the data loading directly
                  } catch (error) {
                    console.error('Error loading Atelier 2 Activite 1 data:', error);
                  }
                }
              }}
            >
              {getStatusIcon(getProgressStatus('activite1'))}
              <div className="ml-2 flex items-center">
                <AlertCircle size={16} className="mr-1.5" />
                <span>{t('workshop2.activities.identifySROV')}</span>
              </div>
            </button>
            <button
              className={`flex items-center px-4 py-2 rounded-md ${
                activeActivity === 'activite2'
                  ? 'bg-blue-50 text-blue-700 font-medium'
                  : 'text-gray-600 hover:bg-gray-50'
              }`}
              onClick={() => setActiveActivity('activite2')}
            >
              {getStatusIcon(getProgressStatus('activite2'))}
              <div className="ml-2 flex items-center">
                <Link size={16} className="mr-1.5" />
                <span>{t('workshop2.activities.evaluateSROV')}</span>
              </div>
            </button>
            <button
              className={`flex items-center px-4 py-2 rounded-md ${
                activeActivity === 'activite3'
                  ? 'bg-blue-50 text-blue-700 font-medium'
                  : 'text-gray-600 hover:bg-gray-50'
              }`}
              onClick={() => setActiveActivity('activite3')}
            >
              {getStatusIcon(getProgressStatus('activite3'))}
              <div className="ml-2 flex items-center">
                <CheckSquare size={16} className="mr-1.5" />
                <span>{t('workshop2.activities.selectSROV')}</span>
              </div>
            </button>
          </div>
        </div>

        {/* Activity Content */}
        <div>
          {activeActivity === 'activite1' && <Activite1 />}
          {activeActivity === 'activite2' && <Activite2 />}
          {activeActivity === 'activite3' && <Activite3 />}
        </div>
      </div>
    </div>
  );
};

export default Atelier2;
