// src/components/Atelier3/Activite3/Activite3.js
import React, { useState, useEffect } from 'react';
import { Target, Shield, AlertTriangle, Info, ChevronDown, ChevronUp, Search, Download } from 'lucide-react';
import { useAnalysis } from '../../../context/AnalysisContext';
import { showLoadingToast, dismissToast, showErrorToast } from '../../../utils/toastUtils';
import attackPathsService from '../../../services/attackPathsService';

const Activite3 = () => {
  // State for data
  const [attackPaths, setAttackPaths] = useState([]);
  const [stakeholders, setStakeholders] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [sortField, setSortField] = useState('sourceRiskName');
  const [sortDirection, setSortDirection] = useState('asc');
  const [expandedRows, setExpandedRows] = useState({});

  // Get analysis context
  const {
    currentAnalysis,
    getStakeholders
  } = useAnalysis();

  // Load data on component mount
  useEffect(() => {
    const fetchData = async () => {
      if (currentAnalysis?.id) {
        setIsLoading(true);
        setError(null);
        const toastId = showLoadingToast('Chargement des chemins d\'attaque...');

        try {
          // Load attack paths
          const response = await attackPathsService.getAttackPaths(currentAnalysis.id);
          if (response.success && Array.isArray(response.data)) {
            setAttackPaths(response.data);
          } else {
            console.error('Invalid attack paths data:', response);
            setAttackPaths([]);
          }

          // Load stakeholders
          try {
            const stakeholdersResponse = await getStakeholders(currentAnalysis.id);
            if (stakeholdersResponse && stakeholdersResponse.success && Array.isArray(stakeholdersResponse.data)) {
              setStakeholders(stakeholdersResponse.data);
            } else {
              console.log('Stakeholders data is not valid:', stakeholdersResponse);
              setStakeholders([]);
            }
          } catch (stakeholderError) {
            console.error('Error loading stakeholders:', stakeholderError);
            setStakeholders([]);
          }

          // Dismiss the loading toast without showing a success toast
          dismissToast(toastId);
        } catch (error) {
          console.error('Error loading data:', error);
          setError('Erreur lors du chargement des données. Veuillez réessayer.');
          // Dismiss the loading toast and show error toast
          dismissToast(toastId);
          showErrorToast('Erreur lors du chargement des chemins d\'attaque');
        } finally {
          setIsLoading(false);
        }
      }
    };

    fetchData();
  }, [currentAnalysis?.id, getStakeholders]);

  // Handle sort
  const handleSort = (field) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  // Toggle row expansion
  const toggleRowExpansion = (id) => {
    setExpandedRows(prev => ({
      ...prev,
      [id]: !prev[id]
    }));
  };

  // Filter and sort attack paths
  const filteredAttackPaths = attackPaths
    .filter(path => {
      if (!searchTerm) return true;
      
      const searchLower = searchTerm.toLowerCase();
      return (
        (path.sourceRiskName && path.sourceRiskName.toLowerCase().includes(searchLower)) ||
        (path.objectifVise && path.objectifVise.toLowerCase().includes(searchLower)) ||
        (path.dreadedEventName && path.dreadedEventName.toLowerCase().includes(searchLower)) ||
        (path.businessValueName && path.businessValueName.toLowerCase().includes(searchLower))
      );
    })
    .sort((a, b) => {
      let aValue = a[sortField] || '';
      let bValue = b[sortField] || '';
      
      if (typeof aValue === 'string') aValue = aValue.toLowerCase();
      if (typeof bValue === 'string') bValue = bValue.toLowerCase();
      
      if (aValue < bValue) return sortDirection === 'asc' ? -1 : 1;
      if (aValue > bValue) return sortDirection === 'asc' ? 1 : -1;
      return 0;
    });

  // Group attack paths by source risk
  const groupedAttackPaths = filteredAttackPaths.reduce((acc, path) => {
    const sourceId = path.sourceRiskId;
    if (!acc[sourceId]) {
      acc[sourceId] = {
        sourceRiskId: sourceId,
        sourceRiskName: path.sourceRiskName,
        objectifVise: path.objectifVise,
        paths: []
      };
    }
    acc[sourceId].paths.push(path);
    return acc;
  }, {});

  // Export to CSV
  const exportToCSV = () => {
    // Create CSV header
    const header = [
      'Source de Risque',
      'Objectif Visé',
      'Événement Redouté',
      'Valeur Métier',
      'Parties Prenantes'
    ].join(',');

    // Create CSV rows
    const rows = filteredAttackPaths.map(path => {
      const stakeholderNames = path.stakeholders
        ? path.stakeholders.map(s => s.name).join('; ')
        : '';

      return [
        `"${path.sourceRiskName || ''}"`,
        `"${path.objectifVise || ''}"`,
        `"${path.dreadedEventName || ''}"`,
        `"${path.businessValueName || ''}"`,
        `"${stakeholderNames}"`
      ].join(',');
    });

    // Combine header and rows
    const csv = [header, ...rows].join('\n');

    // Create download link
    const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.setAttribute('href', url);
    link.setAttribute('download', `chemins-attaque-${new Date().toISOString().split('T')[0]}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  // Render sort indicator
  const renderSortIndicator = (field) => {
    if (sortField !== field) return null;
    return sortDirection === 'asc' ? <ChevronUp size={14} /> : <ChevronDown size={14} />;
  };

  return (
    <div className="space-y-8 p-4 sm:p-6 lg:p-8 bg-gradient-to-br from-slate-50 to-blue-50 min-h-screen">
      {/* Loading state */}
      {isLoading ? (
        <div className="flex justify-center items-center h-64 bg-slate-50 rounded-xl shadow-sm">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-slate-600">Chargement des chemins d'attaque...</p>
          </div>
        </div>
      ) : error ? (
        <div className="p-6 bg-slate-50 rounded-lg">
          <div className="bg-red-100 border border-red-300 text-red-800 p-4 rounded-lg mb-4 shadow-sm">
            <h3 className="font-bold mb-2 text-lg flex items-center">
              <Target size={20} className="mr-2"/>
              Erreur de chargement
            </h3>
            <p className="mb-3">{error}</p>
            <button
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition duration-200 text-sm"
              onClick={() => window.location.reload()}
            >
              Réessayer
            </button>
          </div>
        </div>
      ) : (
        <div className="bg-white rounded-xl shadow-md overflow-hidden">
          {/* Header */}
          <div className="p-5 border-b border-slate-200 bg-gradient-to-r from-blue-50 to-white">
            <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
              <div>
                <h2 className="text-xl font-semibold text-slate-800 flex items-center">
                  <Target size={20} className="mr-2 text-blue-600" />
                  Chemins d'Attaque
                </h2>
                <p className="text-sm text-slate-500 mt-1">
                  Visualisez les chemins d'attaque identifiés pour votre analyse de risque.
                </p>
              </div>
              <div className="flex items-center space-x-3">
                {/* Search input */}
                <div className="relative">
                  <input
                    type="text"
                    placeholder="Rechercher..."
                    className="pl-9 pr-4 py-2 border border-slate-300 rounded-lg focus:ring-blue-500 focus:border-blue-500 w-full md:w-64 shadow-sm"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                  <Search size={16} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400" />
                </div>

                {/* Export button */}
                <button
                  onClick={exportToCSV}
                  className="flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition duration-200 shadow-sm"
                >
                  <Download size={16} className="mr-2" />
                  Exporter CSV
                </button>
              </div>
            </div>
          </div>

          {/* Table */}
          {filteredAttackPaths.length === 0 ? (
            <div className="p-8 text-center">
              <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-blue-50 text-blue-500 mb-4">
                <Info size={24} />
              </div>
              <h3 className="text-lg font-medium text-slate-900 mb-2">Aucun chemin d'attaque trouvé</h3>
              <p className="text-slate-500 max-w-md mx-auto">
                Aucun chemin d'attaque n'a été défini pour cette analyse. Veuillez retourner à l'Activité 2 pour définir des chemins d'attaque.
              </p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-slate-200">
                <thead className="bg-slate-50">
                  <tr>
                    <th 
                      scope="col" 
                      className="px-4 py-3 text-left text-xs font-medium text-slate-700 uppercase tracking-wider cursor-pointer"
                      onClick={() => handleSort('sourceRiskName')}
                    >
                      <div className="flex items-center">
                        Source de Risque
                        {renderSortIndicator('sourceRiskName')}
                      </div>
                    </th>
                    <th 
                      scope="col" 
                      className="px-4 py-3 text-left text-xs font-medium text-slate-700 uppercase tracking-wider cursor-pointer"
                      onClick={() => handleSort('objectifVise')}
                    >
                      <div className="flex items-center">
                        Objectif Visé
                        {renderSortIndicator('objectifVise')}
                      </div>
                    </th>
                    <th 
                      scope="col" 
                      className="px-4 py-3 text-left text-xs font-medium text-slate-700 uppercase tracking-wider cursor-pointer"
                      onClick={() => handleSort('dreadedEventName')}
                    >
                      <div className="flex items-center">
                        Événement Redouté
                        {renderSortIndicator('dreadedEventName')}
                      </div>
                    </th>
                    <th 
                      scope="col" 
                      className="px-4 py-3 text-left text-xs font-medium text-slate-700 uppercase tracking-wider cursor-pointer"
                      onClick={() => handleSort('businessValueName')}
                    >
                      <div className="flex items-center">
                        Valeur Métier
                        {renderSortIndicator('businessValueName')}
                      </div>
                    </th>
                    <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-slate-700 uppercase tracking-wider">
                      Parties Prenantes
                    </th>
                    <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-slate-700 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-slate-200">
                  {filteredAttackPaths.map((path) => (
                    <tr 
                      key={path.id} 
                      className={`hover:bg-slate-50 ${expandedRows[path.id] ? 'bg-slate-50' : ''}`}
                    >
                      <td className="px-4 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-slate-900">{path.sourceRiskName}</div>
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap">
                        <div className="text-sm text-slate-700">{path.objectifVise}</div>
                      </td>
                      <td className="px-4 py-4">
                        <div className="text-sm text-slate-700 flex items-start">
                          <AlertTriangle size={16} className="text-amber-500 mr-2 mt-0.5 flex-shrink-0" />
                          <span>{path.dreadedEventName}</span>
                        </div>
                      </td>
                      <td className="px-4 py-4">
                        <div className="text-sm text-slate-700">{path.businessValueName}</div>
                      </td>
                      <td className="px-4 py-4">
                        <div className="flex flex-wrap gap-1">
                          {path.stakeholders && path.stakeholders.map((stakeholder) => (
                            <span 
                              key={stakeholder.id} 
                              className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-teal-100 text-teal-800"
                            >
                              {stakeholder.name}
                            </span>
                          ))}
                        </div>
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <button
                          onClick={() => toggleRowExpansion(path.id)}
                          className="text-blue-600 hover:text-blue-900 flex items-center"
                        >
                          {expandedRows[path.id] ? (
                            <>
                              <ChevronUp size={16} className="mr-1" />
                              Réduire
                            </>
                          ) : (
                            <>
                              <ChevronDown size={16} className="mr-1" />
                              Détails
                            </>
                          )}
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default Activite3;
