# Système d'authentification EBIOS RM

## Vue d'ensemble

Le système d'authentification d'EBIOS RM utilise JWT (JSON Web Tokens) pour sécuriser l'accès à l'application. Il implémente:

- Une authentification basée sur les JWT avec access token (courte durée) et refresh token (longue durée)
- Un hachage sécurisé des mots de passe avec bcrypt
- Un système de vérification des permissions basé sur les rôles (superadmin, admin, simpleuser)
- Une gestion centralisée de l'état d'authentification avec React Context
- Des routes protégées selon les rôles des utilisateurs

## Architecture

### Frontend

- **AuthContext** : Fournit l'état d'authentification et les fonctions à toute l'application
- **apiClient** : Client Axios avec intercepteurs pour la gestion des tokens
- **tokenStorage** : Utilitaires pour la gestion des tokens dans le localStorage
- **PrivateRoute** : Composant pour protéger les routes en fonction des rôles
- **LoginForm** : Formulaire de connexion utilisateur

### Backend

- **Middleware d'authentification** : Vérifie les tokens JWT dans les en-têtes de requête
- **Middleware d'autorisation** : Vérifie les permissions selon les rôles
- **Service d'authentification** : Génère et vérifie les tokens JWT
- **Controllers** : Gèrent les requêtes d'authentification (login, token refresh, logout)

## Rôles utilisateurs

- **superadmin** : Accès à toutes les fonctionnalités et à l'administration globale de la plateforme
- **admin** : Accès à la gestion de son entreprise (Provider)
- **simpleuser** : Accès aux fonctionnalités d'analyse (Analyste ou lecteur)

## Flux d'authentification

1. **Connexion** : L'utilisateur s'authentifie avec email et mot de passe
2. **Tokens** : Le serveur génère un access token (30 min) et un refresh token (7 jours)
3. **Accès API** : L'access token est envoyé dans l'en-tête Authorization pour chaque requête
4. **Rafraîchissement** : Lorsque l'access token expire, le refresh token est utilisé pour obtenir un nouveau access token
5. **Déconnexion** : Le refresh token est invalidé sur le serveur

## Configuration

Les variables d'environnement permettent de configurer:

- L'URL de l'API backend
- La durée de vie des tokens
- Les clés de hachage (en production)

## Installation

1. Installez les dépendances :
```bash
npm install axios bcryptjs jsonwebtoken jwt-decode
```

2. Créez les fichiers .env nécessaires:
```
# .env.development (déjà configuré)
```

## Utilisation

### Authentification

```jsx
import { useAuth } from './context/AuthContext';

function MyComponent() {
  const { user, login, logout, isAuthenticated } = useAuth();
  
  const handleLogin = async () => {
    const result = await login('<EMAIL>', 'password');
    if (result.success) {
      // Connexion réussie
    }
  };
  
  return (
    <div>
      {isAuthenticated ? (
        <button onClick={logout}>Déconnexion</button>
      ) : (
        <button onClick={handleLogin}>Connexion</button>
      )}
    </div>
  );
}
```

### Protection des routes

```jsx
import PrivateRoute from './components/common/PrivateRoute';

function App() {
  return (
    <Routes>
      <Route path="/login" element={<LoginForm />} />
      
      {/* Route accessible uniquement aux utilisateurs authentifiés */}
      <Route 
        path="/dashboard" 
        element={
          <PrivateRoute>
            <Dashboard />
          </PrivateRoute>
        } 
      />
      
      {/* Route accessible uniquement aux admins */}
      <Route 
        path="/admin" 
        element={
          <PrivateRoute roles={['superadmin', 'admin']}>
            <AdminPanel />
          </PrivateRoute>
        } 
      />
    </Routes>
  );
}
```

### Requêtes API sécurisées

```jsx
import { api } from './api/apiClient';

// L'intercepteur gère automatiquement l'ajout du token
async function fetchData() {
  try {
    const response = await api.get('/users');
    return response.data;
  } catch (error) {
    console.error('Error fetching data:', error);
  }
}
```

## Sécurité

Ce système implémente plusieurs bonnes pratiques de sécurité:

- Mots de passe hachés avec bcrypt (10 rounds)
- Tokens JWT signés avec des clés secrètes
- Double système de tokens pour limiter l'exposition
- Vérification fine des permissions basée sur les rôles
- Liste noire de refresh tokens pour les déconnexions

## Développement

Pour le développement, un serveur backend simple est fourni pour simuler les API d'authentification. Pour le lancer:

```bash
npm run server
```

Pour lancer simultanément le frontend et le backend:

```bash
npm run dev
```