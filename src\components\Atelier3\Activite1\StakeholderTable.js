// src/components/Atelier3/Activite1/StakeholderTable.js
import React, { useState } from 'react';
import { Edit, Trash2, ChevronUp, ChevronDown, AlertCircle, Shield, Users, Building, Check, X, ThumbsUp, ThumbsDown } from 'lucide-react';
import { useTranslation } from 'react-i18next';

const StakeholderTable = ({ stakeholders, onEdit, onDelete, onToggleRetained, isLoading }) => {
  const { t } = useTranslation();
  const [sortField, setSortField] = useState('threatLevel');
  const [sortDirection, setSortDirection] = useState('desc');

  // Handle sort click
  const handleSort = (field) => {
    if (sortField === field) {
      // Toggle direction if same field
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      // Set new field and default to ascending
      setSortField(field);
      setSortDirection('asc');
    }
  };

  // Sort stakeholders
  const sortedStakeholders = [...stakeholders].sort((a, b) => {
    let aValue = a[sortField];
    let bValue = b[sortField];

    // Handle string comparison
    if (typeof aValue === 'string' && typeof bValue === 'string') {
      aValue = aValue.toLowerCase();
      bValue = bValue.toLowerCase();
    }

    // Compare values
    if (aValue < bValue) return sortDirection === 'asc' ? -1 : 1;
    if (aValue > bValue) return sortDirection === 'asc' ? 1 : -1;
    return 0;
  });

  // Get threat zone based on threat level
  const getThreatZone = (threatLevel) => {
    if (threatLevel >= 3) return 'danger';
    if (threatLevel >= 1.5) return 'control';
    if (threatLevel >= 0.5) return 'watch';
    return 'outside';
  };

  // Get color for threat zone
  const getThreatZoneColor = (zone) => {
    switch (zone) {
      case 'danger': return 'text-red-700 bg-red-100 border border-red-300';
      case 'control': return 'text-orange-700 bg-orange-100 border border-orange-300';
      case 'watch': return 'text-teal-700 bg-teal-100 border border-teal-300';
      default: return 'text-slate-700 bg-slate-100 border border-slate-300';
    }
  };

  // Get text for threat zone
  const getThreatZoneText = (zone) => {
    switch (zone) {
      case 'danger': return 'Zone de danger';
      case 'control': return 'Zone de contrôle';
      case 'watch': return 'Zone de veille';
      default: return 'Hors périmètre';
    }
  };

  // Get icon for category
  const getCategoryIcon = (category) => {
    switch (category) {
      case 'client': return <Users size={16} className="text-blue-500" />;
      case 'partner': return <Building size={16} className="text-green-500" />;
      case 'provider': return <Shield size={16} className="text-purple-500" />;
      case 'technical': return <AlertCircle size={16} className="text-orange-500" />;
      case 'business': return <Building size={16} className="text-indigo-500" />;
      case 'subsidiary': return <Building size={16} className="text-pink-500" />;
      default: return <Users size={16} className="text-gray-500" />;
    }
  };

  // Get text for category
  const getCategoryText = (category) => {
    switch (category) {
      case 'client': return t('workshop3.activity1.categories.client', 'Client');
      case 'partner': return t('workshop3.activity1.categories.partner', 'Partner');
      case 'provider': return t('workshop3.activity1.categories.provider', 'Provider');
      case 'technical': return t('workshop3.activity1.categories.technical', 'Technical service');
      case 'business': return t('workshop3.activity1.categories.business', 'Business service');
      case 'subsidiary': return t('workshop3.activity1.categories.subsidiary', 'Subsidiary');
      default: return category;
    }
  };

  // Get text for type
  const getTypeText = (type) => {
    return type === 'internal' ? t('workshop3.activity1.filter.internal', 'Internal') : t('workshop3.activity1.filter.external', 'External');
  };

  // Get text for rank
  const getRankText = (rank) => {
    return `${t('workshop3.activity1.table.rank', 'Rank')} ${rank}`;
  };

  // Render sort icon
  const renderSortIcon = (field) => {
    if (sortField !== field) return null;

    return sortDirection === 'asc'
      ? <ChevronUp size={16} className="ml-1" />
      : <ChevronDown size={16} className="ml-1" />;
  };

  // Render loading state
  if (isLoading) {
    return (
      <div className="p-8 text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto"></div>
        <p className="mt-4 text-gray-600">{t('workshop3.activity1.loading', 'Loading stakeholders...')}</p>
      </div>
    );
  }

  // Render empty state
  if (stakeholders.length === 0) {
    return (
      <div className="p-8 text-center">
        <div className="bg-blue-50 p-4 rounded-full inline-flex items-center justify-center mb-4">
          <Users size={32} className="text-blue-500" />
        </div>
        <h3 className="text-lg font-medium text-gray-900 mb-2">{t('workshop3.activity1.empty.noStakeholders', 'No stakeholders')}</h3>
        <p className="text-gray-600 mb-4">
          {t('workshop3.activity1.empty.addStakeholders', 'Add stakeholders to start mapping the ecosystem.')}
        </p>
      </div>
    );
  }

  return (
    <div className="p-1"> {/* Adjusted padding for the main container */}
      {/* Sort Controls */}
      <div className="mb-4 flex flex-wrap gap-2 items-center bg-slate-50 p-3 rounded-md">
        <span className="text-sm font-medium text-slate-700 mr-2">{t('workshop3.activity1.table.sortBy', 'Sort by')}:</span>
        {[
          { label: t('workshop3.activity1.table.name', 'Name'), field: 'name' },
          { label: t('workshop3.activity1.table.category', 'Category'), field: 'category' },
          { label: t('workshop3.activity1.table.type', 'Type'), field: 'type' },
          { label: t('workshop3.activity1.table.threat', 'Threat'), field: 'threatLevel' },
        ].map(item => (
          <button
            key={item.field}
            onClick={() => handleSort(item.field)}
            className={`px-3 py-1.5 text-xs rounded-md flex items-center transition-colors
              ${sortField === item.field
                ? 'bg-blue-600 text-white shadow-sm'
                : 'bg-white text-slate-600 hover:bg-slate-100 border border-slate-300'}`}
          >
            {item.label}
            {sortField === item.field && (sortDirection === 'asc'
              ? <ChevronUp size={14} className="ml-1" />
              : <ChevronDown size={14} className="ml-1" />
            )}
          </button>
        ))}
      </div>

      {/* Cards Container - 2 cards per row */}
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-2 xl:grid-cols-2 2xl:grid-cols-2 gap-4">
        {sortedStakeholders.map((stakeholder) => {
          const threatZone = getThreatZone(stakeholder.threatLevel);
          const zoneColorClasses = getThreatZoneColor(threatZone);

          return (
            <div
              key={stakeholder.id}
              className="bg-white rounded-md shadow-sm border border-slate-200 hover:shadow transition-shadow duration-300 flex flex-col overflow-hidden"
            >
              {/* Card Header - More compact */}
              <div className="p-2 border-b border-slate-200 bg-slate-50 flex justify-between items-center">
                <div className="flex items-center space-x-2">
                  <div className={`w-2 h-full rounded-full ${stakeholder.retained ? 'bg-blue-500' : 'bg-gray-300'}`}></div>
                  <h3 className="text-sm font-medium text-slate-800 truncate max-w-[220px]" title={stakeholder.name}>{stakeholder.name}</h3>
                </div>
                <div className="flex space-x-1">
                  <button
                    onClick={() => onEdit(stakeholder)}
                    className="p-1 text-blue-600 hover:text-blue-800 hover:bg-blue-50 rounded"
                    title={t('common.edit', 'Edit')}
                  >
                    <Edit size={14} />
                  </button>
                  <button
                    onClick={() => onDelete(stakeholder.id)}
                    className="p-1 text-red-600 hover:text-red-800 hover:bg-red-50 rounded"
                    title={t('common.delete', 'Delete')}
                  >
                    <Trash2 size={14} />
                  </button>
                </div>
              </div>

              {/* Card Body - More compact with grid layout */}
              <div className="p-3 text-xs space-y-2">
                {/* Category */}
                <div className="flex items-center justify-between">
                  <span className="text-slate-500">{t('workshop3.activity1.table.category', 'Category')}:</span>
                  <div className="flex items-center font-medium text-slate-700 max-w-[65%]">
                    {getCategoryIcon(stakeholder.category)}
                    <span className="ml-1 truncate" title={getCategoryText(stakeholder.category)}>{getCategoryText(stakeholder.category)}</span>
                  </div>
                </div>

                {/* Type/Rank */}
                <div className="flex items-center justify-between">
                  <span className="text-slate-500">{t('workshop3.activity1.table.type', 'Type')}/{t('workshop3.activity1.table.rank', 'Rank')}:</span>
                  <div className="font-medium text-slate-700 truncate max-w-[65%]" title={`${getTypeText(stakeholder.type)} (${getRankText(stakeholder.rank)})`}>
                    {getTypeText(stakeholder.type)} ({getRankText(stakeholder.rank)})
                  </div>
                </div>

                {/* Threat Level */}
                <div className="flex items-center justify-between">
                  <span className="text-slate-500">{t('workshop3.activity1.table.threat', 'Threat')}:</span>
                  <span className={`font-medium px-1.5 py-0.5 rounded text-xs ${
                    stakeholder.threatLevel >= 3 ? 'bg-red-100 text-red-700' :
                    stakeholder.threatLevel >= 1.5 ? 'bg-orange-100 text-orange-700' :
                    'bg-teal-100 text-teal-700'
                  }`}>{stakeholder.threatLevel.toFixed(2)}</span>
                </div>

                {/* Metrics */}
                <div className="mt-2 pt-2 border-t border-slate-100">
                  <div className="grid grid-cols-4 gap-2 text-center">
                    <div title={t('workshop3.activity1.stakeholderForm.dependency', 'Dependency')} className="flex flex-col items-center bg-slate-50 rounded p-1">
                      <span className="text-slate-500 text-[10px]">{t('workshop3.activity1.table.dependency', 'Dep')}</span>
                      <span className={`font-medium ${stakeholder.dependency >= 3 ? 'text-red-600' : 'text-slate-600'}`}>{stakeholder.dependency}</span>
                    </div>
                    <div title={t('workshop3.activity1.stakeholderForm.penetration', 'Penetration')} className="flex flex-col items-center bg-slate-50 rounded p-1">
                      <span className="text-slate-500 text-[10px]">{t('workshop3.activity1.table.penetration', 'Pen')}</span>
                      <span className={`font-medium ${stakeholder.penetration >= 3 ? 'text-red-600' : 'text-slate-600'}`}>{stakeholder.penetration}</span>
                    </div>
                    <div title={t('workshop3.activity1.stakeholderForm.cyberMaturity', 'Cyber Maturity')} className="flex flex-col items-center bg-slate-50 rounded p-1">
                      <span className="text-slate-500 text-[10px]">{t('workshop3.activity1.table.cyberMaturity', 'Mat')}</span>
                      <span className={`font-medium ${stakeholder.cyberMaturity <= 2 ? 'text-red-600' : 'text-slate-600'}`}>{stakeholder.cyberMaturity}</span>
                    </div>
                    <div title={t('workshop3.activity1.stakeholderForm.trust', 'Trust')} className="flex flex-col items-center bg-slate-50 rounded p-1">
                      <span className="text-slate-500 text-[10px]">{t('workshop3.activity1.table.trust', 'Trust')}</span>
                      <span className={`font-medium ${stakeholder.trust <= 2 ? 'text-red-600' : 'text-slate-600'}`}>{stakeholder.trust}</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Retained Status - Simplified */}
              <div className="px-3 py-2 border-t border-slate-200 flex items-center justify-between bg-slate-50">
                <span className="text-xs font-medium text-slate-600">{t('workshop3.activity1.table.retained', 'Retained')}:</span>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    className="sr-only peer"
                    checked={stakeholder.retained}
                    onChange={() => onToggleRetained(stakeholder.id)}
                  />
                  <div className="w-9 h-5 bg-gray-200 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-4 after:w-4 after:transition-all peer-checked:bg-blue-600"></div>
                </label>
              </div>

              {/* Card Footer - Threat Zone - More compact */}
              <div className={`py-2 px-3 border-t border-slate-200 ${zoneColorClasses} flex items-center justify-center`}>
                <AlertCircle size={14} className="mr-1.5" />
                <span className="text-xs font-medium">
                  {getThreatZoneText(threatZone)}
                </span>
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default StakeholderTable;
