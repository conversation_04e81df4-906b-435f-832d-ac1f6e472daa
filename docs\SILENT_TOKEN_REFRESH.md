 # Silent Token Refresh Implementation

Ce document explique l'implémentation du rafraîchissement silencieux des tokens d'authentification dans l'application EBIOS RM.

## Problème résolu

Lorsqu'un token d'authentification expire, l'application doit le rafraîchir pour maintenir la session de l'utilisateur. Sans une solution de rafraîchissement silencieux, l'application peut:
- Rediriger l'utilisateur vers la page de connexion
- Recharger la page entière
- Perdre les données non sauvegardées

Notre implémentation résout ces problèmes en rafraîchissant le token en arrière-plan sans interruption pour l'utilisateur.

## Composants de la solution

### 1. Utilitaire de rafraîchissement de token (`tokenRefresh.js`)

Ce module gère la logique de surveillance et de rafraîchissement des tokens:

- **Surveillance proactive**: Vérifie régulièrement si le token approche de son expiration
- **Rafraîchissement anticipé**: Rafra<PERSON><PERSON><PERSON> le token 10 minutes avant son expiration
- **Gestion des erreurs**: Réessaie automatiquement en cas d'échec
- **Gestion de l'état réseau**: Vérifie à nouveau lorsque la connexion est rétablie

### 2. Client API amélioré (`apiClient.js`)

Le client API a été modifié pour:

- **Gérer les erreurs 401**: Rafraîchit automatiquement le token et réessaie la requête
- **File d'attente des requêtes**: Met en file d'attente les requêtes pendant le rafraîchissement
- **Événements de notification**: Émet des événements pour informer l'utilisateur du statut

### 3. Contexte d'authentification (`AuthContext.jsx`)

Le contexte d'authentification:

- **Initialise le moniteur**: Démarre la surveillance du token lorsqu'un utilisateur est connecté
- **Gère le cycle de vie**: Arrête la surveillance lorsque l'utilisateur se déconnecte
- **Expose les fonctions**: Fournit des méthodes pour rafraîchir manuellement le token

### 4. Composant de notification (`TokenRefreshNotification.js`)

Un composant discret qui:

- **Écoute les événements**: Réagit aux événements de rafraîchissement de token
- **Affiche des notifications**: Informe l'utilisateur du statut sans interrompre son travail
- **Disparaît automatiquement**: Les notifications de succès disparaissent après quelques secondes

## Flux de rafraîchissement de token

1. Le moniteur détecte qu'un token va expirer dans les 10 minutes
2. Une notification discrète apparaît indiquant "Rafraîchissement de votre session..."
3. Le token est rafraîchi en arrière-plan
4. Une notification de succès apparaît brièvement
5. L'utilisateur continue à travailler sans interruption

## Gestion des erreurs

Si le rafraîchissement échoue:

1. Le système réessaie automatiquement après 30 secondes
2. Si plusieurs tentatives échouent, une notification d'erreur est affichée
3. L'utilisateur peut continuer à travailler et sauvegarder ses données
4. Une boîte de dialogue apparaît uniquement en dernier recours, donnant à l'utilisateur le choix de se reconnecter ou de rester sur la page

## Avantages de cette approche

- **Expérience utilisateur fluide**: Aucune interruption du flux de travail
- **Prévention de la perte de données**: Pas de rechargement de page inattendu
- **Transparence**: L'utilisateur est informé sans être dérangé
- **Robustesse**: Gestion des erreurs réseau et des problèmes de connexion

## Comment tester

Pour tester le rafraîchissement silencieux des tokens:

1. Connectez-vous à l'application
2. Ouvrez la console développeur (F12)
3. Exécutez `localStorage.setItem('ebiosrm_access_token', 'expired_token')` pour simuler un token expiré
4. Effectuez une action qui déclenche une requête API
5. Observez dans la console et l'interface utilisateur comment le token est rafraîchi sans rechargement

## Limitations

- Nécessite que le refresh token soit valide
- Dépend de la disponibilité du serveur d'authentification
- Nécessite JavaScript activé dans le navigateur
