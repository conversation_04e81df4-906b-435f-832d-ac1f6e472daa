// src/components/Atelier4/ScenarioGenerator.js
import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Brain, Info, BookOpen, Zap, Target } from 'lucide-react';
import OperationalScenarioGenerator from './Activite1/OperationalScenarioGenerator';
import { useAnalysis } from '../../context/AnalysisContext';

const ScenarioGenerator = () => {
  const [selectedAttackPath, setSelectedAttackPath] = useState(null);
  const [threatIntelligence, setThreatIntelligence] = useState(null);
  const [businessAssets, setBusinessAssets] = useState([]);
  const [attackPaths, setAttackPaths] = useState([]);
  const [generatedScenarios, setGeneratedScenarios] = useState([]);
  const { currentAnalysis } = useAnalysis();

  // Load data when component mounts
  useEffect(() => {
    const loadData = async () => {
      if (currentAnalysis?.id) {
        try {
          console.log('[ScenarioGenerator] Loading data for analysis:', currentAnalysis.id);
          
          // Mock attack paths
          const mockAttackPaths = [
            {
              id: 'path1',
              referenceCode: 'CA01',
              sourceRiskName: 'Concurrent malveillant',
              targetObjective: 'Données clients',
              dreadedEventName: 'Vol de données personnelles',
              description: 'Attaque ciblée pour voler les données clients'
            },
            {
              id: 'path2',
              referenceCode: 'CA02', 
              sourceRiskName: 'Employé mécontent',
              targetObjective: 'Système de production',
              dreadedEventName: 'Sabotage du système',
              description: 'Sabotage interne du système de production'
            }
          ];
          
          // Mock business assets
          const mockAssets = [
            { id: 'asset1', name: 'Serveur Web', description: 'Serveur web principal', type: 'Infrastructure' },
            { id: 'asset2', name: 'Base de données', description: 'Base de données clients', type: 'Data' },
            { id: 'asset3', name: 'Application métier', description: 'Application de gestion', type: 'Application' }
          ];
          
          setAttackPaths(mockAttackPaths);
          setBusinessAssets(mockAssets);
        } catch (error) {
          console.error('[ScenarioGenerator] Error loading data:', error);
        }
      }
    };

    loadData();
  }, [currentAnalysis?.id]);

  // Handle scenario generation
  const handleScenarioGenerated = (scenario) => {
    console.log('[ScenarioGenerator] New scenario generated:', scenario);
    setGeneratedScenarios(prev => [...prev, scenario]);
  };

  // Handle threat intelligence update
  const handleThreatIntelligenceUpdate = (threatData) => {
    setThreatIntelligence(threatData);
    console.log('[ScenarioGenerator] Threat intelligence updated:', threatData);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-100">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-800 mb-2 flex items-center">
              <Brain size={28} className="mr-3 text-indigo-600" />
              Phase 4: Générateur IA de scénarios
            </h1>
            <p className="text-gray-600">
              Génération automatique de scénarios opérationnels enrichis avec l'intelligence des menaces.
            </p>
          </div>
          <div className="flex items-center space-x-2 text-sm text-gray-500">
            <Info size={16} />
            <span>IA + CVE + MITRE ATT&CK</span>
          </div>
        </div>

        {/* AI Generation Process */}
        <div className="bg-indigo-50 p-4 rounded-lg border border-indigo-200">
          <h3 className="font-medium text-indigo-800 mb-3 flex items-center">
            <BookOpen size={16} className="mr-2" />
            Processus de génération IA
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 text-sm">
            <div className="bg-white p-3 rounded border border-indigo-100">
              <div className="font-medium text-indigo-700 mb-1 flex items-center">
                <Target size={14} className="mr-1" />
                Sélection
              </div>
              <div className="text-gray-600">Choisir un chemin d'attaque stratégique</div>
            </div>
            <div className="bg-white p-3 rounded border border-indigo-100">
              <div className="font-medium text-indigo-700 mb-1 flex items-center">
                <Brain size={14} className="mr-1" />
                Enrichissement
              </div>
              <div className="text-gray-600">Intégration des données CVE et ATT&CK</div>
            </div>
            <div className="bg-white p-3 rounded border border-indigo-100">
              <div className="font-medium text-indigo-700 mb-1 flex items-center">
                <Zap size={14} className="mr-1" />
                Génération
              </div>
              <div className="text-gray-600">Création du scénario 4-phases</div>
            </div>
            <div className="bg-white p-3 rounded border border-indigo-100">
              <div className="font-medium text-indigo-700 mb-1 flex items-center">
                <Info size={14} className="mr-1" />
                Validation
              </div>
              <div className="text-gray-600">Révision et ajustement manuel</div>
            </div>
          </div>
        </div>
      </div>

      {/* Attack Path Selection */}
      {attackPaths.length > 0 && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-white p-6 rounded-xl shadow-sm border border-gray-100"
        >
          <h3 className="text-lg font-medium text-gray-800 mb-4">Sélectionner un chemin d'attaque</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {attackPaths.map((path) => (
              <div 
                key={path.id} 
                className={`border rounded-lg p-4 cursor-pointer transition-all ${
                  selectedAttackPath?.id === path.id 
                    ? 'border-indigo-500 bg-indigo-50' 
                    : 'border-gray-200 hover:border-indigo-300 hover:bg-gray-50'
                }`}
                onClick={() => setSelectedAttackPath(path)}
              >
                <div className="flex items-center justify-between mb-2">
                  <span className="font-mono text-sm font-medium text-indigo-600">{path.referenceCode}</span>
                  {selectedAttackPath?.id === path.id && (
                    <div className="w-3 h-3 bg-indigo-500 rounded-full"></div>
                  )}
                </div>
                <div className="space-y-1 text-sm">
                  <div><span className="font-medium">Source:</span> {path.sourceRiskName}</div>
                  <div><span className="font-medium">Objectif:</span> {path.targetObjective}</div>
                  <div><span className="font-medium">Événement:</span> {path.dreadedEventName}</div>
                </div>
              </div>
            ))}
          </div>
        </motion.div>
      )}

      {/* Scenario Generator Component */}
      <OperationalScenarioGenerator
        selectedAttackPath={selectedAttackPath}
        threatIntelligence={threatIntelligence}
        businessAssets={businessAssets}
        onScenarioGenerated={handleScenarioGenerated}
        onThreatIntelligenceUpdate={handleThreatIntelligenceUpdate}
      />

      {/* Generated Scenarios Summary */}
      {generatedScenarios.length > 0 && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-white p-6 rounded-xl shadow-sm border border-gray-100"
        >
          <h3 className="text-lg font-medium text-gray-800 mb-4">Scénarios générés</h3>
          
          <div className="space-y-4">
            {generatedScenarios.map((scenario, index) => (
              <div key={index} className="border border-gray-200 rounded-lg p-4">
                <div className="flex items-center justify-between mb-2">
                  <h4 className="font-medium text-gray-800">{scenario.name}</h4>
                  <div className="flex items-center space-x-2 text-sm">
                    <span className={`px-2 py-1 rounded text-xs font-medium ${
                      scenario.severity === 'Très élevé' ? 'bg-red-100 text-red-800' :
                      scenario.severity === 'Élevé' ? 'bg-orange-100 text-orange-800' :
                      scenario.severity === 'Moyen' ? 'bg-yellow-100 text-yellow-800' :
                      'bg-green-100 text-green-800'
                    }`}>
                      {scenario.severity}
                    </span>
                    <span className="text-gray-500">
                      {scenario.steps?.length || 0} étapes
                    </span>
                  </div>
                </div>
                <p className="text-sm text-gray-600 mb-3">{scenario.description}</p>
                <div className="flex items-center space-x-4 text-xs text-gray-500">
                  <span>Probabilité: {scenario.likelihood}</span>
                  <span>Durée: {scenario.timeline}</span>
                  <span>Compétences: {scenario.requiredSkills}</span>
                </div>
              </div>
            ))}
          </div>
        </motion.div>
      )}

      {/* AI Features */}
      <div className="bg-gray-50 p-6 rounded-xl border border-gray-200">
        <h3 className="font-medium text-gray-800 mb-3">Fonctionnalités IA avancées</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-600">
          <div className="space-y-2">
            <div className="font-medium text-gray-700">Intelligence des menaces</div>
            <div>• Intégration automatique des CVE récents</div>
            <div>• Mapping avec les techniques MITRE ATT&CK</div>
            <div>• Évaluation des scores de risque CVSS</div>
          </div>
          <div className="space-y-2">
            <div className="font-medium text-gray-700">Génération contextuelle</div>
            <div>• Adaptation aux actifs métier spécifiques</div>
            <div>• Respect du modèle EBIOS RM 4-phases</div>
            <div>• Recommandations de sécurité personnalisées</div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ScenarioGenerator;
