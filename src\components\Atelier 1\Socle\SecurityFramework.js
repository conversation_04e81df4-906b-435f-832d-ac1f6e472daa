// --- START OF FILE SecurityFramework.js ---

import React, { useReducer, useEffect, useCallback, useMemo, useState, useRef } from 'react';
import { useTranslation } from 'react-i18next';
// ** Step 1: Re-introduce useAnalysis **
import { useAnalysis } from '../../../context/AnalysisContext';
import frameworkDefinitionService from '../../../services/frameworkDefinitionService'; // Import the new service
import { showSuccessToast, showErrorToast, showLoadingToast, updateToast, showInfoToast } from '../../../utils/toastUtils';
import { Shield } from 'lucide-react';

// Import components (assuming FrameworkComponents.js is already updated)
import {
    FrameworkListItem, RuleItem, SelectedRuleCard,
    SecurityFrameworkSummaryTable, RuleDescriptionModal
} from './FrameworkComponents';
import { Card, Input } from './UIComponents';

// ** Step 2: Import your service for fetching definitions (Create this service if needed) **
// Assuming you have a service like this:
// import { getFrameworkDefinitions } from '../../../services/frameworkDefinitionService';
// ** Placeholder function if service doesn't exist yet **
const getFrameworkDefinitions = async () => {
    console.warn("Using placeholder for getFrameworkDefinitions API call!");
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 500));
    // Return data in the expected format
    return {
        success: true,
        data: [ // Using previous mock data as example response
            { id: "iso27001", name: "ISO/IEC 27001:2022 (Fetched)", description: "Information security standard", is_predefined: true, rules: [ { id: "iso-a5.1", name: "A.5.1 Policies for information security", description: "Define policies..." }, { id: "iso-a5.2", name: "A.5.2 Information security roles", description: "Assign roles..." }, { id: "iso-a5.3", name: "A.5.3 Segregation of duties", description: "Separate duties..." } ] },
            { id: "nist-csf", name: "NIST CSF v1.1 (Fetched)", description: "Cybersecurity Framework", is_predefined: true, rules: [ { id: "nist-id.am-1", name: "ID.AM-1: Physical devices", description: "Inventory devices..." }, { id: "nist-id.am-2", name: "ID.AM-2: Software platforms", description: "Inventory software..." }, { id: "nist-pr.at-1", name: "PR.AT-1: Security awareness training", description: "Train users..." } ] },
            { id: "custom-projx", name: "Exigences Projet X (Fetched)", description: "Custom project requirements", is_predefined: false, rules: [ { id: "projx-rule-1", name: "Accès spécifique API partenaires", description: "..." }, { id: "projx-rule-2", name: "Chiffrement données sensibles", description: "..." } ] }
        ]
    };
};
// --- (End Placeholder) ---


// --- Helper Function ---
/*
const ensureShortId = (item, prefix = '') => {
    if (item.shortId) return item.shortId;
    const idStr = String(item.id);
    const randomPart = Math.random().toString(36).substring(2, 5);
    return `${prefix}${idStr.substring(idStr.length - 4, idStr.length)}-${randomPart}`.toUpperCase();
};
*/

// --- Helper Functions ---
const parseCSV = (csvText) => {
    const lines = csvText.split('\n');
    return lines
        .filter(line => line.trim()) // Remove empty lines
        .map(line => {
            const [name, description] = line.split(';').map(s => s.trim());
            return {
                id: `custom-${Math.random().toString(36).substr(2, 9)}`,
                name,
                description,
                is_predefined: false
            };
        });
};

// --- Reducer Logic (UPDATED) ---
const initialState = {
    frameworkDefinitions: [],      // Will hold both predefined & custom from API + newly imported
    selectedRules: {},
    ruleBusinessValues: {},
    ruleDreadedEvents: {},
    isLoadingDefinitions: true,
    isSaving: false,
    error: null,                   // Combined load/save error state
    loadError: null,             // Specific loading error
    saveError: null,             // Specific saving error
    selectedFrameworkId: null,
    searchTerm: '',
    csvImportData: null,
    showImportModal: false,
    importError: null
};

function securityFrameworkReducer(state, action) {
    // Removed console log for reducer actions
    switch (action.type) {
        case 'LOAD_START':
            // Reset errors on load start
            return { ...state, isLoadingDefinitions: true, loadError: null, error: null };
        case 'LOAD_DEFINITIONS_SUCCESS':
             const definitions = action.payload.definitions || [];
             const firstFrameworkId = definitions[0]?.id || definitions[0]?._id || null; // Use _id if returned from DB
             const currentSelectedIsValid = definitions.some(f => (f.id || f._id) === state.selectedFrameworkId);
            return {
                ...state,
                isLoadingDefinitions: false,
                frameworkDefinitions: definitions,
                selectedFrameworkId: currentSelectedIsValid ? state.selectedFrameworkId : firstFrameworkId,
                loadError: null, // Clear load error on success
                error: null
            };
        case 'LOAD_SELECTIONS_SUCCESS':
            // This loads the *selections* saved in AnalysisComponent
            return {
                ...state,
                // No change to definitions here
                selectedRules: action.payload.savedData.selectedRules || {},
                ruleBusinessValues: action.payload.savedData.ruleBusinessValues || {},
                ruleDreadedEvents: action.payload.savedData.ruleDreadedEvents || {},
            };
        case 'LOAD_ERROR':
            return { ...state, isLoadingDefinitions: false, loadError: action.payload, error: action.payload };

        case 'ADD_CUSTOM_FRAMEWORK': {
            const { framework } = action.payload;
            // Add the is_new flag to track unsaved definitions
            const newFramework = { ...framework, is_new: true };
            const updatedDefinitions = [...state.frameworkDefinitions, newFramework];
            return {
                ...state,
                frameworkDefinitions: updatedDefinitions,
                selectedFrameworkId: newFramework.id, // Auto-select the new framework
                showImportModal: false, // Close modal implicitly on add
                csvImportData: null,
                importError: null
            };
        }
        case 'UPDATE_SAVED_DEFINITIONS': // NEW: Action to update definitions after saving
            return {
                 ...state,
                 frameworkDefinitions: action.payload.updatedDefinitions,
                 // Potentially re-evaluate selectedFrameworkId if IDs changed, but maybe not necessary
            };

        case 'SELECT_FRAMEWORK':
            return { ...state, selectedFrameworkId: action.payload, searchTerm: '' };
        case 'TOGGLE_RULE': {
             const { frameworkId, rule } = action.payload;
             const currentRules = state.selectedRules[frameworkId] || [];
             const ruleIndex = currentRules.findIndex(r => r.id === rule.id);
             let updatedRules;
             let updatedBVs = { ...state.ruleBusinessValues };
             let updatedDEs = { ...state.ruleDreadedEvents };

             if (ruleIndex > -1) { // Rule exists, remove it and its associations
                 updatedRules = currentRules.filter(r => r.id !== rule.id);
                 delete updatedBVs[rule.id];
                 delete updatedDEs[rule.id];
             } else { // Rule doesn't exist, add it
                 const newRule = {
                    id: rule.id,
                    name: rule.name,
                    description: rule.description || '',
                    status: 'not-applied',
                    justification: ''
                 };
                 updatedRules = [...currentRules, newRule];
             }

             const newSelectedRules = { ...state.selectedRules };
             if (updatedRules.length > 0) {
                 newSelectedRules[frameworkId] = updatedRules;
             } else { // If no rules left for this framework, remove the framework key
                 delete newSelectedRules[frameworkId];
             }
             return { ...state, selectedRules: newSelectedRules, ruleBusinessValues: updatedBVs, ruleDreadedEvents: updatedDEs };
        }
        case 'UPDATE_RULE': {
             const { frameworkId, ruleId, field, value } = action.payload;
             const currentRules = state.selectedRules[frameworkId] || [];
             const updatedRules = currentRules.map(r => r.id === ruleId ? { ...r, [field]: value } : r);
             // Ensure the frameworkId exists before trying to update
             if (!state.selectedRules[frameworkId]) {
                 console.warn(`Attempted to update rule for non-existent framework selection: ${frameworkId}`);
                 return state; // No change if framework isn't selected/doesn't have rules yet
             }
             return { ...state, selectedRules: { ...state.selectedRules, [frameworkId]: updatedRules } };
        }
        case 'UPDATE_RULE_BVS': {
             const { ruleId, businessValueIds } = action.payload;
             const newRuleBVs = { ...state.ruleBusinessValues };
             if (businessValueIds && businessValueIds.length > 0) {
                 newRuleBVs[ruleId] = businessValueIds;
             } else { // Remove the key if the array is empty or null
                 delete newRuleBVs[ruleId];
             }
             return { ...state, ruleBusinessValues: newRuleBVs };
        }
        case 'UPDATE_RULE_DES': {
             const { ruleId, dreadedEventIds } = action.payload;
             const newRuleDEs = { ...state.ruleDreadedEvents };
             if (dreadedEventIds && dreadedEventIds.length > 0) {
                 newRuleDEs[ruleId] = dreadedEventIds;
             } else { // Remove the key if the array is empty or null
                 delete newRuleDEs[ruleId];
             }
             return { ...state, ruleDreadedEvents: newRuleDEs };
        }
        case 'SET_SEARCH_TERM':
            return { ...state, searchTerm: action.payload };
        case 'SAVE_START':
            return { ...state, isSaving: true, saveError: null, error: null }; // Clear previous save error
        case 'SAVE_SUCCESS':
            // Optionally clear the is_new flag from frameworkDefinitions here IF the save logic doesn't dispatch UPDATE_SAVED_DEFINITIONS
             // For now, assume UPDATE_SAVED_DEFINITIONS handles it.
            return { ...state, isSaving: false, saveError: null };
        case 'SAVE_ERROR':
            return { ...state, isSaving: false, saveError: action.payload, error: action.payload };
        case 'SET_CSV_DATA':
             return { ...state, csvImportData: action.payload, importError: null };
        case 'SHOW_IMPORT_MODAL':
             return { ...state, showImportModal: true };
        case 'HIDE_IMPORT_MODAL':
             return { ...state, showImportModal: false, csvImportData: null, importError: null };
        case 'SET_IMPORT_ERROR':
             return { ...state, importError: action.payload };
        default: return state;
    }
}

// --- Main Component ---

const SecurityFramework = () => {
  const { t } = useTranslation();
  const analysisContext = useAnalysis();
  // console.log("Full analysis context data:", analysisContext); // Log the raw context first is fine

  const [state, dispatch] = useReducer(securityFrameworkReducer, initialState);
  // console.log("Reducer initialized:", { // This log is fine
  //   hasDispatch: Boolean(dispatch),
  //   hasStateData: Boolean(state),
  //   stateKeys: Object.keys(state)
  // });

  // NEW: State for scrolling to a specific rule card
  const [ruleIdToScrollTo, setRuleIdToScrollTo] = useState(null);
  // NEW: Ref to store DOM elements of rule cards
  const ruleCardRefs = useRef({});
  const fileInputRef = useRef(null); // NEW: Ref for hidden file input

  // NEW: State for custom framework name during import
  const [customFrameworkName, setCustomFrameworkName] = useState('');

  // NEW: State for description modal
  const [showDescriptionModal, setShowDescriptionModal] = useState(false);
  const [ruleForDescription, setRuleForDescription] = useState(null);

  // ** Step 3: Use Context Data **
  // Destructure the context values HERE
  const {
    currentAnalysis,
    currentSecurityFramework,
    currentBusinessValues,
    currentDreadedEvents,
    saveCurrentSecurityFramework,
    isWorkshopDataLoading,
    workshopDataError
  } = analysisContext;

  // Removed console log for component rendering

  // Derive framework definitions directly from the context data
  // Assumes currentSecurityFramework has a 'definitions' array property
  const frameworkDefinitions = useMemo(() =>
    currentSecurityFramework?.definitions || [],
    [currentSecurityFramework?.definitions]
  );

  // Automatically select the first framework when definitions load/change
  useEffect(() => {
    if (frameworkDefinitions.length > 0 && !state.selectedFrameworkId) {
      const firstFrameworkId = frameworkDefinitions[0]?._id || frameworkDefinitions[0]?.id;
      if (firstFrameworkId) {
        dispatch({ type: 'SELECT_FRAMEWORK', payload: firstFrameworkId });
      }
    }
    // If the currently selected ID is no longer valid, reset it (optional)
    else if (state.selectedFrameworkId && !frameworkDefinitions.some(f => (f._id || f.id) === state.selectedFrameworkId)) {
       dispatch({ type: 'SELECT_FRAMEWORK', payload: null });
    }
  }, [frameworkDefinitions, state.selectedFrameworkId]);

  // Update local state if context selections change (e.g., after initial load)
  useEffect(() => {
      // This might cause issues if save also updates context - needs careful handling
      // Consider if reducer should directly use context or sync like this
      if (currentSecurityFramework) {
           dispatch({
                type: 'LOAD_SELECTIONS_SUCCESS', // Reuse existing action type
                payload: { savedData: currentSecurityFramework }
           });
      }
  }, [currentSecurityFramework?.selectedRules, currentSecurityFramework?.ruleBusinessValues, currentSecurityFramework?.ruleDreadedEvents]); // Depend on specific parts

  // NEW: Effect to scroll to and highlight the rule card when ruleIdToScrollTo changes
  useEffect(() => {
    if (ruleIdToScrollTo && ruleCardRefs.current[ruleIdToScrollTo]) {
      const cardElement = ruleCardRefs.current[ruleIdToScrollTo];

      cardElement.scrollIntoView({
        behavior: 'smooth',
        block: 'center' // Try to center the card vertically
      });

      // Add a temporary highlight effect
      cardElement.classList.add('ring-2', 'ring-offset-2', 'ring-yellow-400', 'transition-all', 'duration-1000', 'ease-out');
      // Remove the highlight after a short delay
      const timer = setTimeout(() => {
        cardElement.classList.remove('ring-2', 'ring-offset-2', 'ring-yellow-400', 'transition-all', 'duration-1000', 'ease-out');
      }, 1500); // Highlight for 1.5 seconds

      // Clear the scroll target state
      setRuleIdToScrollTo(null);

      // Cleanup timeout on unmount or if ruleIdToScrollTo changes again quickly
      return () => clearTimeout(timer);
    }
  }, [ruleIdToScrollTo]); // Run only when ruleIdToScrollTo changes

  // --- Derived Data --- (Use definitions from state)
  const frameworksMap = useMemo(() => {
      // Use _id from DB if available, otherwise temporary frontend id
      return new Map(frameworkDefinitions.map(f => [f._id || f.id, f.name]));
  }, [frameworkDefinitions]);

  // RE-ADD selectedFramework and filteredRulesForPanel definitions
  const selectedFramework = useMemo(() => {
      if (!state.selectedFrameworkId) return null;
      // Find using either DB _id or temporary frontend id
      return frameworkDefinitions.find(f => (f._id || f.id) === state.selectedFrameworkId);
   }, [frameworkDefinitions, state.selectedFrameworkId]);

  const rulesForSelectedFramework = useMemo(() => {
      return selectedFramework?.rules || [];
  }, [selectedFramework]);

  const filteredRulesForPanel = useMemo(() => {
      if (!selectedFramework) return [];
      const searchTermLower = state.searchTerm.toLowerCase();
      // Ensure ruleDef.name exists before calling toLowerCase
      return rulesForSelectedFramework.filter( ruleDef =>
          ruleDef.name && ruleDef.name.toLowerCase().includes(searchTermLower)
      );
  }, [selectedFramework, rulesForSelectedFramework, state.searchTerm]);

  // Ensure summaryDataForTable uses consistent IDs
  const summaryDataForTable = useMemo(() => {
      // Removed console log for summary data recalculation
      const allRulesSummary = [];
      // Make sure these maps use IDs consistent with how they are linked (number vs string)
      const businessValuesMap = new Map((currentBusinessValues || []).map(bv => [bv.id, { ...bv, supportAssets: (bv.supportAssets || []).map(asset => ({ ...asset })) }]));
      const dreadedEventsMap = new Map((currentDreadedEvents || []).map(de => [de.id, { ...de }])); // Assuming DE ID is string here based on previous code

      // Iterate through selected rules using the correct framework ID (_id or id)
      Object.entries(state.selectedRules).forEach(([frameworkDbOrTempId, savedRules]) => {
          const frameworkName = frameworksMap.get(frameworkDbOrTempId) || frameworkDbOrTempId; // Get name from map

          if (Array.isArray(savedRules)) {
            savedRules.forEach(savedRule => {
                  // Use the rule's own unique ID (which should be consistent) for linking BVs/DEs
                if (savedRule && savedRule.id) {
                    const associatedDeIds = state.ruleDreadedEvents[savedRule.id] || [];
                    const resolvedDesWithLinkedData = Array.isArray(associatedDeIds) ? associatedDeIds.map(deId => {
                        const deObject = dreadedEventsMap.get(deId); // Look up DE by its ID
                        if (!deObject) {
                            // Removed console warning for dreaded event not found
                            return null;
                        }

                        // Ensure severity exists on the object fetched from the map
                        // console.log(`DE Object for ${deObject.name}:`, deObject); // Debugging line

                        let linkedBusinessValue = null;
                        const linkedBvId = deObject.businessValue; // Get linked BV ID from DE object
                        if (linkedBvId) {
                            // Ensure lookup type matches key type (e.g., both numbers or both strings)
                            const bvObject = businessValuesMap.get(Number(linkedBvId)); // Assuming BV ID needs conversion
                            if (bvObject) {
                                linkedBusinessValue = { ...bvObject, supportAssets: bvObject.supportAssets || [] };
                            } else {
                                 console.warn(`Business value with ID ${linkedBvId} (linked from DE ${deId}) not found in map.`);
                            }
                        }
                        // Return the full DE object (including severity) plus the linked BV
                        return { ...deObject, linkedBusinessValue };
                    }).filter(Boolean) : [];

                    allRulesSummary.push({
                        ...savedRule, // id, name, status, justification from selectedRules state
                        frameworkName, // Name from frameworksMap
                        frameworkId: frameworkDbOrTempId, // The DB or Temp ID used as the key for grouping
                        dreadedEvents: resolvedDesWithLinkedData // Array of DE objects (with severity) + linked BVs
            });
          }
      });
          }
      });
      // Removed console log for calculated summary data
      return allRulesSummary;
  // Ensure all dependencies that affect the calculation are included
  }, [state.selectedRules, state.ruleBusinessValues, state.ruleDreadedEvents, frameworksMap, currentBusinessValues, currentDreadedEvents]);


  // --- Callback Functions ---
  const handleSelectFramework = useCallback((frameworkId) => {
      dispatch({ type: 'SELECT_FRAMEWORK', payload: frameworkId });
  }, [dispatch]);

  const handleToggleRule = useCallback((frameworkActualId, ruleDefinition) => {
      dispatch({ type: 'TOGGLE_RULE', payload: { frameworkId: frameworkActualId, rule: ruleDefinition } });
  }, [dispatch]);

  const handleRuleChange = useCallback((frameworkActualId, ruleId, field, value) => {
      dispatch({ type: 'UPDATE_RULE', payload: { frameworkId: frameworkActualId, ruleId, field, value } });
  }, [dispatch]);

  // These don't depend on framework ID directly
  const handleUpdateRuleBusinessValues = useCallback((ruleId, businessValueIds) => { dispatch({ type: 'UPDATE_RULE_BVS', payload: { ruleId, businessValueIds } }); }, [dispatch]);
  const handleUpdateRuleDreadedEvents = useCallback((ruleId, dreadedEventIds) => { dispatch({ type: 'UPDATE_RULE_DES', payload: { ruleId, dreadedEventIds } }); }, [dispatch]);
  const handleSearchTermChange = useCallback((e) => { dispatch({ type: 'SET_SEARCH_TERM', payload: e.target.value }); }, [dispatch]);

  // Modify Rule Handler - Ensure framework selection uses correct ID
  const handleModifyRule = useCallback((ruleIdToModify) => {
    // Find the framework ID (_id or id) associated with the selected rule
    let frameworkDbOrTempId = null;
    let ruleToModify = null;

    for (const [fwId, rules] of Object.entries(state.selectedRules)) {
      const foundRule = rules.find(r => r.id === ruleIdToModify);
      if (foundRule) {
        frameworkDbOrTempId = fwId; // This is the _id or id used as the key
        ruleToModify = foundRule;
        break;
      }
    }

    if (!frameworkDbOrTempId || !ruleToModify) {
      alert(t('securityFoundation.status.ruleNotFoundForModification'));
      return;
    }

    // Ensure the framework panel corresponding to this ID is selected.
    // The ID stored in state.selectedFrameworkId should match this.
    if (state.selectedFrameworkId !== frameworkDbOrTempId) {
        dispatch({ type: 'SELECT_FRAMEWORK', payload: frameworkDbOrTempId });
    }

    // Set the state to trigger the scroll effect.
    // The key for ruleCardRefs should match ruleToModify.id
    setRuleIdToScrollTo(ruleIdToModify);

  }, [state.selectedRules, state.selectedFrameworkId, dispatch]);

  const handleDeleteRule = useCallback((ruleIdToDelete) => {
    // Find the framework and the specific rule object
    let frameworkId = null;
    let ruleToDelete = null;

    for (const [fwId, rules] of Object.entries(state.selectedRules)) {
      const foundRule = rules.find(r => r.id === ruleIdToDelete);
      if (foundRule) {
        frameworkId = fwId;
        ruleToDelete = foundRule;
        break;
      }
    }

    if (!frameworkId || !ruleToDelete) {
      alert(t('securityFoundation.status.ruleNotFound'));
      return;
    }

    // Confirm with the user
    const confirmDelete = window.confirm(t('securityFoundation.status.confirmDeleteRule', { name: ruleToDelete.name }));

    if (confirmDelete) {
      // Dispatch the existing TOGGLE_RULE action. The reducer handles removal.
      dispatch({
        type: 'TOGGLE_RULE',
        payload: {
          frameworkId: frameworkId,
          rule: { id: ruleIdToDelete } // Pass the rule ID for identification
        }
      });
      // Optional: Show a success notification (e.g., using a toast library)
    }
  }, [state.selectedRules, dispatch]); // Depend on selectedRules and dispatch

  // --- NEW: Select All Toggle Handler ---
  const handleSelectAllToggle = useCallback(() => {
      if (!selectedFramework) return;

      const frameworkActualId = selectedFramework._id || selectedFramework.id;
      const currentSelectedRuleIds = new Set((state.selectedRules[frameworkActualId] || []).map(r => r.id));
      const visibleRuleIds = new Set(filteredRulesForPanel.map(r => r.id));

      const allVisibleSelected = filteredRulesForPanel.length > 0 &&
          filteredRulesForPanel.every(rule => currentSelectedRuleIds.has(rule.id));

      if (allVisibleSelected) {
          // Deselect all visible rules
          filteredRulesForPanel.forEach(ruleDef => {
              if (currentSelectedRuleIds.has(ruleDef.id)) { // Double-check if actually selected
                  dispatch({ type: 'TOGGLE_RULE', payload: { frameworkId: frameworkActualId, rule: ruleDef } });
              }
          });
      } else {
          // Select all visible rules that are not already selected
          filteredRulesForPanel.forEach(ruleDef => {
              if (!currentSelectedRuleIds.has(ruleDef.id)) { // Only toggle if not already selected
                  dispatch({ type: 'TOGGLE_RULE', payload: { frameworkId: frameworkActualId, rule: ruleDef } });
              }
          });
      }
  }, [selectedFramework, state.selectedRules, filteredRulesForPanel, dispatch]);

  // REWRITTEN Save Handler
  const handleSaveSecurityData = useCallback(async () => {
      if (!currentAnalysis?.id) {
          showErrorToast(t('securityFoundation.status.noAnalysisSelected'));
           return;
      }
      dispatch({ type: 'SAVE_START' });

      // Show loading toast
      const toastId = showLoadingToast(t('securityFoundation.status.savingSecurityFoundation'));

      let definitionsSaved = false;
      let updatedDefinitionsList = [...state.frameworkDefinitions]; // Start with current list

      try {
          // Step A: Save newly added framework definitions
          const newDefinitionsToSave = state.frameworkDefinitions.filter(f => f.is_new);

          if (newDefinitionsToSave.length > 0) {
              console.log(`Attempting to save ${newDefinitionsToSave.length} new framework definition(s)...`);
              const savePromises = newDefinitionsToSave.map(def => {
                  const payload = {
                      name: def.name,
                      description: def.description,
                      rules: def.rules,
                      analysisId: currentAnalysis.id
                  };
                  return frameworkDefinitionService.createFrameworkDefinition(payload);
              });

              const results = await Promise.allSettled(savePromises);

              const failedSaves = results.filter(r => r.status === 'rejected' || !r.value?.success);
              if (failedSaves.length > 0) {
                  console.error('Failed to save some framework definitions:', failedSaves);
                  const errorMsg = failedSaves[0].reason?.message || failedSaves[0].value?.message || 'Erreur lors de la sauvegarde des nouvelles définitions.';
                  throw new Error(errorMsg); // Throw error to stop proceeding
              }

              // All definitions saved successfully, update local state
              console.log("New definitions saved successfully. Updating local state...");
              const savedDefinitionData = results.map(r => r.value.data); // Extract saved data with DB _id

              // Create a map of tempId -> savedData
              const tempIdToSavedDataMap = new Map();
              newDefinitionsToSave.forEach((tempDef, index) => {
                  if(savedDefinitionData[index]) {
                      tempIdToSavedDataMap.set(tempDef.id, savedDefinitionData[index]);
                  }
              });

              // Update the main list: replace temp items with saved ones (remove is_new)
              updatedDefinitionsList = state.frameworkDefinitions.map(f => {
                  if (f.is_new && tempIdToSavedDataMap.has(f.id)) {
                      const savedData = tempIdToSavedDataMap.get(f.id);
                      // Return the saved data from API (it has _id), ensure no is_new flag
                      const { is_new, ...rest } = savedData;
                      return rest;
                  }
                  // If not new or save failed somehow (shouldn't happen if checks passed), return as is
                  // Make sure existing saved items don't have is_new flag either
                  const { is_new, ...rest } = f;
                  return rest;
              });

              // Update state with the list containing DB IDs and no is_new flags
              dispatch({ type: 'UPDATE_SAVED_DEFINITIONS', payload: { updatedDefinitions: updatedDefinitionsList }});
              definitionsSaved = true; // Mark definitions as saved
          } else {
              console.log("No new framework definitions to save.");
              definitionsSaved = true; // Nothing to save, so conceptually successful
          }

          // Step B: Save selections (only if definitions saved or none needed saving)
          if (definitionsSaved) {
              console.log("Proceeding to save selections...");
              const selectionData = {
          selectedRules: state.selectedRules,
          ruleBusinessValues: state.ruleBusinessValues,
          ruleDreadedEvents: state.ruleDreadedEvents,
                  // Note: summaryDataForTable might need recalculation if definition IDs changed above
                  // It might be safer to recalculate it here or ensure the `useMemo` recalculates
                  summary: summaryDataForTable // Pass summary data if needed by backend/context save
      };

              // Call the context function to save selections to AnalysisComponent
              const selectionResponse = await saveCurrentSecurityFramework(selectionData);

              if (selectionResponse && selectionResponse.success) {
                  console.log("Selections saved successfully via context.");
                  dispatch({ type: 'SAVE_SUCCESS' });

                  // Update loading toast to success
                  updateToast(toastId, "Socle de sécurité sauvegardé avec succès!", 'success');
              } else {
                  const errorMsg = selectionResponse?.message || "La sauvegarde des sélections via le contexte a échoué.";
                  console.error("Save selections failed:", selectionResponse);
                  throw new Error(errorMsg); // Throw to be caught below
              }
          }

      } catch (error) {
          console.error('Error during save process:', error);
          dispatch({ type: 'SAVE_ERROR', payload: error.message || 'Une erreur est survenue lors de la sauvegarde.' });

          // Update loading toast to error
          updateToast(toastId, `Erreur lors de la sauvegarde: ${error.message || 'Une erreur est survenue lors de la sauvegarde.'}`, 'error');
      }
  }, [
      currentAnalysis?.id,
      state.frameworkDefinitions,
      state.selectedRules,
      state.ruleBusinessValues,
      state.ruleDreadedEvents,
      saveCurrentSecurityFramework,
      summaryDataForTable,
      dispatch
  ]);

  // Handlers for CSV import
  const handleFileUpload = useCallback((event) => {
    const file = event.target.files[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (e) => {
        try {
            const parsedData = parseCSV(e.target.result);
            if (parsedData.length === 0) {
                dispatch({ type: 'SET_IMPORT_ERROR', payload: 'Le fichier CSV est vide ou mal formaté.' });
                return;
            }
            // Reset custom name when opening modal
            setCustomFrameworkName('Référentiel Personnalisé ' + (state.frameworkDefinitions.filter(f => !f.is_predefined).length + 1));
            dispatch({ type: 'SET_CSV_DATA', payload: parsedData });
            dispatch({ type: 'SHOW_IMPORT_MODAL' }); // Corrected: pass payload: true is not needed
        } catch (error) {
            dispatch({ type: 'SET_IMPORT_ERROR', payload: 'Erreur lors du parsing du fichier CSV.' });
        }
    };
    reader.onerror = () => {
        dispatch({ type: 'SET_IMPORT_ERROR', payload: 'Erreur lors de la lecture du fichier.' });
    };
    reader.readAsText(file);
    // Clear the input value so the same file can be selected again
    event.target.value = null;
  }, [dispatch, state.frameworkDefinitions]); // Added state.frameworkDefinitions

  // UPDATED: Use customFrameworkName state
  const handleImportConfirm = useCallback(() => {
    if (!state.csvImportData || !customFrameworkName.trim()) {
      showErrorToast(t('securityFoundation.status.specifyFrameworkName'));
      return;
    }

    const temporaryId = `custom-${Date.now()}-${Math.random().toString(36).substr(2, 5)}`;
    const customFramework = {
        id: temporaryId, // Use temporary frontend ID
        name: customFrameworkName.trim(),
        description: `${t('securityFoundation.status.importedOn')} ${new Date().toLocaleDateString()}`,
        is_predefined: false,
        rules: state.csvImportData.map(rule => ({ ...rule, id: rule.id || `rule-${Date.now()}-${Math.random().toString(36).substr(2, 5)}` })) // Ensure rules also have temp IDs
    };

    // Dispatch to add locally with is_new flag
    dispatch({ type: 'ADD_CUSTOM_FRAMEWORK', payload: { framework: customFramework } });

    // Show success toast
    showSuccessToast(t('securityFoundation.status.frameworkImported', { name: customFrameworkName.trim(), count: customFramework.rules.length }));
  }, [state.csvImportData, customFrameworkName, dispatch]);

  // NEW: Function to trigger file input click
  const handleImportClick = useCallback(() => {
    fileInputRef.current?.click();
  }, []);

  // NEW: Print function for summary table (UPDATED)
  const printTable = useCallback(() => {
    const printArea = document.querySelector('.summary-table-print-area');
    if (!printArea) {
        console.error("Print area '.summary-table-print-area' not found.");
        showErrorToast(t('securityFoundation.status.printAreaNotFound'));
        return;
    }
    const printWindow = window.open('', '_blank');
    printWindow.document.write(`<html><head><title>${t('securityFoundation.sections.summary')}</title>`);
    // Print styles remain the same
    printWindow.document.write(`
        <style>
            body { font-family: sans-serif; }
            table { border-collapse: collapse; width: 100%; font-size: 9pt; margin-bottom: 20px; }
            th, td { border: 1px solid #ccc; padding: 6px; text-align: left; vertical-align: top; word-wrap: break-word; }
            th { background-color: #f2f2f2; font-weight: bold; }
            .no-print { display: none; }
            h3 { font-size: 14pt; margin-bottom: 15px; }
            /* Attempt to style severity squares for print */
            .severity-square { padding: 4px; border-radius: 4px; font-size: 8pt; text-align: center; color: black; border: 1px solid #ddd; margin-bottom: 2px; }
            .bg-black { background-color: #000 !important; color: #fff !important; -webkit-print-color-adjust: exact; print-color-adjust: exact; }
            .bg-red-600 { background-color: #DC2626 !important; color: #fff !important; -webkit-print-color-adjust: exact; print-color-adjust: exact; }
            .bg-yellow-500 { background-color: #F59E0B !important; -webkit-print-color-adjust: exact; print-color-adjust: exact; }
            .bg-yellow-200 { background-color: #FEF08A !important; -webkit-print-color-adjust: exact; print-color-adjust: exact; }
            .bg-cyan-300 { background-color: #67E8F9 !important; -webkit-print-color-adjust: exact; print-color-adjust: exact; }
            .bg-gray-200 { background-color: #E5E7EB !important; -webkit-print-color-adjust: exact; print-color-adjust: exact; }
            .text-white { color: #fff !important; }
            .text-black { color: #000 !important; }
        </style>
    `);
    printWindow.document.write('</head><body>');
    printWindow.document.write(`<h3>${t('securityFoundation.sections.summary')}</h3>`);

    // Clone the table to avoid modifying the original
    const tableClone = printArea.querySelector('table')?.cloneNode(true);
    if(tableClone) {
        // Remove the last header (Actions)
        const headers = tableClone.querySelectorAll('thead th');
        if (headers.length > 0) {
            headers[headers.length - 1].remove();
        }

        // Remove the last cell (Actions) from each body row
        const bodyRows = tableClone.querySelectorAll('tbody tr');
        bodyRows.forEach(row => {
            const cells = row.querySelectorAll('td');
            if (cells.length > 0) {
                 // Check if it's the 'no rules' row by looking at colspan
                 const noRulesCell = cells[0];
                 if (noRulesCell && noRulesCell.hasAttribute('colspan')) {
                     // Decrease colspan if it's the 'no rules' row
                     const currentCs = parseInt(noRulesCell.getAttribute('colspan'), 10);
                     if (!isNaN(currentCs) && currentCs > 1) {
                         noRulesCell.setAttribute('colspan', (currentCs - 1).toString());
                     }
                 } else {
                     // Otherwise, remove the last cell (actions)
                     cells[cells.length - 1].remove();
                 }
            }
        });

        // Add specific classes to severity divs for print styling
        tableClone.querySelectorAll('[title]').forEach(el => {
            if(el.classList.contains('flex-shrink-0')) { // Target the severity divs
                 el.classList.add('severity-square');
            }
        });

        printWindow.document.write(tableClone.outerHTML);
    } else {
        printWindow.document.write(`<p>${t('securityFoundation.status.tableNotFound')}</p>`);
    }

    printWindow.document.write('</body></html>');
    printWindow.document.close();
    setTimeout(() => {
        printWindow.print();
        showSuccessToast(t('securityFoundation.status.printingInProgress'));
    }, 500);
  }, []);

  // NEW: Handlers for description modal
  const handleShowDescription = useCallback((rule) => {
    setRuleForDescription(rule);
    setShowDescriptionModal(true);
  }, []);
  const handleCloseDescriptionModal = useCallback(() => {
    setShowDescriptionModal(false);
    setRuleForDescription(null);
  }, []);

  // --- NEW: Calculate Select All checkbox state ---
  const currentSelectedRuleIds = useMemo(() =>
      selectedFramework
          ? new Set((state.selectedRules[selectedFramework._id || selectedFramework.id] || []).map(r => r.id))
          : new Set(),
      [state.selectedRules, selectedFramework]
  );

  const { isAllSelected, isIndeterminate } = useMemo(() => {
      if (!selectedFramework || filteredRulesForPanel.length === 0) {
          return { isAllSelected: false, isIndeterminate: false };
      }
      const visibleSelectedCount = filteredRulesForPanel.filter(rule =>
          currentSelectedRuleIds.has(rule.id)
      ).length;

      const allSelected = visibleSelectedCount === filteredRulesForPanel.length;
      const indeterminate = visibleSelectedCount > 0 && visibleSelectedCount < filteredRulesForPanel.length;

      return { isAllSelected: allSelected, isIndeterminate: indeterminate };

  }, [selectedFramework, filteredRulesForPanel, currentSelectedRuleIds]);

  // --- Render Logic (Use context loading/error states) ---

  // Combine loading states: Use context loading state directly
  const isLoading = isWorkshopDataLoading;
  // Combine error states (prioritize local SAVE error, then context WORKSHOP error)
  // Allow specific save error to override general workshop error for feedback
  const error = state.saveError || workshopDataError?.securityFramework || workshopDataError?.general;

  if (isLoading) {
      // Avoid rendering parts of the UI that depend on data not yet loaded
      return <div className="p-6 text-center"><p>{t('securityFoundation.status.loadingData')}</p></div>;
  }

  // Display workshop error if no specific save error exists
  if (error && !state.saveError) {
      return <div className="p-6 bg-red-100 border border-red-400 text-red-700 rounded-lg">
          <p className="font-semibold">{t('securityFoundation.status.loadingError')}</p>
          <p>{typeof error === 'string' ? error : JSON.stringify(error)}</p>
      </div>;
  }

  // Display specific save error if it exists
  if (state.saveError) {
     // Maybe display this near the save button instead? Or use a toast?
     // For now, keep it simple:
      return <div className="p-6 bg-red-100 border border-red-400 text-red-700 rounded-lg">
          <p className="font-semibold">{t('securityFoundation.status.saveError')}</p>
          <p>{state.saveError}</p>
          {/* Add a button to clear the error? */}
      </div>;
  }

  // Ensure selectedFrameworkId exists and corresponds to a loaded framework before accessing rules
  const selectedRulesDetails = (state.selectedFrameworkId && state.selectedRules[state.selectedFrameworkId])
      ? state.selectedRules[state.selectedFrameworkId]
      : [];

  // Check if definitions have loaded before rendering the main UI
  if (frameworkDefinitions.length === 0 && !isLoading) {
      // Handle case where definitions might be empty even if loading is finished
      return <div className="p-6 text-center text-gray-500">{t('securityFoundation.status.noFrameworkDefinitions')}</div>;
  }

    return (
        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
            {/* Hidden File Input for CSV */}
            <input
                type="file"
                accept=".csv"
                onChange={handleFileUpload}
                className="hidden"
                id="csv-upload"
                ref={fileInputRef} // Assign ref
            />

            {/* === MERGED HEADER === */}
            <div className="bg-white rounded-xl shadow-sm border border-slate-100 p-4 mb-6">
                <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
                    {/* Left Side: Breadcrumb & Title */}
                    <div>
                        <div className="flex items-center text-sm text-slate-500 mb-2">
                            <span className="hover:text-blue-600 transition-colors">{t('securityFoundation.breadcrumb.workshop1')}</span>
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mx-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                            </svg>
                            <span className="text-blue-600 font-medium">{t('securityFoundation.breadcrumb.securityFoundation')}</span>
                        </div>
                        <h1 className="text-2xl sm:text-3xl font-bold text-slate-800 flex items-center">
                            <Shield size={28} className="mr-3 text-blue-600" />
                            {t('securityFoundation.title')}
                        </h1>
                    </div>

                    {/* Right Side: Action Buttons */}
                    <div className="flex items-center space-x-3">
                        {/* Import Button */}
                        <button
                            onClick={handleImportClick}
                            className="text-sm font-medium bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 flex items-center shadow-sm transition duration-200"
                            title={t('securityFoundation.import.importTooltip')}
                        >
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10" />
                            </svg>
                            {t('securityFoundation.buttons.import')}
                        </button>

                        {/* Save Button */}
                        <button
                            className={`text-sm font-medium px-4 py-2 rounded-lg flex items-center shadow-sm transition duration-200 ${state.isSaving ? 'bg-gray-400 cursor-wait text-white' : 'bg-blue-600 hover:bg-blue-700 text-white'}`}
                            onClick={handleSaveSecurityData}
                            disabled={state.isSaving || isLoading}
                        >
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3 3m0 0l-3-3m3 3V4" />
                            </svg>
                            {state.isSaving ? t('securityFoundation.buttons.saving') : t('securityFoundation.buttons.save')}
                        </button>
                    </div>
                </div>
            </div>
            {/* === END MERGED HEADER === */}

            {/* Import Modal */}
            {state.showImportModal && (
                <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
                    <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full">
                        <div className="p-6">
                            <h3 className="text-lg font-medium text-gray-900 mb-4">{t('securityFoundation.import.title')}</h3>

                            {state.importError ? (
                                <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
                                    {state.importError}
                                </div>
                            ) : (
                                <div className="space-y-4">
                                    {/* Input for Framework Name */}
                                    <div>
                                        <label htmlFor="custom-framework-name" className="block text-sm font-medium text-gray-700 mb-1">
                                            {t('securityFoundation.import.frameworkName')}
                                        </label>
                                        <Input
                                            id="custom-framework-name"
                                            type="text"
                                            value={customFrameworkName}
                                            onChange={(e) => setCustomFrameworkName(e.target.value)}
                                            placeholder={t('securityFoundation.placeholders.frameworkName')}
                                            className="w-full"
                                        />
                                    </div>
                                    {/* Rules Preview */}
                                    <p className="text-sm text-gray-500">
                                        {state.csvImportData?.length || 0} {t('securityFoundation.status.rulesFound')}
                                    </p>
                                    <div className="max-h-60 overflow-y-auto border rounded p-4">
                                        {state.csvImportData?.map((rule, index) => (
                                            <div key={index} className="mb-2 p-2 bg-gray-50 rounded">
                                                <div className="font-medium">{rule.name}</div>
                                                <div className="text-sm text-gray-500">{rule.description}</div>
                                            </div>
                                        ))}
                                    </div>
                                </div>
                            )}
                        </div>
                        <div className="px-6 py-4 bg-gray-50 border-t border-gray-200 flex justify-end space-x-3">
                            <button
                                className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
                                onClick={() => dispatch({ type: 'HIDE_IMPORT_MODAL' })}
                            >
                                {t('securityFoundation.buttons.cancel')}
                            </button>
                            <button
                                className="px-4 py-2 rounded-md text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed"
                                onClick={handleImportConfirm}
                                // Disable if no name, no data, or error
                                disabled={!customFrameworkName.trim() || !state.csvImportData || state.importError}
                            >
                                {t('securityFoundation.import.importButton')}
                            </button>
                        </div>
                    </div>
                </div>
            )}

            {/* NEW: Description Modal */}
            <RuleDescriptionModal
                isOpen={showDescriptionModal}
                onClose={handleCloseDescriptionModal}
                ruleName={ruleForDescription?.name}
                ruleDescription={ruleForDescription?.description}
            />

            {/* Main Content Grid */}
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
                {/* Left Column: Framework List */}
                <div className="lg:col-span-1">
                    {frameworkDefinitions.length > 0 ? (
                        <div className="sticky top-4 bg-white p-4 rounded-lg border border-gray-200 shadow-sm">
                            <div className="flex items-center justify-between mb-4">
                                <h3 className="text-lg font-medium text-gray-900">{t('securityFoundation.sections.frameworks')}</h3>
                                <span className="text-sm text-gray-500">{frameworkDefinitions.length} {t('securityFoundation.status.available')}</span>
                            </div>
                            <div className="space-y-2 max-h-[70vh] overflow-y-auto pr-2">
                                {frameworkDefinitions.map(frameworkDef => {
                                    const currentFrameworkId = frameworkDef._id || frameworkDef.id;
                                    return (
                                    <FrameworkListItem
                                            key={currentFrameworkId}
                                        framework={frameworkDef}
                                            isSelected={state.selectedFrameworkId === currentFrameworkId}
                                            onSelect={() => handleSelectFramework(currentFrameworkId)}
                                            selectedRuleCount={(state.selectedRules[currentFrameworkId] || []).length}
                                        />
                                    );
                                })}
                            </div>
                        </div>
                    ) : (
                        <div className="bg-white p-4 rounded-lg border border-gray-200 shadow-sm text-center text-gray-500">
                            {t('securityFoundation.status.noFrameworkLoaded')}
                        </div>
                    )}
                 </div>

                {/* Right Column: Details Panel (Pass context lists) */}
                <div className="lg:col-span-2">
                    {selectedFramework ? (
                         <Card className="p-6 shadow-md">
                             {/* Framework Header */}
                             <div className="mb-6 pb-4 border-b border-gray-200">
                                 <h3 className="text-xl font-semibold text-gray-900 mb-1">{selectedFramework.name}</h3>
                                 <p className="text-sm text-gray-500">{selectedFramework.description || t('securityFoundation.placeholders.configure')}</p>
                            </div>
                             {/* Rules Section */}
                             <div className="rules-selection-section space-y-6">
                                 {/* Search and Available Rules */}
                                 <div>
                                     <h4 className="text-sm uppercase tracking-wider text-gray-500 font-semibold mb-3">{t('securityFoundation.sections.availableRules')}</h4>
                                     <Input
                                        placeholder={t('securityFoundation.placeholders.searchRule')}
                                        value={state.searchTerm}
                                        onChange={handleSearchTermChange}
                                        className="mb-4 w-full"
                                     />
                                     {/* --- ADD Select All Checkbox --- */}
                                     <div className="flex items-center my-2 px-3 py-1.5 border-b border-gray-200 bg-gray-50 rounded-t-lg">
                                          <input
                                              type="checkbox"
                                              id={`select-all-${selectedFramework._id || selectedFramework.id}`}
                                              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded mr-3 cursor-pointer"
                                              checked={isAllSelected}
                                              ref={el => el && (el.indeterminate = isIndeterminate)} // Set indeterminate state via ref
                                              onChange={handleSelectAllToggle} // Use the new handler
                                              disabled={filteredRulesForPanel.length === 0} // Disable if no rules visible
                                          />
                                          <label
                                              htmlFor={`select-all-${selectedFramework._id || selectedFramework.id}`}
                                              className="text-sm font-medium text-gray-700 cursor-pointer select-none"
                                          >
                                              {isAllSelected ? t('securityFoundation.buttons.deselectAll') : t('securityFoundation.buttons.selectAll')} {t('securityFoundation.buttons.visible')}
                                          </label>
                                      </div>
                                     {/* Available Rules List */}
                                     <div className="border rounded-b-lg max-h-72 overflow-y-auto bg-white">
                                        {filteredRulesForPanel.length > 0 ? (
                                            <div className="divide-y divide-gray-100">
                                                {filteredRulesForPanel.map(ruleDef => {
                                                   // ... (existing RuleItem rendering logic) ...
                                                     const isSelected = currentSelectedRuleIds.has(ruleDef.id);
                                                    return (
                                                    <RuleItem
                                                        key={ruleDef.id}
                                                        rule={ruleDef}
                                                            isSelected={isSelected}
                                                            onToggle={() => handleToggleRule(selectedFramework._id || selectedFramework.id, ruleDef)}
                                                            disabled={false}
                                                            onShowDescription={handleShowDescription}
                                                        />
                                                    );
                                                })}
                                            </div>
                                        ) : (
                                            <div className="p-6 text-center text-gray-500 text-sm">
                                                {state.searchTerm ? t('securityFoundation.status.noRuleFound') : t('securityFoundation.status.noRuleDefined')}
                                            </div>
                                        )}
                                     </div>
                                 </div>
                                 {/* Selected Rules Configuration (UPDATED with Grid) */}
                                 <div>
                                      <div className="mb-3 flex justify-between items-center">
                                          <h4 className="text-sm uppercase tracking-wider text-gray-500 font-semibold">{t('securityFoundation.sections.selectedRules')}</h4>
                                          <span className="text-sm text-gray-500">{selectedRulesDetails.length} {t('securityFoundation.status.selected')}</span>
                                    </div>
                                     {selectedRulesDetails.length > 0 ? (
                                        // Add Grid Container
                                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                            {selectedRulesDetails.map((savedRule) => (
                                                // Assign ref to the outer div of SelectedRuleCard implicitly via wrapper or direct prop if component supports it
                                                // This wrapper div remains the same, the grid handles layout
                                                <div key={savedRule.id} ref={el => ruleCardRefs.current[savedRule.id] = el}>
                                                <SelectedRuleCard
                                                    rule={savedRule}
                                                        onRemove={() => handleToggleRule(selectedFramework._id || selectedFramework.id, { id: savedRule.id, name: savedRule.name })}
                                                        onStatusChange={(e) => handleRuleChange(selectedFramework._id || selectedFramework.id, savedRule.id, 'status', e.target.value)}
                                                        onJustificationChange={(e) => handleRuleChange(selectedFramework._id || selectedFramework.id, savedRule.id, 'justification', e.target.value)}
                                                    allBusinessValues={currentBusinessValues || []}
                                                    onUpdateRuleBusinessValues={handleUpdateRuleBusinessValues}
                                                    allDreadedEvents={currentDreadedEvents || []}
                                                    linkedDreadedEventIds={state.ruleDreadedEvents[savedRule.id] || []}
                                                    onUpdateRuleDreadedEvents={handleUpdateRuleDreadedEvents}
                                                />
                                                </div>
                                            ))}
                                        </div>
                                    ) : (
                                        <div className="p-8 text-center text-gray-400 border border-dashed rounded-lg">
                                            <p>{t('securityFoundation.status.noRuleSelected')}</p>
                                            <p className="text-sm mt-1">{t('securityFoundation.status.addRulesFromList')}</p>
                                        </div>
                                    )}
                                 </div>
                            </div>
                        </Card>
                    ) : (
                         <div className="bg-white border border-dashed border-gray-300 rounded-lg flex items-center justify-center p-12 min-h-[400px]">
                             <p className="text-gray-500">
                                {frameworkDefinitions.length > 0 ? t('securityFoundation.status.selectFramework') : t('securityFoundation.status.loadingFrameworks')}
                            </p>
                        </div>
                    )}
                </div>
            </div>

            {/* Summary Table - Removed button props */}
            {summaryDataForTable.length > 0 ? (
                <SecurityFrameworkSummaryTable
                    rulesToDisplay={summaryDataForTable}
                    onModifyRule={handleModifyRule}
                    onDeleteRule={handleDeleteRule}
                />
            ) : (
                <div className="mt-8 p-6 bg-white rounded-lg border border-gray-200 text-center text-gray-500">
                    {t('securityFoundation.status.noRuleSelectedSummary')}
                </div>
            )}

            {/* NEW: Buttons Section After Summary Table */}
            {summaryDataForTable.length > 0 && (
                <div className="mt-6 flex justify-end space-x-4">
                    {/* Print Button (Modern Look) */}
                    <button
                        onClick={printTable}
                        className="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition duration-150 ease-in-out"
                    >
                         {/* Optional Print Icon */}
                         <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2}>
                             <path strokeLinecap="round" strokeLinejoin="round" d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z" />
                         </svg>
                        {t('securityFoundation.buttons.print')}
                    </button>

                    {/* Save Button (Modern Look) */}
                    <button
                        onClick={handleSaveSecurityData}
                        disabled={state.isSaving || isLoading}
                        className={`inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white transition duration-150 ease-in-out ${state.isSaving ? 'bg-gray-400 cursor-wait' : 'bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500'}`}
                    >
                        {/* Optional Save Icon */}
                         <svg xmlns="http://www.w3.org/2000/svg" className={`h-5 w-5 mr-2 ${state.isSaving ? 'animate-spin' : ''}`} fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2}>
                             <path strokeLinecap="round" strokeLinejoin="round" d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3 3m0 0l-3-3m3 3V4" />
                         </svg>
                        {state.isSaving ? t('securityFoundation.buttons.saving') : t('securityFoundation.buttons.save')}
                    </button>
                </div>
            )}

        </div>
    );
};

export default SecurityFramework;

// --- END OF FILE SecurityFramework.js ---
