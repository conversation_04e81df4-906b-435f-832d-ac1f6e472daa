// src/components/Atelier 2/Activite2/GuideModal.js
import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { X, HelpCircle, MousePointer, Edit, Save, AlertCircle, Target, Activity, Database } from 'lucide-react';

const GuideModal = ({ isOpen, onClose }) => {
  const steps = [
    {
      icon: <Activity size={20} className="text-blue-600" />,
      title: "Évaluer les couples SR/OV",
      description: "Évaluez chaque couple source de risque/objectif visé en fonction de sa motivation, son activité et ses ressources. Ces trois facteurs déterminent le niveau global du couple SR/OV."
    },
    {
      icon: <Target size={20} className="text-blue-600" />,
      title: "Comprendre le calcul du niveau",
      description: "Le niveau est calculé automatiquement à partir des trois facteurs : Motivation + Activité + Ressources = Score total. Si Score < 6 : Niveau 1 (mod<PERSON><PERSON>), Si Score = 6 : Niveau 2 (moyen), Si Score > 6 : Niveau 3 (<PERSON>lev<PERSON>)."
    },
    {
      icon: <MousePointer size={20} className="text-blue-600" />,
      title: "Sélectionner les couples SR/OV",
      description: "Cochez les cases pour sélectionner les couples source de risque/objectif visé que vous souhaitez retenir pour l'analyse des risques. Les couples retenus apparaissent en vert, les non retenus en rouge."
    },
    {
      icon: <Edit size={20} className="text-blue-600" />,
      title: "Modifier les évaluations",
      description: "Cliquez sur l'icône de modification pour changer les valeurs de motivation, activité et ressources d'un couple SR/OV. Le niveau sera automatiquement recalculé."
    },
    {
      icon: <Save size={20} className="text-blue-600" />,
      title: "Enregistrer vos modifications",
      description: "N'oubliez pas d'enregistrer vos modifications en cliquant sur le bouton 'Enregistrer' en haut du tableau. Un indicateur vous signale si vous avez des modifications non sauvegardées."
    },
    {
      icon: <Database size={20} className="text-blue-600" />,
      title: "Visualiser les données",
      description: "Utilisez l'onglet 'Visualisation' pour voir deux représentations graphiques : 'Vision par source de risque' (points = OV) et 'Vision par objectif visé' (points = SR). Plus un point est proche du centre, plus son niveau est élevé."
    },
    {
      icon: <HelpCircle size={20} className="text-blue-600" />,
      title: "Interagir avec la visualisation",
      description: "Faites glisser les points pour modifier leur niveau. Les points restent dans leur secteur. Cliquez sur un point dans le tableau pour changer son statut (retenu/non retenu). La légende affiche la liste des sources de risque et objectifs visés."
    }
  ];

  return (
    <AnimatePresence>
      {isOpen && (
        <>
          {/* Backdrop */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.2 }}
            className="fixed inset-0 bg-black bg-opacity-50 z-50 backdrop-blur-sm"
            onClick={onClose}
          />

          {/* Modal */}
          <motion.div
            initial={{ opacity: 0, scale: 0.9, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.9, y: 20 }}
            transition={{ type: "spring", damping: 25, stiffness: 300 }}
            className="fixed inset-0 z-50 flex items-center justify-center p-4 sm:p-0"
          >
            <div className="bg-white rounded-xl shadow-xl w-full max-w-3xl max-h-[90vh] overflow-hidden">
              {/* Header */}
              <div className="flex justify-between items-center p-6 border-b border-gray-200">
                <div className="flex items-center">
                  <HelpCircle size={24} className="text-blue-600 mr-2" />
                  <h3 className="text-lg font-semibold text-gray-900">Guide d'évaluation et de visualisation des couples SR/OV</h3>
                </div>
                <button
                  onClick={onClose}
                  className="text-gray-400 hover:text-gray-500 focus:outline-none"
                >
                  <X size={24} />
                </button>
              </div>

              {/* Content */}
              <div className="p-6 overflow-y-auto max-h-[calc(90vh-140px)]">
                <div className="mb-6 bg-blue-50 p-4 rounded-lg border border-blue-100">
                  <h4 className="text-lg font-semibold text-blue-800 mb-2">Objectif de l'activité</h4>
                  <p className="text-blue-700">
                    Cette activité vous permet d'évaluer les couples Source de Risque/Objectif Visé (SR/OV) en fonction de leur motivation, activité et ressources.
                    Vous pouvez visualiser ces évaluations sous forme de graphiques radar pour mieux comprendre les relations entre les sources de risque et les objectifs visés.
                    Les couples retenus seront utilisés dans les étapes suivantes de l'analyse des risques.
                  </p>
                </div>
                <div className="space-y-8">
                  {steps.map((step, index) => (
                    <motion.div
                      key={index}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: index * 0.1 }}
                      className="flex"
                    >
                      <div className="flex-shrink-0 mr-4">
                        <div className="flex items-center justify-center w-10 h-10 rounded-full bg-blue-100 text-blue-600 font-bold">
                          {index + 1}
                        </div>
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center mb-2">
                          {step.icon}
                          <h3 className="text-lg font-semibold text-gray-800 ml-2">{step.title}</h3>
                        </div>
                        <p className="text-gray-600">{step.description}</p>
                      </div>
                    </motion.div>
                  ))}
                </div>

                <div className="mt-8 bg-gray-50 p-4 rounded-lg border border-gray-200">
                  <h4 className="text-lg font-semibold text-gray-800 mb-2">Points importants à retenir</h4>
                  <ul className="list-disc pl-5 space-y-2 text-gray-700">
                    <li>Le niveau est calculé automatiquement selon la formule : <span className="font-medium">Score = Motivation + Activité + Ressources</span></li>
                    <li>Les couples <span className="text-green-600 font-medium">retenus</span> apparaissent en vert, les <span className="text-red-600 font-medium">non retenus</span> en rouge</li>
                    <li>Dans les visualisations radar, plus un point est <span className="font-medium">proche du centre</span>, plus son niveau de risque est élevé</li>
                    <li>N'oubliez pas de <span className="font-medium">sauvegarder</span> vos modifications avant de quitter la page</li>
                  </ul>
                </div>
              </div>

              {/* Footer */}
              <div className="bg-gray-50 px-6 py-4 flex justify-end">
                <button
                  onClick={onClose}
                  className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                >
                  Fermer
                </button>
              </div>
            </div>
          </motion.div>
        </>
      )}
    </AnimatePresence>
  );
};

export default GuideModal;
