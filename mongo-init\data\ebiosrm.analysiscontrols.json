[{"_id": {"$oid": "67f1824e1c9cf3b70edaad5d"}, "analysisId": {"$oid": "67ef307f8fc56749148e9ad6"}, "__v": 0, "createdAt": {"$date": "2025-04-05T19:19:42.383Z"}, "planData": {"custom-bcvcz7tr4_1743729502461": {"ruleId": "custom-bcvcz7tr4", "ruleName": "CIS Safeguard 1,1: Establish and Maintain Detailed Enterprise Asset Inventory", "ruleDescription": "(Asset Type: Devices, Security Function: Identify) Establish and maintain an accurate, detailed, and up-to-date inventory of all enterprise assets with the potential to store or process data, to include: end-user devices (including portable and mobile), network devices, non-computing/IoT devices, and servers. Ensure the inventory records the network address (if static), hardware address, machine name, enterprise asset owner, department for each asset, and whether the asset has been approved to connect to the network. For mobile end-user devices, MDM type tools can support this process, where appropriate. This inventory includes assets connected to the infrastructure physically, virtually, remotely, and those within cloud environments. Additionally, it includes assets that are regularly connected to the enterprise’s network infrastructure, even if they are not under control of the enterprise. Review and update the inventory of all enterprise assets bi-annually, or more frequently.", "dreadedEventId": 1743729502461, "dreadedEventName": "Défaut de conservation", "initialSeverity": "moderate", "residualSeverity": "low", "controls": [{"controlId": "ctrl-003", "controlName": "Contrôle C: Accès Physique", "responsiblePerson": "RSSI", "decision": "planned", "decisionDate": null}, {"controlId": "ctrl-002", "controlName": "Contrôle B: Chiffrement", "responsiblePerson": "CEO", "decision": "apply_before", "decisionDate": "2025-04-25"}]}, "custom-g23shxos1_1743729502868": {"ruleId": "custom-g23shxos1", "ruleName": "CIS Safeguard 1,2: Address Unauthorized Assets", "ruleDescription": "(Asset Type: Devices, Security Function: Respond) Ensure that a process exists to address unauthorized assets on a weekly basis. The enterprise may choose to remove the asset from the network, deny the asset from connecting remotely to the network, or quarantine the asset.", "dreadedEventId": 1743729502868, "dreadedEventName": "Falsification de preuve", "initialSeverity": "critical", "residualSeverity": null, "controls": []}, "custom-8oz6liwcd_1743729502868": {"ruleId": "custom-8oz6liwcd", "ruleName": "PR.AT-01: Provide General Security Training", "ruleDescription": "Personnel are provided with awareness and training so that they possess the knowledge and skills to perform general tasks with cybersecurity risks in mind", "dreadedEventId": 1743729502868, "dreadedEventName": "Falsification de preuve", "initialSeverity": "critical", "residualSeverity": null, "controls": []}, "custom-oo6au3jce_1743729502461": {"ruleId": "custom-oo6au3jce", "ruleName": "PR.AA-06: Manage Physical Access", "ruleDescription": "Physical access to assets is managed, monitored, and enforced commensurate with risk", "dreadedEventId": 1743729502461, "dreadedEventName": "Défaut de conservation", "initialSeverity": "moderate", "residualSeverity": "low", "controls": []}}, "updatedAt": {"$date": "2025-04-05T19:39:11.238Z"}}, {"_id": {"$oid": "67f18bc51c9cf3b70edab5de"}, "analysisId": {"$oid": "67e4a998b8ec79118b11e01a"}, "__v": 0, "createdAt": {"$date": "2025-04-05T20:00:05.025Z"}, "planData": {"projx-rule-2_1743604490888": {"ruleId": "projx-rule-2", "ruleName": "Chiffrement données sensibles", "ruleDescription": "", "dreadedEventId": 1743604490888, "dreadedEventName": "Attaque par déni de service", "initialSeverity": "critical", "residualSeverity": "high", "controls": [{"controlId": "ctrl-003", "controlName": "Contrôle C: Accès Physique", "responsiblePerson": "RSSI", "decision": "planned", "decisionDate": null}]}, "projx-rule-2_1743604491654": {"ruleId": "projx-rule-2", "ruleName": "Chiffrement données sensibles", "ruleDescription": "", "dreadedEventId": 1743604491654, "dreadedEventName": "Panne d'infrastructure", "initialSeverity": "moderate", "residualSeverity": "moderate", "controls": [{"controlId": "ctrl-001", "controlName": "Contrôle A: Pare-feu", "responsiblePerson": "", "decision": "abandoned", "decisionDate": null}]}, "projx-rule-1_1743604490888": {"ruleId": "projx-rule-1", "ruleName": "Accès spécifique API partenaires", "ruleDescription": "", "dreadedEventId": 1743604490888, "dreadedEventName": "Attaque par déni de service", "initialSeverity": "critical", "residualSeverity": "high", "controls": []}, "nist-pr.at-1_1743688238877": {"ruleId": "nist-pr.at-1", "ruleName": "PR.AT-1: Security awareness training", "ruleDescription": "", "dreadedEventId": 1743688238877, "dreadedEventName": "Nom de l'événement 1", "initialSeverity": "catastrophic", "residualSeverity": null, "controls": []}, "custom-fyf1s4xyb_1743630980686": {"ruleId": "custom-fyf1s4xyb", "ruleName": "CIS Control 1: Inventory and Control of Enterprise Assets", "ruleDescription": "Actively manage (inventory, track, and correct) all enterprise assets (end-user devices, including portable and mobile", "dreadedEventId": 1743630980686, "dreadedEventName": "Panne d'infrastructure", "initialSeverity": "moderate", "residualSeverity": null, "controls": []}}, "updatedAt": {"$date": "2025-04-05T20:00:05.025Z"}}, {"_id": {"$oid": "67f6a07d808f7fd7dd68ba1b"}, "analysisId": {"$oid": "67f2e2a0a5ad300283807d49"}, "__v": 0, "createdAt": {"$date": "2025-04-09T16:29:49.382Z"}, "planData": {"plan": {"custom-kaczivfnw_1744498112806": {"ruleId": "custom-kaczivfnw", "dreadedEventId": 1744498112806, "treatmentOption": "reduce", "residualSeverity": "minor", "controls": [{"controlId": "ai_1744498315662_tkzg7ud", "name": "Mettre à jour régulièrement la liste blanche des applications autorisées", "isNewSuggestion": true, "responsiblePerson": "RSSI", "decision": "implemented", "decisionDate": null}]}, "custom-kaczivfnw_1746216349990": {"ruleId": "custom-kaczivfnw", "dreadedEventId": 1746216349990, "treatmentOption": "accept", "residualSeverity": "moderate", "controls": [{"controlId": "ai_1746216409529_3f25tmp", "name": "Documentation des logiciels autorisés existants", "isNewSuggestion": true, "responsiblePerson": "RSSI", "decision": "planned", "decisionDate": null}, {"controlId": "ai_1746216409529_2wytjb9", "name": "Revue semestrielle de la liste des logiciels autorisés", "isNewSuggestion": true, "responsiblePerson": "RSSI", "decision": "planned", "decisionDate": null}]}, "custom-75za880n4_1745355678739": {"ruleId": "custom-75za880n4", "dreadedEventId": 1745355678739, "treatmentOption": "reduce", "residualSeverity": "minor", "controls": [{"controlId": "680575d33fca057a8d18c070", "name": "Effectuer des sauvegardes de données régulières et tester fréquemment les procédures de restauration.", "isNewSuggestion": false, "responsiblePerson": null, "decision": null, "decisionDate": null}]}, "custom-75za880n4_1745355691557": {"ruleId": "custom-75za880n4", "dreadedEventId": 1745355691557, "treatmentOption": "transfer", "residualSeverity": "moderate", "controls": [{"controlId": "ai_1745355965563_wysrg6z", "name": "Accord de niveau de service (SLA) avec le fournisseur de la passerelle de paiement garantissant la confidentialité et l'intégrité des données en transit", "isNewSuggestion": true, "responsiblePerson": null, "decision": null, "decisionDate": null}, {"controlId": "ai_1745356348683_j25bs4w", "name": "Exigence contractuelle imposant au fournisseur de la passerelle de paiement une certification de sécurité (ex: PCI DSS)", "isNewSuggestion": true, "responsiblePerson": null, "decision": "implemented", "decisionDate": null}, {"controlId": "ai_1745356367835_w8p1v3c", "name": "Notification écrite au client précisant le transfert de responsabilité de la sécurité des données en transit au fournisseur", "isNewSuggestion": true, "responsiblePerson": null, "decision": "partial", "decisionDate": null}]}, "custom-45js3lhrr_1744476313359": {"ruleId": "custom-45js3lhrr", "dreadedEventId": 1744476313359, "treatmentOption": "reduce", "residualSeverity": "moderate", "controls": [{"controlId": "680575d33fca057a8d18c096", "name": "Utiliser des composants matériels redondants (alimentations, cartes réseau, disques - RAID).", "isNewSuggestion": false, "responsiblePerson": null, "decision": null, "decisionDate": null}]}, "custom-45js3lhrr_1745351903260": {"ruleId": "custom-45js3lhrr", "dreadedEventId": 1745351903260, "treatmentOption": "avoid", "residualSeverity": "major", "controls": [{"controlId": "680575d33fca057a8d18c0a0", "name": "Éviter de planifier la maintenance des systèmes critiques pendant les heures de pointe.", "isNewSuggestion": false, "responsiblePerson": null, "decision": "partial", "decisionDate": null}]}, "custom-n88hy4592_1745351925080": {"ruleId": "custom-n88hy4592", "dreadedEventId": 1745351925080, "treatmentOption": "reduce", "residualSeverity": "minor", "controls": [{"controlId": "680575d33fca057a8d18c097", "name": "Maintenir et tester régulièrement un plan complet de reprise d'activité (PRA) / récupération après sinistre (DR).", "isNewSuggestion": false, "responsiblePerson": null, "decision": null, "decisionDate": null}]}, "custom-n88hy4592_1745351925118": {"ruleId": "custom-n88hy4592", "dreadedEventId": 1745351925118, "treatmentOption": "reduce", "residualSeverity": "minor", "controls": [{"controlId": "680575d33fca057a8d18c098", "name": "Effectuer des sauvegardes système régulières et assurer la capacité de restauration.", "isNewSuggestion": false, "responsiblePerson": null, "decision": null, "decisionDate": null}]}, "custom-z2h4ebult_1744925300411": {"ruleId": "custom-z2h4ebult", "dreadedEventId": 1744925300411, "treatmentOption": "avoid", "residualSeverity": "minor", "controls": [{"controlId": "680575d33fca057a8d18c0a0", "name": "Éviter de planifier la maintenance des systèmes critiques pendant les heures de pointe.", "isNewSuggestion": false, "responsiblePerson": "RSSI", "decision": "apply_before", "decisionDate": "2025-04-30"}]}}}, "updatedAt": {"$date": "2025-05-02T20:07:09.678Z"}}]