// Définir l'ordre des onglets dans chaque workshop pour la navigation
export const TAB_ORDER = {
    workshop1: ['dashboard','context', 'business-values', 'events', 'security','security-controls','risk-treatment'],
    workshop2: ['atelier2-activite1', 'atelier2-activite2', 'atelier2-activite3'],
    workshop3: ['atelier3-activite1', 'atelier3-activite2', 'atelier3-activite3', 'atelier3-activite4', 'atelier3-activite5'],
    workshop4: ['atelier4-activite1'],

    // Vous pouvez ajouter d'autres workshops au fur et à mesure que vous les implémentez
  };

  // Déterminer le workshop actif en fonction de l'onglet sélectionné
  export const getActiveWorkshop = (activeTab) => {
    for (const [workshop, tabs] of Object.entries(TAB_ORDER)) {
      if (tabs.includes(activeTab)) {
        return workshop;
      }
    }
    return 'workshop1'; // Valeur par défaut
  };

  // Fonction pour naviguer vers l'onglet précédent
  export const getPreviousTab = (activeTab, activeWorkshop) => {
    const currentWorkshopTabs = TAB_ORDER[activeWorkshop] || [];
    const currentIndex = currentWorkshopTabs.indexOf(activeTab);

    if (currentIndex > 0) {
      // Aller à l'onglet précédent dans le même workshop
      return {
        nextTab: currentWorkshopTabs[currentIndex - 1],
        nextWorkshop: activeWorkshop
      };
    } else if (activeWorkshop !== 'workshop1') {
      // Si nous sommes au premier onglet d'un workshop (pas le premier workshop),
      // aller au dernier onglet du workshop précédent

      // Trouver le workshop précédent
      const workshopKeys = Object.keys(TAB_ORDER);
      const currentWorkshopIndex = workshopKeys.indexOf(activeWorkshop);

      if (currentWorkshopIndex > 0) {
        const previousWorkshop = workshopKeys[currentWorkshopIndex - 1];
        const previousWorkshopTabs = TAB_ORDER[previousWorkshop] || [];

        // Aller au dernier onglet du workshop précédent
        return {
          nextTab: previousWorkshopTabs[previousWorkshopTabs.length - 1],
          nextWorkshop: previousWorkshop
        };
      }
    }

    // Rester sur le même onglet si nous sommes déjà au début
    return {
      nextTab: activeTab,
      nextWorkshop: activeWorkshop
    };
  };

  // Fonction pour naviguer vers l'onglet suivant
  export const getNextTab = (activeTab, activeWorkshop) => {
    const currentWorkshopTabs = TAB_ORDER[activeWorkshop] || [];
    const currentIndex = currentWorkshopTabs.indexOf(activeTab);

    if (currentIndex < currentWorkshopTabs.length - 1) {
      // Aller à l'onglet suivant dans le même workshop
      return {
        nextTab: currentWorkshopTabs[currentIndex + 1],
        nextWorkshop: activeWorkshop
      };
    } else {
      // Si nous sommes au dernier onglet d'un workshop,
      // aller au premier onglet du workshop suivant

      // Trouver le workshop suivant
      const workshopKeys = Object.keys(TAB_ORDER);
      const currentWorkshopIndex = workshopKeys.indexOf(activeWorkshop);

      if (currentWorkshopIndex < workshopKeys.length - 1) {
        const nextWorkshop = workshopKeys[currentWorkshopIndex + 1];
        const nextWorkshopTabs = TAB_ORDER[nextWorkshop] || [];

        // Aller au premier onglet du workshop suivant
        if (nextWorkshopTabs.length > 0) {
          return {
            nextTab: nextWorkshopTabs[0],
            nextWorkshop: nextWorkshop
          };
        }
      }
    }

    // Rester sur le même onglet si nous sommes déjà à la fin
    return {
      nextTab: activeTab,
      nextWorkshop: activeWorkshop
    };
  };

  // Déterminer si c'est le premier onglet global
  export const isFirstTab = (activeTab) => {
    const firstWorkshop = Object.keys(TAB_ORDER)[0];
    const firstTab = TAB_ORDER[firstWorkshop][0];
    return activeTab === firstTab;
  };

  // Déterminer si c'est le dernier onglet global
  export const isLastTab = (activeTab) => {
    const lastWorkshop = Object.keys(TAB_ORDER)[Object.keys(TAB_ORDER).length - 1];
    const lastWorkshopTabs = TAB_ORDER[lastWorkshop];
    const lastTab = lastWorkshopTabs[lastWorkshopTabs.length - 1];
    return activeTab === lastTab;
  };

  // Variable globale pour stocker l'état des modifications non enregistrées
  let _hasUnsavedChanges = false;
  let _navigationWarningCallback = null;
  let _navigationWarningDialogOpen = false;

  // Fonction pour définir l'état des modifications non enregistrées
  export const setUnsavedChanges = (hasUnsavedChanges) => {
    _hasUnsavedChanges = hasUnsavedChanges;
  };

  // Fonction pour obtenir l'état des modifications non enregistrées
  export const getUnsavedChanges = () => {
    return _hasUnsavedChanges;
  };

  // Fonction pour définir le callback de navigation
  export const setNavigationWarningCallback = (callback) => {
    _navigationWarningCallback = callback;
  };

  // Fonction pour vérifier si la navigation est autorisée
  export const checkNavigationAllowed = () => {
    if (_hasUnsavedChanges && _navigationWarningCallback) {
      return _navigationWarningCallback();
    }
    return true;
  };

  // Fonction pour définir l'état de la boîte de dialogue d'avertissement
  export const setNavigationWarningDialogOpen = (isOpen) => {
    _navigationWarningDialogOpen = isOpen;
  };

  // Fonction pour obtenir l'état de la boîte de dialogue d'avertissement
  export const getNavigationWarningDialogOpen = () => {
    return _navigationWarningDialogOpen;
  };
