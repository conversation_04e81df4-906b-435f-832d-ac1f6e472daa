// src/middleware/authMiddleware.js
import { verifyAccessToken } from '../services/authService';

/**
 * Middleware d'authentification - vérifie que l'utilisateur est connecté
 * via un JWT valide dans le header Authorization
 */
export const authenticate = (req, res, next) => {
  // Récupérer le token du header Authorization
  const authHeader = req.headers.authorization;
  
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return res.status(401).json({ 
      success: false, 
      message: 'Accès non autorisé. Token manquant ou invalide' 
    });
  }
  
  // Extraire le token sans le prefix 'Bearer '
  const token = authHeader.split(' ')[1];
  
  // Vérifier le token
  const decoded = verifyAccessToken(token);
  
  if (!decoded) {
    return res.status(401).json({ 
      success: false, 
      message: 'Token invalide ou expiré' 
    });
  }
  
  // Attacher les informations utilisateur au request
  req.user = {
    id: decoded.userId,
    role: decoded.role
  };
  
  // Continuer vers le prochain middleware ou contrôleur
  next();
};

/**
 * Middleware de contrôle des rôles - vérifie que l'utilisateur a un rôle autorisé
 * @param {string[]} allowedRoles - Liste des rôles autorisés
 * @returns {Function} - Middleware Express
 */
export const authorize = (allowedRoles) => {
  return (req, res, next) => {
    // Vérifie d'abord que req.user existe (middleware authenticate appelé avant)
    if (!req.user || !req.user.role) {
      return res.status(401).json({
        success: false,
        message: 'Utilisateur non authentifié'
      });
    }

    // Vérifie que le rôle de l'utilisateur est dans les rôles autorisés
    if (!allowedRoles.includes(req.user.role)) {
      return res.status(403).json({
        success: false,
        message: 'Permission refusée. Rôle insuffisant'
      });
    }

    // Si tout est bon, continuer
    next();
  };
};

/**
 * Middlewares pré-configurés pour les vérifications de rôles courantes
 */
export const roles = {
  // Superadmin uniquement
  isSuperAdmin: authorize(['superadmin']),
  
  // Admin (superadmin ou admin d'entreprise)
  isAdmin: authorize(['superadmin', 'admin']),
  
  // Tous utilisateurs authentifiés (inclut simpleuser)
  isAuthenticated: authenticate,
  
  // Superadmin ou admin
  isAdminOrSuperAdmin: authorize(['superadmin', 'admin']),
  
  // Admin ou simple user de la même entreprise
  isSameCompanyOrAdmin: (req, res, next) => {
    // Vérifie d'abord que req.user existe
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: 'Utilisateur non authentifié'
      });
    }
    
    // Si superadmin ou admin, autoriser
    if (['superadmin', 'admin'].includes(req.user.role)) {
      return next();
    }
    
    // Pour un utilisateur simple, vérifier l'appartenance à la même entreprise
    // Note: req.params.companyId devrait être disponible sur la route
    if (req.user.companyId && req.params.companyId && 
        req.user.companyId.toString() === req.params.companyId.toString()) {
      return next();
    }
    
    // Autrement, refuser l'accès
    return res.status(403).json({
      success: false,
      message: 'Permission refusée. Vous n\'avez pas accès à cette ressource'
    });
  }
};

/**
 * Middleware qui vérifie si l'utilisateur est propriétaire de la ressource
 * ou s'il est admin/superadmin
 * @param {Function} getResourceOwnerId - Fonction qui retourne l'ID du propriétaire 
 *                                      à partir de req (ex: req => req.params.userId)
 */
export const isOwnerOrAdmin = (getResourceOwnerId) => {
  return async (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: 'Utilisateur non authentifié'
      });
    }
    
    // Si superadmin ou admin, autoriser
    if (['superadmin', 'admin'].includes(req.user.role)) {
      return next();
    }
    
    // Pour les autres utilisateurs, vérifier s'ils sont propriétaires
    const ownerId = getResourceOwnerId(req);
    if (ownerId && req.user.id && ownerId.toString() === req.user.id.toString()) {
      return next();
    }
    
    // Autrement, refuser l'accès
    return res.status(403).json({
      success: false,
      message: 'Permission refusée. Vous n\'êtes pas propriétaire de cette ressource'
    });
  };
};