// BusinessValueListView.js
import React from 'react';
import { useTranslation } from 'react-i18next';
import BusinessValueListItem from './BusinessValueListItem';

const BusinessValueListView = ({
  businessValues,
  expandedValue,
  setExpandedValue,
  editingValue,
  setEditingValue,
  editValue,
  setEditValue,
  handleStartEdit,
  handleEditSave,
  handleCancelEdit,
  handleRemoveBusinessValue,
  handleToggleSecurityPillar,
  newSupportAssetName,
  setNewSupportAssetName,
  newSupportAssetDescription,
  setNewSupportAssetDescription,
  newSupportAssetType,
  setNewSupportAssetType,
  newSupportAssetOwner,
  setNewSupportAssetOwner,
  newSupportAssetLocation,
  setNewSupportAssetLocation,
  // CPE fields
  newSupportAssetVendor,
  setNewSupportAssetVendor,
  newSupportAssetProduct,
  setNewSupportAssetProduct,
  newSupportAssetVersion,
  setNewSupportAssetVersion,
  discoveredCPE,
  cpeConfidence,
  cpeSearching,
  assetTypes,
  handleAddSupportAsset,
  editingAsset,
  handleStartEditSupportAsset,
  handleCancelEditSupportAsset,
  handleEditSupportAssetChange,
  handleEditSupportAssetSave,
  handleRemoveSupportAsset
}) => {
  const { t } = useTranslation();
  return (
    <div className="bg-gray-50 p-4 rounded-md">
      <h3 className="font-medium mb-2">{t('businessValues.sections.identifiedValues')}</h3>
      {businessValues.length === 0 ? (
        <p className="text-gray-500">{t('businessValues.empty.noValues')}</p>
      ) : (
        <ul className="space-y-4">
          {businessValues.map((value) => (
            <BusinessValueListItem
              key={value.id}
              value={value}
              expandedValue={expandedValue}
              setExpandedValue={setExpandedValue}
              editingValue={editingValue}
              setEditingValue={setEditingValue}
              editValue={editValue}
              setEditValue={setEditValue}
              handleStartEdit={handleStartEdit}
              handleEditSave={handleEditSave}
              handleCancelEdit={handleCancelEdit}
              handleRemoveBusinessValue={handleRemoveBusinessValue}
              handleToggleSecurityPillar={handleToggleSecurityPillar}
              newSupportAssetName={newSupportAssetName}
              setNewSupportAssetName={setNewSupportAssetName}
              newSupportAssetDescription={newSupportAssetDescription}
              setNewSupportAssetDescription={setNewSupportAssetDescription}
              newSupportAssetType={newSupportAssetType}
              setNewSupportAssetType={setNewSupportAssetType}
              newSupportAssetOwner={newSupportAssetOwner}
              setNewSupportAssetOwner={setNewSupportAssetOwner}
              newSupportAssetLocation={newSupportAssetLocation}
              setNewSupportAssetLocation={setNewSupportAssetLocation}
              // CPE fields
              newSupportAssetVendor={newSupportAssetVendor}
              setNewSupportAssetVendor={setNewSupportAssetVendor}
              newSupportAssetProduct={newSupportAssetProduct}
              setNewSupportAssetProduct={setNewSupportAssetProduct}
              newSupportAssetVersion={newSupportAssetVersion}
              setNewSupportAssetVersion={setNewSupportAssetVersion}
              discoveredCPE={discoveredCPE}
              cpeConfidence={cpeConfidence}
              cpeSearching={cpeSearching}
              assetTypes={assetTypes}
              handleAddSupportAsset={handleAddSupportAsset}
              editingAsset={editingAsset}
              handleStartEditSupportAsset={handleStartEditSupportAsset}
              handleCancelEditSupportAsset={handleCancelEditSupportAsset}
              handleEditSupportAssetChange={handleEditSupportAssetChange}
              handleEditSupportAssetSave={handleEditSupportAssetSave}
              handleRemoveSupportAsset={handleRemoveSupportAsset}
            />
          ))}
        </ul>
      )}
    </div>
  );
};

export default BusinessValueListView;