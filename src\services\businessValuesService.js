// src/services/businessValueService.js
import api from '../api/apiClient';

/**
 * Service for handling business values operations
 * API-first approach without localStorage fallback for loading
 */
const businessValueService = {
  /**
   * Get business values for a specific analysis
   * @param {string} analysisId - Analysis ID
   * @returns {Promise<Object>} Business values data
   */
  getBusinessValues: async (analysisId) => {
    try {
      // Try to get from API
      const response = await api.get(`/analyses/${analysisId}/business-values`);
      
      if (response.success) {
        return response.data;
      }
      
      throw new Error(response.message || 'Failed to fetch business values data');
    } catch (error) {
      console.error('Error fetching business values data'); // Removed error object from log
      // No localStorage fallback - API is the source of truth
      throw error;
    }
  },
  
  /**
   * Save business values for a specific analysis
   * @param {string} analysisId - Analysis ID
   * @param {Object} businessValuesData - Business values data to save
   * @returns {Promise<Object>} Saved business values data
   */
  saveBusinessValues: async (analysisId, businessValuesData) => {
    // Format data for API (if needed)
    const formattedData = {
      data: {
        businessValues: businessValuesData
      }
    };
    
    try {
      // Save to API
      const response = await api.put(`/analyses/${analysisId}/business-values`, formattedData);
      
      // Also save to local storage as backup
      localStorage.setItem('businessValuesData', JSON.stringify(businessValuesData));
      
      return response;
    } catch (error) {
      console.error('Error saving business values data'); // Removed error object from log

      // If API fails, at least try to save locally
      localStorage.setItem('businessValuesData', JSON.stringify(businessValuesData));
      
      // Return a success response with a local flag
      return {
        success: true,
        data: businessValuesData,
        savedLocally: true,
        message: 'Data saved locally only. Will sync when connection is restored.'
      };
    }
  },
  
  /**
   * Format business values data for component consumption
   * @param {Object} rawData - Raw data from API
   * @returns {Array} Formatted business values array
   */
  formatBusinessValuesData: (rawData) => {
    // If data is already in array format, return it
    if (Array.isArray(rawData)) {
      return rawData;
    }
    
    // Extract from API response
    const data = rawData.data || {};
    
    // Return business values array from data
    return data.businessValues || [];
  },
  
  /**
   * Get business values history
   * @param {string} analysisId - Analysis ID
   * @returns {Promise<Array>} Business values history
   */
  getBusinessValuesHistory: async (analysisId) => {
    try {
      const response = await api.get(`/analyses/${analysisId}/components/business-values/history`);
      return response.data || [];
    } catch (error) {
      console.error('Error fetching business values history'); // Removed error object from log
      throw error;
    }
  }
};

export default businessValueService;