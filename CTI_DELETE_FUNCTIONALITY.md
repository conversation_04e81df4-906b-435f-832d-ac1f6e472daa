# CTI Analysis Delete Functionality

## Overview

This document describes the comprehensive delete functionality added for CTI (Cyber Threat Intelligence) analysis results, allowing users to remove saved CTI data at various levels of granularity.

## Features Implemented

### 1. Backend API Endpoints

#### Delete CTI Results
- **Endpoint**: `DELETE /api/cti/delete-results`
- **Parameters**:
  - `analysisId` (optional): Delete all CTI results for a specific analysis
  - `reportId` (optional): Delete a specific CTI report by ID
  - No parameters: Delete general CTI results (backward compatibility)

#### List CTI Results
- **Endpoint**: `GET /api/cti/list-results`
- **Parameters**:
  - `analysisId` (optional): List CTI results for a specific analysis
  - No parameters: List general CTI results

#### Enhanced Load CTI Results
- **Endpoint**: `GET /api/cti/load-results`
- **Parameters**:
  - `analysisId` (optional): Load CTI results for a specific analysis
  - No parameters: Load general CTI results (backward compatibility)

### 2. Frontend Integration

#### CTI Display Component
- **Delete Button**: Added red delete button next to save button
- **Confirmation Dialog**: Requires user confirmation before deletion
- **Loading States**: Shows loading spinner during deletion process
- **Error Handling**: Displays appropriate error messages

#### Service Layer
- **deleteCTIResults**: Delete CTI results for an analysis (existing)
- **deleteCTIReport**: Delete specific CTI report by ID
- **deleteAllCTIResults**: Delete all CTI results for an analysis
- **listCTIResults**: List all CTI results for an analysis

## API Documentation

### Delete CTI Results

```javascript
// Delete all CTI results for an analysis
DELETE /api/cti/delete-results?analysisId=analysis-123

// Delete specific CTI report
DELETE /api/cti/delete-results?reportId=report-456

// Delete general CTI results
DELETE /api/cti/delete-results
```

**Response Format**:
```json
{
  "success": true,
  "message": "Successfully deleted 3 CTI results",
  "data": {
    "deletedCount": 3,
    "analysisId": "analysis-123",
    "reportId": null
  }
}
```

### List CTI Results

```javascript
// List CTI results for specific analysis
GET /api/cti/list-results?analysisId=analysis-123

// List all general CTI results
GET /api/cti/list-results
```

**Response Format**:
```json
{
  "success": true,
  "count": 2,
  "data": [
    {
      "id": "report-id-1",
      "attackPath": { "id": "path-1", "name": "Attack Path 1" },
      "selectedAssets": ["asset-1", "asset-2"],
      "timestamp": "2025-07-04T21:00:00.000Z",
      "createdAt": "2025-07-04T21:00:00.000Z",
      "updatedAt": "2025-07-04T21:00:00.000Z",
      "totalVulnerabilities": 5,
      "totalAttackTechniques": 3,
      "totalAssets": 2
    }
  ]
}
```

## Frontend Usage

### CTI Display Component

```jsx
<CTIDisplay 
  results={ctiResults} 
  onSave={handleSave} 
  onDelete={handleDelete}
  isSaving={isSaving} 
  isDeleting={isDeleting}
/>
```

### Delete Handler Implementation

```javascript
const handleDelete = async () => {
  if (!ctiResults) return showErrorToast("Aucun résultat à supprimer.");
  
  // Show confirmation dialog
  const confirmed = window.confirm(
    "Êtes-vous sûr de vouloir supprimer tous les résultats d'analyse CTI ? Cette action est irréversible."
  );
  
  if (!confirmed) return;
  
  setIsDeleting(true);

  try {
    await ctiResultsService.deleteCTIResults(currentAnalysis.id);
    
    // Clear the results from state
    setCtiResults(null);
    setHasSavedResults(false);
    
    showSuccessToast("Résultats d'analyse CTI supprimés avec succès !");
  } catch (err) {
    console.error('Delete error:', err);
    showErrorToast(`Erreur lors de la suppression: ${err.message}`);
  } finally {
    setIsDeleting(false);
  }
};
```

## Service Layer Methods

### Delete Methods

```javascript
// Delete CTI results for an analysis (existing method)
await ctiResultsService.deleteCTIResults(analysisId);

// Delete specific CTI report by ID
await ctiResultsService.deleteCTIReport(reportId);

// Delete all CTI results for an analysis (new method)
await ctiResultsService.deleteAllCTIResults(analysisId);

// List CTI results
const results = await ctiResultsService.listCTIResults(analysisId);
```

## Error Handling

### Backend Error Responses

- **404 Not Found**: When no CTI results exist to delete
- **500 Internal Server Error**: For database or server errors
- **400 Bad Request**: For invalid parameters

### Frontend Error Handling

- **Confirmation Dialog**: Prevents accidental deletions
- **Loading States**: Prevents multiple delete operations
- **Toast Notifications**: Clear feedback on success/failure
- **State Management**: Proper cleanup of local state after deletion

## Security Considerations

### Access Control
- Routes are currently public for testing (marked in comments)
- Production deployment should add authentication middleware
- Consider role-based access control for delete operations

### Data Validation
- Parameter validation on backend
- Confirmation dialogs on frontend
- Proper error handling for edge cases

## Testing

### Test Script
Run `node test-cti-delete.js` to verify:
1. **Create Test Data**: Creates sample CTI results
2. **List Results**: Verifies listing functionality
3. **Load Results**: Verifies loading functionality
4. **Delete General Results**: Tests general deletion
5. **Verify Deletion**: Confirms data was deleted
6. **Analysis-Specific Tests**: Tests analysis-specific operations
7. **Edge Cases**: Tests deletion of non-existent data

### Expected Test Results
- ✅ CTI data creation and deletion work properly
- ✅ List and load operations function correctly
- ✅ Analysis-specific operations work as expected
- ✅ Edge cases are handled gracefully
- ✅ Proper error responses for non-existent data

## User Experience

### UI/UX Features
- **Visual Feedback**: Red delete button with trash icon
- **Confirmation**: Required confirmation before deletion
- **Loading States**: Spinner during deletion process
- **Disabled States**: Buttons disabled during operations
- **Toast Messages**: Clear success/error feedback

### Workflow Integration
- Delete button appears alongside save button
- Deletion clears local state and updates UI
- Proper integration with existing CTI workflow
- Maintains consistency with other delete operations

## Future Enhancements

### Potential Improvements
1. **Bulk Operations**: Select and delete multiple reports
2. **Soft Delete**: Mark as deleted instead of permanent removal
3. **Audit Trail**: Log deletion activities for compliance
4. **Backup/Restore**: Create backups before deletion
5. **Advanced Filtering**: Delete by date range, asset type, etc.

### Performance Considerations
- **Pagination**: For large numbers of CTI results
- **Background Jobs**: For bulk deletion operations
- **Caching**: Invalidate caches after deletion
- **Database Optimization**: Proper indexing for delete queries

## Configuration

### Environment Variables
No additional environment variables required for basic functionality.

### Database Considerations
- Ensure proper indexing on `analysisId` and `_id` fields
- Consider cascade deletion for related data
- Monitor database performance for large deletions

## Monitoring and Logging

### Logging
- All delete operations are logged with details
- Error logging for troubleshooting
- Performance monitoring for large operations

### Metrics
- Track deletion frequency and patterns
- Monitor error rates for delete operations
- Measure user engagement with delete functionality
