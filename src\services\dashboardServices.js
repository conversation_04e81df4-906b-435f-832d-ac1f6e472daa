// src/services/dashboardServices.js
import api from '../api/apiClient';
import { handleApiError } from '../utils/removeMockDataFallbacks';

/**
 * Dashboard services for fetching analytics and metrics
 */
export const dashboardService = {
  /**
   * Get dashboard metrics for superadmin
   * @returns {Promise<Object>} Dashboard metrics data
   */
  getSuperAdminMetrics: async () => {
    try {
      return await api.get('/dashboard/metrics/superadmin');
    } catch (error) {
      handleApiError(error, 'getSuperAdminMetrics');
    }
  },

  /**
   * Get dashboard metrics for company provider
   * @param {string} companyId - Company ID (optional, uses current user's company if not provided)
   * @returns {Promise<Object>} Dashboard metrics data for the company
   */
  getProviderMetrics: async (companyId = null) => {
    try {
      const endpoint = companyId
        ? `/dashboard/metrics/provider/${companyId}`
        : '/dashboard/metrics/provider';

      return await api.get(endpoint);
    } catch (error) {
      handleApiError(error, 'getProviderMetrics');
    }
  },

  /**
   * Get activity summary by time period (day, week, month)
   * @param {Object} params - Filter parameters
   * @param {string} params.period - Time period ('day', 'week', 'month')
   * @param {string} params.companyId - Company ID (optional)
   * @returns {Promise<Object>} Activity summary data
   */
  getActivitySummary: async (params = {}) => {
    try {
      return await api.get('/dashboard/activity-summary', params);
    } catch (error) {
      handleApiError(error, 'getActivitySummary');
    }
  }
};

// Export enhanced companyService with additional metrics methods
export const enhanceCompanyService = (originalCompanyService) => {
  return {
    ...originalCompanyService,

    /**
     * Get detailed company metrics
     * @returns {Promise<Object>} Detailed metrics for all companies
     */
    getCompanyMetrics: async () => {
      try {
        return await api.get('/companies/metrics');
      } catch (error) {
        handleApiError(error, 'getCompanyMetrics');
      }
    }
  };
};