// test-routes-connectivity.js
// Test script to verify that all CTI proxy routes are accessible

const axios = require('axios');

const BASE_URL = 'http://localhost:5000';

async function testRoutesConnectivity() {
  console.log('🧪 Testing CTI Proxy Routes Connectivity...\n');

  const routes = [
    { name: 'NIST Test', url: `${BASE_URL}/api/nist/test` },
    { name: 'EUVD Test', url: `${BASE_URL}/api/euvd/test` },
    { name: 'MITRE Test', url: `${BASE_URL}/api/mitre/test` },
    { name: 'ATLAS Test', url: `${BASE_URL}/api/atlas/test` },
    { name: 'CTI Test', url: `${BASE_URL}/api/cti/load-results` }
  ];

  for (const route of routes) {
    try {
      console.log(`Testing ${route.name}...`);
      
      const response = await axios.get(route.url, {
        timeout: 5000,
        validateStatus: function (status) {
          // Accept any status code (including 404) as long as we get a response
          return status < 500;
        }
      });

      if (response.status === 200) {
        console.log(`✅ ${route.name}: Connected successfully`);
        console.log(`   Response: ${response.data.message || 'OK'}`);
      } else if (response.status === 404) {
        console.log(`⚠️ ${route.name}: Route accessible but endpoint not found (${response.status})`);
        console.log(`   This is expected for some test endpoints`);
      } else {
        console.log(`⚠️ ${route.name}: Accessible but returned status ${response.status}`);
      }

    } catch (error) {
      if (error.code === 'ECONNREFUSED') {
        console.log(`❌ ${route.name}: Connection refused - server not running`);
      } else if (error.code === 'ENOTFOUND') {
        console.log(`❌ ${route.name}: Host not found`);
      } else if (error.response) {
        console.log(`❌ ${route.name}: HTTP ${error.response.status} - ${error.response.statusText}`);
        if (error.response.data && error.response.data.message) {
          console.log(`   Error: ${error.response.data.message}`);
        }
      } else {
        console.log(`❌ ${route.name}: ${error.message}`);
      }
    }

    // Add small delay between requests
    await new Promise(resolve => setTimeout(resolve, 500));
  }

  console.log('\n🔍 Testing actual NIST search functionality...');
  
  // Test actual NIST search
  try {
    const nistSearchResponse = await axios.get(`${BASE_URL}/api/nist/search`, {
      params: {
        keywordSearch: 'apache',
        resultsPerPage: 5,
        startIndex: 0
      },
      timeout: 10000
    });

    console.log(`✅ NIST Search: Working! Found ${nistSearchResponse.data.totalResults || 0} results`);
    
    if (nistSearchResponse.data.vulnerabilities && nistSearchResponse.data.vulnerabilities.length > 0) {
      const firstVuln = nistSearchResponse.data.vulnerabilities[0];
      console.log(`   First result: ${firstVuln.cve.id}`);
    }

  } catch (error) {
    if (error.response) {
      console.log(`❌ NIST Search: HTTP ${error.response.status} - ${error.response.statusText}`);
      if (error.response.data && error.response.data.message) {
        console.log(`   Error: ${error.response.data.message}`);
      }
    } else {
      console.log(`❌ NIST Search: ${error.message}`);
    }
  }

  console.log('\n🏁 Route connectivity testing completed!');
  console.log('\n💡 Next steps:');
  console.log('   1. If routes are not accessible, restart the backend server');
  console.log('   2. If NIST search fails, check rate limiting and API connectivity');
  console.log('   3. Test the frontend CTI functionality');
}

// Run the test
if (require.main === module) {
  testRoutesConnectivity().catch(console.error);
}

module.exports = { testRoutesConnectivity };
