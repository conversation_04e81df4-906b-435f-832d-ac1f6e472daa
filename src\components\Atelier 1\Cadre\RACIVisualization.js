import React, { useState, useMemo } from 'react';
import { motion } from 'framer-motion';
import { useTranslation } from 'react-i18next';
import { Filter, BarChart2, Grid, Thermometer, Users, X } from 'lucide-react';
import { roleColors } from '../../../constants'; // Ensure roleColors is correctly defined/exported

const RACIVisualization = ({ participants = [], workshops = [], matrix = {} }) => {
  const { t } = useTranslation();

  const viewOptions = [
    { id: 'matrix', labelKey: 'matrixSynthesis', icon: Grid },
    { id: 'heatmap', labelKey: 'workshopFocus', icon: Thermometer },
    { id: 'chart', labelKey: 'participantFocus', icon: Users },
  ];
  const [view, setView] = useState('matrix'); // 'matrix', 'heatmap', 'chart'
  const [filteredRole, setFilteredRole] = useState('');
  const [filteredWorkshop, setFilteredWorkshop] = useState('');

  // Memoize calculations for performance
  const { filteredParticipants, filteredWorkshops, totalCounts } = useMemo(() => {
    let currentParticipants = [...participants];
    let currentWorkshops = [...workshops];

    // Filter by Role
    if (filteredRole) {
      currentParticipants = currentParticipants.filter(participant =>
        matrix[participant.id] && Object.values(matrix[participant.id]).includes(filteredRole)
      );
    }

    // Filter by Workshop
    if (filteredWorkshop) {
        const workshopToFilter = filteredWorkshop; // Store the value
        currentWorkshops = currentWorkshops.filter(w => w.id === workshopToFilter);

        // If filtering by workshop, ensure participants have *some* role in that specific workshop,
        // unless already filtered by a specific role (in which case, they must have *that* role in the workshop).
        currentParticipants = currentParticipants.filter(participant => {
            const roleInWorkshop = matrix[participant.id]?.[workshopToFilter];
            if (filteredRole) {
                return roleInWorkshop === filteredRole;
            }
            return roleInWorkshop && roleInWorkshop !== '' && roleInWorkshop !== '-'; // Has any actual role (not empty or '-')
        });

    }

    // Calculate total counts based on the *filtered* data
    const counts = { 'R': 0, 'A': 0, 'C': 0, 'I': 0, '-': 0 };
    currentParticipants.forEach(participant => {
      if (matrix[participant.id]) {
        currentWorkshops.forEach(workshop => {
          const role = matrix[participant.id][workshop.id];
          if (role && counts[role] !== undefined) {
            counts[role]++;
          }
        });
      }
    });

    return {
      filteredParticipants: currentParticipants,
      filteredWorkshops: currentWorkshops,
      totalCounts: counts
    };
  }, [participants, workshops, matrix, filteredRole, filteredWorkshop]);

  // Check if there's anything to display after filtering
  const hasDataToShow = filteredParticipants.length > 0 && filteredWorkshops.length > 0;
  const totalAssignments = Object.values(totalCounts).reduce((sum, count) => sum + count, 0);

  // Helper to get role color safely
  const getRoleColor = (role) => roleColors[role] || roleColors['-']; // Default to '-' style if role unknown

   // Helper to get participant details for heatmap/chart
   const getParticipantsByWorkshopAndRole = (workshopId, role) => {
       return filteredParticipants.filter(participant =>
           matrix[participant.id]?.[workshopId] === role
       );
   };

   // --- Render Components ---

   const renderFilters = () => (
       <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-4 p-4 bg-slate-50 rounded-lg border border-slate-200">
           {/* Role Filter */}

           <div>
               <label htmlFor="roleFilter" className="block text-xs font-medium text-slate-600 mb-1">{t('context.visualization.filterByRole')}</label>
               <select
                   id="roleFilter"
                   className="w-full p-2 border border-slate-300 rounded-md text-sm shadow-sm focus:ring-1 focus:ring-blue-500 focus:border-blue-500 transition"
                   value={filteredRole}
                   onChange={(e) => setFilteredRole(e.target.value)}
                   aria-label="Filtrer la visualisation par rôle RACI"
               >
                   <option value="">{t('context.visualization.allRoles')}</option>
                   <option value="R">R - {t('context.raci.responsible')}</option>
                   <option value="A">A - {t('context.raci.accountable')}</option>
                   <option value="C">C - {t('context.raci.consulted')}</option>
                   <option value="I">I - {t('context.raci.informed')}</option>
                   <option value="-">- - {t('context.raci.notInvolved')}</option>
               </select>
           </div>
           {/* Workshop Filter */}
           <div>
               <label htmlFor="workshopFilter" className="block text-xs font-medium text-slate-600 mb-1">{t('context.visualization.filterByWorkshop')}</label>
               <select
                   id="workshopFilter"
                   className="w-full p-2 border border-slate-300 rounded-md text-sm shadow-sm focus:ring-1 focus:ring-blue-500 focus:border-blue-500 transition"
                   value={filteredWorkshop}
                   onChange={(e) => setFilteredWorkshop(e.target.value)}
                   aria-label="Filtrer la visualisation par atelier"
               >
                   <option value="">{t('context.visualization.allWorkshops')}</option>
                   {workshops.map(workshop => ( // Use original workshops list for filter options
                       <option key={workshop.id} value={workshop.id}>
                           {t(`context.table.workshop${workshop.id.slice(-1)}`) || workshop.name}
                       </option>
                   ))}
               </select>
           </div>
           {/* Active Filter Badges */}
           {(filteredRole || filteredWorkshop) && (
                <div className="sm:col-span-2 flex flex-wrap gap-2 items-center mt-2">
                    <span className="text-xs font-medium text-slate-500">{t('context.visualization.activeFilters')}</span>
                     {filteredRole && (
                        <div className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 border border-blue-200">
                            {t('context.visualization.role')} {roleColors[filteredRole]?.label || filteredRole}
                            <button
                                className="ml-1.5 -mr-0.5 flex-shrink-0 h-4 w-4 rounded-full inline-flex items-center justify-center text-blue-500 hover:bg-blue-200 hover:text-blue-600 focus:outline-none focus:bg-blue-500 focus:text-white"
                                onClick={() => setFilteredRole('')} aria-label={t('context.visualization.removeRoleFilter')}
                            >
                                <X size={12} />
                            </button>
                        </div>
                    )}
                    {filteredWorkshop && (
                        <div className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 border border-green-200">
                            {t('context.visualization.workshop')} {(() => {
                              const workshop = workshops.find(w => w.id === filteredWorkshop);
                              return workshop ? (t(`context.table.workshop${workshop.id.slice(-1)}`) || workshop.name) : filteredWorkshop;
                            })()}
                             <button
                                className="ml-1.5 -mr-0.5 flex-shrink-0 h-4 w-4 rounded-full inline-flex items-center justify-center text-green-500 hover:bg-green-200 hover:text-green-600 focus:outline-none focus:bg-green-500 focus:text-white"
                                onClick={() => setFilteredWorkshop('')} aria-label={t('context.visualization.removeWorkshopFilter')}
                            >
                                <X size={12} />
                            </button>
                        </div>
                    )}
                 </div>
            )}
       </div>
   );

   const renderMatrixView = () => (
        <div className="overflow-x-auto shadow-sm rounded-lg border border-slate-200 bg-white">
            <table className="min-w-full divide-y divide-slate-200">
                 <thead className="bg-slate-50">
                    <tr>
                        <th scope="col" className="sticky left-0 bg-slate-50 z-10 py-3 px-4 text-left text-xs font-semibold text-slate-600 uppercase tracking-wider w-48 border-r border-slate-200">{t('context.table.participant')}</th>
                         <th scope="col" className="py-3 px-4 text-left text-xs font-semibold text-slate-600 uppercase tracking-wider w-40 border-r border-slate-200">{t('context.visualization.function')}</th>
                        {filteredWorkshops.map(workshop => (
                            <th key={workshop.id} scope="col" className="py-3 px-4 text-center text-xs font-semibold text-slate-600 uppercase tracking-wider min-w-[100px] border-r border-slate-200 whitespace-nowrap">
                              {t(`context.table.workshop${workshop.id.slice(-1)}`) || workshop.name}
                            </th>
                        ))}
                         <th scope="col" className="py-3 px-4 text-center text-xs font-semibold text-slate-600 uppercase tracking-wider w-32">{t('context.visualization.distribution')}</th>
                    </tr>
                 </thead>
                 <tbody className="bg-white divide-y divide-slate-200">
                    {filteredParticipants.map((participant, index) => {
                        // Calculate role counts ONLY for the filtered workshops for this participant
                        const roleCounts = { 'R': 0, 'A': 0, 'C': 0, 'I': 0, '-': 0 };
                        let assignedRolesCount = 0;
                         if (matrix[participant.id]) {
                            filteredWorkshops.forEach(workshop => {
                                const role = matrix[participant.id][workshop.id];
                                if (role && roleCounts[role] !== undefined) {
                                    roleCounts[role]++;
                                    if (role !== '-' && role !== '') assignedRolesCount++;
                                }
                            });
                        }

                        return (
                             <tr key={participant.id} className={`hover:bg-slate-50 transition-colors duration-150 ${index % 2 !== 0 ? 'bg-slate-50/50' : ''}`}>
                                 <td className="sticky left-0 z-0 py-3 px-4 whitespace-nowrap text-sm font-medium text-slate-900 border-r border-slate-200" style={{ backgroundColor: index % 2 !== 0 ? '#F8FAFC' : '#FFFFFF' }}>{participant.name || 'N/A'}</td>
                                 <td className="py-3 px-4 whitespace-nowrap text-sm text-slate-600 border-r border-slate-200">{participant.position === "OTHER" ? (participant.customPosition || 'Autre') : (participant.position || 'N/D')}</td>
                                 {filteredWorkshops.map(workshop => {
                                    const role = matrix[participant.id]?.[workshop.id] || '-';
                                    const roleColor = getRoleColor(role);
                                    return (
                                        <td key={workshop.id} className={`py-3 px-4 whitespace-nowrap text-center border-r border-slate-200`}>
                                            <span className={`inline-flex items-center justify-center w-7 h-7 rounded-full ${roleColor.bg} ${roleColor.text} font-bold text-xs shadow-sm border ${roleColor.border}`}>
                                                {role}
                                            </span>
                                         </td>
                                    );
                                })}
                                 <td className="py-3 px-4 whitespace-nowrap text-center">
                                    {assignedRolesCount > 0 ? (
                                         <div className="flex space-x-1.5 justify-center items-center">
                                             {['R', 'A', 'C', 'I', '-'].map(role => {
                                                if (roleCounts[role] === 0) return null;
                                                const roleColor = getRoleColor(role);
                                                return (
                                                     <div key={role} className="flex items-center" title={`${roleColor.label}: ${roleCounts[role]}`}>
                                                        <span className={`inline-block w-2.5 h-2.5 rounded-full ${roleColor.pill}`}></span>
                                                        <span className="text-xs text-slate-600 ml-1">{roleCounts[role]}</span>
                                                    </div>
                                                );
                                             })}
                                        </div>
                                     ) : (
                                         <span className="text-xs text-slate-400 italic">{t('context.visualization.none')}</span>
                                     )}
                                 </td>
                             </tr>
                         );
                    })}
                 </tbody>
            </table>
        </div>
   );

   const renderHeatmapView = () => (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-5">
            {filteredWorkshops.map(workshop => (
                 <div key={workshop.id} className="border border-slate-200 rounded-xl shadow-sm overflow-hidden bg-white">
                    <div className="bg-slate-100 p-3 border-b border-slate-200">
                         <h3 className="font-semibold text-base text-slate-800 truncate">
                           {t(`context.table.workshop${workshop.id.slice(-1)}`) || workshop.name}
                         </h3>
                    </div>
                     <div className="p-4 space-y-3">
                        {['R', 'A', 'C', 'I'].map(role => {
                             // Skip rendering this role box if a different role is filtered
                             if (filteredRole && filteredRole !== role) return null;

                            const roleColor = getRoleColor(role);
                             const participantsWithRole = getParticipantsByWorkshopAndRole(workshop.id, role);

                             return (
                                 <div key={role} className={`rounded-lg p-3 border ${roleColor.border} ${participantsWithRole.length > 0 ? roleColor.bg : 'bg-slate-50'}`}>
                                     <div className="flex items-center justify-between mb-2">
                                         <div className="flex items-center">
                                             <span className={`inline-flex items-center justify-center w-6 h-6 rounded-full ${roleColor.pill} text-white font-bold text-xs mr-2 shadow-sm`}>{role}</span>
                                             <span className={`font-medium text-sm ${roleColor.text}`}>{roleColor.label}</span>
                                         </div>
                                         <span className={`text-xs font-semibold px-2 py-0.5 rounded-full ${participantsWithRole.length > 0 ? 'bg-white text-slate-700' : 'bg-slate-200 text-slate-500'}`}>
                                            {participantsWithRole.length}
                                         </span>
                                     </div>
                                      {participantsWithRole.length > 0 ? (
                                         <ul className="text-xs space-y-1 text-slate-700">
                                             {participantsWithRole.map(p => (
                                                 <li key={p.id} className="flex items-center">
                                                     <span className={`w-1.5 h-1.5 ${roleColor.pill} rounded-full mr-1.5`}></span>{p.name}
                                                  </li>
                                             ))}
                                         </ul>
                                     ) : (
                                         <p className="text-xs text-slate-400 italic text-center pt-1">{t('context.visualization.noParticipant')}</p>
                                     )}
                                 </div>
                             );
                        })}
                    </div>
                 </div>
            ))}
        </div>
   );

   const renderChartView = () => (
       <div>
            {/* Overall Distribution Bar */}
            <div className="mb-8 p-4 bg-slate-100 rounded-lg border border-slate-200">
                 <h3 className="text-sm font-semibold text-slate-700 mb-2">
                    {t('context.visualization.globalDistribution')} ({totalAssignments})
                 </h3>
                 {totalAssignments > 0 ? (
                    <>
                        <div className="h-5 w-full bg-slate-200 rounded-full overflow-hidden flex shadow-inner">
                            {['R', 'A', 'C', 'I'].map(role => {
                                if (totalCounts[role] === 0) return null;
                                const roleColor = getRoleColor(role);
                                const percentage = totalAssignments > 0 ? (totalCounts[role] / totalAssignments) * 100 : 0;
                                return (
                                    <div
                                        key={role}
                                        className={`${roleColor.pill} h-full transition-all duration-300 ease-out`}
                                        style={{ width: `${percentage}%` }}
                                        title={`${roleColor.label}: ${totalCounts[role]} (${Math.round(percentage)}%)`}
                                    ></div>
                                );
                             })}
                         </div>
                         <div className="flex justify-around mt-2 text-xs text-slate-600">
                             {['R', 'A', 'C', 'I'].map(role => (
                                 <div key={role} className="flex items-center">
                                     <span className={`inline-block w-2.5 h-2.5 rounded-full ${getRoleColor(role).pill} mr-1`}></span>
                                     <span>{role}: {totalCounts[role]}</span>
                                 </div>
                             ))}
                         </div>
                    </>
                  ) : (
                    <p className="text-sm text-slate-500 text-center py-2">{t('context.visualization.noAssignments')}</p>
                  )}
            </div>

             {/* Per Participant Breakdown */}
             <div className="grid grid-cols-1 md:grid-cols-2 gap-5">
                 {filteredParticipants.map(participant => {
                    // Calculate role counts ONLY for the filtered workshops for this participant
                     const roleCounts = { 'R': 0, 'A': 0, 'C': 0, 'I': 0, '-': 0 };
                     let participantTotalAssignments = 0;
                      if (matrix[participant.id]) {
                         filteredWorkshops.forEach(workshop => {
                             const role = matrix[participant.id][workshop.id];
                             if (role && roleCounts[role] !== undefined) {
                                 roleCounts[role]++;
                                 if (role !== '-' && role !== '') participantTotalAssignments++;
                             }
                         });
                     }

                     // Skip rendering if a role is filtered and this participant doesn't have it
                      if (filteredRole && roleCounts[filteredRole] === 0) return null;

                     return (
                          <div key={participant.id} className="border border-slate-200 rounded-xl shadow-sm overflow-hidden bg-white">
                             <div className="bg-slate-50 p-3 border-b border-slate-200 flex justify-between items-center">
                                 <h3 className="font-semibold text-base text-slate-800 truncate">{participant.name || 'N/A'}</h3>
                                 <span className="text-xs text-slate-500 shrink-0 ml-2">{participant.position === "OTHER" ? (participant.customPosition || 'Autre') : (participant.position || 'N/D')}</span>
                             </div>
                              <div className="p-4 space-y-4">
                                  {participantTotalAssignments > 0 ? (
                                     ['R', 'A', 'C', 'I'].map(role => {
                                          // Skip rendering if a different role is filtered
                                         if (filteredRole && filteredRole !== role) return null;
                                         if (roleCounts[role] === 0 && filteredRole !== role) return null; // Hide roles with 0 count unless it's the filtered one

                                         const roleColor = getRoleColor(role);
                                          const percentage = participantTotalAssignments > 0 ? (roleCounts[role] / participantTotalAssignments) * 100 : 0;
                                          const workshopsWithRole = filteredWorkshops.filter(w => matrix[participant.id]?.[w.id] === role);

                                         return (
                                             <div key={role}>
                                                 <div className="flex items-center justify-between mb-1">
                                                     <div className="flex items-center">
                                                          <span className={`inline-flex items-center justify-center w-6 h-6 rounded-full ${roleColor.pill} text-white font-bold text-xs mr-2 shadow-sm`}>{role}</span>
                                                         <span className="text-sm font-medium text-slate-700">{roleColor.label}</span>
                                                     </div>
                                                     <span className="text-xs font-semibold text-slate-600">
                                                         {roleCounts[role]} ({Math.round(percentage)}%)
                                                     </span>
                                                 </div>
                                                 <div className="h-2 w-full bg-slate-200 rounded-full overflow-hidden shadow-inner">
                                                     <div
                                                         className={`${roleColor.pill} h-full transition-all duration-300 ease-out`}
                                                         style={{ width: `${percentage}%` }}
                                                      ></div>
                                                 </div>
                                                  {workshopsWithRole.length > 0 && percentage > 0 && (
                                                     <div className="mt-1.5 flex flex-wrap gap-1">
                                                         {workshopsWithRole.map(w => (
                                                             <span key={w.id} className="inline-flex items-center px-1.5 py-0.5 rounded-full bg-slate-100 text-slate-700 text-[10px] font-medium border border-slate-200">
                                                                 {t(`context.table.workshop${w.id.slice(-1)}`) || w.name}
                                                             </span>
                                                         ))}
                                                     </div>
                                                 )}
                                             </div>
                                         );
                                     })
                                  ) : (
                                    <p className="text-sm text-slate-500 text-center py-4 italic">{t('context.visualization.noAssignmentForParticipant')}</p>
                                  )}
                              </div>
                         </div>
                     );
                 })}
            </div>
       </div>
   );


  // --- Main Render ---

  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.4, delay: 0.2 }} // Delay visualization load slightly
      className="bg-white rounded-xl shadow-lg border border-slate-100 overflow-hidden"
    >
        {/* Header & View Selector */}
        <div className="p-4 border-b border-slate-200 bg-slate-50/50">
            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
                <h2 className="text-xl font-semibold text-slate-800 flex items-center">
                   <BarChart2 size={20} className="mr-2 text-blue-600" />
                   {t('context.sections.visualization')}
                 </h2>
                 <div className="hidden sm:block px-3 py-1 bg-blue-50 text-blue-700 rounded-full text-xs font-medium">
                                  {t('context.steps.step3')}
                                </div>
                <div className="flex bg-slate-200 p-1 rounded-lg shadow-inner">
                    {viewOptions.map(option => (
                        <button
                            key={option.id}
                            onClick={() => setView(option.id)}
                            className={`px-3 py-1.5 rounded-md text-sm font-medium flex items-center transition-all duration-200 ${
                                view === option.id
                                 ? 'bg-white text-blue-700 shadow'
                                 : 'bg-transparent text-slate-600 hover:bg-slate-300/50'
                            }`}
                            aria-pressed={view === option.id}
                         >
                            <option.icon size={16} className="mr-1.5" />
                            {t(`context.visualization.${option.labelKey}`)}
                         </button>
                    ))}
                </div>
            </div>
        </div>

        {/* Filters */}
        {renderFilters()}

        {/* Content Area */}
        <div className="p-4 md:p-6">
            {hasDataToShow ? (
                <>
                    {view === 'matrix' && renderMatrixView()}
                    {view === 'heatmap' && renderHeatmapView()}
                    {view === 'chart' && renderChartView()}
                </>
            ) : (
                <div className="text-center py-12 px-6">
                    <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-slate-100 text-slate-500 mb-5 border border-slate-200 shadow-sm">
                         <Filter size={32} />
                    </div>
                    <p className="text-slate-700 font-semibold text-lg mb-1">{t('context.visualization.noResults')}</p>
                     <p className="text-slate-500 text-sm">
                        {t('context.visualization.adjustFilters')}
                     </p>
                </div>
            )}
        </div>

        {/* Footer Info removed as requested */}
    </motion.div>
  );
};

export default RACIVisualization;