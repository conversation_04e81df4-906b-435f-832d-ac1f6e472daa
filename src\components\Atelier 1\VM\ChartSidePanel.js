// ChartSidePanel.js
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { SECURITY_PILLARS } from '../../../constants';

// Simple Collapsible Section Component
const CollapsibleSection = ({ title, children, initiallyOpen = false }) => {
  const [isOpen, setIsOpen] = useState(initiallyOpen);

  return (
    <div className="border rounded-lg bg-gray-50 overflow-hidden">
      <button
        className="w-full flex justify-between items-center p-4 text-left font-semibold text-gray-700 hover:bg-gray-100 focus:outline-none"
        onClick={() => setIsOpen(!isOpen)}
      >
        {title}
        <svg
          xmlns="http://www.w3.org/2000/svg"
          className={`h-5 w-5 transition-transform duration-200 ${isOpen ? 'rotate-180' : ''}`}
          viewBox="0 0 20 20"
          fill="currentColor"
        >
          <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
        </svg>
      </button>
      {isOpen && (
        <div className="p-4 border-t border-gray-200">
          {children}
        </div>
      )}
    </div>
  );
};

const ChartSidePanel = ({
  businessValues,
  selectedValueForChart,
  setSelectedValueForChart,
  useShortIds,
  setUseShortIds,
  handleToggleSecurityPillar,
  setExpandedValue,
  setViewMode,
  handleRemoveSupportAsset // Nouvelle fonction pour supprimer un bien support
}) => {
  const { t } = useTranslation();
  // Rechercher la valeur métier sélectionnée
  const selectedBusinessValue = businessValues.find(value => value.id === selectedValueForChart);

  return (
    <div className="lg:w-1/3 space-y-4">
      <div className="mb-2">
        <label className="block text-sm font-medium text-gray-700 mb-2">
          {t('businessValues.chart.selectValue')}
        </label>
      </div>

      <div className="mb-4 flex items-center">
        <label className="flex items-center cursor-pointer">
          <input
            type="checkbox"
            className="form-checkbox h-4 w-4 text-blue-600 transition duration-150 ease-in-out"
            checked={useShortIds}
            onChange={(e) => setUseShortIds(e.target.checked)}
          />
          <span className="ml-2 text-sm text-gray-700">{t('businessValues.chart.useShortIds')}</span>
        </label>
      </div>

      {selectedBusinessValue && (
        <div className="space-y-4">
          {/* Piliers de sécurité Section - Now Collapsible */}
          <CollapsibleSection title={t('businessValues.chart.securityPillars')} initiallyOpen={true}>
            <div className="grid grid-cols-1 gap-2">
              {SECURITY_PILLARS.map(pillar => {
                const isSelected = (selectedBusinessValue.securityPillars || []).includes(pillar.id);
                return (
                  <div
                    key={pillar.id}
                    className={`p-2 rounded-md flex items-center ${isSelected ? 'border-l-4' : 'opacity-50 border-l'}`}
                    style={{ borderLeftColor: pillar.color }}
                  >
                    <div
                      className="w-3 h-3 rounded-full mr-2"
                      style={{ backgroundColor: pillar.color }}
                    ></div>
                    <span className={`${isSelected ? 'font-medium' : ''} text-sm`}>{pillar.name}</span>
                    <button
                      className="ml-auto text-sm text-blue-500 hover:text-blue-700"
                      onClick={() => handleToggleSecurityPillar(selectedBusinessValue.id, pillar.id)}
                    >
                      {isSelected ? t('businessValues.buttons.remove') : t('businessValues.buttons.addValue')}
                    </button>
                  </div>
                );
              })}
            </div>
          </CollapsibleSection>

          {/* Biens Supports Section - Now Collapsible */}
          <CollapsibleSection title={t('businessValues.chart.supportAssets')} initiallyOpen={true}>
            <div className="flex justify-end mb-3">
              <button
                className="text-xs bg-blue-500 text-white px-2 py-1 rounded hover:bg-blue-600"
                onClick={() => {
                  setExpandedValue(selectedBusinessValue.id);
                  setViewMode('list');
                }}
              >
                {t('businessValues.buttons.manage')}
              </button>
            </div>

            {(!selectedBusinessValue.supportAssets || selectedBusinessValue.supportAssets.length === 0) ? (
              <p className="text-gray-500 italic text-sm">{t('businessValues.empty.noAssetsChart')}</p>
            ) : (
              <div className="max-h-40 overflow-y-auto">
                <div className="grid grid-cols-1 gap-2">
                  {(selectedBusinessValue.supportAssets || []).map(supportAsset => (
                    <div key={supportAsset.id} className="p-2 bg-white border rounded-md text-sm flex items-center justify-between">
                      <div>
                        <span className="text-blue-600 mr-1 font-medium">{supportAsset.shortId}</span>
                        {supportAsset.name}
                      </div>
                      <button
                        className="text-gray-500 hover:text-red-500 transition-colors"
                        onClick={() => handleRemoveSupportAsset(selectedBusinessValue.id, supportAsset.id)}
                        title={t('businessValues.buttons.delete')}
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                        </svg>
                      </button>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </CollapsibleSection>

          {/* Légende Section - Now Collapsible */}
          <CollapsibleSection title={t('businessValues.sections.legend')}>
            <div className="space-y-2 text-sm">
              <div className="flex items-center">
                <div className="w-5 h-5 rounded-full bg-gray-200 flex items-center justify-center mr-2">
                  <span className="text-xs text-gray-600">SI</span>
                </div>
                <span>{t('businessValues.legend.informationSystem')}</span>
              </div>
              <div className="flex items-center">
                <div className="w-5 h-5 rounded-full border-2 border-gray-300 bg-white mr-2"></div>
                <span>{t('businessValues.legend.businessValue')}</span>
              </div>
              <div className="flex items-center">
                <div className="w-5 h-5 rounded-sm bg-gray-100 border border-gray-300 mr-2"></div>
                <span>{t('businessValues.legend.supportAsset')}</span>
              </div>
              <div className="flex items-center">
                <div className="w-5 h-5 rounded-full bg-blue-500 flex items-center justify-center text-white mr-2 text-xs">C</div>
                <span>{t('businessValues.legend.securityPillar')}</span>
              </div>
              <div className="flex items-center">
                <div className="w-5 border-t border-blue-400 mr-2"></div>
                <span>{t('businessValues.legend.studyPerimeter')}</span>
              </div>
            </div>
          </CollapsibleSection>
        </div>
      )}
    </div>
  );
};

export default ChartSidePanel;