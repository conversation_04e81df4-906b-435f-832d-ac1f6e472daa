// src/components/Atelier 3/Activite1/GuideModal.js
import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { X, Users, Target, Activity, Shield, AlertTriangle, BarChart2 } from 'lucide-react';

const GuideModal = ({ isOpen, onClose }) => {
  // Guide steps
  const steps = [
    {
      title: "Identifier les parties prenantes",
      description: "Identifiez les parties prenantes internes et externes qui interagissent avec l'objet de l'étude. Classez-les par catégorie (client, partenaire, prestataire, etc.) et par type (interne ou externe).",
      icon: <Users size={20} className="text-blue-500" />
    },
    {
      title: "Évaluer les critères d'exposition",
      description: "Pour chaque partie prenante, évaluez le niveau de dépendance (relation vitale pour l'activité) et de pénétration (accès aux ressources internes).",
      icon: <Target size={20} className="text-orange-500" />
    },
    {
      title: "Évaluer les critères de fiabilité cyber",
      description: "Évaluez la maturité cyber (capacités en matière de sécurité) et la confiance (intentions et intérêts) de chaque partie prenante.",
      icon: <Shield size={20} className="text-green-500" />
    },
    {
      title: "Calculer le niveau de menace",
      description: "Le niveau de menace est calculé selon la formule : (Dépendance × Pénétration) / (Maturité cyber × Confiance). Plus le niveau est élevé, plus la partie prenante représente une menace.",
      icon: <AlertTriangle size={20} className="text-red-500" />
    },
    {
      title: "Interpréter la cartographie",
      description: "Sur la visualisation radar, les parties prenantes sont positionnées selon leur niveau de menace. Plus elles sont proches du centre, plus elles représentent une menace élevée.",
      icon: <BarChart2 size={20} className="text-purple-500" />
    },
    {
      title: "Définir les seuils",
      description: "Ajustez les seuils de danger, contrôle et veille pour délimiter les zones de menace selon votre contexte et votre tolérance au risque.",
      icon: <Activity size={20} className="text-indigo-500" />
    }
  ];

  return (
    <AnimatePresence>
      {isOpen && (
        <>
          {/* Backdrop */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black bg-opacity-50 z-40 backdrop-blur-sm"
            onClick={onClose}
          />
          
          {/* Modal */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: 20 }}
            className="fixed inset-0 z-50 overflow-y-auto"
          >
            <div className="flex items-center justify-center min-h-screen p-4">
              <div className="bg-white rounded-xl shadow-xl max-w-3xl w-full max-h-[90vh] overflow-hidden">
                {/* Header */}
                <div className="p-6 bg-gradient-to-r from-blue-600 to-indigo-700 flex justify-between items-center">
                  <div className="flex items-center text-white">
                    <Users size={24} className="mr-3" />
                    <h2 className="text-xl font-bold">Guide: Cartographier l'écosystème</h2>
                  </div>
                  <button
                    onClick={onClose}
                    className="text-white hover:bg-white hover:bg-opacity-20 rounded-full p-2 transition-colors"
                  >
                    <X size={20} />
                  </button>
                </div>
                
                {/* Content */}
                <div className="p-6 overflow-y-auto max-h-[calc(90vh-80px)]">
                  <div className="prose max-w-none">
                    <p className="text-gray-600 mb-6">
                      Cette activité vous permet de cartographier l'écosystème de parties prenantes et d'évaluer le niveau de menace qu'elles représentent pour l'objet de l'étude.
                    </p>
                    
                    <h3 className="text-lg font-medium text-gray-900 mb-4">Étapes à suivre</h3>
                    
                    <div className="space-y-6">
                      {steps.map((step, index) => (
                        <div key={index} className="flex">
                          <div className="flex-shrink-0 mt-1">
                            <div className="flex items-center justify-center w-8 h-8 rounded-full bg-blue-100">
                              {step.icon}
                            </div>
                          </div>
                          <div className="ml-4">
                            <h4 className="text-base font-medium text-gray-900">{step.title}</h4>
                            <p className="mt-1 text-sm text-gray-600">{step.description}</p>
                          </div>
                        </div>
                      ))}
                    </div>
                    
                    <h3 className="text-lg font-medium text-gray-900 mt-8 mb-4">Zones de menace</h3>
                    
                    <div className="space-y-4">
                      <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
                        <h4 className="text-base font-medium text-red-700">Zone de danger</h4>
                        <p className="mt-1 text-sm text-red-600">
                          Zone pour laquelle le niveau de menace est considéré comme très élevé et difficilement acceptable. Aucune partie prenante ne devrait se situer dans cette zone. Des mesures de sécurité doivent être prises pour faire sortir de cette zone les parties prenantes qui s'y trouvent.
                        </p>
                      </div>
                      
                      <div className="p-4 bg-orange-50 border border-orange-200 rounded-lg">
                        <h4 className="text-base font-medium text-orange-700">Zone de contrôle</h4>
                        <p className="mt-1 text-sm text-orange-600">
                          Zone pour laquelle le niveau de menace est considéré comme tolérable sous contrôle. Les parties prenantes qui s'y trouvent doivent faire l'objet d'une vigilance particulière et ont vocation, à moyen terme, à rejoindre une position moins menaçante au travers de mesures de réduction du risque.
                        </p>
                      </div>
                      
                      <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
                        <h4 className="text-base font-medium text-green-700">Zone de veille</h4>
                        <p className="mt-1 text-sm text-green-600">
                          Zone pour laquelle le niveau de menace est considéré comme faible et acceptable en l'état. Les parties prenantes qui s'y trouvent peuvent faire l'objet d'une veille sans être prises en compte dans l'élaboration des scénarios stratégiques.
                        </p>
                      </div>
                    </div>
                    
                    <h3 className="text-lg font-medium text-gray-900 mt-8 mb-4">Critères d'évaluation</h3>
                    
                    <div className="overflow-x-auto">
                      <table className="min-w-full border border-gray-200 text-sm">
                        <thead>
                          <tr className="bg-gray-50">
                            <th className="py-2 px-3 border-b border-r text-left">Niveau</th>
                            <th className="py-2 px-3 border-b border-r text-left">Dépendance</th>
                            <th className="py-2 px-3 border-b border-r text-left">Pénétration</th>
                            <th className="py-2 px-3 border-b border-r text-left">Maturité cyber</th>
                            <th className="py-2 px-3 border-b text-left">Confiance</th>
                          </tr>
                        </thead>
                        <tbody>
                          <tr>
                            <td className="py-2 px-3 border-b border-r font-medium">1</td>
                            <td className="py-2 px-3 border-b border-r">Relation non nécessaire aux fonctions stratégiques</td>
                            <td className="py-2 px-3 border-b border-r">Pas d'accès ou accès utilisateur à des terminaux</td>
                            <td className="py-2 px-3 border-b border-r">Règles d'hygiène ponctuelles et non formalisées</td>
                            <td className="py-2 px-3 border-b">Intentions non évaluables</td>
                          </tr>
                          <tr>
                            <td className="py-2 px-3 border-b border-r font-medium">2</td>
                            <td className="py-2 px-3 border-b border-r">Relation utile aux fonctions stratégiques</td>
                            <td className="py-2 px-3 border-b border-r">Accès administrateur à des terminaux ou accès physique aux sites</td>
                            <td className="py-2 px-3 border-b border-r">Règles d'hygiène prises en compte sans politique globale</td>
                            <td className="py-2 px-3 border-b">Intentions considérées comme neutres</td>
                          </tr>
                          <tr>
                            <td className="py-2 px-3 border-b border-r font-medium">3</td>
                            <td className="py-2 px-3 border-b border-r">Relation indispensable mais non exclusive</td>
                            <td className="py-2 px-3 border-b border-r">Accès administrateur à des serveurs métier</td>
                            <td className="py-2 px-3 border-b border-r">Politique globale appliquée en mode réactif</td>
                            <td className="py-2 px-3 border-b">Intentions connues et probablement positives</td>
                          </tr>
                          <tr>
                            <td className="py-2 px-3 border-r font-medium">4</td>
                            <td className="py-2 px-3 border-r">Relation indispensable et unique</td>
                            <td className="py-2 px-3 border-r">Accès administrateur à l'infrastructure ou aux salles serveurs</td>
                            <td className="py-2 px-3 border-r">Politique de management du risque proactive</td>
                            <td className="py-2 px-3">Intentions parfaitement connues et compatibles</td>
                          </tr>
                        </tbody>
                      </table>
                    </div>
                  </div>
                </div>
                
                {/* Footer */}
                <div className="p-4 bg-gray-50 border-t border-gray-200 flex justify-end">
                  <button
                    onClick={onClose}
                    className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
                  >
                    Fermer
                  </button>
                </div>
              </div>
            </div>
          </motion.div>
        </>
      )}
    </AnimatePresence>
  );
};

export default GuideModal;
