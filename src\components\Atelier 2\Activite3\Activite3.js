// src/components/Atelier 2/Activite3/Activite3.js
import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Shield, ArrowRight, Table, Save, RefreshCw, Info } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import SourceRiskDreadedEventMapping from './SourceRiskDreadedEventMapping';
import GuideModal from './GuideModal';

const Activite3 = () => {
  const { t } = useTranslation();
  const [showSummaryTable, setShowSummaryTable] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [isGuideOpen, setIsGuideOpen] = useState(false);

  // Function to pass to child component
  const updateShowSummaryTable = (value) => {
    setShowSummaryTable(value);
  };

  // Function to pass to child component
  const updateIsSaving = (value) => {
    setIsSaving(value);
  };

  return (
    <div className="space-y-6">
      {/* Modern Header with breadcrumb */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-white p-6 rounded-xl shadow-md border border-gray-100"
      >
        <div className="flex items-center text-sm text-gray-500 mb-2">
          <span>{t('workshop2.title')}</span>
          <ArrowRight size={14} className="mx-2" />
          <span className="font-medium text-blue-600">{t('workshop2.activity3.breadcrumb')}</span>
        </div>
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <div className="bg-blue-100 p-2 rounded-lg mr-4">
              <Shield size={24} className="text-blue-600" />
            </div>
            <div>
              <h1 className="text-2xl font-bold text-gray-800">{t('workshop2.activity3.title')}</h1>
              <p className="text-gray-600 mt-1">
                {t('workshop2.activity3.subtitle')}
              </p>
            </div>
          </div>
          <div className="flex items-center space-x-3">
            {/* Toggle Summary Table button */}
            <button
              onClick={() => setShowSummaryTable(!showSummaryTable)}
              className={`${
                showSummaryTable
                  ? 'bg-blue-600 text-white hover:bg-blue-700'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              } px-4 py-2 rounded-lg flex items-center transition-all duration-200`}
            >
              <Table size={18} className="mr-2" />
              <span className="font-medium">
                {showSummaryTable ? t('workshop2.activity3.hideTable') : t('workshop2.activity3.showTable')}
              </span>
            </button>

            {/* Save button - will be controlled by child component */}
            <button
              id="save-mappings-button"
              disabled={isSaving}
              className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center disabled:opacity-70 transition-all duration-200"
            >
              {isSaving ? (
                <RefreshCw size={18} className="mr-2 animate-spin" />
              ) : (
                <Save size={18} className="mr-2" />
              )}
              <span className="font-medium">{t('common.save')}</span>
            </button>

            {/* Help Button */}
            <button
              className="text-sm font-medium bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 flex items-center shadow-sm transition duration-200"
              onClick={() => setIsGuideOpen(true)}
            >
              <Info size={16} className="mr-2" />
              {t('common.help')}
            </button>
          </div>
        </div>
      </motion.div>

      {/* Content */}
      <div className="bg-transparent overflow-hidden">
        <SourceRiskDreadedEventMapping
          showSummaryTable={showSummaryTable}
          setShowSummaryTable={updateShowSummaryTable}
          isSaving={isSaving}
          setIsSaving={updateIsSaving}
          saveButtonId="save-mappings-button"
        />
      </div>

      {/* Guide Modal */}
      <GuideModal isOpen={isGuideOpen} onClose={() => setIsGuideOpen(false)} />
    </div>
  );
};

export default Activite3;
