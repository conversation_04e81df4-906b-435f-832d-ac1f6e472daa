// src/components/Atelier4/Synthesis.js
import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  FileText,
  Info,
  Download,
  BarChart3,
  Shield,
  Target,
  Database,
  Brain,
  Users,
  Calendar,
  CheckCircle,
  AlertTriangle,
  TrendingUp,
  Award,
  Share2,
  MessageSquare,
  Clock,
  Star
} from 'lucide-react';
import { useAnalysis } from '../../context/AnalysisContext';

const Synthesis = () => {
  const [synthesisData, setSynthesisData] = useState(null);
  const [loading, setLoading] = useState(false);
  const { currentAnalysis } = useAnalysis();

  // Load synthesis data
  useEffect(() => {
    const loadSynthesisData = async () => {
      if (currentAnalysis?.id) {
        setLoading(true);
        try {
          console.log('[Synthesis] Loading synthesis data for analysis:', currentAnalysis.id);

          // Mock synthesis data
          const mockSynthesis = {
            threatIntelligence: {
              totalAssets: 3,
              totalCVEs: 5,
              totalTechniques: 8,
              highSeverityVulns: 3,
              criticalSeverityVulns: 1
            },
            operationalScenarios: {
              totalScenarios: 4,
              highRiskScenarios: 2,
              mediumRiskScenarios: 1,
              lowRiskScenarios: 1,
              aiGeneratedScenarios: 2
            },
            attackGraphs: {
              totalPaths: 2,
              avgStepsPerPath: 4,
              mostCommonPhase: 'RENTRER',
              complexityScore: 'Moyen'
            },
            riskTreatment: {
              totalDecisions: 4,
              acceptDecisions: 1,
              reduceDecisions: 2,
              transferDecisions: 0,
              avoidDecisions: 1,
              pendingDecisions: 0
            },
            recommendations: [
              {
                priority: 'Critique',
                title: 'Mise à jour urgente des systèmes',
                description: 'Appliquer les correctifs pour les CVE critiques identifiés',
                category: 'Technique'
              },
              {
                priority: 'Élevée',
                title: 'Formation anti-phishing',
                description: 'Sensibiliser les employés aux techniques de phishing',
                category: 'Humain'
              },
              {
                priority: 'Moyenne',
                title: 'Surveillance renforcée',
                description: 'Déployer des outils de détection avancés',
                category: 'Organisationnel'
              }
            ]
          };

          setSynthesisData(mockSynthesis);
        } catch (error) {
          console.error('[Synthesis] Error loading synthesis data:', error);
        } finally {
          setLoading(false);
        }
      }
    };

    loadSynthesisData();
  }, [currentAnalysis?.id]);

  // Generate PDF report
  const handleGenerateReport = () => {
    console.log('[Synthesis] Generating PDF report...');
    // This would integrate with a PDF generation service
    alert('Fonctionnalité de génération PDF en cours de développement');
  };

  // Export data
  const handleExportData = () => {
    console.log('[Synthesis] Exporting data...');
    // This would export the analysis data
    alert('Fonctionnalité d\'export en cours de développement');
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Génération de la synthèse...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Modern Header */}
      <div className="relative overflow-hidden bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 rounded-2xl shadow-lg border border-gray-200">
        {/* Background Pattern */}
        <div className="absolute inset-0 bg-grid-pattern opacity-5"></div>

        <div className="relative p-8">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between mb-6">
            <div className="mb-6 lg:mb-0">
              <div className="flex items-center mb-4">
                <div className="w-12 h-12 bg-gradient-to-br from-slate-600 to-slate-800 rounded-xl flex items-center justify-center mr-4 shadow-lg">
                  <FileText size={24} className="text-white" />
                </div>
                <div>
                  <h1 className="text-3xl font-bold text-gray-900 mb-1">
                    Synthèse & Documentation
                  </h1>
                  <p className="text-slate-600 font-medium">Phase 6: Collaboration et Rapport Final</p>
                </div>
              </div>
              <p className="text-gray-700 max-w-2xl leading-relaxed">
                Synthèse complète de l'analyse EBIOS RM avec recommandations stratégiques,
                collaboration des parties prenantes et documentation pour le suivi.
              </p>
            </div>

            {/* Action Buttons */}
            <div className="flex flex-col sm:flex-row gap-3">
              <button
                onClick={handleExportData}
                className="group flex items-center px-6 py-3 bg-white text-gray-700 rounded-xl hover:bg-gray-50 transition-all duration-200 shadow-md hover:shadow-lg border border-gray-200"
              >
                <Download size={18} className="mr-2 group-hover:scale-110 transition-transform" />
                <span className="font-medium">Exporter Données</span>
              </button>
              <button
                onClick={handleGenerateReport}
                className="group flex items-center px-6 py-3 bg-gradient-to-r from-slate-600 to-slate-700 text-white rounded-xl hover:from-slate-700 hover:to-slate-800 transition-all duration-200 shadow-md hover:shadow-lg"
              >
                <FileText size={18} className="mr-2 group-hover:scale-110 transition-transform" />
                <span className="font-medium">Rapport PDF</span>
              </button>
              <button
                className="group flex items-center px-6 py-3 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-xl hover:from-blue-700 hover:to-indigo-700 transition-all duration-200 shadow-md hover:shadow-lg"
              >
                <Share2 size={18} className="mr-2 group-hover:scale-110 transition-transform" />
                <span className="font-medium">Partager</span>
              </button>
            </div>
          </div>

          {/* Enhanced Analysis Overview */}
          <div className="bg-white/70 backdrop-blur-sm p-6 rounded-xl border border-white/50 shadow-sm">
            <div className="flex items-center mb-4">
              <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-lg flex items-center justify-center mr-3">
                <Info size={16} className="text-white" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900">Vue d'ensemble de l'analyse</h3>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <div className="group bg-white p-4 rounded-xl border border-gray-100 hover:border-blue-200 hover:shadow-md transition-all duration-200">
                <div className="flex items-center justify-between mb-2">
                  <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                    <Target size={16} className="text-blue-600" />
                  </div>
                  <div className="text-xs text-gray-500 font-medium">ANALYSE</div>
                </div>
                <div className="font-semibold text-gray-900 mb-1">{currentAnalysis?.name || 'Analyse EBIOS RM'}</div>
                <div className="text-sm text-gray-600">Analyse principale</div>
              </div>

              <div className="group bg-white p-4 rounded-xl border border-gray-100 hover:border-green-200 hover:shadow-md transition-all duration-200">
                <div className="flex items-center justify-between mb-2">
                  <div className="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                    <Calendar size={16} className="text-green-600" />
                  </div>
                  <div className="text-xs text-gray-500 font-medium">DATE</div>
                </div>
                <div className="font-semibold text-gray-900 mb-1">{new Date().toLocaleDateString('fr-FR')}</div>
                <div className="text-sm text-gray-600">Dernière mise à jour</div>
              </div>

              <div className="group bg-white p-4 rounded-xl border border-gray-100 hover:border-purple-200 hover:shadow-md transition-all duration-200">
                <div className="flex items-center justify-between mb-2">
                  <div className="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                    <BarChart3 size={16} className="text-purple-600" />
                  </div>
                  <div className="text-xs text-gray-500 font-medium">PHASES</div>
                </div>
                <div className="font-semibold text-gray-900 mb-1">6 phases</div>
                <div className="text-sm text-gray-600">Complétées</div>
              </div>

              <div className="group bg-white p-4 rounded-xl border border-gray-100 hover:border-emerald-200 hover:shadow-md transition-all duration-200">
                <div className="flex items-center justify-between mb-2">
                  <div className="w-8 h-8 bg-emerald-100 rounded-lg flex items-center justify-center">
                    <CheckCircle size={16} className="text-emerald-600" />
                  </div>
                  <div className="text-xs text-gray-500 font-medium">STATUT</div>
                </div>
                <div className="font-semibold text-emerald-600 mb-1">Terminé</div>
                <div className="text-sm text-gray-600">Prêt pour rapport</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Enhanced Key Metrics */}
      {synthesisData && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6"
        >
          {/* Threat Intelligence Metrics */}
          <div className="group relative overflow-hidden bg-gradient-to-br from-blue-50 to-indigo-50 p-6 rounded-2xl shadow-lg border border-blue-100 hover:shadow-xl transition-all duration-300 hover:scale-[1.02]">
            <div className="absolute top-0 right-0 w-20 h-20 bg-gradient-to-br from-blue-400/20 to-indigo-500/20 rounded-full -mr-10 -mt-10"></div>
            <div className="relative">
              <div className="flex items-center justify-between mb-4">
                <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center shadow-lg">
                  <Database size={20} className="text-white" />
                </div>
                <div className="text-xs font-semibold text-blue-600 bg-blue-100 px-2 py-1 rounded-full">CTI</div>
              </div>
              <h3 className="font-bold text-gray-900 mb-4 text-lg">Intelligence des Menaces</h3>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-gray-700 font-medium">Actifs analysés</span>
                  <span className="font-bold text-blue-600 text-lg">{synthesisData.threatIntelligence.totalAssets}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-gray-700 font-medium">CVE trouvés</span>
                  <span className="font-bold text-gray-900 text-lg">{synthesisData.threatIntelligence.totalCVEs}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-gray-700 font-medium">Techniques ATT&CK</span>
                  <span className="font-bold text-gray-900 text-lg">{synthesisData.threatIntelligence.totalTechniques}</span>
                </div>
                <div className="flex items-center justify-between pt-2 border-t border-blue-200">
                  <span className="text-red-600 font-medium flex items-center">
                    <AlertTriangle size={14} className="mr-1" />
                    CVE critiques
                  </span>
                  <span className="font-bold text-red-600 text-lg">{synthesisData.threatIntelligence.criticalSeverityVulns}</span>
                </div>
              </div>
            </div>
          </div>

          {/* Operational Scenarios Metrics */}
          <div className="group relative overflow-hidden bg-gradient-to-br from-green-50 to-emerald-50 p-6 rounded-2xl shadow-lg border border-green-100 hover:shadow-xl transition-all duration-300 hover:scale-[1.02]">
            <div className="absolute top-0 right-0 w-20 h-20 bg-gradient-to-br from-green-400/20 to-emerald-500/20 rounded-full -mr-10 -mt-10"></div>
            <div className="relative">
              <div className="flex items-center justify-between mb-4">
                <div className="w-12 h-12 bg-gradient-to-br from-green-500 to-emerald-600 rounded-xl flex items-center justify-center shadow-lg">
                  <Target size={20} className="text-white" />
                </div>
                <div className="text-xs font-semibold text-green-600 bg-green-100 px-2 py-1 rounded-full">SCÉNARIOS</div>
              </div>
              <h3 className="font-bold text-gray-900 mb-4 text-lg">Scénarios Opérationnels</h3>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-gray-700 font-medium">Total scénarios</span>
                  <span className="font-bold text-green-600 text-lg">{synthesisData.operationalScenarios.totalScenarios}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-red-600 font-medium">Risque élevé</span>
                  <span className="font-bold text-red-600 text-lg">{synthesisData.operationalScenarios.highRiskScenarios}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-orange-600 font-medium">Risque moyen</span>
                  <span className="font-bold text-orange-600 text-lg">{synthesisData.operationalScenarios.mediumRiskScenarios}</span>
                </div>
                <div className="flex items-center justify-between pt-2 border-t border-green-200">
                  <span className="text-purple-600 font-medium flex items-center">
                    <Brain size={14} className="mr-1" />
                    Générés par IA
                  </span>
                  <span className="font-bold text-purple-600 text-lg">{synthesisData.operationalScenarios.aiGeneratedScenarios}</span>
                </div>
              </div>
            </div>
          </div>

          {/* Attack Graphs Metrics */}
          <div className="group relative overflow-hidden bg-gradient-to-br from-purple-50 to-violet-50 p-6 rounded-2xl shadow-lg border border-purple-100 hover:shadow-xl transition-all duration-300 hover:scale-[1.02]">
            <div className="absolute top-0 right-0 w-20 h-20 bg-gradient-to-br from-purple-400/20 to-violet-500/20 rounded-full -mr-10 -mt-10"></div>
            <div className="relative">
              <div className="flex items-center justify-between mb-4">
                <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-violet-600 rounded-xl flex items-center justify-center shadow-lg">
                  <BarChart3 size={20} className="text-white" />
                </div>
                <div className="text-xs font-semibold text-purple-600 bg-purple-100 px-2 py-1 rounded-full">GRAPHES</div>
              </div>
              <h3 className="font-bold text-gray-900 mb-4 text-lg">Graphes d'Attaque</h3>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-gray-700 font-medium">Chemins d'attaque</span>
                  <span className="font-bold text-purple-600 text-lg">{synthesisData.attackGraphs.totalPaths}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-gray-700 font-medium">Étapes moyennes</span>
                  <span className="font-bold text-gray-900 text-lg">{synthesisData.attackGraphs.avgStepsPerPath}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-gray-700 font-medium">Phase critique</span>
                  <span className="font-bold text-gray-900 text-sm bg-gray-100 px-2 py-1 rounded">{synthesisData.attackGraphs.mostCommonPhase}</span>
                </div>
                <div className="flex items-center justify-between pt-2 border-t border-purple-200">
                  <span className="text-gray-700 font-medium flex items-center">
                    <TrendingUp size={14} className="mr-1" />
                    Complexité
                  </span>
                  <span className="font-bold text-purple-600 text-lg">{synthesisData.attackGraphs.complexityScore}</span>
                </div>
              </div>
            </div>
          </div>

          {/* Risk Treatment Metrics */}
          <div className="group relative overflow-hidden bg-gradient-to-br from-emerald-50 to-teal-50 p-6 rounded-2xl shadow-lg border border-emerald-100 hover:shadow-xl transition-all duration-300 hover:scale-[1.02]">
            <div className="absolute top-0 right-0 w-20 h-20 bg-gradient-to-br from-emerald-400/20 to-teal-500/20 rounded-full -mr-10 -mt-10"></div>
            <div className="relative">
              <div className="flex items-center justify-between mb-4">
                <div className="w-12 h-12 bg-gradient-to-br from-emerald-500 to-teal-600 rounded-xl flex items-center justify-center shadow-lg">
                  <Shield size={20} className="text-white" />
                </div>
                <div className="text-xs font-semibold text-emerald-600 bg-emerald-100 px-2 py-1 rounded-full">TRAITEMENT</div>
              </div>
              <h3 className="font-bold text-gray-900 mb-4 text-lg">Traitement des Risques</h3>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-green-600 font-medium">Accepter</span>
                  <span className="font-bold text-green-600 text-lg">{synthesisData.riskTreatment.acceptDecisions}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-blue-600 font-medium">Réduire</span>
                  <span className="font-bold text-blue-600 text-lg">{synthesisData.riskTreatment.reduceDecisions}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-orange-600 font-medium">Transférer</span>
                  <span className="font-bold text-orange-600 text-lg">{synthesisData.riskTreatment.transferDecisions}</span>
                </div>
                <div className="flex items-center justify-between pt-2 border-t border-emerald-200">
                  <span className="text-red-600 font-medium flex items-center">
                    <AlertTriangle size={14} className="mr-1" />
                    Éviter
                  </span>
                  <span className="font-bold text-red-600 text-lg">{synthesisData.riskTreatment.avoidDecisions}</span>
                </div>
              </div>
            </div>
          </div>
        </motion.div>
      )}

      {/* Stakeholder Collaboration Section */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-gradient-to-br from-indigo-50 to-blue-50 p-8 rounded-2xl shadow-lg border border-indigo-100"
      >
        <div className="flex items-center mb-6">
          <div className="w-12 h-12 bg-gradient-to-br from-indigo-500 to-blue-600 rounded-xl flex items-center justify-center mr-4 shadow-lg">
            <Users size={24} className="text-white" />
          </div>
          <div>
            <h3 className="text-2xl font-bold text-gray-900 mb-1">Collaboration des Parties Prenantes</h3>
            <p className="text-indigo-700">Coordination et communication pour la mise en œuvre</p>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Stakeholder Roles */}
          <div className="bg-white/70 backdrop-blur-sm p-6 rounded-xl border border-white/50">
            <div className="flex items-center mb-4">
              <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                <Award size={16} className="text-blue-600" />
              </div>
              <h4 className="font-semibold text-gray-900">Rôles & Responsabilités</h4>
            </div>
            <div className="space-y-3 text-sm">
              <div className="flex items-center justify-between">
                <span className="text-gray-700">RSSI</span>
                <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs font-medium">Pilotage</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-gray-700">DSI</span>
                <span className="bg-green-100 text-green-800 px-2 py-1 rounded text-xs font-medium">Technique</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-gray-700">DG</span>
                <span className="bg-purple-100 text-purple-800 px-2 py-1 rounded text-xs font-medium">Validation</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-gray-700">Équipes</span>
                <span className="bg-orange-100 text-orange-800 px-2 py-1 rounded text-xs font-medium">Exécution</span>
              </div>
            </div>
          </div>

          {/* Communication Timeline */}
          <div className="bg-white/70 backdrop-blur-sm p-6 rounded-xl border border-white/50">
            <div className="flex items-center mb-4">
              <div className="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center mr-3">
                <MessageSquare size={16} className="text-green-600" />
              </div>
              <h4 className="font-semibold text-gray-900">Communication</h4>
            </div>
            <div className="space-y-3 text-sm">
              <div className="flex items-start space-x-3">
                <div className="w-2 h-2 bg-green-500 rounded-full mt-2"></div>
                <div>
                  <div className="font-medium text-gray-900">Présentation Direction</div>
                  <div className="text-gray-600">Semaine 1</div>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <div className="w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
                <div>
                  <div className="font-medium text-gray-900">Formation Équipes</div>
                  <div className="text-gray-600">Semaine 2-3</div>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <div className="w-2 h-2 bg-orange-500 rounded-full mt-2"></div>
                <div>
                  <div className="font-medium text-gray-900">Suivi Mensuel</div>
                  <div className="text-gray-600">Continu</div>
                </div>
              </div>
            </div>
          </div>

          {/* Documentation Status */}
          <div className="bg-white/70 backdrop-blur-sm p-6 rounded-xl border border-white/50">
            <div className="flex items-center mb-4">
              <div className="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center mr-3">
                <FileText size={16} className="text-purple-600" />
              </div>
              <h4 className="font-semibold text-gray-900">Documentation</h4>
            </div>
            <div className="space-y-3 text-sm">
              <div className="flex items-center justify-between">
                <span className="text-gray-700">Rapport Exécutif</span>
                <CheckCircle size={16} className="text-green-500" />
              </div>
              <div className="flex items-center justify-between">
                <span className="text-gray-700">Plan d'Action</span>
                <CheckCircle size={16} className="text-green-500" />
              </div>
              <div className="flex items-center justify-between">
                <span className="text-gray-700">Procédures</span>
                <Clock size={16} className="text-orange-500" />
              </div>
              <div className="flex items-center justify-between">
                <span className="text-gray-700">Formation</span>
                <Clock size={16} className="text-orange-500" />
              </div>
            </div>
          </div>
        </div>
      </motion.div>

      {/* Enhanced Recommendations */}
      {synthesisData?.recommendations && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-white p-8 rounded-2xl shadow-lg border border-gray-100"
        >
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center">
              <div className="w-12 h-12 bg-gradient-to-br from-orange-500 to-red-600 rounded-xl flex items-center justify-center mr-4 shadow-lg">
                <Star size={24} className="text-white" />
              </div>
              <div>
                <h3 className="text-2xl font-bold text-gray-900 mb-1">Recommandations Prioritaires</h3>
                <p className="text-gray-600">Actions stratégiques pour la sécurisation</p>
              </div>
            </div>
            <div className="text-sm text-gray-500 bg-gray-100 px-3 py-1 rounded-full">
              {synthesisData.recommendations.length} recommandations
            </div>
          </div>

          <div className="grid gap-6">
            {synthesisData.recommendations.map((rec, index) => (
              <div key={index} className={`group relative overflow-hidden p-6 rounded-xl border-l-4 transition-all duration-200 hover:shadow-md ${
                rec.priority === 'Critique' ? 'bg-gradient-to-r from-red-50 to-red-25 border-red-400 hover:from-red-100' :
                rec.priority === 'Élevée' ? 'bg-gradient-to-r from-orange-50 to-orange-25 border-orange-400 hover:from-orange-100' :
                'bg-gradient-to-r from-yellow-50 to-yellow-25 border-yellow-400 hover:from-yellow-100'
              }`}>
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-start space-x-4">
                    <div className={`w-10 h-10 rounded-xl flex items-center justify-center ${
                      rec.priority === 'Critique' ? 'bg-red-100' :
                      rec.priority === 'Élevée' ? 'bg-orange-100' :
                      'bg-yellow-100'
                    }`}>
                      <span className="text-lg font-bold text-gray-700">{index + 1}</span>
                    </div>
                    <div className="flex-1">
                      <h4 className="text-lg font-bold text-gray-900 mb-2">{rec.title}</h4>
                      <p className="text-gray-700 leading-relaxed">{rec.description}</p>
                    </div>
                  </div>
                  <div className="flex flex-col items-end space-y-2">
                    <span className={`px-3 py-1 rounded-full text-xs font-bold ${
                      rec.priority === 'Critique' ? 'bg-red-500 text-white' :
                      rec.priority === 'Élevée' ? 'bg-orange-500 text-white' :
                      'bg-yellow-500 text-white'
                    }`}>
                      {rec.priority}
                    </span>
                    <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">{rec.category}</span>
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="flex items-center justify-between pt-4 border-t border-gray-200">
                  <div className="flex items-center space-x-2 text-sm text-gray-600">
                    <Clock size={14} />
                    <span>Délai: {rec.priority === 'Critique' ? 'Immédiat' : rec.priority === 'Élevée' ? '1 mois' : '3 mois'}</span>
                  </div>
                  <div className="flex space-x-2">
                    <button className="px-3 py-1 bg-blue-100 text-blue-700 rounded-lg text-xs font-medium hover:bg-blue-200 transition-colors">
                      Assigner
                    </button>
                    <button className="px-3 py-1 bg-green-100 text-green-700 rounded-lg text-xs font-medium hover:bg-green-200 transition-colors">
                      Planifier
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </motion.div>
      )}

      {/* Methodology Summary */}
      <div className="bg-gray-50 p-6 rounded-xl border border-gray-200">
        <h3 className="font-medium text-gray-800 mb-4">Résumé de la méthodologie EBIOS RM</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
          <div className="space-y-2">
            <div className="font-medium text-gray-700">Ateliers 1-3 (Stratégique)</div>
            <div className="text-gray-600">• Cadrage et périmètre</div>
            <div className="text-gray-600">• Sources de risque et objectifs visés</div>
            <div className="text-gray-600">• Scénarios stratégiques</div>
          </div>
          <div className="space-y-2">
            <div className="font-medium text-gray-700">Atelier 4 (Opérationnel)</div>
            <div className="text-gray-600">• Intelligence des menaces</div>
            <div className="text-gray-600">• Scénarios opérationnels détaillés</div>
            <div className="text-gray-600">• Traitement des risques</div>
          </div>
          <div className="space-y-2">
            <div className="font-medium text-gray-700">Atelier 5 (Suivi)</div>
            <div className="text-gray-600">• Plan de traitement</div>
            <div className="text-gray-600">• Indicateurs de suivi</div>
            <div className="text-gray-600">• Mise à jour continue</div>
          </div>
        </div>
      </div>

      {/* Enhanced Completion Status */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="relative overflow-hidden bg-gradient-to-br from-emerald-50 via-green-50 to-teal-50 p-8 rounded-2xl shadow-lg border border-emerald-200"
      >
        {/* Background Pattern */}
        <div className="absolute inset-0 bg-grid-pattern opacity-5"></div>

        <div className="relative">
          <div className="text-center mb-8">
            <div className="relative inline-block">
              <div className="w-20 h-20 bg-gradient-to-br from-emerald-500 to-green-600 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-xl">
                <CheckCircle size={40} className="text-white" />
              </div>
              <div className="absolute -top-2 -right-2 w-8 h-8 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-full flex items-center justify-center shadow-lg">
                <Star size={16} className="text-white" />
              </div>
            </div>

            <h3 className="text-3xl font-bold text-gray-900 mb-3">
              🎉 Analyse EBIOS RM Terminée avec Succès
            </h3>
            <p className="text-lg text-gray-700 max-w-2xl mx-auto leading-relaxed">
              Félicitations ! L'analyse des risques est complète et documentée.
              Votre organisation dispose maintenant d'une vision claire des menaces
              et d'un plan d'action structuré pour la sécurisation.
            </p>
          </div>

          {/* Next Steps */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <div className="bg-white/70 backdrop-blur-sm p-6 rounded-xl border border-white/50 text-center">
              <div className="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center mx-auto mb-4">
                <FileText size={20} className="text-blue-600" />
              </div>
              <h4 className="font-bold text-gray-900 mb-2">Générer le Rapport</h4>
              <p className="text-sm text-gray-600 mb-4">
                Créez le rapport exécutif pour la direction et les parties prenantes
              </p>
              <button className="w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium">
                Rapport PDF
              </button>
            </div>

            <div className="bg-white/70 backdrop-blur-sm p-6 rounded-xl border border-white/50 text-center">
              <div className="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center mx-auto mb-4">
                <Users size={20} className="text-green-600" />
              </div>
              <h4 className="font-bold text-gray-900 mb-2">Coordonner les Équipes</h4>
              <p className="text-sm text-gray-600 mb-4">
                Organisez la mise en œuvre avec les parties prenantes identifiées
              </p>
              <button className="w-full px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors font-medium">
                Planifier
              </button>
            </div>

            <div className="bg-white/70 backdrop-blur-sm p-6 rounded-xl border border-white/50 text-center">
              <div className="w-12 h-12 bg-purple-100 rounded-xl flex items-center justify-center mx-auto mb-4">
                <TrendingUp size={20} className="text-purple-600" />
              </div>
              <h4 className="font-bold text-gray-900 mb-2">Suivre les Progrès</h4>
              <p className="text-sm text-gray-600 mb-4">
                Mettez en place le suivi continu et les indicateurs de performance
              </p>
              <button className="w-full px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors font-medium">
                Tableau de Bord
              </button>
            </div>
          </div>

          {/* Achievement Summary */}
          <div className="bg-white/70 backdrop-blur-sm p-6 rounded-xl border border-white/50">
            <h4 className="font-bold text-gray-900 mb-4 text-center">🏆 Résultats de l'Analyse</h4>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
              <div>
                <div className="text-2xl font-bold text-emerald-600">6</div>
                <div className="text-sm text-gray-600">Phases Complétées</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-blue-600">100%</div>
                <div className="text-sm text-gray-600">Couverture EBIOS RM</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-purple-600">✓</div>
                <div className="text-sm text-gray-600">Conformité Méthodologique</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-orange-600">🚀</div>
                <div className="text-sm text-gray-600">Prêt pour Déploiement</div>
              </div>
            </div>
          </div>
        </div>
      </motion.div>
    </div>
  );
};

export default Synthesis;
