// src/components/Atelier3/Activite2/Activite2.js
import React, { useState, useEffect } from 'react';
import { Target } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { useAnalysis } from '../../../context/AnalysisContext';
import StrategicScenariosTable from './StrategicScenariosTable';
import { showLoadingToast, dismissToast, showErrorToast } from '../../../utils/toastUtils';
import securityControlsService from '../../../services/securityControlsService';
import attackPathsService from '../../../services/attackPathsService';

const Activite2 = () => {
  const { t } = useTranslation();
  // State for data
  const [mappings, setMappings] = useState([]);
  const [sourcesRisque, setSourcesRisque] = useState([]);
  const [dreadedEvents, setDreadedEvents] = useState([]);
  const [controlsData, setControlsData] = useState(null);
  const [stakeholders, setStakeholders] = useState([]);
  const [selectedStakeholders, setSelectedStakeholders] = useState({});
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);

  // Get analysis context
  const {
    currentAnalysis,
    getSourcesDeRisque,
    getSelectedCouplesSROV,
    currentDreadedEvents,
    getStakeholders,
    currentBusinessValues
  } = useAnalysis();

  // Load data on component mount
  useEffect(() => {
    const fetchData = async () => {
      if (!currentAnalysis?.id) {
        return;
      }

      setIsLoading(true);
      setError(null);
      const toastId = showLoadingToast(t('workshop3.activity2.loading'));

      try {
        // Load sources de risque
        const sourcesData = await getSourcesDeRisque(currentAnalysis.id);
        if (sourcesData) {
          setSourcesRisque(sourcesData);
        }

        // Load dreaded events
        if (currentDreadedEvents && currentDreadedEvents.length > 0) {
          setDreadedEvents(currentDreadedEvents);
        }

        // Load existing mappings
        const mappingsData = await getSelectedCouplesSROV(currentAnalysis.id);
        if (mappingsData) {
          setMappings(mappingsData);
        }

        // Load stakeholders
        try {
          const stakeholdersResponse = await getStakeholders(currentAnalysis.id);
          if (stakeholdersResponse && stakeholdersResponse.success && Array.isArray(stakeholdersResponse.data)) {
            // Filter to only include retained stakeholders
            setStakeholders(stakeholdersResponse.data.filter(s => s.retained === true));
          } else {
            console.log('Stakeholders data is not valid:', stakeholdersResponse);
            setStakeholders([]);
          }
        } catch (stakeholderError) {
          console.error('Error loading stakeholders:', stakeholderError);
          setStakeholders([]);
        }

        // Load security controls data
        try {
          const controlsResponse = await securityControlsService.getSecurityControls(currentAnalysis.id);
          if (controlsResponse) {
            setControlsData(controlsResponse);
          }
        } catch (controlsError) {
          console.error('Error loading security controls:', controlsError);
          // Don't fail the whole load if controls can't be loaded
          setControlsData({ planData: {} });
        }

        // Load attack paths data to get previously selected stakeholders
        try {
          const attackPathsResponse = await attackPathsService.getAttackPaths(currentAnalysis.id);
          if (attackPathsResponse.success && attackPathsResponse.data && Array.isArray(attackPathsResponse.data)) {
            // Initialize a new selectedStakeholders object
            const newSelectedStakeholders = {};

            // Process each attack path
            attackPathsResponse.data.forEach(path => {
              // Create a unique key for this source-dreaded event pair
              const key = `${path.sourceRiskId}-${path.dreadedEventId}`;

              // Initialize the array for this key if it doesn't exist
              if (!newSelectedStakeholders[key]) {
                newSelectedStakeholders[key] = [];
              }

              // Add the stakeholders from this path
              if (path.stakeholders && Array.isArray(path.stakeholders)) {
                path.stakeholders.forEach(stakeholder => {
                  if (!newSelectedStakeholders[key].includes(stakeholder.id)) {
                    newSelectedStakeholders[key].push(stakeholder.id);
                  }
                });
              }
            });

            // Update the state with the loaded stakeholders
            setSelectedStakeholders(newSelectedStakeholders);
            console.log('Loaded selected stakeholders from attack paths:', newSelectedStakeholders);
          } else {
            // If no data or unsuccessful response, set empty state
            setSelectedStakeholders({});
          }
        } catch (attackPathsError) {
          console.error('Error loading attack paths:', attackPathsError);
          // Set empty state on error to prevent retries
          setSelectedStakeholders({});
        }

        // Dismiss the loading toast without showing a success toast
        dismissToast(toastId);
      } catch (error) {
        console.error('Error loading data:', error);
        setError(t('workshop3.activity2.errors.loadingError'));
        // Dismiss the loading toast and show error toast
        dismissToast(toastId);
        showErrorToast(t('workshop3.activity2.errors.loadingDataError'));
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [currentAnalysis?.id]); // Only depend on the analysis ID

  // Separate effect to update dreaded events when they change in context
  useEffect(() => {
    if (currentDreadedEvents && currentDreadedEvents.length > 0) {
      setDreadedEvents(currentDreadedEvents);
    }
  }, [currentDreadedEvents]);

  return (
    <div className="space-y-8 p-4 sm:p-6 lg:p-8 bg-gradient-to-br from-slate-50 to-blue-50 min-h-screen">
      {/* Loading state */}
      {isLoading ? (
        <div className="flex justify-center items-center h-64 bg-slate-50 rounded-xl shadow-sm">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-slate-600">{t('workshop3.activity2.loadingMessage')}</p>
          </div>
        </div>
      ) : error ? (
        <div className="p-6 bg-slate-50 rounded-lg">
          <div className="bg-red-100 border border-red-300 text-red-800 p-4 rounded-lg mb-4 shadow-sm">
            <h3 className="font-bold mb-2 text-lg flex items-center">
              <Target size={20} className="mr-2"/>
              {t('workshop3.activity2.errors.loadingErrorTitle')}
            </h3>
            <p className="mb-3">{error}</p>
            <button
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition duration-200 text-sm"
              onClick={() => window.location.reload()}
            >
              {t('common.retry')}
            </button>
          </div>
        </div>
      ) : (
        <StrategicScenariosTable
          mappings={mappings}
          sourcesRisque={sourcesRisque}
          dreadedEvents={dreadedEvents}
          controlsData={controlsData}
          stakeholders={stakeholders}
          businessValues={currentBusinessValues}
          initialSelectedStakeholders={selectedStakeholders}
          onSelectedStakeholdersChange={setSelectedStakeholders}
        />
      )}
    </div>
  );
};

export default Activite2;
