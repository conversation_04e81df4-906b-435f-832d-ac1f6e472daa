// Quick test to check if the buttons are rendering correctly
// Run this in browser console on the CTI page

console.log('🧪 Testing CTI Analysis Buttons');

// Check if the AnalysisLauncher component is rendered
const analysisSection = document.querySelector('[class*="border-t border-gray-200"]');
if (analysisSection) {
  console.log('✅ Analysis section found');
  
  // Check for buttons
  const buttons = analysisSection.querySelectorAll('button');
  console.log(`📊 Found ${buttons.length} buttons`);
  
  buttons.forEach((btn, index) => {
    console.log(`Button ${index + 1}:`, {
      text: btn.textContent,
      disabled: btn.disabled,
      classes: btn.className
    });
  });
  
  // Check for specific button types
  const buttonTexts = Array.from(buttons).map(btn => btn.textContent);
  console.log('Button texts:', buttonTexts);
  
  // Check if EUVD button exists
  const euvdButton = Array.from(buttons).find(btn => btn.textContent.includes('EUVD'));
  if (euvdButton) {
    console.log('✅ EUVD button found!');
  } else {
    console.log('❌ EUVD button NOT found');
  }
  
} else {
  console.log('❌ Analysis section not found');
  console.log('Available sections:', document.querySelectorAll('section, div[class*="Section"]'));
}

// Check for any React errors
if (window.React) {
  console.log('✅ React is loaded');
} else {
  console.log('❌ React not found');
}

// Check for console errors
console.log('🔍 Check browser console for any React/JavaScript errors');
