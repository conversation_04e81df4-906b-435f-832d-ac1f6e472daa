// src/services/atelier2Service.js
import api from '../api/apiClient';

/**
 * Service for handling Atelier 2 components
 */
const atelier2Service = {
  // Cache for storing data
  _cache: {
    threatCategories: {},
    sourcesRisque: {},
    objectifsVises: {},
    couplesSROV: {},
    selectedCouplesSROV: {}
  },
  /**
   * Get threat categories for an analysis
   * @param {string} analysisId - Analysis ID
   * @param {boolean} fromCache - Whether to get data from cache
   * @returns {Promise<Array>} - Threat categories
   */
  getThreatCategories: async (analysisId, fromCache = false) => {
    // Return cached data if requested and available
    if (fromCache && atelier2Service._cache.threatCategories[analysisId]) {
      return atelier2Service._cache.threatCategories[analysisId];
    }

    try {
      const response = await api.get(`/analyses/${analysisId}/atelier2/threat-categories`);
      const data = response.data || [];

      // Cache the data
      atelier2Service._cache.threatCategories[analysisId] = data;

      return data;
    } catch (error) {
      console.error('Error getting threat categories'); // Removed error object from log
      // Return empty array for development
      if (process.env.NODE_ENV === 'development') {
        return [];
      }
      throw error;
    }
  },

  /**
   * Save threat categories for an analysis
   * @param {string} analysisId - Analysis ID
   * @param {Array} categories - Threat categories
   * @returns {Promise<Object>} - Response
   */
  saveThreatCategories: async (analysisId, categories) => {
    try {
      const response = await api.post(`/analyses/${analysisId}/atelier2/threat-categories`, { categories });

      // Update cache
      atelier2Service._cache.threatCategories[analysisId] = categories;

      return response;
    } catch (error) {
      console.error('Error saving threat categories'); // Removed error object from log
      // Return success for development
      if (process.env.NODE_ENV === 'development') {
        // Update cache even in development mode
        atelier2Service._cache.threatCategories[analysisId] = categories;
        return { success: true, data: categories };
      }
      throw error;
    }
  },

  /**
   * Get sources de risque for an analysis
   * @param {string} analysisId - Analysis ID
   * @param {boolean} fromCache - Whether to get data from cache
   * @returns {Promise<Array>} - Sources de risque
   */
  getSourcesRisque: async (analysisId, fromCache = false) => {
    console.log("getSourcesRisque called with analysisId:", analysisId, "fromCache:", fromCache);

    // Return cached data if requested and available
    if (fromCache && atelier2Service._cache.sourcesRisque[analysisId]) {
      console.log("Returning cached data for sources de risque");
      return atelier2Service._cache.sourcesRisque[analysisId];
    }

    try {
      // Try to get the data directly from the components endpoint
      try {
        console.log("Trying components endpoint for sources de risque");
        const componentsResponse = await api.get(`/analyses/${analysisId}/components`, { type: 'sources-risque' });
        console.log("Components endpoint response:", componentsResponse);

        if (componentsResponse && Array.isArray(componentsResponse) && componentsResponse.length > 0) {
          const component = componentsResponse[0];

          if (component.data && component.data.sources && Array.isArray(component.data.sources)) {
            const sources = component.data.sources;
            console.log("Found sources in components endpoint:", sources.length);
            // Cache the data
            atelier2Service._cache.sourcesRisque[analysisId] = sources;
            return sources;
          }
        }
      } catch (componentsError) {
        console.error('Error getting data from components endpoint:', componentsError);
      }

      // If we couldn't get the data from the components endpoint, try the atelier2 endpoint
      try {
        console.log("Trying atelier2 endpoint for sources de risque");
        const response = await api.get(`/analyses/${analysisId}/atelier2/sources-risque`);
        console.log("Atelier2 endpoint response:", response);

        // Check if we have data and it has the expected structure
        if (response && Array.isArray(response)) {
          // If the response is already an array, use it directly
          const sources = response;
          console.log("Found sources array directly in response:", sources.length);
          // Cache the data
          atelier2Service._cache.sourcesRisque[analysisId] = sources;
          return sources;
        } else if (response && typeof response === 'object') {
          // Try to find sources in the response object
          if (response.data && Array.isArray(response.data)) {
            const sources = response.data;
            console.log("Found sources in response.data:", sources.length);
            atelier2Service._cache.sourcesRisque[analysisId] = sources;
            return sources;
          } else if (response.data && response.data.sources && Array.isArray(response.data.sources)) {
            const sources = response.data.sources;
            console.log("Found sources in response.data.sources:", sources.length);
            atelier2Service._cache.sourcesRisque[analysisId] = sources;
            return sources;
          } else if (response.sources && Array.isArray(response.sources)) {
            const sources = response.sources;
            console.log("Found sources in response.sources:", sources.length);
            atelier2Service._cache.sourcesRisque[analysisId] = sources;
            return sources;
          }
        }

        console.log("No sources found in response structure:", response);
      } catch (atelier2Error) {
        console.error('Error getting data from atelier2 endpoint:', atelier2Error);
      }

      // Return empty array if no data found
      console.log("No sources found, returning empty array");
      return [];
    } catch (error) {
      console.error('Error getting sources de risque:', error);
      // Return empty array for development
      if (process.env.NODE_ENV === 'development') {
        console.log("Development mode, returning empty array");
        return [];
      }
      throw error;
    }
  },

  /**
   * Save sources de risque for an analysis
   * @param {string} analysisId - Analysis ID
   * @param {Array} sources - Sources de risque
   * @returns {Promise<Object>} - Response
   */
  saveSourcesRisque: async (analysisId, sources) => {
    console.log("saveSourcesRisque called with analysisId:", analysisId, "sources:", sources);
    try {
      // Format the data according to the expected structure
      const formattedData = {
        sources: sources
      };

      console.log("Sending formatted data to API:", formattedData);
      const response = await api.post(`/analyses/${analysisId}/atelier2/sources-risque`, formattedData);
      console.log("API response from saving sources de risque:", response);

      // Update cache with the sources array
      atelier2Service._cache.sourcesRisque[analysisId] = sources;
      console.log("Updated cache with sources");

      return response;
    } catch (error) {
      console.error('Error saving sources de risque:', error);
      // Return success for development
      if (process.env.NODE_ENV === 'development') {
        // Update cache even in development mode
        atelier2Service._cache.sourcesRisque[analysisId] = sources;
        console.log("Development mode, returning success and updating cache");
        return { success: true, data: sources };
      }
      throw error;
    }
  },

  /**
   * Get objectifs visés for an analysis
   * @param {string} analysisId - Analysis ID
   * @param {boolean} fromCache - Whether to get data from cache
   * @returns {Promise<Array>} - Objectifs visés
   */
  getObjectifsVises: async (analysisId, fromCache = false) => {
    // Return cached data if requested and available
    if (fromCache && atelier2Service._cache.objectifsVises[analysisId]) {
      return atelier2Service._cache.objectifsVises[analysisId];
    }

    try {
      const response = await api.get(`/analyses/${analysisId}/atelier2/objectifs-vises`);
      const data = response.data || [];

      // Cache the data
      atelier2Service._cache.objectifsVises[analysisId] = data;

      return data;
    } catch (error) {
      console.error('Error getting objectifs visés'); // Removed error object from log
      // Return empty array for development
      if (process.env.NODE_ENV === 'development') {
        return [];
      }
      throw error;
    }
  },

  /**
   * Save objectifs visés for an analysis
   * @param {string} analysisId - Analysis ID
   * @param {Array} objectifs - Objectifs visés
   * @returns {Promise<Object>} - Response
   */
  saveObjectifsVises: async (analysisId, objectifs) => {
    try {
      const response = await api.post(`/analyses/${analysisId}/atelier2/objectifs-vises`, { objectifs });

      // Update cache
      atelier2Service._cache.objectifsVises[analysisId] = objectifs;

      return response;
    } catch (error) {
      console.error('Error saving objectifs visés'); // Removed error object from log
      // Return success for development
      if (process.env.NODE_ENV === 'development') {
        // Update cache even in development mode
        atelier2Service._cache.objectifsVises[analysisId] = objectifs;
        return { success: true, data: objectifs };
      }
      throw error;
    }
  },

  /**
   * Get couples SR/OV for an analysis
   * @param {string} analysisId - Analysis ID
   * @param {boolean} fromCache - Whether to get data from cache
   * @returns {Promise<Array>} - Couples SR/OV
   */
  getCouplesSROV: async (analysisId, fromCache = false) => {
    // Return cached data if requested and available
    if (fromCache && atelier2Service._cache.couplesSROV[analysisId]) {
      return atelier2Service._cache.couplesSROV[analysisId];
    }

    try {
      // Try to get the data directly from the components endpoint
      try {
        const componentsResponse = await api.get(`/analyses/${analysisId}/components`, { type: 'couples-srov' });

        if (componentsResponse && Array.isArray(componentsResponse) && componentsResponse.length > 0) {
          const component = componentsResponse[0];

          if (component.data && component.data.couples && Array.isArray(component.data.couples)) {
            const couples = component.data.couples;
            // Cache the data
            atelier2Service._cache.couplesSROV[analysisId] = couples;
            return couples;
          }
        }
      } catch (componentsError) {
        console.error('Error getting data from components endpoint'); // Removed error object from log
      }

      // If we couldn't get the data from the components endpoint, try the atelier2 endpoint
      try {
        const response = await api.get(`/analyses/${analysisId}/atelier2/couples-srov`);

        // Check if we have data and it has the expected structure
        if (response && Array.isArray(response)) {
          // If the response is already an array, use it directly
          const couples = response;
          // Cache the data
          atelier2Service._cache.couplesSROV[analysisId] = couples;
          return couples;
        } else if (response && typeof response === 'object') {
          // Try to find couples in the response object
          if (response.data && Array.isArray(response.data)) {
            const couples = response.data;
            atelier2Service._cache.couplesSROV[analysisId] = couples;
            return couples;
          } else if (response.data && response.data.couples && Array.isArray(response.data.couples)) {
            const couples = response.data.couples;
            atelier2Service._cache.couplesSROV[analysisId] = couples;
            return couples;
          } else if (response.couples && Array.isArray(response.couples)) {
            const couples = response.couples;
            atelier2Service._cache.couplesSROV[analysisId] = couples;
            return couples;
          }
        }
      } catch (atelier2Error) {
        console.error('Error getting data from atelier2 endpoint'); // Removed error object from log
      }

      // Return empty array if no data found
      return [];
    } catch (error) {
      console.error('Error getting couples SR/OV'); // Removed error object from log
      // Return empty array for development
      if (process.env.NODE_ENV === 'development') {
        return [];
      }
      throw error;
    }
  },

  /**
   * Save couples SR/OV for an analysis
   * @param {string} analysisId - Analysis ID
   * @param {Array} couples - Couples SR/OV
   * @returns {Promise<Object>} - Response
   */
  saveCouplesSROV: async (analysisId, couples) => {
    try {
      // Ensure all required fields are present in each couple
      const formattedCouples = couples.map(couple => ({
        id: couple.id,
        sourceId: couple.sourceId,
        sourceName: couple.sourceName,
        sourceCategory: couple.sourceCategory,
        objectifVise: couple.objectifVise || '',
        objectifViseCategory: couple.objectifViseCategory || '',
        motivation: couple.motivation || 'faible',
        activite: couple.activite || 'faible',
        ressources: couple.ressources || 'faibles',
        niveau: couple.niveau || 1,
        selected: couple.selected || false
      }));

      const response = await api.post(`/analyses/${analysisId}/atelier2/couples-srov`, { couples: formattedCouples });

      // Update cache
      atelier2Service._cache.couplesSROV[analysisId] = formattedCouples;

      return response;
    } catch (error) {
      console.error('Error saving couples SR/OV'); // Removed error object from log
      // Return success for development
      if (process.env.NODE_ENV === 'development') {
        // Update cache even in development mode
        atelier2Service._cache.couplesSROV[analysisId] = couples;
        return { success: true, data: couples };
      }
      throw error;
    }
  },

  /**
   * Get selected couples SR/OV for an analysis
   * This function now supports both the old format (array of IDs) and the new format (array of mapping objects)
   * @param {string} analysisId - Analysis ID
   * @param {boolean} fromCache - Whether to get data from cache
   * @returns {Promise<Array>} - Selected couples SR/OV or source risk to dreaded event mappings
   */
  getSelectedCouplesSROV: async (analysisId, fromCache = false) => {
    // Return cached data if requested and available
    if (fromCache && atelier2Service._cache.selectedCouplesSROV[analysisId]) {
      return atelier2Service._cache.selectedCouplesSROV[analysisId];
    }

    try {
      const response = await api.get(`/analyses/${analysisId}/atelier2/selected-couples-srov`);

      // Handle different response formats
      let data = [];

      if (response && typeof response === 'object') {
        // Check for new format (mappings)
        if (response.mappings && Array.isArray(response.mappings)) {
          data = response.mappings;
        }
        // Check for old format directly in response
        else if (response.selectedCouples && Array.isArray(response.selectedCouples)) {
          data = response.selectedCouples;
        }
        // Check for attackPaths format
        else if (response.attackPaths && Array.isArray(response.attackPaths)) {
          data = response.attackPaths;
        }
        // Check for data property
        else if (response.data) {
          // Check for new format in data
          if (response.data.mappings && Array.isArray(response.data.mappings)) {
            data = response.data.mappings;
          }
          // Check for old format in data
          else if (response.data.selectedCouples && Array.isArray(response.data.selectedCouples)) {
            data = response.data.selectedCouples;
          }
          // Check for attackPaths in data
          else if (response.data.attackPaths && Array.isArray(response.data.attackPaths)) {
            data = response.data.attackPaths;
          }
          // If data is an array, use it directly
          else if (Array.isArray(response.data)) {
            data = response.data;
          }
        }
        // If response is an array, use it directly
        else if (Array.isArray(response)) {
          data = response;
        }
      }

      // Cache the data
      atelier2Service._cache.selectedCouplesSROV[analysisId] = data;

      return data;
    } catch (error) {
      console.error('Error getting selected couples SR/OV'); // Removed error object from log
      // Return empty array for development
      if (process.env.NODE_ENV === 'development') {
        return [];
      }
      throw error;
    }
  },

  /**
   * Save selected couples SR/OV for an analysis
   * This function now supports both the old format (array of IDs) and the new format (array of mapping objects)
   * @param {string} analysisId - Analysis ID
   * @param {Array|Object} selectedCouples - Selected couples SR/OV or source risk to dreaded event mappings
   * @returns {Promise<Object>} - Response
   */
  saveSelectedCouplesSROV: async (analysisId, selectedCouples) => {
    try {
      let formattedData;

      // Check if we're dealing with a combined format (object with both selectedCouples and mappings)
      if (typeof selectedCouples === 'object' && !Array.isArray(selectedCouples) &&
          selectedCouples.selectedCouples !== undefined && selectedCouples.mappings !== undefined) {
        // Already formatted correctly
        formattedData = selectedCouples;
      }
      // Check if we're dealing with the new mapping format (objects with sourceId and dreadedEventId)
      else if (Array.isArray(selectedCouples) && selectedCouples.length > 0 &&
          typeof selectedCouples[0] === 'object' &&
          (selectedCouples[0].sourceId !== undefined)) {
        formattedData = { mappings: selectedCouples }; // New format: array of mapping objects
      }
      // Old format: array of IDs
      else {
        formattedData = { selectedCouples };
      }

      const response = await api.post(`/analyses/${analysisId}/atelier2/selected-couples-srov`, formattedData);

      // Update cache - store the original format
      atelier2Service._cache.selectedCouplesSROV[analysisId] = selectedCouples;

      return response;
    } catch (error) {
      console.error('Error saving selected couples SR/OV'); // Removed error object from log
      // Return success for development
      if (process.env.NODE_ENV === 'development') {
        // Update cache even in development mode
        atelier2Service._cache.selectedCouplesSROV[analysisId] = selectedCouples;
        return { success: true, data: selectedCouples };
      }
      throw error;
    }
  }
};

export default atelier2Service;
