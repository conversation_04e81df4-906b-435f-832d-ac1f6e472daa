// src/components/Atelier3/Activite4/MeasureForm.jsx
import React, { useState, useEffect } from 'react';
import { X, Plus, Trash2 } from 'lucide-react';
import api from '../../../api/apiClient';

const CATEGORIES = [
  { id: 'governance', label: 'GOUVERNANCE' },
  { id: 'protection', label: 'PROTECTION' },
  { id: 'defense', label: 'DÉFENSE' },
  { id: 'resilience', label: 'RÉSILIENCE' },
  { id: 'other', label: 'AUTRE' }
];

const PRIORITIES = [
  { id: 'high', label: 'Haute', color: 'bg-red-100 text-red-800 border-red-200' },
  { id: 'medium', label: 'Moyenne', color: 'bg-yellow-100 text-yellow-800 border-yellow-200' },
  { id: 'low', label: 'Basse', color: 'bg-green-100 text-green-800 border-green-200' }
];

const IMPLEMENTATION_STATUSES = [
  { id: 'planned', label: 'Planifiée' },
  { id: 'in-progress', label: 'En cours' },
  { id: 'implemented', label: 'Implémentée' },
  { id: 'abandoned', label: 'Abandonnée' }
];

const IMPACT_AREAS = [
  { id: 'confidentiality', label: 'Confidentialité' },
  { id: 'integrity', label: 'Intégrité' },
  { id: 'availability', label: 'Disponibilité' },
  { id: 'traceability', label: 'Traçabilité' },
  { id: 'resilience', label: 'Résilience' }
];

const MeasureForm = ({ measure, onSave, onCancel, analysisId }) => {
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    category: 'governance',
    stakeholders: [],
    attackPaths: [],
    sourceRisks: [], // Added field for risk sources
    objectifsVises: [], // Added field for target objectives
    priority: 'medium',
    implementation: 'planned',
    responsibleParty: '',
    deadline: '',
    cost: 'medium',
    benefits: [],
    effectiveness: 3,
    notes: ''
  });

  const [stakeholderOptions, setStakeholderOptions] = useState([]);
  const [attackPathOptions, setAttackPathOptions] = useState([]);
  const [sourceRiskOptions, setSourceRiskOptions] = useState([]);
  const [objectifViseOptions, setObjectifViseOptions] = useState([]);
  const [newBenefit, setNewBenefit] = useState({
    description: '',
    impactArea: 'confidentiality',
    stakeholder: '',
    riskReduction: 'medium'
  });

  // Load data and initialize form
  useEffect(() => {
    if (analysisId) {
      loadData();
    }

    if (measure) {
      setFormData({
        ...formData,
        ...measure
      });
    }
  }, [analysisId, measure]);

  const loadData = async () => {
    try {
      // Load stakeholders
      const stakeholdersResponse = await api.get(`/analyses/${analysisId}/atelier3/stakeholders`);
      if (stakeholdersResponse.success && stakeholdersResponse.data) {
        setStakeholderOptions(stakeholdersResponse.data);
      }

      // Load attack paths
      const pathsResponse = await api.get(`/analyses/${analysisId}/attack-paths`);
      if (pathsResponse.success && pathsResponse.data) {
        // Process attack paths to include reference codes if not present
        const processedPaths = Array.isArray(pathsResponse.data)
          ? pathsResponse.data.map((path, index) => ({
              ...path,
              referenceCode: path.referenceCode || `CA${String(index + 1).padStart(2, '0')}`
            }))
          : [];

        setAttackPathOptions(processedPaths);
      }

      // Load sources de risque
      const sourcesResponse = await api.get(`/analyses/${analysisId}/atelier2/sources-risque`);
      if (sourcesResponse.success && sourcesResponse.data) {
        setSourceRiskOptions(sourcesResponse.data);
      }

      // Load objectifs visés
      const objectifsResponse = await api.get(`/analyses/${analysisId}/atelier2/objectifs-vises`);
      if (objectifsResponse.success && objectifsResponse.data) {
        setObjectifViseOptions(objectifsResponse.data);
      }
    } catch (error) {
      console.error('Error loading data for measure form:', error);
    }
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleStakeholderChange = (e) => {
    const stakeholderId = e.target.value;
    if (e.target.checked) {
      setFormData(prev => ({
        ...prev,
        stakeholders: [...prev.stakeholders, stakeholderId]
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        stakeholders: prev.stakeholders.filter(id => id !== stakeholderId)
      }));
    }
  };

  const handleAttackPathChange = (e) => {
    const pathId = e.target.value;

    // Find the selected attack path
    const selectedPath = attackPathOptions.find(path => path.id === pathId);

    if (e.target.checked) {
      // Add the attack path
      setFormData(prev => {
        // Update attack paths array
        const updatedAttackPaths = [...prev.attackPaths, pathId];

        // Update sourceRisks array if the path has a sourceRiskId
        let updatedSourceRisks = [...prev.sourceRisks];
        if (selectedPath && selectedPath.sourceRiskId && !updatedSourceRisks.includes(selectedPath.sourceRiskId)) {
          updatedSourceRisks.push(selectedPath.sourceRiskId);
        }

        // Update objectifsVises array if the path has an objectifVise
        let updatedObjectifsVises = [...prev.objectifsVises];
        if (selectedPath && selectedPath.objectifVise && !updatedObjectifsVises.includes(selectedPath.objectifVise)) {
          updatedObjectifsVises.push(selectedPath.objectifVise);
        }

        return {
          ...prev,
          attackPaths: updatedAttackPaths,
          sourceRisks: updatedSourceRisks,
          objectifsVises: updatedObjectifsVises
        };
      });
    } else {
      // Remove the attack path
      setFormData(prev => {
        // Update attack paths array
        const updatedAttackPaths = prev.attackPaths.filter(id => id !== pathId);

        // We don't automatically remove sourceRisks and objectifsVises
        // as they might be used by other selected attack paths

        return {
          ...prev,
          attackPaths: updatedAttackPaths
        };
      });
    }
  };

  const handleSourceRiskChange = (e) => {
    const sourceId = e.target.value;
    if (e.target.checked) {
      setFormData(prev => ({
        ...prev,
        sourceRisks: [...prev.sourceRisks, sourceId]
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        sourceRisks: prev.sourceRisks.filter(id => id !== sourceId)
      }));
    }
  };

  const handleObjectifViseChange = (e) => {
    const objectifId = e.target.value;
    if (e.target.checked) {
      setFormData(prev => ({
        ...prev,
        objectifsVises: [...prev.objectifsVises, objectifId]
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        objectifsVises: prev.objectifsVises.filter(id => id !== objectifId)
      }));
    }
  };

  const handleBenefitChange = (e) => {
    const { name, value } = e.target;
    setNewBenefit(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const addBenefit = () => {
    if (newBenefit.description.trim() === '') return;

    setFormData(prev => ({
      ...prev,
      benefits: [...prev.benefits, { ...newBenefit, id: `benefit-${Date.now()}` }]
    }));

    setNewBenefit({
      description: '',
      impactArea: 'confidentiality',
      stakeholder: '',
      riskReduction: 'medium'
    });
  };

  const removeBenefit = (benefitId) => {
    setFormData(prev => ({
      ...prev,
      benefits: prev.benefits.filter(benefit => benefit.id !== benefitId)
    }));
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    onSave(formData);
  };

  return (
    <div className="bg-white border border-gray-200 rounded-xl shadow-lg overflow-hidden">
      {/* Header */}
      <div className="bg-gradient-to-r from-blue-600 to-indigo-600 px-6 py-4">
        <div className="flex justify-between items-center">
          <div>
            <h3 className="text-xl font-semibold text-white">
              {measure ? 'Modifier la mesure' : 'Ajouter une nouvelle mesure'}
            </h3>
            <p className="text-blue-100 text-sm mt-1">
              {measure ? 'Modifiez les détails de cette mesure de sécurité' : 'Créez une nouvelle mesure de sécurité pour votre plan'}
            </p>
          </div>
          <button
            onClick={onCancel}
            className="text-blue-100 hover:text-white hover:bg-blue-700 p-2 rounded-lg transition-colors duration-150"
          >
            <X size={20} />
          </button>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="p-6 space-y-6">
        {/* Section 1: Informations de base */}
        <div className="bg-gray-50 rounded-lg p-6 border border-gray-200">
          <h4 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <div className="w-3 h-3 bg-blue-500 rounded-full mr-3"></div>
            Informations de base
          </h4>

          <div className="space-y-4">
            {/* Title and Category */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="md:col-span-2">
                <label htmlFor="title" className="block text-sm font-semibold text-gray-700 mb-2">
                  Titre de la mesure *
                </label>
                <input
                  type="text"
                  id="title"
                  name="title"
                  value={formData.title}
                  onChange={handleChange}
                  required
                  placeholder="Ex: Mise en place d'une authentification multi-facteur"
                  className="w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 focus:ring-2 transition-all duration-150"
                />
              </div>

              <div>
                <label htmlFor="category" className="block text-sm font-semibold text-gray-700 mb-2">
                  Catégorie *
                </label>
                <select
                  id="category"
                  name="category"
                  value={formData.category}
                  onChange={handleChange}
                  required
                  className="w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 focus:ring-2 transition-all duration-150"
                >
                  {CATEGORIES.map(category => (
                    <option key={category.id} value={category.id}>
                      {category.label}
                    </option>
                  ))}
                </select>
              </div>
            </div>

            {/* Description */}
            <div>
              <label htmlFor="description" className="block text-sm font-semibold text-gray-700 mb-2">
                Description détaillée *
              </label>
              <textarea
                id="description"
                name="description"
                value={formData.description}
                onChange={handleChange}
                required
                rows={4}
                placeholder="Décrivez en détail cette mesure de sécurité, son objectif et sa mise en œuvre..."
                className="w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 focus:ring-2 transition-all duration-150"
              />
            </div>
          </div>
        </div>

        {/* Section 2: Paramètres de mise en œuvre */}
        <div className="bg-gray-50 rounded-lg p-6 border border-gray-200">
          <h4 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <div className="w-3 h-3 bg-green-500 rounded-full mr-3"></div>
            Paramètres de mise en œuvre
          </h4>

          <div className="space-y-4">
            {/* Priority, Implementation, Effectiveness */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label htmlFor="priority" className="block text-sm font-semibold text-gray-700 mb-2">
                  Priorité *
                </label>
                <select
                  id="priority"
                  name="priority"
                  value={formData.priority}
                  onChange={handleChange}
                  required
                  className="w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 focus:ring-2 transition-all duration-150"
                >
                  {PRIORITIES.map(priority => (
                    <option key={priority.id} value={priority.id}>
                      {priority.label}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label htmlFor="implementation" className="block text-sm font-semibold text-gray-700 mb-2">
                  Statut d'implémentation
                </label>
                <select
                  id="implementation"
                  name="implementation"
                  value={formData.implementation}
                  onChange={handleChange}
                  className="w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 focus:ring-2 transition-all duration-150"
                >
                  {IMPLEMENTATION_STATUSES.map(status => (
                    <option key={status.id} value={status.id}>
                      {status.label}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label htmlFor="effectiveness" className="block text-sm font-semibold text-gray-700 mb-2">
                  Efficacité estimée
                </label>
                <div className="relative">
                  <input
                    type="number"
                    id="effectiveness"
                    name="effectiveness"
                    value={formData.effectiveness}
                    onChange={handleChange}
                    min={1}
                    max={5}
                    className="w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 focus:ring-2 transition-all duration-150"
                  />
                  <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                    <span className="text-gray-500 text-sm">/5</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Responsible Party and Deadline */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label htmlFor="responsibleParty" className="block text-sm font-semibold text-gray-700 mb-2">
                  Responsable de la mise en œuvre
                </label>
                <input
                  type="text"
                  id="responsibleParty"
                  name="responsibleParty"
                  value={formData.responsibleParty}
                  onChange={handleChange}
                  placeholder="Ex: Équipe sécurité, DSI, etc."
                  className="w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 focus:ring-2 transition-all duration-150"
                />
              </div>

              <div>
                <label htmlFor="deadline" className="block text-sm font-semibold text-gray-700 mb-2">
                  Date d'échéance
                </label>
                <input
                  type="date"
                  id="deadline"
                  name="deadline"
                  value={formData.deadline}
                  onChange={handleChange}
                  className="w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 focus:ring-2 transition-all duration-150"
                />
              </div>
            </div>
          </div>
        </div>

        {/* Section 3: Chemins d'attaque */}
        <div className="bg-gray-50 rounded-lg p-6 border border-gray-200">
          <h4 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <div className="w-3 h-3 bg-red-500 rounded-full mr-3"></div>
            Chemins d'attaque traités
          </h4>

          <div className="bg-white rounded-lg border border-gray-200 p-4 max-h-80 overflow-y-auto">
            {attackPathOptions.length === 0 ? (
              <div className="text-center py-8">
                <div className="text-gray-400 mb-2">
                  <svg className="mx-auto h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <p className="text-sm text-gray-500">Aucun chemin d'attaque disponible</p>
                <p className="text-xs text-gray-400 mt-1">Les chemins d'attaque seront chargés depuis l'Atelier 2</p>
              </div>
            ) : (
              <div className="space-y-3">
                <div className="text-sm text-gray-600 mb-4 p-3 bg-blue-50 rounded-lg border border-blue-200">
                  <strong>Instructions:</strong> Sélectionnez les chemins d'attaque que cette mesure de sécurité permet de traiter ou de mitiger.
                </div>
                {attackPathOptions.map(path => {
                  // Find the source risk and objectif visé for this path
                  const sourceRisk = sourceRiskOptions.find(s => s.id === path.sourceRiskId);
                  // Use description if available, otherwise fall back to name
                  const sourceName = sourceRisk ?
                    (sourceRisk.description || sourceRisk.name) :
                    (path.sourceRiskDescription || path.sourceRiskName || 'Source inconnue');
                  const objectifName = path.objectifVise || 'Objectif non spécifié';

                  return (
                    <div key={path.id} className={`p-4 border-2 rounded-lg transition-all duration-150 cursor-pointer ${
                      formData.attackPaths.includes(path.id)
                        ? 'border-blue-500 bg-blue-50 shadow-md'
                        : 'border-gray-200 bg-white hover:border-blue-300 hover:bg-blue-50'
                    }`}>
                      <div className="flex items-start">
                        <input
                          type="checkbox"
                          id={`path-${path.id}`}
                          value={path.id}
                          checked={formData.attackPaths.includes(path.id)}
                          onChange={handleAttackPathChange}
                          className="h-5 w-5 mt-1 text-blue-600 rounded focus:ring-blue-500 focus:ring-2"
                        />
                        <div className="ml-3 flex-grow">
                          <div className="flex items-center mb-2">
                            <span className="inline-flex items-center px-3 py-1 rounded-md text-sm font-bold bg-blue-100 text-blue-800 border border-blue-200 mr-3 shadow-sm">
                              {path.referenceCode}
                            </span>
                            <label htmlFor={`path-${path.id}`} className="text-sm font-semibold text-gray-700 cursor-pointer">
                              Chemin d'attaque
                            </label>
                          </div>
                          <div className="space-y-2">
                            <div className="flex items-center">
                              <span className="text-xs font-medium text-gray-500 w-16">Source:</span>
                              <span className="inline-flex items-center px-3 py-1 rounded-lg text-xs font-medium bg-red-100 text-red-800 border border-red-200">
                                {sourceName.length > 40 ? sourceName.substring(0, 40) + '...' : sourceName}
                              </span>
                            </div>
                            <div className="flex items-center">
                              <span className="text-xs font-medium text-gray-500 w-16">Objectif:</span>
                              <span className="inline-flex items-center px-3 py-1 rounded-lg text-xs font-medium bg-green-100 text-green-800 border border-green-200">
                                {objectifName.length > 40 ? objectifName.substring(0, 40) + '...' : objectifName}
                              </span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            )}
          </div>
        </div>

        {/* Section 4: Bénéfices (optionnel) */}
        <div className="bg-gray-50 rounded-lg p-6 border border-gray-200">
          <h4 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <div className="w-3 h-3 bg-purple-500 rounded-full mr-3"></div>
            Bénéfices attendus
            <span className="ml-2 text-sm font-normal text-gray-500">(optionnel)</span>
          </h4>

          {/* Existing benefits */}
          {formData.benefits.length > 0 && (
            <div className="mb-4 space-y-3">
              {formData.benefits.map(benefit => (
                <div key={benefit.id} className="bg-white rounded-lg p-4 border border-gray-200 shadow-sm">
                  <div className="flex items-start justify-between">
                    <div className="flex-grow">
                      <p className="text-sm font-medium text-gray-900 mb-2">{benefit.description}</p>
                      <div className="flex flex-wrap gap-2">
                        <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 border border-blue-200">
                          {IMPACT_AREAS.find(area => area.id === benefit.impactArea)?.label || benefit.impactArea}
                        </span>
                        {benefit.stakeholder && (
                          <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800 border border-purple-200">
                            {stakeholderOptions.find(s => s.id === benefit.stakeholder)?.name || benefit.stakeholder}
                          </span>
                        )}
                        <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 border border-green-200">
                          Réduction: {benefit.riskReduction === 'high' ? 'Haute' : benefit.riskReduction === 'medium' ? 'Moyenne' : 'Basse'}
                        </span>
                      </div>
                    </div>
                    <button
                      type="button"
                      onClick={() => removeBenefit(benefit.id)}
                      className="ml-3 p-2 text-red-500 hover:text-red-700 hover:bg-red-50 rounded-lg transition-colors duration-150"
                    >
                      <Trash2 size={16} />
                    </button>
                  </div>
                </div>
              ))}
            </div>
          )}

          {/* Add new benefit */}
          <div className="bg-white rounded-lg border border-gray-200 p-4">
            <h5 className="text-sm font-semibold text-gray-700 mb-3">Ajouter un nouveau bénéfice</h5>

            <div className="space-y-4">
              <div>
                <label className="block text-xs font-medium text-gray-600 mb-1">Description du bénéfice</label>
                <input
                  type="text"
                  placeholder="Ex: Réduction des risques d'accès non autorisé aux systèmes critiques..."
                  value={newBenefit.description}
                  onChange={handleBenefitChange}
                  name="description"
                  className="w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 focus:ring-2 transition-all duration-150"
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="block text-xs font-medium text-gray-600 mb-1">Domaine d'impact</label>
                  <select
                    value={newBenefit.impactArea}
                    onChange={handleBenefitChange}
                    name="impactArea"
                    className="w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 focus:ring-2 transition-all duration-150"
                  >
                    {IMPACT_AREAS.map(area => (
                      <option key={area.id} value={area.id}>
                        {area.label}
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-xs font-medium text-gray-600 mb-1">Partie prenante</label>
                  <select
                    value={newBenefit.stakeholder}
                    onChange={handleBenefitChange}
                    name="stakeholder"
                    className="w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 focus:ring-2 transition-all duration-150"
                  >
                    <option value="">-- Optionnel --</option>
                    {stakeholderOptions.map(stakeholder => (
                      <option key={stakeholder.id} value={stakeholder.id}>
                        {stakeholder.name}
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-xs font-medium text-gray-600 mb-1">Niveau de réduction</label>
                  <select
                    value={newBenefit.riskReduction}
                    onChange={handleBenefitChange}
                    name="riskReduction"
                    className="w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 focus:ring-2 transition-all duration-150"
                  >
                    <option value="high">Haute</option>
                    <option value="medium">Moyenne</option>
                    <option value="low">Basse</option>
                  </select>
                </div>
              </div>

              <button
                type="button"
                onClick={addBenefit}
                disabled={!newBenefit.description.trim()}
                className={`flex items-center px-4 py-2 rounded-lg text-sm font-medium transition-all duration-150 ${
                  !newBenefit.description.trim()
                    ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                    : 'bg-blue-600 text-white hover:bg-blue-700 shadow-sm hover:shadow-md'
                }`}
              >
                <Plus size={16} className="mr-2" />
                Ajouter ce bénéfice
              </button>
            </div>
          </div>
        </div>

        {/* Section 5: Notes additionnelles */}
        <div className="bg-gray-50 rounded-lg p-6 border border-gray-200">
          <h4 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <div className="w-3 h-3 bg-gray-500 rounded-full mr-3"></div>
            Notes additionnelles
            <span className="ml-2 text-sm font-normal text-gray-500">(optionnel)</span>
          </h4>

          <textarea
            id="notes"
            name="notes"
            value={formData.notes}
            onChange={handleChange}
            rows={3}
            placeholder="Ajoutez des notes, commentaires ou informations complémentaires sur cette mesure..."
            className="w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 focus:ring-2 transition-all duration-150"
          />
        </div>

        {/* Form actions */}
        <div className="bg-gray-50 rounded-lg p-6 border border-gray-200">
          <div className="flex justify-end space-x-4">
            <button
              type="button"
              onClick={onCancel}
              className="px-6 py-3 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 hover:bg-gray-50 hover:border-gray-400 transition-all duration-150"
            >
              Annuler
            </button>
            <button
              type="submit"
              className="px-6 py-3 bg-blue-600 rounded-lg text-sm font-medium text-white hover:bg-blue-700 shadow-sm hover:shadow-md transition-all duration-150"
            >
              {measure ? 'Mettre à jour la mesure' : 'Créer la mesure'}
            </button>
          </div>
        </div>
      </form>
    </div>
  );
};

export default MeasureForm;
