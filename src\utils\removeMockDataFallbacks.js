// src/utils/removeMockDataFallbacks.js
// Utility to remove mock data fallbacks from services

/**
 * Enhanced error handler that throws errors instead of falling back to mock data
 * @param {Error} error - The original error
 * @param {string} context - Context information for debugging
 * @throws {Error} Always throws the error instead of falling back
 */
export const handleApiError = (error, context = '') => {
  console.error(`API Error${context ? ` in ${context}` : ''}:`, error);
  
  // Check if it's an authentication error
  if (error.response?.status === 401 || error.message?.includes('401')) {
    // Token expired - let session manager handle this
    console.log('Authentication error detected - session will be refreshed');
    throw new Error('Authentication required');
  }
  
  // Check if it's a network error
  if (!error.response && error.request) {
    throw new Error('Network error - please check your connection');
  }
  
  // For other errors, throw with more context
  const errorMessage = error.response?.data?.message || error.message || 'Unknown error occurred';
  throw new Error(`${context ? `${context}: ` : ''}${errorMessage}`);
};

/**
 * Wrapper for API calls that removes mock data fallbacks
 * @param {Function} apiCall - The API call function
 * @param {string} context - Context for error handling
 * @returns {Promise} The API response or throws an error
 */
export const apiCallWithoutFallback = async (apiCall, context = '') => {
  try {
    return await apiCall();
  } catch (error) {
    handleApiError(error, context);
  }
};

/**
 * Check if we're in development mode (for conditional behavior)
 * @returns {boolean} True if in development mode
 */
export const isDevelopmentMode = () => {
  return process.env.NODE_ENV === 'development';
};

/**
 * Log when mock data would have been used (for debugging)
 * @param {string} service - The service name
 * @param {string} method - The method name
 */
export const logMockDataRemoval = (service, method) => {
  if (isDevelopmentMode()) {
    console.warn(`🚫 Mock data fallback removed for ${service}.${method} - API call will fail if backend is unavailable`);
  }
};

export default {
  handleApiError,
  apiCallWithoutFallback,
  isDevelopmentMode,
  logMockDataRemoval
};
