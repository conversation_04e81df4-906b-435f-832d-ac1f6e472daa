// src/components/Atelier4/Activite2/Activite2.js
import React, { useState, useEffect, useContext } from 'react';
import { AnalysisContext } from '../../../context/AnalysisContext';
import { api } from '../../../api/apiClient';
import OperationalScenariosTable from './OperationalScenariosTable';
import OperationalScenariosVisualization from './OperationalScenariosVisualization';
import AIGenerationModal from './AIGenerationModal';
import GuideModal from './GuideModal';
import './Activite2.css';

const Activite2 = () => {
  const { currentAnalysis } = useContext(AnalysisContext);
  const [operationalScenarios, setOperationalScenarios] = useState([]);
  const [attackPaths, setAttackPaths] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [showAIModal, setShowAIModal] = useState(false);
  const [showGuide, setShowGuide] = useState(false);
  const [activeView, setActiveView] = useState('table'); // 'table' or 'visualization'
  const [selectedScenarios, setSelectedScenarios] = useState([]);

  // Load data on component mount
  useEffect(() => {
    if (currentAnalysis?.id) {
      loadData();
    }
  }, [currentAnalysis?.id]);

  const loadData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Load attack paths from Atelier 3
      const attackPathsResponse = await api.get(`/analyses/${currentAnalysis.id}/attack-paths`);
      if (attackPathsResponse?.data?.attackPaths) {
        setAttackPaths(attackPathsResponse.data.attackPaths);
      }

      // Load existing operational scenarios
      const scenariosResponse = await api.get(`/analyses/${currentAnalysis.id}/operational-scenarios`);
      if (scenariosResponse?.data?.scenarios) {
        setOperationalScenarios(scenariosResponse.data.scenarios);
      }

    } catch (err) {
      console.error('Error loading data:', err);
      setError('Erreur lors du chargement des données');
    } finally {
      setLoading(false);
    }
  };

  const handleGenerateScenarios = async (selectedAttackPaths) => {
    try {
      setLoading(true);
      
      // Prepare attack paths data for AI
      const attackPathsData = attackPaths.filter(path => 
        selectedAttackPaths.includes(path.id)
      );

      // Call AI service to generate operational scenarios
      const response = await api.post('/ai/generate-operational-scenarios', {
        analysisId: currentAnalysis.id,
        attackPaths: attackPathsData,
        existingScenarios: operationalScenarios
      });

      if (response?.data?.scenarios) {
        // Add generated scenarios to the list
        const newScenarios = response.data.scenarios.map(scenario => ({
          ...scenario,
          id: `scenario_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          createdAt: new Date().toISOString(),
          source: 'ai_generated'
        }));

        setOperationalScenarios(prev => [...prev, ...newScenarios]);
        
        // Save to backend
        await saveScenarios([...operationalScenarios, ...newScenarios]);
      }

      setShowAIModal(false);
    } catch (err) {
      console.error('Error generating scenarios:', err);
      setError('Erreur lors de la génération des scénarios opérationnels');
    } finally {
      setLoading(false);
    }
  };

  const saveScenarios = async (scenarios) => {
    try {
      await api.post(`/analyses/${currentAnalysis.id}/operational-scenarios`, {
        scenarios
      });
    } catch (err) {
      console.error('Error saving scenarios:', err);
      throw err;
    }
  };

  const handleScenarioUpdate = async (scenarioId, updates) => {
    try {
      const updatedScenarios = operationalScenarios.map(scenario =>
        scenario.id === scenarioId ? { ...scenario, ...updates } : scenario
      );
      
      setOperationalScenarios(updatedScenarios);
      await saveScenarios(updatedScenarios);
    } catch (err) {
      console.error('Error updating scenario:', err);
      setError('Erreur lors de la mise à jour du scénario');
    }
  };

  const handleScenarioDelete = async (scenarioId) => {
    try {
      const updatedScenarios = operationalScenarios.filter(scenario => scenario.id !== scenarioId);
      setOperationalScenarios(updatedScenarios);
      await saveScenarios(updatedScenarios);
    } catch (err) {
      console.error('Error deleting scenario:', err);
      setError('Erreur lors de la suppression du scénario');
    }
  };

  const handleExportScenarios = () => {
    const dataStr = JSON.stringify(operationalScenarios, null, 2);
    const dataUri = 'data:application/json;charset=utf-8,'+ encodeURIComponent(dataStr);
    
    const exportFileDefaultName = `scenarios_operationnels_${currentAnalysis?.name || 'analyse'}_${new Date().toISOString().split('T')[0]}.json`;
    
    const linkElement = document.createElement('a');
    linkElement.setAttribute('href', dataUri);
    linkElement.setAttribute('download', exportFileDefaultName);
    linkElement.click();
  };

  if (loading) {
    return (
      <div className="activite2-container">
        <div className="loading-spinner">
          <div className="spinner"></div>
          <p>Chargement des scénarios opérationnels...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="activite2-container">
      <div className="activite2-header">
        <div className="header-content">
          <h2>Activité 2 - Scénarios opérationnels</h2>
          <p className="header-description">
            Décomposition des chemins d'attaque en scénarios opérationnels détaillés
          </p>
        </div>
        
        <div className="header-actions">
          <button 
            className="btn btn-help"
            onClick={() => setShowGuide(true)}
            title="Guide d'utilisation"
          >
            <i className="fas fa-question-circle"></i>
            Guide
          </button>
          
          <button 
            className="btn btn-primary"
            onClick={() => setShowAIModal(true)}
            disabled={attackPaths.length === 0}
            title="Générer des scénarios avec l'IA"
          >
            <i className="fas fa-robot"></i>
            Générer avec IA
          </button>
          
          <button 
            className="btn btn-secondary"
            onClick={handleExportScenarios}
            disabled={operationalScenarios.length === 0}
            title="Exporter les scénarios"
          >
            <i className="fas fa-download"></i>
            Exporter
          </button>
        </div>
      </div>

      {error && (
        <div className="error-message">
          <i className="fas fa-exclamation-triangle"></i>
          {error}
          <button onClick={() => setError(null)} className="error-close">
            <i className="fas fa-times"></i>
          </button>
        </div>
      )}

      <div className="view-controls">
        <div className="view-tabs">
          <button 
            className={`tab ${activeView === 'table' ? 'active' : ''}`}
            onClick={() => setActiveView('table')}
          >
            <i className="fas fa-table"></i>
            Vue tableau
          </button>
          <button 
            className={`tab ${activeView === 'visualization' ? 'active' : ''}`}
            onClick={() => setActiveView('visualization')}
          >
            <i className="fas fa-project-diagram"></i>
            Visualisation
          </button>
        </div>

        <div className="scenario-stats">
          <span className="stat">
            <i className="fas fa-list"></i>
            {operationalScenarios.length} scénario{operationalScenarios.length !== 1 ? 's' : ''}
          </span>
          <span className="stat">
            <i className="fas fa-route"></i>
            {attackPaths.length} chemin{attackPaths.length !== 1 ? 's' : ''} d'attaque
          </span>
        </div>
      </div>

      <div className="activite2-content">
        {activeView === 'table' ? (
          <OperationalScenariosTable
            scenarios={operationalScenarios}
            attackPaths={attackPaths}
            onScenarioUpdate={handleScenarioUpdate}
            onScenarioDelete={handleScenarioDelete}
            selectedScenarios={selectedScenarios}
            onSelectionChange={setSelectedScenarios}
          />
        ) : (
          <OperationalScenariosVisualization
            scenarios={operationalScenarios}
            attackPaths={attackPaths}
            selectedScenarios={selectedScenarios}
            onScenarioSelect={setSelectedScenarios}
          />
        )}
      </div>

      {showAIModal && (
        <AIGenerationModal
          attackPaths={attackPaths}
          existingScenarios={operationalScenarios}
          onGenerate={handleGenerateScenarios}
          onClose={() => setShowAIModal(false)}
        />
      )}

      {showGuide && (
        <GuideModal onClose={() => setShowGuide(false)} />
      )}
    </div>
  );
};

export default Activite2;