// backend/controllers/companyController.js
const Company = require('../models/Company');
const User = require('../models/User');
const ActivityLog = require('../models/ActivityLog');

// Helper function to get IP address
const getIpAddress = (req) => {
  return req.ip || 
         req.headers['x-forwarded-for'] || 
         req.connection.remoteAddress || 
         'unknown';
};

// Helper function to create activity log
const logActivity = async (req, actionType, resourceType, resourceId, details = {}) => {
  try {
    if (!req.user) return;
    
    await ActivityLog.create({
      userId: req.user.id,
      userName: req.user.name,
      companyId: req.user.companyId,
      companyName: req.user.companyName,
      actionType,
      resourceType,
      resourceId,
      ipAddress: getIpAddress(req),
      userAgent: req.headers['user-agent'] || '',
      details: {
        ...details,
        timestamp: new Date()
      }
    });
  } catch (error) {
    console.error('Activity log error:', error);
  }
};

/**
 * @desc    Get all companies
 * @route   GET /api/companies
 * @access  Private (SuperAdmin only)
 */
exports.getCompanies = async (req, res) => {
  try {
    const { status, search, page = 1, limit = 10 } = req.query;
    const skip = (page - 1) * limit;
    
    // Build filter
    const filter = {};
    
    // Status filter
    if (status) {
      filter.status = status;
    }
    
    // Search filter
    if (search) {
      filter.$or = [
        { name: { $regex: search, $options: 'i' } },
        { domain: { $regex: search, $options: 'i' } }
      ];
    }
    
    // Execute query with pagination
    const companies = await Company.find(filter)
      .sort({ name: 1 })
      .skip(skip)
      .limit(parseInt(limit));
    
    // Get total count for pagination
    const total = await Company.countDocuments(filter);
    
    // Get user counts for each company
    const companyIds = companies.map(company => company._id);
    const userCounts = await User.aggregate([
      { $match: { companyId: { $in: companyIds } } },
      { $group: { _id: '$companyId', count: { $sum: 1 } } }
    ]);
    
    // Add user count to each company
    const companiesWithUserCount = companies.map(company => {
      const userCount = userCounts.find(uc => uc._id.toString() === company._id.toString());
      return {
        ...company._doc,
        userCount: userCount ? userCount.count : 0
      };
    });
    
    // Return companies
    res.status(200).json({
      success: true,
      data: companiesWithUserCount,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Get companies error:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération des entreprises',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * @desc    Get company by ID
 * @route   GET /api/companies/:id
 * @access  Private (SuperAdmin, or Admin/User of the company)
 */
exports.getCompanyById = async (req, res) => {
  try {
    const company = await Company.findById(req.params.id);
    
    if (!company) {
      return res.status(404).json({
        success: false,
        message: 'Entreprise non trouvée'
      });
    }
    
    // Check permission
    if (req.user.role !== 'superadmin') {
      // Non-superadmin users can only see their own company
      if (req.user.companyId.toString() !== company._id.toString()) {
        return res.status(403).json({
          success: false,
          message: 'Vous n\'avez pas la permission de voir cette entreprise'
        });
      }
    }
    
    // Get user count
    const userCount = await User.countDocuments({ companyId: company._id });
    
    // Return company with user count
    res.status(200).json({
      success: true,
      data: {
        ...company._doc,
        userCount
      }
    });
  } catch (error) {
    console.error('Get company error:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération de l\'entreprise',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * @desc    Create new company
 * @route   POST /api/companies
 * @access  Private (SuperAdmin only)
 */
exports.createCompany = async (req, res) => {
  try {
    const { name, domain, status = 'active' } = req.body;
    
    // Validation
    if (!name) {
      return res.status(400).json({
        success: false,
        message: 'Le nom de l\'entreprise est requis'
      });
    }
    
    // Check if company name exists
    const existingCompany = await Company.findOne({ name });
    
    if (existingCompany) {
      return res.status(400).json({
        success: false,
        message: 'Une entreprise avec ce nom existe déjà'
      });
    }
    
    // Check if domain exists
    if (domain) {
      const existingDomain = await Company.findOne({ domain });
      
      if (existingDomain) {
        return res.status(400).json({
          success: false,
          message: 'Une entreprise avec ce domaine existe déjà'
        });
      }
    }
    
    // Create company
    const company = await Company.create({
      name,
      domain: domain || null,
      status,
      createdBy: req.user.id
    });
    
    // Log activity
    await logActivity(
      req, 
      'COMPANY_CREATE', 
      'company', 
      company._id, 
      { 
        companyName: company.name, 
        companyDomain: company.domain 
      }
    );
    
    res.status(201).json({
      success: true,
      data: company
    });
  } catch (error) {
    console.error('Create company error:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la création de l\'entreprise',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * @desc    Update company
 * @route   PUT /api/companies/:id
 * @access  Private (SuperAdmin only)
 */
exports.updateCompany = async (req, res) => {
  try {
    const { name, domain, status } = req.body;
    const companyId = req.params.id;
    
    // Find company
    const company = await Company.findById(companyId);
    
    if (!company) {
      return res.status(404).json({
        success: false,
        message: 'Entreprise non trouvée'
      });
    }
    
    // Check if new name exists for another company
    if (name && name !== company.name) {
      const existingCompany = await Company.findOne({ name });
      
      if (existingCompany) {
        return res.status(400).json({
          success: false,
          message: 'Une entreprise avec ce nom existe déjà'
        });
      }
    }
    
    // Check if new domain exists for another company
    if (domain && domain !== company.domain) {
      const existingDomain = await Company.findOne({ domain });
      
      if (existingDomain) {
        return res.status(400).json({
          success: false,
          message: 'Une entreprise avec ce domaine existe déjà'
        });
      }
    }
    
    // Update company fields
    if (name) company.name = name;
    if (domain !== undefined) company.domain = domain;
    if (status) company.status = status;
    
    // Update timestamp
    company.updatedAt = Date.now();
    
    // Save changes
    await company.save();
    
    // If name changed, update all users with this company
    if (name && name !== company.name) {
      await User.updateMany(
        { companyId: company._id },
        { companyName: name }
      );
    }
    
    // Log activity
    await logActivity(
      req, 
      'COMPANY_UPDATE', 
      'company', 
      company._id, 
      { 
        companyName: company.name, 
        companyDomain: company.domain,
        changedFields: Object.keys(req.body)
      }
    );
    
    res.status(200).json({
      success: true,
      data: company
    });
  } catch (error) {
    console.error('Update company error:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la mise à jour de l\'entreprise',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * @desc    Delete company
 * @route   DELETE /api/companies/:id
 * @access  Private (SuperAdmin only)
 */
exports.deleteCompany = async (req, res) => {
  try {
    const companyId = req.params.id;
    
    // Find company
    const company = await Company.findById(companyId);
    
    if (!company) {
      return res.status(404).json({
        success: false,
        message: 'Entreprise non trouvée'
      });
    }
    
    // Check if company has users
    const userCount = await User.countDocuments({ companyId: company._id });
    
    if (userCount > 0) {
      return res.status(400).json({
        success: false,
        message: 'Impossible de supprimer l\'entreprise car elle contient encore des utilisateurs'
      });
    }
    
    // Store company information for logging
    const companyInfo = {
      id: company._id,
      name: company.name,
      domain: company.domain
    };
    
    // Delete company
    await Company.findByIdAndDelete(companyId);
    
    // Log activity
    await logActivity(
      req, 
      'COMPANY_DELETE', 
      'company', 
      companyInfo.id, 
      companyInfo
    );
    
    res.status(200).json({
      success: true,
      message: 'Entreprise supprimée avec succès',
      data: companyInfo
    });
  } catch (error) {
    console.error('Delete company error:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la suppression de l\'entreprise',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * @desc    Get company users
 * @route   GET /api/companies/:id/users
 * @access  Private (SuperAdmin, Admin of the company)
 */
exports.getCompanyUsers = async (req, res) => {
  try {
    const companyId = req.params.id;
    
    // Find company
    const company = await Company.findById(companyId);
    
    if (!company) {
      return res.status(404).json({
        success: false,
        message: 'Entreprise non trouvée'
      });
    }
    
    // Check permission
    if (req.user.role !== 'superadmin') {
      // Non-superadmin can only see users from their own company
      if (req.user.companyId.toString() !== company._id.toString()) {
        return res.status(403).json({
          success: false,
          message: 'Vous n\'avez pas la permission de voir les utilisateurs de cette entreprise'
        });
      }
    }
    
    // Get company users
    const users = await User.find({ companyId: company._id }).select('-password');
    
    res.status(200).json({
      success: true,
      data: users
    });
  } catch (error) {
    console.error('Get company users error:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération des utilisateurs de l\'entreprise',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * @desc    Get company metrics
 * @route   GET /api/companies/metrics
 * @access  Private (SuperAdmin only)
 */
exports.getCompanyMetrics = async (req, res) => {
  try {
    const companies = await Company.find();
    
    // Get user counts for all companies
    const userCounts = await User.aggregate([
      { $group: { _id: '$companyId', count: { $sum: 1 } } }
    ]);
    
    // Get active user counts
    const activeUserCounts = await User.aggregate([
      { $match: { status: 'active' } },
      { $group: { _id: '$companyId', count: { $sum: 1 } } }
    ]);
    
    // Build metrics
    const metrics = companies.map(company => {
      const userCount = userCounts.find(uc => 
        uc._id && company._id && uc._id.toString() === company._id.toString()
      );
      
      const activeUserCount = activeUserCounts.find(auc => 
        auc._id && company._id && auc._id.toString() === company._id.toString()
      );
      
      return {
        companyId: company._id,
        companyName: company.name,
        totalUsers: userCount ? userCount.count : 0,
        activeUsers: activeUserCount ? activeUserCount.count : 0,
        status: company.status
      };
    });
    
    res.status(200).json({
      success: true,
      data: metrics
    });
  } catch (error) {
    console.error('Get company metrics error:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération des métriques des entreprises',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};