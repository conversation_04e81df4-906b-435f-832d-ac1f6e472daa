#!/bin/bash

FILE="src/services/ctiService.js"

# Find the line number where searchByKeywords starts
LINE_START=$(grep -n "async searchByKeywords" "$FILE" | cut -d: -f1)
LINE_END=$((LINE_START + 25))

# Replace the searchByKeywords function with enhanced version
sed -i "${LINE_START},${LINE_END}c\
  // Search by keywords (vendor + product) - Enhanced with multiple strategies\
  async searchByKeywords(asset) {\
    let allResults = [];\
    \
    // Strategy 1: Vendor + Product\
    const keywords1 = \`\${asset.vendor} \${asset.product}\`.trim();\
    if (keywords1) {\
      console.log(\`[CTI] Searching by keywords: \${keywords1}\`);\
      const results1 = await this.performKeywordSearch(keywords1, 15);\
      allResults = [...allResults, ...results1];\
    }\
    \
    // Strategy 2: Product only (if vendor+product gave few results)\
    if (allResults.length < 5 && asset.product) {\
      console.log(\`[CTI] Trying product-only search: \${asset.product}\`);\
      const results2 = await this.performKeywordSearch(asset.product, 10);\
      // Merge avoiding duplicates\
      const existingIds = new Set(allResults.map(v => v.id));\
      const newResults = results2.filter(v => !existingIds.has(v.id));\
      allResults = [...allResults, ...newResults];\
    }\
    \
    // Strategy 3: Vendor only (if still few results)\
    if (allResults.length < 3 && asset.vendor) {\
      console.log(\`[CTI] Trying vendor-only search: \${asset.vendor}\`);\
      const results3 = await this.performKeywordSearch(asset.vendor, 5);\
      // Merge avoiding duplicates\
      const existingIds = new Set(allResults.map(v => v.id));\
      const newResults = results3.filter(v => !existingIds.has(v.id));\
      allResults = [...allResults, ...newResults];\
    }\
    \
    console.log(\`[CTI] Total keyword search results for \${asset.name}: \${allResults.length}\`);\
    return allResults.slice(0, 20); // Limit to 20 most relevant\
  }\
  \
  // Helper function to perform actual keyword search\
  async performKeywordSearch(keywords, maxResults = 10) {\
    if (!keywords) return [];\
    \
    const params = new URLSearchParams({\
      keywordSearch: keywords,\
      resultsPerPage: maxResults.toString(),\
      startIndex: '"'"'0'"'"'\
    });\
    \
    try {\
      const response = await fetch(\`\${this.nistBaseUrl}?\${params}\`, {\
        method: '"'"'GET'"'"',\
        headers: {\
          '"'"'Accept'"'"': '"'"'application/json'"'"',\
        }\
      });\
      \
      if (!response.ok) {\
        console.error(\`[CTI] NIST keyword search error: \${response.status} \${response.statusText}\`);\
        return [];\
      }\
      \
      const data = await response.json();\
      console.log(\`[CTI] Keyword "\${keywords}" results:\`, {\
        totalResults: data.totalResults,\
        vulnerabilitiesCount: data.vulnerabilities?.length || 0\
      });\
      \
      return this.parseVulnerabilities(data);\
    } catch (error) {\
      console.error(\`[CTI] Keyword search failed for "\${keywords}":\`, error);\
      return [];\
    }\
  }' "$FILE"

echo "Enhanced keyword search in ctiService.js"
