// src/pages/provider/CompanyActivities.jsx
import React, { useState } from 'react';

const CompanyActivities = () => {
  // Données simulées - à remplacer par des appels API réels plus tard
  const [activities, setActivities] = useState([
    { id: '1', timestamp: '2025-03-19T09:30:00', userId: '<EMAIL>', userName: '<PERSON>', actionType: 'USER_LOGIN', resourceType: 'user', ipAddress: '*************', details: {} },
    { id: '2', timestamp: '2025-03-18T14:45:00', userId: '<EMAIL>', userName: '<PERSON>', actionType: 'ANALYSIS_CREATE', resourceType: 'analysis', ipAddress: '*************', details: {} },
    { id: '3', timestamp: '2025-03-18T10:15:00', userId: '<EMAIL>', userName: '<PERSON>', actionType: 'USER_UPDATE', resourceType: 'user', ipAddress: '*************', details: {} },
    { id: '4', timestamp: '2025-03-17T16:30:00', userId: '<EMAIL>', userName: 'Bob <PERSON>', actionType: 'ANALYSIS_UPDATE', resourceType: 'analysis', ipAddress: '*************', details: {} }
  ]);

  const [users] = useState([
    { id: '', name: 'Tous les utilisateurs' },
    { id: '1', name: 'John Doe' },
    { id: '2', name: 'Jane Smith' },
    { id: '3', name: 'Bob Johnson' }
  ]);

  const [filters, setFilters] = useState({
    userId: '',
    actionType: '',
    resourceType: '',
    startDate: '',
    endDate: ''
  });

  const handleFilterChange = (e) => {
    const { name, value } = e.target;
    setFilters({
      ...filters,
      [name]: value
    });
  };

  const applyFilters = () => {
    // Cette fonction simule l'application des filtres
    // Dans une implémentation réelle, elle appellerait l'API avec les filtres
    console.log('Filtres appliqués:', filters);
    // Ici, nous pourrions filtrer les activités localement pour la démonstration
  };

  // Helper pour obtenir la couleur du badge en fonction du type d'action
  const getActionColor = (actionType) => {
    if (actionType.includes('LOGIN')) return 'bg-blue-100 text-blue-800';
    if (actionType.includes('CREATE')) return 'bg-green-100 text-green-800';
    if (actionType.includes('UPDATE')) return 'bg-yellow-100 text-yellow-800';
    if (actionType.includes('DELETE')) return 'bg-red-100 text-red-800';
    return 'bg-gray-100 text-gray-800';
  };

  return (
    <div className="p-6">
      <h1 className="text-2xl font-bold mb-6">Logs d'Activité de l'Entreprise</h1>
      
      {/* Filtres */}
      <div className="bg-white p-4 rounded-lg shadow-md mb-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Utilisateur
            </label>
            <select
              name="userId"
              value={filters.userId}
              onChange={handleFilterChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md"
            >
              {users.map((user) => (
                <option key={user.id} value={user.id}>
                  {user.name}
                </option>
              ))}
            </select>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Type d'action
            </label>
            <select
              name="actionType"
              value={filters.actionType}
              onChange={handleFilterChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md"
            >
              <option value="">Toutes les actions</option>
              <option value="USER_LOGIN">Connexion</option>
              <option value="USER_CREATE">Création utilisateur</option>
              <option value="ANALYSIS_CREATE">Création analyse</option>
              <option value="USER_UPDATE">Modification utilisateur</option>
              <option value="ANALYSIS_UPDATE">Modification analyse</option>
            </select>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Type de ressource
            </label>
            <select
              name="resourceType"
              value={filters.resourceType}
              onChange={handleFilterChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md"
            >
              <option value="">Toutes les ressources</option>
              <option value="user">Utilisateur</option>
              <option value="analysis">Analyse</option>
            </select>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Date de début
            </label>
            <input
              type="date"
              name="startDate"
              value={filters.startDate}
              onChange={handleFilterChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Date de fin
            </label>
            <input
              type="date"
              name="endDate"
              value={filters.endDate}
              onChange={handleFilterChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md"
            />
          </div>
        </div>
        
        <div className="mt-4">
          <button
            onClick={applyFilters}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
          >
            Appliquer les filtres
          </button>
        </div>
      </div>
      
      {/* Liste des activités */}
      <div className="bg-white rounded-lg shadow-md overflow-hidden">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Date
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Utilisateur
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Action
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Ressource
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                IP
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Détails
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {activities.map((activity) => (
              <tr key={activity.id}>
                <td className="px-6 py-4 whitespace-nowrap">
                  {new Date(activity.timestamp).toLocaleString()}
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  {activity.userName}
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <span
                    className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getActionColor(
                      activity.actionType
                    )}`}
                  >
                    {activity.actionType}
                  </span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  {activity.resourceType}
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  {activity.ipAddress}
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <button
                    onClick={() => {
                      // Afficher les détails dans une modal ou un tooltip
                      alert(JSON.stringify(activity.details, null, 2));
                    }}
                    className="text-blue-600 hover:text-blue-900"
                  >
                    Voir détails
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default CompanyActivities;