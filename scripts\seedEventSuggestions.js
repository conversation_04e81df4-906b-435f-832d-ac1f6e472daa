// scripts/seedEventSuggestions.js
const mongoose = require('mongoose');
const dotenv = require('dotenv');
const EventSuggestion = require('../models/EventSuggestion');

// Load environment variables
dotenv.config();

// Connect to MongoDB
mongoose.connect(process.env.MONGO_URI, {
  useNewUrlParser: true,
  useUnifiedTopology: true
})
.then(() => console.log('MongoDB Connected'))
.catch(err => {
  console.error('MongoDB Connection Error:', err);
  process.exit(1);
});

// Event suggestions data
const eventSuggestions = [
  // Confidentialité
  {
    name: "Divulgation de données sensibles",
    description: "Fuite accidentelle de données personnelles ou professionnelles.",
    securityPillar: "confidentialite",
    severity: "catastrophic",
    isDefault: true,
    impactFields: ["Impacts sur les missions et services de l’organisation", "Impacts humains, matériels ou environnementaux", "Impacts financiers", "Impacts juridiques", "Impacts sur l’image et la confiance"]
  },
  {
    name: "Interception réseau",
    description: "Interception de communications non chiffrées sur le réseau.",
    securityPillar: "confidentialite",
    severity: "moderate",
    isDefault: true,
    impactFields: ["Impacts sur les missions et services de l’organisation", "Impacts financiers", "Impacts juridiques", "Impacts sur l’image et la confiance"]
  },
  {
    name: "Espionnage industriel",
    description: "Collecte illégale d'informations concurrentielles.",
    securityPillar: "confidentialite",
    severity: "minor", // Note: Severity might be higher depending on context, but classified as minor here
    isDefault: true,
    impactFields: ["Impacts sur les missions et services de l’organisation", "Impacts financiers", "Impacts juridiques", "Impacts sur l’image et la confiance"]
  },
  {
    name: "Fuite interne",
    description: "Employé divulguant volontairement des informations confidentielles.",
    securityPillar: "confidentialite",
    severity: "critical",
    isDefault: true,
    impactFields: ["Impacts sur les missions et services de l’organisation", "Impacts humains, matériels ou environnementaux", "Impacts financiers", "Impacts juridiques", "Impacts sur l’image et la confiance", "Impacts sur la gouvernance"]
  },
  {
    name: "Exfiltration de base",
    description: "Extraction non autorisée de bases de données sensibles.",
    securityPillar: "confidentialite",
    severity: "moderate", // Note: Often Critical or Catastrophic
    isDefault: true,
    impactFields: ["Impacts sur les missions et services de l’organisation", "Impacts humains, matériels ou environnementaux", "Impacts financiers", "Impacts juridiques", "Impacts sur l’image et la confiance"]
  },
  {
    name: "Mauvais partage",
    description: "Documents sensibles partagés publiquement par erreur.",
    securityPillar: "confidentialite",
    severity: "critical",
    isDefault: true,
    impactFields: ["Impacts sur les missions et services de l’organisation", "Impacts humains, matériels ou environnementaux", "Impacts financiers", "Impacts juridiques", "Impacts sur l’image et la confiance"]
  },
  {
    name: "Accès non autorisé",
    description: "Connexion à un fichier confidentiel sans permission.",
    securityPillar: "confidentialite",
    severity: "major",
    isDefault: true,
    impactFields: ["Impacts sur les missions et services de l’organisation", "Impacts juridiques", "Impacts sur l’image et la confiance", "Impacts sur la gouvernance"]
  },
  {
    name: "Vol d'identifiants",
    description: "Utilisation d'identifiants volés pour accéder aux données.",
    securityPillar: "confidentialite",
    severity: "major",
    isDefault: true,
    impactFields: ["Impacts sur les missions et services de l’organisation", "Impacts humains, matériels ou environnementaux", "Impacts financiers", "Impacts juridiques", "Impacts sur l’image et la confiance"]
  },
  {
    name: "Perte support",
    description: "Disque ou clé USB contenant des données perdu.",
    securityPillar: "confidentialite",
    severity: "major",
    isDefault: true,
    impactFields: ["Impacts humains, matériels ou environnementaux", "Impacts financiers", "Impacts juridiques", "Impacts sur l’image et la confiance"]
  },
  {
    name: "Lecture email",
    description: "Consultation d'emails confidentiels via faille.",
    securityPillar: "confidentialite",
    severity: "critical",
    isDefault: true,
    impactFields: ["Impacts sur les missions et services de l’organisation", "Impacts financiers", "Impacts juridiques", "Impacts sur l’image et la confiance"]
  },
  {
    name: "Usurpation d'identité",
    description: "Utilisation d'identité d'un collègue pour obtenir des infos.",
    securityPillar: "confidentialite",
    severity: "catastrophic",
    isDefault: true,
    impactFields: ["Impacts sur les missions et services de l’organisation", "Impacts humains, matériels ou environnementaux", "Impacts financiers", "Impacts juridiques", "Impacts sur l’image et la confiance", "Impacts sur la gouvernance"]
  },
  {
    name: "Partage transversal",
    description: "Accès à des données par un département non concerné.",
    securityPillar: "confidentialite",
    severity: "catastrophic", // Note: Often Major or Critical, depends on sensitivity
    isDefault: true,
    impactFields: ["Impacts sur la gouvernance", "Impacts juridiques", "Impacts sur l’image et la confiance"]
  },
  {
    name: "Application tierce",
    description: "Fuite via une application interconnectée.",
    securityPillar: "confidentialite",
    severity: "moderate",
    isDefault: true,
    impactFields: ["Impacts sur les missions et services de l’organisation", "Impacts financiers", "Impacts juridiques", "Impacts sur l’image et la confiance"]
  },
  {
    name: "Pirate cloud",
    description: "Intrusion dans un compte cloud d'entreprise.",
    securityPillar: "confidentialite",
    severity: "critical",
    isDefault: true,
    impactFields: ["Impacts sur les missions et services de l’organisation", "Impacts humains, matériels ou environnementaux", "Impacts financiers", "Impacts juridiques", "Impacts sur l’image et la confiance"]
  },
  {
    name: "Vol malware",
    description: "Un logiciel malveillant exporte les données.",
    securityPillar: "confidentialite",
    severity: "catastrophic",
    isDefault: true,
    impactFields: ["Impacts sur les missions et services de l’organisation", "Impacts humains, matériels ou environnementaux", "Impacts financiers", "Impacts juridiques", "Impacts sur l’image et la confiance"]
  },
  {
    name: "Droits trop larges",
    description: "Droits d'accès mal configurés donnant accès à tout.",
    securityPillar: "confidentialite",
    severity: "catastrophic", // Note: Often Major or Critical, depends on sensitivity
    isDefault: true,
    impactFields: ["Impacts sur la gouvernance", "Impacts juridiques", "Impacts sur l’image et la confiance"]
  },
  {
    name: "Réutilisation mot de passe",
    description: "Un mot de passe exposé est réutilisé.",
    securityPillar: "confidentialite",
    severity: "minor", // Note: Can escalate quickly
    isDefault: true,
    impactFields: ["Impacts sur les missions et services de l’organisation", "Impacts humains, matériels ou environnementaux", "Impacts financiers", "Impacts juridiques", "Impacts sur l’image et la confiance"]
  },
  {
    name: "Stockage non sécurisé",
    description: "Fichiers sensibles stockés sans chiffrement.",
    securityPillar: "confidentialite",
    severity: "major",
    isDefault: true,
    impactFields: ["Impacts sur la gouvernance", "Impacts juridiques", "Impacts financiers", "Impacts sur l’image et la confiance"]
  },
  {
    name: "Interception appel",
    description: "Ecoute d'un appel contenant des infos critiques.",
    securityPillar: "confidentialite",
    severity: "moderate",
    isDefault: true,
    impactFields: ["Impacts sur les missions et services de l’organisation", "Impacts financiers", "Impacts juridiques", "Impacts sur l’image et la confiance"]
  },
  {
    name: "Sous-traitant malveillant",
    description: "Partenaire externe revend les informations.",
    securityPillar: "confidentialite",
    severity: "minor", // Note: Can be much higher depending on data
    isDefault: true,
    impactFields: ["Impacts sur les missions et services de l’organisation", "Impacts humains, matériels ou environnementaux", "Impacts financiers", "Impacts juridiques", "Impacts sur l’image et la confiance", "Impacts sur la gouvernance"]
  },

  // Intégrité
  {
    name: "Altération fichier critique",
    description: "Modification malveillante d'un fichier système.",
    securityPillar: "integrite",
    severity: "moderate",
    isDefault: true,
    impactFields: ["Impacts sur les missions et services de l’organisation", "Impacts humains, matériels ou environnementaux", "Impacts financiers", "Impacts sur la gouvernance"]
  },
  {
    name: "Fraude comptable",
    description: "Manipulation de données comptables.",
    securityPillar: "integrite",
    severity: "critical",
    isDefault: true,
    impactFields: ["Impacts financiers", "Impacts juridiques", "Impacts sur l’image et la confiance", "Impacts sur la gouvernance"]
  },
  {
    name: "Base clients corrompue",
    description: "Altération des données clients.",
    securityPillar: "integrite",
    severity: "minor", // Note: Can be higher depending on corruption level
    isDefault: true,
    impactFields: ["Impacts sur les missions et services de l’organisation", "Impacts humains, matériels ou environnementaux", "Impacts financiers", "Impacts sur l’image et la confiance"]
  },
  {
    name: "Falsification rapport",
    description: "Résultat d'audit falsifié.",
    securityPillar: "integrite",
    severity: "major",
    isDefault: true,
    impactFields: ["Impacts sur la gouvernance", "Impacts financiers", "Impacts juridiques", "Impacts sur l’image et la confiance"]
  },
  {
    name: "Règle modifiée",
    description: "Règle de sécurité modifiée sans autorisation.",
    securityPillar: "integrite",
    severity: "major",
    isDefault: true,
    impactFields: ["Impacts sur les missions et services de l’organisation", "Impacts sur la gouvernance", "Impacts financiers", "Impacts juridiques"]
  },
  {
    name: "Injection code",
    description: "Code malveillant ajouté à une application.",
    securityPillar: "integrite",
    severity: "catastrophic",
    isDefault: true,
    impactFields: ["Impacts sur les missions et services de l’organisation", "Impacts humains, matériels ou environnementaux", "Impacts financiers", "Impacts juridiques", "Impacts sur l’image et la confiance"]
  },
  {
    name: "Malware modifiant données",
    description: "Logiciel modifie fichiers internes.",
    securityPillar: "integrite",
    severity: "critical",
    isDefault: true,
    impactFields: ["Impacts sur les missions et services de l’organisation", "Impacts humains, matériels ou environnementaux", "Impacts financiers", "Impacts juridiques", "Impacts sur l’image et la confiance"]
  },
  {
    name: "Fichier corrompu réseau",
    description: "Transfert endommagé de fichiers.",
    securityPillar: "integrite",
    severity: "minor",
    isDefault: true,
    impactFields: ["Impacts sur les missions et services de l’organisation", "Impacts financiers"]
  },
  {
    name: "Droits modifiés",
    description: "Modification non autorisée de permissions.",
    securityPillar: "integrite",
    severity: "minor", // Note: Can be higher
    isDefault: true,
    impactFields: ["Impacts sur la gouvernance", "Impacts juridiques"]
  },
  {
    name: "Logs altérés",
    description: "Modification des journaux système.",
    securityPillar: "integrite",
    severity: "moderate",
    isDefault: true,
    impactFields: ["Impacts sur la gouvernance", "Impacts juridiques", "Impacts sur l’image et la confiance"]
  },
  {
    name: "Bug logiciel",
    description: "Erreur écrivant des données erronées.",
    securityPillar: "integrite",
    severity: "minor",
    isDefault: true,
    impactFields: ["Impacts sur les missions et services de l’organisation", "Impacts financiers", "Impacts sur l’image et la confiance"]
  },
  {
    name: "Changement manuel",
    description: "Modification humaine involontaire.",
    securityPillar: "integrite",
    severity: "critical", // Note: Depends heavily on what was changed
    isDefault: true,
    impactFields: ["Impacts sur les missions et services de l’organisation", "Impacts financiers", "Impacts sur l’image et la confiance", "Impacts sur la gouvernance"]
  },
  {
    name: "Messages modifiés",
    description: "Contenu altéré dans une communication.",
    securityPillar: "integrite",
    severity: "major",
    isDefault: true,
    impactFields: ["Impacts sur les missions et services de l’organisation", "Impacts humains, matériels ou environnementaux", "Impacts financiers", "Impacts juridiques", "Impacts sur l’image et la confiance"]
  },
  {
    name: "Sync incorrecte",
    description: "Mauvaise synchronisation entre bases.",
    securityPillar: "integrite",
    severity: "moderate",
    isDefault: true,
    impactFields: ["Impacts sur les missions et services de l’organisation", "Impacts financiers"]
  },
  {
    name: "Mise à jour corrompue",
    description: "Mise à jour altérant l'intégrité.",
    securityPillar: "integrite",
    severity: "moderate",
    isDefault: true,
    impactFields: ["Impacts sur les missions et services de l’organisation", "Impacts financiers", "Impacts sur la gouvernance"]
  },
  {
    name: "Règle pare-feu modifiée",
    description: "Pare-feu reconfiguré par erreur.",
    securityPillar: "integrite",
    severity: "moderate", // Can easily be higher
    isDefault: true,
    impactFields: ["Impacts sur les missions et services de l’organisation", "Impacts financiers", "Impacts sur la gouvernance", "Impacts juridiques"]
  },
  {
    name: "Document falsifié",
    description: "Création d'un faux document numérique.",
    securityPillar: "integrite",
    severity: "catastrophic",
    isDefault: true,
    impactFields: ["Impacts sur les missions et services de l’organisation", "Impacts financiers", "Impacts juridiques", "Impacts sur l’image et la confiance", "Impacts sur la gouvernance"]
  },
  {
    name: "Transaction détournée",
    description: "Montant bancaire modifié par virus.",
    securityPillar: "integrite",
    severity: "catastrophic",
    isDefault: true,
    impactFields: ["Impacts financiers", "Impacts juridiques", "Impacts sur l’image et la confiance"]
  },
  {
    name: "Mauvaise config",
    description: "Configuration système corrompant les données.",
    securityPillar: "integrite",
    severity: "minor", // Can be higher
    isDefault: true,
    impactFields: ["Impacts sur les missions et services de l’organisation", "Impacts financiers", "Impacts sur la gouvernance"]
  },
  {
    name: "Code reprogrammé",
    description: "Un système est reprogrammé à distance.",
    securityPillar: "integrite",
    severity: "minor", // Can be higher
    isDefault: true,
    impactFields: ["Impacts sur les missions et services de l’organisation", "Impacts financiers", "Impacts juridiques", "Impacts sur la gouvernance"]
  },

  // Disponibilité
  {
    name: "Panne serveur",
    description: "Indisponibilité totale des services suite à une panne.",
    securityPillar: "disponibilite",
    severity: "catastrophic",
    isDefault: true,
    impactFields: ["Impacts sur les missions et services de l’organisation", "Impacts humains, matériels ou environnementaux", "Impacts financiers", "Impacts juridiques", "Impacts sur l’image et la confiance"]
  },
  {
    name: "Surcharge réseau",
    description: "Trop de trafic empêchant l'accès.",
    securityPillar: "disponibilite",
    severity: "major",
    isDefault: true,
    impactFields: ["Impacts sur les missions et services de l’organisation", "Impacts financiers", "Impacts sur l’image et la confiance"]
  },
  {
    name: "DDoS",
    description: "Attaque de déni de service massif.",
    securityPillar: "disponibilite",
    severity: "critical",
    isDefault: true,
    impactFields: ["Impacts sur les missions et services de l’organisation", "Impacts financiers", "Impacts juridiques", "Impacts sur l’image et la confiance"]
  },
  {
    name: "Piratage cloud",
    description: "Indisponibilité due à une attaque cloud.",
    securityPillar: "disponibilite",
    severity: "critical",
    isDefault: true,
    impactFields: ["Impacts sur les missions et services de l’organisation", "Impacts financiers", "Impacts juridiques", "Impacts sur l’image et la confiance"]
  },
  {
    name: "Panne électricité",
    description: "Coupure de courant non prévue.",
    securityPillar: "disponibilite",
    severity: "critical",
    isDefault: true,
    impactFields: ["Impacts sur les missions et services de l’organisation", "Impacts humains, matériels ou environnementaux", "Impacts financiers", "Impacts juridiques"]
  },
  {
    name: "Erreur planification",
    description: "Mise hors ligne par erreur.",
    securityPillar: "disponibilite",
    severity: "major",
    isDefault: true,
    impactFields: ["Impacts sur les missions et services de l’organisation", "Impacts financiers", "Impacts sur la gouvernance", "Impacts sur l’image et la confiance"]
  },
  {
    name: "Maintenance prolongée",
    description: "Durée de maintenance anormalement longue.",
    securityPillar: "disponibilite",
    severity: "catastrophic", // Depends on service criticality
    isDefault: true,
    impactFields: ["Impacts sur les missions et services de l’organisation", "Impacts financiers", "Impacts juridiques", "Impacts sur l’image et la confiance", "Impacts sur la gouvernance"]
  },
  {
    name: "Incident climat",
    description: "Tempête provoquant arrêt des équipements.",
    securityPillar: "disponibilite",
    severity: "catastrophic",
    isDefault: true,
    impactFields: ["Impacts sur les missions et services de l’organisation", "Impacts humains, matériels ou environnementaux", "Impacts financiers", "Impacts juridiques"]
  },
  {
    name: "Surcharge disque",
    description: "Plus d'espace pour stocker.",
    securityPillar: "disponibilite",
    severity: "catastrophic", // Depends on affected service
    isDefault: true,
    impactFields: ["Impacts sur les missions et services de l’organisation", "Impacts humains, matériels ou environnementaux", "Impacts financiers", "Impacts sur l’image et la confiance"]
  },
  {
    name: "Mise à jour ratée",
    description: "Mise à jour provoquant un crash.",
    securityPillar: "disponibilite",
    severity: "critical",
    isDefault: true,
    impactFields: ["Impacts sur les missions et services de l’organisation", "Impacts financiers", "Impacts sur l’image et la confiance", "Impacts sur la gouvernance"]
  },
  {
    name: "Panne sauvegarde",
    description: "Aucune restauration possible.",
    securityPillar: "disponibilite", // Also Integrity/Confidentiality if data lost
    severity: "major", // Often higher, critical for recovery
    isDefault: true,
    impactFields: ["Impacts sur les missions et services de l’organisation", "Impacts financiers", "Impacts juridiques", "Impacts sur la gouvernance"]
  },
  {
    name: "Défaillance réseau",
    description: "Connexion interrompue.",
    securityPillar: "disponibilite",
    severity: "catastrophic", // Depends on scope
    isDefault: true,
    impactFields: ["Impacts sur les missions et services de l’organisation", "Impacts financiers", "Impacts humains, matériels ou environnementaux", "Impacts sur l’image et la confiance"]
  },
  {
    name: "Virus destructeur",
    description: "Supprime les fichiers critiques.",
    securityPillar: "disponibilite", // Also Integrity
    severity: "catastrophic",
    isDefault: true,
    impactFields: ["Impacts sur les missions et services de l’organisation", "Impacts humains, matériels ou environnementaux", "Impacts financiers", "Impacts juridiques", "Impacts sur l’image et la confiance"]
  },
  {
    name: "Incident datacenter",
    description: "Centre de données hors ligne.",
    securityPillar: "disponibilite",
    severity: "critical",
    isDefault: true,
    impactFields: ["Impacts sur les missions et services de l’organisation", "Impacts humains, matériels ou environnementaux", "Impacts financiers", "Impacts juridiques", "Impacts sur l’image et la confiance"]
  },
  {
    name: "Application bloquée",
    description: "Logiciel essentiel inutilisable.",
    securityPillar: "disponibilite",
    severity: "moderate",
    isDefault: true,
    impactFields: ["Impacts sur les missions et services de l’organisation", "Impacts financiers", "Impacts humains, matériels ou environnementaux"]
  },
  {
    name: "Crash matériel",
    description: "Serveur grillé suite à défaut.",
    securityPillar: "disponibilite",
    severity: "catastrophic", // Depends on server role
    isDefault: true,
    impactFields: ["Impacts sur les missions et services de l’organisation", "Impacts humains, matériels ou environnementaux", "Impacts financiers", "Impacts juridiques"]
  },
  {
    name: "Perte internet",
    description: "Connexion réseau non disponible.",
    securityPillar: "disponibilite",
    severity: "minor", // Can be higher for cloud-dependent orgs
    isDefault: true,
    impactFields: ["Impacts sur les missions et services de l’organisation", "Impacts financiers"]
  },
  {
    name: "Bug applicatif",
    description: "Erreur empêchant l'utilisation.",
    securityPillar: "disponibilite",
    severity: "moderate",
    isDefault: true,
    impactFields: ["Impacts sur les missions et services de l’organisation", "Impacts financiers", "Impacts humains, matériels ou environnementaux", "Impacts sur l’image et la confiance"]
  },
  {
    name: "Perte VM",
    description: "Machine virtuelle supprimée.",
    securityPillar: "disponibilite",
    severity: "minor", // Can be higher
    isDefault: true,
    impactFields: ["Impacts sur les missions et services de l’organisation", "Impacts financiers", "Impacts humains, matériels ou environnementaux"]
  },
  {
    name: "Erreur humaine",
    description: "Mauvaise commande supprimant service.",
    securityPillar: "disponibilite",
    severity: "minor", // Can be higher
    isDefault: true,
    impactFields: ["Impacts sur les missions et services de l’organisation", "Impacts financiers", "Impacts sur la gouvernance", "Impacts humains, matériels ou environnementaux"]
  },

  // Traçabilité
  {
    name: "Logs absents",
    description: "Pas de traces sur un incident.",
    securityPillar: "tracabilite",
    severity: "critical",
    isDefault: true,
    impactFields: ["Impacts sur la gouvernance", "Impacts juridiques", "Impacts financiers"] // Hinders investigation/resolution
  },
  {
    name: "Effacement logs",
    description: "Suppression volontaire des journaux.",
    securityPillar: "tracabilite",
    severity: "moderate", // Often critical if malicious
    isDefault: true,
    impactFields: ["Impacts sur la gouvernance", "Impacts juridiques", "Impacts financiers", "Impacts sur l’image et la confiance"]
  },
  {
    name: "Logs corrompus",
    description: "Journaux modifiés ou illisibles.",
    securityPillar: "tracabilite", // Also Integrity
    severity: "major",
    isDefault: true,
    impactFields: ["Impacts sur la gouvernance", "Impacts juridiques", "Impacts financiers"]
  },
  {
    name: "Journalisation désactivée",
    description: "Fonction de logs désactivée.",
    securityPillar: "tracabilite",
    severity: "catastrophic",
    isDefault: true,
    impactFields: ["Impacts sur la gouvernance", "Impacts juridiques", "Impacts financiers"]
  },
  {
    name: "Traces partielles",
    description: "Logs incomplets sur certaines actions.",
    securityPillar: "tracabilite",
    severity: "critical",
    isDefault: true,
    impactFields: ["Impacts sur la gouvernance", "Impacts juridiques", "Impacts financiers"]
  },
  {
    name: "Défaut horodatage",
    description: "Heure incorrecte dans les journaux.",
    securityPillar: "tracabilite",
    severity: "catastrophic", // Makes correlation impossible
    isDefault: true,
    impactFields: ["Impacts sur la gouvernance", "Impacts juridiques", "Impacts financiers"]
  },
  {
    name: "Collecte incohérente",
    description: "Format ou structure erroné.",
    securityPillar: "tracabilite",
    severity: "critical",
    isDefault: true,
    impactFields: ["Impacts sur la gouvernance", "Impacts financiers"]
  },
  {
    name: "Logs surchargés",
    description: "Données noyées dans le volume.",
    securityPillar: "tracabilite",
    severity: "minor",
    isDefault: true,
    impactFields: ["Impacts sur la gouvernance", "Impacts financiers"] // Makes analysis difficult
  },
  {
    name: "Logs dispersés",
    description: "Impossible de corréler plusieurs sources.",
    securityPillar: "tracabilite",
    severity: "critical",
    isDefault: true,
    impactFields: ["Impacts sur la gouvernance", "Impacts financiers", "Impacts juridiques"]
  },
  {
    name: "Pas de journalisation réseau",
    description: "Aucune trace du trafic.",
    securityPillar: "tracabilite",
    severity: "major",
    isDefault: true,
    impactFields: ["Impacts sur la gouvernance", "Impacts juridiques", "Impacts financiers"]
  },
  {
    name: "Oubli logs sécurité",
    description: "Pas de trace des accès critiques.",
    securityPillar: "tracabilite",
    severity: "moderate", // Often higher
    isDefault: true,
    impactFields: ["Impacts sur la gouvernance", "Impacts juridiques", "Impacts financiers"]
  },
  {
    name: "Logs non centralisés",
    description: "Pas de SIEM pour collecter.",
    securityPillar: "tracabilite",
    severity: "catastrophic", // Hinders global view
    isDefault: true,
    impactFields: ["Impacts sur la gouvernance", "Impacts financiers", "Impacts juridiques"]
  },
  {
    name: "Erreur d'analyse",
    description: "Logs interprétés de façon erronée.",
    securityPillar: "tracabilite",
    severity: "major",
    isDefault: true,
    impactFields: ["Impacts sur la gouvernance", "Impacts financiers", "Impacts juridiques", "Impacts sur les missions et services de l’organisation"] // Leads to wrong decisions
  },
  {
    name: "Logs non protégés",
    description: "Modifiables sans autorisation.",
    securityPillar: "tracabilite", // Also Integrity
    severity: "catastrophic",
    isDefault: true,
    impactFields: ["Impacts sur la gouvernance", "Impacts juridiques", "Impacts financiers", "Impacts sur l’image et la confiance"]
  },
  {
    name: "Fuite journaux",
    description: "Logs exposés publiquement.",
    securityPillar: "tracabilite", // Also Confidentiality
    severity: "major",
    isDefault: true,
    impactFields: ["Impacts sur la gouvernance", "Impacts juridiques", "Impacts financiers", "Impacts sur l’image et la confiance"]
  },
  {
    name: "Logs non sauvegardés",
    description: "Perdus après redémarrage.",
    securityPillar: "tracabilite", // Also Availability
    severity: "critical",
    isDefault: true,
    impactFields: ["Impacts sur la gouvernance", "Impacts juridiques", "Impacts financiers"]
  },
  {
    name: "Pas d'alerte",
    description: "Anomalie détectée mais ignorée.",
    securityPillar: "tracabilite",
    severity: "catastrophic",
    isDefault: true,
    impactFields: ["Impacts sur la gouvernance", "Impacts financiers", "Impacts juridiques", "Impacts sur les missions et services de l’organisation", "Impacts sur l’image et la confiance"] // Allows incidents to worsen
  },
  {
    name: "Agent désactivé",
    description: "Logiciel de suivi arrêté.",
    securityPillar: "tracabilite",
    severity: "critical",
    isDefault: true,
    impactFields: ["Impacts sur la gouvernance", "Impacts juridiques", "Impacts financiers"]
  },
  {
    name: "Perte logs incidents",
    description: "Traces supprimées accidentellement.",
    securityPillar: "tracabilite",
    severity: "catastrophic",
    isDefault: true,
    impactFields: ["Impacts sur la gouvernance", "Impacts juridiques", "Impacts financiers"]
  },
  {
    name: "Traçabilité mobile absente",
    description: "Rien sur les connexions mobiles.",
    securityPillar: "tracabilite",
    severity: "major",
    isDefault: true,
    impactFields: ["Impacts sur la gouvernance", "Impacts juridiques", "Impacts financiers"]
  },

  // Preuve
  {
    name: "Éléments supprimés",
    description: "Preuves effacées avant analyse.",
    securityPillar: "preuve",
    severity: "critical",
    isDefault: true,
    impactFields: ["Impacts juridiques", "Impacts sur la gouvernance", "Impacts financiers"]
  },
  {
    name: "Chaines altérées",
    description: "Lien entre preuves brisé.",
    securityPillar: "preuve",
    severity: "major",
    isDefault: true,
    impactFields: ["Impacts juridiques", "Impacts sur la gouvernance"]
  },
  {
    name: "Capture écran manquante",
    description: "Pas de preuve visuelle.",
    securityPillar: "preuve",
    severity: "minor",
    isDefault: true,
    impactFields: ["Impacts juridiques", "Impacts sur la gouvernance"]
  },
  {
    name: "Trace non fiable",
    description: "Fichier modifiable sans signature.",
    securityPillar: "preuve", // Also Integrity
    severity: "catastrophic",
    isDefault: true,
    impactFields: ["Impacts juridiques", "Impacts sur la gouvernance", "Impacts financiers", "Impacts sur l’image et la confiance"]
  },
  {
    name: "Absence métadonnées",
    description: "Données sans auteur ni date.",
    securityPillar: "preuve",
    severity: "catastrophic",
    isDefault: true,
    impactFields: ["Impacts juridiques", "Impacts sur la gouvernance"]
  },
  {
    name: "Aucune copie",
    description: "Pas de double sécurisé.",
    securityPillar: "preuve", // Also Availability
    severity: "catastrophic",
    isDefault: true,
    impactFields: ["Impacts juridiques", "Impacts sur la gouvernance", "Impacts financiers"]
  },
  {
    name: "Support cassé",
    description: "Preuve inaccessible physiquement.",
    securityPillar: "preuve", // Also Availability
    severity: "critical",
    isDefault: true,
    impactFields: ["Impacts juridiques", "Impacts sur la gouvernance", "Impacts financiers", "Impacts humains, matériels ou environnementaux"]
  },
  {
    name: "Fichier non signé",
    description: "Sans garantie d'authenticité.",
    securityPillar: "preuve",
    severity: "major",
    isDefault: true,
    impactFields: ["Impacts juridiques", "Impacts sur la gouvernance", "Impacts financiers"]
  },
  {
    name: "Document falsifié",
    description: "Preuve modifiée numériquement.",
    securityPillar: "preuve", // Also Integrity
    severity: "critical",
    isDefault: true,
    impactFields: ["Impacts juridiques", "Impacts sur la gouvernance", "Impacts financiers", "Impacts sur l’image et la confiance"]
  },
  {
    name: "Capture non horodatée",
    description: "Preuve sans date certaine.",
    securityPillar: "preuve",
    severity: "moderate",
    isDefault: true,
    impactFields: ["Impacts juridiques", "Impacts sur la gouvernance"]
  },
  {
    name: "Disque formaté",
    description: "Effacement définitif d'éléments.",
    securityPillar: "preuve",
    severity: "moderate", // Often higher
    isDefault: true,
    impactFields: ["Impacts juridiques", "Impacts sur la gouvernance", "Impacts financiers", "Impacts humains, matériels ou environnementaux"]
  },
  {
    name: "Témoin non vérifié",
    description: "Aucune preuve externe.",
    securityPillar: "preuve",
    severity: "major",
    isDefault: true,
    impactFields: ["Impacts juridiques", "Impacts sur la gouvernance"]
  },
  {
    name: "Rejet judiciaire",
    description: "Preuve non recevable légalement.",
    securityPillar: "preuve",
    severity: "critical",
    isDefault: true,
    impactFields: ["Impacts juridiques", "Impacts financiers", "Impacts sur la gouvernance", "Impacts sur l’image et la confiance"]
  },
  {
    name: "Absence enregistrement",
    description: "Pas de trace audio/vidéo.",
    securityPillar: "preuve",
    severity: "critical",
    isDefault: true,
    impactFields: ["Impacts juridiques", "Impacts sur la gouvernance"]
  },
  {
    name: "Traces incompatibles",
    description: "Preuves contradictoires.",
    securityPillar: "preuve",
    severity: "catastrophic",
    isDefault: true,
    impactFields: ["Impacts juridiques", "Impacts sur la gouvernance", "Impacts sur l’image et la confiance"]
  },
  {
    name: "Mail supprimé",
    description: "Message clé introuvable.",
    securityPillar: "preuve",
    severity: "major",
    isDefault: true,
    impactFields: ["Impacts juridiques", "Impacts sur la gouvernance", "Impacts financiers"]
  },
  {
    name: "Message non archivé",
    description: "Conversation effacée.",
    securityPillar: "preuve",
    severity: "major",
    isDefault: true,
    impactFields: ["Impacts juridiques", "Impacts sur la gouvernance", "Impacts financiers"]
  },
  {
    name: "Fichier corrompu",
    description: "Impossible d'ouvrir la preuve.",
    securityPillar: "preuve", // Also Integrity/Availability
    severity: "minor", // Can be higher
    isDefault: true,
    impactFields: ["Impacts juridiques", "Impacts sur la gouvernance", "Impacts financiers"]
  },
  {
    name: "Pas d'authentification",
    description: "Identité non prouvée.",
    securityPillar: "preuve",
    severity: "critical",
    isDefault: true,
    impactFields: ["Impacts juridiques", "Impacts sur la gouvernance", "Impacts financiers", "Impacts sur l’image et la confiance"]
  },
  {
    name: "Fichier temporaire supprimé",
    description: "Disparition automatique d'une preuve.",
    securityPillar: "preuve",
    severity: "moderate",
    isDefault: true,
    impactFields: ["Impacts juridiques", "Impacts sur la gouvernance"]
  },

  // Auditabilité
  {
    name: "Non-conformité processus",
    description: "Audit révèle procédure absente.",
    securityPillar: "Auditabilite",
    severity: "major",
    isDefault: true,
    impactFields: ["Impacts sur la gouvernance", "Impacts juridiques", "Impacts financiers", "Impacts sur l’image et la confiance"]
  },
  {
    name: "Absence de contrôle",
    description: "Aucune vérification prévue.",
    securityPillar: "Auditabilite",
    severity: "catastrophic",
    isDefault: true,
    impactFields: ["Impacts sur la gouvernance", "Impacts juridiques", "Impacts financiers", "Impacts sur l’image et la confiance", "Impacts sur les missions et services de l’organisation"]
  },
  {
    name: "Audit non faisable",
    description: "Système trop opaque.",
    securityPillar: "Auditabilite",
    severity: "catastrophic",
    isDefault: true,
    impactFields: ["Impacts sur la gouvernance", "Impacts juridiques", "Impacts financiers", "Impacts sur l’image et la confiance"]
  },
  {
    name: "Pas d'historique",
    description: "Impossible de revenir dans le temps.",
    securityPillar: "Auditabilite", // Also Traceability
    severity: "critical",
    isDefault: true,
    impactFields: ["Impacts sur la gouvernance", "Impacts juridiques", "Impacts financiers"]
  },
  {
    name: "Documentation absente",
    description: "Pas de référence d'audit.",
    securityPillar: "Auditabilite",
    severity: "minor", // Can be higher
    isDefault: true,
    impactFields: ["Impacts sur la gouvernance", "Impacts juridiques", "Impacts financiers"]
  },
  {
    name: "Mauvais archivage",
    description: "Données d'audit mal classées.",
    securityPillar: "Auditabilite",
    severity: "catastrophic", // Makes audit findings hard to use/verify
    isDefault: true,
    impactFields: ["Impacts sur la gouvernance", "Impacts juridiques", "Impacts financiers"]
  },
  {
    name: "Données effacées",
    description: "Perte des éléments vérifiables.",
    securityPillar: "Auditabilite", // Also Traceability/Preuve
    severity: "minor", // Often higher
    isDefault: true,
    impactFields: ["Impacts sur la gouvernance", "Impacts juridiques", "Impacts financiers"]
  },
  {
    name: "Audit refusé",
    description: "Système refuse d'être audité.",
    securityPillar: "Auditabilite",
    severity: "critical",
    isDefault: true,
    impactFields: ["Impacts sur la gouvernance", "Impacts juridiques", "Impacts financiers", "Impacts sur l’image et la confiance"]
  },
  {
    name: "Anomalies ignorées",
    description: "Défauts non signalés.",
    securityPillar: "Auditabilite",
    severity: "moderate", // Often higher
    isDefault: true,
    impactFields: ["Impacts sur la gouvernance", "Impacts juridiques", "Impacts financiers", "Impacts sur l’image et la confiance", "Impacts sur les missions et services de l’organisation"]
  },
  {
    name: "Événements non tracés",
    description: "Pas de suivi d'actions.",
    securityPillar: "Auditabilite", // Also Traceability
    severity: "major",
    isDefault: true,
    impactFields: ["Impacts sur la gouvernance", "Impacts juridiques", "Impacts financiers"]
  },
  {
    name: "Audit tronqué",
    description: "Informations manquantes.",
    securityPillar: "Auditabilite",
    severity: "minor", // Can be higher
    isDefault: true,
    impactFields: ["Impacts sur la gouvernance", "Impacts juridiques", "Impacts financiers", "Impacts sur l’image et la confiance"]
  },
  {
    name: "Rapport non disponible",
    description: "Audit non publié.",
    securityPillar: "Auditabilite",
    severity: "moderate",
    isDefault: true,
    impactFields: ["Impacts sur la gouvernance", "Impacts juridiques", "Impacts financiers", "Impacts sur l’image et la confiance"]
  },
  {
    name: "Absence indicateurs",
    description: "Pas de KPIs disponibles.",
    securityPillar: "Auditabilite",
    severity: "moderate",
    isDefault: true,
    impactFields: ["Impacts sur la gouvernance", "Impacts financiers"] // Hard to measure effectiveness
  },
  {
    name: "Échantillon erroné",
    description: "Mauvais périmètre audité.",
    securityPillar: "Auditabilite",
    severity: "major",
    isDefault: true,
    impactFields: ["Impacts sur la gouvernance", "Impacts juridiques", "Impacts financiers", "Impacts sur l’image et la confiance"] // Gives false sense of security/compliance
  },
  {
    name: "Audit externe impossible",
    description: "Pas d'accès pour auditeurs.",
    securityPillar: "Auditabilite",
    severity: "minor", // Often critical for certifications/compliance
    isDefault: true,
    impactFields: ["Impacts sur la gouvernance", "Impacts juridiques", "Impacts financiers", "Impacts sur l’image et la confiance"]
  },
  {
    name: "Conflit d'intérêt",
    description: "Auditeur interne biaisé.",
    securityPillar: "Auditabilite",
    severity: "catastrophic",
    isDefault: true,
    impactFields: ["Impacts sur la gouvernance", "Impacts juridiques", "Impacts financiers", "Impacts sur l’image et la confiance"]
  },
  {
    name: "Chiffres incohérents",
    description: "Résultats non fiables.",
    securityPillar: "Auditabilite",
    severity: "moderate",
    isDefault: true,
    impactFields: ["Impacts sur la gouvernance", "Impacts financiers", "Impacts sur l’image et la confiance"]
  },
  {
    name: "Erreur méthode",
    description: "Protocole non conforme.",
    securityPillar: "Auditabilite",
    severity: "major",
    isDefault: true,
    impactFields: ["Impacts sur la gouvernance", "Impacts juridiques", "Impacts financiers", "Impacts sur l’image et la confiance"]
  },
  {
    name: "Règlement non appliqué",
    description: "Audit montre une violation.",
    securityPillar: "Auditabilite",
    severity: "major",
    isDefault: true,
    impactFields: ["Impacts sur la gouvernance", "Impacts juridiques", "Impacts financiers", "Impacts sur l’image et la confiance"]
  },
  {
    name: "Résistance interne",
    description: "Refus de collaboration.",
    securityPillar: "Auditabilite",
    severity: "minor", // Can hinder significantly
    isDefault: true,
    impactFields: ["Impacts sur la gouvernance", "Impacts financiers", "Impacts sur l’image et la confiance"]
  }
];



// Function to convert impact fields to the correct format
const convertImpactFields = (suggestions) => {
  return suggestions.map(suggestion => {
    // Create a new object without the impactFields property
    const { impactFields, ...newSuggestion } = suggestion;

    // Map the impact fields to the correct format
    if (impactFields && impactFields.length > 0) {
      const impactMap = {
        "Impacts sur les missions et services de l'organisation": "missions_services",
        "Impacts humains, matériels ou environnementaux": "humain_materiel_environnemental",
        "Impacts sur la gouvernance": "gouvernance",
        "Impacts financiers": "financier",
        "Impacts juridiques": "juridique",
        "Impacts sur l'image et la confiance": "image_confiance"
      };

      // Convert each impact field to its corresponding value
      newSuggestion.impacts = [];
      impactFields.forEach(field => {
        const mappedValue = impactMap[field];
        if (mappedValue) {
          newSuggestion.impacts.push(mappedValue);
        }
      });
    } else {
      newSuggestion.impacts = [];
    }

    console.log(`Converted impacts for ${suggestion.name}:`, newSuggestion.impacts);
    return newSuggestion;
  });
};

// Function to seed the database
const seedDatabase = async () => {
  try {
    // Clear existing data
    await EventSuggestion.deleteMany({ isDefault: true });
    console.log('Cleared existing default event suggestions');

    // Convert impact fields to the correct format
    const formattedSuggestions = convertImpactFields(eventSuggestions);

    // Insert new data
    const result = await EventSuggestion.insertMany(formattedSuggestions);
    console.log(`Successfully seeded ${result.length} event suggestions`);

    // Disconnect from MongoDB
    mongoose.disconnect();
    console.log('MongoDB Disconnected');
  } catch (error) {
    console.error('Error seeding database:', error);
    process.exit(1);
  }
};

// Run the seed function
seedDatabase();
