/* Modern Design System for EBIOS RM */

/* CSS Variables for consistent theming */
:root {
  /* Primary Colors */
  --primary-50: #eff6ff;
  --primary-100: #dbeafe;
  --primary-200: #bfdbfe;
  --primary-300: #93c5fd;
  --primary-400: #60a5fa;
  --primary-500: #3b82f6;
  --primary-600: #2563eb;
  --primary-700: #1d4ed8;
  --primary-800: #1e40af;
  --primary-900: #1e3a8a;

  /* Secondary Colors */
  --secondary-50: #f8fafc;
  --secondary-100: #f1f5f9;
  --secondary-200: #e2e8f0;
  --secondary-300: #cbd5e1;
  --secondary-400: #94a3b8;
  --secondary-500: #64748b;
  --secondary-600: #475569;
  --secondary-700: #334155;
  --secondary-800: #1e293b;
  --secondary-900: #0f172a;

  /* Success Colors */
  --success-50: #f0fdf4;
  --success-100: #dcfce7;
  --success-500: #22c55e;
  --success-600: #16a34a;

  /* Warning Colors */
  --warning-50: #fffbeb;
  --warning-100: #fef3c7;
  --warning-500: #f59e0b;
  --warning-600: #d97706;

  /* Error Colors */
  --error-50: #fef2f2;
  --error-100: #fee2e2;
  --error-500: #ef4444;
  --error-600: #dc2626;

  /* Spacing */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --spacing-2xl: 3rem;

  /* Border Radius */
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
  --radius-2xl: 1.5rem;

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);

  /* Typography */
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 1.875rem;
  --font-size-4xl: 2.25rem;
}

/* Modern Card Component */
.modern-card {
  background: white;
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--secondary-200);
  transition: all 0.2s ease-in-out;
  overflow: hidden;
}

.modern-card:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
}

.modern-card-header {
  padding: var(--spacing-lg);
  border-bottom: 1px solid var(--secondary-100);
  background: linear-gradient(135deg, var(--secondary-50) 0%, white 100%);
}

.modern-card-body {
  padding: var(--spacing-lg);
}

.modern-card-footer {
  padding: var(--spacing-lg);
  border-top: 1px solid var(--secondary-100);
  background: var(--secondary-50);
}

/* Modern Button System */
.modern-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-sm) var(--spacing-lg);
  border-radius: var(--radius-lg);
  font-weight: 500;
  font-size: var(--font-size-sm);
  transition: all 0.2s ease-in-out;
  cursor: pointer;
  border: none;
  text-decoration: none;
  gap: var(--spacing-sm);
}

.modern-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.modern-btn-primary {
  background: linear-gradient(135deg, var(--primary-500) 0%, var(--primary-600) 100%);
  color: white;
  box-shadow: var(--shadow-sm);
}

.modern-btn-primary:hover:not(:disabled) {
  background: linear-gradient(135deg, var(--primary-600) 0%, var(--primary-700) 100%);
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
}

.modern-btn-secondary {
  background: white;
  color: var(--secondary-700);
  border: 1px solid var(--secondary-300);
}

.modern-btn-secondary:hover:not(:disabled) {
  background: var(--secondary-50);
  border-color: var(--secondary-400);
}

.modern-btn-success {
  background: linear-gradient(135deg, var(--success-500) 0%, var(--success-600) 100%);
  color: white;
}

.modern-btn-warning {
  background: linear-gradient(135deg, var(--warning-500) 0%, var(--warning-600) 100%);
  color: white;
}

.modern-btn-error {
  background: linear-gradient(135deg, var(--error-500) 0%, var(--error-600) 100%);
  color: white;
}

/* Modern Input System */
.modern-input {
  width: 100%;
  padding: var(--spacing-sm) var(--spacing-md);
  border: 1px solid var(--secondary-300);
  border-radius: var(--radius-md);
  font-size: var(--font-size-sm);
  transition: all 0.2s ease-in-out;
  background: white;
}

.modern-input:focus {
  outline: none;
  border-color: var(--primary-500);
  box-shadow: 0 0 0 3px var(--primary-100);
}

.modern-input:disabled {
  background: var(--secondary-100);
  color: var(--secondary-500);
}

/* Modern Badge System */
.modern-badge {
  display: inline-flex;
  align-items: center;
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-md);
  font-size: var(--font-size-xs);
  font-weight: 500;
  gap: var(--spacing-xs);
}

.modern-badge-primary {
  background: var(--primary-100);
  color: var(--primary-800);
}

.modern-badge-success {
  background: var(--success-100);
  color: var(--success-800);
}

.modern-badge-warning {
  background: var(--warning-100);
  color: var(--warning-800);
}

.modern-badge-error {
  background: var(--error-100);
  color: var(--error-800);
}

/* Modern Table System */
.modern-table {
  width: 100%;
  border-collapse: collapse;
  background: white;
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
}

.modern-table th {
  background: linear-gradient(135deg, var(--secondary-50) 0%, var(--secondary-100) 100%);
  padding: var(--spacing-md);
  text-align: left;
  font-weight: 600;
  color: var(--secondary-700);
  font-size: var(--font-size-sm);
  border-bottom: 1px solid var(--secondary-200);
}

.modern-table td {
  padding: var(--spacing-md);
  border-bottom: 1px solid var(--secondary-100);
  font-size: var(--font-size-sm);
}

.modern-table tr:hover {
  background: var(--secondary-50);
}

.modern-table tr:last-child td {
  border-bottom: none;
}

/* Modern Header System */
.modern-header {
  background: linear-gradient(135deg, var(--primary-600) 0%, var(--primary-700) 100%);
  color: white;
  padding: var(--spacing-xl);
  border-radius: var(--radius-xl);
  margin-bottom: var(--spacing-xl);
  position: relative;
  overflow: hidden;
}

.modern-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.05'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E") repeat;
  opacity: 0.1;
}

.modern-header-content {
  position: relative;
  z-index: 1;
}

/* Modern Navigation */
.modern-nav {
  background: white;
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--secondary-200);
  overflow: hidden;
}

.modern-nav-item {
  display: flex;
  align-items: center;
  padding: var(--spacing-md) var(--spacing-lg);
  color: var(--secondary-700);
  text-decoration: none;
  transition: all 0.2s ease-in-out;
  border-bottom: 1px solid var(--secondary-100);
  gap: var(--spacing-sm);
}

.modern-nav-item:hover {
  background: var(--primary-50);
  color: var(--primary-700);
}

.modern-nav-item.active {
  background: linear-gradient(135deg, var(--primary-500) 0%, var(--primary-600) 100%);
  color: white;
}

.modern-nav-item:last-child {
  border-bottom: none;
}

/* Modern Grid System */
.modern-grid {
  display: grid;
  gap: var(--spacing-lg);
}

.modern-grid-cols-1 { grid-template-columns: repeat(1, 1fr); }
.modern-grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
.modern-grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
.modern-grid-cols-4 { grid-template-columns: repeat(4, 1fr); }

@media (max-width: 768px) {
  .modern-grid-cols-2,
  .modern-grid-cols-3,
  .modern-grid-cols-4 {
    grid-template-columns: 1fr;
  }
}

/* Modern Animation Classes */
.modern-fade-in {
  animation: modernFadeIn 0.3s ease-in-out;
}

.modern-slide-up {
  animation: modernSlideUp 0.3s ease-in-out;
}

.modern-scale-in {
  animation: modernScaleIn 0.2s ease-in-out;
}

@keyframes modernFadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes modernSlideUp {
  from { 
    opacity: 0; 
    transform: translateY(20px); 
  }
  to { 
    opacity: 1; 
    transform: translateY(0); 
  }
}

@keyframes modernScaleIn {
  from { 
    opacity: 0; 
    transform: scale(0.95); 
  }
  to { 
    opacity: 1; 
    transform: scale(1); 
  }
}

/* Modern Utility Classes */
.modern-text-gradient {
  background: linear-gradient(135deg, var(--primary-600) 0%, var(--primary-800) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.modern-backdrop-blur {
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.8);
}

.modern-glass-effect {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}
