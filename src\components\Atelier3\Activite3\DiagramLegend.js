// src/components/Atelier3/Activite3/DiagramLegend.js
import React, { useState } from 'react';
import { ChevronDown, ChevronUp, Target, AlertTriangle, Database, Shield, ArrowRight, Box } from 'lucide-react';

const DiagramLegend = () => {
  const [isExpanded, setIsExpanded] = useState(false);

  return (
    <div className="absolute bottom-4 left-8 z-10 bg-white rounded-lg shadow-md border border-slate-200 w-64 overflow-hidden">
      <div
        className="p-3 bg-slate-50 border-b border-slate-200 flex justify-between items-center cursor-pointer"
        onClick={() => setIsExpanded(!isExpanded)}
      >
        <h3 className="text-sm font-medium text-slate-800">Légende</h3>
        {isExpanded ? (
          <ChevronUp size={16} className="text-slate-500" />
        ) : (
          <ChevronDown size={16} className="text-slate-500" />
        )}
      </div>

      {isExpanded && (
        <div className="p-3 space-y-3">
          <div className="space-y-2">
            <h4 className="text-xs font-semibold text-slate-700 uppercase">Types de nœuds</h4>

            <div className="flex items-center">
              <div className="w-6 h-6 rounded-md bg-red-50 border border-red-500 flex items-center justify-center mr-2">
                <Target size={14} className="text-red-600" />
              </div>
              <span className="text-xs text-slate-700">Source de Risque</span>
            </div>

            <div className="flex items-center">
              <div className="w-6 h-6 rounded-md bg-slate-50 border border-slate-500 flex items-center justify-center mr-2">
                <Box size={14} className="text-slate-600" />
              </div>
              <span className="text-xs text-slate-700">Personnalisé</span>
            </div>

            <div className="flex items-center">
              <div className="w-6 h-6 rounded-md bg-green-50 border border-green-500 flex items-center justify-center mr-2">
                <Database size={14} className="text-green-600" />
              </div>
              <span className="text-xs text-slate-700">Objectif Visé</span>
            </div>

            <div className="flex items-center">
              <div className="w-6 h-6 rounded-md bg-blue-50 border border-blue-500 flex items-center justify-center mr-2">
                <Shield size={14} className="text-blue-600" />
              </div>
              <span className="text-xs text-slate-700">Partie Prenante</span>
            </div>
          </div>

          <div className="space-y-2">
            <h4 className="text-xs font-semibold text-slate-700 uppercase">Connexions</h4>

            <div className="flex items-center">
              <div className="w-6 h-6 flex items-center justify-center mr-2">
                <ArrowRight size={14} className="text-red-600" />
              </div>
              <span className="text-xs text-slate-700">Cible</span>
            </div>

            <div className="flex items-center">
              <div className="w-6 h-6 flex items-center justify-center mr-2">
                <ArrowRight size={14} className="text-blue-600" />
              </div>
              <span className="text-xs text-slate-700">Vise</span>
            </div>

            <div className="flex items-center">
              <div className="w-6 h-6 flex items-center justify-center mr-2">
                <ArrowRight size={14} className="text-gray-600" style={{ strokeDasharray: '2,2' }} />
              </div>
              <span className="text-xs text-slate-700">Attaque directe</span>
            </div>

            <div className="flex items-center">
              <div className="w-6 h-6 flex items-center justify-center mr-2">
                <ArrowRight size={14} className="text-gray-600" />
              </div>
              <span className="text-xs text-slate-700">Connexion</span>
            </div>
          </div>

          <div className="space-y-2">
            <h4 className="text-xs font-semibold text-slate-700 uppercase">Informations supplémentaires</h4>

            <div className="flex items-center">
              <div className="w-3 h-3 rounded-md bg-yellow-50 border border-yellow-200 mr-2"></div>
              <span className="text-xs text-slate-700">Événement Redouté</span>
            </div>

            <div className="flex items-center">
              <div className="w-3 h-3 rounded-md bg-blue-50 border border-blue-200 mr-2"></div>
              <span className="text-xs text-slate-700">Valeur Métier</span>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default DiagramLegend;
