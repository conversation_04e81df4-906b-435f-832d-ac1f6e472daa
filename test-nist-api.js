// test-nist-api.js
// Test script to verify NIST API fixes

const axios = require('axios');

const BASE_URL = 'http://localhost:5000/api/nist';

async function testNISTAPI() {
  console.log('🧪 Testing NIST API fixes...\n');

  // Test 1: Test endpoint
  try {
    console.log('1. Testing basic connectivity...');
    const testResponse = await axios.get(`${BASE_URL}/test`);
    console.log('✅ Test endpoint working:', testResponse.data.message);
  } catch (error) {
    console.error('❌ Test endpoint failed:', error.message);
  }

  // Test 2: Valid CPE search
  try {
    console.log('\n2. Testing valid CPE search...');
    const cpeResponse = await axios.get(`${BASE_URL}/search`, {
      params: {
        cpeName: 'cpe:2.3:a:cisco:ios:*:*:*:*:*:*:*:*',
        resultsPerPage: 5,
        startIndex: 0
      }
    });
    console.log('✅ Valid CPE search working. Results:', cpeResponse.data.totalResults || 0);
  } catch (error) {
    console.error('❌ Valid CPE search failed:', error.response?.status, error.response?.data?.message || error.message);
  }

  // Test 3: Invalid CPE search
  try {
    console.log('\n3. Testing invalid CPE search...');
    const invalidCpeResponse = await axios.get(`${BASE_URL}/search`, {
      params: {
        cpeName: 'invalid-cpe-format',
        resultsPerPage: 5
      }
    });
    console.log('❌ Invalid CPE should have failed but didn\'t');
  } catch (error) {
    if (error.response?.status === 400) {
      console.log('✅ Invalid CPE properly rejected:', error.response.data.message);
    } else {
      console.error('❌ Unexpected error for invalid CPE:', error.message);
    }
  }

  // Test 4: Keyword search
  try {
    console.log('\n4. Testing keyword search...');
    const keywordResponse = await axios.get(`${BASE_URL}/search`, {
      params: {
        keywordSearch: 'Cisco Router',
        resultsPerPage: 5,
        startIndex: 0
      }
    });
    console.log('✅ Keyword search working. Results:', keywordResponse.data.totalResults || 0);
  } catch (error) {
    console.error('❌ Keyword search failed:', error.response?.status, error.response?.data?.message || error.message);
  }

  // Test 5: Missing parameters
  try {
    console.log('\n5. Testing missing parameters...');
    const noParamsResponse = await axios.get(`${BASE_URL}/search`);
    console.log('❌ Missing parameters should have failed but didn\'t');
  } catch (error) {
    if (error.response?.status === 400) {
      console.log('✅ Missing parameters properly rejected:', error.response.data.message);
    } else {
      console.error('❌ Unexpected error for missing parameters:', error.message);
    }
  }

  // Test 6: Rate limiting behavior
  console.log('\n6. Testing rate limiting...');
  const startTime = Date.now();
  
  try {
    // Make two quick requests to test rate limiting
    const promise1 = axios.get(`${BASE_URL}/search`, {
      params: { keywordSearch: 'test1', resultsPerPage: 1 }
    });
    
    const promise2 = axios.get(`${BASE_URL}/search`, {
      params: { keywordSearch: 'test2', resultsPerPage: 1 }
    });

    await Promise.all([promise1, promise2]);
    const endTime = Date.now();
    const duration = endTime - startTime;
    
    if (duration >= 6000) {
      console.log(`✅ Rate limiting working: ${duration}ms delay`);
    } else {
      console.log(`⚠️ Rate limiting may not be working: only ${duration}ms delay`);
    }
  } catch (error) {
    console.log('✅ Rate limiting or API protection working (requests handled gracefully)');
  }

  console.log('\n🏁 NIST API testing completed!');
}

// Run the test
if (require.main === module) {
  testNISTAPI().catch(console.error);
}

module.exports = { testNISTAPI };
