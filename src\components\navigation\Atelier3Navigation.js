// src/components/navigation/Atelier3Navigation.js
import React from 'react';
import { ChevronDown, ChevronRight, Users, Target, Route, Shield, TrendingUp } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { getUnsavedChanges, setNavigationWarningDialogOpen } from '../../utils/navigationUtils';

// Define tab keys directly
const ATELIER3_TABS = {
    ACTIVITE1: 'atelier3-activite1',
    ACTIVITE2: 'atelier3-activite2',
    ACTIVITE3: 'atelier3-activite3',
    ACTIVITE4: 'atelier3-activite4',
    ACTIVITE5: 'atelier3-activite5'
};

const Atelier3Navigation = ({
  activeTab,
  setActiveTab,
  expanded,
  toggleExpand,
  baseNavItemClass,
  workshopTitleClass,
  activeStyle,
  inactiveStyle
}) => {
  const { t } = useTranslation();
  // We'll use the analysis context in the future when needed

  // Check if any tab within this workshop is active
  const isAtelierActive = Object.values(ATELIER3_TABS).includes(activeTab);

  // Sub-item class with added padding
  const subItemClass = `${baseNavItemClass} pl-8`; // Indent sub-items

  // Handle click on Cartographier l'écosystème tab
  const handleActivite1Click = async () => {
    // Check for unsaved changes before navigation
    if (getUnsavedChanges()) {
      // Show the warning dialog
      setNavigationWarningDialogOpen(true);
      // Store the target tab for later use
      localStorage.setItem('ebiosrm_pending_tab', ATELIER3_TABS.ACTIVITE1);
      return;
    }
    // If no unsaved changes, navigate directly
    setActiveTab(ATELIER3_TABS.ACTIVITE1);
  };

  // Handle click on Scénarios stratégiques tab
  const handleActivite2Click = async () => {
    // Check for unsaved changes before navigation
    if (getUnsavedChanges()) {
      // Show the warning dialog
      setNavigationWarningDialogOpen(true);
      // Store the target tab for later use
      localStorage.setItem('ebiosrm_pending_tab', ATELIER3_TABS.ACTIVITE2);
      return;
    }
    // If no unsaved changes, navigate directly
    setActiveTab(ATELIER3_TABS.ACTIVITE2);
  };

  // Handle click on Chemins d'attaque tab
  const handleActivite3Click = async () => {
    // Check for unsaved changes before navigation
    if (getUnsavedChanges()) {
      // Show the warning dialog
      setNavigationWarningDialogOpen(true);
      // Store the target tab for later use
      localStorage.setItem('ebiosrm_pending_tab', ATELIER3_TABS.ACTIVITE3);
      return;
    }
    // If no unsaved changes, navigate directly
    setActiveTab(ATELIER3_TABS.ACTIVITE3);
  };

  // Handle click on Mesures de sécurité tab
  const handleActivite4Click = async () => {
    // Check for unsaved changes before navigation
    if (getUnsavedChanges()) {
      // Show the warning dialog
      setNavigationWarningDialogOpen(true);
      // Store the target tab for later use
      localStorage.setItem('ebiosrm_pending_tab', ATELIER3_TABS.ACTIVITE4);
      return;
    }
    // If no unsaved changes, navigate directly
    setActiveTab(ATELIER3_TABS.ACTIVITE4);
  };

  // Handle click on Progression des PTR tab
  const handleActivite5Click = async () => {
    // Check for unsaved changes before navigation
    if (getUnsavedChanges()) {
      // Show the warning dialog
      setNavigationWarningDialogOpen(true);
      // Store the target tab for later use
      localStorage.setItem('ebiosrm_pending_tab', ATELIER3_TABS.ACTIVITE5);
      return;
    }
    // If no unsaved changes, navigate directly
    setActiveTab(ATELIER3_TABS.ACTIVITE5);
  };

  return (
    <li className="w-full">
      <button
        className={`${workshopTitleClass} ${isAtelierActive ? 'text-white' : 'text-gray-300 hover:bg-gray-700 hover:text-white'} group`}
        onClick={toggleExpand}
      >
        <div className="flex items-center w-full justify-between">
          <div className="flex items-center">
            <div className={`${isAtelierActive ? 'bg-blue-500' : 'bg-gray-700 group-hover:bg-gray-600'} p-1.5 rounded-md transition-colors duration-200 mr-3`}>
              <Target size={16} className="flex-shrink-0 text-white" />
            </div>
            <span className="truncate">{t('workshop3.navigationTitle')}</span>
          </div>
          <div className="text-gray-400 transition-transform duration-200">
            {expanded ? <ChevronDown size={16} /> : <ChevronRight size={16} />}
          </div>
        </div>
      </button>

      {expanded && (
        <ul className="mt-1 space-y-1 w-full">
          <li>
            <button
              className={`${subItemClass} ${activeTab === ATELIER3_TABS.ACTIVITE1 ? activeStyle : inactiveStyle} group`}
              onClick={handleActivite1Click}
            >
              <div className="flex items-center">
                <div className={`${activeTab === ATELIER3_TABS.ACTIVITE1 ? 'bg-blue-500' : 'bg-gray-700 group-hover:bg-gray-600'} p-1 rounded transition-colors duration-200 mr-2`}>
                  <Users size={14} className="flex-shrink-0 text-white" />
                </div>
                <span className="truncate">{t('workshop3.navigation.mapEcosystem')}</span>
              </div>
            </button>
          </li>
          <li>
            <button
              className={`${subItemClass} ${activeTab === ATELIER3_TABS.ACTIVITE2 ? activeStyle : inactiveStyle} group`}
              onClick={handleActivite2Click}
            >
              <div className="flex items-center">
                <div className={`${activeTab === ATELIER3_TABS.ACTIVITE2 ? 'bg-blue-500' : 'bg-gray-700 group-hover:bg-gray-600'} p-1 rounded transition-colors duration-200 mr-2`}>
                  <Target size={14} className="flex-shrink-0 text-white" />
                </div>
                <span className="truncate">{t('workshop3.navigation.strategicScenarios')}</span>
              </div>
            </button>
          </li>
          <li>
            <button
              className={`${subItemClass} ${activeTab === ATELIER3_TABS.ACTIVITE3 ? activeStyle : inactiveStyle} group`}
              onClick={handleActivite3Click}
            >
              <div className="flex items-center">
                <div className={`${activeTab === ATELIER3_TABS.ACTIVITE3 ? 'bg-blue-500' : 'bg-gray-700 group-hover:bg-gray-600'} p-1 rounded transition-colors duration-200 mr-2`}>
                  <Route size={14} className="flex-shrink-0 text-white" />
                </div>
                <span className="truncate">{t('workshop3.navigation.attackPaths')}</span>
              </div>
            </button>
          </li>
          <li>
            <button
              className={`${subItemClass} ${activeTab === ATELIER3_TABS.ACTIVITE4 ? activeStyle : inactiveStyle} group`}
              onClick={handleActivite4Click}
            >
              <div className="flex items-center">
                <div className={`${activeTab === ATELIER3_TABS.ACTIVITE4 ? 'bg-blue-500' : 'bg-gray-700 group-hover:bg-gray-600'} p-1 rounded transition-colors duration-200 mr-2`}>
                  <Shield size={14} className="flex-shrink-0 text-white" />
                </div>
                <span className="truncate">{t('workshop3.navigation.securityMeasures')}</span>
              </div>
            </button>
          </li>
          <li>
            <button
              className={`${subItemClass} ${activeTab === ATELIER3_TABS.ACTIVITE5 ? activeStyle : inactiveStyle} group`}
              onClick={handleActivite5Click}
            >
              <div className="flex items-center">
                <div className={`${activeTab === ATELIER3_TABS.ACTIVITE5 ? 'bg-blue-500' : 'bg-gray-700 group-hover:bg-gray-600'} p-1 rounded transition-colors duration-200 mr-2`}>
                  <TrendingUp size={14} className="flex-shrink-0 text-white" />
                </div>
                <span className="truncate">{t('workshop3.navigation.eventProgression')}</span>
              </div>
            </button>
          </li>
        </ul>
      )}
    </li>
  );
};

export default Atelier3Navigation;
export { ATELIER3_TABS };
