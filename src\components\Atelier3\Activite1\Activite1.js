// src/components/Atelier3/Activite1/Activite1.js
import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Save, Plus, Info, Users, BarChart2, Table, Filter, Settings } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { useAnalysis } from '../../../context/AnalysisContext';
import StakeholderForm from './StakeholderForm';
import StakeholderTable from './StakeholderTable';
import ThreatRadarVisualization from './ThreatRadarVisualization';
import GuideModal from './GuideModal';
import { showSuccessToast, showErrorToast, showLoadingToast, updateToast } from '../../../utils/toastUtils';

// Get color based on stakeholder category
const getCategoryColor = (category) => {
  switch(category) {
    case 'client': return '#3B82F6'; // blue
    case 'partner': return '#10B981'; // green
    case 'provider': return '#F59E0B'; // amber
    case 'technical': return '#8B5CF6'; // purple
    case 'business': return '#EC4899'; // pink
    case 'subsidiary': return '#6366F1'; // indigo
    default: return '#6B7280'; // gray
  }
};

const Activite1 = () => {
  const { t } = useTranslation();
  const [stakeholders, setStakeholders] = useState([]);
  const [selectedStakeholder, setSelectedStakeholder] = useState(null);
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [isGuideOpen, setIsGuideOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [isThresholdEditing, setIsThresholdEditing] = useState(false);
  const [thresholds, setThresholds] = useState({
    danger: 3,
    control: 1.5,
    watch: 0.5
  });
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [filterType, setFilterType] = useState('all'); // 'all', 'internal', 'external'
  const [filterCategory, setFilterCategory] = useState('all'); // 'all', 'client', 'partner', etc.

  // Get analysis context
  const {
    currentAnalysis,
    getStakeholders,
    saveStakeholders,
    getStakeholderThresholds,
    updateStakeholderThresholds
  } = useAnalysis();

  // Load data when component mounts
  useEffect(() => {
    let isMounted = true;

    const loadData = async () => {
      if (!currentAnalysis?.id) return;

      setIsLoading(true);
      try {
        // Load stakeholders
        const stakeholdersData = await getStakeholders(currentAnalysis.id);
        if (isMounted) {
          if (stakeholdersData && stakeholdersData.data) {
            setStakeholders(stakeholdersData.data);
          } else {
            setStakeholders([]);
          }
        }

        // Load thresholds
        const thresholdsData = await getStakeholderThresholds(currentAnalysis.id);
        if (isMounted && thresholdsData && thresholdsData.data) {
          setThresholds(thresholdsData.data);
        }
      } catch (error) {
        if (isMounted) {
          console.error('Error loading data:', error);
          showErrorToast(t('workshop3.activity1.errors.loadingError'));
        }
      } finally {
        if (isMounted) {
          setIsLoading(false);
        }
      }
    };

    loadData();

    // Cleanup function to prevent state updates after unmount
    return () => {
      isMounted = false;
    };
  }, [currentAnalysis?.id]); // Only depend on the analysis ID

  // Handle save button click
  const handleSave = async () => {
    if (!currentAnalysis?.id) {
      showErrorToast(t('workshop2.activity1.errors.noAnalysisSelected'));
      return;
    }

    // Show loading toast
    const toastId = showLoadingToast(t('workshop3.activity1.saving'));

    try {
      const response = await saveStakeholders(currentAnalysis.id, stakeholders);

      if (response.success) {
        updateToast(toastId, t('workshop3.activity1.success.stakeholdersSaved'), 'success');
        setHasUnsavedChanges(false);
      } else {
        updateToast(toastId, t('workshop3.activity1.errors.saveError', { message: response.message || t('workshop3.activity1.errors.unknownError') }), 'error');
      }
    } catch (error) {
      console.error('Error saving stakeholders:', error);
      updateToast(toastId, t('workshop3.activity1.errors.saveStakeholdersError'), 'error');
    }
  };

  // Handle threshold update
  const handleUpdateThresholds = async (newThresholds) => {
    if (!currentAnalysis?.id) {
      showErrorToast(t('workshop2.activity1.errors.noAnalysisSelected'));
      return;
    }

    // Show loading toast
    const toastId = showLoadingToast(t('workshop3.activity1.updatingThresholds'));

    try {
      const response = await updateStakeholderThresholds(currentAnalysis.id, newThresholds);

      if (response.success) {
        setThresholds(newThresholds);
        updateToast(toastId, t('workshop3.activity1.success.thresholdsUpdated'), 'success');
      } else {
        updateToast(toastId, t('workshop3.activity1.errors.saveError', { message: response.message || t('workshop3.activity1.errors.unknownError') }), 'error');
      }
    } catch (error) {
      console.error('Error updating thresholds:', error);
      updateToast(toastId, t('workshop3.activity1.errors.updateThresholdsError'), 'error');
    }
  };

  // Handle stakeholder creation/update
  const handleStakeholderSubmit = (stakeholderData) => {
    if (selectedStakeholder) {
      // Update existing stakeholder
      const updatedStakeholders = stakeholders.map(s =>
        s.id === selectedStakeholder.id ? { ...s, ...stakeholderData, updatedAt: new Date() } : s
      );
      setStakeholders(updatedStakeholders);
      showSuccessToast(t('workshop3.activity1.success.stakeholderUpdated'));
    } else {
      // Create new stakeholder
      const newStakeholder = {
        ...stakeholderData,
        id: Date.now().toString(), // Simple ID generation
        createdAt: new Date(),
        updatedAt: new Date()
      };
      setStakeholders([...stakeholders, newStakeholder]);
      showSuccessToast(t('workshop3.activity1.success.stakeholderAdded'));
    }

    setSelectedStakeholder(null);
    setIsFormOpen(false);
    setHasUnsavedChanges(true);
  };

  // Handle stakeholder deletion
  const handleDeleteStakeholder = (id) => {
    // Find the stakeholder to get its name for the toast message
    const stakeholderToDelete = stakeholders.find(s => s.id === id);
    const stakeholderName = stakeholderToDelete?.name || t('workshop3.activity1.stakeholder');

    setStakeholders(stakeholders.filter(s => s.id !== id));
    setHasUnsavedChanges(true);
    showSuccessToast(t('workshop3.activity1.success.stakeholderDeleted', { name: stakeholderName }));
  };

  // Handle toggling the retained status of a stakeholder
  const handleToggleRetained = (id) => {
    const updatedStakeholders = stakeholders.map(stakeholder => {
      if (stakeholder.id === id) {
        const newRetainedStatus = !stakeholder.retained;
        const statusText = newRetainedStatus ? t('workshop3.activity1.retained') : t('workshop3.activity1.notRetained');
        showSuccessToast(t('workshop3.activity1.success.stakeholderStatusChanged', { name: stakeholder.name, status: statusText }));
        return { ...stakeholder, retained: newRetainedStatus, updatedAt: new Date() };
      }
      return stakeholder;
    });

    setStakeholders(updatedStakeholders);
    setHasUnsavedChanges(true);
  };

  // Filter stakeholders based on current filters
  const filteredStakeholders = stakeholders.filter(stakeholder => {
    const typeMatch = filterType === 'all' || stakeholder.type === filterType;
    const categoryMatch = filterCategory === 'all' || stakeholder.category === filterCategory;
    return typeMatch && categoryMatch;
  });

  return (
    <div className="space-y-6 p-4 sm:p-6 lg:p-8 bg-gradient-to-br from-slate-50 to-blue-50 min-h-screen">
      {/* === MERGED HEADER === */}
      <div className="bg-white rounded-xl shadow-sm border border-slate-100 p-4 mb-6">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          {/* Left Side: Breadcrumb & Title */}
          <div>
            <div className="flex items-center text-sm text-slate-500 mb-2">
              <span className="hover:text-blue-600 transition-colors">{t('workshop3.title')}</span>
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mx-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
              <span className="text-blue-600 font-medium">{t('workshop3.activity1.breadcrumb')}</span>
            </div>
            <h1 className="text-2xl sm:text-3xl font-bold text-slate-800 flex items-center">
              <Users size={28} className="mr-3 text-blue-600" />
              {t('workshop3.activity1.title')}
            </h1>
          </div>

          {/* Right Side: Buttons */}
          <div className="flex items-center gap-2 flex-wrap">
            {/* Save Button */}
            <button
              onClick={handleSave}
              disabled={!hasUnsavedChanges}
              className={`text-sm font-medium px-4 py-2 rounded-md flex items-center shadow-sm transition duration-200 ${
                hasUnsavedChanges
                  ? 'bg-blue-600 text-white hover:bg-blue-700'
                  : 'bg-gray-100 text-gray-400 cursor-not-allowed'
              }`}
            >
              <Save size={16} className="mr-2" />
              {t('common.save')}
            </button>

            {/* Help Button */}
            <button
              className="text-sm font-medium bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 flex items-center shadow-sm transition duration-200"
              onClick={() => setIsGuideOpen(true)}
            >
              <Info size={16} className="mr-2" />
              {t('common.help')}
            </button>
          </div>
        </div>
      </div>
      {/* === END MERGED HEADER === */}

      {/* Add stakeholder button and filters */}
      <div className="flex justify-between items-center">
        <div className="flex items-center space-x-4 bg-gray-50 p-3 rounded-lg">
          <div className="flex items-center">
            <Filter size={16} className="text-gray-500 mr-2" />
            <span className="text-sm text-gray-600 mr-2">{t('workshop3.activity1.filters')}:</span>
          </div>
          <select
            value={filterType}
            onChange={(e) => setFilterType(e.target.value)}
            className="text-sm border border-gray-300 rounded-md px-2 py-1"
          >
            <option value="all">{t('workshop3.activity1.filter.allTypes')}</option>
            <option value="internal">{t('workshop3.activity1.filter.internal')}</option>
            <option value="external">{t('workshop3.activity1.filter.external')}</option>
          </select>
          <select
            value={filterCategory}
            onChange={(e) => setFilterCategory(e.target.value)}
            className="text-sm border border-gray-300 rounded-md px-2 py-1"
          >
            <option value="all">{t('workshop3.activity1.filter.allCategories')}</option>
            <option value="client">{t('workshop3.activity1.categories.client')}</option>
            <option value="partner">{t('workshop3.activity1.categories.partner')}</option>
            <option value="provider">{t('workshop3.activity1.categories.provider')}</option>
            <option value="technical">{t('workshop3.activity1.categories.technical')}</option>
            <option value="business">{t('workshop3.activity1.categories.business')}</option>
            <option value="subsidiary">{t('workshop3.activity1.categories.subsidiary')}</option>
          </select>
        </div>
        <button
          onClick={() => {
            setSelectedStakeholder(null);
            setIsFormOpen(true);
          }}
          className="px-3 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors flex items-center"
        >
          <Plus size={18} className="mr-1.5" />
          {t('workshop3.activity1.addStakeholder')}
        </button>
      </div>

      {/* Main content with three-column layout with separation */}
      <div className="grid grid-cols-1 lg:grid-cols-12 gap-4"> {/* Using 12 columns with gap for separation */}
        {/* Left column - Stakeholder Cards (5/12 width) */}
        <div className="lg:col-span-5 bg-white rounded-xl border border-gray-200 shadow-sm overflow-hidden">
          <div className="p-4 border-b border-gray-200 bg-gray-50 flex justify-between items-center">
            <h3 className="font-medium text-gray-700 flex items-center">
              <Users size={18} className="mr-2 text-blue-600" /> {/* Changed icon to Users for cards */}
              {t('workshop3.activity1.stakeholders')}
            </h3>
          </div>
          <div className="max-h-[600px] overflow-auto p-1">
            <StakeholderTable
              stakeholders={filteredStakeholders}
              onEdit={(stakeholder) => {
                setSelectedStakeholder(stakeholder);
                setIsFormOpen(true);
              }}
              onDelete={handleDeleteStakeholder}
              onToggleRetained={handleToggleRetained}
              isLoading={isLoading}
            />
          </div>
        </div>

        {/* Middle and Right columns - Legend and Visualization OR Form */}
        {isFormOpen ? (
          // When form is open, it takes the full width of both legend and visualization columns
          <div className="lg:col-span-7 bg-white rounded-xl border border-gray-200 shadow-sm overflow-hidden">
            <div className="p-4 border-b border-gray-200 bg-gray-50 flex justify-between items-center">
              <h3 className="font-medium text-gray-700 flex items-center">
                <Users size={18} className="mr-2 text-blue-600" />
                {selectedStakeholder ? t('workshop3.activity1.editStakeholder') : t('workshop3.activity1.addStakeholder')}
              </h3>
            </div>
            <div className="max-h-[600px] overflow-auto">
              <StakeholderForm
                stakeholder={selectedStakeholder}
                onSubmit={handleStakeholderSubmit}
                onCancel={() => {
                  setSelectedStakeholder(null);
                  setIsFormOpen(false);
                }}
              />
            </div>
          </div>
        ) : (
          // When form is not open, show both legend and visualization columns
          <>
            {/* Middle column - Vertical Legend (2/12 width) */}
            <div className="lg:col-span-2 bg-white rounded-xl border border-gray-200 shadow-sm overflow-hidden">
              <div className="p-3 border-b border-gray-200 bg-gray-50">
                <h3 className="font-medium text-gray-700 flex items-center justify-center">
                  <Info size={16} className="mr-2 text-blue-600" />
                  {t('workshop3.activity1.legend.title')}
                </h3>
              </div>
              <div className="p-3 space-y-4">
                {/* Zones section */}
                <div>
                  <div className="font-medium text-xs mb-2 text-gray-700">{t('workshop3.activity1.legend.zones')}</div>
                  <div className="space-y-2">
                    <div className="flex items-center">
                      <div className="w-4 h-4 bg-red-100 border border-red-400 mr-2"></div>
                      <span className="text-xs text-gray-600">{t('workshop3.activity1.legend.danger')}</span>
                    </div>
                    <div className="flex items-center">
                      <div className="w-4 h-4 bg-amber-100 border border-amber-400 mr-2"></div>
                      <span className="text-xs text-gray-600">{t('workshop3.activity1.legend.control')}</span>
                    </div>
                    <div className="flex items-center">
                      <div className="w-4 h-4 bg-green-100 border border-green-400 mr-2"></div>
                      <span className="text-xs text-gray-600">{t('workshop3.activity1.legend.watch')}</span>
                    </div>
                  </div>
                </div>

                {/* Types section */}
                <div>
                  <div className="font-medium text-xs mb-2 text-gray-700">{t('workshop3.activity1.legend.types')}</div>
                  <div className="space-y-2">
                    <div className="flex items-center">
                      <div className="w-4 h-4 bg-slate-500 rounded-full mr-2"></div>
                      <span className="text-xs text-gray-600">{t('workshop3.activity1.filter.external')}</span>
                    </div>
                    <div className="flex items-center">
                      <div className="w-4 h-4 bg-slate-500 mr-2" style={{transform: 'rotate(45deg)'}}></div>
                      <span className="text-xs text-gray-600">{t('workshop3.activity1.filter.internal')}</span>
                    </div>
                  </div>
                </div>

                {/* Fiabilité Cyber section */}
                <div>
                  <div className="font-medium text-xs mb-2 text-gray-700">
                    {t('workshop3.activity1.legend.cyberReliability', 'Fiabilité Cyber')} (Maturité × Confiance)
                  </div>
                  <div className="space-y-2">
                    <div className="flex items-center">
                      <div className="w-4 h-4 rounded-full mr-2" style={{backgroundColor: '#EF4444'}}></div>
                      <span className="text-xs text-gray-600">
                        {t('workshop3.activity1.legend.weak', 'Faible')} (&lt; 4)
                      </span>
                    </div>
                    <div className="flex items-center">
                      <div className="w-4 h-4 rounded-full mr-2" style={{backgroundColor: '#F97316'}}></div>
                      <span className="text-xs text-gray-600">
                        {t('workshop3.activity1.legend.average', 'Moyenne')} (4-5)
                      </span>
                    </div>
                    <div className="flex items-center">
                      <div className="w-4 h-4 rounded-full mr-2" style={{backgroundColor: '#3B82F6'}}></div>
                      <span className="text-xs text-gray-600">
                        {t('workshop3.activity1.legend.good', 'Bonne')} (6-7)
                      </span>
                    </div>
                    <div className="flex items-center">
                      <div className="w-4 h-4 rounded-full mr-2" style={{backgroundColor: '#10B981'}}></div>
                      <span className="text-xs text-gray-600">
                        {t('workshop3.activity1.legend.excellent', 'Excellente')} (&gt; 7)
                      </span>
                    </div>
                  </div>
                </div>

                {/* Exposition section */}
                <div>
                  <div className="font-medium text-xs mb-2 text-gray-700">
                    {t('workshop3.activity1.legend.exposure', 'Exposition')} (Dépendance × Pénétration)
                  </div>
                  <div className="space-y-2">
                    <div className="flex items-center">
                      <div className="w-3 h-3 rounded-full mr-2 border border-gray-400 bg-gray-100"></div>
                      <span className="text-xs text-gray-600">
                        {t('workshop3.activity1.legend.minimal', 'Minimal')} (&lt; 3)
                      </span>
                    </div>
                    <div className="flex items-center">
                      <div className="w-4 h-4 rounded-full mr-2 border border-gray-400 bg-gray-100"></div>
                      <span className="text-xs text-gray-600">
                        {t('workshop3.activity1.legend.moderate', 'Moderate')} (3-6)
                      </span>
                    </div>
                    <div className="flex items-center">
                      <div className="w-5 h-5 rounded-full mr-2 border border-gray-400 bg-gray-100"></div>
                      <span className="text-xs text-gray-600">
                        {t('workshop3.activity1.legend.high', 'High')} (7-9)
                      </span>
                    </div>
                    <div className="flex items-center">
                      <div className="w-6 h-6 rounded-full mr-2 border border-gray-400 bg-gray-100"></div>
                      <span className="text-xs text-gray-600">
                        {t('workshop3.activity1.legend.critical', 'Critical')} (&gt; 9)
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Right column - Visualization (5/12 width) */}
            <div className="lg:col-span-5 bg-white rounded-xl border border-gray-200 shadow-sm overflow-hidden">
              <div className="p-4 border-b border-gray-200 bg-gray-50 flex justify-between items-center">
                <h3 className="font-medium text-gray-700 flex items-center">
                  <BarChart2 size={18} className="mr-2 text-blue-600" />
                  {t('workshop3.activity1.radarVisualization')}
                </h3>
                <button
                  onClick={() => setIsThresholdEditing(!isThresholdEditing)}
                  className="p-2 bg-white border border-gray-200 rounded-md shadow-sm hover:bg-gray-50 flex items-center text-xs"
                  title={t('workshop3.activity1.modifyThresholds')}
                >
                  <Settings size={16} className="text-gray-600 mr-1" />
                  {t('workshop3.activity1.modifyThresholds')}
                </button>
              </div>
              <div className="max-h-[600px] overflow-auto">
                <ThreatRadarVisualization
                  stakeholders={filteredStakeholders}
                  thresholds={thresholds}
                  onUpdateThresholds={handleUpdateThresholds}
                  onSelectStakeholder={(stakeholder) => {
                    setSelectedStakeholder(stakeholder);
                    setIsFormOpen(true);
                  }}
                  isLoading={isLoading}
                  isThresholdEditing={isThresholdEditing}
                  setIsThresholdEditing={setIsThresholdEditing}
                />
              </div>
            </div>
          </>
        )}
      </div>

      {/* Guide Modal */}
      <GuideModal isOpen={isGuideOpen} onClose={() => setIsGuideOpen(false)} />
    </div>
  );
};

export default Activite1;
