// src/services/operationalScenariosService.js
import { api } from '../api/apiClient';

// Get all operational scenarios
export const getOperationalScenarios = async () => {
  try {
    const response = await api.get('/api/operational-scenarios');
    return response.data;
  } catch (error) {
    console.error('Error fetching operational scenarios:', error);
    throw error;
  }
};

// Get operational scenarios by attack path ID
export const getOperationalScenariosByAttackPath = async (attackPathId) => {
  try {
    const response = await api.get(`/api/operational-scenarios/attack-path/${attackPathId}`);
    return response.data;
  } catch (error) {
    console.error('Error fetching operational scenarios by attack path:', error);
    throw error;
  }
};

// Create a new operational scenario
export const createOperationalScenario = async (scenarioData) => {
  try {
    const response = await api.post('/api/operational-scenarios', scenarioData);
    return response.data;
  } catch (error) {
    console.error('Error creating operational scenario:', error);
    throw error;
  }
};

// Update an operational scenario
export const updateOperationalScenario = async (id, scenarioData) => {
  try {
    const response = await api.put(`/api/operational-scenarios/${id}`, scenarioData);
    return response.data;
  } catch (error) {
    console.error('Error updating operational scenario:', error);
    throw error;
  }
};

// Delete an operational scenario
export const deleteOperationalScenario = async (id) => {
  try {
    const response = await api.delete(`/api/operational-scenarios/${id}`);
    return response.data;
  } catch (error) {
    console.error('Error deleting operational scenario:', error);
    throw error;
  }
};

// Generate operational scenarios using AI
export const generateOperationalScenariosAI = async (attackPaths, options = {}) => {
  try {
    const response = await api.post('/api/ai/generate-operational-scenarios', {
      attackPaths,
      options
    });
    return response.data;
  } catch (error) {
    console.error('Error generating operational scenarios with AI:', error);
    throw error;
  }
};

// Bulk create operational scenarios
export const bulkCreateOperationalScenarios = async (scenarios) => {
  try {
    const response = await api.post('/api/operational-scenarios/bulk', { scenarios });
    return response.data;
  } catch (error) {
    console.error('Error bulk creating operational scenarios:', error);
    throw error;
  }
};

// Export operational scenarios
export const exportOperationalScenarios = async (format = 'json') => {
  try {
    const response = await api.get(`/api/operational-scenarios/export?format=${format}`);
    return response.data;
  } catch (error) {
    console.error('Error exporting operational scenarios:', error);
    throw error;
  }
};

// Get operational scenario statistics
export const getOperationalScenarioStats = async () => {
  try {
    const response = await api.get('/api/operational-scenarios/stats');
    return response.data;
  } catch (error) {
    console.error('Error fetching operational scenario statistics:', error);
    throw error;
  }
};

// === NEW CTI-BASED AI SCENARIO GENERATION ===

/**
 * Generate operational scenarios using AI based on CTI data
 * @param {Object} ctiData - CTI analysis data
 * @returns {Promise<Object>} Generated scenarios
 */
export const generateScenariosFromCTI = async (ctiData) => {
  try {
    console.log('Generating operational scenarios with CTI data:', ctiData);

    // Prepare the prompt for AI generation
    const prompt = buildEBIOSPrompt(ctiData);

    const response = await api.post('/ai/generate-operational-scenarios', {
      prompt,
      ctiData,
      maxTokens: 4000,
      temperature: 0.7
    });

    if (response.data.success) {
      return {
        success: true,
        scenarios: parseAIResponse(response.data.content, ctiData)
      };
    } else {
      throw new Error(response.data.message || 'Failed to generate scenarios');
    }
  } catch (error) {
    console.error('Error generating operational scenarios:', error);
    throw error;
  }
};

/**
 * Build EBIOS RM compliant prompt for AI generation
 * @param {Object} ctiData - CTI analysis data
 * @returns {string} Formatted prompt
 */
export const buildEBIOSPrompt = (ctiData) => {
  const { assets, vulnerabilities, techniques, scenarioParams } = ctiData;

  const attackerProfiles = {
    'cybercrime': 'groupe de cybercriminalité organisée motivé par le profit',
    'apt': 'groupe APT sophistiqué soutenu par un État-nation',
    'insider': 'employé malveillant ou compromis avec accès privilégié',
    'hacktivist': 'hacktiviste motivé par des causes politiques ou sociales',
    'script-kiddie': 'attaquant novice utilisant des outils automatisés'
  };

  const attackGoals = {
    'data-theft': 'voler des données sensibles ou confidentielles',
    'ransomware': 'chiffrer les systèmes et demander une rançon',
    'sabotage': 'perturber ou détruire les opérations critiques',
    'espionage': 'collecter des informations stratégiques',
    'financial-fraud': 'commettre une fraude financière',
    'disruption': 'perturber les services et opérations'
  };

  const complexityLevels = {
    'simple': '3-5 étapes avec des techniques basiques',
    'medium': '6-10 étapes avec une combinaison de techniques',
    'complex': '11+ étapes avec des techniques avancées et de la persistance'
  };

  // Build assets summary
  const assetsSummary = assets.map(asset => {
    const assetVulns = vulnerabilities.filter(v => v.assetId === asset.id || v.attackPathId === asset.id);
    const assetTechs = techniques.filter(t => t.assetId === asset.id || t.attackPathId === asset.id);

    return `
Actif: ${asset.name} (${asset.type})
Vulnérabilités identifiées:
${assetVulns.map(v => `- ${v.id}: ${v.description || v.summary || 'Vulnérabilité critique'} (CVSS: ${v.baseScore || 'N/A'})`).join('\n')}

Techniques d'attaque MITRE ATT&CK associées:
${assetTechs.map(t => `- ${t.techniqueId}: ${t.name} - ${t.description || 'Technique d\'attaque'}`).join('\n')}
`;
  }).join('\n---\n');

  return `
Tu es un expert en cybersécurité spécialisé dans la méthodologie EBIOS Risk Manager.

CONTEXTE:
Analyse: ${ctiData.analysisName}
Profil attaquant: ${attackerProfiles[scenarioParams.attackerProfile]}
Objectif: ${attackGoals[scenarioParams.attackGoal]}
Complexité: ${complexityLevels[scenarioParams.complexity]}

DONNÉES CTI DISPONIBLES:
${assetsSummary}

MISSION:
Génère ${assets.length} scénario(s) opérationnel(s) réaliste(s) suivant STRICTEMENT la méthodologie EBIOS RM avec les 4 phases obligatoires:

1. CONNAÎTRE (Reconnaissance): Comment l'attaquant découvre et analyse les vulnérabilités
2. RENTRER (Accès initial): Exploitation des vulnérabilités pour obtenir un accès initial
3. TROUVER (Mouvement latéral): Navigation dans le système pour atteindre les cibles
4. EXPLOITER (Objectif final): Réalisation de l'objectif d'attaque

EXIGENCES:
- Utilise OBLIGATOIREMENT les vulnérabilités et techniques MITRE ATT&CK fournies
- Chaque phase doit contenir 2-4 actions détaillées et techniques
- Inclus les identifiants MITRE ATT&CK (ex: T1190, T1059.004)
- Décris des actions concrètes et réalistes
- Respecte le profil d'attaquant et l'objectif choisis
- Assure la cohérence technique entre les phases

FORMAT DE RÉPONSE (JSON):
{
  "scenarios": [
    {
      "title": "Titre du scénario",
      "description": "Description générale du scénario",
      "attackerProfile": "${scenarioParams.attackerProfile}",
      "attackGoal": "${scenarioParams.attackGoal}",
      "phases": [
        {
          "name": "CONNAÎTRE",
          "title": "Phase de reconnaissance",
          "description": "Description de la phase",
          "actions": [
            {
              "description": "Action détaillée",
              "techniques": ["T1595", "T1590"]
            }
          ]
        },
        {
          "name": "RENTRER",
          "title": "Accès initial",
          "description": "Description de la phase",
          "actions": [...]
        },
        {
          "name": "TROUVER",
          "title": "Mouvement latéral",
          "description": "Description de la phase",
          "actions": [...]
        },
        {
          "name": "EXPLOITER",
          "title": "Objectif final",
          "description": "Description de la phase",
          "actions": [...]
        }
      ],
      "assetsUsed": ["${assets.map(a => a.name).join('", "')}"]
    }
  ]
}

Génère maintenant les scénarios en JSON valide:`;
};

/**
 * Parse AI response and structure scenarios
 * @param {string} aiResponse - Raw AI response
 * @param {Object} ctiData - Original CTI data for validation
 * @returns {Array} Structured scenarios
 */
export const parseAIResponse = (aiResponse, ctiData) => {
  try {
    // Try to extract JSON from the response
    let jsonMatch = aiResponse.match(/\{[\s\S]*\}/);
    if (!jsonMatch) {
      throw new Error('No JSON found in AI response');
    }

    const parsedResponse = JSON.parse(jsonMatch[0]);

    if (!parsedResponse.scenarios || !Array.isArray(parsedResponse.scenarios)) {
      throw new Error('Invalid scenarios format in AI response');
    }

    // Validate and enhance scenarios
    return parsedResponse.scenarios.map((scenario, index) => ({
      id: `scenario-${Date.now()}-${index}`,
      title: scenario.title || `Scénario ${index + 1}`,
      description: scenario.description || 'Scénario d\'attaque généré par IA',
      attackerProfile: scenario.attackerProfile || ctiData.scenarioParams.attackerProfile,
      attackGoal: scenario.attackGoal || ctiData.scenarioParams.attackGoal,
      phases: validatePhases(scenario.phases || []),
      assetsUsed: scenario.assetsUsed || ctiData.assets.map(a => a.name),
      generatedAt: new Date().toISOString(),
      ctiDataUsed: {
        vulnerabilitiesCount: ctiData.vulnerabilities?.length || 0,
        techniquesCount: ctiData.techniques?.length || 0,
        assetsCount: ctiData.assets?.length || 0
      }
    }));
  } catch (error) {
    console.error('Error parsing AI response:', error);

    // Fallback: create a basic scenario structure
    return createFallbackScenario(ctiData);
  }
};

/**
 * Validate EBIOS RM phases structure
 * @param {Array} phases - Phases from AI response
 * @returns {Array} Validated phases
 */
export const validatePhases = (phases) => {
  const requiredPhases = ['CONNAÎTRE', 'RENTRER', 'TROUVER', 'EXPLOITER'];
  const validatedPhases = [];

  requiredPhases.forEach(phaseName => {
    const phase = phases.find(p => p.name === phaseName) || {
      name: phaseName,
      title: `Phase ${phaseName}`,
      description: `Phase ${phaseName} du scénario d'attaque`,
      actions: []
    };

    // Ensure actions exist and are properly formatted
    if (!phase.actions || !Array.isArray(phase.actions)) {
      phase.actions = [];
    }

    phase.actions = phase.actions.map(action => ({
      description: action.description || 'Action non spécifiée',
      techniques: Array.isArray(action.techniques) ? action.techniques : []
    }));

    validatedPhases.push(phase);
  });

  return validatedPhases;
};

/**
 * Create fallback scenario when AI parsing fails
 * @param {Object} ctiData - CTI data
 * @returns {Array} Fallback scenario
 */
export const createFallbackScenario = (ctiData) => {
  return [{
    id: `fallback-scenario-${Date.now()}`,
    title: 'Scénario d\'attaque basé sur CTI',
    description: 'Scénario généré automatiquement basé sur les vulnérabilités et techniques identifiées',
    attackerProfile: ctiData.scenarioParams.attackerProfile,
    attackGoal: ctiData.scenarioParams.attackGoal,
    phases: [
      {
        name: 'CONNAÎTRE',
        title: 'Reconnaissance',
        description: 'Phase de reconnaissance et collecte d\'informations',
        actions: [
          {
            description: 'Scan des services exposés et identification des vulnérabilités',
            techniques: ['T1595', 'T1590']
          }
        ]
      },
      {
        name: 'RENTRER',
        title: 'Accès initial',
        description: 'Exploitation des vulnérabilités pour obtenir un accès',
        actions: [
          {
            description: 'Exploitation des vulnérabilités identifiées dans l\'analyse CTI',
            techniques: ctiData.techniques?.slice(0, 3).map(t => t.techniqueId) || ['T1190']
          }
        ]
      },
      {
        name: 'TROUVER',
        title: 'Mouvement latéral',
        description: 'Navigation dans le système compromis',
        actions: [
          {
            description: 'Escalade de privilèges et mouvement latéral vers les actifs critiques',
            techniques: ['T1078', 'T1021']
          }
        ]
      },
      {
        name: 'EXPLOITER',
        title: 'Objectif final',
        description: 'Réalisation de l\'objectif d\'attaque',
        actions: [
          {
            description: 'Exécution de l\'objectif final selon le profil d\'attaquant',
            techniques: ['T1041', 'T1486']
          }
        ]
      }
    ],
    assetsUsed: ctiData.assets?.map(a => a.name) || [],
    generatedAt: new Date().toISOString(),
    ctiDataUsed: {
      vulnerabilitiesCount: ctiData.vulnerabilities?.length || 0,
      techniquesCount: ctiData.techniques?.length || 0,
      assetsCount: ctiData.assets?.length || 0
    }
  }];
};

/**
 * Save CTI-generated scenarios with React Flow state to database
 * @param {string} analysisId - Analysis ID
 * @param {Array} scenarios - Generated scenarios
 * @param {Object} reactFlowState - React Flow nodes and edges state
 * @returns {Promise<Object>} Save result
 */
export const saveCTIScenarios = async (analysisId, scenarios, reactFlowState = null) => {
  try {
    console.log('Service: Saving CTI scenarios', {
      analysisId,
      scenariosCount: scenarios?.length,
      hasReactFlowState: !!reactFlowState
    });

    const payload = {
      scenarios,
      reactFlowState,
      generatedAt: new Date().toISOString(),
      source: 'CTI-AI-Generated'
    };

    console.log('Service: Payload being sent:', payload);

    const response = await api.post(`/analyses/${analysisId}/cti-operational-scenarios`, payload);

    console.log('Service: Save response:', response.data);
    return response.data;
  } catch (error) {
    console.error('Error saving CTI operational scenarios:', error);
    console.error('Service: Full error details:', {
      message: error.message,
      status: error.status,
      data: error.data,
      response: error.response
    });
    throw error;
  }
};

/**
 * Load CTI-generated scenarios with React Flow state from database
 * @param {string} analysisId - Analysis ID
 * @returns {Promise<Object>} Saved scenarios with React Flow state
 */
export const loadCTIScenarios = async (analysisId) => {
  try {
    const response = await api.get(`/analyses/${analysisId}/cti-operational-scenarios`);
    return response.data;
  } catch (error) {
    console.error('Error loading CTI operational scenarios:', error);
    // Return empty state if no data found
    if (error.response?.status === 404) {
      return {
        success: true,
        data: {
          scenarios: [],
          reactFlowState: null,
          lastSaved: null
        }
      };
    }
    throw error;
  }
};
