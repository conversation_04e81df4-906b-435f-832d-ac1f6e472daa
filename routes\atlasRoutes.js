// routes/atlasRoutes.js
const express = require('express');
const router = express.Router();

// NOTE: These routes are intentionally PUBLIC (no authentication required)
// They serve as proxy routes for AI/ML threat intelligence and should be accessible
// without authentication to allow CTI analysis functionality

// Middleware to explicitly bypass any global authentication
router.use((req, res, next) => {
  // Remove any authorization headers that might cause issues
  delete req.headers.authorization;
  console.log(`[ATLAS Proxy] ${req.method} ${req.path} - Authentication bypassed`);
  next();
});

// @route   GET /api/atlas/test
// @desc    Test endpoint to verify ATLAS routes are working
// @access  Public (no auth required)
router.get('/test', (req, res) => {
  console.log('[ATLAS Proxy] Test endpoint called - routes are working!');
  res.json({
    success: true,
    message: 'ATLAS proxy routes are working',
    timestamp: new Date().toISOString(),
    source: 'MITRE ATLAS AI/ML Threats'
  });
});

// @route   GET /api/atlas/threats
// @desc    Get MITRE ATLAS AI/ML threats (enhanced local database)
// @access  Public
router.get('/threats', async (req, res) => {
  try {
    console.log('[ATLAS Proxy] AI/ML threats request for asset type:', req.query.assetType);

    // Enhanced MITRE ATLAS AI/ML threat database
    const atlasThreats = [
      {
        id: 'AML.T0001',
        name: 'Model Inversion',
        description: 'An adversary may attempt to infer sensitive information about the training data or model parameters by analyzing the model\'s outputs.',
        tactics: ['ML Model Access'],
        techniques: ['Inference'],
        platforms: ['Machine Learning Models'],
        severity: 'HIGH',
        impact: 'Data exposure, privacy violation',
        mitigation: 'Implement differential privacy, output perturbation',
        references: [
          {
            source_name: 'MITRE ATLAS',
            url: 'https://atlas.mitre.org/techniques/AML.T0001',
            external_id: 'AML.T0001'
          }
        ],
        applicableAssetTypes: ['ai_system', 'ml_model', 'data_processing']
      },
      {
        id: 'AML.T0002',
        name: 'Model Extraction',
        description: 'An adversary may attempt to steal a machine learning model by querying it and using the responses to train a substitute model.',
        tactics: ['ML Model Access'],
        techniques: ['Extraction'],
        platforms: ['Machine Learning Models'],
        severity: 'HIGH',
        impact: 'Intellectual property theft, competitive advantage loss',
        mitigation: 'Rate limiting, query monitoring, watermarking',
        references: [
          {
            source_name: 'MITRE ATLAS',
            url: 'https://atlas.mitre.org/techniques/AML.T0002',
            external_id: 'AML.T0002'
          }
        ],
        applicableAssetTypes: ['ai_system', 'ml_model', 'api_service']
      },
      {
        id: 'AML.T0003',
        name: 'Adversarial Examples',
        description: 'An adversary may craft inputs designed to cause a machine learning model to make incorrect predictions.',
        tactics: ['ML Attack Staging'],
        techniques: ['Evasion'],
        platforms: ['Machine Learning Models'],
        severity: 'CRITICAL',
        impact: 'Model malfunction, incorrect decisions, safety risks',
        mitigation: 'Adversarial training, input validation, ensemble methods',
        references: [
          {
            source_name: 'MITRE ATLAS',
            url: 'https://atlas.mitre.org/techniques/AML.T0003',
            external_id: 'AML.T0003'
          }
        ],
        applicableAssetTypes: ['ai_system', 'ml_model', 'autonomous_system']
      },
      {
        id: 'AML.T0004',
        name: 'Data Poisoning',
        description: 'An adversary may introduce malicious data into the training dataset to compromise the model\'s performance.',
        tactics: ['ML Attack Staging'],
        techniques: ['Poisoning'],
        platforms: ['Training Data'],
        severity: 'CRITICAL',
        impact: 'Model corruption, backdoor insertion, performance degradation',
        mitigation: 'Data validation, anomaly detection, secure data pipelines',
        references: [
          {
            source_name: 'MITRE ATLAS',
            url: 'https://atlas.mitre.org/techniques/AML.T0004',
            external_id: 'AML.T0004'
          }
        ],
        applicableAssetTypes: ['ai_system', 'ml_model', 'data_processing', 'training_pipeline']
      },
      {
        id: 'AML.T0005',
        name: 'Model Backdoor',
        description: 'An adversary may embed hidden functionality in a machine learning model that can be triggered by specific inputs.',
        tactics: ['Persistence'],
        techniques: ['Backdoor'],
        platforms: ['Machine Learning Models'],
        severity: 'CRITICAL',
        impact: 'Unauthorized access, data exfiltration, system compromise',
        mitigation: 'Model verification, behavioral analysis, secure training',
        references: [
          {
            source_name: 'MITRE ATLAS',
            url: 'https://atlas.mitre.org/techniques/AML.T0005',
            external_id: 'AML.T0005'
          }
        ],
        applicableAssetTypes: ['ai_system', 'ml_model', 'third_party_model']
      },
      {
        id: 'AML.T0006',
        name: 'Membership Inference',
        description: 'An adversary may determine whether a specific data point was used in the training dataset of a machine learning model.',
        tactics: ['Collection'],
        techniques: ['Inference'],
        platforms: ['Machine Learning Models'],
        severity: 'MEDIUM',
        impact: 'Privacy violation, sensitive data exposure',
        mitigation: 'Differential privacy, data anonymization, access controls',
        references: [
          {
            source_name: 'MITRE ATLAS',
            url: 'https://atlas.mitre.org/techniques/AML.T0006',
            external_id: 'AML.T0006'
          }
        ],
        applicableAssetTypes: ['ai_system', 'ml_model', 'data_processing']
      },
      {
        id: 'AML.T0007',
        name: 'Supply Chain Compromise',
        description: 'An adversary may compromise machine learning models, datasets, or frameworks in the supply chain.',
        tactics: ['Initial Access'],
        techniques: ['Supply Chain'],
        platforms: ['ML Frameworks', 'Pre-trained Models'],
        severity: 'HIGH',
        impact: 'Widespread compromise, backdoor distribution, trust violation',
        mitigation: 'Vendor verification, model provenance, security scanning',
        references: [
          {
            source_name: 'MITRE ATLAS',
            url: 'https://atlas.mitre.org/techniques/AML.T0007',
            external_id: 'AML.T0007'
          }
        ],
        applicableAssetTypes: ['ai_system', 'ml_model', 'third_party_model', 'ml_framework']
      }
    ];

    // Filter threats based on asset type if provided
    const assetType = req.query.assetType;
    let filteredThreats = atlasThreats;

    if (assetType) {
      filteredThreats = atlasThreats.filter(threat =>
        threat.applicableAssetTypes.includes(assetType) ||
        threat.applicableAssetTypes.includes('ai_system') // AI systems are generally applicable
      );
    }

    console.log('[ATLAS Proxy] AI/ML threats response:', {
      totalThreats: atlasThreats.length,
      filteredThreats: filteredThreats.length,
      assetType: assetType || 'all'
    });

    res.json({
      threats: filteredThreats,
      totalCount: filteredThreats.length,
      allThreatsCount: atlasThreats.length,
      assetType: assetType || 'all',
      source: 'MITRE ATLAS Enhanced Database',
      lastUpdated: new Date().toISOString()
    });

  } catch (error) {
    console.error('[ATLAS Proxy] AI/ML threats error:', {
      message: error.message
    });

    res.status(500).json({
      error: 'ATLAS threats request failed',
      message: error.message,
      status: 500
    });
  }
});

// @route   GET /api/atlas/threat/:id
// @desc    Get specific ATLAS threat by ID
// @access  Public
router.get('/threat/:id', async (req, res) => {
  try {
    const threatId = req.params.id;
    console.log('[ATLAS Proxy] Specific threat request for ID:', threatId);

    // Get all threats first
    const threatsResponse = await axios.get(`${req.protocol}://${req.get('host')}/api/atlas/threats`);
    const threats = threatsResponse.data.threats;

    // Find specific threat
    const threat = threats.find(t => t.id === threatId);

    if (!threat) {
      return res.status(404).json({
        error: 'Threat not found',
        message: `ATLAS threat with ID ${threatId} not found`,
        status: 404
      });
    }

    console.log('[ATLAS Proxy] Found threat:', threat.name);

    res.json({
      threat: threat,
      source: 'MITRE ATLAS Enhanced Database'
    });

  } catch (error) {
    console.error('[ATLAS Proxy] Specific threat error:', {
      message: error.message
    });

    res.status(500).json({
      error: 'ATLAS threat request failed',
      message: error.message,
      status: 500
    });
  }
});

module.exports = router;
