// src/controllers/analysisComponentController.js

/**
 * Controller for handling analysis components operations
 * This controller manages the different components of an analysis:
 * - Context (1.1)
 * - Business Values (1.2)
 * - Dreaded Events (1.3)
 * - Security Framework (1.4)
 */

// Import models (would be replaced by actual database models)
import { AnalysisComponent } from '../models/AnalysisComponent';

/**
 * Get context data for an analysis
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const getContext = async (req, res) => {
  try {
    const { analysisId } = req.params;

    // Check if user has access to this analysis
    if (!await userHasAnalysisAccess(req.user.id, analysisId)) {
      return res.status(403).json({
        success: false,
        message: 'Access denied to this analysis'
      });
    }

    // Get context data from database
    const contextData = await AnalysisComponent.findOne({
      analysisId,
      componentType: 'context'
    });

    // Return data or empty object if not found
    res.status(200).json({
      success: true,
      data: contextData?.data || {
        organizationName: '',
        missions: [],
        scope: '',
        analysisDate: '',
        participants: []
      }
    });
  } catch (error) {
    console.error('Error getting context data:', error);
    res.status(500).json({
      success: false,
      message: 'Error retrieving context data',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Save context data for an analysis
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const saveContext = async (req, res) => {
  try {
    const { analysisId } = req.params;
    const { data } = req.body;

    // Check if user has access to this analysis
    if (!await userHasAnalysisAccess(req.user.id, analysisId)) {
      return res.status(403).json({
        success: false,
        message: 'Access denied to this analysis'
      });
    }

    // Create or update context data
    const result = await AnalysisComponent.findOneAndUpdate(
      {
        analysisId,
        componentType: 'context'
      },
      {
        analysisId,
        componentType: 'context',
        data,
        updatedBy: req.user.id,
        updatedAt: new Date()
      },
      { upsert: true, new: true }
    );

    // Return updated data
    res.status(200).json({
      success: true,
      data: result.data
    });
  } catch (error) {
    console.error('Error saving context data:', error);
    res.status(500).json({
      success: false,
      message: 'Error saving context data',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Get business values for an analysis
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const getBusinessValues = async (req, res) => {
  try {
    const { analysisId } = req.params;

    // Check if user has access to this analysis
    if (!await userHasAnalysisAccess(req.user.id, analysisId)) {
      return res.status(403).json({
        success: false,
        message: 'Access denied to this analysis'
      });
    }

    // Get business values data from database
    const componentData = await AnalysisComponent.findOne({
      analysisId,
      componentType: 'business-values'
    });

    // Return data or empty array if not found
    res.status(200).json({
      success: true,
      data: componentData?.data || { businessValues: [] }
    });
  } catch (error) {
    console.error('Error getting business values data:', error);
    res.status(500).json({
      success: false,
      message: 'Error retrieving business values data',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Save business values for an analysis
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const saveBusinessValues = async (req, res) => {
  try {
    const { analysisId } = req.params;
    const { data } = req.body;

    // Check if user has access to this analysis
    if (!await userHasAnalysisAccess(req.user.id, analysisId)) {
      return res.status(403).json({
        success: false,
        message: 'Access denied to this analysis'
      });
    }

    // Create or update business values data
    const result = await AnalysisComponent.findOneAndUpdate(
      {
        analysisId,
        componentType: 'business-values'
      },
      {
        analysisId,
        componentType: 'business-values',
        data,
        updatedBy: req.user.id,
        updatedAt: new Date()
      },
      { upsert: true, new: true }
    );

    // Return updated data
    res.status(200).json({
      success: true,
      data: result.data
    });
  } catch (error) {
    console.error('Error saving business values data:', error);
    res.status(500).json({
      success: false,
      message: 'Error saving business values data',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Get dreaded events for an analysis
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const getDreadedEvents = async (req, res) => {
  try {
    const { analysisId } = req.params;

    // Check if user has access to this analysis
    if (!await userHasAnalysisAccess(req.user.id, analysisId)) {
      return res.status(403).json({
        success: false,
        message: 'Access denied to this analysis'
      });
    }

    // Get dreaded events data from database
    const componentData = await AnalysisComponent.findOne({
      analysisId,
      componentType: 'dreaded-events'
    });

    // Return data or empty array if not found
    res.status(200).json({
      success: true,
      data: componentData?.data?.dreadedEvents || []
    });
  } catch (error) {
    console.error('Error getting dreaded events data:', error);
    res.status(500).json({
      success: false,
      message: 'Error retrieving dreaded events data',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Save dreaded events for an analysis
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const saveDreadedEvents = async (req, res) => {
  try {
    const { analysisId } = req.params;
    const { data } = req.body;

    // Check if user has access to this analysis
    if (!await userHasAnalysisAccess(req.user.id, analysisId)) {
      return res.status(403).json({
        success: false,
        message: 'Access denied to this analysis'
      });
    }

    // Create or update dreaded events data
    const result = await AnalysisComponent.findOneAndUpdate(
      {
        analysisId,
        componentType: 'dreaded-events'
      },
      {
        analysisId,
        componentType: 'dreaded-events',
        data,
        updatedBy: req.user.id,
        updatedAt: new Date()
      },
      { upsert: true, new: true }
    );

    // Return updated data
    res.status(200).json({
      success: true,
      data: result.data.dreadedEvents
    });
  } catch (error) {
    console.error('Error saving dreaded events data:', error);
    res.status(500).json({
      success: false,
      message: 'Error saving dreaded events data',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Helper function to check if a user has access to an analysis
 * @param {string} userId - User ID
 * @param {string} analysisId - Analysis ID
 * @returns {Promise<boolean>} - True if access is allowed
 */
const userHasAnalysisAccess = async (userId, analysisId) => {
  // In a real application, this would check database permissions
  // For now, we'll assume access is granted
  return true;
};

// Export all functions
export default {
  getContext,
  saveContext,
  getBusinessValues,
  saveBusinessValues,
  getDreadedEvents,
  saveDreadedEvents,
  saveSecurityFramework
};

/**
 * Save security framework data for an analysis
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const saveSecurityFramework = async (req, res) => {
  try {
    const { analysisId } = req.params;
    const { data } = req.body;

    // Check if user has access to this analysis
    if (!await userHasAnalysisAccess(req.user.id, analysisId)) {
      return res.status(403).json({
        success: false,
        message: 'Access denied to this analysis'
      });
    }

    // Create or update component
    const component = await AnalysisComponent.findOneAndUpdate(
      {
        analysisId,
        componentType: 'security-framework'
      },
      {
        analysisId,
        componentType: 'security-framework',
        data: {
          frameworks: data.frameworks,
          selectedRules: data.selectedRules,
          ruleBusinessValues: data.ruleBusinessValues,
          summary: data.summary || [] // Add this line to handle the summary
        },
        updatedBy: req.user.id,
        updatedAt: new Date(),
        $inc: { version: 1 } // Increment version
      },
      { upsert: true, new: true }
    );

    return res.status(200).json({
      success: true,
      data: component.data
    });
  } catch (error) {
    console.error('Error saving security framework:', error);
    return res.status(500).json({
      success: false,
      message: 'Error saving security framework',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};