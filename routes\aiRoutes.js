const { GoogleGenerativeAI } = require("@google/generative-ai");
const dotenv = require('dotenv');
const express = require('express');
const router = express.Router();
const aiController = require('../controllers/aiController');
const { protect } = require('../middleware/authMiddleware');

dotenv.config(); // Load environment variables

const apiKey = process.env.GEMINI_API_KEY;
if (!apiKey) {
  console.error("Error: GEMINI_API_KEY is not set in the environment variables.");
  // Optionally, throw an error or use a default non-functional key
  // to prevent the app from crashing but indicate the issue.
}

// Initialize the Gemini client
// Ensure GEMINI_API_KEY is set in your .env file
const genAI = new GoogleGenerativeAI(apiKey || 'YOUR_API_KEY_PLACEHOLDER'); // Use placeholder if key is missing

// Choose a model (e.g., gemini-pro)
const model = genAI.getGenerativeModel({ model: "gemini-pro" });

/**
 * Generates content using the Gemini API.
 * @param {string} prompt - The text prompt to send to the model.
 * @returns {Promise<string>} The generated text content.
 * @throws {Error} If the API call fails or returns an error.
 */
const generateContent = async (prompt) => {
  if (!apiKey) {
      throw new Error("Gemini API key is not configured. Cannot generate content.");
  }
  try {
    console.log("Sending prompt to Gemini API...");
    const result = await model.generateContent(prompt);
    const response = await result.response;

    // Check for safety ratings or other issues if needed
    // console.log(JSON.stringify(response, null, 2));

    if (!response || !response.text) {
        // Attempt to get more detail if possible
        const candidate = response?.candidates?.[0];
        if (candidate?.finishReason && candidate.finishReason !== 'STOP') {
            throw new Error(`Gemini generation stopped due to ${candidate.finishReason}. Safety ratings: ${JSON.stringify(candidate.safetyRatings)}`);
        }
        throw new Error("Gemini API returned an empty or invalid response.");
    }

    const text = response.text();
    console.log("Received response from Gemini API.");
    return text;
  } catch (error) {
    console.error("Error calling Gemini API:", error);
    // Re-throw a more specific error or handle it as needed
    throw new Error(`Gemini API request failed: ${error.message}`);
  }
};

// Check if controller function is loaded correctly
console.log('[routes/aiRoutes.js] Type of suggestDreadedEvents:', typeof aiController.suggestDreadedEvents);
console.log('[routes/aiRoutes.js] Type of suggestSecurityControls:', typeof aiController.suggestSecurityControls);

// Check the protect middleware
console.log('[routes/aiRoutes.js] Type of protect middleware:', typeof protect);

// @desc    Get AI suggestions for dreaded events
// @route   POST /api/ai/suggest-dreaded-events
// @access  Private (requires login)
// Ensure both protect and aiController.suggestDreadedEvents are functions
if (typeof protect === 'function' && typeof aiController.suggestDreadedEvents === 'function') {
    router.post('/suggest-dreaded-events', protect, aiController.suggestDreadedEvents);
} else {
    console.error('[routes/aiRoutes.js] ERROR: Middleware or controller suggestDreadedEvents is not a function!');
    // Optionally, define a fallback route or throw an error during setup
}

// @desc    Get AI suggestions for security controls
// @route   POST /api/ai/suggest-security-controls
// @access  Private (requires login)
if (typeof protect === 'function' && typeof aiController.suggestSecurityControls === 'function') {
    router.post('/suggest-security-controls', protect, aiController.suggestSecurityControls);
} else {
    // Log error only if the controller function is specifically missing
    if (typeof aiController.suggestSecurityControls !== 'function') {
      console.error('[routes/aiRoutes.js] ERROR: Controller suggestSecurityControls is not a function!');
    }
    // We assume `protect` middleware check passed above if it didn't log an error there.
}

// @desc    Get AI suggestions for sources de risque
// @route   POST /api/ai/suggest-sources-risque
// @access  Private (requires login)
if (typeof protect === 'function' && typeof aiController.suggestSourcesRisque === 'function') {
    router.post('/suggest-sources-risque', protect, aiController.suggestSourcesRisque);
} else {
    // Log error only if the controller function is specifically missing
    if (typeof aiController.suggestSourcesRisque !== 'function') {
      console.error('[routes/aiRoutes.js] ERROR: Controller suggestSourcesRisque is not a function!');
    }
    // We assume `protect` middleware check passed above if it didn't log an error there.
}

// @desc    Generate ecosystem security measures
// @route   POST /api/ai/generate-ecosystem-measures
// @access  Private (requires login)
if (typeof protect === 'function' && typeof aiController.generateEcosystemMeasures === 'function') {
    router.post('/generate-ecosystem-measures', protect, aiController.generateEcosystemMeasures);
} else {
    // Log error only if the controller function is specifically missing
    if (typeof aiController.generateEcosystemMeasures !== 'function') {
      console.error('[routes/aiRoutes.js] ERROR: Controller generateEcosystemMeasures is not a function!');
    }
    // We assume `protect` middleware check passed above if it didn't log an error there.
}

// @desc    Generate operational scenarios from attack paths
// @route   POST /api/ai/generate-operational-scenarios
// @access  Private (requires login)
if (typeof protect === 'function' && typeof aiController.generateOperationalScenarios === 'function') {
    router.post('/generate-operational-scenarios', protect, aiController.generateOperationalScenarios);
} else {
    // Log error only if the controller function is specifically missing
    if (typeof aiController.generateOperationalScenarios !== 'function') {
      console.error('[routes/aiRoutes.js] ERROR: Controller generateOperationalScenarios is not a function!');
    }
    // We assume `protect` middleware check passed above if it didn't log an error there.
}

// @desc    Generate enhanced operational scenarios with threat intelligence
// @route   POST /api/ai/generate-enhanced-operational-scenarios
// @access  Private (requires login)
if (typeof protect === 'function' && typeof aiController.generateEnhancedOperationalScenarios === 'function') {
    router.post('/generate-enhanced-operational-scenarios', protect, aiController.generateEnhancedOperationalScenarios);
} else {
    // Log error only if the controller function is specifically missing
    if (typeof aiController.generateEnhancedOperationalScenarios !== 'function') {
      console.error('[routes/aiRoutes.js] ERROR: Controller generateEnhancedOperationalScenarios is not a function!');
    }
}

console.log('[routes/aiRoutes.js] Exporting router type:', typeof router);
console.log('[routes/aiRoutes.js] Is router instance?:', router && typeof router.stack !== 'undefined' ? 'Yes' : 'No');

// Export the generateContent function for use in controllers
module.exports = router; // Make sure this line is exactly like this
module.exports.generateJsonContent = generateContent;
