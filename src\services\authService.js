import api from '../api/apiClient';
import { clearAuthData, saveAuthData, getUserInfo, getAccessToken } from '../utils/tokenStorage';

export const authService = {
  login: async (email, password) => {
    try {
      const response = await api.post('/auth/login', { email, password });
      if (response.data.success) {
        // Store the auth data using the utility function
        saveAuthData(response.data.data);
        return response.data;
      }
      throw new Error(response.data.message || 'Login failed');
    } catch (error) {
      // Log without revealing credentials
      console.error('Login error occurred');
      throw error;
    }
  },

  logout: async () => {
    try {
      // Call the logout endpoint
      await api.post('/auth/logout');
    } catch (error) {
      // Log without revealing sensitive details
      console.error('Logout API error occurred');
    } finally {
      // Clear all auth data regardless of API call success
      clearAuthData();
    }
  },

  getCurrentUser: () => {
    return getUserInfo();
  },

  getToken: () => {
    return getAccessToken();
  },

  isAuthenticated: () => {
    return !!getAccessToken();
  },

  // Get the appropriate dashboard path based on user role
  getDashboardPath: () => {
    const user = authService.getCurrentUser();
    if (!user) return '/login';

    switch (user.role) {
      case 'superadmin':
        return '/superadmin/dashboard';
      case 'admin':
        return '/admin/dashboard';
      case 'simpleuser':
        return '/app/dashboard';
      default:
        return '/login';
    }
  },

  // Check if user has required role
  hasRole: (requiredRole) => {
    const user = authService.getCurrentUser();
    if (!user) return false;

    // Superadmin has access to everything
    if (user.role === 'superadmin') return true;

    // Admin has access to admin and app routes
    if (user.role === 'admin') {
      return ['admin', 'app'].includes(requiredRole);
    }

    // Simple user only has access to app routes
    if (user.role === 'simpleuser') {
      return requiredRole === 'app';
    }

    return false;
  }
};