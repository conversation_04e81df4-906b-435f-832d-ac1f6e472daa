import api from '../api/apiClient';
import { clearAuthData } from '../utils/tokenStorage';

export const authService = {
  login: async (email, password) => {
    try {
      const response = await api.post('/auth/login', { email, password });
      if (response.data.success) {
        // Store the token and user data
        localStorage.setItem('token', response.data.data.tokens.accessToken);
        localStorage.setItem('user', JSON.stringify(response.data.data.user));
        return response.data;
      }
      throw new Error(response.data.message || 'Login failed');
    } catch (error) {
      console.error('Login error:', error);
      throw error;
    }
  },

  logout: async () => {
    try {
      // Call the logout endpoint
      await api.post('/auth/logout');
    } catch (error) {
      console.error('Logout API error:', error);
    } finally {
      // Clear all auth data regardless of API call success
      clearAuthData();
      localStorage.removeItem('token');
      localStorage.removeItem('user');
    }
  },

  getCurrentUser: () => {
    const userStr = localStorage.getItem('user');
    return userStr ? JSON.parse(userStr) : null;
  },

  getToken: () => {
    return localStorage.getItem('token');
  },

  isAuthenticated: () => {
    return !!localStorage.getItem('token');
  },

  // Get the appropriate dashboard path based on user role
  getDashboardPath: () => {
    const user = authService.getCurrentUser();
    if (!user) return '/login';

    switch (user.role) {
      case 'superadmin':
        return '/superadmin/dashboard';
      case 'admin':
        return '/admin/dashboard';
      case 'simpleuser':
        return '/app/dashboard';
      default:
        return '/login';
    }
  },

  // Check if user has required role
  hasRole: (requiredRole) => {
    const user = authService.getCurrentUser();
    if (!user) return false;

    // Superadmin has access to everything
    if (user.role === 'superadmin') return true;

    // Admin has access to admin and app routes
    if (user.role === 'admin') {
      return ['admin', 'app'].includes(requiredRole);
    }

    // Simple user only has access to app routes
    if (user.role === 'simpleuser') {
      return requiredRole === 'app';
    }

    return false;
  }
}; 