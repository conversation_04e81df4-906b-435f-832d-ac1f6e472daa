@echo off
echo Creating new-build folder...

REM Navigate to correct directory
cd /d "C:\Users\<USER>\Desktop\EBROS RM Project\APP\Atelier 1\my-ebios-app"

REM Check if build exists, if not run npm build
if not exist build (
    echo No build folder found, running npm run build...
    npm run build
)

REM Copy build to new-build
if exist build (
    if exist new-build rmdir /s /q new-build
    xcopy build new-build\ /E /I /Y
    echo ✅ new-build folder created successfully!
    dir new-build
) else (
    echo ❌ Build folder not found
)

pause
