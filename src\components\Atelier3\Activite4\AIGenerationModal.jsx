// src/components/Atelier3/Activite4/AIGenerationModal.jsx
import React, { useState, useEffect } from 'react';
import { X, Loader } from 'lucide-react';
import api from '../../../api/apiClient';
import ecosystemMeasuresService from '../../../services/ecosystemMeasuresService';

const AIGenerationModal = ({ onClose, onGenerate, analysisId }) => {
  const [isLoading, setIsLoading] = useState(false);
  const [isGenerating, setIsGenerating] = useState(false);
  const [stakeholders, setStakeholders] = useState([]);
  const [attackPaths, setAttackPaths] = useState([]);
  const [selectedStakeholders, setSelectedStakeholders] = useState([]);
  const [selectedAttackPaths, setSelectedAttackPaths] = useState([]);
  const [generationFocus, setGenerationFocus] = useState('balanced');
  const [measuresPerPath, setMeasuresPerPath] = useState(5); // Default to 5 measures per attack path
  const [generatedMeasures, setGeneratedMeasures] = useState([]);
  const [error, setError] = useState(null);

  // Load stakeholders and attack paths
  useEffect(() => {
    if (analysisId) {
      loadData();
    }
  }, [analysisId]);

  const loadData = async () => {
    setIsLoading(true);
    setError(null);
    try {
      // Load stakeholders - use the correct endpoint with atelier3 prefix
      const stakeholdersResponse = await api.get(`/analyses/${analysisId}/atelier3/stakeholders`);
      if (stakeholdersResponse.success && stakeholdersResponse.data) {
        setStakeholders(stakeholdersResponse.data);
      }

      // Load attack paths
      const pathsResponse = await api.get(`/analyses/${analysisId}/attack-paths`);
      if (pathsResponse.success && pathsResponse.data) {
        // Process attack paths to include reference codes if not present
        const processedPaths = Array.isArray(pathsResponse.data)
          ? pathsResponse.data.map((path, index) => ({
              ...path,
              referenceCode: path.referenceCode || `CA${String(index + 1).padStart(2, '0')}`
            }))
          : [];

        setAttackPaths(processedPaths);
      }
    } catch (error) {
      console.error('Error loading data for AI generation:', error);
      setError('Erreur lors du chargement des données. Veuillez réessayer.');
    } finally {
      setIsLoading(false);
    }
  };

  // Generate measures using AI
  const generateMeasures = async () => {
    setIsGenerating(true);
    setError(null);
    try {
      // Use the ecosystem measures service
      const response = await ecosystemMeasuresService.generateMeasures({
        analysisId,
        attackPaths: selectedAttackPaths,
        focus: generationFocus,
        measuresPerPath: measuresPerPath // Add the number of measures per attack path
      });

      if (response.success && response.data) {
        setGeneratedMeasures(response.data);
      } else {
        throw new Error(response.message || 'Erreur lors de la génération');
      }
    } catch (error) {
      console.error('Error generating measures:', error);
      setError(`Erreur: ${error.message || 'Problème de génération avec l\'IA'}`);
    } finally {
      setIsGenerating(false);
    }
  };

  // Apply generated measures
  const applyMeasures = () => {
    // Make sure each measure has the attackPaths property
    const measuresToApply = generatedMeasures.map(measure => {
      // If attackPaths is missing or empty but we have sourceRisks, try to match them
      if ((!measure.attackPaths || measure.attackPaths.length === 0) &&
          measure.sourceRisks && measure.sourceRisks.length > 0) {

        // Find matching attack paths based on sourceRisks
        const matchingPaths = attackPaths.filter(path =>
          measure.sourceRisks.some(source =>
            (path.sourceRiskName && path.sourceRiskName.includes(source)) ||
            (path.sourceRiskDescription && path.sourceRiskDescription.includes(source)) ||
            (source && path.sourceRiskName && source.includes(path.sourceRiskName)) ||
            (source && path.sourceRiskDescription && source.includes(path.sourceRiskDescription))
          )
        );

        return {
          ...measure,
          attackPaths: matchingPaths.map(path => path.id)
        };
      }

      return measure;
    });

    onGenerate(measuresToApply);
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-xl shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-hidden flex flex-col">
        {/* Header */}
        <div className="bg-gradient-to-r from-purple-600 to-indigo-600 px-6 py-4">
          <div className="flex justify-between items-center">
            <div>
              <h3 className="text-xl font-semibold text-white">Générer des mesures de sécurité avec l'IA</h3>
              <p className="text-purple-100 text-sm mt-1">Utilisez l'intelligence artificielle pour créer des mesures adaptées à vos chemins d'attaque</p>
            </div>
            <button
              onClick={onClose}
              className="text-purple-100 hover:text-white hover:bg-purple-700 p-2 rounded-lg transition-colors duration-150"
            >
              <X size={20} />
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto flex-grow">
          {error && (
            <div className="mb-4 bg-red-50 border-l-4 border-red-500 p-4 rounded-md">
              <p className="text-sm text-red-700">{error}</p>
            </div>
          )}

          {isLoading ? (
            <div className="flex justify-center items-center h-40">
              <Loader className="animate-spin text-blue-500" size={24} />
              <span className="ml-2 text-gray-600">Chargement des données...</span>
            </div>
          ) : isGenerating ? (
            <div className="flex flex-col justify-center items-center h-40">
              <Loader className="animate-spin text-blue-500" size={24} />
              <span className="mt-2 text-gray-600">Génération des mesures en cours...</span>
              <span className="mt-1 text-xs text-gray-500">Cela peut prendre quelques instants</span>
            </div>
          ) : !generatedMeasures.length ? (
            <div className="space-y-6">
              {/* Instruction */}
              <div className="mb-6">
                <div className="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 p-6 rounded-lg">
                  <div className="flex items-start">
                    <div className="flex-shrink-0">
                      <svg className="h-6 w-6 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </div>
                    <div className="ml-3">
                      <h4 className="text-sm font-semibold text-blue-900 mb-1">Comment ça fonctionne</h4>
                      <p className="text-sm text-blue-700">
                        Sélectionnez les chemins d'attaque pour lesquels vous souhaitez générer des mesures de sécurité.
                        L'IA créera des mesures spécifiques pour traiter les sources de risque et protéger les objectifs visés.
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Attack path selection */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Chemins d'attaque
                </label>
                <div className="max-h-60 overflow-y-auto border rounded-md p-3 bg-gray-50">
                  {attackPaths.length === 0 ? (
                    <p className="text-sm text-gray-500 p-2">Aucun chemin d'attaque disponible</p>
                  ) : (
                    <div className="space-y-3">
                      {attackPaths.map(path => (
                        <div key={path.id} className="p-3 border rounded-md bg-white hover:bg-blue-50 transition-colors">
                          <div className="flex items-start">
                            <input
                              type="checkbox"
                              id={`path-${path.id}`}
                              checked={selectedAttackPaths.includes(path.id)}
                              onChange={() => {
                                setSelectedAttackPaths(prev =>
                                  prev.includes(path.id)
                                    ? prev.filter(id => id !== path.id)
                                    : [...prev, path.id]
                                );
                              }}
                              className="h-4 w-4 mt-1 text-blue-600 rounded"
                            />
                            <div className="ml-2 flex-grow">
                              <div className="flex items-center mb-1">
                                <span className="inline-flex items-center px-2 py-0.5 rounded-md text-xs font-bold bg-blue-100 text-blue-800 border border-blue-200 mr-2">
                                  {path.referenceCode}
                                </span>
                                <label htmlFor={`path-${path.id}`} className="text-sm font-medium text-gray-700 cursor-pointer">
                                  Chemin d'attaque
                                </label>
                              </div>
                              <div className="flex flex-col sm:flex-row sm:items-center text-sm">
                                <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-red-100 text-red-800 border border-red-200">
                                  {path.sourceRiskDescription || path.sourceRiskName || 'Source inconnue'}
                                </span>
                                <span className="mx-1 my-1 sm:my-0 text-gray-500">→</span>
                                <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800 border border-green-200">
                                  {path.objectifVise || 'Objectif non spécifié'}
                                </span>
                              </div>
                              {path.dreadedEventName && (
                                <div className="text-xs text-gray-500 mt-1">
                                  Événement redouté: {path.dreadedEventName}
                                </div>
                              )}
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
                <div className="mt-2 flex justify-between">
                  <button
                    type="button"
                    onClick={() => setSelectedAttackPaths([])}
                    className="text-xs text-gray-500 hover:text-gray-700"
                  >
                    Désélectionner tout
                  </button>
                  <button
                    type="button"
                    onClick={() => setSelectedAttackPaths(attackPaths.map(p => p.id))}
                    className="text-xs text-blue-500 hover:text-blue-700"
                  >
                    Sélectionner tout
                  </button>
                </div>
              </div>

              {/* Measures per attack path */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Nombre de mesures par chemin d'attaque
                </label>
                <div className="flex items-center">
                  <input
                    type="number"
                    min="1"
                    max="10"
                    value={measuresPerPath}
                    onChange={(e) => setMeasuresPerPath(Math.max(1, Math.min(10, parseInt(e.target.value) || 5)))}
                    className="w-20 rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 mr-3"
                  />
                  <span className="text-sm text-gray-500">
                    {selectedAttackPaths.length > 0 &&
                      `Total: environ ${selectedAttackPaths.length * measuresPerPath} mesures`}
                  </span>
                </div>
                <p className="mt-1 text-xs text-gray-500">
                  Chaque chemin d'attaque sélectionné générera ce nombre de mesures. Certaines mesures peuvent s'appliquer à plusieurs chemins d'attaque.
                </p>
              </div>

              {/* Generation focus */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Focus de la génération
                </label>
                <div className="grid grid-cols-3 gap-2">
                  <button
                    type="button"
                    onClick={() => setGenerationFocus('practical')}
                    className={`px-4 py-2 border rounded-md text-sm ${
                      generationFocus === 'practical'
                        ? 'bg-blue-50 border-blue-500 text-blue-700'
                        : 'border-gray-300 text-gray-700'
                    }`}
                  >
                    Pratique
                  </button>
                  <button
                    type="button"
                    onClick={() => setGenerationFocus('balanced')}
                    className={`px-4 py-2 border rounded-md text-sm ${
                      generationFocus === 'balanced'
                        ? 'bg-blue-50 border-blue-500 text-blue-700'
                        : 'border-gray-300 text-gray-700'
                    }`}
                  >
                    Équilibré
                  </button>
                  <button
                    type="button"
                    onClick={() => setGenerationFocus('comprehensive')}
                    className={`px-4 py-2 border rounded-md text-sm ${
                      generationFocus === 'comprehensive'
                        ? 'bg-blue-50 border-blue-500 text-blue-700'
                        : 'border-gray-300 text-gray-700'
                    }`}
                  >
                    Exhaustif
                  </button>
                </div>
                <p className="mt-1 text-xs text-gray-500">
                  {generationFocus === 'practical' && "Génère des mesures pratiques et faciles à mettre en œuvre."}
                  {generationFocus === 'balanced' && "Équilibre entre l'exhaustivité et la facilité de mise en œuvre."}
                  {generationFocus === 'comprehensive' && "Génère un ensemble complet de mesures couvrant tous les aspects de sécurité."}
                </p>
              </div>
            </div>
          ) : (
            <div className="space-y-4">
              <p className="text-sm text-gray-700 mb-4">
                L'IA a généré {generatedMeasures.length} mesures de sécurité. Vous pouvez les ajouter à votre plan ou générer de nouvelles mesures.
              </p>

              <div className="border rounded-md overflow-hidden">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Titre</th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Catégorie</th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Priorité</th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Chemins d'attaque</th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {generatedMeasures.map((measure, index) => (
                      <tr key={index} className="hover:bg-gray-50">
                        <td className="px-4 py-3 text-sm text-gray-900">{measure.title}</td>
                        <td className="px-4 py-3 text-sm text-gray-500">{measure.category}</td>
                        <td className="px-4 py-3 text-sm text-gray-500">{measure.priority}</td>
                        <td className="px-4 py-3">
                          <div className="flex flex-wrap gap-1">
                            {measure.attackPaths && measure.attackPaths.length > 0 ? (
                              measure.attackPaths.map((pathId, idx) => {
                                const path = attackPaths.find(p => p.id === pathId);
                                return path ? (
                                  <span key={idx} className="inline-flex items-center px-2 py-0.5 rounded-md text-xs font-medium bg-blue-100 text-blue-800 border border-blue-200 mr-1 mb-1">
                                    {path.referenceCode}
                                  </span>
                                ) : null;
                              })
                            ) : (
                              <span className="text-xs text-gray-500">Non spécifié</span>
                            )}

                            {/* Display source risks as additional info */}
                            {measure.sourceRisks && measure.sourceRisks.length > 0 && (
                              <div className="mt-1 text-xs text-gray-500">
                                Sources: {measure.sourceRisks.map((source, idx) =>
                                  <span key={idx} className="inline-flex items-center px-1 py-0.5 rounded text-xs bg-red-50 text-red-700 mr-1">
                                    {typeof source === 'string' && source.length > 15 ? source.substring(0, 15) + '...' : source}
                                  </span>
                                )}
                              </div>
                            )}
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="bg-gray-50 px-6 py-4 border-t border-gray-200">
          <div className="flex justify-between items-center">
            {!generatedMeasures.length ? (
              <>
                <div className="text-sm text-gray-600">
                  {selectedAttackPaths.length > 0 && (
                    <span>
                      {selectedAttackPaths.length} chemin{selectedAttackPaths.length > 1 ? 's' : ''} d'attaque sélectionné{selectedAttackPaths.length > 1 ? 's' : ''}
                    </span>
                  )}
                </div>
                <div className="flex space-x-3">
                  <button
                    onClick={onClose}
                    className="px-6 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 hover:bg-gray-50 hover:border-gray-400 transition-all duration-150"
                  >
                    Annuler
                  </button>
                  <button
                    onClick={generateMeasures}
                    disabled={isGenerating || selectedAttackPaths.length === 0}
                    className={`px-6 py-2 rounded-lg text-sm font-medium transition-all duration-150 ${
                      isGenerating || selectedAttackPaths.length === 0
                        ? 'bg-gray-400 text-white cursor-not-allowed'
                        : 'bg-purple-600 text-white hover:bg-purple-700 shadow-sm hover:shadow-md'
                    }`}
                  >
                    {isGenerating ? (
                      <span className="flex items-center">
                        <Loader className="animate-spin mr-2" size={16} />
                        Génération...
                      </span>
                    ) : (
                      'Générer les mesures'
                    )}
                  </button>
                </div>
              </>
            ) : (
              <>
                <div className="text-sm text-gray-600">
                  <span className="font-medium text-green-600">{generatedMeasures.length}</span> mesures générées avec succès
                </div>
                <div className="flex space-x-3">
                  <button
                    onClick={() => {
                      setGeneratedMeasures([]);
                    }}
                    className="px-6 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 hover:bg-gray-50 hover:border-gray-400 transition-all duration-150"
                  >
                    Générer d'autres mesures
                  </button>
                  <button
                    onClick={applyMeasures}
                    className="px-6 py-2 bg-green-600 rounded-lg text-sm font-medium text-white hover:bg-green-700 shadow-sm hover:shadow-md transition-all duration-150"
                  >
                    Appliquer ces mesures
                  </button>
                </div>
              </>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default AIGenerationModal;
