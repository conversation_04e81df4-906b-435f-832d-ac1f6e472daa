// backend/routes/activityLogRoutes.js
const express = require('express');
const { 
  getLogs, 
  getActivityStats
} = require('../controllers/activityLogController');
const { protect, restrictTo } = require('../middleware/authMiddleware');

const router = express.Router();

// All routes need authentication
router.use(protect);

// Get all logs - accessible by all users, but filtered by permission in controller
router.get('/', protect, getLogs);

// Get activity statistics - superadmin and admin, filtered by permission in controller
router.get('/stats', restrictTo('superadmin', 'admin'), getActivityStats);

module.exports = router;