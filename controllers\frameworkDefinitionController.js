// backend/controllers/frameworkDefinitionController.js
const mongoose = require('mongoose');
const FrameworkDefinition = require('../models/FrameworkDefinition');
const Analysis = require('../models/Analysis'); // Needed for access checks

/**
 * Checks if the requesting user has access to a specific analysis.
 * This is a common pattern, adapt if your logic differs significantly.
 * @param {String} analysisId - The ID of the analysis to check.
 * @param {String} userId - The ID of the user making the request.
 * @param {String} userRole - The role of the user making the request.
 * @param {String} userCompanyId - The company ID of the user (if applicable).
 * @returns {Promise<Object>} - { success: boolean, status?: number, message?: string, analysis?: object }
 */
async function checkAnalysisAccess(analysisId, userId, userRole, userCompanyId) {
    try {
        if (!mongoose.Types.ObjectId.isValid(analysisId)) {
            return { success: false, status: 400, message: 'Invalid analysis ID format.' };
        }

        const analysis = await Analysis.findById(analysisId);
        if (!analysis) {
            return { success: false, status: 404, message: 'Analysis not found.' };
        }

        // Check 1: Is the user the creator of the analysis?
        if (analysis.createdBy && analysis.createdBy.toString() === userId.toString()) {
            return { success: true, analysis };
        }

        // Check 2: Is the user a superadmin?
        if (userRole === 'superadmin') {
            return { success: true, analysis };
        }

        // Check 3: Is the user an admin within the same company?
        // This assumes your Analysis model has a companyId field
        if (userRole === 'admin' && analysis.companyId && userCompanyId && analysis.companyId.toString() === userCompanyId.toString()) {
             return { success: true, analysis };
        }

        // If none of the above, deny access
        return { success: false, status: 403, message: 'Access denied to this analysis.' };

    } catch (error) {
         console.error("Error checking analysis access:", error);
         return { success: false, status: 500, message: 'Server error during access check.' };
    }
}


/**
 * @desc    Create a new custom framework definition linked to an analysis
 * @route   POST /api/framework-definitions
 * @access  Private
 */
exports.createFrameworkDefinition = async (req, res) => {
    const { name, description, rules, analysisId } = req.body;
    const userId = req.user.id; // Assuming protect middleware adds user to req
    const userRole = req.user.role;
    const userCompanyId = req.user.companyId;

    // --- Basic Validation ---
    if (!name || !rules || !Array.isArray(rules) || rules.length === 0) {
        return res.status(400).json({ success: false, message: 'Missing required fields: name and rules array.' });
    }
    if (!analysisId) {
         return res.status(400).json({ success: false, message: 'Missing required field: analysisId.' });
    }
    // No need to validate analysisId format here, checkAnalysisAccess does it

    try {
        // --- Authorization Check ---
        const accessCheck = await checkAnalysisAccess(analysisId, userId, userRole, userCompanyId);
        if (!accessCheck.success) {
            return res.status(accessCheck.status).json({ success: false, message: accessCheck.message });
        }

        // --- Create Definition ---
        const newDefinition = new FrameworkDefinition({
            name: name.trim(),
            description: description?.trim() || '',
            rules: rules, // Assuming rules have {id, name, description} structure from frontend
            is_predefined: false, // Explicitly false for user creations via this route
            analysisId: analysisId,
            createdBy: userId,
        });

        const savedDefinition = await newDefinition.save();

        res.status(201).json({
            success: true,
            message: 'Framework definition created successfully.',
            data: savedDefinition
        });

    } catch (error) {
        console.error('Error creating framework definition:', error);
        if (error.name === 'ValidationError') {
             return res.status(400).json({ success: false, message: `Validation Error: ${error.message}` });
        }
        // Handle potential duplicate key errors if you add unique indexes
        if (error.code === 11000) {
             return res.status(400).json({ success: false, message: 'A framework with this name might already exist for this analysis.' });
        }
        res.status(500).json({ success: false, message: 'Server error creating framework definition.' });
    }
};


/**
 * @desc    Get framework definitions (predefined + custom for a specific analysis)
 * @route   GET /api/framework-definitions?analysisId=<analysis_id>
 * @access  Private
 */
exports.getFrameworkDefinitions = async (req, res) => {
    const { analysisId } = req.query;
    const userId = req.user.id;
    const userRole = req.user.role;
    const userCompanyId = req.user.companyId;

    if (!analysisId) {
        return res.status(400).json({ success: false, message: 'Missing required query parameter: analysisId.' });
    }
    // No need to validate analysisId format here, checkAnalysisAccess does it

    try {
         // --- Authorization Check ---
         const accessCheck = await checkAnalysisAccess(analysisId, userId, userRole, userCompanyId);
         if (!accessCheck.success) {
             // Allow fetching even if access check fails temporarily? Maybe not.
             // If the goal is strictly "only see definitions for analyses you can access", then return error.
             return res.status(accessCheck.status).json({ success: false, message: accessCheck.message });
         }

        // --- Fetch Definitions ---
        // Find all predefined OR those matching the specific analysisId
        const definitions = await FrameworkDefinition.find({
            $or: [
                { is_predefined: true },
                { analysisId: analysisId } // We already checked access to this analysisId
            ]
        })
        .populate('createdBy', 'name email') // Optionally populate creator info
        .sort({ is_predefined: -1, name: 1 }); // Show predefined first, then sort by name

        res.status(200).json({
            success: true,
            count: definitions.length,
            data: definitions
        });

    } catch (error) {
        console.error('Error fetching framework definitions:', error);
        res.status(500).json({ success: false, message: 'Server error fetching framework definitions.' });
    }
};

// Add controllers for Update and Delete later if needed
// exports.updateFrameworkDefinition = async (req, res) => { ... };
// exports.deleteFrameworkDefinition = async (req, res) => { ... };