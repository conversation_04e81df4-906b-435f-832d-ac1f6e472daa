const asyncHandler = require('express-async-handler');
const { generateJsonContent } = require('../utils/geminiService'); // Import Gemini service
const axios = require('axios'); // Import axios for HTTP requests

// @desc    Get AI suggestions for dreaded events
// @route   POST /api/ai/suggest-dreaded-events
// @access  Private
const suggestDreadedEvents = asyncHandler(async (req, res) => {
  console.log('Received context for AI suggestion:', req.body);
  const {
    analysisId,
    analysisName,
    analysisDescription,
    organizationContext,
    businessValue,
    securityPillar,
    existingSuggestionNames
  } = req.body;

  // --- Input Validation ---
  if (!analysisId || !businessValue || !securityPillar) {
    res.status(400);
    throw new Error('Missing required context: analysisId, businessValue, and securityPillar');
  }

  // --- Prepare Prompt for Gemini ---
  // Create a string listing existing suggestions, if any
  const existingListString = (existingSuggestionNames && existingSuggestionNames.length > 0)
    ? `\n\nIMPORTANT: Évitez de générer des suggestions identiques ou sémantiquement très similaires aux événements déjà suggérés suivants:\n- ${existingSuggestionNames.join('\n- ')}`
    : ''; // Empty string if no existing suggestions

  const prompt = `
    Rôle: Assistant expert en analyse de risques cyber sécurité.
    Objectif: Générer des suggestions d'événements redoutés potentiels.

    Contexte Fourni:
    - Cadre d'Analyse: Analyse de Risques Cyber pour ${organizationContext || 'l\'organisation spécifiée'}.
    - Nom de l'Analyse: ${analysisName || 'Non spécifié'}
    - Description de l'Analyse: ${analysisDescription || 'Non spécifiée'}
    - Valeur Métier Cible: ${businessValue?.name || 'Non spécifiée'} (Description: ${businessValue?.description || 'Non spécifiée'})
    - Pilier de Sécurité Cible: ${securityPillar}
    ${existingListString} // This part already handles avoiding duplicates

    Tâche:
    Strictement basé sur le 'Contexte Fourni' ci-dessus, et sans utiliser de connaissances externes, veuillez générer entre 5 et 10 événements redoutés potentiels (incidents de sécurité négatifs spécifiques). Ces événements doivent directement impacter la 'Valeur Métier Cible' en relation avec le 'Pilier de Sécurité Cible'.

    Pour chaque événement suggéré, fournir les informations suivantes:
    1. 'name': Un nom concis et descriptif (en français). Exemple: "Accès non autorisé à la base de données clients".
    2. 'description': Une brève description (en français) expliquant l'événement dans le contexte de la valeur métier et du pilier.
    3. 'severity': Un niveau de gravité plausible choisi exclusivement dans la liste suivante: minor, moderate, major, critical, catastrophic.
    4. 'impacts': Un tableau contenant un ou plusieurs types d'impacts choisis exclusivement parmi les valeurs suivantes:
       - 'missions_services' (Impacts sur les missions et services de l'organisation)
       - 'humain_materiel_environnemental' (Impacts humains, matériels ou environnementaux)
       - 'gouvernance' (Impacts sur la gouvernance)
       - 'financier' (Impacts financiers)
       - 'juridique' (Impacts juridiques)
       - 'image_confiance' (Impacts sur l'image et la confiance)

    Format de Réponse Exigé:
    Répondez UNIQUEMENT avec un tableau JSON valide contenant des objets. Chaque objet doit comporter les clés 'name', 'description', 'severity', et 'impacts'. Le contenu des champs 'name' et 'description' doit être exclusivement en français. N'incluez aucun texte, formatage markdown, ou explication en dehors de la structure JSON.

    Exemple de format JSON attendu:
    [
      { "name": "Nom Événement 1", "description": "Description en français 1", "severity": "moderate", "impacts": ["financier", "image_confiance"] },
      { "name": "Nom Événement 2", "description": "Description en français 2", "severity": "critical", "impacts": ["missions_services", "juridique"] }
    ]
  `;
  console.log("--- Generated Prompt ---");
  console.log(prompt);
  console.log("----------------------");

  try {
    // --- Call Gemini API Service ---
    const jsonResponseString = await generateJsonContent(prompt);

    // --- Parse the JSON response ---
    let suggestions = [];
    try {
        suggestions = JSON.parse(jsonResponseString);
        // Optional: Add further validation on the structure of suggestions array/objects
        if (!Array.isArray(suggestions)) {
            console.error("Parsed Gemini response is not an array:", suggestions);
            throw new Error("Parsed response is not an array.");
        }
        // Example: Check if objects have the required keys
        if (suggestions.length > 0 && (!suggestions[0].name || !suggestions[0].description || !suggestions[0].severity || !suggestions[0].impacts)) {
             console.error("Parsed Gemini response objects missing required keys:", suggestions[0]);
             throw new Error("Parsed response objects missing required keys (name, description, severity, impacts).");
        }

        // Validate that impacts is an array
        if (suggestions.length > 0 && !Array.isArray(suggestions[0].impacts)) {
            console.error("Impacts field is not an array:", suggestions[0].impacts);
            throw new Error("Impacts field must be an array of impact types.");
        }

    } catch (parseError) {
        console.error("Failed to parse JSON response from Gemini:", parseError);
        console.error("Received non-JSON string:", jsonResponseString); // Log the problematic string
        // Send back a specific error about the format
        res.status(500);
        throw new Error(`AI service returned an invalid data format.`);
    }

    // --- Send successful response ---
    res.status(200).json({
      success: true,
      data: { suggestions: suggestions },
      message: "AI suggestions retrieved successfully."
    });

  } catch (error) {
     // Handle errors from Gemini service or the JSON parsing validation
     console.error("Error in suggestDreadedEvents controller:", error);
     // Determine status code based on error type if possible
     const statusCode = res.statusCode === 200 ? 500 : res.statusCode; // Use 500 unless already set
     res.status(statusCode);
     // Send a user-friendly error message, potentially masking internal details
     // If it's a format error from parsing, use that specific message
     throw new Error(error.message.includes("invalid data format")
                     ? error.message
                     : `Failed to get suggestions from AI service.`);
  }
});

// @desc    Generate operational scenarios from attack paths
// @route   POST /api/ai/generate-operational-scenarios
// @access  Private
const generateOperationalScenarios = asyncHandler(async (req, res) => {
  console.log('Received attack paths for operational scenario generation:', req.body);
  const {
    analysisId,
    attackPaths,
    existingScenarios = []
  } = req.body;

  // --- Input Validation ---
  if (!analysisId || !attackPaths || !Array.isArray(attackPaths) || attackPaths.length === 0) {
    res.status(400);
    throw new Error('Missing required context: analysisId and attackPaths array are required.');
  }

  console.log('[AI Controller] Generating operational scenarios for', attackPaths.length, 'attack paths');
  console.log('[AI Controller] Attack paths data summary:', attackPaths.map(path => ({
    id: path.id,
    pathName: path.pathName,
    vulnerabilities: path.vulnerabilities?.length || 0,
    attackTechniques: path.attackTechniques?.length || 0,
    vulnerabilityDetails: path.vulnerabilities?.map(v => ({ id: v.cveId || v.id, severity: v.severity })) || []
  })));

  // --- Fetch Business Values, Dreaded Events, and Stakeholders Context ---
  let businessValuesContext = '';
  let dreadedEventsContext = '';
  let stakeholdersContext = '';

  try {
    const AnalysisComponent = require('../models/AnalysisComponent');

    // Fetch business values
    const businessValuesData = await AnalysisComponent.findOne({
      analysisId,
      componentType: 'business-values'
    });

    if (businessValuesData && businessValuesData.data && businessValuesData.data.businessValues) {
      const businessValues = businessValuesData.data.businessValues;
      businessValuesContext = `\n\nCONTEXTE DES VALEURS MÉTIER DE L'ORGANISATION:
${businessValues.map(bv =>
  `- ${bv.name} (${bv.shortId}):
    * Biens supports: ${bv.supportAssets ? bv.supportAssets.map(asset => `${asset.name} (${asset.type || 'Type non spécifié'})`).join(', ') : 'Aucun'}
    * Piliers de sécurité: ${bv.securityPillars ? bv.securityPillars.join(', ') : 'Non spécifiés'}
    * Description: ${bv.description || 'Non spécifiée'}`
).join('\n')}`;
    }

    // Fetch dreaded events
    const dreadedEventsData = await AnalysisComponent.findOne({
      analysisId,
      componentType: 'dreaded-events'
    });

    if (dreadedEventsData && dreadedEventsData.data && dreadedEventsData.data.dreadedEvents) {
      const dreadedEvents = dreadedEventsData.data.dreadedEvents;
      dreadedEventsContext = `\n\nCONTEXTE DES ÉVÉNEMENTS REDOUTÉS:
${dreadedEvents.map(event =>
  `- ${event.name} (ID: ${event.id}):
    * Gravité: ${event.severity || 'Non spécifiée'}
    * Impact: ${event.impact || 'Non spécifié'}
    * Pilier de sécurité: ${event.securityPillar || 'Non spécifié'}
    * Description: ${event.description || 'Non spécifiée'}`
).join('\n')}`;
    }

    // Fetch stakeholders (parties prenantes)
    const stakeholdersData = await AnalysisComponent.findOne({
      analysisId,
      componentType: 'stakeholders'
    });

    if (stakeholdersData && stakeholdersData.data && stakeholdersData.data.stakeholders) {
      const stakeholders = stakeholdersData.data.stakeholders;
      stakeholdersContext = `\n\nCONTEXTE DES PARTIES PRENANTES (VECTEURS D'ATTAQUE):
${stakeholders.map(stakeholder =>
  `- ${stakeholder.name}:
    * Catégorie: ${stakeholder.category || 'Non spécifiée'}
    * Type: ${stakeholder.type || 'Non spécifié'}
    * Niveau de dépendance: ${stakeholder.dependency || 'Non spécifié'}
    * Niveau de pénétration: ${stakeholder.penetration || 'Non spécifié'}
    * Maturité cyber: ${stakeholder.cyberMaturity || 'Non spécifiée'}
    * Niveau de confiance: ${stakeholder.trust || 'Non spécifié'}
    * Niveau de menace: ${stakeholder.threatLevel || 'Non spécifié'}
    * Description: ${stakeholder.description || 'Non spécifiée'}
    * Notes: ${stakeholder.notes || 'Aucune'}`
).join('\n')}`;
    }
  } catch (error) {
    console.warn('Could not fetch business context:', error.message);
    // Continue without business context
  }

  // --- Prepare existing scenarios list ---
  const existingListString = (existingScenarios && existingScenarios.length > 0)
    ? `\n\nIMPORTANT: Évitez de générer des scénarios identiques ou sémantiquement très similaires aux scénarios opérationnels déjà existants suivants:\n- ${existingScenarios.map(s => s.name).join('\n- ')}`
    : '';

  // --- Prepare attack paths context with business values and detailed information ---
  const attackPathsContext = attackPaths.map((path, index) =>
    `Chemin d'attaque ${index + 1} (ID: ${path.id}):
    - Code de référence: ${path.referenceCode || `CA${String(index + 1).padStart(2, '0')}`}
    - Source de risque: ${path.sourceRiskName || path.name || 'Non spécifié'}
    - Objectif visé: ${path.objectifVise || path.targetObjective || 'Non spécifié'}
    - Événement redouté: ${path.dreadedEventName || 'Non spécifié'}
    - Valeur métier impactée: ${path.businessValueName || 'Non spécifiée'}
    - ID de la valeur métier: ${path.businessValueId || 'Non spécifié'}
    - ID de l'événement redouté: ${path.dreadedEventId || 'Non spécifié'}
    - Parties prenantes impliquées: ${path.stakeholders ? path.stakeholders.map(s => s.name).join(', ') : 'Non spécifiées'}
    - Description: ${path.description || 'Non spécifiée'}
    - Étapes: ${path.steps ? path.steps.map(step => `${step.name} (${step.description})`).join(' → ') : 'Non spécifiées'}
    - Niveau de difficulté: ${path.difficulty || 'Non spécifié'}
    - Impact potentiel: ${path.impact || 'Non spécifié'}
    - Vulnérabilités CTI: ${path.vulnerabilities ? path.vulnerabilities.map(v => `${v.cveId} (${v.severity})`).join(', ') : 'Aucune'}
    - Techniques d'attaque CTI: ${path.attackTechniques ? path.attackTechniques.map(t => `${t.id} (${t.name})`).join(', ') : 'Aucune'}`
  ).join('\n\n');

  // --- Prepare CTI techniques context for constraint ---
  const allCTITechniques = [];
  attackPaths.forEach(path => {
    if (path.attackTechniques && path.attackTechniques.length > 0) {
      path.attackTechniques.forEach(tech => {
        if (!allCTITechniques.find(t => t.id === tech.id)) {
          allCTITechniques.push(tech);
        }
      });
    }
  });

  // --- Prepare vulnerabilities context ---
  const allCTIVulnerabilities = [];
  attackPaths.forEach(path => {
    if (path.vulnerabilities && path.vulnerabilities.length > 0) {
      path.vulnerabilities.forEach(vuln => {
        if (!allCTIVulnerabilities.find(v => v.cveId === vuln.cveId || v.id === vuln.id)) {
          allCTIVulnerabilities.push(vuln);
        }
      });
    }
  });

  const ctiTechniquesContext = allCTITechniques.length > 0
    ? `\n\nTECHNIQUES MITRE ATT&CK DISPONIBLES DANS L'ANALYSE CTI (UTILISEZ UNIQUEMENT CELLES-CI):
${allCTITechniques.map(tech =>
  `- ${tech.id} (${tech.name}): ${tech.description || 'Technique d\'attaque identifiée dans l\'analyse CTI'}`
).join('\n')}

IMPORTANT: Vous devez UNIQUEMENT utiliser les techniques listées ci-dessus dans vos scénarios. Ne pas inventer ou ajouter d'autres techniques MITRE ATT&CK.`
    : allCTIVulnerabilities.length > 0
    ? `\n\nAUCUNE TECHNIQUE MITRE ATT&CK DISPONIBLE mais ${allCTIVulnerabilities.length} vulnérabilité(s) CTI identifiée(s):
${allCTIVulnerabilities.map(vuln =>
  `- ${vuln.cveId || vuln.id}: ${vuln.description?.substring(0, 200) || 'Vulnérabilité identifiée dans l\'analyse CTI'} (Sévérité: ${vuln.severity?.severity || vuln.severity || 'Non spécifiée'})`
).join('\n')}

IMPORTANT: Basez vos scénarios sur l'exploitation de ces vulnérabilités spécifiques. Utilisez des techniques d'attaque génériques appropriées pour exploiter ces CVE.`
    : '\n\nAUCUNE DONNÉE CTI SPÉCIFIQUE: Aucune technique d\'attaque ou vulnérabilité n\'a été identifiée dans l\'analyse CTI. Utilisez des descriptions génériques d\'outils et techniques sans références spécifiques.';

  const prompt = `
    Rôle: Expert en cybersécurité spécialisé dans la méthode EBIOS RM et l'analyse de scénarios opérationnels d'attaque de haute qualité.
    Objectif: Générer des scénarios opérationnels détaillés, spécifiques et actionables basés sur les chemins d'attaque fournis, en respectant strictement le modèle de séquence d'attaque EBIOS RM.

    EXIGENCES DE QUALITÉ POUR LES SCÉNARIOS:

    1. LANGAGE SPÉCIFIQUE ET CONCRET:
       - Utilisez des outils et techniques précis (ex: "Nmap", "Metasploit", "PowerShell")
       - Mentionnez les CVE spécifiques quand disponibles
       - Décrivez les actions concrètes plutôt que vagues

    2. INTÉGRATION CTI OBLIGATOIRE:
       - Utilisez UNIQUEMENT les vulnérabilités CVE identifiées dans l'analyse CTI fournie
       - Utilisez UNIQUEMENT les techniques MITRE ATT&CK trouvées dans l'analyse CTI fournie
       - Ne pas inventer de nouvelles techniques - utilisez seulement celles fournies dans le contexte
       - Adaptez aux actifs et technologies de l'organisation

    3. LOGIQUE NARRATIVE:
       - Chaque étape doit permettre logiquement la suivante
       - Expliquez comment l'attaquant progresse dans l'attaque
       - Incluez des considérations de discrétion

    4. IMPACT MESURABLE:
       - Liez aux événements redoutés identifiés
       - Quantifiez l'impact quand possible

    MODÈLE DE SÉQUENCE D'ATTAQUE OBLIGATOIRE (EBIOS RM):
    Les scénarios DOIVENT suivre les 4 phases dans l'ordre:

    1. CONNAITRE (Reconnaissance et découverte externes):
       - Reconnaissance OSINT spécifique aux technologies identifiées
       - Scan ciblé des services exposés avec outils précis
       - Identification des vulnérabilités CVE spécifiques

    2. RENTRER (Intrusion dans le système):
       - Exploitation de vulnérabilités CVE spécifiques avec outils/exploits nommés
       - Techniques d'intrusion adaptées aux parties prenantes identifiées
       - Établissement de persistance avec méthodes concrètes

    3. TROUVER (Reconnaissance interne et latéralisation):
       - Énumération réseau avec commandes et outils spécifiques
       - Mouvement latéral exploitant les relations de confiance identifiées
       - Escalade de privilèges avec techniques MITRE ATT&CK précises

    4. EXPLOITER (Exploitation des données et biens supports):
       - Actions sur objectifs liées aux événements redoutés spécifiques
       - Techniques d'exfiltration/destruction avec outils et méthodes détaillés
       - Mesures d'effacement de traces avec commandes précises

    Contexte Fourni:
    - ID d'Analyse: ${analysisId}
    - Chemins d'attaque à analyser:
    ${attackPathsContext}
    ${ctiTechniquesContext}
    ${businessValuesContext}
    ${dreadedEventsContext}
    ${stakeholdersContext}
    ${existingListString}

    Tâche:
    Pour chaque chemin d'attaque fourni, générez 2-3 scénarios opérationnels détaillés qui décrivent comment un attaquant pourrait concrètement exploiter ce chemin. Chaque scénario doit être spécifique, réaliste et actionnable du point de vue de la défense.

    IMPORTANT:
    1. Chaque scénario DOIT contenir des étapes pour les 4 phases (CONNAITRE, RENTRER, TROUVER, EXPLOITER) dans cet ordre exact.
    2. Tenez compte de la VALEUR MÉTIER IMPACTÉE pour adapter la gravité, les techniques d'attaque et les motivations.
    3. Considérez l'ÉVÉNEMENT REDOUTÉ spécifique pour orienter les objectifs finaux de l'attaque.
    4. Les PARTIES PRENANTES sont ESSENTIELLES car elles représentent les VECTEURS D'ATTAQUE principaux:
       - Utilisez les parties prenantes comme points d'entrée dans la phase RENTRER
       - Exploitez leurs vulnérabilités spécifiques (techniques, humaines, organisationnelles)
       - Adaptez les techniques d'attaque selon le type de partie prenante (fournisseur, partenaire, sous-traitant, etc.)
       - Considérez leur niveau de maturité cyber et leurs relations de confiance
    5. Adaptez la complexité et les ressources nécessaires selon l'importance de la valeur métier et la vulnérabilité des parties prenantes.

    Pour chaque scénario opérationnel, fournir les informations suivantes:
    1. 'name': Un nom concis et descriptif du scénario (en français)
    2. 'description': Une description détaillée du scénario d'attaque (en français)
    3. 'attackPathId': L'ID exact du chemin d'attaque source (utilisez l'ID fourni dans le contexte, pas l'index)
    4. 'attackPathReference': Le code de référence du chemin d'attaque (ex: CA01, CA02, etc.)
    5. 'steps': Un tableau d'étapes opérationnelles détaillées suivant OBLIGATOIREMENT les 4 phases EBIOS RM, chaque étape contenant:
       - 'id': Identifiant unique de l'étape
       - 'name': Nom de l'étape
       - 'phase': Phase EBIOS RM (CONNAITRE, RENTRER, TROUVER, ou EXPLOITER)
       - 'description': Description détaillée de l'action
       - 'techniques': Techniques/outils utilisés (selon les catégories EBIOS RM)
       - 'indicators': Indicateurs de compromission potentiels
       - 'duration': Durée estimée de l'étape
    6. 'severity': Niveau de gravité (Très faible, Faible, Moyen, Élevé, Très élevé)
    7. 'likelihood': Probabilité de réussite (Très faible, Faible, Moyen, Élevé, Très élevé)
    8. 'detectionDifficulty': Difficulté de détection (Très faible, Faible, Moyen, Élevé, Très élevé)
    9. 'requiredSkills': Compétences requises pour l'attaquant (Débutant, Intermédiaire, Avancé, Expert)
    10. 'resources': Ressources nécessaires pour l'attaque
    11. 'timeline': Durée totale estimée du scénario

    Format de Réponse Exigé:
    Répondez UNIQUEMENT avec un tableau JSON valide. N'incluez aucun texte, formatage markdown, ou explication en dehors de la structure JSON.

    Exemple de format JSON attendu (HAUTE QUALITÉ AVEC DÉTAILS TECHNIQUES SPÉCIFIQUES):
    [
      {
        "name": "Compromission base de données clients via CVE Apache",
        "description": "Exploitation de CVE-2021-41773 sur serveur Apache pour accéder à la base de données clients, causant l'événement redouté 'Vol de données clients sensibles' et compromettant la valeur métier 'Données clients et transactions'",
        "attackPathId": "virtual-path-0",
        "attackPathReference": "CA01",
        "steps": [
          {
            "id": "step_1",
            "name": "Reconnaissance OSINT ciblée serveur Apache",
            "phase": "CONNAITRE",
            "description": "L'attaquant utilise Shodan avec la requête 'Apache/2.4.49 country:FR' pour identifier les serveurs Apache vulnérables, puis lance Nmap avec les options '-sV -p 80,443 --script http-apache-server-info' sur l'adresse IP cible pour confirmer la version Apache 2.4.49 vulnérable à CVE-2021-41773",
            "techniques": ["T1595.002 (Vulnerability Scanning)", "Shodan OSINT", "Nmap version detection"],
            "indicators": ["Requêtes Shodan inhabituelles", "Scans Nmap sur ports 80/443", "Tentatives d'accès aux endpoints /server-info"],
            "duration": "4-6 heures"
          },
          {
            "id": "step_2",
            "name": "Exploitation CVE-2021-41773 pour RCE",
            "phase": "RENTRER",
            "description": "L'attaquant télécharge l'exploit public CVE-2021-41773 depuis GitHub, puis exécute 'curl -s --path-as-is -d 'echo Content-Type: text/plain; echo; id' 'http://target.com/cgi-bin/.%2e/%2e%2e/%2e%2e/%2e%2e/bin/sh'' pour obtenir l'exécution de code à distance sur le serveur web Apache",
            "techniques": ["T1190 (Exploit Public-Facing Application)", "CVE-2021-41773", "Path traversal attack"],
            "indicators": ["Requêtes HTTP avec patterns '../' suspects", "Exécution de commandes système via Apache", "Processus fils inattendus du processus Apache"],
            "duration": "30 minutes"
          },
          {
            "id": "step_3",
            "name": "Mouvement latéral dans l'écosystème d'entreprise",
            "phase": "TROUVER",
            "description": "Progression dans l'infrastructure critique en exploitant les interconnexions [INTERCONNEXIONS_SYSTÈMES] et les relations de confiance établies, utilisant [TECHNIQUE_MITRE_FOURNIE_CTI] pour accéder aux [DONNÉES_HÉBERGÉES] sensibles stockées sur [BIEN_SUPPORT_CRITIQUE], contournant les contrôles de conformité [CONFORMITÉ_RÉGLEMENTAIRE]",
            "techniques": ["[TECHNIQUE_MITRE_FOURNIE_DANS_CTI]", "Exploitation des interconnexions d'entreprise", "Contournement des contrôles de conformité"],
            "indicators": ["Accès anormal aux systèmes interconnectés", "Violation des politiques de conformité [RÉGLEMENTATIONS]", "Activité suspecte sur [BIEN_SUPPORT_CRITIQUE]"],
            "duration": "Durée selon architecture [COMPLEXITÉ_INFRASTRUCTURE]"
          },
          {
            "id": "step_4",
            "name": "Réalisation de l'événement redouté métier",
            "phase": "EXPLOITER",
            "description": "Concrétisation de l'événement redouté '[ÉVÉNEMENT_REDOUTÉ_SPÉCIFIQUE]' par compromission de [VALEUR_MÉTIER_CRITIQUE], causant [IMPACT_OPÉRATIONNEL] avec interruption des [PROCESSUS_MÉTIER_DÉPENDANTS], générant [IMPACT_FINANCIER] de pertes, violation de [RÉGLEMENTATIONS_APPLICABLES] avec risque de sanctions, et dégradation [IMPACT_RÉPUTATION] nécessitant activation du [PLAN_CONTINUITÉ] dans les délais [RTO_ACCEPTABLE]",
            "techniques": ["[TECHNIQUE_MITRE_FOURNIE_DANS_CTI]", "Compromission des piliers [PILIERS_SÉCURITÉ_COMPROMIS]", "Impact sur continuité d'activité"],
            "indicators": ["Compromission effective de [VALEUR_MÉTIER]", "Violation des seuils [RTO_ACCEPTABLE]", "Déclenchement des alertes de conformité [RÉGLEMENTATIONS]", "Impact mesurable sur [PROCESSUS_MÉTIER]"],
            "duration": "Impact immédiat - Récupération selon [PLAN_CONTINUITÉ]"
          }
        ],
        "severity": "[BASÉ_SUR_IMPACT_MÉTIER_ET_SCORES_CVE]",
        "likelihood": "[BASÉ_SUR_MATURITÉ_CYBER_ET_CONTRÔLES_EN_PLACE]",
        "detectionDifficulty": "[BASÉ_SUR_INFORMATIONS_DÉTECTION_CTI]",
        "requiredSkills": "[BASÉ_SUR_COMPLEXITÉ_TECHNIQUES_CTI]",
        "resources": "[BASÉ_SUR_RESSOURCES_SOURCE_RISQUE]",
        "timeline": "[BASÉ_SUR_COMPLEXITÉ_ÉCOSYSTÈME_PROFESSIONNEL]"
      }
    ]
  `;

  console.log("--- Generated Operational Scenarios Prompt ---");
  console.log(prompt);
  console.log("--------------------------------------------");

  try {
    // --- Call Gemini API Service ---
    const jsonResponseString = await generateJsonContent(prompt);

    // --- Parse the JSON response ---
    let scenarios = [];
    try {
      scenarios = JSON.parse(jsonResponseString);

      if (!Array.isArray(scenarios)) {
        console.error("Parsed Gemini response is not an array:", scenarios);
        throw new Error("Parsed response is not an array.");
      }

      // Validate required fields
      if (scenarios.length > 0) {
        const requiredFields = ['name', 'description', 'attackPathId', 'steps', 'severity', 'likelihood'];
        const firstScenario = scenarios[0];

        for (const field of requiredFields) {
          if (!firstScenario.hasOwnProperty(field)) {
            console.error(`Missing required field: ${field}`, firstScenario);
            throw new Error(`Parsed response objects missing required field: ${field}`);
          }
        }

        // Validate attackPathId is not an index but an actual ID
        if (typeof firstScenario.attackPathId === 'number') {
          console.error('attackPathId should be a string ID, not a number index:', firstScenario.attackPathId);
          throw new Error('attackPathId should be the actual attack path ID, not an index');
        }

        // Validate steps array
        if (!Array.isArray(firstScenario.steps)) {
          console.error("Steps field is not an array:", firstScenario.steps);
          throw new Error("Steps field must be an array of step objects.");
        }
      }

    } catch (parseError) {
      console.error("Failed to parse JSON response from Gemini:", parseError);
      console.error("Received non-JSON string:", jsonResponseString);
      res.status(500);
      throw new Error(`AI service returned an invalid data format.`);
    }

    // --- Send successful response ---
    res.status(200).json({
      success: true,
      data: { scenarios: scenarios },
      message: "Operational scenarios generated successfully."
    });

  } catch (error) {
    console.error("Error in generateOperationalScenarios controller:", error);
    const statusCode = res.statusCode === 200 ? 500 : res.statusCode;
    res.status(statusCode);
    throw new Error(error.message.includes("invalid data format")
                    ? error.message
                    : `Failed to generate operational scenarios from AI service.`);
  }
});

// @desc    Get AI suggestions for security controls
// @route   POST /api/ai/suggest-security-controls
// @access  Private
const suggestSecurityControls = asyncHandler(async (req, res) => {
  console.log('Received context for AI control suggestion:', req.body);
  const {
    ruleName,
    ruleDescription,
    dreadedEventName,
    dreadedEventSeverity,
    existingControlNames, // Optional: Array of names of controls already considered/added
    treatmentOption,
    businessValueName,
    businessValueDescription,
    supportAssetNames, // Expected to be an array of strings
    language // e.g., 'fr'
  } = req.body;

  // --- Input Validation ---
  if (!ruleName || !dreadedEventName) {
    res.status(400);
    throw new Error('Missing required context: ruleName and dreadedEventName are required.');
  }

  // --- Prepare Enhanced Prompt for Gemini ---

  // Language instruction
  const languageInstruction = (language === 'fr')
    ? "Répondez uniquement en français."
    : "Respond only in English.";

  // Define Treatment Option explanations for prompt guidance
  const treatmentGuidance = {
      reduce: 'Suggest controls that decrease likelihood or impact.',
      transfer: 'Suggest actions/requirements for shifting risk responsibility (e.g., specific contractual clauses, insurance policy terms, mandatory third-party SLAs). The suggestions should NAME these transfer mechanisms.',
      avoid: 'Suggest controls that help cease the risky activity.',
      accept: 'Suggest monitoring or minimal documentation controls. Few or no *new* controls might be needed.'
  };
  const chosenGuidance = treatmentOption ? treatmentGuidance[treatmentOption] : 'Consider the unspecified treatment option.';

  // Build context strings, handling null/undefined values
  // Get the user-facing label for the chosen treatment option
  const TREATMENT_OPTIONS_FR = [ { value: 'reduce', label: 'Réduire le risque' }, { value: 'transfer', label: 'Transférer le risque' }, { value: 'avoid', label: 'Éviter le risque' }, { value: 'accept', label: 'Accepter le risque' }, ];
  const chosenTreatmentLabel = (language === 'fr')
      ? TREATMENT_OPTIONS_FR.find(o => o.value === treatmentOption)?.label
      : treatmentOption; // Use the value directly if not French
  const treatmentString = chosenTreatmentLabel ? `\nChosen Treatment Strategy: ${chosenTreatmentLabel}` : '';
  const businessValueString = businessValueName ? `\nImpacted Business Value: ${businessValueName} (${businessValueDescription || 'No description'})` : '';
  const supportAssetsString = (supportAssetNames && supportAssetNames.length > 0) ? `\nRelated Support Assets: ${supportAssetNames.join(', ')}` : '';
  const existingListString = (existingControlNames && existingControlNames.length > 0)
    ? `\n\nAvoid suggesting controls similar to these already considered:\n- ${existingControlNames.join('\n- ')}`
    : '';

  const prompt = `
    Context: Cybersecurity Risk Treatment Plan.
    Security Rule: ${ruleName} (${ruleDescription || 'No description provided.'})
    Dreaded Event to mitigate: ${dreadedEventName} (Severity: ${dreadedEventSeverity || 'N/A'})
    ${businessValueString}
    ${supportAssetsString}
    ${treatmentString} ${existingListString}

    Task: Suggest specific, actionable security controls (technical or procedural) to implement the Security Rule and mitigate the Dreaded Event.
    Crucially, the suggestions MUST align with the "Chosen Treatment Strategy" (${chosenTreatmentLabel || 'Not specified'}). Guidance: ${chosenGuidance}
    Note: The *type* of suggestions should significantly change based on the Treatment Strategy. For 'Transfer', 'Avoid', 'Accept', suggestions should name requirements, actions, or agreements, not just technical mitigations.

    IMPORTANT: Each suggested control must be categorized by measure type:
    - "gouvernance et anticipation": Governance, policies, procedures, planning measures
    - "protection": Preventive technical and organizational measures
    - "défense": Detective and reactive security measures
    - "résilience": Recovery, continuity, and resilience measures

    Provide at least 3, but no more than 6, highly relevant control suggestions with their measure types.

    Output Format: Respond ONLY with a valid JSON array of objects, where each object contains "name" (control name) and "measure_type" (one of the four types above). ${languageInstruction}. Do not include any other text, markdown, or explanations.

    Example (${language === 'fr' ? 'French' : 'English'}):
    ${language === 'fr' ? `[
      {
        "name": "Mettre en œuvre l'authentification multifacteur pour les administrateurs",
        "measure_type": "protection"
      },
      {
        "name": "Définir une politique de gestion des accès privilégiés",
        "measure_type": "gouvernance et anticipation"
      },
      {
        "name": "Surveillance continue des connexions administrateur",
        "measure_type": "défense"
      },
      {
        "name": "Plan de récupération en cas de compromission des comptes admin",
        "measure_type": "résilience"
      }
    ]` : `[
      {
        "name": "Implement Multi-Factor Authentication for Admins",
        "measure_type": "protection"
      },
      {
        "name": "Define Privileged Access Management Policy",
        "measure_type": "gouvernance et anticipation"
      },
      {
        "name": "Continuous Monitoring of Admin Connections",
        "measure_type": "défense"
      },
      {
        "name": "Recovery Plan for Compromised Admin Accounts",
        "measure_type": "résilience"
      }
    ]`}
  `;
  console.log("--- Generated Enhanced Prompt for Controls --- ");
  console.log(prompt);
  console.log("-----------------------------------------");

  try {
    // --- Call Gemini API Service ---
    const jsonResponseString = await generateJsonContent(prompt);

    // --- Parse the JSON response (expecting an array of objects with name and measure_type) ---
    let suggestions = [];
    try {
      suggestions = JSON.parse(jsonResponseString);
      if (!Array.isArray(suggestions)) {
        console.error("Parsed Gemini response is not an array:", suggestions);
        throw new Error("Parsed response is not an array.");
      }

      // Validate that each suggestion has the required fields
      if (suggestions.length > 0) {
        const requiredFields = ['name', 'measure_type'];
        const firstSuggestion = suggestions[0];
        const missingFields = requiredFields.filter(field => !firstSuggestion.hasOwnProperty(field));

        if (missingFields.length > 0) {
          console.error("Parsed Gemini response objects missing required fields:", missingFields, firstSuggestion);
          throw new Error(`Parsed response objects missing required fields: ${missingFields.join(', ')}`);
        }

        // Validate measure_type values
        const validMeasureTypes = ['gouvernance et anticipation', 'protection', 'défense', 'résilience'];
        const invalidSuggestions = suggestions.filter(s => !validMeasureTypes.includes(s.measure_type));
        if (invalidSuggestions.length > 0) {
          console.warn("Some suggestions have invalid measure_type values:", invalidSuggestions);
          // Fix invalid measure types by defaulting to 'protection'
          suggestions = suggestions.map(s => ({
            ...s,
            measure_type: validMeasureTypes.includes(s.measure_type) ? s.measure_type : 'protection'
          }));
        }
      }
    } catch (parseError) {
      console.error("Failed to parse JSON response from Gemini:", parseError);
      console.error("Received non-JSON string:", jsonResponseString);
      res.status(500);
      throw new Error(`AI service returned an invalid data format (expected JSON array of objects with name and measure_type).`);
    }

    // --- Send successful response ---
    res.status(200).json({
      success: true,
      data: { suggestions: suggestions }, // Send back array of strings
      message: "AI control suggestions retrieved successfully."
    });

  } catch (error) {
    console.error("Error in suggestSecurityControls controller:", error);
    const statusCode = res.statusCode === 200 ? 500 : res.statusCode;
    res.status(statusCode);
    throw new Error(error.message.includes("invalid data format")
                    ? error.message
                    : `Failed to get control suggestions from AI service.`);
  }
});

// @desc    Get AI suggestions for sources de risque
// @route   POST /api/ai/suggest-sources-risque
// @access  Private
const suggestSourcesRisque = asyncHandler(async (req, res) => {
  console.log('Received context for AI source de risque suggestion:', req.body);
  const {
    analysisId,
    analysisName,
    analysisDescription,
    organizationContext,
    threatCategories,
    existingSourceNames,
    requestMore,
    timestamp // Used to ensure we get different results when requesting more
  } = req.body;

  // --- Input Validation ---
  if (!analysisId) {
    res.status(400);
    throw new Error('Missing required context: analysisId is required');
  }

  // --- Prepare Prompt for Gemini ---
  // Create a string listing existing suggestions, if any
  const existingListString = (existingSourceNames && existingSourceNames.length > 0)
    ? `\n\nIMPORTANT: Évitez de générer des sources de risque identiques ou sémantiquement très similaires aux sources déjà existantes suivantes:\n- ${existingSourceNames.join('\n- ')}`
    : ''; // Empty string if no existing suggestions

  // Create a string listing threat categories, if any
  const threatCategoriesString = (threatCategories && threatCategories.length > 0)
    ? `\n\nCatégories de menaces à considérer:\n- ${threatCategories.join('\n- ')}`
    : `\n\nCatégories de menaces à considérer:
    - 🏛️ ÉTATIQUE (etatique): États, agences de renseignement.
    - 🔪 CRIME ORGANISÉ (criminel): Organisations cybercriminelles (mafias, gangs, officines).
    - 💣 TERRORISTE (terroriste): Cyberterroristes, cybermilices.
    - ✊ ACTIVISTE IDÉOLOGIQUE (activiste): Cyber-hacktivistes, groupements d'intérêt, sectes.
    - 🕵️ OFFICINE SPÉCIALISÉE (officine): Cybermercenaires avec capacités techniques élevées.
    - 🎮 AMATEUR (amateur): Hackers "script-kiddies" ou avec bonnes connaissances informatiques.
    - 😡 VENGEUR (vengeur): Individus guidés par un esprit de vengeance ou sentiment d'injustice.
    - 🦹 MALVEILLANT PATHOLOGIQUE (malveillant): Individus aux motivations pathologiques ou opportunistes.`; // Enhanced categories with emojis and descriptions

  // Get business values and assets from the request body
  const businessValues = req.body.businessValues || [];
  const businessAssets = req.body.businessAssets || [];

  // Create strings for business values and assets if available
  const businessValuesString = businessValues.length > 0
    ? `\n\nValeurs métier de l'organisation:
    ${businessValues.map(bv => `- ${bv.name}: ${bv.description || 'Pas de description'}`).join('\n    ')}`
    : '';

  const businessAssetsString = businessAssets.length > 0
    ? `\n\nBiens supports de l'organisation:
    ${businessAssets.map(asset => `- ${asset.name}: ${asset.description || 'Pas de description'} (Type: ${asset.type || 'Non spécifié'})`).join('\n    ')}`
    : '';

  // Add a special instruction for requesting more suggestions
  const moreRequestString = requestMore
    ? `\n\nIMPORTANT: L'utilisateur a déjà reçu des suggestions et demande des sources de risque DIFFÉRENTES et COMPLÉMENTAIRES. Assurez-vous de générer des suggestions qui explorent de nouvelles catégories, motivations ou objectifs visés par rapport aux suggestions précédentes. Timestamp: ${timestamp || Date.now()}`
    : '';

  const prompt = `
    Rôle: Assistant expert en analyse de risques cyber sécurité.
    Objectif: Générer des suggestions de sources de risque potentielles.

    Contexte Fourni:
    - Cadre d'Analyse: Analyse de Risques Cyber pour ${organizationContext || 'l\'organisation spécifiée'}.
    - Nom de l'Analyse: ${analysisName || 'Non spécifié'}
    - Description de l'Analyse: ${analysisDescription || 'Non spécifiée'}
    ${businessValuesString}
    ${businessAssetsString}
    ${threatCategoriesString}
    ${existingListString}
    ${moreRequestString}

    Tâche:
    Strictement basé sur le 'Contexte Fourni' ci-dessus, et sans utiliser de connaissances externes, veuillez générer entre ${requestMore ? '3 et 5' : '5 et 10'} sources de risque potentielles (acteurs malveillants ou sources de menaces). Ces sources doivent être pertinentes pour le contexte de l'organisation, ses valeurs métier et ses biens supports.

    Pour chaque source de risque suggérée, fournir les informations suivantes:
    1. 'name': Un nom concis et descriptif (en français). Exemple: "Groupe de hackers étatiques".
    2. 'description': Une brève description (en français) expliquant la source de risque et pourquoi elle ciblerait cette organisation.
    3. 'category': La catégorie de la source de risque, choisie parmi: etatique, criminel, terroriste, activiste, officine, amateur, vengeur, malveillant.
    4. 'objectifVise': Un objectif visé par cette source de risque (en français). Exemple: "Espionnage industriel".
    5. 'objectifViseCategory': La catégorie de l'objectif visé, choisie parmi: espionnage, prepositionnement, influence, entrave, lucratif, defi.
    6. 'motivation': Le niveau de motivation, choisi parmi: faible, moyen, eleve.
    7. 'activite': Le niveau d'activité, choisi parmi: faible, moyen, eleve.
    8. 'ressources': Le niveau de ressources, choisi parmi: faibles, moyennes, importantes.

    Format de Réponse Exigé:
    Répondez UNIQUEMENT avec un tableau JSON valide contenant des objets. Chaque objet doit comporter les clés 'name', 'description', 'category', 'objectifVise', 'objectifViseCategory', 'motivation', 'activite', et 'ressources'. Le contenu des champs 'name', 'description' et 'objectifVise' doit être exclusivement en français. N'incluez aucun texte, formatage markdown, ou explication en dehors de la structure JSON.

    Exemple de format JSON attendu:
    [
      {
        "name": "Groupe de hackers étatiques",
        "description": "Groupe sponsorisé par un état étranger visant à obtenir des informations sensibles",
        "category": "etatique",
        "objectifVise": "Espionnage industriel",
        "objectifViseCategory": "espionnage",
        "motivation": "eleve",
        "activite": "moyen",
        "ressources": "importantes"
      },
      {
        "name": "Hacktiviste idéologique",
        "description": "Individu motivé par des convictions idéologiques",
        "category": "activiste",
        "objectifVise": "Perturbation des services en ligne",
        "objectifViseCategory": "entrave",
        "motivation": "eleve",
        "activite": "faible",
        "ressources": "faibles"
      }
    ]
  `;
  console.log("--- Generated Prompt ---");
  console.log(prompt);
  console.log("----------------------");

  try {
    // --- Call Gemini API Service ---
    const jsonResponseString = await generateJsonContent(prompt);

    // --- Parse the JSON response ---
    let suggestions = [];
    try {
      suggestions = JSON.parse(jsonResponseString);
      // Optional: Add further validation on the structure of suggestions array/objects
      if (!Array.isArray(suggestions)) {
        console.error("Parsed Gemini response is not an array:", suggestions);
        throw new Error("Parsed response is not an array.");
      }
      // Example: Check if objects have the required keys
      if (suggestions.length > 0) {
        const requiredKeys = ['name', 'description', 'category', 'objectifVise', 'objectifViseCategory', 'motivation', 'activite', 'ressources'];
        const missingKeys = requiredKeys.filter(key => !suggestions[0].hasOwnProperty(key));
        if (missingKeys.length > 0) {
          console.error(`Parsed Gemini response objects missing required keys: ${missingKeys.join(', ')}`, suggestions[0]);
          throw new Error(`Parsed response objects missing required keys: ${missingKeys.join(', ')}`);
        }
      }
    } catch (parseError) {
      console.error("Failed to parse JSON response from Gemini:", parseError);
      console.error("Received non-JSON string:", jsonResponseString); // Log the problematic string
      // Send back a specific error about the format
      res.status(500);
      throw new Error(`AI service returned an invalid data format.`);
    }

    // --- Send successful response ---
    res.status(200).json({
      success: true,
      data: { suggestions: suggestions },
      message: "AI suggestions retrieved successfully."
    });

  } catch (error) {
    // Handle errors from Gemini service or the JSON parsing validation
    console.error("Error in suggestSourcesRisque controller:", error);
    // Determine status code based on error type if possible
    const statusCode = res.statusCode === 200 ? 500 : res.statusCode; // Use 500 unless already set
    res.status(statusCode);
    // Send a user-friendly error message, potentially masking internal details
    // If it's a format error from parsing, use that specific message
    throw new Error(error.message.includes("invalid data format")
                   ? error.message
                   : `Failed to get suggestions from AI service.`);
  }
});

// @desc    Generate ecosystem security measures
// @route   POST /api/ai/generate-ecosystem-measures
// @access  Private
const generateEcosystemMeasures = asyncHandler(async (req, res) => {
  console.log('Received context for AI ecosystem measures generation:', req.body);
  const {
    analysisId,
    stakeholders = [],
    attackPaths = [],
    focus = 'balanced', // 'practical', 'balanced', or 'comprehensive'
    measuresPerPath = 5 // Number of measures to generate per attack path
  } = req.body;

  // --- Input Validation ---
  if (!analysisId) {
    res.status(400);
    throw new Error('Missing required context: analysisId is required');
  }

  // Validate measuresPerPath
  const measuresPerPathNum = parseInt(measuresPerPath) || 5;
  if (measuresPerPathNum < 1 || measuresPerPathNum > 10) {
    res.status(400);
    throw new Error('measuresPerPath must be between 1 and 10');
  }

  // Validate focus parameter
  if (!['practical', 'balanced', 'comprehensive'].includes(focus)) {
    console.warn(`Invalid focus value: ${focus}, defaulting to 'balanced'`);
  }

  // --- Prepare Prompt for Gemini ---
  // Create strings for stakeholders and attack paths if available
  const stakeholdersString = stakeholders.length > 0
    ? `\n\nParties prenantes critiques sélectionnées:
    ${stakeholders.map(id => `- ID: ${id}`).join('\n    ')}`
    : '';

  const attackPathsString = attackPaths.length > 0
    ? `\n\nChemins d'attaque sélectionnés:
    ${attackPaths.map(id => `- ID: ${id}`).join('\n    ')}`
    : '';

  // Récupérer les chemins d'attaque pour inclure les sources de risque et objectifs visés dans le prompt
  let attackPathsDetailsString = '';

  try {
    // Récupérer les chemins d'attaque
    const attackPathsResponse = await axios.get(`${process.env.API_URL || 'http://localhost:5000'}/api/analyses/${analysisId}/attack-paths`);
    let paths = [];

    if (attackPathsResponse.data) {
      // Extraire les chemins d'attaque selon la structure de la réponse
      if (attackPathsResponse.data.data && attackPathsResponse.data.data.attackPaths) {
        paths = attackPathsResponse.data.data.attackPaths;
      } else if (attackPathsResponse.data.attackPaths) {
        paths = attackPathsResponse.data.attackPaths;
      } else if (Array.isArray(attackPathsResponse.data)) {
        paths = attackPathsResponse.data;
      }

      if (paths.length > 0) {
        // Filtrer les chemins d'attaque sélectionnés si spécifiés
        const filteredPaths = attackPaths.length > 0
          ? paths.filter(path => attackPaths.includes(path.id))
          : paths;

        attackPathsDetailsString = `\n\nDétails des chemins d'attaque:
        ${filteredPaths.map(path =>
          `- ID: ${path.id}
           Source de risque: ${path.sourceRiskName || 'Non spécifiée'}
           Objectif visé: ${path.objectifVise || 'Non spécifié'}
           Événement redouté: ${path.dreadedEventName || 'Non spécifié'}`
        ).join('\n\n        ')}`;
      }
    }
  } catch (error) {
    console.error('Erreur lors de la récupération des chemins d\'attaque:', error);
    // Continuer sans ces informations si erreur
  }

  const focusString = `\n\nFocus de la génération: ${focus === 'practical' ? 'Pratique (mesures faciles à mettre en œuvre)' : focus === 'comprehensive' ? 'Exhaustif (ensemble complet de mesures)' : 'Équilibré'}
    \nNombre de mesures par chemin d'attaque: ${measuresPerPathNum}
    \nRéutilisation des mesures: Une mesure peut s'appliquer à plusieurs chemins d'attaque si elle est pertinente pour ces chemins.`;

  const prompt = `
    Rôle: Expert en cybersécurité spécialisé dans la protection des écosystèmes numériques.
    Objectif: Générer des mesures de sécurité pour protéger l'écosystème d'une organisation contre les menaces identifiées.

    Contexte:
    - Analyse de risques pour l'écosystème d'une organisation (ID: ${analysisId})
    ${stakeholdersString}
    ${attackPathsString}
    ${attackPathsDetailsString}
    ${focusString}

    Tâche:
    IMPORTANT: Générer EXACTEMENT ${measuresPerPathNum} mesures de sécurité DISTINCTES pour CHAQUE chemin d'attaque sélectionné.

    Pour chaque chemin d'attaque, créez ${measuresPerPathNum} mesures spécifiquement conçues pour contrer ce chemin d'attaque particulier.

    IMPORTANT: Pour chaque mesure générée, vous DEVEZ inclure un champ 'attackPathId' contenant l'ID exact du chemin d'attaque pour lequel cette mesure est conçue.

    Exemple: Si le chemin d'attaque a l'ID "source-1747146593886-53u9984ih-1745351907073-1748021655843", alors la mesure doit avoir:
    "attackPathId": "source-1747146593886-53u9984ih-1745351907073-1748021655843"

    Pour chaque mesure, fournir les informations suivantes:
    1. 'title': Un titre concis et descriptif (en français).
    2. 'description': Une description détaillée de la mesure (en français).
    3. 'category': La catégorie de la mesure, choisie parmi: governance, protection, defense, resilience, other.
    4. 'priority': La priorité de la mesure, choisie parmi: high, medium, low.
    5. 'implementation': Le statut d'implémentation initial, toujours 'planned'.
    6. 'effectiveness': L'efficacité estimée de la mesure (nombre entier de 1 à 5).
    7. 'attackPathId': L'ID exact du chemin d'attaque pour lequel cette mesure est conçue.
    8. 'benefits': Un tableau d'objets décrivant les bénéfices de la mesure, chaque objet contenant:
       a. 'description': Description du bénéfice (en français).
       b. 'impactArea': Domaine d'impact, choisi parmi: confidentiality, integrity, availability, traceability, resilience.
       c. 'riskReduction': Niveau de réduction du risque, choisi parmi: high, medium, low.

    Format de Réponse Exigé:
    Répondez UNIQUEMENT avec un tableau JSON valide contenant des objets. Chaque objet doit comporter les clés 'title', 'description', 'category', 'priority', 'implementation', 'effectiveness', 'attackPathId', et 'benefits'. N'incluez aucun texte, formatage markdown, ou explication en dehors de la structure JSON.

    Exemple de format JSON attendu:
    [
      {
        "title": "Mise en place d'un programme de sensibilisation à la sécurité",
        "description": "Développer et déployer un programme de sensibilisation à la sécurité pour contrer les tentatives de dénigrement par les compétiteurs.",
        "category": "governance",
        "priority": "high",
        "implementation": "planned",
        "effectiveness": 4,
        "attackPathId": "source-1747146593886-53u9984ih-1745351907073-1748021655843",
        "benefits": [
          {
            "description": "Réduction des risques d'ingénierie sociale",
            "impactArea": "confidentiality",
            "riskReduction": "high"
          },
          {
            "description": "Amélioration de la détection des incidents",
            "impactArea": "traceability",
            "riskReduction": "medium"
          }
        ]
      },
      {
        "title": "Mise en œuvre d'un système de détection d'intrusion",
        "description": "Déployer un système de détection d'intrusion avancé pour protéger contre les groupes cybercriminels organisés.",
        "category": "defense",
        "priority": "high",
        "implementation": "planned",
        "effectiveness": 5,
        "attackPathId": "source-1747146593886-abc123def-1745351907073-1748021655844",
        "benefits": [
          {
            "description": "Détection précoce des tentatives d'intrusion",
            "impactArea": "integrity",
            "riskReduction": "high"
          },
          {
            "description": "Réduction du temps de réponse aux incidents",
            "impactArea": "availability",
            "riskReduction": "high"
          }
        ]
      }
    ]
  `;
  console.log("--- Generated Prompt ---");
  console.log(prompt);
  console.log("----------------------");

  try {
    // --- Call Gemini API Service ---
    const jsonResponseString = await generateJsonContent(prompt);

    // --- Parse the JSON response ---
    let measures = [];
    try {
      measures = JSON.parse(jsonResponseString);
      // Validate the structure of measures array/objects
      if (!Array.isArray(measures)) {
        console.error("Parsed Gemini response is not an array:", measures);
        throw new Error("Parsed response is not an array.");
      }
      // Check if objects have the required keys
      if (measures.length > 0) {
        const requiredKeys = ['title', 'description', 'category', 'priority', 'implementation', 'effectiveness', 'benefits'];

        // Check for required keys
        const missingKeys = requiredKeys.filter(key => !measures[0].hasOwnProperty(key));
        if (missingKeys.length > 0) {
          console.error(`Parsed Gemini response objects missing required keys: ${missingKeys.join(', ')}`, measures[0]);
          throw new Error(`Parsed response objects missing required keys: ${missingKeys.join(', ')}`);
        }

        // Simple processing: just add attackPaths array based on attackPathId
        measures = measures.map(measure => {
          const updatedMeasure = { ...measure };

          // If the measure has an attackPathId, add it to attackPaths array
          if (updatedMeasure.attackPathId) {
            updatedMeasure.attackPaths = [updatedMeasure.attackPathId];
          } else {
            updatedMeasure.attackPaths = [];
          }

          return updatedMeasure;
        });
      }
    } catch (parseError) {
      console.error("Failed to parse JSON response from Gemini:", parseError);
      console.error("Received non-JSON string:", jsonResponseString); // Log the problematic string
      // Send back a specific error about the format
      res.status(500);
      throw new Error(`AI service returned an invalid data format.`);
    }

    // --- Send successful response ---
    res.status(200).json({
      success: true,
      data: measures,
      message: "AI-generated ecosystem measures retrieved successfully."
    });

  } catch (error) {
    // Handle errors from Gemini service or the JSON parsing validation
    console.error("Error in generateEcosystemMeasures controller:", error);
    // Determine status code based on error type if possible
    const statusCode = res.statusCode === 200 ? 500 : res.statusCode; // Use 500 unless already set
    res.status(statusCode);
    // Send a user-friendly error message, potentially masking internal details
    // If it's a format error from parsing, use that specific message
    throw new Error(error.message.includes("invalid data format")
                   ? error.message
                   : `Failed to generate ecosystem measures from AI service.`);
  }
});

// @desc    Generate enhanced operational scenarios with threat intelligence
// @route   POST /api/ai/generate-enhanced-operational-scenarios
// @access  Private
const generateEnhancedOperationalScenarios = asyncHandler(async (req, res) => {
  console.log('Received request for enhanced operational scenario generation:', req.body);
  const {
    analysisId,
    attackPath,
    threatIntelligence,
    businessAssets = [],
    options = {}
  } = req.body;

  // --- Input Validation ---
  if (!analysisId || !attackPath) {
    res.status(400);
    throw new Error('Missing required context: analysisId and attackPath are required.');
  }

  // --- Prepare threat intelligence context ---
  let threatContext = '';
  if (threatIntelligence && threatIntelligence.assetThreats) {
    const relevantThreats = Object.entries(threatIntelligence.assetThreats)
      .filter(([asset, threats]) => threats.vulnerabilities.length > 0)
      .map(([asset, threats]) => {
        const topCVEs = threats.vulnerabilities.slice(0, 3);
        const topTechniques = threats.techniques.slice(0, 5);

        return `Asset: ${asset}
        - Vulnerabilities: ${topCVEs.map(cve => `${cve.cveId} (${cve.severity}, Score: ${cve.score})`).join(', ')}
        - ATT&CK Techniques: ${topTechniques.map(t => `${t.techniqueId} (${t.techniqueName})`).join(', ')}
        - Risk Score: ${threats.riskScore}`;
      }).join('\n\n');

    threatContext = `\n\nCONTEXTE D'INTELLIGENCE DES MENACES:
${relevantThreats}

TECHNIQUES ATT&CK GLOBALES DISPONIBLES:
${threatIntelligence.globalTechniques.slice(0, 10).map(t =>
  `- ${t.techniqueId}: ${t.techniqueName} (${t.tactic})`
).join('\n')}`;
  }

  // --- Prepare business assets context ---
  const businessAssetsContext = businessAssets.length > 0
    ? `\n\nACTIFS MÉTIER CONCERNÉS:
${businessAssets.map(asset =>
  `- ${asset.name}: ${asset.description || 'Description non disponible'} (Type: ${asset.type || 'Non spécifié'})`
).join('\n')}`
    : '';

  const prompt = `
    Rôle: Expert en cybersécurité spécialisé dans la méthode EBIOS RM et l'analyse de scénarios opérationnels d'attaque avec intelligence des menaces.
    Objectif: Générer un scénario opérationnel détaillé et réaliste basé sur le chemin d'attaque fourni, enrichi par l'intelligence des menaces réelles (CVE et MITRE ATT&CK).

    MODÈLE DE SÉQUENCE D'ATTAQUE OBLIGATOIRE (EBIOS RM):
    Le scénario DOIT suivre les 4 phases dans l'ordre:

    1. CONNAITRE (Reconnaissance et découverte externes):
       - Reconnaissance externe sources ouvertes
       - Reconnaissance externe avancée
       - Recrutement d'une source

    2. RENTRER (Intrusion dans le système):
       - Intrusion via mail de hameçonnage
       - Intrusion via un canal d'accès préexistant
       - Intrusion ou piège physique

    3. TROUVER (Reconnaissance interne et latéralisation):
       - Reconnaissance interne réseaux
       - Latéralisation vers réseaux LAN
       - Élévation de privilèges

    4. EXPLOITER (Exploitation des données et biens supports):
       - Exploitation maliciel de collecte
       - Création canal d'exfiltration
       - Vol et exploitation de données

    Contexte Fourni:
    - ID d'Analyse: ${analysisId}
    - Chemin d'attaque:
      * Code de référence: ${attackPath.referenceCode || 'Non spécifié'}
      * Source de risque: ${attackPath.sourceRiskName || attackPath.name || 'Non spécifié'}
      * Objectif visé: ${attackPath.objectifVise || attackPath.targetObjective || 'Non spécifié'}
      * Événement redouté: ${attackPath.dreadedEventName || 'Non spécifié'}
      * Description: ${attackPath.description || 'Non spécifiée'}
    ${threatContext}
    ${businessAssetsContext}

    INSTRUCTIONS SPÉCIALES POUR L'INTELLIGENCE DES MENACES:
    1. UTILISEZ les CVE réels fournis pour justifier les vulnérabilités exploitées
    2. INTÉGREZ les techniques MITRE ATT&CK spécifiques dans chaque phase
    3. ADAPTEZ la complexité selon les scores CVSS des vulnérabilités
    4. RÉFÉRENCEZ les CVE et techniques ATT&CK dans les descriptions d'étapes
    5. CALCULEZ la probabilité basée sur la disponibilité des exploits et la sévérité des CVE

    Tâche:
    Générez UN scénario opérationnel détaillé qui exploite concrètement les vulnérabilités et techniques identifiées par l'intelligence des menaces.

    Pour le scénario opérationnel, fournir les informations suivantes:
    1. 'name': Un nom concis et descriptif du scénario (en français)
    2. 'description': Une description détaillée du scénario d'attaque (en français)
    3. 'attackPathId': L'ID exact du chemin d'attaque source
    4. 'attackPathReference': Le code de référence du chemin d'attaque
    5. 'steps': Un tableau d'étapes opérationnelles détaillées suivant OBLIGATOIREMENT les 4 phases EBIOS RM, chaque étape contenant:
       - 'id': Identifiant unique de l'étape
       - 'name': Nom de l'étape
       - 'phase': Phase EBIOS RM (CONNAITRE, RENTRER, TROUVER, ou EXPLOITER)
       - 'description': Description détaillée de l'action avec références CVE/ATT&CK
       - 'techniques': Techniques MITRE ATT&CK utilisées (IDs et noms)
       - 'vulnerabilities': CVE exploités dans cette étape
       - 'indicators': Indicateurs de compromission potentiels
       - 'duration': Durée estimée de l'étape
    6. 'severity': Niveau de gravité basé sur les CVE (Très faible, Faible, Moyen, Élevé, Très élevé)
    7. 'likelihood': Probabilité de réussite basée sur l'intelligence des menaces
    8. 'detectionDifficulty': Difficulté de détection
    9. 'requiredSkills': Compétences requises pour l'attaquant
    10. 'resources': Ressources nécessaires pour l'attaque
    11. 'timeline': Durée totale estimée du scénario
    12. 'threatIntelligenceUsed': Résumé de l'intelligence des menaces utilisée

    Format de Réponse Exigé:
    Répondez UNIQUEMENT avec un objet JSON valide (pas un tableau). N'incluez aucun texte, formatage markdown, ou explication en dehors de la structure JSON.
  `;

  console.log("--- Generated Enhanced Operational Scenario Prompt ---");
  console.log(prompt);
  console.log("--------------------------------------------------");

  try {
    // --- Call Gemini API Service ---
    const jsonResponseString = await generateJsonContent(prompt);

    // --- Parse the JSON response ---
    let scenario = {};
    try {
      scenario = JSON.parse(jsonResponseString);

      if (Array.isArray(scenario)) {
        console.error("Expected object but received array:", scenario);
        throw new Error("Expected single scenario object, not array.");
      }

      // Validate required fields
      const requiredFields = ['name', 'description', 'steps', 'severity', 'likelihood'];
      for (const field of requiredFields) {
        if (!scenario.hasOwnProperty(field)) {
          console.error(`Missing required field: ${field}`, scenario);
          throw new Error(`Parsed response missing required field: ${field}`);
        }
      }

      // Validate steps array
      if (!Array.isArray(scenario.steps)) {
        console.error("Steps field is not an array:", scenario.steps);
        throw new Error("Steps field must be an array of step objects.");
      }

      // Add metadata
      scenario.id = `enhanced_scenario_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      scenario.createdAt = new Date().toISOString();
      scenario.source = 'ai_generated_enhanced';
      scenario.attackPathId = attackPath.id;
      scenario.attackPathReference = attackPath.referenceCode;

    } catch (parseError) {
      console.error("Failed to parse JSON response from Gemini:", parseError);
      console.error("Received non-JSON string:", jsonResponseString);
      res.status(500);
      throw new Error(`AI service returned an invalid data format.`);
    }

    // --- Send successful response ---
    res.status(200).json({
      success: true,
      data: { scenario: scenario },
      message: "Enhanced operational scenario generated successfully."
    });

  } catch (error) {
    console.error("Error in generateEnhancedOperationalScenarios controller:", error);
    const statusCode = res.statusCode === 200 ? 500 : res.statusCode;
    res.status(statusCode);
    throw new Error(error.message.includes("invalid data format")
                    ? error.message
                    : `Failed to generate enhanced operational scenario from AI service.`);
  }
});

module.exports = {
  suggestDreadedEvents,
  generateOperationalScenarios,
  suggestSecurityControls,
  suggestSourcesRisque,
  generateEcosystemMeasures,
  generateEnhancedOperationalScenarios
};