// src/services/frameworkDefinitionService.js
import api from '../api/apiClient'; // Adjust the path based on your structure

const BASE_URL = '/framework-definitions'; // Base path for these endpoints

/**
 * Fetches framework definitions (predefined + custom) for a specific analysis.
 * @param {string} analysisId - The ID of the analysis.
 * @returns {Promise<Object>} - The API response (might contain { success, data, message }).
 */
const getFrameworkDefinitions = async (analysisId) => {
    if (!analysisId) {
        console.error("getFrameworkDefinitions requires an analysisId.");
        // Return a consistent error format or throw?
        return { success: false, message: "Analysis ID is required." };
    }
    try {
        // CORRECTED: Pass parameters directly, not nested under 'params'
        const response = await api.get(BASE_URL, { analysisId });
        // Assuming your api client wraps the response, adjust if needed
        return response;
    } catch (error) {
        console.error('Error fetching framework definitions:', error);
        // Return error structure consistent with your api client
        return { success: false, message: error.message || 'Failed to fetch framework definitions.' };
    }
};

/**
 * Creates a new custom framework definition linked to an analysis.
 * @param {Object} definitionData - The framework data ({ name, description, rules, analysisId }).
 * @returns {Promise<Object>} - The API response.
 */
const createFrameworkDefinition = async (definitionData) => {
    if (!definitionData || !definitionData.analysisId || !definitionData.name || !definitionData.rules) {
        console.error("createFrameworkDefinition requires definitionData with analysisId, name, and rules.");
        return { success: false, message: "Required definition data is missing." };
    }
    try {
        const response = await api.post(BASE_URL, definitionData);
        return response;
    } catch (error) {
        console.error('Error creating framework definition:', error);
        return { success: false, message: error.message || 'Failed to create framework definition.' };
    }
};

// Add update/delete functions later if needed
// const updateFrameworkDefinition = async (definitionId, updateData) => { ... };
// const deleteFrameworkDefinition = async (definitionId) => { ... };

const frameworkDefinitionService = {
    getFrameworkDefinitions,
    createFrameworkDefinition,
    // updateFrameworkDefinition,
    // deleteFrameworkDefinition,
};

export default frameworkDefinitionService;