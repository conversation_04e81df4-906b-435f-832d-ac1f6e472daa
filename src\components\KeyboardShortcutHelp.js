// Create a new component for displaying keyboard shortcuts
// You can place this in a separate file like components/KeyboardShortcutHelp.jsx

import React, { useState } from 'react';
import { Keyboard, X } from 'lucide-react';

const KeyboardShortcutHelp = () => {
  const [isOpen, setIsOpen] = useState(false);
  
  const shortcuts = [
    { keys: ['Ctrl', '→'], description: 'Aller à l\'onglet suivant' },
    { keys: ['Ctrl', '←'], description: 'Aller à l\'onglet précédent' },
    // You can add more shortcuts here in the future
  ];
  
  return (
    <div className="fixed bottom-10 right-4 z-10">
      {/* Keyboard icon button */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="bg-gray-700 hover:bg-gray-800 text-white p-2 rounded-full shadow-lg flex items-center justify-center transition-all"
        title="Raccourcis clavier"
      >
        <Keyboard size={18} />
      </button>
      
      {/* Shortcut info panel */}
      {isOpen && (
        <div className="absolute bottom-12 right-0 bg-white rounded-lg shadow-xl border border-gray-200 w-64 overflow-hidden">
          <div className="flex items-center justify-between bg-gray-100 px-4 py-2 border-b border-gray-200">
            <h3 className="font-medium text-gray-700">Raccourcis clavier</h3>
            <button 
              onClick={() => setIsOpen(false)}
              className="text-gray-500 hover:text-gray-700"
            >
              <X size={16} />
            </button>
          </div>
          
          <ul className="py-2">
            {shortcuts.map((shortcut, index) => (
              <li key={index} className="px-4 py-2 hover:bg-gray-50 flex items-center justify-between">
                <span className="text-gray-700">{shortcut.description}</span>
                <div className="flex items-center space-x-1">
                  {shortcut.keys.map((key, keyIndex) => (
                    <React.Fragment key={keyIndex}>
                      <kbd className="px-2 py-1 text-xs font-semibold text-gray-800 bg-gray-100 border border-gray-200 rounded-md shadow-sm">
                        {key}
                      </kbd>
                      {keyIndex < shortcut.keys.length - 1 && (
                        <span className="text-gray-500">+</span>
                      )}
                    </React.Fragment>
                  ))}
                </div>
              </li>
            ))}
          </ul>
        </div>
      )}
    </div>
  );
};

export default KeyboardShortcutHelp;