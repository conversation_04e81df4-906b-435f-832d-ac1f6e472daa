// routes/threatIntelligenceRoutes.js
const express = require('express');
const router = express.Router();
const {
  createOrUpdateReport,
  getReportsByAnalysis,
  getReportByAsset,
  updateSelections,
  deleteReport,
  getAnalysisStats,
  saveCTIResults,
  loadCTIResults
} = require('../controllers/threatIntelligenceController');
const { protect } = require('../middleware/authMiddleware');

// All routes are protected
router.use(protect);

// @route   POST /api/threat-intelligence/reports
// @desc    Create or update threat intelligence report
// @access  Private
router.post('/reports', createOrUpdateReport);

// @route   GET /api/threat-intelligence/reports/:analysisId
// @desc    Get all threat intelligence reports for an analysis
// @access  Private
router.get('/reports/:analysisId', getReportsByAnalysis);

// @route   GET /api/threat-intelligence/reports/:analysisId/:assetId
// @desc    Get specific threat intelligence report for an asset
// @access  Private
router.get('/reports/:analysisId/:assetId', getReportByAsset);

// @route   PUT /api/threat-intelligence/reports/:analysisId/:assetId/selections
// @desc    Update user selections for vulnerabilities and attack techniques
// @access  Private
router.put('/reports/:analysisId/:assetId/selections', updateSelections);

// @route   DELETE /api/threat-intelligence/reports/:analysisId/:assetId
// @desc    Delete threat intelligence report
// @access  Private
router.delete('/reports/:analysisId/:assetId', deleteReport);

// @route   GET /api/threat-intelligence/stats/:analysisId
// @desc    Get threat intelligence statistics for an analysis
// @access  Private
router.get('/stats/:analysisId', getAnalysisStats);

module.exports = router;
