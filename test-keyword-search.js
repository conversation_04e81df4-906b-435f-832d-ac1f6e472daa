// test-keyword-search.js
// Test script to verify enhanced keyword-based CTI searching

const axios = require('axios');

const BASE_URL = 'http://localhost:5000/api/nist/search';

async function testKeywordBasedSearch() {
  console.log('🧪 Testing Enhanced Keyword-Based CTI Search...\n');

  // Test cases with different asset types
  const testAssets = [
    {
      name: 'Cisco ISR 4000 Router',
      vendor: 'Cisco',
      product: 'ISR 4000',
      version: '16.12.05',
      expectedKeywords: ['Cisco', 'ISR', '4000']
    },
    {
      name: 'Windows Server 2019',
      vendor: 'Microsoft',
      product: 'Windows Server',
      version: '2019',
      expectedKeywords: ['Microsoft', 'Windows', 'Server']
    },
    {
      name: 'Apache HTTP Server',
      vendor: 'Apache',
      product: 'HTTP Server',
      version: '2.4.41',
      expectedKeywords: ['Apache', 'HTTP', 'Server']
    },
    {
      name: 'Fortinet FortiGate Firewall',
      vendor: 'Fortinet',
      product: 'FortiGate',
      version: '6.4.0',
      expectedKeywords: ['Fortinet', 'FortiGate']
    },
    {
      name: 'VMware vSphere ESXi',
      vendor: 'VMware',
      product: 'vSphere ESXi',
      version: '7.0',
      expectedKeywords: ['VMware', 'vSphere', 'ESXi']
    }
  ];

  for (const asset of testAssets) {
    console.log(`\n📋 Testing asset: ${asset.name}`);
    
    // Test 1: Keyword search with product name
    try {
      console.log(`1. Testing keyword search with: ${asset.expectedKeywords.join(' ')}`);
      
      const keywordResponse = await axios.get(BASE_URL, {
        params: {
          keywordSearch: asset.expectedKeywords.join(' '),
          resultsPerPage: 10,
          startIndex: 0
        }
      });

      console.log(`✅ Keyword search results: ${keywordResponse.data.totalResults || 0} vulnerabilities found`);
      
      if (keywordResponse.data.vulnerabilities && keywordResponse.data.vulnerabilities.length > 0) {
        const firstVuln = keywordResponse.data.vulnerabilities[0];
        console.log(`   📄 First result: ${firstVuln.cve.id}`);
        console.log(`   📝 Description: ${firstVuln.cve.descriptions[0]?.value.substring(0, 100)}...`);
      }

    } catch (error) {
      console.error(`❌ Keyword search failed:`, error.response?.data?.message || error.message);
    }

    // Test 2: Individual product keyword search
    try {
      console.log(`2. Testing individual product search: ${asset.product}`);
      
      const productResponse = await axios.get(BASE_URL, {
        params: {
          keywordSearch: asset.product,
          resultsPerPage: 5,
          startIndex: 0
        }
      });

      console.log(`✅ Product search results: ${productResponse.data.totalResults || 0} vulnerabilities found`);

    } catch (error) {
      console.error(`❌ Product search failed:`, error.response?.data?.message || error.message);
    }

    // Test 3: Vendor-specific search
    try {
      console.log(`3. Testing vendor search: ${asset.vendor}`);
      
      const vendorResponse = await axios.get(BASE_URL, {
        params: {
          keywordSearch: asset.vendor,
          resultsPerPage: 5,
          startIndex: 0
        }
      });

      console.log(`✅ Vendor search results: ${vendorResponse.data.totalResults || 0} vulnerabilities found`);

    } catch (error) {
      console.error(`❌ Vendor search failed:`, error.response?.data?.message || error.message);
    }

    // Add delay between asset tests to respect rate limiting
    await new Promise(resolve => setTimeout(resolve, 2000));
  }

  // Test 4: Generic technology searches
  console.log('\n🔍 Testing generic technology searches...');
  
  const genericSearches = [
    'router vulnerability',
    'firewall security',
    'web server apache',
    'database mysql',
    'operating system windows'
  ];

  for (const searchTerm of genericSearches) {
    try {
      console.log(`\n4. Testing generic search: "${searchTerm}"`);
      
      const genericResponse = await axios.get(BASE_URL, {
        params: {
          keywordSearch: searchTerm,
          resultsPerPage: 3,
          startIndex: 0
        }
      });

      console.log(`✅ Generic search "${searchTerm}": ${genericResponse.data.totalResults || 0} results`);

    } catch (error) {
      console.error(`❌ Generic search failed:`, error.response?.data?.message || error.message);
    }

    // Add delay between searches
    await new Promise(resolve => setTimeout(resolve, 1500));
  }

  // Test 5: Compare keyword vs CPE search
  console.log('\n⚖️ Comparing keyword vs CPE search...');
  
  const comparisonAsset = testAssets[0]; // Cisco ISR 4000
  
  try {
    // Keyword search
    const keywordResult = await axios.get(BASE_URL, {
      params: {
        keywordSearch: 'Cisco ISR 4000',
        resultsPerPage: 10,
        startIndex: 0
      }
    });

    console.log(`📊 Keyword search results: ${keywordResult.data.totalResults || 0}`);

    // CPE search
    const cpe = `cpe:2.3:a:cisco:isr_4000:16.12.05:*:*:*:*:*:*:*`;
    const cpeResult = await axios.get(BASE_URL, {
      params: {
        cpeName: cpe,
        resultsPerPage: 10,
        startIndex: 0
      }
    });

    console.log(`📊 CPE search results: ${cpeResult.data.totalResults || 0}`);
    
    if (keywordResult.data.totalResults > cpeResult.data.totalResults) {
      console.log('✅ Keyword search found more results than CPE search');
    } else if (cpeResult.data.totalResults > keywordResult.data.totalResults) {
      console.log('⚠️ CPE search found more results than keyword search');
    } else {
      console.log('📊 Both searches found similar number of results');
    }

  } catch (error) {
    console.error(`❌ Comparison test failed:`, error.message);
  }

  console.log('\n🏁 Keyword-based search testing completed!');
  console.log('\n💡 Recommendations:');
  console.log('   - Use keyword search as primary method for better coverage');
  console.log('   - Combine vendor + product names for best results');
  console.log('   - Use CPE search as fallback for specific version targeting');
  console.log('   - Generic technology terms can provide broader vulnerability coverage');
}

// Run the test
if (require.main === module) {
  testKeywordBasedSearch().catch(console.error);
}

module.exports = { testKeywordBasedSearch };
