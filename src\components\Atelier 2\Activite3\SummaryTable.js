// src/components/Atelier 2/Activite3/SummaryTable.js
import React, { useState, useMemo } from 'react';
import { Search, ChevronDown, ChevronUp, Download, X, Table, RefreshCw, AlertCircle } from 'lucide-react';

const SummaryTable = ({
  mappings,
  sourcesRisque,
  dreadedEvents,
  // Unused props removed
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [sortField, setSortField] = useState('sourceRisk');
  const [sortDirection, setSortDirection] = useState('asc');
  const [filterPillar, setFilterPillar] = useState('');
  const [filterCategory, setFilterCategory] = useState('');

  // Get all unique pillars and categories
  const pillars = [...new Set(dreadedEvents.map(event => event.securityPillar))];
  const categories = [...new Set(sourcesRisque.map(source => source.category))];

  // Create a combined data structure with all the information
  const tableData = mappings.map(mapping => {
    const sourceRisk = sourcesRisque.find(source => source.id === mapping.sourceId);
    const dreadedEvent = dreadedEvents.find(event => event.id === mapping.dreadedEventId);

    if (!sourceRisk || !dreadedEvent) return null;

    // Extract impact information
    const impacts = {};
    if (dreadedEvent.impacts) {
      Object.keys(dreadedEvent.impacts).forEach(impactType => {
        impacts[impactType] = dreadedEvent.impacts[impactType];
      });
    }

    return {
      id: mapping.id,
      sourceId: sourceRisk.id,
      dreadedEventId: dreadedEvent.id,
      sourceRisk: sourceRisk.name,
      sourceCategory: sourceRisk.category,
      objectifVise: sourceRisk.objectifVise,
      objectifViseCategory: sourceRisk.objectifViseCategory,
      dreadedEvent: dreadedEvent.name,
      securityPillar: dreadedEvent.securityPillar,
      severity: dreadedEvent.severity,
      motivation: sourceRisk.motivation,
      activite: sourceRisk.activite,
      ressources: sourceRisk.ressources,
      sourceDescription: sourceRisk.description,
      dreadedEventDescription: dreadedEvent.description,
      impacts: impacts,
      fullSourceRisk: sourceRisk,
      fullDreadedEvent: dreadedEvent
    };
  }).filter(Boolean);

  // Filter the data
  const filteredData = tableData.filter(item => {
    const matchesSearch =
      (item.sourceRisk?.toLowerCase() || '').includes(searchTerm.toLowerCase()) ||
      (item.dreadedEvent?.toLowerCase() || '').includes(searchTerm.toLowerCase()) ||
      (item.objectifVise?.toLowerCase() || '').includes(searchTerm.toLowerCase());

    const matchesPillar = filterPillar ? item.securityPillar === filterPillar : true;
    const matchesCategory = filterCategory ? item.sourceCategory === filterCategory : true;

    return matchesSearch && matchesPillar && matchesCategory;
  });

  // Sort the data
  const sortedData = [...filteredData].sort((a, b) => {
    let aValue = a[sortField] || '';
    let bValue = b[sortField] || '';

    // Handle string comparison
    if (typeof aValue === 'string') {
      aValue = aValue.toLowerCase();
      bValue = bValue.toLowerCase();
    }

    if (aValue < bValue) return sortDirection === 'asc' ? -1 : 1;
    if (aValue > bValue) return sortDirection === 'asc' ? 1 : -1;
    return 0;
  });

  // Process data for merged cells
  const processedData = useMemo(() => {
    if (sortedData.length === 0) return [];

    // Create a new array with rowspan information
    const result = [];

    // Track current source, category, and objectif for rowspan calculation
    let currentSourceId = null;
    let currentSourceIndex = -1;
    let currentCategoryValue = null;
    let currentCategoryIndex = -1;
    let currentObjectifValue = null;
    let currentObjectifIndex = -1;

    sortedData.forEach((item, index) => {
      // Create a new item with rowspan information
      const newItem = {
        ...item,
        sourceRowspan: 1,
        categoryRowspan: 1,
        objectifRowspan: 1,
        // Ensure all cells have values, even if empty
        sourceRisk: item.sourceRisk || '',
        sourceCategory: item.sourceCategory || '',
        objectifVise: item.objectifVise || '',
        objectifViseCategory: item.objectifViseCategory || '',
        dreadedEvent: item.dreadedEvent || '',
        securityPillar: item.securityPillar || '',
        severity: item.severity || '',
        motivation: item.motivation || '',
        impacts: item.impacts || {}
      };

      // Check if this is a new source or the same as the previous one
      if (item.sourceId === currentSourceId) {
        // Same source, increment the rowspan of the first occurrence
        result[currentSourceIndex].sourceRowspan++;
        // Mark this item to not display the source
        newItem.hideSource = true;

        // Check if category is the same as the current category
        if (item.sourceCategory === currentCategoryValue) {
          // Same category, increment the rowspan of the first occurrence
          result[currentCategoryIndex].categoryRowspan++;
          // Mark this item to not display the category
          newItem.hideCategory = true;
        } else {
          // New category within the same source
          currentCategoryValue = item.sourceCategory;
          currentCategoryIndex = index;
        }

        // Check if objectif is the same as the current objectif
        if (item.objectifVise === currentObjectifValue) {
          // Same objectif, increment the rowspan of the first occurrence
          result[currentObjectifIndex].objectifRowspan++;
          // Mark this item to not display the objectif
          newItem.hideObjectif = true;
        } else {
          // New objectif within the same source
          currentObjectifValue = item.objectifVise;
          currentObjectifIndex = index;
        }
      } else {
        // New source
        currentSourceId = item.sourceId;
        currentSourceIndex = index;

        // Reset category and objectif tracking for the new source
        currentCategoryValue = item.sourceCategory;
        currentCategoryIndex = index;
        currentObjectifValue = item.objectifVise;
        currentObjectifIndex = index;
      }

      result.push(newItem);
    });

    return result;
  }, [sortedData]);

  // Handle sort change
  const handleSort = (field) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  // Format severity
  const formatSeverity = (severity) => {
    switch(severity) {
      case 'minor': return 'Mineure';
      case 'moderate': return 'Modérée';
      case 'major': return 'Majeure';
      case 'critical': return 'Critique';
      case 'catastrophic': return 'Catastrophique';
      default: return severity;
    }
  };

  // Format level
  const formatLevel = (level) => {
    if (!level) return 'N/A';

    const normalizedLevel = String(level).toLowerCase();

    // Motivation et activité
    if (normalizedLevel.startsWith('faible')) return 'Faible';
    if (normalizedLevel.startsWith('moyen')) return 'Moyen';
    if (normalizedLevel.startsWith('elev')) return 'Élevé';

    // Ressources
    if (normalizedLevel.startsWith('import')) return 'Importantes';
    if (normalizedLevel === 'moyennes') return 'Moyennes';
    if (normalizedLevel === 'faibles') return 'Faibles';

    return level;
  };

  // Get color for level
  const getLevelColor = (level) => {
    if (!level) return 'bg-gray-100 text-gray-800';

    const lowerLevel = level.toLowerCase();
    if (lowerLevel === 'faible' || lowerLevel === 'faibles') {
      return 'bg-green-100 text-green-800';
    } else if (lowerLevel === 'moyen' || lowerLevel === 'moyennes') {
      return 'bg-yellow-100 text-yellow-800';
    } else if (lowerLevel === 'eleve' || lowerLevel === 'importantes') {
      return 'bg-red-100 text-red-800';
    }

    return 'bg-gray-100 text-gray-800';
  };

  // Get color for severity - using the same colors as in Dashboard.js
  const getSeverityColor = (severity) => {
    switch(severity) {
      case 'minor': return 'bg-emerald-100 text-emerald-800 border border-emerald-300';
      case 'moderate': return 'bg-yellow-100 text-yellow-800 border border-yellow-300';
      case 'major': return 'bg-orange-100 text-orange-800 border border-orange-300';
      case 'critical': return 'bg-red-100 text-red-800 border border-red-300';
      case 'catastrophic': return 'bg-slate-800 text-white border border-slate-700';
      default: return 'bg-gray-100 text-gray-800 border border-gray-300';
    }
  };

  // Get color for security pillar - using the same colors as in constants/index.js
  const getPillarColor = (pillar) => {
    switch (pillar?.toLowerCase()) {
      case 'confidentialite': return 'bg-blue-100 text-blue-800 border border-blue-300';
      case 'integrite': return 'bg-green-100 text-green-800 border border-green-300';
      case 'disponibilite': return 'bg-yellow-100 text-yellow-800 border border-yellow-300';
      case 'tracabilite': return 'bg-orange-100 text-orange-800 border border-orange-300';
      case 'preuve': return 'bg-purple-100 text-purple-800 border border-purple-300';
      case 'auditabilite':
      case 'auditabilité':
      case 'Auditabilite': return 'bg-red-100 text-red-800 border border-red-300';
      default: return 'bg-gray-100 text-gray-800 border border-gray-300';
    }
  };

  // Export to CSV
  const exportToCSV = () => {
    // Define headers
    const headers = [
      'Source de Risque',
      'Catégorie SR',
      'Objectif Visé',
      'Catégorie OV',
      'Événement Redouté',
      'Pilier de Sécurité',
      'Gravité',
      'Motivation',
      'Activité',
      'Ressources'
    ];

    // Create CSV content
    const csvContent = [
      headers.join(','),
      ...sortedData.map(item => [
        `"${item.sourceRisk || ''}"`,
        `"${item.sourceCategory || ''}"`,
        `"${item.objectifVise || ''}"`,
        `"${item.objectifViseCategory || ''}"`,
        `"${item.dreadedEvent || ''}"`,
        `"${item.securityPillar || ''}"`,
        `"${formatSeverity(item.severity) || ''}"`,
        `"${formatLevel(item.motivation) || ''}"`,
        `"${formatLevel(item.activite) || ''}"`,
        `"${formatLevel(item.ressources) || ''}"`
      ].join(','))
    ].join('\n');

    // Create download link
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.setAttribute('href', url);
    link.setAttribute('download', 'sr-ov-er-mappings.csv');
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  return (
    <div className="bg-white rounded-lg border border-gray-200 overflow-hidden shadow-md">
      {/* Enhanced Header */}
      <div className="p-5 border-b border-gray-200 bg-gradient-to-r from-blue-50 to-white">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between">
          <div className="flex items-center mb-3 md:mb-0">
            <div className="bg-blue-100 p-2.5 rounded-lg mr-3 shadow-sm">
              <Table size={20} className="text-blue-600" />
            </div>
            <div>
              <h2 className="text-xl font-semibold text-gray-800">Tableau Récapitulatif des Associations</h2>
              <p className="text-sm text-gray-500 mt-1">Visualisation des associations entre sources de risque et événements redoutés</p>
            </div>
          </div>
          <div className="flex items-center space-x-3">
            <span className="text-sm bg-blue-50 text-blue-700 px-3 py-1.5 rounded-md font-medium shadow-sm border border-blue-100">
              {filteredData.length} association(s)
            </span>
            <button
              onClick={exportToCSV}
              className="bg-blue-600 text-white px-4 py-2 rounded-md flex items-center text-sm hover:bg-blue-700 transition-colors shadow-sm font-medium"
            >
              <Download size={16} className="mr-2" />
              Exporter CSV
            </button>
          </div>
        </div>
      </div>

      {/* Enhanced Filters */}
      <div className="p-5 border-b border-gray-200 bg-gray-50">
        <div className="flex flex-col md:flex-row gap-4">
          <div className="flex-1">
            <label className="block text-xs font-medium text-gray-700 mb-1.5">Recherche</label>
            <div className="relative">
              <input
                type="text"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-10 py-2.5 border border-gray-300 rounded-md shadow-sm focus:ring-2 focus:ring-blue-300 focus:border-blue-300 text-sm"
                placeholder="Rechercher par nom, description..."
              />
              <div className="absolute left-3 top-2.5 text-blue-500">
                <Search size={18} />
              </div>
              {searchTerm && (
                <button
                  className="absolute right-3 top-2.5 text-gray-400 hover:text-gray-600 hover:bg-gray-100 p-1 rounded-full"
                  onClick={() => setSearchTerm('')}
                  title="Effacer la recherche"
                >
                  <X size={16} />
                </button>
              )}
            </div>
          </div>

          <div className="md:w-56">
            <label className="block text-xs font-medium text-gray-700 mb-1.5">Pilier de sécurité</label>
            <div className="relative">
              <select
                value={filterPillar}
                onChange={(e) => setFilterPillar(e.target.value)}
                className="w-full pl-4 pr-10 py-2.5 border border-gray-300 rounded-md shadow-sm appearance-none focus:ring-2 focus:ring-blue-300 focus:border-blue-300 text-sm"
              >
                <option value="">Tous les piliers</option>
                {pillars.map((pillar) => (
                  <option key={pillar} value={pillar}>
                    {pillar.charAt(0).toUpperCase() + pillar.slice(1)}
                  </option>
                ))}
              </select>
              <div className="absolute right-0 top-0 h-full flex items-center justify-center w-8 pointer-events-none text-gray-500">
                <ChevronDown size={16} />
              </div>
            </div>
          </div>

          <div className="md:w-56">
            <label className="block text-xs font-medium text-gray-700 mb-1.5">Catégorie de source</label>
            <div className="relative">
              <select
                value={filterCategory}
                onChange={(e) => setFilterCategory(e.target.value)}
                className="w-full pl-3 pr-8 py-2 border border-gray-300 rounded appearance-none focus:ring-1 focus:ring-blue-300 focus:border-blue-300"
              >
                <option value="">Toutes les catégories</option>
                {categories.map((category) => (
                  <option key={category} value={category}>
                    {category.charAt(0).toUpperCase() + category.slice(1)}
                  </option>
                ))}
              </select>
              <div className="absolute right-0 top-0 h-full flex items-center justify-center w-8 pointer-events-none text-gray-500">
                <ChevronDown size={14} />
              </div>
            </div>
          </div>
        </div>

        {/* Active filters */}
        {(searchTerm || filterPillar || filterCategory) && (
          <div className="flex flex-wrap gap-2 mt-3 items-center">
            <span className="text-xs text-gray-500">Filtres actifs:</span>

            {searchTerm && (
              <div className="bg-gray-100 text-gray-700 px-2 py-1 rounded text-xs flex items-center">
                <span className="mr-1">Recherche:</span>
                <span className="font-medium">{searchTerm}</span>
                <button
                  className="ml-1.5 text-gray-500 hover:text-gray-700"
                  onClick={() => setSearchTerm('')}
                >
                  <X size={12} />
                </button>
              </div>
            )}

            {filterPillar && (
              <div className="bg-gray-100 text-gray-700 px-2 py-1 rounded text-xs flex items-center">
                <span className="mr-1">Pilier:</span>
                <span className="font-medium">{filterPillar.charAt(0).toUpperCase() + filterPillar.slice(1)}</span>
                <button
                  className="ml-1.5 text-gray-500 hover:text-gray-700"
                  onClick={() => setFilterPillar('')}
                >
                  <X size={12} />
                </button>
              </div>
            )}

            {filterCategory && (
              <div className="bg-gray-100 text-gray-700 px-2 py-1 rounded text-xs flex items-center">
                <span className="mr-1">Catégorie:</span>
                <span className="font-medium">{filterCategory.charAt(0).toUpperCase() + filterCategory.slice(1)}</span>
                <button
                  className="ml-1.5 text-gray-500 hover:text-gray-700"
                  onClick={() => setFilterCategory('')}
                >
                  <X size={12} />
                </button>
              </div>
            )}

            <button
              className="text-blue-600 hover:text-blue-800 text-xs flex items-center"
              onClick={() => {
                setSearchTerm('');
                setFilterPillar('');
                setFilterCategory('');
              }}
            >
              <RefreshCw size={12} className="mr-1" />
              Réinitialiser
            </button>
          </div>
        )}
      </div>

      {/* Table with horizontal scrolling */}
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-300 border-collapse">
          <thead className="bg-gray-100 sticky top-0 z-10">
            <tr>
              <th
                scope="col"
                className="px-4 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider cursor-pointer border-b-2 border-gray-300 border-r border-gray-200 w-[16%]"
                onClick={() => handleSort('sourceRisk')}
              >
                <div className="flex items-center">
                  Source de Risque
                  {sortField === 'sourceRisk' && (
                    sortDirection === 'asc' ?
                      <ChevronUp size={14} className="ml-1" /> :
                      <ChevronDown size={14} className="ml-1" />
                  )}
                </div>
              </th>
              <th
                scope="col"
                className="px-4 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider cursor-pointer border-b-2 border-gray-300 border-r border-gray-200 w-[15%]"
                onClick={() => handleSort('objectifVise')}
              >
                <div className="flex items-center">
                  Objectif Visé
                  {sortField === 'objectifVise' && (
                    sortDirection === 'asc' ?
                      <ChevronUp size={14} className="ml-1" /> :
                      <ChevronDown size={14} className="ml-1" />
                  )}
                </div>
              </th>
              <th
                scope="col"
                className="px-4 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider cursor-pointer border-b-2 border-gray-300 border-r border-gray-200 w-[18%]"
                onClick={() => handleSort('dreadedEvent')}
              >
                <div className="flex items-center">
                  Événement Redouté
                  {sortField === 'dreadedEvent' && (
                    sortDirection === 'asc' ?
                      <ChevronUp size={14} className="ml-1" /> :
                      <ChevronDown size={14} className="ml-1" />
                  )}
                </div>
              </th>
              <th
                scope="col"
                className="px-4 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider cursor-pointer border-b-2 border-gray-300 border-r border-gray-200 w-[10%]"
                onClick={() => handleSort('securityPillar')}
              >
                <div className="flex items-center">
                  Pilier
                  {sortField === 'securityPillar' && (
                    sortDirection === 'asc' ?
                      <ChevronUp size={14} className="ml-1" /> :
                      <ChevronDown size={14} className="ml-1" />
                  )}
                </div>
              </th>
              <th
                scope="col"
                className="px-4 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider cursor-pointer border-b-2 border-gray-300 border-r border-gray-200 w-[8%]"
                onClick={() => handleSort('severity')}
              >
                <div className="flex items-center">
                  Gravité
                  {sortField === 'severity' && (
                    sortDirection === 'asc' ?
                      <ChevronUp size={14} className="ml-1" /> :
                      <ChevronDown size={14} className="ml-1" />
                  )}
                </div>
              </th>
              <th
                scope="col"
                className="px-4 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider cursor-pointer border-b-2 border-gray-300 border-r border-gray-200 w-[11%]"
                onClick={() => handleSort('motivation')}
              >
                <div className="flex items-center">
                  Capacités SR
                  {sortField === 'motivation' && (
                    sortDirection === 'asc' ?
                      <ChevronUp size={14} className="ml-1" /> :
                      <ChevronDown size={14} className="ml-1" />
                  )}
                </div>
              </th>
              <th
                scope="col"
                className="px-3 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider border-b-2 border-gray-300 border-r border-gray-200 w-[22%]"
              >
                <div className="flex flex-col">
                  <div className="flex items-center mb-1">
                    <span>Types d'Impacts</span>
                  </div>

                </div>
              </th>
              {/* Actions column removed */}
            </tr>
          </thead>

          <tbody className="bg-white divide-y divide-gray-300">
            {filteredData.length === 0 ? (
              <tr>
                <td colSpan="8" className="px-6 py-10 text-center">
                  <div className="w-16 h-16 mx-auto bg-gray-100 rounded-full flex items-center justify-center mb-4">
                    <Search size={24} className="text-gray-400" />
                  </div>
                  <h3 className="text-lg font-medium text-gray-800 mb-1">Aucune association trouvée</h3>
                  <p className="text-gray-500">Essayez de modifier vos critères de recherche</p>
                </td>
              </tr>
            ) : (
              processedData.map((item) => (
                <tr key={`${item.sourceId}-${item.dreadedEventId}`} className="hover:bg-gray-50 even:bg-gray-50 odd:bg-white border-b border-gray-300">
                  {/* Source de Risque avec Catégorie - with rowspan */}
                  {!item.hideSource ? (
                    <td
                      className="px-4 py-4 border-r border-gray-200 w-[220px]"
                      rowSpan={item.sourceRowspan}
                      style={{ backgroundColor: item.sourceRowspan > 1 ? '#f9fafb' : 'transparent' }}
                    >
                      <div className="text-sm font-medium text-gray-900" title={item.sourceRisk}>{item.sourceRisk}</div>
                      {item.sourceCategory ? (
                        <div className="mt-2">
                          <span className="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-800" title={item.sourceCategory}>
                            {item.sourceCategory}
                          </span>
                        </div>
                      ) : null}
                    </td>
                  ) : null}

                  {/* Objectif Visé - with rowspan */}
                  {!item.hideObjectif ? (
                    <td
                      className="px-4 py-4 border-r border-gray-200 w-[170px]"
                      rowSpan={item.objectifRowspan}
                      style={{ backgroundColor: item.objectifRowspan > 1 ? '#f9fafb' : 'transparent' }}
                    >
                      {item.objectifVise ? (
                        <>
                          <div className="text-sm text-gray-900" title={item.objectifVise}>{item.objectifVise}</div>
                          {item.objectifViseCategory && (
                            <div className="text-xs text-gray-500 mt-2" title={item.objectifViseCategory}>
                              {item.objectifViseCategory}
                            </div>
                          )}
                        </>
                      ) : (
                        <span className="text-xs text-gray-400">-</span>
                      )}
                    </td>
                  ) : null}

                  {/* Événement Redouté */}
                  <td className="px-4 py-4 border-r border-gray-200 w-[200px]">
                    {item.dreadedEvent ? (
                      <>
                        <div className="text-sm text-gray-900" title={item.dreadedEvent}>{item.dreadedEvent}</div>
                        {item.dreadedEventDescription && (
                          <div className="text-xs text-gray-500 mt-2" title={item.dreadedEventDescription}>
                            {item.dreadedEventDescription.length > 60
                              ? `${item.dreadedEventDescription.substring(0, 60)}...`
                              : item.dreadedEventDescription}
                          </div>
                        )}
                      </>
                    ) : (
                      <span className="text-xs text-gray-400">-</span>
                    )}
                  </td>

                  {/* Pilier */}
                  <td className="px-4 py-4 border-r border-gray-200 w-[130px]">
                    {item.securityPillar ? (
                      <span className={`px-3 py-1.5 inline-flex text-xs leading-5 font-semibold rounded-full ${getPillarColor(item.securityPillar)}`} title={item.securityPillar}>
                        {item.securityPillar.charAt(0).toUpperCase() + item.securityPillar.slice(1)}
                      </span>
                    ) : (
                      <span className="text-xs text-gray-400">-</span>
                    )}
                  </td>

                  {/* Gravité */}
                  <td className="px-4 py-4 border-r border-gray-200 w-[100px]">
                    {item.severity ? (
                      <span className={`px-3 py-1.5 inline-flex text-xs leading-5 font-semibold rounded-full ${getSeverityColor(item.severity)}`} title={formatSeverity(item.severity)}>
                        {formatSeverity(item.severity)}
                      </span>
                    ) : (
                      <span className="text-xs text-gray-400">-</span>
                    )}
                  </td>

                  {/* Capacités SR (Ressources, Activité, Motivation) */}
                  <td className="px-4 py-4 border-r border-gray-200 w-[140px]">
                    <div className="flex flex-col space-y-2">
                      {/* Ressources */}
                      <div className="flex items-center">
                        <span className="text-xs text-gray-500 w-16">Ress:</span>
                        {item.ressources ? (
                          <span className={`ml-1 px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${getLevelColor(item.ressources)}`} title={formatLevel(item.ressources)}>
                            {formatLevel(item.ressources)}
                          </span>
                        ) : (
                          <span className="text-xs text-gray-400">-</span>
                        )}
                      </div>

                      {/* Activité */}
                      <div className="flex items-center">
                        <span className="text-xs text-gray-500 w-16">Activ:</span>
                        {item.activite ? (
                          <span className={`ml-1 px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${getLevelColor(item.activite)}`} title={formatLevel(item.activite)}>
                            {formatLevel(item.activite)}
                          </span>
                        ) : (
                          <span className="text-xs text-gray-400">-</span>
                        )}
                      </div>

                      {/* Motivation */}
                      <div className="flex items-center">
                        <span className="text-xs text-gray-500 w-16">Motiv:</span>
                        {item.motivation ? (
                          <span className={`ml-1 px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${getLevelColor(item.motivation)}`} title={formatLevel(item.motivation)}>
                            {formatLevel(item.motivation)}
                          </span>
                        ) : (
                          <span className="text-xs text-gray-400">-</span>
                        )}
                      </div>
                    </div>
                  </td>

                  {/* Optimized Impacts - Compact display of impact types */}
                  <td className="px-3 py-3 border-r border-gray-200 w-[22%]">
                    {item.fullDreadedEvent && item.fullDreadedEvent.impacts && item.fullDreadedEvent.impacts.length > 0 ? (
                      <div className="grid grid-cols-3 gap-1.5">
                        {/* Missions/Services */}
                        <div
                          className={`flex flex-col items-center justify-center p-1.5 rounded ${item.fullDreadedEvent.impacts.includes('missions_services') ? 'bg-blue-100 text-blue-800' : 'bg-gray-50 text-gray-400'}`}
                          title="Missions/Services"
                        >
                          <div className={`w-2.5 h-2.5 rounded-full mb-1 ${item.fullDreadedEvent.impacts.includes('missions_services') ? 'bg-blue-500' : 'bg-gray-300'}`}></div>
                          <span className="text-[10px] font-medium truncate w-full text-center">MS</span>
                        </div>

                        {/* Humain/Matériel */}
                        <div
                          className={`flex flex-col items-center justify-center p-1.5 rounded ${item.fullDreadedEvent.impacts.includes('humain_materiel_environnemental') ? 'bg-red-100 text-red-800' : 'bg-gray-50 text-gray-400'}`}
                          title="Humain/Matériel"
                        >
                          <div className={`w-2.5 h-2.5 rounded-full mb-1 ${item.fullDreadedEvent.impacts.includes('humain_materiel_environnemental') ? 'bg-red-500' : 'bg-gray-300'}`}></div>
                          <span className="text-[10px] font-medium truncate w-full text-center">HM</span>
                        </div>

                        {/* Gouvernance */}
                        <div
                          className={`flex flex-col items-center justify-center p-1.5 rounded ${item.fullDreadedEvent.impacts.includes('gouvernance') ? 'bg-purple-100 text-purple-800' : 'bg-gray-50 text-gray-400'}`}
                          title="Gouvernance"
                        >
                          <div className={`w-2.5 h-2.5 rounded-full mb-1 ${item.fullDreadedEvent.impacts.includes('gouvernance') ? 'bg-purple-500' : 'bg-gray-300'}`}></div>
                          <span className="text-[10px] font-medium truncate w-full text-center">GV</span>
                        </div>

                        {/* Financier */}
                        <div
                          className={`flex flex-col items-center justify-center p-1.5 rounded ${item.fullDreadedEvent.impacts.includes('financier') ? 'bg-green-100 text-green-800' : 'bg-gray-50 text-gray-400'}`}
                          title="Financier"
                        >
                          <div className={`w-2.5 h-2.5 rounded-full mb-1 ${item.fullDreadedEvent.impacts.includes('financier') ? 'bg-green-500' : 'bg-gray-300'}`}></div>
                          <span className="text-[10px] font-medium truncate w-full text-center">FI</span>
                        </div>

                        {/* Juridique */}
                        <div
                          className={`flex flex-col items-center justify-center p-1.5 rounded ${item.fullDreadedEvent.impacts.includes('juridique') ? 'bg-amber-100 text-amber-800' : 'bg-gray-50 text-gray-400'}`}
                          title="Juridique"
                        >
                          <div className={`w-2.5 h-2.5 rounded-full mb-1 ${item.fullDreadedEvent.impacts.includes('juridique') ? 'bg-amber-500' : 'bg-gray-300'}`}></div>
                          <span className="text-[10px] font-medium truncate w-full text-center">JU</span>
                        </div>

                        {/* Image/Confiance */}
                        <div
                          className={`flex flex-col items-center justify-center p-1.5 rounded ${item.fullDreadedEvent.impacts.includes('image_confiance') ? 'bg-pink-100 text-pink-800' : 'bg-gray-50 text-gray-400'}`}
                          title="Image/Confiance"
                        >
                          <div className={`w-2.5 h-2.5 rounded-full mb-1 ${item.fullDreadedEvent.impacts.includes('image_confiance') ? 'bg-pink-500' : 'bg-gray-300'}`}></div>
                          <span className="text-[10px] font-medium truncate w-full text-center">IC</span>
                        </div>
                      </div>
                    ) : (
                      <div className="flex items-center justify-center h-full">
                        <span className="text-xs text-gray-500 italic">Aucun impact</span>
                      </div>
                    )}
                  </td>

                  {/* Actions column removed */}
                </tr>
              ))
            )}
          </tbody>
          </table>
      </div>
    </div>
  );
};

export default SummaryTable;
