// backend/routes/attackPathsRoutes.js
const express = require('express');
const router = express.Router();
const { v4: uuidv4 } = require('uuid');
const fs = require('fs');
const path = require('path');
const { protect } = require('../middleware/authMiddleware');
const AnalysisComponent = require('../models/AnalysisComponent');
const Analysis = require('../models/Analysis');

// Path to the data directory (for fallback file storage)
const dataDir = path.join(__dirname, '../data');

// Ensure the data directory exists
if (!fs.existsSync(dataDir)) {
  fs.mkdirSync(dataDir, { recursive: true });
}

/**
 * Get attack paths for a specific analysis
 * GET /api/analyses/:analysisId/attack-paths
 */
router.get('/analyses/:analysisId/attack-paths', protect, async (req, res) => {

  try {
    const { analysisId } = req.params;

    // Try to get from database first
    const component = await AnalysisComponent.findOne({
      analysisId,
      componentType: 'attack-paths',
      isLatestVersion: true
    }).sort({ createdAt: -1 });

    if (component && component.data && component.data.attackPaths) {
      return res.json({
        success: true,
        data: component.data.attackPaths,
        message: 'Attack paths retrieved successfully'
      });
    }

    // Fallback to file if not in database
    const filePath = path.join(dataDir, `${analysisId}-attack-paths.json`);

    // Check if the file exists
    if (fs.existsSync(filePath)) {
      // Read the file
      const data = fs.readFileSync(filePath, 'utf8');
      const attackPaths = JSON.parse(data);

      return res.json({
        success: true,
        data: attackPaths,
        message: 'Attack paths retrieved from file'
      });
    }

    // No data found
    return res.json({
      success: true,
      data: [],
      message: 'No attack paths found'
    });
  } catch (error) {
    console.error('Error getting attack paths:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get attack paths',
      error: error.message
    });
  }
});

/**
 * Save attack paths for a specific analysis
 * POST /api/analyses/:analysisId/attack-paths
 */
router.post('/analyses/:analysisId/attack-paths', protect, async (req, res) => {

  try {

    const { analysisId } = req.params;
    const { attackPaths } = req.body;

    if (!Array.isArray(attackPaths)) {
      return res.status(400).json({
        success: false,
        message: 'Attack paths must be an array'
      });
    }

    // Process the attack paths
    const processedAttackPaths = attackPaths.map((path, index) => ({
      ...path,
      id: path.id || uuidv4(),
      referenceCode: path.referenceCode || `CA${String(index + 1).padStart(2, '0')}`,
      createdAt: path.createdAt || new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }));

    // Save to database using AnalysisComponent model
    const component = await AnalysisComponent.createNewVersion(
      analysisId,
      'attack-paths',
      { attackPaths: processedAttackPaths },
      req.user.id
    );

    // Update the analysis lastUpdatedBy fields
    await Analysis.findByIdAndUpdate(analysisId, {
      lastUpdatedBy: req.user.id,
      lastUpdatedByName: req.user.name
    });

    // Also save to file as backup
    try {
      const filePath = path.join(dataDir, `${analysisId}-attack-paths.json`);
      fs.writeFileSync(filePath, JSON.stringify(processedAttackPaths, null, 2));
    } catch (fileError) {
      console.warn('Could not save to file (non-critical):', fileError.message);
    }

    console.log(`Successfully saved ${processedAttackPaths.length} attack paths for analysis ${analysisId}`);

    res.status(200).json({
      success: true,
      message: 'Attack paths saved successfully',
      data: processedAttackPaths
    });
  } catch (error) {
    console.error('Error saving attack paths:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to save attack paths',
      error: error.message
    });
  }
});



// Public route for testing (no authentication required)
router.post('/public-attack-paths', (req, res) => {
  try {
    console.log('==========================================');
    console.log('PUBLIC ATTACK PATHS ENDPOINT CALLED');
    console.log('==========================================');
    console.log('Request URL:', req.originalUrl);
    console.log('Request method:', req.method);
    console.log('Request headers:', req.headers);
    console.log('Request body:', JSON.stringify(req.body, null, 2));
    console.log('==========================================');

    const { attackPaths } = req.body;

    if (!Array.isArray(attackPaths)) {
      return res.status(400).json({
        success: false,
        message: 'Attack paths must be an array'
      });
    }

    // Process the attack paths
    const processedAttackPaths = attackPaths.map((path, index) => ({
      ...path,
      id: path.id || uuidv4(),
      referenceCode: path.referenceCode || `CA${String(index + 1).padStart(2, '0')}`,
      createdAt: path.createdAt || new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }));

    res.json({
      success: true,
      message: 'Attack paths saved successfully (public route)',
      data: processedAttackPaths
    });
  } catch (error) {
    console.error('Error in public attack paths route:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to save attack paths',
      error: error.message
    });
  }
});

/**
 * Get diagram state for a specific attack path
 * GET /api/analyses/:analysisId/attack-paths/:attackPathId/diagram
 */
router.get('/analyses/:analysisId/attack-paths/:attackPathId/diagram', protect, async (req, res) => {
  try {
    const { analysisId, attackPathId } = req.params;

    // Try to get from database first
    const component = await AnalysisComponent.findOne({
      analysisId,
      componentType: 'attack-path-diagrams',
      'data.attackPathId': attackPathId,
      isLatestVersion: true
    }).sort({ createdAt: -1 });

    if (component && component.data && component.data.diagramState) {
      return res.json({
        success: true,
        data: component.data.diagramState,
        message: 'Diagram state retrieved successfully'
      });
    }

    // Fallback to file if not in database
    const filePath = path.join(dataDir, `${analysisId}-${attackPathId}-diagram.json`);

    // Check if the file exists
    if (fs.existsSync(filePath)) {
      // Read the file
      const data = fs.readFileSync(filePath, 'utf8');
      const diagramState = JSON.parse(data);

      return res.json({
        success: true,
        data: diagramState,
        message: 'Diagram state retrieved from file'
      });
    }

    // No data found
    return res.json({
      success: false,
      message: 'No diagram state found for this attack path'
    });
  } catch (error) {
    console.error('Error getting diagram state:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get diagram state',
      error: error.message
    });
  }
});

/**
 * Save diagram state for a specific attack path
 * POST /api/analyses/:analysisId/attack-paths/:attackPathId/diagram
 */
router.post('/analyses/:analysisId/attack-paths/:attackPathId/diagram', protect, async (req, res) => {
  try {
    const { analysisId, attackPathId } = req.params;
    const { diagramState } = req.body;

    if (!diagramState || !diagramState.nodes || !diagramState.edges) {
      return res.status(400).json({
        success: false,
        message: 'Invalid diagram state data'
      });
    }

    // Save to database using AnalysisComponent model
    const component = await AnalysisComponent.createNewVersion(
      analysisId,
      'attack-path-diagrams',
      {
        attackPathId,
        diagramState,
        updatedAt: new Date().toISOString()
      },
      req.user.id
    );

    // Update the analysis lastUpdatedBy fields
    await Analysis.findByIdAndUpdate(analysisId, {
      lastUpdatedBy: req.user.id,
      lastUpdatedByName: req.user.name
    });

    // Also save to file as backup
    try {
      const filePath = path.join(dataDir, `${analysisId}-${attackPathId}-diagram.json`);
      fs.writeFileSync(filePath, JSON.stringify(diagramState, null, 2));
    } catch (fileError) {
      console.warn('Could not save diagram to file (non-critical):', fileError.message);
    }

    console.log(`Successfully saved diagram state for attack path ${attackPathId} in analysis ${analysisId}`);

    res.status(200).json({
      success: true,
      message: 'Diagram state saved successfully',
      data: diagramState
    });
  } catch (error) {
    console.error('Error saving diagram state:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to save diagram state',
      error: error.message
    });
  }
});

/**
 * Update specific attack path
 * PUT /api/analyses/:analysisId/attack-paths/:pathId
 */
router.put('/analyses/:analysisId/attack-paths/:pathId', protect, async (req, res) => {
  try {
    const { analysisId, pathId } = req.params;
    const pathData = req.body;

    // Get existing attack paths
    const component = await AnalysisComponent.findOne({
      analysisId,
      componentType: 'attack-paths',
      isLatestVersion: true
    }).sort({ createdAt: -1 });

    if (!component || !component.data || !component.data.attackPaths) {
      return res.status(404).json({
        success: false,
        message: 'Attack paths not found'
      });
    }

    // Find and update the specific attack path
    const attackPaths = component.data.attackPaths;
    const pathIndex = attackPaths.findIndex(path => path.id === pathId);

    if (pathIndex === -1) {
      return res.status(404).json({
        success: false,
        message: 'Attack path not found'
      });
    }

    // Update the attack path
    attackPaths[pathIndex] = {
      ...attackPaths[pathIndex],
      ...pathData,
      id: pathId, // Ensure ID doesn't change
      updatedAt: new Date().toISOString()
    };

    // Save updated attack paths
    const newComponent = await AnalysisComponent.createNewVersion(
      analysisId,
      'attack-paths',
      { attackPaths },
      req.user.id
    );

    // Update the analysis lastUpdatedBy fields
    await Analysis.findByIdAndUpdate(analysisId, {
      lastUpdatedBy: req.user.id,
      lastUpdatedByName: req.user.name
    });

    res.json({
      success: true,
      data: attackPaths[pathIndex],
      message: 'Attack path updated successfully'
    });
  } catch (error) {
    console.error('Error updating attack path:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update attack path',
      error: error.message
    });
  }
});

/**
 * Delete specific attack path
 * DELETE /api/analyses/:analysisId/attack-paths/:pathId
 */
router.delete('/analyses/:analysisId/attack-paths/:pathId', protect, async (req, res) => {
  try {
    const { analysisId, pathId } = req.params;

    // Get existing attack paths
    const component = await AnalysisComponent.findOne({
      analysisId,
      componentType: 'attack-paths',
      isLatestVersion: true
    }).sort({ createdAt: -1 });

    if (!component || !component.data || !component.data.attackPaths) {
      return res.status(404).json({
        success: false,
        message: 'Attack paths not found'
      });
    }

    // Filter out the attack path to delete
    const attackPaths = component.data.attackPaths.filter(path => path.id !== pathId);

    if (attackPaths.length === component.data.attackPaths.length) {
      return res.status(404).json({
        success: false,
        message: 'Attack path not found'
      });
    }

    // Save updated attack paths
    const newComponent = await AnalysisComponent.createNewVersion(
      analysisId,
      'attack-paths',
      { attackPaths },
      req.user.id
    );

    // Update the analysis lastUpdatedBy fields
    await Analysis.findByIdAndUpdate(analysisId, {
      lastUpdatedBy: req.user.id,
      lastUpdatedByName: req.user.name
    });

    res.json({
      success: true,
      message: 'Attack path deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting attack path:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete attack path',
      error: error.message
    });
  }
});

/**
 * Generate attack sequences from strategic scenarios
 * POST /api/analyses/:analysisId/attack-paths/generate
 */
router.post('/analyses/:analysisId/attack-paths/generate', protect, async (req, res) => {
  try {
    const { analysisId } = req.params;
    const { strategicScenarios } = req.body;

    if (!Array.isArray(strategicScenarios)) {
      return res.status(400).json({
        success: false,
        message: 'Strategic scenarios must be an array'
      });
    }

    // Generate attack paths from strategic scenarios
    const generatedAttackPaths = strategicScenarios.map((scenario, index) => ({
      id: uuidv4(),
      referenceCode: `CA${String(index + 1).padStart(2, '0')}`,
      name: `Chemin d'attaque ${index + 1}`,
      sourceRiskName: scenario.sourceRisque || 'Source non définie',
      objectifVise: scenario.objectifVise || 'Objectif non défini',
      dreadedEventName: scenario.dreadedEventName || 'Événement redouté non défini',
      businessValueName: scenario.businessValueName || 'Valeur métier non définie',
      dreadedEventId: scenario.dreadedEventId,
      businessValueId: scenario.businessValueId,
      stakeholders: scenario.partiesPrenantes || [],
      description: `Chemin d'attaque généré à partir du scénario stratégique: ${scenario.sourceRisque} → ${scenario.objectifVise}`,
      complexity: 'Moyen',
      likelihood: 'Moyen',
      impact: 'Moyen',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      generatedFromScenario: true,
      scenarioId: scenario.id
    }));

    res.json({
      success: true,
      data: generatedAttackPaths,
      message: 'Attack paths generated successfully from strategic scenarios'
    });
  } catch (error) {
    console.error('Error generating attack sequences:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to generate attack sequences',
      error: error.message
    });
  }
});

/**
 * Analyze attack path complexity
 * GET /api/analyses/:analysisId/attack-paths/:pathId/complexity
 */
router.get('/analyses/:analysisId/attack-paths/:pathId/complexity', protect, async (req, res) => {
  try {
    const { analysisId, pathId } = req.params;

    // Get the attack path first
    const component = await AnalysisComponent.findOne({
      analysisId,
      componentType: 'attack-paths',
      isLatestVersion: true
    }).sort({ createdAt: -1 });

    if (!component || !component.data || !component.data.attackPaths) {
      return res.status(404).json({
        success: false,
        message: 'Attack paths not found'
      });
    }

    const attackPath = component.data.attackPaths.find(path => path.id === pathId);
    if (!attackPath) {
      return res.status(404).json({
        success: false,
        message: 'Attack path not found'
      });
    }

    // Analyze complexity based on various factors
    const complexityAnalysis = {
      pathId: pathId,
      pathName: attackPath.name || 'Chemin d\'attaque',
      referenceCode: attackPath.referenceCode,
      complexity: {
        level: attackPath.complexity || 'Moyen',
        factors: {
          stakeholderCount: attackPath.stakeholders ? attackPath.stakeholders.length : 0,
          technicalComplexity: 'Moyen',
          resourceRequirements: 'Moyen',
          detectionDifficulty: 'Moyen'
        }
      },
      risk: {
        likelihood: attackPath.likelihood || 'Moyen',
        impact: attackPath.impact || 'Moyen',
        overallRisk: 'Moyen'
      },
      recommendations: [
        'Surveiller les activités suspectes',
        'Mettre en place des contrôles de sécurité appropriés',
        'Former les utilisateurs aux bonnes pratiques'
      ],
      analysisDate: new Date().toISOString()
    };

    res.json({
      success: true,
      data: complexityAnalysis,
      message: 'Attack path complexity analyzed successfully'
    });
  } catch (error) {
    console.error('Error analyzing attack path complexity:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to analyze attack path complexity',
      error: error.message
    });
  }
});

module.exports = router;
