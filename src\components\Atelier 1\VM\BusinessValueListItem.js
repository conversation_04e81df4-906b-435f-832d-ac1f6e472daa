// BusinessValueListItem.js
import React from 'react';
import { useTranslation } from 'react-i18next';
import { SECURITY_PILLARS } from '../../../constants';
import { ChevronDown, ChevronRight, Edit2, Trash2, Plus, Save, X, Package, Server, Users, MapPin, FileText, Code, Network, Home } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';

// Helper to get icon based on asset type
const AssetTypeIcon = ({ type }) => {
  switch (type) {
    case 'organisation': return <Users size={16} className="text-orange-500" />;
    case 'locaux': return <Home size={16} className="text-pink-500" />;
    case 'equipements': return <Server size={16} className="text-purple-500" />;
    case 'reseaux': return <Network size={16} className="text-indigo-500" />;
    case 'logiciels': return <Code size={16} className="text-green-500" />;
    default: return <Package size={16} className="text-gray-500" />;
  }
};

const BusinessValueListItem = ({
  value,
  expandedValue,
  setExpandedValue,
  editingValue,
  setEditingValue,
  editValue,
  setEditValue,
  editDescription,
  setEditDescription,
  handleStartEdit,
  handleEditSave,
  handleCancelEdit,
  handleRemoveBusinessValue,
  handleToggleSecurityPillar,
  handleAddSupportAsset,
  newSupportAssetName,
  setNewSupportAssetName,
  newSupportAssetDescription,
  setNewSupportAssetDescription,
  newSupportAssetType,
  setNewSupportAssetType,
  newSupportAssetOwner,
  setNewSupportAssetOwner,
  newSupportAssetLocation,
  setNewSupportAssetLocation,
  // CPE fields
  newSupportAssetVendor,
  setNewSupportAssetVendor,
  newSupportAssetProduct,
  setNewSupportAssetProduct,
  newSupportAssetVersion,
  setNewSupportAssetVersion,
  discoveredCPE,
  cpeConfidence,
  cpeSearching,
  assetTypes = [],
  handleRemoveSupportAsset,
  editingAsset,
  handleStartEditSupportAsset,
  handleCancelEditSupportAsset,
  handleEditSupportAssetChange,
  handleEditSupportAssetSave
}) => {
  const { t } = useTranslation();
  const isExpanded = expandedValue === value.id;
  const isEditing = editingValue === value.id;

  // Check if a specific support asset is being edited
  const isAssetEditing = (assetId) => editingAsset && editingAsset.asset.id === assetId && editingAsset.businessValueId === value.id;

  // Ensure assetTypes is an array even if prop is somehow undefined initially
  const safeAssetTypes = Array.isArray(assetTypes) ? assetTypes : [];

  return (
    <motion.div
      className="mb-4 overflow-hidden rounded-md border bg-white shadow-sm hover:shadow-md transition-shadow duration-200"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      whileHover={{ scale: 1.01 }}
      layout
    >
      <div className="p-4 flex justify-between items-center">
        {isEditing ? (
          <div className="flex-grow flex flex-col gap-2">
            <input
              type="text"
              className="w-full p-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none"
              value={editValue}
              onChange={(e) => setEditValue(e.target.value)}
              placeholder={t('businessValues.placeholders.businessValueName')}
              autoFocus
            />
            <textarea
              className="w-full p-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none text-sm"
              value={editDescription}
              onChange={(e) => setEditDescription(e.target.value)}
              placeholder={t('businessValues.placeholders.businessValueDescription')}
              rows="2"
            />
            <div className="flex justify-end gap-2 mt-1">
                <motion.button
                  onClick={() => handleEditSave(value.id)}
                  className="flex items-center gap-1 bg-green-500 hover:bg-green-600 text-white px-3 py-1 rounded-md text-sm"
                  disabled={!editValue.trim()}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  transition={{ type: 'spring', stiffness: 400, damping: 17 }}
                >
                  <Save size={16} />
                  <span>{t('businessValues.buttons.save')}</span>
                </motion.button>
                <motion.button
                  onClick={handleCancelEdit}
                  className="flex items-center gap-1 bg-gray-200 hover:bg-gray-300 text-gray-700 px-3 py-1 rounded-md text-sm"
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  transition={{ type: 'spring', stiffness: 400, damping: 17 }}
                >
                  <X size={16} />
                  <span>{t('businessValues.buttons.cancel')}</span>
                </motion.button>
            </div>
          </div>
        ) : (
          <>
            <div className="flex items-center flex-grow mr-4 overflow-hidden">
              <button
                onClick={() => setExpandedValue(isExpanded ? null : value.id)}
                className="mr-2 text-gray-500 hover:text-gray-700 p-1 rounded-full hover:bg-gray-100 flex-shrink-0"
                aria-label={isExpanded ? t('businessValues.buttons.collapse') : t('businessValues.buttons.expand')}
              >
                {isExpanded ? <ChevronDown size={20} /> : <ChevronRight size={20} />}
              </button>
              <div className="font-medium flex-grow">
                <span className="mr-2 px-2 py-1 text-xs rounded-full text-blue-600 border border-blue-200 bg-blue-50 align-middle">
                  {value.shortId}
                </span>
                <span className="text-gray-800 align-middle mr-2">{value.name}</span>
                {value.description && (
                    <p className="text-sm text-gray-500 mt-1 truncate" title={value.description}>
                      {value.description}
                    </p>
                )}
              </div>
            </div>
            <div className="flex gap-2 flex-shrink-0">
              <motion.button
                onClick={() => handleStartEdit(value)}
                className="flex items-center gap-1 bg-blue-50 hover:bg-blue-100 text-blue-700 border border-blue-200 px-3 py-1 rounded-md text-sm"
                title={t('businessValues.buttons.edit')}
                whileHover={{ scale: 1.05, backgroundColor: '#dbeafe' }}
                whileTap={{ scale: 0.95 }}
                transition={{ type: 'spring', stiffness: 400, damping: 17 }}
              >
                <Edit2 size={16} />
                <span className="hidden sm:inline">{t('businessValues.buttons.edit')}</span>
              </motion.button>
              <motion.button
                onClick={() => {
                    if (window.confirm(t('businessValues.confirmations.deleteValue', { name: value.name }))) {
                        handleRemoveBusinessValue(value.id);
                    }
                 }}
                className="flex items-center gap-1 bg-red-50 hover:bg-red-100 text-red-700 border border-red-200 px-3 py-1 rounded-md text-sm"
                title={t('businessValues.buttons.delete')}
                whileHover={{ scale: 1.05, backgroundColor: '#fee2e2' }}
                whileTap={{ scale: 0.95 }}
                transition={{ type: 'spring', stiffness: 400, damping: 17 }}
              >
                <Trash2 size={16} />
                <span className="hidden sm:inline">{t('businessValues.buttons.delete')}</span>
              </motion.button>
            </div>
          </>
        )}
      </div>

      <AnimatePresence>
        {isExpanded && (
          <motion.div
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: "auto", opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            transition={{ duration: 0.3 }}
            className="overflow-hidden"
          >
            <div className="border-t p-4 bg-gray-50">
              {/* Piliers de sécurité */}
              <div className="mb-6">
                <h4 className="font-medium mb-3 text-gray-700 flex items-center">
                  <span className="mr-2">{t('businessValues.sections.securityPillars')}</span>
                  <span className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded-full border border-gray-200">
                    {((value.securityPillars || []).length || 0)} {t('businessValues.status.selected')}
                  </span>
                </h4>
                <div className="flex flex-wrap gap-2">
                  {SECURITY_PILLARS.map(pillar => {
                    const isSelected = (value.securityPillars || []).includes(pillar.id);
                    return (
                      <button
                        key={pillar.id}
                        className={`px-3 py-1 rounded-full text-sm transition-all duration-200 ${
                          isSelected
                            ? 'text-white border-transparent shadow-sm'
                            : 'bg-white text-gray-700 border border-gray-300 hover:border-gray-400'
                        }`}
                        style={{ backgroundColor: isSelected ? pillar.color : '' }}
                        onClick={() => handleToggleSecurityPillar(value.id, pillar.id)}
                      >
                        {pillar.name}
                      </button>
                    );
                  })}
                </div>
              </div>

              {/* Support Assets Section - UPDATED */}
              <div>
                <h4 className="font-medium mb-3 text-gray-700 flex items-center">
                  <span className="mr-2">{t('businessValues.sections.supportAssets')}</span>
                  <span className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded-full border border-gray-200">
                    {((value.supportAssets || []).length || 0)} {t('businessValues.status.assets')}
                  </span>
                </h4>

                {/* --- Enhanced Add Support Asset Form with CPE Discovery --- */}
                <div className="mb-4 bg-white rounded-md p-4 border border-gray-200 shadow-sm">
                  <h5 className="text-sm font-medium text-gray-600 mb-3 flex items-center">
                    <Package size={16} className="mr-2 text-blue-500" />
                    {t('businessValues.sections.addSupportAsset')}
                  </h5>

                  {/* Basic Information */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    {/* Name */}
                    <input
                      type="text"
                      className="w-full p-2 border rounded-md text-sm focus:ring-1 focus:ring-blue-500 focus:border-blue-500 outline-none"
                      placeholder={t('businessValues.placeholders.supportAssetName')}
                      value={newSupportAssetName}
                      onChange={(e) => setNewSupportAssetName(e.target.value)}
                    />
                    {/* Type (Dropdown) */}
                    <select
                      className="w-full p-2 border rounded-md text-sm bg-white focus:ring-1 focus:ring-blue-500 focus:border-blue-500 outline-none"
                      value={newSupportAssetType}
                      onChange={(e) => setNewSupportAssetType(e.target.value)}
                    >
                      {safeAssetTypes.map(type => (
                        <option key={type.value} value={type.value}>{type.label}</option>
                      ))}
                    </select>
                  </div>

                  {/* CPE Discovery Section */}
                  <div className="mb-4 p-3 bg-blue-50 rounded-md border border-blue-200">
                    <h6 className="text-sm font-medium text-blue-800 mb-2 flex items-center">
                      <Code size={14} className="mr-2" />
                      Identification technique (pour l'analyse des menaces)
                    </h6>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-3 mb-3">
                      {/* Vendor */}
                      <input
                        type="text"
                        className="w-full p-2 border rounded-md text-sm focus:ring-1 focus:ring-blue-500 focus:border-blue-500 outline-none bg-white"
                        placeholder="Éditeur (ex: Apache, Microsoft)"
                        value={newSupportAssetVendor}
                        onChange={(e) => setNewSupportAssetVendor(e.target.value)}
                      />
                      {/* Product */}
                      <input
                        type="text"
                        className="w-full p-2 border rounded-md text-sm focus:ring-1 focus:ring-blue-500 focus:border-blue-500 outline-none bg-white"
                        placeholder="Produit (ex: Log4j, Windows)"
                        value={newSupportAssetProduct}
                        onChange={(e) => setNewSupportAssetProduct(e.target.value)}
                      />
                      {/* Version */}
                      <input
                        type="text"
                        className="w-full p-2 border rounded-md text-sm focus:ring-1 focus:ring-blue-500 focus:border-blue-500 outline-none bg-white"
                        placeholder="Version (ex: 2.14.0)"
                        value={newSupportAssetVersion}
                        onChange={(e) => setNewSupportAssetVersion(e.target.value)}
                      />
                    </div>

                    {/* CPE Discovery Result */}
                    {(newSupportAssetVendor || newSupportAssetProduct) && (
                      <div className="mt-3 p-2 bg-white rounded border">
                        <div className="flex items-center justify-between mb-2">
                          <span className="text-xs font-medium text-gray-600">CPE Identifiant:</span>
                          {cpeSearching && (
                            <div className="flex items-center text-xs text-blue-600">
                              <div className="animate-spin rounded-full h-3 w-3 border-b border-blue-600 mr-1"></div>
                              Recherche...
                            </div>
                          )}
                          {cpeConfidence && (
                            <span className={`text-xs px-2 py-1 rounded ${
                              cpeConfidence === 'High' ? 'bg-green-100 text-green-800' :
                              cpeConfidence === 'Generated' ? 'bg-yellow-100 text-yellow-800' :
                              'bg-gray-100 text-gray-800'
                            }`}>
                              {cpeConfidence === 'High' ? 'Trouvé' :
                               cpeConfidence === 'Generated' ? 'Généré' : cpeConfidence}
                            </span>
                          )}
                        </div>
                        {discoveredCPE && (
                          <code className="text-xs bg-gray-100 p-1 rounded block font-mono text-gray-700 break-all">
                            {discoveredCPE}
                          </code>
                        )}
                      </div>
                    )}
                  </div>

                  {/* Additional Information */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-3">
                    {/* Owner */}
                    <input
                      type="text"
                      className="w-full p-2 border rounded-md text-sm focus:ring-1 focus:ring-blue-500 focus:border-blue-500 outline-none"
                      placeholder={t('businessValues.placeholders.owner')}
                      value={newSupportAssetOwner}
                      onChange={(e) => setNewSupportAssetOwner(e.target.value)}
                    />
                     {/* Location */}
                    <input
                      type="text"
                      className="w-full p-2 border rounded-md text-sm focus:ring-1 focus:ring-blue-500 focus:border-blue-500 outline-none"
                      placeholder={t('businessValues.placeholders.location')}
                      value={newSupportAssetLocation}
                      onChange={(e) => setNewSupportAssetLocation(e.target.value)}
                    />
                    {/* Description (col-span-2) */}
                    <textarea
                      className="w-full p-2 border rounded-md text-sm md:col-span-2 focus:ring-1 focus:ring-blue-500 focus:border-blue-500 outline-none"
                      placeholder={t('businessValues.placeholders.supportAssetDescription')}
                      rows="2"
                      value={newSupportAssetDescription}
                      onChange={(e) => setNewSupportAssetDescription(e.target.value)}
                    />
                  </div>
                  {/* Add Button */}
                   <div className="flex justify-end">
                        <button
                          className="bg-green-500 hover:bg-green-600 text-white flex items-center gap-2 px-4 py-2 rounded-md text-sm disabled:opacity-50 disabled:cursor-not-allowed"
                          onClick={() => handleAddSupportAsset(value.id)}
                          disabled={!(newSupportAssetName || '').trim()}
                        >
                          <Plus size={18} />
                          <span>{t('businessValues.buttons.addAsset')}</span>
                        </button>
                   </div>
                </div>

                {/* --- List of Support Assets - UPDATED --- */}
                {(!value.supportAssets || value.supportAssets.length === 0) ? (
                  <div className="text-gray-500 p-4 border border-dashed border-gray-300 rounded-md bg-white text-center">
                    {t('businessValues.empty.noAssets')}
                  </div>
                ) : (
                  <div className="bg-white rounded-md border border-gray-200 divide-y divide-gray-100 shadow-sm">
                    {(value.supportAssets || []).map(asset => (
                      <motion.div
                        key={asset.id} // Use asset ID as key
                        initial={{ opacity: 0 }} // simpler animation
                        animate={{ opacity: 1 }}
                        className="p-3 hover:bg-gray-50"
                      >
                        {isAssetEditing(asset.id) ? (
                          // --- Inline Edit Form for Asset ---
                          <div className="space-y-3">
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                                {/* Edit Name */}
                                <input type="text" value={editingAsset.asset.name} onChange={(e) => handleEditSupportAssetChange('name', e.target.value)} placeholder={t('businessValues.fields.name')} className="w-full p-1 border rounded text-sm" />
                                {/* Edit Type */}
                                <select value={editingAsset.asset.type} onChange={(e) => handleEditSupportAssetChange('type', e.target.value)} className="w-full p-1 border rounded text-sm bg-white">
                                    {safeAssetTypes.map(type => <option key={type.value} value={type.value}>{type.label}</option>)}
                                </select>
                                {/* Edit Owner */}
                                <input type="text" value={editingAsset.asset.owner || ''} onChange={(e) => handleEditSupportAssetChange('owner', e.target.value)} placeholder={t('businessValues.fields.owner')} className="w-full p-1 border rounded text-sm" />
                                {/* Edit Location */}
                                <input type="text" value={editingAsset.asset.location || ''} onChange={(e) => handleEditSupportAssetChange('location', e.target.value)} placeholder={t('businessValues.fields.location')} className="w-full p-1 border rounded text-sm" />
                            </div>
                             {/* Edit Description */}
                             <textarea value={editingAsset.asset.description || ''} onChange={(e) => handleEditSupportAssetChange('description', e.target.value)} placeholder={t('businessValues.fields.description')} rows="2" className="w-full p-1 border rounded text-sm" />
                             {/* Edit Actions */}
                             <div className="flex justify-end gap-2">
                                 <button onClick={handleEditSupportAssetSave}
                                         className="bg-green-500 text-white px-2 py-1 rounded text-xs disabled:opacity-50"
                                         disabled={!(editingAsset?.asset?.name || '').trim()}
                                 >
                                     <Save size={14} className="inline mr-1"/>{t('businessValues.buttons.save')}
                                 </button>
                                 <button onClick={handleCancelEditSupportAsset} className="bg-gray-200 text-gray-700 px-2 py-1 rounded text-xs"><X size={14} className="inline mr-1"/>{t('businessValues.buttons.cancel')}</button>
                             </div>
                          </div>
                        ) : (
                          // --- Display Asset ---
                          <div className="flex justify-between items-start gap-2">
                            <div className="flex-grow">
                                <div className="flex items-center mb-1">
                                    <span className="mr-2 px-1.5 py-0.5 text-xs rounded-full text-blue-700 bg-blue-100 border border-blue-200 font-mono">
                                        {asset.shortId}
                                    </span>
                                    <span className="font-medium text-gray-800 mr-2">{asset.name}</span>
                                    <span className="text-xs text-gray-500 flex items-center gap-1">
                                        <AssetTypeIcon type={asset.type} />
                                        {safeAssetTypes.find(t => t.value === asset.type)?.label || asset.type}
                                    </span>
                                </div>
                                {asset.description && <p className="text-sm text-gray-600 mb-1 ml-1 pl-2 border-l-2 border-gray-200">{asset.description}</p>}

                                {/* CPE Information */}
                                {asset.cpe && (
                                  <div className="mt-2 p-2 bg-blue-50 rounded border border-blue-200">
                                    <div className="flex items-center justify-between mb-1">
                                      <span className="text-xs font-medium text-blue-800 flex items-center">
                                        <Code size={12} className="mr-1" />
                                        Identifiant technique
                                      </span>
                                      {asset.cpeConfidence && (
                                        <span className={`text-xs px-1.5 py-0.5 rounded ${
                                          asset.cpeConfidence === 'High' ? 'bg-green-100 text-green-700' :
                                          asset.cpeConfidence === 'Generated' ? 'bg-yellow-100 text-yellow-700' :
                                          'bg-gray-100 text-gray-700'
                                        }`}>
                                          {asset.cpeConfidence === 'High' ? 'Vérifié' :
                                           asset.cpeConfidence === 'Generated' ? 'Généré' : asset.cpeConfidence}
                                        </span>
                                      )}
                                    </div>
                                    {(asset.vendor || asset.product || asset.version) && (
                                      <div className="text-xs text-blue-700 mb-1">
                                        {asset.vendor && <span className="mr-2">📦 {asset.vendor}</span>}
                                        {asset.product && <span className="mr-2">🔧 {asset.product}</span>}
                                        {asset.version && <span>📋 v{asset.version}</span>}
                                      </div>
                                    )}
                                    <code className="text-xs bg-white p-1 rounded block font-mono text-gray-600 break-all border">
                                      {asset.cpe}
                                    </code>
                                  </div>
                                )}

                                <div className="text-xs text-gray-500 ml-1 flex flex-wrap gap-x-3 gap-y-1 mt-2">
                                    {asset.owner && <span className="flex items-center"><Users size={12} className="mr-1"/> {asset.owner}</span>}
                                    {asset.location && <span className="flex items-center"><MapPin size={12} className="mr-1"/> {asset.location}</span>}
                                </div>
                            </div>
                            {/* Asset Actions */}
                            <div className="flex flex-col sm:flex-row gap-1 flex-shrink-0">
                                <button
                                  onClick={() => handleStartEditSupportAsset(value.id, asset)}
                                  className="text-blue-600 hover:text-blue-800 hover:bg-blue-50 p-1 rounded text-xs"
                                  title={t('businessValues.buttons.edit')}
                                >
                                  <Edit2 size={14} />
                                </button>
                                <button
                                  onClick={() => {
                                      if (window.confirm(t('businessValues.confirmations.deleteAsset', { name: asset.name }))) {
                                          handleRemoveSupportAsset(value.id, asset.id);
                                      }
                                  }}
                                  className="text-red-500 hover:text-red-700 hover:bg-red-50 p-1 rounded text-xs"
                                  title={t('businessValues.buttons.delete')}
                                >
                                  <Trash2 size={14} />
                                </button>
                            </div>
                          </div>
                        )}
                      </motion.div>
                    ))}
                  </div>
                )}
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </motion.div>
  );
};

export default BusinessValueListItem;