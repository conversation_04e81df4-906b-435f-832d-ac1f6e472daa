// controllers/riskTreatmentController.js
const AnalysisComponent = require('../models/AnalysisComponent');
const Analysis = require('../models/Analysis');
const mongoose = require('mongoose');

/**
 * Helper to check if user has access to the analysis
 */
const checkAnalysisAccess = async (analysisId, userId, userRole, userCompanyId) => {
  try {
    const analysis = await Analysis.findById(analysisId);

    if (!analysis) {
      return {
        success: false,
        status: 404,
        message: 'Analysis not found'
      };
    }

    // SuperAdmin has access to all analyses
    if (userRole === 'superadmin') {
      return { success: true, analysis };
    }

    // Other users can only access analyses from their company
    if (analysis.companyId.toString() !== userCompanyId.toString()) {
      return {
        success: false,
        status: 403,
        message: 'You do not have permission to access this analysis'
      };
    }

    return { success: true, analysis };
  } catch (error) {
    return {
      success: false,
      status: 500,
      message: 'Error checking analysis access',
      error
    };
  }
};

/**
 * Transform component for response
 */
const transformComponentForResponse = (component) => {
  if (!component) return null;

  const plainComponent = component.toObject ? component.toObject() : { ...component };

  return {
    id: plainComponent._id.toString(),
    analysisId: plainComponent.analysisId.toString(),
    componentType: plainComponent.componentType,
    data: plainComponent.data,
    version: plainComponent.version,
    createdBy: plainComponent.createdBy.toString(),
    updatedBy: plainComponent.updatedBy ? plainComponent.updatedBy.toString() : null,
    createdAt: plainComponent.createdAt.toISOString(),
    updatedAt: plainComponent.updatedAt.toISOString()
  };
};

/**
 * @desc    Get risk treatment component for an analysis
 * @route   GET /api/analyses/:analysisId/risk-treatment
 * @access  Private
 */
exports.getRiskTreatment = async (req, res) => {
  try {
    const { analysisId } = req.params;
    const componentType = 'risk-treatment';

    // Check if user has access to this analysis
    const accessCheck = await checkAnalysisAccess(
      analysisId,
      req.user.id,
      req.user.role,
      req.user.companyId
    );

    if (!accessCheck.success) {
      return res.status(accessCheck.status).json({
        success: false,
        message: accessCheck.message
      });
    }

    // Get the component
    const component = await AnalysisComponent.findOne({
      analysisId,
      componentType
    }).sort({ version: -1 }); // Get the latest version

    if (!component) {
      return res.status(200).json({
        success: true,
        data: {
          riskTreatments: []
        }
      });
    }

    res.status(200).json({
      success: true,
      data: transformComponentForResponse(component)
    });
  } catch (error) {
    console.error(`Get risk treatment error:`, error);
    res.status(500).json({
      success: false,
      message: 'Error retrieving risk treatment data',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * @desc    Save risk treatment component for an analysis
 * @route   POST /api/analyses/:analysisId/risk-treatment
 * @access  Private
 */
exports.saveRiskTreatment = async (req, res) => {
  try {
    const { analysisId } = req.params;
    const componentType = 'risk-treatment';
    const { data } = req.body;

    // Check if user has access to this analysis
    const accessCheck = await checkAnalysisAccess(
      analysisId,
      req.user.id,
      req.user.role,
      req.user.companyId
    );

    if (!accessCheck.success) {
      return res.status(accessCheck.status).json({
        success: false,
        message: accessCheck.message
      });
    }

    // Get the latest version of this component
    const latestComponent = await AnalysisComponent.findOne({
      analysisId,
      componentType
    }).sort({ version: -1 });

    // Calculate the new version number
    const newVersion = latestComponent ? latestComponent.version + 1 : 1;

    // Create a new component version
    const newComponent = await AnalysisComponent.create({
      analysisId,
      componentType,
      data,
      version: newVersion,
      createdBy: req.user.id,
      updatedBy: req.user.id
    });

    res.status(201).json({
      success: true,
      data: transformComponentForResponse(newComponent)
    });
  } catch (error) {
    console.error(`Save risk treatment error:`, error);
    res.status(500).json({
      success: false,
      message: 'Error saving risk treatment data',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};
