// src/api/apiClient.js
import 'isomorphic-fetch';

import {
  getAccessToken,
  getRefreshToken,
  updateAccessToken,
  clearAuthData
} from '../utils/tokenStorage';

// Base URL from environment variables
const BASE_URL = process.env.REACT_APP_API_URL || '/api';

// Flag to prevent infinite refresh token loops
let isRefreshing = false;
let failedQueue = [];

// Process queue of failed requests
const processQueue = (error, token = null) => {
  failedQueue.forEach(prom => {
    if (error) {
      prom.reject(error);
    } else {
      prom.resolve(token);
    }
  });

  failedQueue = [];
};

// Utility function to safely stringify data for API requests
const safeStringify = (data, url) => {
  try {
    // Use a replacer function to handle circular references
    const seen = new WeakSet();
    return JSON.stringify(data, (key, value) => {
      // Skip DOM nodes, React elements, and circular references
      if (key === 'window' || key === 'document' || key === 'global' ||
          (typeof value === 'object' && value !== null)) {
        // Check for circular references
        if (seen.has(value)) {
          return undefined; // Skip circular reference
        }

        // Check for DOM nodes and React elements
        if (value && (value.nodeType !== undefined ||
            value._reactInternalFiber !== undefined ||
            value._reactInternals !== undefined ||
            key.startsWith('__react') ||
            (typeof HTMLElement === 'function' && value instanceof HTMLElement))) {
          return undefined; // Skip DOM/React elements
        }

        // Add object to seen set to detect circular references
        if (typeof value === 'object' && value !== null) {
          seen.add(value);
        }
      }
      return value;
    });
  } catch (error) {
    console.error('Error stringifying data for API request:', error);
    // Create a minimal safe version with primitive values only
    return JSON.stringify({
      error: 'Data contained circular references and could not be serialized',
      url: url
    });
  }
};

// Token refresh functionality has been disabled
const refreshAccessToken = async () => {
  console.log('Token refresh has been disabled');
  clearAuthData();

  // Dispatch a session expired event
  window.dispatchEvent(new CustomEvent('auth:sessionExpired', {
    detail: {
      message: 'Votre session a expiré. Veuillez vous reconnecter.',
      currentPath: window.location.pathname
    }
  }));

  throw new Error('Token refresh has been disabled');
};

// Generic fetch wrapper with token handling
const fetchWithAuth = async (url, options = {}) => {
  // Always use silent auth for authentication-related endpoints to prevent page reloads
  const isAuthEndpoint = url.includes('/auth/');

  if (isAuthEndpoint) {
    // Force silent auth for all auth endpoints
    options.silentAuth = true;
  }

  // Create headers with authentication if token exists
  const token = getAccessToken();
  const headers = {
    'Content-Type': 'application/json',
    ...options.headers
  };

  if (token) {
    // Make sure the token is properly formatted
    headers['Authorization'] = `Bearer ${token.trim()}`;

    // Removed token logging for security
  }

  // Make the request
  try {
    const response = await fetch(`${BASE_URL}${url}`, {
      ...options,
      headers
    });

    // If the response is OK, return it
    if (response.ok) {
      if (response.status === 204) {
        return { success: true };
      }

      // For blob responses, return the blob directly
      if (options.responseType === 'blob') {
        return response.blob();
      }

      // Otherwise parse JSON
      const jsonResponse = await response.json();
      // Removed response logging for security
      return jsonResponse;
    }

    // Handle 401 Unauthorized - no token refresh, just redirect to login
    if (response.status === 401) {
      console.log('Unauthorized request - token refresh disabled');

      // Clear auth data
      clearAuthData();

      // Only show notification if we're not on the login page and not a silent auth request
      if (window.location.pathname !== '/login' && !options.silentAuth) {
        window.dispatchEvent(new CustomEvent('auth:sessionExpired', {
          detail: {
            message: 'Votre session a expiré. Veuillez vous reconnecter.',
            currentPath: window.location.pathname
          }
        }));
      }

      // Throw an error to indicate authentication failure
      throw new Error('Authentication failed - please log in again');
    }

    // For other errors, parse response and throw
    let errorData;
    try {
      errorData = await response.json();
    } catch (e) {
      errorData = { message: response.statusText };
    }

    const error = {
      status: response.status,
      data: errorData,
      message: errorData.message || 'Request failed'
    };

    // Suppress 404 errors for CTI endpoints (expected when no data exists)
    const isCTIEndpoint = url.includes('/cti-results');
    const is404Error = response.status === 404;

    if (isCTIEndpoint && is404Error) {
      // Log 404 CTI errors as info instead of error (this is expected behavior)
      console.log(`CTI data not found (404) - this is normal when no analysis has been performed yet`);
    } else {
      // Log other errors normally
      console.error(`Error response from API endpoint`);
    }

    throw error;
  } catch (error) {
    // Log without revealing sensitive data
    console.error(`Network error for API endpoint`);
    throw error;
  }
};

// API client methods
export const api = {
  /**
   * GET request
   * @param {string} url - API endpoint
   * @param {Object} params - Query parameters
   * @param {Object} options - Additional options
   * @param {boolean} options.silentAuth - Whether to handle auth silently (no page reload)
   * @returns {Promise} - Response promise
   */
  get: (url, params = {}, options = {}) => {
    // Add query params to URL
    const queryString = new URLSearchParams(params).toString();
    const urlWithParams = queryString ? `${url}?${queryString}` : url;

    return fetchWithAuth(urlWithParams, {
      method: 'GET',
      silentAuth: options.silentAuth
    });
  },

  /**
   * POST request
   * @param {string} url - API endpoint
   * @param {Object} data - Request body
   * @param {Object} options - Additional options
   * @param {boolean} options.silentAuth - Whether to handle auth silently (no page reload)
   * @returns {Promise} - Response promise
   */
  post: (url, data = {}, options = {}) => {
    // Use the utility function to safely stringify the data
    const safeData = safeStringify(data, url);

    return fetchWithAuth(url, {
      method: 'POST',
      body: safeData,
      silentAuth: options.silentAuth
    });
  },

  /**
   * PUT request
   * @param {string} url - API endpoint
   * @param {Object} data - Request body
   * @param {Object} options - Additional options
   * @param {boolean} options.silentAuth - Whether to handle auth silently (no page reload)
   * @returns {Promise} - Response promise
   */
  put: (url, data = {}, options = {}) => {
    // Use the utility function to safely stringify the data
    const safeData = safeStringify(data, url);

    return fetchWithAuth(url, {
      method: 'PUT',
      body: safeData,
      silentAuth: options.silentAuth
    });
  },

  /**
   * DELETE request
   * @param {string} url - API endpoint
   * @param {Object} params - Query parameters
   * @param {Object} options - Additional options
   * @param {boolean} options.silentAuth - Whether to handle auth silently (no page reload)
   * @returns {Promise} - Response promise
   */
  delete: (url, params = {}, options = {}) => {
    // Add query params to URL
    const queryString = new URLSearchParams(params).toString();
    const urlWithParams = queryString ? `${url}?${queryString}` : url;

    return fetchWithAuth(urlWithParams, {
      method: 'DELETE',
      silentAuth: options.silentAuth
    });
  },

  /**
   * Download a file
   * @param {string} url - API endpoint
   * @param {Object} params - Query parameters
   * @param {Object} options - Additional options
   * @param {boolean} options.silentAuth - Whether to handle auth silently (no page reload)
   * @returns {Promise<Blob>} - Blob promise
   */
  download: (url, params = {}, options = {}) => {
    // Add query params to URL
    const queryString = new URLSearchParams(params).toString();
    const urlWithParams = queryString ? `${url}?${queryString}` : url;

    return fetchWithAuth(urlWithParams, {
      method: 'GET',
      responseType: 'blob',
      silentAuth: options.silentAuth
    });
  },

  /**
   * Upload file(s)
   * @param {string} url - API endpoint
   * @param {FormData} formData - Form data with files
   * @param {Function} onProgress - Progress callback (not supported in fetch)
   * @param {Object} options - Additional options
   * @param {boolean} options.silentAuth - Whether to handle auth silently (no page reload)
   * @returns {Promise} - Response promise
   */
  upload: (url, formData, onProgress = null, options = {}) => {
    // Note: fetch doesn't support upload progress
    if (onProgress) {
      // Removed sensitive data logging for security
    }

    // Don't set Content-Type header, let the boundary be set automatically
    return fetchWithAuth(url, {
      method: 'POST',
      headers: {}, // Override the default Content-Type
      body: formData,
      silentAuth: options?.silentAuth
    });
  }
};

export default api;