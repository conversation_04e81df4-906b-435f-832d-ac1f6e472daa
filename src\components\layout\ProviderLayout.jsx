// src/components/layout/ProviderLayout.jsx
import React from 'react';
import { Link, useLocation } from 'react-router-dom';

const ProviderLayout = ({ children }) => {
  const location = useLocation();
  
  // Déterminer quelle page est active
  const isActive = (path) => {
    return location.pathname === path;
  };
  
  return (
    <div className="min-h-screen bg-gray-100">
      {/* Header */}
      <header className="bg-blue-700 text-white p-4 shadow-md">
        <div className="container mx-auto flex justify-between items-center">
          <h1 className="text-2xl font-bold">EBIOS RM - Entreprise</h1>
          <div className="flex items-center space-x-4">
            <span className="text-sm">Provider</span>
           <button className="bg-blue-800 px-3 py-1 rounded-md text-sm hover:bg-blue-900">
             Déconnexion
           </button>
         </div>
       </div>
     </header>
     
     {/* Navigation */}
     <nav className="bg-white shadow-md">
       <div className="container mx-auto">
         <ul className="flex">
           <li>
             <Link
               to="/provider/dashboard"
               className={`px-4 py-3 inline-block ${
                 isActive('/provider/dashboard')
                   ? 'text-blue-700 border-b-2 border-blue-700 font-medium'
                   : 'text-gray-600 hover:text-blue-600'
               }`}
             >
               Dashboard
             </Link>
           </li>
           <li>
             <Link
               to="/provider/users"
               className={`px-4 py-3 inline-block ${
                 isActive('/provider/users')
                   ? 'text-blue-700 border-b-2 border-blue-700 font-medium'
                   : 'text-gray-600 hover:text-blue-600'
               }`}
             >
               Utilisateurs
             </Link>
           </li>
           <li>
             <Link
               to="/provider/analyses"
               className={`px-4 py-3 inline-block ${
                 isActive('/provider/analyses')
                   ? 'text-blue-700 border-b-2 border-blue-700 font-medium'
                   : 'text-gray-600 hover:text-blue-600'
               }`}
             >
               Analyses
             </Link>
           </li>
           <li>
             <Link
               to="/provider/activities"
               className={`px-4 py-3 inline-block ${
                 isActive('/provider/activities')
                   ? 'text-blue-700 border-b-2 border-blue-700 font-medium'
                   : 'text-gray-600 hover:text-blue-600'
               }`}
             >
               Logs d'activité
             </Link>
           </li>
         </ul>
       </div>
     </nav>
     
     {/* Main Content */}
     <main className="container mx-auto py-6">{children}</main>
     
     {/* Footer */}
     <footer className="bg-gray-200 p-4 text-center text-gray-600">
       EBIOS RM - Entreprise © 2025
     </footer>
   </div>
 );
};

export default ProviderLayout;