// src/components/Atelier4/Activite2/OperationalScenariosTable.js
import React, { useState } from 'react';
import './OperationalScenariosTable.css';

const OperationalScenariosTable = ({
  scenarios,
  attackPaths,
  onScenarioUpdate,
  onScenarioDelete,
  selectedScenarios,
  onSelectionChange
}) => {
  const [expandedScenarios, setExpandedScenarios] = useState(new Set());
  const [editingScenario, setEditingScenario] = useState(null);
  const [sortConfig, setSortConfig] = useState({ key: null, direction: 'asc' });

  const toggleExpanded = (scenarioId) => {
    const newExpanded = new Set(expandedScenarios);
    if (newExpanded.has(scenarioId)) {
      newExpanded.delete(scenarioId);
    } else {
      newExpanded.add(scenarioId);
    }
    setExpandedScenarios(newExpanded);
  };

  const handleSort = (key) => {
    let direction = 'asc';
    if (sortConfig.key === key && sortConfig.direction === 'asc') {
      direction = 'desc';
    }
    setSortConfig({ key, direction });
  };

  const sortedScenarios = React.useMemo(() => {
    if (!sortConfig.key) return scenarios;

    return [...scenarios].sort((a, b) => {
      let aValue = a[sortConfig.key];
      let bValue = b[sortConfig.key];

      if (typeof aValue === 'string') {
        aValue = aValue.toLowerCase();
        bValue = bValue.toLowerCase();
      }

      if (aValue < bValue) {
        return sortConfig.direction === 'asc' ? -1 : 1;
      }
      if (aValue > bValue) {
        return sortConfig.direction === 'asc' ? 1 : -1;
      }
      return 0;
    });
  }, [scenarios, sortConfig]);

  const handleSelectScenario = (scenarioId) => {
    const newSelection = selectedScenarios.includes(scenarioId)
      ? selectedScenarios.filter(id => id !== scenarioId)
      : [...selectedScenarios, scenarioId];
    onSelectionChange(newSelection);
  };

  const handleSelectAll = () => {
    if (selectedScenarios.length === scenarios.length) {
      onSelectionChange([]);
    } else {
      onSelectionChange(scenarios.map(s => s.id));
    }
  };

  const getSeverityColor = (severity) => {
    const colors = {
      low: '#28a745',
      medium: '#ffc107',
      high: '#fd7e14',
      critical: '#dc3545'
    };
    return colors[severity] || '#6c757d';
  };

  const getLikelihoodColor = (likelihood) => {
    const colors = {
      low: '#28a745',
      medium: '#ffc107',
      high: '#dc3545'
    };
    return colors[likelihood] || '#6c757d';
  };

  const getAttackPathName = (attackPathId) => {
    const path = attackPaths.find(p => p.id === attackPathId);
    return path ? path.name : `Chemin ${attackPathId}`;
  };

  const handleEdit = (scenario) => {
    setEditingScenario({ ...scenario });
  };

  const handleSaveEdit = () => {
    if (editingScenario) {
      onScenarioUpdate(editingScenario.id, editingScenario);
      setEditingScenario(null);
    }
  };

  const handleCancelEdit = () => {
    setEditingScenario(null);
  };

  if (scenarios.length === 0) {
    return (
      <div className="scenarios-table-container">
        <div className="empty-state">
          <i className="fas fa-list-alt"></i>
          <h3>Aucun scénario opérationnel</h3>
          <p>Commencez par générer des scénarios opérationnels à partir des chemins d'attaque.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="scenarios-table-container">
      <div className="table-header">
        <div className="table-controls">
          <label className="select-all">
            <input
              type="checkbox"
              checked={selectedScenarios.length === scenarios.length}
              onChange={handleSelectAll}
            />
            Sélectionner tout ({scenarios.length})
          </label>
          
          {selectedScenarios.length > 0 && (
            <div className="bulk-actions">
              <span className="selection-count">
                {selectedScenarios.length} sélectionné{selectedScenarios.length > 1 ? 's' : ''}
              </span>
              <button 
                className="btn btn-danger btn-sm"
                onClick={() => {
                  selectedScenarios.forEach(id => onScenarioDelete(id));
                  onSelectionChange([]);
                }}
              >
                <i className="fas fa-trash"></i>
                Supprimer
              </button>
            </div>
          )}
        </div>
      </div>

      <div className="table-wrapper">
        <table className="scenarios-table">
          <thead>
            <tr>
              <th className="select-column"></th>
              <th 
                className="sortable"
                onClick={() => handleSort('name')}
              >
                Nom du scénario
                {sortConfig.key === 'name' && (
                  <i className={`fas fa-sort-${sortConfig.direction === 'asc' ? 'up' : 'down'}`}></i>
                )}
              </th>
              <th 
                className="sortable"
                onClick={() => handleSort('severity')}
              >
                Gravité
                {sortConfig.key === 'severity' && (
                  <i className={`fas fa-sort-${sortConfig.direction === 'asc' ? 'up' : 'down'}`}></i>
                )}
              </th>
              <th 
                className="sortable"
                onClick={() => handleSort('likelihood')}
              >
                Probabilité
                {sortConfig.key === 'likelihood' && (
                  <i className={`fas fa-sort-${sortConfig.direction === 'asc' ? 'up' : 'down'}`}></i>
                )}
              </th>
              <th>Chemin d'attaque</th>
              <th>Compétences requises</th>
              <th>Durée estimée</th>
              <th className="actions-column">Actions</th>
            </tr>
          </thead>
          <tbody>
            {sortedScenarios.map((scenario) => (
              <React.Fragment key={scenario.id}>
                <tr className={selectedScenarios.includes(scenario.id) ? 'selected' : ''}>
                  <td className="select-column">
                    <input
                      type="checkbox"
                      checked={selectedScenarios.includes(scenario.id)}
                      onChange={() => handleSelectScenario(scenario.id)}
                    />
                  </td>
                  <td className="scenario-name">
                    <div className="name-content">
                      <button
                        className="expand-btn"
                        onClick={() => toggleExpanded(scenario.id)}
                      >
                        <i className={`fas fa-chevron-${expandedScenarios.has(scenario.id) ? 'down' : 'right'}`}></i>
                      </button>
                      <span className="name">{scenario.name}</span>
                      {scenario.source === 'ai_generated' && (
                        <span className="ai-badge">
                          <i className="fas fa-robot"></i>
                          IA
                        </span>
                      )}
                    </div>
                  </td>
                  <td>
                    <span 
                      className="severity-badge"
                      style={{ backgroundColor: getSeverityColor(scenario.severity) }}
                    >
                      {scenario.severity}
                    </span>
                  </td>
                  <td>
                    <span 
                      className="likelihood-badge"
                      style={{ backgroundColor: getLikelihoodColor(scenario.likelihood) }}
                    >
                      {scenario.likelihood}
                    </span>
                  </td>
                  <td className="attack-path">
                    {getAttackPathName(scenario.attackPathId)}
                  </td>
                  <td>
                    <span className="skills-badge">
                      {scenario.requiredSkills}
                    </span>
                  </td>
                  <td>{scenario.timeline}</td>
                  <td className="actions-column">
                    <div className="action-buttons">
                      <button
                        className="btn btn-sm btn-primary"
                        onClick={() => handleEdit(scenario)}
                        title="Modifier"
                      >
                        <i className="fas fa-edit"></i>
                      </button>
                      <button
                        className="btn btn-sm btn-danger"
                        onClick={() => onScenarioDelete(scenario.id)}
                        title="Supprimer"
                      >
                        <i className="fas fa-trash"></i>
                      </button>
                    </div>
                  </td>
                </tr>
                
                {expandedScenarios.has(scenario.id) && (
                  <tr className="expanded-row">
                    <td colSpan="8">
                      <div className="scenario-details">
                        <div className="description-section">
                          <h4>Description</h4>
                          <p>{scenario.description}</p>
                        </div>
                        
                        <div className="steps-section">
                          <h4>Étapes opérationnelles ({scenario.steps?.length || 0})</h4>
                          {scenario.steps && scenario.steps.length > 0 ? (
                            <div className="steps-list">
                              {scenario.steps.map((step, index) => (
                                <div key={step.id || index} className="step-item">
                                  <div className="step-header">
                                    <span className="step-number">{index + 1}</span>
                                    <h5>{step.name}</h5>
                                    <span className="step-duration">{step.duration}</span>
                                  </div>
                                  <p className="step-description">{step.description}</p>
                                  
                                  <div className="step-details">
                                    <div className="detail-group">
                                      <strong>Techniques:</strong>
                                      <div className="techniques">
                                        {step.techniques?.map((technique, i) => (
                                          <span key={i} className="technique-tag">{technique}</span>
                                        ))}
                                      </div>
                                    </div>
                                    
                                    <div className="detail-group">
                                      <strong>Indicateurs:</strong>
                                      <div className="indicators">
                                        {step.indicators?.map((indicator, i) => (
                                          <span key={i} className="indicator-tag">{indicator}</span>
                                        ))}
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              ))}
                            </div>
                          ) : (
                            <p className="no-steps">Aucune étape définie</p>
                          )}
                        </div>
                        
                        <div className="metadata-section">
                          <div className="metadata-grid">
                            <div className="metadata-item">
                              <strong>Difficulté de détection:</strong>
                              <span>{scenario.detectionDifficulty}</span>
                            </div>
                            <div className="metadata-item">
                              <strong>Ressources nécessaires:</strong>
                              <span>{scenario.resources}</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </td>
                  </tr>
                )}
              </React.Fragment>
            ))}
          </tbody>
        </table>
      </div>

      {editingScenario && (
        <div className="edit-modal-overlay">
          <div className="edit-modal">
            <div className="modal-header">
              <h3>Modifier le scénario</h3>
              <button className="close-btn" onClick={handleCancelEdit}>
                <i className="fas fa-times"></i>
              </button>
            </div>
            
            <div className="modal-body">
              <div className="form-group">
                <label>Nom du scénario</label>
                <input
                  type="text"
                  value={editingScenario.name}
                  onChange={(e) => setEditingScenario({
                    ...editingScenario,
                    name: e.target.value
                  })}
                />
              </div>
              
              <div className="form-group">
                <label>Description</label>
                <textarea
                  value={editingScenario.description}
                  onChange={(e) => setEditingScenario({
                    ...editingScenario,
                    description: e.target.value
                  })}
                  rows="4"
                />
              </div>
              
              <div className="form-row">
                <div className="form-group">
                  <label>Gravité</label>
                  <select
                    value={editingScenario.severity}
                    onChange={(e) => setEditingScenario({
                      ...editingScenario,
                      severity: e.target.value
                    })}
                  >
                    <option value="low">Faible</option>
                    <option value="medium">Moyenne</option>
                    <option value="high">Élevée</option>
                    <option value="critical">Critique</option>
                  </select>
                </div>
                
                <div className="form-group">
                  <label>Probabilité</label>
                  <select
                    value={editingScenario.likelihood}
                    onChange={(e) => setEditingScenario({
                      ...editingScenario,
                      likelihood: e.target.value
                    })}
                  >
                    <option value="low">Faible</option>
                    <option value="medium">Moyenne</option>
                    <option value="high">Élevée</option>
                  </select>
                </div>
              </div>
            </div>
            
            <div className="modal-footer">
              <button className="btn btn-secondary" onClick={handleCancelEdit}>
                Annuler
              </button>
              <button className="btn btn-primary" onClick={handleSaveEdit}>
                Sauvegarder
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default OperationalScenariosTable;