// src/components/Atelier 2/Activite1/SourcesRisques.js
import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Save, Plus, RefreshCw, AlertCircle, Trash2, Edit, X, Check, Search, Filter, Layers, Sparkles } from 'lucide-react';
import { useAnalysis } from '../../../context/AnalysisContext';
import { showSuccessToast, showErrorToast, showLoadingToast, updateToast } from '../../../utils/toastUtils';
import { THREAT_CATEGORIES } from './ThreatCategories';
import CategorySourcesTable from './CategorySourcesTable';
import SliderSelector from '../common/SliderSelector';
import { getAiSourcesRisqueSuggestions } from '../../../services/sourcesRisqueService';

const SourcesRisques = () => {
  // State for sources de risque
  const [sourcesRisque, setSourcesRisque] = useState([]);
  const [newSource, setNewSource] = useState({
    name: '',
    description: '',
    objectifVise: 'confidentialite', // Default objectif: confidentialite, integrite, disponibilite, auditabilite
    motivation: 'faible', // faible, moyen, eleve
    capacite: 'faible', // faible, moyenne, forte
    ressources: 'faibles', // faibles, moyennes, importantes
    activite: '', // Description of the activity
    category: '', // Threat category
  });
  const [editingSource, setEditingSource] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState('');
  const [filterCategory, setFilterCategory] = useState('');
  const [isSaving, setIsSaving] = useState(false);
  const [selectedCategories, setSelectedCategories] = useState([]);
  const [viewMode, setViewMode] = useState('category'); // 'category' or 'list'
  const [isAiModalOpen, setIsAiModalOpen] = useState(false);
  const [aiSuggestions, setAiSuggestions] = useState([]);
  const [isLoadingAi, setIsLoadingAi] = useState(false);
  const [selectedAiSuggestions, setSelectedAiSuggestions] = useState([]);

  // Get analysis context
  const {
    currentAnalysis,
    saveSourcesDeRisque,
    getSourcesDeRisque,
    loadThreatCategories,
  } = useAnalysis();

  // Load sources de risque and threat categories on component mount
  useEffect(() => {
    const fetchData = async () => {
      if (currentAnalysis?.id) {
        try {
          // Load sources de risque
          const sourcesData = await getSourcesDeRisque(currentAnalysis.id);
          if (sourcesData) {
            setSourcesRisque(sourcesData);
          }

          // Load threat categories
          const categoriesData = await loadThreatCategories(currentAnalysis.id);
          if (categoriesData && Array.isArray(categoriesData)) {
            setSelectedCategories(categoriesData);
          }
        } catch (error) {
          console.error('Error loading data:', error);
          showErrorToast('Erreur lors du chargement des données');
        }
      }
    };

    fetchData();
  }, [currentAnalysis?.id, getSourcesDeRisque, loadThreatCategories]);

  // Listen for custom events
  useEffect(() => {
    const handleAiEvent = () => {
      handleGetAiSuggestions();
    };

    const handleSaveEvent = () => {
      // Make sure we're using the latest state
      setTimeout(() => {
        handleSaveAll();
      }, 100);
    };

    window.addEventListener('get-ai-sources-suggestions', handleAiEvent);
    window.addEventListener('save-sources-risque', handleSaveEvent);

    return () => {
      window.removeEventListener('get-ai-sources-suggestions', handleAiEvent);
      window.removeEventListener('save-sources-risque', handleSaveEvent);
    };
  }, [currentAnalysis?.id, sourcesRisque]);

  // Handle adding a new source de risque
  const handleAddSource = () => {
    if (!newSource.name.trim()) {
      showErrorToast('Le nom de la source de risque est requis');
      return;
    }

    const sourceToAdd = {
      ...newSource,
      id: Date.now().toString(), // Temporary ID until saved to backend
      createdAt: new Date().toISOString(),
    };

    setSourcesRisque([...sourcesRisque, sourceToAdd]);
    setNewSource({
      name: '',
      description: '',
      objectifVise: 'confidentialite',
      motivation: 'faible',
      capacite: 'faible',
      ressources: 'faibles',
      activite: '',
      category: '',
    });

    showSuccessToast('Source de risque ajoutée');
  };

  // Handle editing a source de risque
  const handleEditSource = (source) => {
    setEditingSource({
      ...source,
    });
  };

  // Handle saving edited source de risque
  const handleSaveEdit = () => {
    if (!editingSource.name.trim()) {
      showErrorToast('Le nom de la source de risque est requis');
      return;
    }

    const updatedSources = sourcesRisque.map(source =>
      source.id === editingSource.id ? editingSource : source
    );

    setSourcesRisque(updatedSources);
    setEditingSource(null);
    showSuccessToast('Source de risque mise à jour');
  };

  // Handle canceling edit
  const handleCancelEdit = () => {
    setEditingSource(null);
  };

  // Handle deleting a source de risque
  const handleDeleteSource = (id) => {
    if (window.confirm('Êtes-vous sûr de vouloir supprimer cette source de risque ?')) {
      const updatedSources = sourcesRisque.filter(source => source.id !== id);
      setSourcesRisque(updatedSources);
      showSuccessToast('Source de risque supprimée');
    }
  };

  // Handle saving all sources de risque
  const handleSaveAll = async () => {
    if (!currentAnalysis?.id) {
      showErrorToast('Aucune analyse sélectionnée');
      return;
    }

    setIsSaving(true);
    const toastId = showLoadingToast('Sauvegarde des sources de risque...');

    try {
      await saveSourcesDeRisque(currentAnalysis.id, sourcesRisque);
      updateToast(toastId, 'Sources de risque sauvegardées avec succès', 'success');
    } catch (error) {
      console.error('Error saving sources de risque:', error);
      updateToast(toastId, 'Erreur lors de la sauvegarde des sources de risque', 'error');
    } finally {
      setIsSaving(false);
    }
  };

  // Handle AI suggestions
  const handleGetAiSuggestions = async () => {
    if (!currentAnalysis?.id) {
      showErrorToast('Aucune analyse sélectionnée');
      return;
    }

    setIsLoadingAi(true);
    setIsAiModalOpen(true);
    const toastId = showLoadingToast('Génération des suggestions IA...');

    try {
      // Get existing source names to avoid duplicates
      const existingSourceNames = sourcesRisque.map(source => source.name);

      // Get threat categories
      const threatCategories = selectedCategories.map(categoryId => {
        const category = THREAT_CATEGORIES.find(c => c.id === categoryId);
        return category ? category.id : null;
      }).filter(Boolean);

      // Call AI API
      const response = await getAiSourcesRisqueSuggestions({
        analysisId: currentAnalysis.id,
        analysisName: currentAnalysis.name,
        analysisDescription: currentAnalysis.description || '',
        organizationContext: currentAnalysis.organizationContext || '',
        threatCategories,
        existingSourceNames
      });

      if (response.success && response.data.suggestions) {
        setAiSuggestions(response.data.suggestions);
        updateToast(toastId, 'Suggestions IA générées avec succès', 'success');
      } else {
        throw new Error('Réponse invalide de l\'API');
      }
    } catch (error) {
      console.error('Error getting AI suggestions:', error);
      updateToast(toastId, 'Erreur lors de la génération des suggestions IA', 'error');
      setIsAiModalOpen(false);
    } finally {
      setIsLoadingAi(false);
    }
  };

  // Handle selecting/deselecting AI suggestions
  const handleToggleAiSuggestion = (suggestion) => {
    setSelectedAiSuggestions(prev => {
      const isSelected = prev.some(s => s.name === suggestion.name);
      if (isSelected) {
        return prev.filter(s => s.name !== suggestion.name);
      } else {
        return [...prev, suggestion];
      }
    });
  };

  // Handle adding selected AI suggestions
  const handleAddAiSuggestions = () => {
    if (selectedAiSuggestions.length === 0) {
      showErrorToast('Aucune suggestion sélectionnée');
      return;
    }

    // Format suggestions to match source de risque structure
    const newSources = selectedAiSuggestions.map(suggestion => ({
      ...suggestion,
      id: `source-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
      createdAt: new Date().toISOString(),
      // Map objectifViseCategory to objectifVise if needed
      objectifVise: suggestion.objectifVise || suggestion.objectifViseCategory || 'confidentialite',
      // Map category to match the expected format
      category: suggestion.category || ''
    }));

    // Add to sources
    setSourcesRisque(prev => [...prev, ...newSources]);

    // Close modal and reset
    setIsAiModalOpen(false);
    setSelectedAiSuggestions([]);
    setAiSuggestions([]);

    showSuccessToast(`${newSources.length} source(s) de risque ajoutée(s)`);
  };

  // Filter sources based on search term, objectifVise filter, and category filter
  const filteredSources = sourcesRisque.filter(source => {
    const matchesSearch = source.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         source.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesType = filterType ? source.objectifVise === filterType : true;
    const matchesCategory = filterCategory ? source.category === filterCategory : true;
    return matchesSearch && matchesType && matchesCategory;
  });

  // Get color for capacity and resources
  const getCapacityColor = (level) => {
    switch (level) {
      case 'faible': return 'bg-green-100 text-green-800';
      case 'moyenne': return 'bg-yellow-100 text-yellow-800';
      case 'forte': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  // Get motivation badge color
  const getMotivationColor = (motivation) => {
    switch (motivation) {
      case 'faible': return 'bg-green-100 text-green-800';
      case 'moyen': return 'bg-yellow-100 text-yellow-800';
      case 'eleve': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getResourcesColor = (level) => {
    switch (level) {
      case 'faibles': return 'bg-green-100 text-green-800';
      case 'moyennes': return 'bg-yellow-100 text-yellow-800';
      case 'importantes': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  // Color maps for sliders
  const capacityColorMap = {
    'faible': 'bg-green-600',
    'moyenne': 'bg-yellow-600',
    'forte': 'bg-red-600'
  };

  const resourcesColorMap = {
    'faibles': 'bg-green-600',
    'moyennes': 'bg-yellow-600',
    'importantes': 'bg-red-600'
  };

  // Get objectif visé badge color
  const getObjectifBadgeColor = (objectif) => {
    switch (objectif) {
      case 'confidentialite': return 'bg-blue-100 text-blue-800';
      case 'integrite': return 'bg-green-100 text-green-800';
      case 'disponibilite': return 'bg-purple-100 text-purple-800';
      case 'auditabilite': return 'bg-red-100 text-red-800';
      case 'preuve': return 'bg-orange-100 text-orange-800';
      case 'tracabilite': return 'bg-indigo-100 text-indigo-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="bg-white p-6 rounded-lg shadow-sm border border-gray-200"
    >
      {/* Header */}
      <div className="flex justify-between items-center mb-6">
        <div>
          <h2 className="text-2xl font-bold text-gray-800 flex items-center">
            <AlertCircle size={24} className="mr-2 text-blue-600" />
            Sources de Risque
          </h2>
          <p className="text-sm text-gray-500 mt-1">
            Identifiez les sources de risque potentielles pour votre analyse
          </p>
        </div>
        <div className="flex items-center space-x-4">
          {/* View mode toggle */}
          <div className="bg-gray-100 rounded-md p-1 flex">
            <button
              className={`px-3 py-1.5 rounded-md flex items-center ${viewMode === 'category' ? 'bg-white shadow-sm' : 'text-gray-600'}`}
              onClick={() => setViewMode('category')}
            >
              <Layers size={16} className="mr-1.5" />
              Par catégorie
            </button>
            <button
              className={`px-3 py-1.5 rounded-md flex items-center ${viewMode === 'list' ? 'bg-white shadow-sm' : 'text-gray-600'}`}
              onClick={() => setViewMode('list')}
            >
              <Filter size={16} className="mr-1.5" />
              Liste complète
            </button>
          </div>

          <div className="flex space-x-2">
            {/* AI Button */}
            <button
              onClick={handleGetAiSuggestions}
              disabled={isLoadingAi || !currentAnalysis?.id}
              className="px-4 py-2 text-sm font-medium text-white bg-gradient-to-r from-purple-500 to-indigo-600 hover:from-purple-600 hover:to-indigo-700 rounded-md flex items-center shadow-sm transition-all duration-200 transform hover:scale-105 hover:shadow focus:outline-none focus:ring-2 focus:ring-offset-1 focus:ring-indigo-500 disabled:opacity-60 disabled:cursor-not-allowed disabled:hover:scale-100 disabled:hover:shadow-sm"
            >
              <Sparkles size={16} className="mr-1.5 text-yellow-100" />
              {isLoadingAi ? (
                <span className="flex items-center">
                  <RefreshCw size={14} className="mr-1.5 animate-spin" />
                  Génération IA...
                </span>
              ) : (
                'Suggestions IA'
              )}
            </button>

            {/* Save Button */}
            <button
              onClick={handleSaveAll}
              disabled={isSaving}
              className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 flex items-center disabled:opacity-50"
            >
              {isSaving ? (
                <RefreshCw size={16} className="mr-2 animate-spin" />
              ) : (
                <Save size={16} className="mr-2" />
              )}
              Enregistrer
            </button>
          </div>
        </div>
      </div>

      {/* Add new source form - only shown in list view */}
      {viewMode === 'list' && (
        <div className="bg-gray-50 p-4 rounded-lg border border-gray-200 mb-6">
          <h3 className="text-lg font-semibold text-gray-700 mb-3">Ajouter une source de risque</h3>

          {selectedCategories.length === 0 ? (
            <div className="bg-yellow-50 p-4 rounded-lg border border-yellow-200 mb-4">
              <div className="flex items-start">
                <AlertCircle size={20} className="text-yellow-500 mt-0.5 mr-3 flex-shrink-0" />
                <div>
                  <h3 className="font-medium text-yellow-800">Aucune catégorie de menace sélectionnée</h3>
                  <p className="text-sm text-yellow-600 mt-1">
                    Veuillez d'abord sélectionner des catégories de menaces dans l'onglet "Catégories de Menaces".
                  </p>
                </div>
              </div>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Nom</label>
                <input
                  type="text"
                  value={newSource.name}
                  onChange={(e) => setNewSource({ ...newSource, name: e.target.value })}
                  className="w-full p-2 border border-gray-300 rounded-md"
                  placeholder="Nom de la source de risque"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Catégorie</label>
                <select
                  value={newSource.category}
                  onChange={(e) => setNewSource({ ...newSource, category: e.target.value })}
                  className="w-full p-2 border border-gray-300 rounded-md"
                >
                  <option value="">Sélectionner une catégorie</option>
                  {selectedCategories.map(categoryId => {
                    const category = THREAT_CATEGORIES.find(c => c.id === categoryId);
                    return category ? (
                      <option key={category.id} value={category.id}>{category.name}</option>
                    ) : null;
                  })}
                </select>
              </div>
            </div>
          )}

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Objectif Visé</label>
              <select
                value={newSource.objectifVise}
                onChange={(e) => setNewSource({ ...newSource, objectifVise: e.target.value })}
                className="w-full p-2 border border-gray-300 rounded-md"
                disabled={selectedCategories.length === 0}
              >
                <option value="confidentialite">Confidentialité</option>
                <option value="integrite">Intégrité</option>
                <option value="disponibilite">Disponibilité</option>
                <option value="auditabilite">Auditabilité</option>
                <option value="preuve">Preuve</option>
                <option value="tracabilite">Traçabilité</option>
              </select>
            </div>
            <div>
              <SliderSelector
                label="Ressources"
                value={newSource.ressources}
                onChange={(value) => setNewSource({ ...newSource, ressources: value })}
                options={[
                  { value: 'faibles', label: 'Faibles' },
                  { value: 'moyennes', label: 'Moyennes' },
                  { value: 'importantes', label: 'Importantes' }
                ]}
                colorMap={resourcesColorMap}
                disabled={selectedCategories.length === 0}
              />
            </div>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            <div>
              <SliderSelector
                label="Capacité"
                value={newSource.capacite}
                onChange={(value) => setNewSource({ ...newSource, capacite: value })}
                options={[
                  { value: 'faible', label: 'Faible' },
                  { value: 'moyenne', label: 'Moyenne' },
                  { value: 'forte', label: 'Forte' }
                ]}
                colorMap={capacityColorMap}
                disabled={selectedCategories.length === 0}
              />
            </div>
            <div>
              <SliderSelector
                label="Motivation"
                value={newSource.motivation}
                onChange={(value) => setNewSource({ ...newSource, motivation: value })}
                options={[
                  { value: 'faible', label: 'Faible' },
                  { value: 'moyen', label: 'Moyen' },
                  { value: 'eleve', label: 'Élevé' }
                ]}
                colorMap={{
                  'faible': 'bg-green-600',
                  'moyen': 'bg-yellow-600',
                  'eleve': 'bg-red-600'
                }}
                disabled={selectedCategories.length === 0}
              />
            </div>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Description</label>
              <textarea
                value={newSource.description}
                onChange={(e) => setNewSource({ ...newSource, description: e.target.value })}
                className="w-full p-2 border border-gray-300 rounded-md"
                rows="2"
                placeholder="Description de la source de risque"
                disabled={selectedCategories.length === 0}
              ></textarea>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Activité</label>
              <textarea
                value={newSource.activite}
                onChange={(e) => setNewSource({ ...newSource, activite: e.target.value })}
                className="w-full p-2 border border-gray-300 rounded-md"
                rows="2"
                placeholder="Activité de la source de risque"
                disabled={selectedCategories.length === 0}
              ></textarea>
            </div>
          </div>
          <div className="flex justify-end">
            <button
              onClick={handleAddSource}
              disabled={selectedCategories.length === 0}
              className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 flex items-center disabled:opacity-50"
            >
              <Plus size={16} className="mr-2" />
              Ajouter
            </button>
          </div>
        </div>
      )}

      {/* Main content based on view mode */}
      {viewMode === 'category' ? (
        // Category-based table view
        <CategorySourcesTable
          selectedCategories={selectedCategories}
          sourcesRisque={sourcesRisque}
          setSourcesRisque={setSourcesRisque}
        />
      ) : (
        // Original list view
        <div>
          {/* Search and filter */}
          <div className="flex flex-col md:flex-row gap-4 mb-6">
            <div className="flex-1">
              <div className="relative">
                <input
                  type="text"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full p-2 pl-10 border border-gray-300 rounded-md"
                  placeholder="Rechercher une source de risque..."
                />
                <Search size={18} className="absolute left-3 top-2.5 text-gray-400" />
              </div>
            </div>
            <div className="w-full md:w-48">
              <select
                value={filterType}
                onChange={(e) => setFilterType(e.target.value)}
                className="w-full p-2 border border-gray-300 rounded-md"
              >
                <option value="">Tous les objectifs</option>
                <option value="confidentialite">Confidentialité</option>
                <option value="integrite">Intégrité</option>
                <option value="disponibilite">Disponibilité</option>
                <option value="auditabilite">Auditabilité</option>
                <option value="preuve">Preuve</option>
                <option value="tracabilite">Traçabilité</option>
              </select>
            </div>
            <div className="w-full md:w-48">
              <select
                value={filterCategory}
                onChange={(e) => setFilterCategory(e.target.value)}
                className="w-full p-2 border border-gray-300 rounded-md"
              >
                <option value="">Toutes les catégories</option>
                {selectedCategories.map(categoryId => {
                  const category = THREAT_CATEGORIES.find(c => c.id === categoryId);
                  return category ? (
                    <option key={category.id} value={category.id}>{category.name}</option>
                  ) : null;
                })}
              </select>
            </div>
          </div>

          {/* Sources list */}
          {filteredSources.length === 0 ? (
            <div className="text-center py-8 bg-gray-50 rounded-lg border border-gray-200">
              <AlertCircle size={48} className="mx-auto text-gray-400 mb-4" />
              <p className="text-gray-600 font-medium">Aucune source de risque trouvée</p>
              <p className="text-gray-500 text-sm mt-1">
                {sourcesRisque.length > 0
                  ? 'Essayez de modifier vos critères de recherche ou de filtre'
                  : 'Commencez par ajouter une source de risque en utilisant le formulaire ci-dessus'}
              </p>
            </div>
          ) : (
            <div className="grid grid-cols-1 gap-4">
              {filteredSources.map((source) => (
                <div
                  key={source.id}
                  className="bg-white p-4 rounded-lg border border-gray-200 shadow-sm hover:shadow-md transition-shadow"
                >
                  {editingSource && editingSource.id === source.id ? (
                    // Edit mode
                    <div>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">Nom</label>
                          <input
                            type="text"
                            value={editingSource.name}
                            onChange={(e) => setEditingSource({ ...editingSource, name: e.target.value })}
                            className="w-full p-2 border border-gray-300 rounded-md"
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">Catégorie</label>
                          <select
                            value={editingSource.category || ''}
                            onChange={(e) => setEditingSource({ ...editingSource, category: e.target.value })}
                            className="w-full p-2 border border-gray-300 rounded-md"
                          >
                            <option value="">Sélectionner une catégorie</option>
                            {selectedCategories.map(categoryId => {
                              const category = THREAT_CATEGORIES.find(c => c.id === categoryId);
                              return category ? (
                                <option key={category.id} value={category.id}>{category.name}</option>
                              ) : null;
                            })}
                          </select>
                        </div>
                      </div>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">Objectif Visé</label>
                          <select
                            value={editingSource.objectifVise}
                            onChange={(e) => setEditingSource({ ...editingSource, objectifVise: e.target.value })}
                            className="w-full p-2 border border-gray-300 rounded-md"
                          >
                            <option value="confidentialite">Confidentialité</option>
                            <option value="integrite">Intégrité</option>
                            <option value="disponibilite">Disponibilité</option>
                            <option value="auditabilite">Auditabilité</option>
                            <option value="preuve">Preuve</option>
                            <option value="tracabilite">Traçabilité</option>
                          </select>
                        </div>
                        <div>
                          <SliderSelector
                            label="Ressources"
                            value={editingSource.ressources}
                            onChange={(value) => setEditingSource({ ...editingSource, ressources: value })}
                            options={[
                              { value: 'faibles', label: 'Faibles' },
                              { value: 'moyennes', label: 'Moyennes' },
                              { value: 'importantes', label: 'Importantes' }
                            ]}
                            colorMap={resourcesColorMap}
                          />
                        </div>
                      </div>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">Description</label>
                          <textarea
                            value={editingSource.description}
                            onChange={(e) => setEditingSource({ ...editingSource, description: e.target.value })}
                            className="w-full p-2 border border-gray-300 rounded-md"
                            rows="2"
                          ></textarea>
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">Activité</label>
                          <textarea
                            value={editingSource.activite || ''}
                            onChange={(e) => setEditingSource({ ...editingSource, activite: e.target.value })}
                            className="w-full p-2 border border-gray-300 rounded-md"
                            rows="2"
                            placeholder="Activité de la source de risque"
                          ></textarea>
                        </div>
                      </div>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                        <div>
                          <SliderSelector
                            label="Capacité"
                            value={editingSource.capacite}
                            onChange={(value) => setEditingSource({ ...editingSource, capacite: value })}
                            options={[
                              { value: 'faible', label: 'Faible' },
                              { value: 'moyenne', label: 'Moyenne' },
                              { value: 'forte', label: 'Forte' }
                            ]}
                            colorMap={capacityColorMap}
                          />
                        </div>
                        <div>
                          <SliderSelector
                            label="Motivation"
                            value={editingSource.motivation || 'faible'}
                            onChange={(value) => setEditingSource({ ...editingSource, motivation: value })}
                            options={[
                              { value: 'faible', label: 'Faible' },
                              { value: 'moyen', label: 'Moyen' },
                              { value: 'eleve', label: 'Élevé' }
                            ]}
                            colorMap={{
                              'faible': 'bg-green-600',
                              'moyen': 'bg-yellow-600',
                              'eleve': 'bg-red-600'
                            }}
                          />
                        </div>
                      </div>
                      <div className="flex justify-end space-x-2">
                        <button
                          onClick={handleCancelEdit}
                          className="bg-gray-200 text-gray-800 px-4 py-2 rounded-md hover:bg-gray-300 flex items-center"
                        >
                          <X size={16} className="mr-2" />
                          Annuler
                        </button>
                        <button
                          onClick={handleSaveEdit}
                          className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 flex items-center"
                        >
                          <Check size={16} className="mr-2" />
                          Enregistrer
                        </button>
                      </div>
                    </div>
                  ) : (
                    // View mode
                    <div>
                      <div className="flex justify-between items-start">
                        <div>
                          <div className="flex items-center">
                            <h3 className="text-lg font-semibold text-gray-800">{source.name}</h3>
                            <span className={`ml-2 px-2 py-0.5 rounded-full text-xs ${getObjectifBadgeColor(source.objectifVise)}`}>
                              {source.objectifVise === 'confidentialite' ? 'Confidentialité' :
                               source.objectifVise === 'integrite' ? 'Intégrité' :
                               source.objectifVise === 'disponibilite' ? 'Disponibilité' :
                               source.objectifVise === 'auditabilite' ? 'Auditabilité' :
                               source.objectifVise === 'preuve' ? 'Preuve' :
                               source.objectifVise === 'tracabilite' ? 'Traçabilité' :
                               source.objectifVise}
                            </span>
                          </div>
                          <p className="text-gray-600 mt-1">{source.description}</p>
                        </div>
                        <div className="flex space-x-1">
                          <button
                            onClick={() => handleEditSource(source)}
                            className="p-1.5 text-blue-600 hover:bg-blue-50 rounded-full"
                            title="Modifier"
                          >
                            <Edit size={16} />
                          </button>
                          <button
                            onClick={() => handleDeleteSource(source.id)}
                            className="p-1.5 text-red-600 hover:bg-red-50 rounded-full"
                            title="Supprimer"
                          >
                            <Trash2 size={16} />
                          </button>
                        </div>
                      </div>
                      <div className="mt-4 grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div className="bg-gray-50 p-3 rounded-lg">
                          <p className="text-xs font-semibold text-gray-700 uppercase mb-2">RESSOURCES</p>
                          <div className="flex items-center">
                            <span className={`inline-block px-2 py-0.5 rounded-full text-xs mr-2 ${getResourcesColor(source.ressources)}`}>
                              {source.ressources.charAt(0).toUpperCase() + source.ressources.slice(1)}
                            </span>
                            <span className={`inline-block px-2 py-0.5 rounded-full text-xs ${getCapacityColor(source.capacite)}`}>
                              Capacité: {source.capacite.charAt(0).toUpperCase() + source.capacite.slice(1)}
                            </span>
                          </div>
                        </div>
                        <div className="bg-gray-50 p-3 rounded-lg">
                          <p className="text-xs font-semibold text-gray-700 uppercase mb-2">ACTIVITÉ</p>
                          <p className="text-sm text-gray-600">{source.activite || 'Non spécifiée'}</p>
                        </div>
                        <div className="bg-gray-50 p-3 rounded-lg">
                          <p className="text-xs font-semibold text-gray-700 uppercase mb-2">MOTIVATION</p>
                          <div className="flex items-center">
                            <span className={`inline-block px-2 py-0.5 rounded-full text-xs mr-2 ${getMotivationColor(source.motivation)}`}>
                              {source.motivation === 'faible' ? 'Faible' : source.motivation === 'moyen' ? 'Moyen' : source.motivation === 'eleve' ? 'Élevé' : source.motivation}
                            </span>
                            <span className="text-sm text-gray-600">{source.category ? THREAT_CATEGORIES.find(c => c.id === source.category)?.name || source.category : 'Non spécifiée'}</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}
        </div>
      )}
      {/* AI Suggestions Modal */}
      {isAiModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] flex flex-col">
            <div className="p-4 border-b flex justify-between items-center">
              <h3 className="text-xl font-semibold text-gray-800 flex items-center">
                <Sparkles size={20} className="mr-2 text-purple-500" />
                Suggestions IA de Sources de Risque
              </h3>
              <button
                onClick={() => setIsAiModalOpen(false)}
                className="text-gray-500 hover:text-gray-700"
              >
                <X size={20} />
              </button>
            </div>

            <div className="p-4 overflow-auto flex-grow">
              {isLoadingAi ? (
                <div className="flex flex-col items-center justify-center h-64">
                  <RefreshCw size={40} className="text-purple-500 animate-spin mb-4" />
                  <p className="text-gray-600">Génération des suggestions IA en cours...</p>
                </div>
              ) : aiSuggestions.length === 0 ? (
                <div className="flex flex-col items-center justify-center h-64">
                  <AlertCircle size={40} className="text-gray-400 mb-4" />
                  <p className="text-gray-600">Aucune suggestion disponible</p>
                </div>
              ) : (
                <div className="space-y-4">
                  <p className="text-sm text-gray-600 mb-4">
                    Sélectionnez les suggestions que vous souhaitez ajouter à votre analyse.
                    {selectedAiSuggestions.length > 0 && (
                      <span className="ml-2 font-medium text-purple-600">
                        {selectedAiSuggestions.length} sélectionnée(s)
                      </span>
                    )}
                  </p>

                  {aiSuggestions.map((suggestion, index) => {
                    const isSelected = selectedAiSuggestions.some(s => s.name === suggestion.name);
                    return (
                      <div
                        key={index}
                        className={`p-4 rounded-lg border ${isSelected ? 'border-purple-500 bg-purple-50' : 'border-gray-200 hover:border-purple-300'} cursor-pointer transition-colors`}
                        onClick={() => handleToggleAiSuggestion(suggestion)}
                      >
                        <div className="flex justify-between items-start">
                          <div className="flex-1">
                            <div className="flex items-center">
                              <h4 className="font-medium text-gray-800">{suggestion.name}</h4>
                              <span className={`ml-2 px-2 py-0.5 rounded-full text-xs ${getObjectifBadgeColor(suggestion.objectifVise || suggestion.objectifViseCategory || 'confidentialite')}`}>
                                {suggestion.objectifVise || suggestion.objectifViseCategory || 'Confidentialité'}
                              </span>
                              <span className="ml-2 px-2 py-0.5 rounded-full text-xs bg-gray-100 text-gray-800">
                                {suggestion.category || 'Non catégorisé'}
                              </span>
                            </div>
                            <p className="text-sm text-gray-600 mt-1">{suggestion.description}</p>
                          </div>
                          <div className={`w-5 h-5 rounded-full border flex-shrink-0 ${isSelected ? 'bg-purple-500 border-purple-500' : 'border-gray-300'}`}>
                            {isSelected && <Check size={16} className="text-white m-auto" />}
                          </div>
                        </div>

                        <div className="mt-3 grid grid-cols-1 md:grid-cols-3 gap-2">
                          <div className="flex items-center">
                            <span className="text-xs font-medium text-gray-500 mr-2">Ressources:</span>
                            <span className={`inline-block px-2 py-0.5 rounded-full text-xs ${getResourcesColor(suggestion.ressources || 'faibles')}`}>
                              {suggestion.ressources === 'faibles' ? 'Faibles' :
                               suggestion.ressources === 'moyennes' ? 'Moyennes' :
                               suggestion.ressources === 'importantes' ? 'Importantes' :
                               suggestion.ressources || 'Faibles'}
                            </span>
                          </div>
                          <div className="flex items-center">
                            <span className="text-xs font-medium text-gray-500 mr-2">Activité:</span>
                            <span className={`inline-block px-2 py-0.5 rounded-full text-xs ${getMotivationColor(suggestion.activite || 'faible')}`}>
                              {suggestion.activite === 'faible' ? 'Faible' :
                               suggestion.activite === 'moyen' ? 'Moyen' :
                               suggestion.activite === 'eleve' ? 'Élevé' :
                               suggestion.activite || 'Faible'}
                            </span>
                          </div>
                          <div className="flex items-center">
                            <span className="text-xs font-medium text-gray-500 mr-2">Motivation:</span>
                            <span className={`inline-block px-2 py-0.5 rounded-full text-xs ${getMotivationColor(suggestion.motivation || 'faible')}`}>
                              {suggestion.motivation === 'faible' ? 'Faible' :
                               suggestion.motivation === 'moyen' ? 'Moyen' :
                               suggestion.motivation === 'eleve' ? 'Élevé' :
                               suggestion.motivation || 'Faible'}
                            </span>
                          </div>
                        </div>
                      </div>
                    );
                  })}
                </div>
              )}
            </div>

            <div className="p-4 border-t flex justify-end space-x-2">
              <button
                onClick={() => setIsAiModalOpen(false)}
                className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
              >
                Annuler
              </button>
              <button
                onClick={handleAddAiSuggestions}
                disabled={selectedAiSuggestions.length === 0 || isLoadingAi}
                className="px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Ajouter {selectedAiSuggestions.length > 0 ? `(${selectedAiSuggestions.length})` : ''}
              </button>
            </div>
          </div>
        </div>
      )}
    </motion.div>
  );
};

export default SourcesRisques;
