// src/components/Atelier3/Activite1/GuideModal.js
import React from 'react';
import { X, Users, Target, Activity, Shield, AlertTriangle, BarChart2 } from 'lucide-react';
import { useTranslation } from 'react-i18next';

const GuideModal = ({ isOpen, onClose }) => {
  const { t } = useTranslation();
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen p-4">
        {/* Backdrop */}
        <div
          className="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm"
          onClick={onClose}
        ></div>

        {/* Modal */}
        <div className="bg-white rounded-xl shadow-xl max-w-3xl w-full max-h-[90vh] overflow-hidden relative z-10">
          {/* Header */}
          <div className="p-6 bg-gradient-to-r from-blue-600 to-indigo-700 flex justify-between items-center">
            <div className="flex items-center text-white">
              <Users size={24} className="mr-3" />
              <h2 className="text-xl font-bold">{t('workshop3.activity1.guide.title')}</h2>
            </div>
            <button
              onClick={onClose}
              className="text-white hover:bg-white hover:bg-opacity-20 rounded-full p-2 transition-colors"
            >
              <X size={20} />
            </button>
          </div>

          {/* Content */}
          <div className="p-6 overflow-y-auto max-h-[calc(90vh-80px)]">
            <div className="prose max-w-none">
              <p className="text-gray-600 mb-6">
                {t('workshop3.activity1.guide.description')}
              </p>

              <h3 className="text-lg font-medium text-gray-900 mb-4">{t('workshop3.activity1.guide.stepsToFollow')}</h3>

              <div className="space-y-6">
                <div className="flex">
                  <div className="flex-shrink-0 mt-1">
                    <div className="flex items-center justify-center w-8 h-8 rounded-full bg-blue-100">
                      <Users size={20} className="text-blue-500" />
                    </div>
                  </div>
                  <div className="ml-4">
                    <h4 className="text-base font-medium text-gray-900">{t('workshop3.activity1.guide.step1Title')}</h4>
                    <p className="mt-1 text-sm text-gray-600">
                      {t('workshop3.activity1.guide.step1Description')}
                    </p>
                  </div>
                </div>

                <div className="flex">
                  <div className="flex-shrink-0 mt-1">
                    <div className="flex items-center justify-center w-8 h-8 rounded-full bg-blue-100">
                      <Target size={20} className="text-orange-500" />
                    </div>
                  </div>
                  <div className="ml-4">
                    <h4 className="text-base font-medium text-gray-900">{t('workshop3.activity1.guide.step2Title')}</h4>
                    <p className="mt-1 text-sm text-gray-600">
                      {t('workshop3.activity1.guide.step2Description')}
                    </p>
                  </div>
                </div>

                <div className="flex">
                  <div className="flex-shrink-0 mt-1">
                    <div className="flex items-center justify-center w-8 h-8 rounded-full bg-blue-100">
                      <Shield size={20} className="text-green-500" />
                    </div>
                  </div>
                  <div className="ml-4">
                    <h4 className="text-base font-medium text-gray-900">{t('workshop3.activity1.guide.step3Title')}</h4>
                    <p className="mt-1 text-sm text-gray-600">
                      {t('workshop3.activity1.guide.step3Description')}
                    </p>
                  </div>
                </div>

                <div className="flex">
                  <div className="flex-shrink-0 mt-1">
                    <div className="flex items-center justify-center w-8 h-8 rounded-full bg-blue-100">
                      <AlertTriangle size={20} className="text-red-500" />
                    </div>
                  </div>
                  <div className="ml-4">
                    <h4 className="text-base font-medium text-gray-900">{t('workshop3.activity1.guide.step4Title')}</h4>
                    <p className="mt-1 text-sm text-gray-600">
                      {t('workshop3.activity1.guide.step4Description')}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Footer */}
          <div className="p-4 bg-gray-50 border-t border-gray-200 flex justify-end">
            <button
              onClick={onClose}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
            >
{t('common.close')}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default GuideModal;
