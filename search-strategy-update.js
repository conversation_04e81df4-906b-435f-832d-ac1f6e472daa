// Updated search strategy for ctiService.js
// This replaces the searchVulnerabilities function with keyword-first approach

const newSearchStrategy = `
      // Try multiple search strategies in order of effectiveness
      let vulnerabilities = [];
      
      // Strategy 1: Keyword search (most likely to find results)
      console.log(\`[CTI] Starting with keyword search for \${asset.name}\`);
      vulnerabilities = await this.searchByKeywords(asset);
      
      // Strategy 2: If few results, try virtual CPE match (partial CPE)
      if (vulnerabilities.length < 3) {
        console.log(\`[CTI] Found \${vulnerabilities.length} with keywords, trying virtual CPE for \${asset.name}\`);
        const virtualResults = await this.searchByVirtualCPE(asset);
        // Merge results, avoiding duplicates
        const existingIds = new Set(vulnerabilities.map(v => v.id));
        const newResults = virtualResults.filter(v => !existingIds.has(v.id));
        vulnerabilities = [...vulnerabilities, ...newResults];
      }
      
      // Strategy 3: If still few results, try exact CPE
      if (vulnerabilities.length < 5) {
        console.log(\`[CTI] Found \${vulnerabilities.length} total, trying exact CPE for \${asset.name}\`);
        const cpeResults = await this.searchByCPE(asset);
        const existingIds = new Set(vulnerabilities.map(v => v.id));
        const newResults = cpeResults.filter(v => !existingIds.has(v.id));
        vulnerabilities = [...vulnerabilities, ...newResults];
      }
`;

// Also need to add the searchByVirtualCPE function
const virtualCPEFunction = `
  // Search by virtual CPE (partial CPE with wildcards)
  async searchByVirtualCPE(asset) {
    if (!asset.vendor) return [];

    // Create virtual CPE with wildcards for broader matching
    const virtualCPE = \`cpe:2.3:*:\${asset.vendor.toLowerCase()}:*\`;
    console.log(\`[CTI] Searching by virtual CPE: \${virtualCPE}\`);

    const params = new URLSearchParams({
      virtualMatchString: virtualCPE,
      resultsPerPage: '10',
      startIndex: '0'
    });

    try {
      const response = await fetch(\`\${this.nistBaseUrl}?\${params}\`, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
        }
      });

      if (!response.ok) {
        console.error(\`[CTI] NIST virtual CPE search error: \${response.status} \${response.statusText}\`);
        return [];
      }

      const data = await response.json();
      console.log(\`[CTI] Virtual CPE search response for \${asset.name}:\`, {
        totalResults: data.totalResults,
        vulnerabilitiesCount: data.vulnerabilities?.length || 0
      });

      return this.parseVulnerabilities(data);
    } catch (error) {
      console.error(\`[CTI] Virtual CPE search failed for \${asset.name}:\`, error);
      return [];
    }
  }
`;

console.log('Search strategy update ready!');
console.log('New strategy prioritizes:');
console.log('1. Keyword search (most effective)');
console.log('2. Virtual CPE (partial matching)');
console.log('3. Exact CPE (fallback)');
