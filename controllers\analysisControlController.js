const AnalysisControl = require('../models/AnalysisControl'); // Corrected path
const mongoose = require('mongoose');

// @desc    Get analysis controls plan for a specific analysis
// @route   GET /api/analyses/:analysisId/controls
// @access  Private (requires authentication)
exports.getAnalysisControls = async (req, res) => {
    try {
        const { analysisId } = req.params;

        if (!mongoose.Types.ObjectId.isValid(analysisId)) {
            return res.status(400).json({ success: false, message: 'Invalid Analysis ID' });
        }

        // Find the control plan associated with the analysis ID
        const analysisControls = await AnalysisControl.findOne({ analysisId: analysisId });

        if (!analysisControls) {
            // If no plan exists yet, return an empty structure
            // Or you could return 404 Not Found if preferred
            return res.status(200).json({ 
                success: true, 
                message: 'No analysis control plan found, returning default.', 
                data: { planData: {} } // Return empty planData map
            });
        }

        res.status(200).json({ success: true, data: analysisControls });

    } catch (error) {
        console.error('Error fetching analysis controls:', error);
        res.status(500).json({ success: false, message: 'Server Error fetching analysis controls' });
    }
};

// @desc    Update analysis controls plan for a specific analysis
// @route   PUT /api/analyses/:analysisId/controls
// @access  Private (requires authentication)
exports.updateAnalysisControls = async (req, res) => {
    try {
        const { analysisId } = req.params;
        const { planData } = req.body; // Expecting the planData map directly in the body

        if (!mongoose.Types.ObjectId.isValid(analysisId)) {
            return res.status(400).json({ success: false, message: 'Invalid Analysis ID' });
        }

        if (planData === undefined || planData === null) {
             return res.status(400).json({ success: false, message: 'Missing planData in request body' });
        }

        // Use findOneAndUpdate with upsert: true
        // This will create the document if it doesn't exist for the analysisId,
        // or update the planData field if it does exist.
        const updatedAnalysisControls = await AnalysisControl.findOneAndUpdate(
            { analysisId: analysisId }, // Filter by analysisId
            { 
                $set: { planData: planData }, // Set the planData field
                $setOnInsert: { analysisId: analysisId } // Ensure analysisId is set on creation
            }, 
            { 
                new: true, // Return the updated document
                upsert: true, // Create if it doesn't exist
                runValidators: true // Ensure schema validation runs
            }
        );

        if (!updatedAnalysisControls) {
             // This case should technically not happen with upsert: true, but handle defensively
            return res.status(404).json({ success: false, message: 'Analysis control plan not found and could not be created' });
        }

        res.status(200).json({ success: true, data: updatedAnalysisControls });

    } catch (error) {
        console.error('Error updating analysis controls:', error);
        if (error.name === 'ValidationError') {
            return res.status(400).json({ success: false, message: `Validation Error: ${error.message}` });
        }
        res.status(500).json({ success: false, message: 'Server Error updating analysis controls' });
    }
};

// Note: Delete functionality might be tied to deleting the entire Analysis.
// If a separate delete for just the controls is needed, it can be added here.
