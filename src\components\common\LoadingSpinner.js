// src/components/common/LoadingSpinner.jsx
import React from 'react';

/**
 * Loading spinner component with customizable size and color
 * 
 * @param {Object} props Component props
 * @param {string} [props.size='md'] Size of the spinner ('sm', 'md', 'lg')
 * @param {string} [props.color='blue'] Color of the spinner ('blue', 'green', 'red', 'yellow', 'gray')
 * @param {string} [props.label='Chargement...'] Text to display below the spinner
 * @param {boolean} [props.fullScreen=false] Whether to display the spinner in full screen
 * @returns {JSX.Element} LoadingSpinner component
 */
const LoadingSpinner = ({ 
  size = 'md', 
  color = 'blue', 
  label = 'Chargement...',
  fullScreen = false 
}) => {
  // Size classes
  const sizeClasses = {
    sm: 'h-6 w-6',
    md: 'h-10 w-10',
    lg: 'h-16 w-16'
  };
  
  // Color classes
  const colorClasses = {
    blue: 'text-blue-600',
    green: 'text-green-600',
    red: 'text-red-600',
    yellow: 'text-yellow-600',
    gray: 'text-gray-600'
  };
  
  // Container classes based on fullScreen prop
  const containerClasses = fullScreen
    ? 'fixed inset-0 flex items-center justify-center bg-white bg-opacity-80 z-50'
    : 'flex flex-col items-center justify-center';
  
  return (
    <div className={containerClasses}>
      <div className="flex flex-col items-center">
        <svg
          className={`animate-spin ${sizeClasses[size] || sizeClasses.md} ${colorClasses[color] || colorClasses.blue}`}
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
        >
          <circle
            className="opacity-25"
            cx="12"
            cy="12"
            r="10"
            stroke="currentColor"
            strokeWidth="4"
          ></circle>
          <path
            className="opacity-75"
            fill="currentColor"
            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
          ></path>
        </svg>
        {label && (
          <p className={`mt-2 text-sm font-medium ${colorClasses[color] || colorClasses.blue}`}>
            {label}
          </p>
        )}
      </div>
    </div>
  );
};

export default LoadingSpinner;