// src/components/Atelier4/OperationalScenariosPhase.js
import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { List, Info, BookOpen, ArrowRight } from 'lucide-react';
import OperationalScenarios from './Activite1/OperationalScenarios';
import { useAnalysis } from '../../context/AnalysisContext';

const OperationalScenariosPhase = () => {
  const [attackPaths, setAttackPaths] = useState([]);
  const [loading, setLoading] = useState(false);
  const { currentAnalysis } = useAnalysis();

  // Load attack paths data
  useEffect(() => {
    const loadAttackPaths = async () => {
      if (currentAnalysis?.id) {
        setLoading(true);
        try {
          // This would typically load from API
          console.log('[OperationalScenariosPhase] Loading attack paths for analysis:', currentAnalysis.id);
          
          // Mock attack paths for demonstration
          const mockAttackPaths = [
            {
              id: 'path1',
              referenceCode: 'CA01',
              sourceRiskName: 'Concurrent malveillant',
              targetObjective: 'Données clients',
              dreadedEventName: 'Vol de données personnelles',
              description: 'Attaque ciblée pour voler les données clients'
            },
            {
              id: 'path2', 
              referenceCode: 'CA02',
              sourceRiskName: 'Employé mécontent',
              targetObjective: 'Système de production',
              dreadedEventName: 'Sabotage du système',
              description: 'Sabotage interne du système de production'
            }
          ];
          
          setAttackPaths(mockAttackPaths);
        } catch (error) {
          console.error('[OperationalScenariosPhase] Error loading attack paths:', error);
          setAttackPaths([]);
        } finally {
          setLoading(false);
        }
      }
    };

    loadAttackPaths();
  }, [currentAnalysis?.id]);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-100">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-800 mb-2 flex items-center">
              <List size={28} className="mr-3 text-green-600" />
              Phase 2: Scénarios opérationnels
            </h1>
            <p className="text-gray-600">
              Création et gestion des scénarios opérationnels détaillés basés sur les chemins d'attaque.
            </p>
          </div>
          <div className="flex items-center space-x-2 text-sm text-gray-500">
            <Info size={16} />
            <span>Séquence EBIOS RM 4 phases</span>
          </div>
        </div>

        {/* EBIOS RM 4-Phase Model */}
        <div className="bg-green-50 p-4 rounded-lg border border-green-200">
          <h3 className="font-medium text-green-800 mb-3 flex items-center">
            <BookOpen size={16} className="mr-2" />
            Modèle de séquence d'attaque EBIOS RM
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 text-sm">
            <div className="bg-white p-3 rounded border border-green-100">
              <div className="font-medium text-blue-700 mb-1 flex items-center">
                <div className="w-6 h-6 bg-blue-500 text-white rounded-full flex items-center justify-center text-xs mr-2">1</div>
                CONNAITRE
              </div>
              <div className="text-gray-600 text-xs">
                Reconnaissance externe, sources ouvertes, recrutement
              </div>
            </div>
            
            <div className="bg-white p-3 rounded border border-green-100">
              <div className="font-medium text-orange-700 mb-1 flex items-center">
                <div className="w-6 h-6 bg-orange-500 text-white rounded-full flex items-center justify-center text-xs mr-2">2</div>
                RENTRER
              </div>
              <div className="text-gray-600 text-xs">
                Intrusion via mail, canal préexistant, piège physique
              </div>
            </div>
            
            <div className="bg-white p-3 rounded border border-green-100">
              <div className="font-medium text-purple-700 mb-1 flex items-center">
                <div className="w-6 h-6 bg-purple-500 text-white rounded-full flex items-center justify-center text-xs mr-2">3</div>
                TROUVER
              </div>
              <div className="text-gray-600 text-xs">
                Reconnaissance interne, latéralisation, élévation privilèges
              </div>
            </div>
            
            <div className="bg-white p-3 rounded border border-green-100">
              <div className="font-medium text-red-700 mb-1 flex items-center">
                <div className="w-6 h-6 bg-red-500 text-white rounded-full flex items-center justify-center text-xs mr-2">4</div>
                EXPLOITER
              </div>
              <div className="text-gray-600 text-xs">
                Collecte maliciel, canal exfiltration, vol données
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Attack Paths Summary */}
      {attackPaths.length > 0 && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-white p-6 rounded-xl shadow-sm border border-gray-100"
        >
          <h3 className="text-lg font-medium text-gray-800 mb-4">Chemins d'attaque disponibles</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {attackPaths.map((path) => (
              <div key={path.id} className="border border-gray-200 rounded-lg p-4 hover:border-green-300 transition-colors">
                <div className="flex items-center justify-between mb-2">
                  <span className="font-mono text-sm font-medium text-green-600">{path.referenceCode}</span>
                  <ArrowRight size={16} className="text-gray-400" />
                </div>
                <div className="space-y-1 text-sm">
                  <div><span className="font-medium">Source:</span> {path.sourceRiskName}</div>
                  <div><span className="font-medium">Objectif:</span> {path.targetObjective}</div>
                  <div><span className="font-medium">Événement:</span> {path.dreadedEventName}</div>
                </div>
              </div>
            ))}
          </div>
        </motion.div>
      )}

      {/* Operational Scenarios Component */}
      <OperationalScenarios attackPaths={attackPaths} />

      {/* Workflow Guide */}
      <div className="bg-gray-50 p-6 rounded-xl border border-gray-200">
        <h3 className="font-medium text-gray-800 mb-3">Guide du workflow</h3>
        <div className="space-y-3 text-sm text-gray-600">
          <div className="flex items-start">
            <div className="w-6 h-6 bg-blue-500 text-white rounded-full flex items-center justify-center text-xs mr-3 mt-0.5">1</div>
            <div>
              <div className="font-medium text-gray-700">Sélectionner un chemin d'attaque</div>
              <div>Choisissez un chemin d'attaque issu de l'Atelier 3 pour créer un scénario opérationnel</div>
            </div>
          </div>
          
          <div className="flex items-start">
            <div className="w-6 h-6 bg-blue-500 text-white rounded-full flex items-center justify-center text-xs mr-3 mt-0.5">2</div>
            <div>
              <div className="font-medium text-gray-700">Créer le scénario</div>
              <div>Définissez les 4 phases d'attaque avec les techniques et outils spécifiques</div>
            </div>
          </div>
          
          <div className="flex items-start">
            <div className="w-6 h-6 bg-blue-500 text-white rounded-full flex items-center justify-center text-xs mr-3 mt-0.5">3</div>
            <div>
              <div className="font-medium text-gray-700">Évaluer la vraisemblance</div>
              <div>Estimez la probabilité de réussite et la difficulté de détection</div>
            </div>
          </div>
          
          <div className="flex items-start">
            <div className="w-6 h-6 bg-blue-500 text-white rounded-full flex items-center justify-center text-xs mr-3 mt-0.5">4</div>
            <div>
              <div className="font-medium text-gray-700">Enrichir avec l'IA</div>
              <div>Utilisez le générateur IA pour enrichir le scénario avec l'intelligence des menaces</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default OperationalScenariosPhase;
