/* French flag-inspired navbar animation */
@keyframes gradientShift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.french-flag-navbar {
  animation: gradientShift 15s ease infinite;
  background-size: 200% 200%;
}

.french-flag-glow::before {
  content: '';
  position: absolute;
  top: -10px;
  left: -10px;
  right: -10px;
  bottom: -10px;
  background: radial-gradient(
    circle at 25% 50%,
    rgba(0, 85, 164, 0.3) 0%,
    transparent 40%
  ), radial-gradient(
    circle at 75% 50%,
    rgba(237, 41, 57, 0.3) 0%,
    transparent 40%
  );
  filter: blur(8px);
  z-index: -1;
  opacity: 0;
  transition: opacity 0.5s ease;
}

.french-flag-navbar:hover .french-flag-glow::before {
  opacity: 1;
}

/* Title radial gradient animation */
@keyframes pulseGradient {
  0% {
    opacity: 0.7;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 0.7;
  }
}

.title-gradient {
  animation: pulseGradient 4s ease-in-out infinite;
}
