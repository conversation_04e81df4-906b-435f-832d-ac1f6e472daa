[{"_id": {"$oid": "67e28e111ed3f1537e02fe15"}, "name": "Projet X", "description": "zg<PERSON><PERSON><PERSON>", "companyId": {"$oid": "67e0a2fc49ee0a836cb1d7bf"}, "companyName": "ACG", "createdBy": {"$oid": "67e270462fe611fd2b86ccaa"}, "createdByName": "<PERSON>", "status": "draft", "lastUpdatedBy": {"$oid": "67e02839208a45ab0a3168b5"}, "lastUpdatedByName": "Admin System", "createdAt": {"$date": "2025-03-25T11:05:53.963Z"}, "updatedAt": {"$date": "2025-04-04T00:58:13.227Z"}, "__v": 0}, {"_id": {"$oid": "67e4a998b8ec79118b11e01a"}, "name": "PROJET DE ACGNY", "description": "TEST", "companyId": {"$oid": "67e0a8c149ee0a836cb1d820"}, "companyName": "ACGNY", "createdBy": {"$oid": "67e4a836b8ec79118b11dea3"}, "createdByName": "Orange Center", "status": "draft", "lastUpdatedBy": {"$oid": "67e4a836b8ec79118b11dea3"}, "lastUpdatedByName": "Orange Center", "createdAt": {"$date": "2025-03-27T01:27:52.415Z"}, "updatedAt": {"$date": "2025-04-30T12:31:04.787Z"}, "__v": 0}, {"_id": {"$oid": "67ef307f8fc56749148e9ad6"}, "name": "Analyse 3 de tests", "description": "TEST DANALYSE 3", "companyId": {"$oid": "67e0a8c149ee0a836cb1d820"}, "companyName": "ACGNY", "createdBy": {"$oid": "67e4a836b8ec79118b11dea3"}, "createdByName": "Orange Center", "status": "draft", "lastUpdatedBy": {"$oid": "67e4a836b8ec79118b11dea3"}, "lastUpdatedByName": "Orange Center", "createdAt": {"$date": "2025-04-04T01:06:07.326Z"}, "updatedAt": {"$date": "2025-04-06T01:52:24.730Z"}, "__v": 0}, {"_id": {"$oid": "67f2e2a0a5ad300283807d49"}, "name": "Analyse pour InnovateRetail Inc.", "description": "Assessment of the primary e-commerce platform (www.innovateretail.com), including customer database, order processing system, and payment gateway integration.", "companyId": {"$oid": "67e0a8c149ee0a836cb1d820"}, "companyName": "ACGNY", "createdBy": {"$oid": "67e4a836b8ec79118b11dea3"}, "createdByName": "Orange Center", "status": "draft", "lastUpdatedBy": {"$oid": "67e4a836b8ec79118b11dea3"}, "lastUpdatedByName": "Orange Center", "createdAt": {"$date": "2025-04-06T20:22:56.819Z"}, "updatedAt": {"$date": "2025-05-02T20:06:21.904Z"}, "__v": 0}]