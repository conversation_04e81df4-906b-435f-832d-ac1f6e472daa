# EBIOS RM - Portable Creation Quick Checklist

## 🚀 Quick Reference for Creating Portable Versions

### Pre-Creation Checklist
- [ ] Latest source code committed and tagged
- [ ] All features tested and working
- [ ] Docker Desktop installed and running
- [ ] MongoDB running with latest data

### 1. Frontend Preparation
```bash
cd my-ebios-app
npm install
npm run build
```
- [ ] Build completed successfully
- [ ] `build/` folder contains latest assets
- [ ] All Atelier components included

### 2. Backend Dependencies Check
Verify `backend/package.json` includes:
- [ ] `axios` (for HTTP requests)
- [ ] `uuid` (for unique identifiers)
- [ ] All other required dependencies

### 3. Database Export
```bash
# Export all collections
mongoexport --db ebiosrm --collection users --out mongo-init/data/ebiosrm.users.json --jsonArray
mongoexport --db ebiosrm --collection companies --out mongo-init/data/ebiosrm.companies.json --jsonArray
# ... (all other collections)
```
- [ ] All collections exported
- [ ] Import script created (`mongo-init/import-data.sh`)
- [ ] Import script has execute permissions

### 4. Docker Configuration
- [ ] `docker-compose.yml` created with 3 services
- [ ] `default.conf` nginx configuration created
- [ ] Inline Dockerfiles in docker-compose are correct

### 5. Package Creation
```bash
create-portable-package.bat  # or equivalent script
```
- [ ] Package script runs successfully
- [ ] All files copied to `ebiosrm-portable/`
- [ ] Launch scripts created for Windows/Linux

### 6. Testing
```bash
cd ebiosrm-portable
docker-compose up -d --build
```
- [ ] All containers start successfully
- [ ] Frontend accessible at http://localhost:8090
- [ ] Backend API accessible at http://localhost:5050/api
- [ ] Database data imported correctly
- [ ] All Atelier components visible and functional

### 7. Final Package
- [ ] Create README.md with instructions
- [ ] Compress to .zip or .tar.gz
- [ ] Test on clean system
- [ ] Document version and requirements

## 🔧 Common Fixes

### Missing Atelier 3/4
```bash
# Rebuild frontend and update package
npm run build
xcopy build ebiosrm-portable\build\ /E /I /Y
```

### Backend Dependencies Error
```json
// Add to backend/package.json dependencies:
"axios": "^1.6.0",
"uuid": "^9.0.0"
```

### Database Import Issues
```bash
# Ensure import script is executable
chmod +x mongo-init/import-data.sh
```

## 📋 File Structure Verification

```
ebiosrm-portable/
├── docker-compose.yml     ✅
├── default.conf          ✅
├── launch.sh             ✅
├── launch.bat            ✅
├── README.md             ✅
├── backend/              ✅
│   ├── package.json      ✅ (with axios, uuid)
│   ├── server.js         ✅
│   └── ...               ✅
├── build/                ✅ (fresh build)
│   ├── index.html        ✅
│   └── static/           ✅
└── mongo-init/           ✅
    ├── import-data.sh    ✅
    └── data/             ✅ (all .json files)
```

## 🎯 Success Criteria

- [ ] Package size reasonable (< 100MB)
- [ ] Works on clean Docker installation
- [ ] All features accessible
- [ ] Database data preserved
- [ ] Cross-platform compatibility
- [ ] Clear user instructions included

---
**Use this checklist for every portable version creation!**
