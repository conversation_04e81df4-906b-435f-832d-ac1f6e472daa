# EBIOS RM Web Application Testing Guide

This guide provides step-by-step instructions and test data for manually testing the EBIOS RM web application through its user interface.

## Login Testing

### Admin Login
- **Email**: <EMAIL>
- **Password**: Admin123!

### Regular User Login
- **Email**: <EMAIL>
- **Password**: User123!

## Creating a New Analysis

1. Click "Créer une nouvelle analyse" button
2. Enter the following information:
   - **Nom de l'analyse**: Test Analyse Sécurité
   - **Description**: Analyse de sécurité pour le système d'information principal
   - **Date de début**: [Today's date]
   - **Date de fin prévue**: [Today's date + 3 months]

## Atelier 1: Socle de la cybersécurité

### Activité 1: Définir le cadre de l'analyse

#### Ajouter des valeurs métiers
1. Navigate to "Définir le cadre de l'analyse"
2. Click "Ajouter une valeur métier"
3. Enter the following for the first business value:
   - **Nom**: Système de gestion des commandes
   - **Description**: Système critique permettant la gestion des commandes clients et la facturation

4. Click "Ajouter une valeur métier" again
5. Enter the following for the second business value:
   - **Nom**: Base de données clients
   - **Description**: Stockage des informations personnelles et financières des clients

6. Click "Ajouter une valeur métier" again
7. Enter the following for the third business value:
   - **Nom**: Site web e-commerce
   - **Description**: Interface client pour les achats en ligne et la gestion des comptes

### Activité 2: Identifier les sources de risque et objectifs visés

#### Ajouter des sources de risque
1. Navigate to "Identifier les sources de risque et objectifs visés"
2. Click "Ajouter une source de risque"
3. Enter the following for the first risk source:
   - **Nom**: Cybercriminel
   - **Description**: Individu ou groupe cherchant à exploiter des vulnérabilités pour un gain financier
   - **Motivation**: Élevée
   - **Ressources**: Moyen
   - **Activité**: Élevée
   - **Capacité**: Élevée
   - **Retenu**: Oui (cocher)

4. Click "Ajouter une source de risque" again
5. Enter the following for the second risk source:
   - **Nom**: Employé malveillant
   - **Description**: Employé interne avec des intentions malveillantes
   - **Motivation**: Moyen
   - **Ressources**: Faible
   - **Activité**: Moyen
   - **Capacité**: Élevée
   - **Retenu**: Oui (cocher)

#### Ajouter des objectifs visés
1. In the same page, click "Ajouter un objectif visé"
2. Enter the following for the first objective:
   - **Nom**: Vol de données clients
   - **Description**: Extraction non autorisée des données clients sensibles
   - **Valeur métier**: Base de données clients (sélectionner dans la liste)
   - **Retenu**: Oui (cocher)

3. Click "Ajouter un objectif visé" again
4. Enter the following for the second objective:
   - **Nom**: Interruption du service e-commerce
   - **Description**: Rendre indisponible le site web pour empêcher les ventes
   - **Valeur métier**: Site web e-commerce (sélectionner dans la liste)
   - **Retenu**: Oui (cocher)

### Activité 3: Évaluer les couples SR/OV

1. Navigate to "Évaluer les couples SR/OV"
2. For the couple "Cybercriminel / Vol de données clients":
   - **Vraisemblance**: Élevée (sélectionner)
   - **Retenu**: Oui (cocher)

3. For the couple "Employé malveillant / Vol de données clients":
   - **Vraisemblance**: Moyen (sélectionner)
   - **Retenu**: Oui (cocher)

4. For the couple "Cybercriminel / Interruption du service e-commerce":
   - **Vraisemblance**: Élevée (sélectionner)
   - **Retenu**: Oui (cocher)

### Activité 4: Identifier les événements redoutés

1. Navigate to "Identifier les événements redoutés"
2. Click "Ajouter un événement redouté"
3. Enter the following for the first dreaded event:
   - **Nom**: Fuite de données clients
   - **Description**: Exfiltration de données clients sensibles incluant des informations personnelles et financières
   - **Valeur métier**: Base de données clients (sélectionner)
   - **Pilier de sécurité**: Confidentialité (sélectionner)
   - **Impacts**:
     - **Missions/Services**: Élevée
     - **Humain/Matériel/Environnemental**: Moyen
     - **Gouvernance**: Moyen
     - **Financier**: Élevée
     - **Juridique**: Élevée
     - **Image/Confiance**: Élevée
   - **Retenu**: Oui (cocher)

4. Click "Ajouter un événement redouté" again
5. Enter the following for the second dreaded event:
   - **Nom**: Indisponibilité du site e-commerce
   - **Description**: Interruption prolongée du site web empêchant les clients de passer des commandes
   - **Valeur métier**: Site web e-commerce (sélectionner)
   - **Pilier de sécurité**: Disponibilité (sélectionner)
   - **Impacts**:
     - **Missions/Services**: Élevée
     - **Humain/Matériel/Environnemental**: Faible
     - **Gouvernance**: Faible
     - **Financier**: Élevée
     - **Juridique**: Faible
     - **Image/Confiance**: Moyen
   - **Retenu**: Oui (cocher)

### Activité 5: Définir les mesures de sécurité

1. Navigate to "Définir les mesures de sécurité"
2. For the dreaded event "Fuite de données clients":
   - Click "Ajouter une mesure de sécurité"
   - Enter the following:
     - **Nom**: Chiffrement des données sensibles
     - **Description**: Mise en place d'un chiffrement fort pour toutes les données clients
     - **Type**: Préventif (sélectionner)
     - **Statut**: Planifié (sélectionner)

   - Click "Ajouter une mesure de sécurité" again
   - Enter the following:
     - **Nom**: Authentification multi-facteurs
     - **Description**: Mise en place d'une authentification à deux facteurs pour tous les accès à la base de données
     - **Type**: Préventif (sélectionner)
     - **Statut**: Implémenté (sélectionner)

3. For the dreaded event "Indisponibilité du site e-commerce":
   - Click "Ajouter une mesure de sécurité"
   - Enter the following:
     - **Nom**: Architecture redondante
     - **Description**: Mise en place d'une infrastructure redondante avec répartition de charge
     - **Type**: Préventif (sélectionner)
     - **Statut**: Planifié (sélectionner)

   - Click "Ajouter une mesure de sécurité" again
   - Enter the following:
     - **Nom**: Plan de continuité d'activité
     - **Description**: Procédures documentées pour la reprise rapide du service en cas d'incident
     - **Type**: Réactif (sélectionner)
     - **Statut**: Planifié (sélectionner)

## Atelier 2: Analyse des risques stratégiques

### Activité 1: Identifier les sources de risque et objectifs visés

#### Ajouter des sources de risque stratégiques
1. Navigate to Atelier 2 > "Identifier les sources de risque et objectifs visés"
2. Click "Ajouter une source de risque"
3. Enter the following:
   - **Nom**: Groupe APT étatique
   - **Description**: Groupe de menace persistante avancée soutenu par un état
   - **Motivation**: Élevée
   - **Ressources**: Élevée
   - **Activité**: Moyen
   - **Capacité**: Élevée
   - **Retenu**: Oui (cocher)

#### Ajouter des objectifs visés stratégiques
1. Click "Ajouter un objectif visé"
2. Enter the following:
   - **Nom**: Espionnage industriel
   - **Description**: Vol de propriété intellectuelle et secrets commerciaux
   - **Valeur métier**: Système de gestion des commandes (sélectionner)
   - **Retenu**: Oui (cocher)

## Utilisation des fonctionnalités IA

### Générer des suggestions d'événements redoutés
1. Navigate to "Identifier les événements redoutés"
2. Click on the bouton "IA" ou "Suggérer des événements"
3. Vérifier que des suggestions pertinentes sont générées

### Générer des suggestions de mesures de sécurité
1. Navigate to "Définir les mesures de sécurité"
2. Pour l'événement "Fuite de données clients", cliquer sur le bouton "IA" ou "Suggérer des mesures"
3. Sélectionner au moins deux suggestions pertinentes
4. Vérifier qu'elles sont ajoutées à la liste des mesures

## Fonctionnalités d'exportation

### Exporter un rapport
1. Navigate to "Tableau de bord" ou "Rapports"
2. Click "Exporter" ou "Générer un rapport"
3. Sélectionner le format (PDF ou Excel)
4. Vérifier que le rapport est généré correctement avec toutes les données saisies

## Vérification des données

Après avoir complété tous les tests ci-dessus, vérifier que:

1. Toutes les valeurs métiers sont visibles dans le tableau de bord
2. Les sources de risque et objectifs visés retenus sont correctement affichés
3. Les événements redoutés apparaissent avec leur niveau de gravité
4. Les mesures de sécurité sont associées aux bons événements redoutés
5. Les graphiques et visualisations reflètent correctement les données saisies
