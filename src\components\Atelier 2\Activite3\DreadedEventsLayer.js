// src/components/Atelier 2/Activite3/DreadedEventsLayer.js
import React, { useState } from 'react';
import { X, Shield, Check, Info, Search, ChevronDown, Filter } from 'lucide-react';

const DreadedEventsLayer = ({ 
  isOpen, 
  onClose, 
  sourceRisk, 
  dreadedEvents, 
  mappedDreadedEventIds, 
  handleToggleMapping,
  setSelectedDreadedEvent,
  getPillarBadgeColor
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [filterPillar, setFilterPillar] = useState('');
  
  // Get all unique pillars
  const pillars = [...new Set(dreadedEvents.map(event => event.securityPillar))];
  
  // Filter dreaded events
  const filteredEvents = dreadedEvents.filter(event => {
    const matchesSearch = event.name.toLowerCase().includes(searchTerm.toLowerCase()) || 
                         (event.description || '').toLowerCase().includes(searchTerm.toLowerCase());
    const matchesPillar = filterPillar ? event.securityPillar === filterPillar : true;
    
    return matchesSearch && matchesPillar;
  });
  
  // Group events by pillar
  const groupedEvents = filteredEvents.reduce((acc, event) => {
    const pillar = event.securityPillar || 'Autre';
    if (!acc[pillar]) acc[pillar] = [];
    acc[pillar].push(event);
    return acc;
  }, {});
  
  if (!isOpen) return null;
  
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
      <div className="bg-white rounded-xl shadow-xl w-full max-w-4xl max-h-[90vh] overflow-hidden flex flex-col">
        {/* Header */}
        <div className="p-4 bg-gradient-to-r from-blue-600 to-indigo-700 flex justify-between items-center text-white">
          <div className="flex items-center">
            <Shield className="mr-2" size={20} />
            <div>
              <h2 className="font-bold text-lg">Événements Redoutés</h2>
              <p className="text-sm text-blue-100">
                {sourceRisk.name} - {sourceRisk.category}
              </p>
            </div>
          </div>
          <button 
            onClick={onClose}
            className="p-2 rounded-full hover:bg-white hover:bg-opacity-20 transition-colors"
          >
            <X size={20} />
          </button>
        </div>
        
        {/* Search and filter */}
        <div className="p-4 border-b border-gray-200 bg-gray-50">
          <div className="flex flex-col md:flex-row gap-3">
            <div className="flex-1">
              <div className="relative">
                <input
                  type="text"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-300 focus:border-blue-400"
                  placeholder="Rechercher un événement redouté..."
                />
                <Search size={18} className="absolute left-3 top-2.5 text-gray-400" />
                {searchTerm && (
                  <button 
                    className="absolute right-3 top-2.5 text-gray-400 hover:text-gray-600"
                    onClick={() => setSearchTerm('')}
                  >
                    <X size={16} />
                  </button>
                )}
              </div>
            </div>
            <div className="md:w-64">
              <div className="relative">
                <select
                  value={filterPillar}
                  onChange={(e) => setFilterPillar(e.target.value)}
                  className="w-full pl-4 pr-10 py-2 border border-gray-300 rounded-lg appearance-none focus:ring-2 focus:ring-blue-300 focus:border-blue-400"
                >
                  <option value="">Tous les piliers</option>
                  {pillars.map((pillar) => (
                    <option key={pillar} value={pillar}>
                      {pillar.charAt(0).toUpperCase() + pillar.slice(1)}
                    </option>
                  ))}
                </select>
                <div className="absolute right-0 top-0 h-full flex items-center justify-center w-10 pointer-events-none text-gray-500">
                  <ChevronDown size={16} />
                </div>
              </div>
            </div>
          </div>
          
          {/* Selection summary */}
          <div className="mt-3 flex items-center justify-between">
            <div className="text-sm text-gray-600">
              {mappedDreadedEventIds.length} événement(s) sélectionné(s) sur {dreadedEvents.length} disponibles
            </div>
            {(searchTerm || filterPillar) && (
              <button 
                className="text-sm text-blue-600 hover:text-blue-800 flex items-center"
                onClick={() => {
                  setSearchTerm('');
                  setFilterPillar('');
                }}
              >
                <Filter size={14} className="mr-1" />
                Réinitialiser les filtres
              </button>
            )}
          </div>
        </div>
        
        {/* Content */}
        <div className="flex-1 overflow-y-auto p-4">
          {Object.keys(groupedEvents).length === 0 ? (
            <div className="text-center py-8">
              <div className="w-16 h-16 mx-auto bg-gray-100 rounded-full flex items-center justify-center mb-4">
                <Search size={24} className="text-gray-400" />
              </div>
              <h3 className="text-lg font-medium text-gray-800 mb-1">Aucun résultat trouvé</h3>
              <p className="text-gray-500">Essayez de modifier vos critères de recherche</p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {Object.entries(groupedEvents).map(([pillar, events]) => (
                <div key={pillar} className="border border-gray-200 rounded-lg overflow-hidden">
                  <div className={`px-4 py-2 ${getPillarBadgeColor(pillar)} font-medium`}>
                    {pillar.charAt(0).toUpperCase() + pillar.slice(1)}
                    <span className="ml-2 text-xs bg-white bg-opacity-30 px-2 py-0.5 rounded-full">
                      {events.length}
                    </span>
                  </div>
                  <div className="divide-y divide-gray-100">
                    {events.map(event => (
                      <div 
                        key={event.id} 
                        className={`p-3 hover:bg-gray-50 ${mappedDreadedEventIds.includes(event.id) ? 'bg-blue-50' : ''}`}
                      >
                        <div className="flex items-start">
                          <div 
                            className={`mt-0.5 w-5 h-5 rounded border flex-shrink-0 flex items-center justify-center mr-3 cursor-pointer ${
                              mappedDreadedEventIds.includes(event.id) 
                                ? 'bg-blue-600 border-blue-600' 
                                : 'border-gray-300'
                            }`}
                            onClick={() => handleToggleMapping(sourceRisk.id, event.id)}
                          >
                            {mappedDreadedEventIds.includes(event.id) && (
                              <Check size={14} className="text-white" />
                            )}
                          </div>
                          <div className="flex-1">
                            <div className="flex items-center justify-between">
                              <h3 className="font-medium text-gray-800">{event.name}</h3>
                              <button
                                className="ml-2 p-1 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-full"
                                onClick={() => setSelectedDreadedEvent(event)}
                                title="Voir les détails"
                              >
                                <Info size={16} />
                              </button>
                            </div>
                            {event.description && (
                              <p className="text-sm text-gray-600 mt-1 line-clamp-2">{event.description}</p>
                            )}
                            {event.severity && (
                              <div className="mt-2">
                                <span className="text-xs text-gray-500">Gravité: </span>
                                <span className={`text-xs px-2 py-0.5 rounded-full ${
                                  event.severity === 'minor' ? 'bg-green-100 text-green-800' :
                                  event.severity === 'moderate' ? 'bg-yellow-100 text-yellow-800' :
                                  event.severity === 'major' ? 'bg-orange-100 text-orange-800' :
                                  event.severity === 'critical' ? 'bg-red-100 text-red-800' :
                                  event.severity === 'catastrophic' ? 'bg-purple-100 text-purple-800' :
                                  'bg-gray-100 text-gray-800'
                                }`}>
                                  {event.severity === 'minor' ? 'Mineure' :
                                   event.severity === 'moderate' ? 'Modérée' :
                                   event.severity === 'major' ? 'Majeure' :
                                   event.severity === 'critical' ? 'Critique' :
                                   event.severity === 'catastrophic' ? 'Catastrophique' :
                                   event.severity}
                                </span>
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
        
        {/* Footer */}
        <div className="p-4 bg-gray-50 border-t border-gray-200 flex justify-between items-center">
          <div className="text-sm text-gray-600">
            {mappedDreadedEventIds.length} événement(s) sélectionné(s)
          </div>
          <button 
            onClick={onClose}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            Fermer
          </button>
        </div>
      </div>
    </div>
  );
};

export default DreadedEventsLayer;
