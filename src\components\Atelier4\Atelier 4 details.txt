 Élaborer des graphes d’attaque
 (atelier 4)
Un scénario opérationnel peut être représenté sous la forme d’un graphe 
d’attaque permettant de visualiser les modes opératoires planifiés par 
l’attaquant pour atteindre son objectif. Le graphe d’attaque se présente 
sous la forme d’un enchaînement d’actions élémentaires sur des biens 
supports. Plusieurs modes opératoires peuvent être réalisés par la source de 
risque pour atteindre son objectif visé : ils sont représentés par des chaînes 
séquentielles différentes avant d’atteindre l’étape finale. Un exemple de 
scénario opérationnel est donné ci-après.
 Séquence d'attaque type
 Connaitre
 Reconnaissance 
externe sources 
ouvertes
 2
 ▲
 Reconnaissance 
externe avancée
 rentrer
 ▲
 Intrusion via un 
canal d'accès 
préexistant
 1
 ▲
 Intrusion via mail 
de hameçonnage 
sur service RH
 ▲
 Intrusion via le site 
du CE (point d'eau)
 ▲
 ▲
 Corruption d'un 
personnel de 
l'équipe R&D
 trouver
 ▲
 Reconnaissance 
interne réseaux 
bureautique & IT 
site de Paris
 exploiter
 ▲
 Exploitation 
maliciel de collecte 
et d'exfiltration
 ▲
 Latéralisation 
vers réseaux 
LAN et R&D
 3
 ▲
 ▲
 Modes opératoires
 possibles
 ▲
 Corruption d'un 
prestataire d'entre
tien des locaux
 Clé USB piégée 
connectée sur un 
poste R&D
 Création et main
tien d'un canal 
d'exfiltration via un 
poste internet
 ▲
 ▲
 ▲
 ▲
 ▲
 Vol et exploitation de 
données R&D
 Étape finale
 Action 
élémentaire
 38 — EBIOS RISK MANAGER - LE SUPPLÉMENT / FICHE MÉTHODE 7
1 / Modèle de séquence d’attaque 
Les scénarios opérationnels peuvent être structurés selon une séquence 
d’attaque type. Le modèle proposé s’articule autour de 4 phases :
 CONNAITRE : ensemble des activités de ciblage, de reconnaissance et de découverte 
externes menées par l’attaquant pour préparer son attaque et accroître ses 
chances de succès (cartographie de l’écosystème, recherche d’information sur les 
personnes et les systèmes clés, recherche et évaluation de vulnérabilités, etc.). Ces 
informations sont collectées par tous les moyens possibles selon la détermination et 
les ressources de l’attaquant : renseignement, intelligence économique, exploitation 
des réseaux socioprofessionnels, approches directes, officines spécialisées pour 
obtenir de l’information inaccessible en source ouverte, etc.
 RENTRER : ensemble des activités menées par l’attaquant pour s’introduire 
numériquement ou physiquement, soit directement et frontalement dans le 
système d’information de la cible, soit dans celui de son écosystème en vue 
d’une attaque par rebond. L’intrusion se fait généralement via des biens supports 
périmétriques qui servent de points d’entrée du fait de leur exposition 
 EXEMPLE : poste utilisateur connecté à Internet, tablette de maintenance 
d’un prestataire, imprimante télé-maintenue, etc.
 TROUVER : ensemble des activités de reconnaissance interne des réseaux et 
systèmes, de latéralisation, d’élévation de privilèges et de persistance qui permettent 
à l’attaquant de localiser les données et biens supports recherchés. Lors de cette 
phase, l’attaquant cherche généralement à rester discret et à ne pas laisser de traces.
 EXPLOITER : ensemble des activités d’exploitation des données et biens supports 
trouvés dans l’étape précédente. Par exemple, dans le cas d’une opération de 
sabotage, cette phase inclut le déclenchement de la charge active, dans le cas 
d’une opération d’espionnage visant à exfiltrer des mails, il peut s’agir de mettre 
en place et maintenir la capacité discrète de recueil et d’exfiltration des données.
 EXEMPLE : rançongiciel.
 FICHE MÉTHODE 7 / EBIOS RISK MANAGER - LE SUPPLÉMENT — 39
Vous pouvez adopter des modèles de séquences d’attaque plus sophistiqués 
et les décliner en variantes selon la technique d’attaque de la source de risque 
pour atteindre son objectif (exfiltration d’information, écoute passive, déni 
de service, rançongiciel, etc.).
 NOTE : pour la phase « Rentrer », nous vous recommandons de distinguer 
dans le séquencement les actions élémentaires pour s’introduire dans 
les systèmes d’information de l’écosystème, et celles portant sur les 
biens supports de l’objet de l’étude. S’agissant de l’écosystème, vous 
ne pourrez pas toujours décrire avec précision quels seront les biens 
supports ciblés chez la partie prenante concernée. Dans ce cas, restez 
à un niveau de description macroscopique et fonctionnel (exemple : SI 
bureautique, chaîne de production). 
2 / Catégories d’actions élémentaires et moyens 
mis en œuvre
 L’illustration ci-après propose une catégorisation d’actions élémentaires en 
lien avec le modèle de séquence d’attaque proposé précédemment. Les moyens 
et techniques couramment observés sont précisés pour chaque catégorie 
d’actions élémentaires (puces ). N’hésitez pas à adapter cette base à votre 
contexte et à l’enrichir de toute information issue de vos activités de veille.
 EXEMPLE : exploitation des bulletins du CERT-FR et des bulletins de veille 
des cyberattaques.
 40 — EBIOS RISK MANAGER - LE SUPPLÉMENT / FICHE MÉTHODE 7
Connaître
 Recrutement
 d'une source
 Reconnaissance
 externe
 Rentrer
 Recrutement
 d'une source
 Intrusion 
depuis Internet
 Intrusion ou
 piège physique 
Trouver
 Recrutement 
d'une source
 Reconnaissance
 interne
 Latéralisation et
 élévation de 
privilèges 
Exploiter
 Recrutement
 d'une source
 Pilotage et 
exploitation de
 l'attaque
 NOTE : dans certains cas, lorsqu’un canal d’exfiltration est nécessaire et 
diffère du canal d’infiltration, une intrusion depuis Internet ou par 
piège physique peut être réalisée dans la phase « Exploiter ». L’intrusion 
initiale peut par exemple être réalisée via Internet, mais l’exfiltration 
via un canal physique ad hoc mis en place (cas des systèmes isolés).
 RECRUTEMENT D’UNE SOURCE, CORRUPTION DE PERSONNEL
 Une opération de « recrutement » d’une source à l’intérieur de 
l’organisation ou y ayant accès peut être longue et complexe, 
mais très utile pour mettre en place un piège matériel ou obtenir des 
informations sur le système ciblé. Les raisons poussant une cible à trahir 
son entité d’origine – potentiellement à son insu – sont couvertes par quatre 
grandes catégories, dites « MICE » (Money, Ideology, Compromission, Ego). 
Des officines spécialisées en matière de recrutement de sources existent.
 FICHE MÉTHODE 7 / EBIOS RISK MANAGER - LE SUPPLÉMENT — 41
RECONNAISSANCE EXTERNE DE LA CIBLE
 Lors de la phase de reconnaissance, la source de risque va rechercher 
dans l’ensemble de ses bases disponibles les informations nécessaires 
à la planification de son attaque. Les données collectées peuvent être de 
nature technique ou concerner l’organisation de la cible et de son écosystème.  
Les moyens employés peuvent être très variés :
 ◆ réseaux sociaux (social engineering) ;
 ◆ Internet (poubelles numériques, sites) ;
 ◆ forums de discussion sur Internet ;
 ◆ forums et salons professionnels ;
 ◆ faux client, faux journaliste, etc. ;
 ◆ prise de contact directe (anciens salariés, etc.) ;
 ◆ officines ou agences spécialisées (sources non ouvertes) ;
 ◆ renseignement d’origine électromagnétique (interceptions).
 INTRUSION DEPUIS INTERNET OU DES RÉSEAUX 
INFORMATIQUES TIERS
 L’intrusion initiale a pour objectif d’introduire un outil malveillant 
dans le système d’information ciblé ou dans un autre appartenant à l’écosystème 
(par exemple la chaîne d’approvisionnement – supply chain), en général au 
niveau d’un bien support plus particulièrement exposé. Idéalement pour 
l’attaquant, l’intrusion initiale de l’outil malveillant est réalisée depuis Internet. 
Les techniques et vecteurs d’intrusion les plus couramment utilisés sont : 
◆ les attaques directes à l’encontre des services exposés sur Internet ;
 ◆ les mails d’hameçonnage (phishing) ou de harponnage (spearfishing) ;
 ◆ les attaques via des serveurs spécifiquement administrés à cet effet ou  
compromis (attaques dites par point d’eau ou waterhole) ;
 ◆ le piège d’une mise à jour a priori légitime. 
42 — EBIOS RISK MANAGER - LE SUPPLÉMENT / FICHE MÉTHODE 7
INTRUSION OU PIÈGE PHYSIQUE
 Cette méthode d’intrusion est utilisée pour accéder physiquement 
à des ressources du système d’information afin de le compromettre. 
Elle peut être réalisée par une personne externe ou simplifiée par le recrutement 
d’une source interne à l’organisation ciblée. L’intrusion physique est notamment 
utile à l’attaquant qui souhaite accéder à un système isolé d’Internet, ce qui 
nécessite de franchir un ou plusieurs air gaps. Des techniques d’intrusion 
physique couramment utilisées sont données ci-après : 
◆ connaissance des identifiants de connexion ;
 ◆ compromission de la machine (exemple : clé USB piégée) ;
 ◆ connexion au réseau d’un matériel externe au système d’information ;
 ◆ intrusion via un réseau sans fil mal sécurisé ;
 ◆ piège d’un matériel en amont via la chaîne d’approvisionnement   
(attaque dite de la supply chain) ;
 ◆ utilisation abusive de moyens d’accès légitimes au système d’information.
 EXEMPLE : vol et utilisation du téléphone portable professionnel d’un 
personnel.
 RECONNAISSANCE INTERNE
 En général, à l’issue de l’intrusion initiale, l’attaquant se retrouve 
dans un environnement de type réseaux locaux dont les accès 
peuvent être contrôlés par des mécanismes d’annuaires (Active Directory, 
OpenLDAP, etc.). De fait, il doit mener des activités de reconnaissance 
interne lui permettant de cartographier l’architecture réseau, identifier les 
mécanismes de protection et de défense mis en place, recenser les vulnérabilités 
exploitables, etc. Lors de cette étape, l’attaquant cherche à localiser les 
services, informations et biens supports, objets de l’attaque. Les techniques 
FICHE MÉTHODE 7 / EBIOS RISK MANAGER - LE SUPPLÉMENT — 43
de reconnaissance interne ci-après sont largement utilisées :
 ◆ cartographie des réseaux et systèmes pour mener la propagation (scan  
réseau) ;
 ◆ cartographie avancée (exemple : dump mémoire) ;
 ◆ recherche de vulnérabilités (par exemple pour faciliter la propagation) ;
 ◆ accès à des données système critiques (plan d’adressage, coffres   
forts, mots de passe, etc.) ;
 ◆ cartographie des services, bases de données et biens supports d’intérêt  
pour l’attaque ;
 ◆ dissimulation des traces ;
 ◆ utilisation de maliciel générique ou à façon permettant    
d’automatiser la reconnaissance interne.
 LATÉRALISATION ET ÉLÉVATION DE PRIVILÈGES 
À partir de son point d’accès initial, l’attaquant va mettre en 
œuvre des techniques de latéralisation et d’élévation de privilèges 
afin de progresser et de se maintenir dans le système d’information. Il s’agit 
généralement pour lui d’exploiter les vulnérabilités structurelles internes du 
système (manque de cloisonnement des réseaux, contrôle d’accès insuffisant, 
politique d’authentification peu robuste, négligences relatives à l’administration 
et à la maintenance du système d’information, absence de supervision, etc.). 
Les techniques ci-après sont largement utilisées :
 ◆ exploitation de vulnérabilités logicielles ou protocolaires (notamment  
identifiées lors de la reconnaissance) ;
 ◆ modification ou abus de droits sur des comptes clés utilisateurs,  
administrateurs, machines ; 
◆ autres techniques spécifiques : attaque par force brute, dump mémoire,   
attaque « pass-the-hash ».
 NOTE : les phases de reconnaissance interne et de latéralisation / élévation 
de privilèges sont en pratique itératives et interviennent au fur et à 
mesure que l’attaquant progresse dans le système d’information. 
44 — EBIOS RISK MANAGER - LE SUPPLÉMENT / FICHE MÉTHODE 7
PILOTAGE ET EXPLOITATION DE L’ATTAQUE
 Cette ultime étape correspond à la réalisation de l’objectif visé par 
la source de risque. Selon cet objectif, il peut par exemple s’agir 
de déclencher la charge malveillante destructrice, d’exfiltrer ou de modifier 
de l’information. L’attaque peut être ponctuelle (par exemple dans le cas 
d’une opération de sabotage) ou durable et se réaliser en toute discrétion 
(par exemple dans le cas d’une opération d’espionnage visant à régulièrement 
exfiltrer des informations). Les moyens et techniques d’exploitation d’une 
attaque vont dépendre de l’objectif visé. Dans le cas où celui-ci perdure dans 
le temps et nécessite d’être orienté, l’attaquant devra mettre en place un 
canal de pilotage, qu’il soit synchrone ou asynchrone, voire même physique 
dans le cas d’un air gap.
 Ci-après quelques exemples de techniques d’exploitation utilisées selon 
l’objectif visé :
 ESPIONNAGE
 ◆ Exfiltration de données ;
 ◆ Observation ou écoute passive à distance (drone, matériel d’écoute, etc.) ;
 ◆ Interception et exploitation de signaux parasites compromettants (menace  
TEMPEST4).
 4 La menace TEMPEST peut également être exploitée de façon active en piégeant préalablement, par 
exemple via la supply chain, un périphérique (câble, clavier, souris, vidéoprojecteur). Il devient alors 
une source de signaux parasites compromettants activable et désactivable à distance à condition de 
disposer d’émetteurs suffisamment puissants pour créer un canal de fuite.
 FICHE MÉTHODE 7 / EBIOS RISK MANAGER - LE SUPPLÉMENT — 45
ENTRAVE AU FONCTIONNEMENT (SABOTAGE, NEUTRALISATION)
 ◆ Attaque par déni de service distribué (DDoS) ;
 ◆ Atteinte à l’intégrité d’un bien support ou d’une donnée (effacement,  
chiffrement, altération) ;
 ◆ Brouillage d’un bien support (pour rendre aveugle ou neutraliser) ;
 ◆ Leurre5 d’un bien support (pour tromper ou falsifier) ;
 ◆ Systèmes industriels : envoi de commandes à risque pour la sûreté de  
fonctionnement6 ;
 ◆ Agression électromagnétique intentionnelle (AGREMI).
 LUCRATIF (FRAUDE, DÉTOURNEMENT D’USAGE, FALSIFICATION)
 ◆ Modification d’une base de données (par exemple pour dissimuler une  
activité frauduleuse) ;
 ◆ Altération ou détournement d’usage d’une application métier ou support ;
 ◆ Usurpation d’identité (dans une logique d’abus de droits) ;
 ◆ Détournement ou extorsion d’argent. 
 EXEMPLE : rançongiciel, mineur de crypto monnaie.
 INFLUENCE (AGITATION, PROPAGANDE, DÉSTABILISATION)
 ◆ Défiguration de sites Internet ;
 ◆ Diffusion de messages idéologiques via la prise de contrôle d’un canal  
d’information ;
 ◆ Usurpation d’identité (dans une logique d’atteinte à la réputation).
 5 Inclut les techniques de leurre cognitif en vue d’induire en erreur ou dissimuler une activité aux 
yeux d’un utilisateur (exemple : demande d’authentification illégitime, message d’alerte masqué).
 6 Par exemple pour entraîner une usure prématurée d’un équipement ou modifier des seuils d’alerte 
sur des paramètres de fonctionnement clés. L’évaluation fine des modes d’exploitation d’une attaque 
sur un système industriel est indissociable des analyses de sûreté de fonctionnement.
 46 — EBIOS RISK MANAGER - LE SUPPLÉMENT / FICHE MÉTHODE 7
FICHE MÉTHODE
 8
 Évaluer la vraisemblance des 
scénarios opérationnels (atelier 4)
La vraisemblance d’un scénario opérationnel reflète le degré de faisabilité 
ou de possibilité que l’un des modes opératoires de l’attaquant aboutisse 
à l’objectif visé. La vraisemblance est un indicateur d’aide à la décision. 
Combinée à la gravité, elle permet d’estimer le niveau de risque et de déduire 
la stratégie de traitement du risque.
 1 / Quelle échelle de vraisemblance utiliser ?
 Une échelle de niveaux de vraisemblance doit être comprise et utilisable par 
les personnes chargées d’évaluer la possibilité qu’un risque se concrétise. Son 
élaboration peut utilement être réalisée en collaboration avec les personnes 
qui vont estimer ces niveaux : ainsi les valeurs auront une signification 
concrète et seront cohérentes.
 Si vous ne disposez pas d’échelle de vraisemblance, établissez-en une au 
début de l’atelier 4. Pour cela, vous pouvez utiliser et adapter l’échelle 
générique ci-après. 
ÉCHELLE DE VRAISEMBLANCE D’UN SCÉNARIO OPÉRATIONNEL
 NIVEAU DE L’ÉCHELLE
 V4 – QUASI-CERTAIN
 DESCRIPTION
 La source de risque va très certainement atteindre son objectif en empruntant l’un 
des modes opératoires envisagés.
 La vraisemblance du scénario de risque est très élevée.
 V3 – TRÈS VRAISEMBLABLE La source de risque va probablement atteindre son objectif en empruntant l’un des 
modes opératoires envisagés.
 La vraisemblance du scénario de risque est élevée.
 V2 – VRAISEMBLABLE
 V0 – INVRAISEMBLABLE
 La source de risque est susceptible d’atteindre son objectif en empruntant l’un des 
modes opératoires envisagés.
 La vraisemblance du scénario de risque est significative.
 V1 – PEU VRAISEMBLABLE La source de risque a relativement peu de chances d’atteindre son objectif en 
empruntant l’un des modes opératoires envisagés.
 La vraisemblance du scénario de risque est faible.
 La source de risque a très peu de chances d’atteindre son objectif visé en empruntant 
l’un des modes opératoires envisagés.
 La vraisemblance du scénario de risque est très faible.
 48 — EBIOS RISK MANAGER - LE SUPPLÉMENT / FICHE MÉTHODE 8
NOTE : l’estimation de la vraisemblance d’un scénario opérationnel n’a 
pas vocation à être prédictive (elle ne traduit pas la probabilité que la 
source de risque réalisera son attaque selon ce scénario 7). Par contre, si 
l’attaquant décide de mener son attaque via le mode opératoire concerné, 
alors sa vraisemblance de réussite sera celle estimée.
 Le recours à une échelle de 4 ou 5 niveaux est guidé par les considérations 
suivantes :
 ■ la cohérence du nombre de niveaux entre les échelles de gravité et de  
vraisemblance (si vous utilisez une échelle de gravité à 4 niveaux, utilisez  
une échelle de vraisemblance à 4 niveaux) ;
 ■ la nécessité d’estimer plus ou moins finement ces vraisemblances.
 2 / Quelle approche choisir pour coter la 
vraisemblance du scénario opérationnel ?
 Vous pouvez envisager trois approches pour coter la vraisemblance du 
scénario opérationnel :
 ■ méthode expresse : cotation directe de la vraisemblance du scénario ;
 ■ méthode standard : cotation de la « probabilité de succès » de chaque  
action élémentaire du scénario, du point de vue de l’attaquant.
 ■ méthode avancée : en plus de la « probabilité de succès », cotation de la  
« difficulté technique » de chaque action élémentaire du scénario,  
du point de vue de l’attaquant.
 NOTE : ici la « probabilité » ne doit pas être entendue au sens mathématique 
du terme.
 7 À contrario, si ce scénario a été sélectionné après les ateliers 2 et 3, c’est qu’il est considéré comme 
pertinent.
 FICHE MÉTHODE 8 / EBIOS RISK MANAGER - LE SUPPLÉMENT — 49
a
 MÉTHODE EXPRESSE : COTATION DIRECTE DE LA VRAISEMBLANCE 
GLOBALE DU SCÉNARIO 
Dans les méthodes présentées ci-après (standard et avancée), on évalue 
la vraisemblance globale du scénario à partir de la cotation des actions 
élémentaires. La méthode expresse consiste à évaluer directement la 
vraisemblance globale du scénario, sur la base de considérations générales 
relatives à la source de risque (motivations, ressources) et à la sécurité des 
biens supports ciblés dans le scénario (exposition, vulnérabilités). La section 
« Comment coter les actions élémentaires ? » sera d’une aide précieuse pour 
l’appréciation. Il est possible de considérer séparément les modes opératoires 
envisagés dans le scénario opérationnel et d’identifier celui qui semble être 
le plus vraisemblable.
 Dans cette approche, vous pouvez :
 ■ estimer directement le niveau de vraisemblance du scénario ;
 ■ ou coter sa probabilité de succès et sa difficulté technique en vue de  
déduire par croisement la vraisemblance du scénario selon la matrice  
type présentée ci-dessous.
 DIFFICULTÉ TECHNIQUE DU SCÉNARIO OPÉRATIONNEL
 0 – NÉGLIGEABLE 1 – FAIBLE
 PROBABILITÉ DE SUCCÈS DU 
SCÉNARIO OPÉRATIONNEL
 4 – QUASI CERTAINE
 3 – TRÈS ÉLEVÉE
 2 – SIGNIFICATIVE
 1 – FAIBLE
 4
 4
 3
 4
 3
 2 – MODÉRÉE 3 – ÉLEVÉE 4 – TRÈS ÉLEVÉE
 3
 2
 3
 3
 2
 0 – TRÈS FAIBLE
 1
 2
 1
 2
 2
 1
 2
 2
 1
 0
 1
 1
 1
 0
 0
 50 — EBIOS RISK MANAGER - LE SUPPLÉMENT / FICHE MÉTHODE 8
b
 MÉTHODE STANDARD : PROBABILITÉ DE SUCCÈS DES ACTIONS 
 ÉLÉMENTAIRES
 Dans la méthode standard, vous allez coter chaque action élémentaire selon 
un indice de probabilité de succès vu de l’attaquant. L’échelle suivante peut 
être adoptée, les pourcentages de chance sont mentionnés à titre indicatif 
pour faciliter la cotation :
 ÉCHELLE DE PROBABILITÉ DE SUCCÈS D’UNE ACTION ÉLÉMENTAIRE
 NIVEAU DE L’ÉCHELLE
 4 – QUASI-CERTAINE
 3 – TRÈS ÉLEVÉE
 2 – SIGNIFICATIVE
 1 – FAIBLE
 DESCRIPTION
 Probabilité de succès quasi-certaine > 90%
 Probabilité de succès très élevée > 60%
 Probabilité de succès significative > 20%
 Probabilité de succès faible < 20%
 0 – TRÈS FAIBLE
 Probabilité de succès très faible  < 3%
 Par exemple, un indice de « 3 – très élevée » pour une action élémentaire 
d’intrusion par mail piégé (spearfishing) signifiera que vous estimez que 
l’attaquant a de très fortes chances de réussir son action, c’est-à-dire que l’un 
des utilisateurs ciblés par la campagne de spearfishing clique sur la pièce 
jointe piégée.
 NOTE : les échelles de cotation des actions élémentaires doivent avoir 
autant de niveaux que l’échelle de vraisemblance.
 FICHE MÉTHODE 8 / EBIOS RISK MANAGER - LE SUPPLÉMENT — 51
c
 MÉTHODE AVANCÉE : PROBABILITÉ DE SUCCÈS ET DIFFICULTÉ TECHNIQUE 
DES ACTIONS ÉLÉMENTAIRES
 Dans la méthode avancée, vous allez également coter la difficulté technique 
de réalisation de l’action élémentaire, du point de vue de l’attaquant. Elle 
permet d’estimer les ressources que l’attaquant devra engager pour mener 
son action et accroître ses chances de réussite. L’échelle suivante peut être 
adoptée :
 ÉCHELLE DE DIFFICULTÉ TECHNIQUE D’UNE ACTION ÉLÉMENTAIRE
 NIVEAU DE L’ÉCHELLE
 4 – TRÈS ÉLEVÉE
 3 – ÉLEVÉE
 2 – MODÉRÉE
 1 – FAIBLE
 0 – NÉGLIGEABLE
 DESCRIPTION
 Difficulté très élevée : l’attaquant engagera des ressources très importantes pour 
mener à bien son action.
 Difficulté élevée : l’attaquant engagera des ressources importantes pour mener à bien 
son action.
 Difficulté modérée : l’attaquant engagera des ressources significatives pour mener à 
bien son action.
 Difficulté faible : les ressources engagées par l’attaquant seront faibles.
 Difficulté négligeable, voire nulle : les ressources engagées par l’attaquant seront né
gligeables ou déjà disponibles.
 52 — EBIOS RISK MANAGER - LE SUPPLÉMENT / FICHE MÉTHODE 8
NOTES : 
■ La méthode avancée permet une appréciation plus fine de la 
vraisemblance : elle prend en compte le niveau d’expertise et de ressources 
dont l’attaquant aura besoin pour mener son attaque, compte tenu de la 
sécurité du système ciblé. De fait, cette méthode permet de considérer le 
retour sur investissement pour l’attaquant et donc de bâtir une stratégie 
de traitement du risque pilotée par une logique de découragement.
 ■ Les critères de cotation « difficulté technique » et « probabilité 
de succès » ne sont pas rigoureusement indépendants. Néanmoins, 
la « difficulté technique » est plus particulièrement liée au niveau de 
protection de la cible (son exposition et ses vulnérabilités), alors que 
la « probabilité de succès » est davantage influencée par son niveau de 
défense et de résilience (capacités de supervision, de réaction en cas 
d’incident et de continuité d’activité).
 3 / Méthodes standard et avancée : 
comment coter les actions élémentaires ? 
La cotation des actions élémentaires n’est pas forcément aisée. En effet, elle 
doit prendre en compte et confronter :
 ■ d’une part la motivation/détermination et les ressources/capacités de la  
source de risque ;
 ■ d’autre part la sécurité du système ciblé au sein de son écosystème.
 La cotation peut être effectuée par jugement d’expert, ce qui implique de 
disposer dans le groupe de travail d’une expertise suffisante en cyberattaques 
et d’une connaissance fine du niveau de sécurité de l’objet de l’étude au sein 
de son écosystème. Pour vous aider dans ce travail de cotation et le rendre 
plus objectif et reproductible, vous trouverez en fin de fiche les principaux 
critères pour déterminer la probabilité de succès ou la difficulté technique 
d’une action élémentaire.
 FICHE MÉTHODE 8 / EBIOS RISK MANAGER - LE SUPPLÉMENT — 53
4 / Méthodes standard et avancée : comment 
calculer la vraisemblance du scénario opéra
tionnel ?
 a
 MÉTHODE STANDARD
 Vous avez coté dans l’étape précédente chaque action élémentaire selon 
un indice de probabilité de succès. Vous pouvez évaluer l’indice global 
de probabilité de succès du scénario en appliquant la règle suivante. Le 
principe est de progresser dans un mode opératoire en évaluant de proche 
en proche à chaque action élémentaire « AEn
 » d’un nœud « n », un indice 
de probabilité cumulé intermédiaire à partir de l’indice élémentaire de « 
AEn
 » et des indices cumulés intermédiaires du nœud précédent « n-1 » :
 Indice_Pr (AEn
 ) = Min {Indice_Pr (AEn
 ), Max (Indices_Pr (AEn
 _1))}
 cumulé intermédiaire                    
cumulés intermédiaires
 L’indice global de probabilité de succès (étape finale) est obtenu en prenant 
l’indice de probabilité cumulé intermédiaire le plus élevé parmi les modes 
opératoires qui aboutissent à l’étape finale. Il correspond au(x) mode(s) 
opératoire(s) dont la chance de succès parait la plus élevée.
 NOTE : la règle de calcul ci-dessus permet une évaluation relativement 
simple et rapide de l’indice global de probabilité de succès. 
Elle trouve toutefois ses limites lorsqu’une séquence d’un mode opératoire 
comporte une longue chaîne d’étapes en série (environ une dizaine 
à titre indicatif8). L’évaluation aura alors tendance à surestimer la 
probabilité de succès du mode opératoire correspondant, aboutissant 
à une vraisemblance surestimée du scénario opérationnel. Pour les 
séquences de mode opératoire concernées, vous pouvez compenser 
cette limite en diminuant d’un niveau l’indice de probabilité cumulé 
intermédiaire obtenu en bout de séquence.
 8  Particulièrement si les actions élémentaires correspondantes ont des indices de difficulté identiques.
 54 — EBIOS RISK MANAGER - LE SUPPLÉMENT / FICHE MÉTHODE 8
La vraisemblance du scénario opérationnel obtenue à l’issue de ces 
opérations correspond à l’indice global de probabilité de succès. 
 EXEMPLE  : société de biotechnologie fabriquant des vaccins.
 L’évaluation de la vraisemblance a été réalisée avec des échelles à 4 niveaux :
 ■ Pour la probabilité de succès  : « Pr 1 » – probabilité faible à « Pr 4 » – quasi-certaine.
 ■ Pour la vraisemblance : « V1 » – peu vraisemblable à « V4 » – quasi-certain.
 Les indices de probabilité cumulés intermédiaires sont indiqués entre parenthèses et en italique.
 Connaitre
 Reconnaissance 
externe sources 
ouvertes
 Pr 3 (3)
 2
 Reconnaissance 
externe avancée
 Pr 2 (2)
 rentrer
 ▲
 Intrusion via un 
canal d'accès 
préexistant
 Pr 2 (2)
 1
 ▲
 Intrusion via mail 
de hameçonnage 
sur service RH
 Pr 4 (3)
 Intrusion via le site 
du CE (point d'eau)
 ▲
 Pr 4 (3)
 ▲
 3
 ▲
 Corruption d'un 
personnel de 
l'équipe R&D
 Pr 1 (1)
 trouver
 (3)
 ▲
 Reconnaissance 
interne réseaux 
bureautique & IT 
site de Paris
 exploiter
 ▲
 Pr 3 (3)
 Exploitation 
maliciel de collecte 
et d'exfiltration
 ▲
 Latéralisation 
vers réseaux 
LAN et R&D
 Pr 4 (3)
 Création et main
tien d'un canal 
d'exfiltration via un 
poste internet
 Pr 4 (3)
 Corruption d'un 
prestataire d'entre
tien des locaux
 Pr 3 (2)
 (1)
 ▲
 Clé USB piégée 
connectée sur un 
poste R&D
 Pr 3 (2)
 (2)
 ▲
 ▲
 ▲
 ▲
 Pr 3 (3)
 (3)
 Vol et exploitation de 
données R&D
 PR GLOBALE 3
 L’indice global de probabilité de succès du scénario est estimé à « 3 – Très élevé » : l’atteinte 
de l’objectif visé par la source de risque selon l’un ou l’autre des modes opératoires du 
scénario opérationnel est considérée comme très vraisemblable (V3). Le mode opératoire 
le plus facile ou faisable étant le rouge numéroté.
 FICHE MÉTHODE 8 / EBIOS RISK MANAGER - LE SUPPLÉMENT — 55
b
 MÉTHODE AVANCÉE
 Commencez par calculer l’indice global de probabilité de succès de chaque 
mode opératoire du scénario opérationnel selon la démarche exposée 
précédemment.
 Calculez ensuite l’indice de difficulté technique de chaque mode opératoire 
selon les modalités ci-après. Le principe est de progresser sur une séquence 
d’un mode opératoire en évaluant de proche en proche à chaque action 
élémentaire « AEn
 » d’un nœud « n », un indice de difficulté cumulé 
intermédiaire à partir de la difficulté élémentaire de « AEn
 » et des difficultés 
cumulées intermédiaires du nœud précédent « n-1 » : 
(AEn
 ) = Max {Indice_Diff(AEn)
 , Min (Indices_Diff(AEn
 _1))}
 cumulés intermédiaires
 NOTE : la règle de calcul ci-dessus permet une évaluation relativement 
simple et rapide de l’indice global de difficulté technique. Elle trouve 
toutefois sa limite lorsqu’une séquence d’un mode opératoire comporte 
une longue chaîne d’étapes en série (environ une dizaine 9). L’évaluation 
aura alors tendance à sous-estimer la difficulté du mode opératoire 
correspondant, aboutissant à une vraisemblance sous-estimée du scénario 
opérationnel. Pour les séquences de mode opératoire concernées, vous 
pouvez compenser cette limite en augmentant d’un niveau l’indice de 
difficulté cumulé intermédiaire obtenu en bout de séquence.
 Enfin, déduisez la vraisemblance globale du scénario opérationnel en 
procédant comme suit10 :
 9 Particulièrement si les actions élémentaires correspondantes ont des indices de difficulté identiques.
 10  Vous pourriez également retenir comme vraisemblance celle obtenue en croisant l’indice global de 
probabilité de succès et l’indice global de difficulté technique obtenus. Mais votre résultat pourrait être 
faussé en cas de croisement des indices de probabilité et de difficulté relatifs à des modes opératoires 
différents. Dans ce cas, la vraisemblance du scénario opérationnel serait surestimée.
 56 — EBIOS RISK MANAGER - LE SUPPLÉMENT / FICHE MÉTHODE 8
■ évaluez le niveau de vraisemblance de chaque mode opératoire aboutissant  
à l’étape finale, en utilisant la grille croisée ci-après (qui peut être adaptée) ;
 ■ le niveau de vraisemblance pour le scénario opérationnel est celui du  
mode opératoire le plus vraisemblable ;
 ■ ce niveau de vraisemblance peut ensuite être pondéré selon la nature de  
la source de risque (motivation et ressources). Si vous estimez celle-ci  
comme particulièrement déterminée à atteindre son objectif – et donc, prête  
à solliciter des moyens conséquents et à persévérer en cas d’échecs  
successifs, alors vous pouvez décider d’augmenter d’un niveau la  
vraisemblance obtenue.
 DIFFICULTÉ TECHNIQUE DU MODE OPÉRATIONNEL
 0 – NÉGLIGEABLE 1 – FAIBLE
 PROBABILITÉ DE SUCCÈS DU 
MODE OPÉRATIONNEL
 4 – QUASI-CERTAINE
 3 – TRÈS ÉLEVÉE
 2 – SIGNIFICATIVE
 1 – FAIBLE
 4
 4
 3
 4
 3
 2 – MODÉRÉE 3 – ÉLEVÉE 4 – TRÈS ÉLEVÉE
 3
 3
 3
 2
 0 – TRÈS FAIBLE
 NOTES : 
1
 2
 1
 2
 2
 1
 2
 2
 2
 1
 0
 1
 1
 1
 0
 0
 ■ Le modèle suppose les probabilités de succès indépendantes 
entre elles, ce qui n’est pas nécessairement vrai. La même remarque 
s’applique pour les difficultés techniques. D’autre part, pour certaines 
catégories d’actions (telle que la corruption d’un membre du personnel), 
la probabilité de succès peut être dépendante de la difficulté, ce qui 
n’est pas capturé par défaut dans le modèle.
 ■ L’utilisation d’un logiciel de construction et de cotation de graphes 
d’attaque est fortement recommandée.
 FICHE MÉTHODE 8 / EBIOS RISK MANAGER - LE SUPPLÉMENT — 57
 EXEMPLE  : société de biotechnologie fabriquant des vaccins.
 Les indices de difficulté cumulés intermédiaires sont indiqués entre parenthèses et en italique.
 Connaitre
 Reconnaissance 
externe sources 
ouvertes
 Diff 1 (1)
 2
 Reconnaissance 
externe avancée
 Diff 2 (2)
 rentrer
 ▲
 Intrusion via un 
canal d'accès 
préexistant
 Diff 1 (1)
 1
 Intrusion via mail 
de hameçonnage 
sur service RH
 ▲
 Diff 1 (1)
 Intrusion via le site 
du CE (point d'eau)
 ▲
 Diff 1 (1)
 Corruption d'un 
personnel de 
l'équipe R&D
 ▲
 3
 Diff 2 (2)
 trouver
 (1)
 ▲
 Reconnaissance 
interne réseaux 
bureautique & IT 
site de Paris
 Diff 2 (2)
 ▲
 Latéralisation 
vers réseaux 
LAN et R&D
 Diff 3 (3)
 ▲
 Corruption d'un 
prestataire d'entre
tien des locaux
 Diff 2 (2)
 ▲
 Clé USB piégée 
connectée sur un 
poste R&D
 Diff 1 (2)
 exploiter
 ▲
 Exploitation 
maliciel de collecte 
et d'exfiltration
 Diff 1 (3)
 Création et main
tien d'un canal 
d'exfiltration via un 
poste internet
 (2)
 ▲
 ▲
 ▲
 ▲
 Diff 3 (3)
 (3)
 Vol et exploitation de 
données R&D
 DIFF GLOBALE 2
 La difficulté technique du scénario est estimée globalement à « 2 – Modéré », les modes 
opératoires les moins difficiles techniquement étant ceux numérotés 
 et 
. Compte-tenu 
des probabilités de succès évaluées précédemment, il est possible d’établir la synthèse suivante :
 Probabilité succès
 Chemin 
Chemin 
Chemin 
3 – Très élevée
 1 – Faible
 2 – Significative
 Difficulté technique
 3 – Élevée
 2 – Modérée
 2 – Modérée
 Vraisemblance
 V2 – Vraisemblable
 V2 – Vraisemblable 
V2 – Vraisemblable
 Scénario global V2 – Vraisemblable
 Les trois modes opératoires envisagés dans le graphe d’attaque ont le même niveau de 
vraisemblance. On aboutit à une vraisemblance « V2 – Vraisemblable » pour le scénario. 
Par rapport à l’évaluation réalisée avec la méthode standard (V3), la vraisemblance estimée 
est moindre. La prise en compte du critère de difficulté technique apporte une pondération 
sur l’estimation du niveau de vraisemblance. En effet, si le mode opératoire 
 apparaît 
comme ayant la probabilité de succès la plus élevée, il présente également une difficulté 
technique relativement élevée.
 58 — EBIOS RISK MANAGER - LE SUPPLÉMENT / FICHE MÉTHODE 8
5 / Éléments d’aide pour la cotation des actions 
élémentaires
 Cette section présente pour chaque catégorie d’action élémentaire (voir 
f
 iche méthode 7) les éléments majeurs qui déterminent sa probabilité de 
succès ou sa difficulté technique.
 RECRUTEMENT D’UNE SOURCE, CORRUPTION DE PERSONNEL
 ■ Nombre de cibles potentielles ayant accès aux informations visées, aux  
biens supports critiques ou à leur environnement physique (Note 1).
 ■ Personnels, prestataires, fournisseurs susceptibles d’être animés par un  
esprit de vengeance 
 EXEMPLE : salarié mécontent licencié récemment.
 ■ Personnels ayant fait l’objet d’un processus d’habilitation de sécurité et/ou  
d’une enquête, qui apporte un certain niveau d’assurance sur leur intégrité.
 ■ Satisfaction des cibles à l’égard de leur salaire ou de leur considération  
au sein de l’organisation.
 ■ Adhésion des cibles potentielles aux valeurs de l’entreprise (Note 2). 
Note 1 : plus les cibles potentielles sont nombreuses, plus il sera facile pour 
l’attaquant de trouver une cible corruptible. 
Note 2 : des personnes mal considérées et mal payées seront naturellement 
plus faciles à corrompre. Il ne faut pas sous-estimer ces leviers. 
FICHE MÉTHODE 8 / EBIOS RISK MANAGER - LE SUPPLÉMENT — 59
RECONNAISSANCE EXTERNE DE LA CIBLE 
■ Informations sur l’entité et son écosystème facilement accessibles sur  
Internet (sites web, forums de discussions en ligne, réseaux socio- 
professionnels, etc.).
 ■ Participation régulière de l’entité, de ses partenaires (fournisseurs,  
sous-traitants, clients) ou d’anciens salariés à des salons professionnels  
ou forums en ligne (Note 3).
 ■ Usage du chiffrement dans les relations de l’entité avec l’extérieur, dans  
les services offerts par l’entité à l’extérieur (Note 4).
 ■ Compétences particulières nécessaires pour la recherche des  
informations, compte tenu du domaine d’activité de l’entité (Note 5). 
Note 3 : beaucoup d’informations sont aisément obtenues au travers 
d’approches informelles dans les milieux professionnels. Lors de 
démarches commerciales notamment, de nombreuses informations 
sensibles sont souvent échangées  
 EXEMPLE : faux client, réponse à un appel d’offres. 
Note 4 : les protocoles de chiffrement permettent de limiter l’impact 
des fuites de données, en particulier vis-à-vis des interceptions ou 
détournements de trafic. 
Note 5 : des attaques nécessitant de fortes compétences dans un ou 
plusieurs domaines d’expertise en lien avec l’activité de la cible  
 EXEMPLE : contrôle aérien, risque NRBC – nucléaire, radiologique, 
bactériologique, chimique, signalisation ferroviaire) sont naturellement 
plus coûteuses et difficiles à identifier et traiter que des attaques mettant 
en œuvre des procédés essentiellement techniques.
 60 — EBIOS RISK MANAGER - LE SUPPLÉMENT / FICHE MÉTHODE 8
INTRUSION DEPUIS INTERNET OU DES RÉSEAUX INFORMATIQUES 
TIERS
 Les critères diffèrent selon la technique d’intrusion utilisée par l’attaquant.
 ATTAQUE FRONTALE DE SERVICES
 ■ Nombre de services et/ou d’applicatifs exposés sur Internet.
 ■ Services exposés ayant fait l’objet d’une homologation ou d’un processus  
de développement intégrant la sécurité.
 ■ Technologie de filtrage mise en place  
 EXEMPLE : reverse proxy, waf, etc. (Note 6). 
■ Utilisation de biens supports « de frontière » certifiés ou qualifiés  
(Note 7).
 Note 6 : ces outils fonctionnent sur la base de signatures et sont assez efficaces 
pour détecter les attaques les plus grossières.
 Note 7 : une technologie qualifiée ou certifiée est plus robuste vis-à-vis des 
exploits, car elle a fait l’objet d’une qualité de développement accrue, avec une 
attention importante donnée à la sécurité, et a fait l’objet de tests d’intrusion 
EXEMPLE : certification de sécurité de premier niveau, critères communs, 
agrément, référentiel général de sécurité.
 FICHE MÉTHODE 8 / EBIOS RISK MANAGER - LE SUPPLÉMENT — 61
HAMEÇONNAGE / POINT D’EAU
 Nombre d’utilisateurs susceptibles d’être visés (Note 8).
 ■ Utilisateurs régulièrement sensibilisés et formés à réagir aux attaques par  
hameçonnage et point d’eau.
 ■ Filtre anti-spam performant mis en place (Note 9).
 ■ Capacité de filtrage de la navigation Internet des utilisateurs mise  
en place  
 EXEMPLE : proxy, IPS (Note 10).
 ■ Filtrage des sites Internet reposant sur une liste blanche (liste de sites 
autorisés) (Note 11). 
Note 8 : plus il y a d’utilisateurs, plus il est facile de tester plusieurs cibles 
jusqu’à ce que l’une d’elles réalise l’opération attendue. 
Note 9 : ce type d’outil est assez efficace pour détecter les attaques les plus 
grossières (hameçonnage de masse, par exemple envoi d’un courriel piégé 
contenant un rançongiciel non ciblé). 
Note 10 : les solutions de filtrage de la navigation permettent à la fois de 
f
 iltrer ce qui est connu comme hébergeant une activité malveillante et 
d’enregistrer le trafic à des fins d’investigation poussée dans le cadre d’une 
supervision de sécurité. 
Note 11 : les navigations autorisées par listes blanches sont relativement 
complexes à contourner par un attaquant qui souhaite mener une attaque 
par point d’eau.
 INTRUSION VIA UN RÉSEAU SANS FIL
 ■ Existence de réseaux sans fil (Wi-Fi) dans l’environnement bureautique  
ou industriel de l’entité.
 ■ Accès Wi-Fi sécurisés, par exemple selon le guide technique de l’ANSSI11.
 11 Note technique – Recommandations de sécurité relatives aux réseaux Wi-Fi, ANSSI, 2013.
 62 — EBIOS RISK MANAGER - LE SUPPLÉMENT / FICHE MÉTHODE 8
INTRUSION VIA UN LOGICIEL OU UN CORRECTIF LÉGITIME
 ■ Existence d’une politique de sécurité relative aux mises à jour des logiciels,  
applications métier et firmware (Note 12).
 ■ Sources et canaux de confiance (voire certifiés ou qualifiés), vérifica 
tion de l’identité des signataires pour les mises à jour. 
Note 12 : la mise en place de mesures de sécurisation des mises à jour 
logicielles et firmware peut rendre beaucoup plus difficile une attaque de 
type cheval de Troie. 
Exemples de mesures : sas antivirus (certifié) avant application des mises à 
jour, procédures de contrôle d’intégrité des patchs et correctifs.
 FICHE MÉTHODE 8 / EBIOS RISK MANAGER - LE SUPPLÉMENT — 63
INTRUSION OU PIÈGE PHYSIQUE
 ■ Maîtrise des interventions des prestataires : gestion des accès aux locaux,  
supervision, journalisation, etc.(Note 13).
 ■ Processus d’habilitation de sécurité ou enquête préliminaire réalisés pour  
les prestataires qui interviennent sur site.
 ■ Utilisation de matériels informatiques gérés par l’organisation pour  
que les prestataires effectuent les interventions sur les biens supports  
de l’entité  
 EXEMPLE : valise de maintenance, clé USB de firmware (Note 14).
 ■ •Nombre et facilité d’accès des points de connexion physique et logique  
aux réseaux informatiques de l’entité.
 ■ Existence de liens de télémaintenance ou de connexions avec des réseaux  
tiers sécurisés.
 ■ Existence d’une politique de sécurité pour la chaîne  
d’approvisionnement industrielle. 
 EXEMPLE : exigences contractuelles, audits de sécurité des fournisseurs, etc.
 ■ Existence de mesures de sécurité pour la maintenance des biens  
supports (Note 15).
 ■ Existence de mesures de sécurité physique et type de technologie  
utilisée : contrôle d’accès  
 EXEMPLE : portique, badge, digicode, biométrie, vidéo protection, etc.
 ■ •Supervision de la sécurité physique et réactivité des équipes d’intervention  
en cas de détection d’intrusion (sur place, à distance, 24/7, seulement  
heures ouvrées).
 ■ Nombre de barrières à franchir pour accéder physiquement aux biens  
supports critiques (Note 16).
 64 — EBIOS RISK MANAGER - LE SUPPLÉMENT / FICHE MÉTHODE 8
■ Personnels de sécurité formés au risque d’introduction physique de  
matériels d’écoute.
 ■ Utilisateurs sensibilisés, voire entraînés, à la vigilance vis-à-vis des intrusions  
physiques. 
■ Connaissance mutuelle des personnes pouvant avoir un accès légitime.
 ■ Existence d’une politique de sécurité pour les déplacements professionnels,  
sensibilisation des salariés aux risques lors de leurs missions.
 Note 13 : des interventions réalisées en dehors des heures ouvrées ou 
en l’absence de toute vigilance/présence humaine facilitent une activité 
frauduleuse ou illégitime. Il en est de même si un prestataire dispose d’un 
badge d’accès lui permettant de circuler librement dans toutes les zones. 
Note 14 : le fait qu’un prestataire utilise ses propres moyens d’intervention 
pour, par exemple, effectuer la maintenance d’un automate ou la mise à jour 
d’un réseau informatique, accroît le risque d’introduction d’un éventuel code 
malveillant ciblé ou non, éventuellement à l’insu du prestataire.
 Note 15 : exemples de mesures : retrait des supports de stockage à mémoire 
non volatile, scellement physique, application du référentiel d’exigences de 
l’ANSSI relatif à l’intégration et à la maintenance des systèmes industriels.
 Note 16 : il est recommandé de disposer d’au moins trois barrières physiques 
pour accéder aux biens supports critiques. 
FICHE MÉTHODE 8 / EBIOS RISK MANAGER - LE SUPPLÉMENT — 65
 RECONNAISSANCE INTERNE  
LATÉRALISATION ET ÉLÉVATION DE PRIVILÈGES 
Les éléments majeurs qui influent sur la probabilité de succès ou la difficulté 
technique d’une reconnaissance interne, d’une latéralisation ou d’une élévation 
de privilèges sont relativement similaires et regroupés.
 ■ •Utilisateurs ayant des droits d’administrateur sur leur poste (Note 17).
 ■ Existence d’une politique de gestion des profils d’utilisateurs et de leurs  
droits d’accès, application du principe du moindre privilège.
 ■ Connexions à distance sur les systèmes limitées à des machines dédiées  
à l’administration, sans accès à Internet.
 ■ Existence d’un centre de supervision de la sécurité (SOC).
 ■ •Existence d’une politique d’authentification sur les réseaux (Note 18).
 ■ Cloisonnement des réseaux informatiques de l’entité par domaines de  
confiance ou de sensibilité des données (par exemple selon les guides de  
recommandations de l’ANSSI).
 ■ Administration sécurisée des réseaux et services (par exemple selon les  
guides de recommandations de l’ANSSI).
 ■ Niveau d’hétérogénéité du parc informatique (Note 19).
 ■ Nombre et spécificité des services offerts par le système d’information  
(Note 20).
 ■ Facilité d’accès des données critiques (Note 21).
 Note 17 : le fait qu’un utilisateur ait des droits d’administrateur sur son poste 
facilite grandement les opérations de reconnaissance interne, latéralisation 
et élévation de privilèges.
 Note 18 : exemples de moyens d’authentification du plus sécurisé au moins 
sécurisé : authentification forte, mot de passe avec politique contraignante, 
mot de passe sans politique, pas d’authentification.
 EXEMPLE : carte à puce.
 66 — EBIOS RISK MANAGER - LE SUPPLÉMENT / FICHE MÉTHODE 8
Note 19 : plus le niveau d’hétérogénéité est élevé, plus la surface d’attaque est 
importante et plus il est facile de trouver une vulnérabilité exploitable. À titre 
indicatif : hétérogénéité élevée (évolutions externes, BYOD, services disparates, 
etc.), hétérogénéité moyenne (rationalisation progressive, convergence des 
applicatifs, etc.), hétérogénéité faible et maîtrisée (applicatifs standards, etc.).
 Note 20 : plus les services métier offerts par le système d’information sont 
nombreux et spécifiques, plus la surface d’attaque est importante et plus il 
est facile d’identifier une vulnérabilité exploitable.
 Note 21 : la recherche des informations techniques (plans d’adressage, mots 
de passe, etc.) ou métiers peut être largement complexifiée pour l’attaquant. 
Exemples par ordre croissant de difficulté : données stockées en clair dans une 
zone centralisée et facilement identifiable (par leur nommage, etc.), données 
stockées à de multiples endroits, données chiffrées (pour l’attaquant, il sera 
alors nécessaire d’obtenir la clé de déchiffrement).  
PILOTAGE ET EXPLOITATION DE L’ATTAQUE
 Les éléments à considérer peuvent dépendre de l’objectif visé par 
l’attaquant et du mode d’attaque employé.
 ■ Nature du canal qu’il faudrait mettre en place pour piloter ou exploiter  
une attaque sur les biens supports visés (Note 22).
 ■ Contraintes de temps présumées pour l’exploitation de l’attaque (Note 23).
 ■ Existence d’un centre de supervision de la sécurité (SOC).
 ■ Existence d’un dispositif anti-DDOS.
 ■ Prise en compte de la menace TEMPEST, liée à l’interception de signaux  
parasites compromettants, notamment si les locaux de l’entité sont situés  
dans une zone urbaine de forte densité.
 Note 22 : exemples de canaux de command & control : canal préexistant déjà 
en place (backdoor), canal synchrone mis en place pour l’attaque 
 EXEMPLES : direct, reverse tcp/http, canal asynchrone (exemples : mail, 
réseaux sociaux), canal physique (exemple : air gap via des supports de 
stockage amovibles).
 FICHE MÉTHODE 8 / EBIOS RISK MANAGER - LE SUPPLÉMENT — 67
Note 23 : le temps d’exploitation va dépendre de l’objectif visé. Il peut être 
très court (quelques minutes à quelques heures), par exemple dans le cas d’un 
sabotage ou d’une attaque en déni de service non persistante, ou relativement 
long (plusieurs mois, voire années) pour une opération d’espionnage. D’autre 
part, certaines contraintes de temps peuvent rendre la tâche plus difficile 
pour l’attaquant. À titre indicatif, par ordre croissant de difficulté : aucune 
contrainte, l’attaque peut-être portée n’importe quand ; le timing doit être 
précis, mais le préavis est important ; le timing doit être précis et l’attaquant 
aura peu de préavis ; l’attaque doit être coordonnée sur plusieurs machines, 
sans connexion à Internet.
 OUTILS MALVEILLANTS
 La plupart des attaques demande l’installation de logiciels malveillants 
dans les systèmes ciblés, parfois en plusieurs étapes. Cette section, 
complémentaire des précédentes, regroupe les éléments majeurs qui déterminent 
la réussite et le coût d’un projet malveillant (mode opératoire, outil(s), etc.). 
Elle peut vous aider à affiner l’estimation de la vraisemblance d’une action 
élémentaire qui nécessiterait l’installation d’un logiciel malveillant.
 ■ Type de technologie des biens supports ciblés par l’outil malveillant  
(Note 24).
 ■ Délai d’application des correctifs de sécurité après leur publication. Mise  
en œuvre des recommandations de l’ANSSI relatives au MCS (Note 25).
 ■ Degré d’ancienneté de la technologie des biens supports ciblés.
 Note 24 : il est rare qu’un logiciel soit développé spécifiquement pour être 
malveillant et servir un dessein d’attaque.
 68 — EBIOS RISK MANAGER - LE SUPPLÉMENT / FICHE MÉTHODE 8
Toutefois, selon la technologie des biens supports visés, l’attaquant pourra être 
amené à adapter ou redévelopper un logiciel malveillant. Dans certains cas, si 
la technologie ciblée est très spécifique, il devra même acquérir le bien support  
 EXEMPLE : calculateur aéronautique, automate programmable industriel. 
Le type de technologie ciblé influe donc énormément sur la difficulté technique.
 Note 25 : un bien support à jour en termes de correctifs de sécurité oblige 
pour l’attaquant le développement d’un exploit dit « 0-day », donc inconnu 
du public. Dans le cas contraire, l’attaquant n’a qu’à exploiter une vulnérabilité 
publique (difficulté nulle et probabilité de succès quasi certaine). Plus le 
délai d’application d’un correctif de sécurité sur une vulnérabilité connue 
est long, plus la fenêtre d’opportunité pour obtenir un exploit sans difficulté 
est importante