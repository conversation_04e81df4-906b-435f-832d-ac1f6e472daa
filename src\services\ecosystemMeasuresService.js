// src/services/ecosystemMeasuresService.js
import api from '../api/apiClient';

/**
 * Service for managing ecosystem security measures in Atelier 3
 */
const ecosystemMeasuresService = {
  /**
   * Get all ecosystem measures for an analysis
   * @param {string} analysisId - The ID of the analysis
   * @returns {Promise<Object>} - Response with measures data
   */
  getEcosystemMeasures: async (analysisId) => {
    try {
      const response = await api.get(`/analyses/${analysisId}/ecosystem-measures`);
      return response;
    } catch (error) {
      console.error('Error getting ecosystem measures:', error);
      return {
        success: false,
        message: 'Erreur lors de la récupération des mesures de sécurité',
        error: error.message,
        data: []
      };
    }
  },

  /**
   * Save ecosystem measures for an analysis
   * @param {string} analysisId - The ID of the analysis
   * @param {Array} measures - The measures to save
   * @returns {Promise<Object>} - Response with success status
   */
  saveEcosystemMeasures: async (analysisId, measures) => {
    try {
      const response = await api.post(`/analyses/${analysisId}/ecosystem-measures`, { measures });
      return response;
    } catch (error) {
      console.error('Error saving ecosystem measures:', error);
      return {
        success: false,
        message: 'Erreur lors de la sauvegarde des mesures de sécurité',
        error: error.message
      };
    }
  },

  /**
   * Generate ecosystem measures using AI
   * @param {Object} params - Parameters for AI generation
   * @param {string} params.analysisId - The ID of the analysis
   * @param {Array} params.stakeholders - Selected stakeholder IDs
   * @param {Array} params.attackPaths - Selected attack path IDs
   * @param {string} params.focus - Generation focus (practical, balanced, comprehensive)
   * @param {number} params.measuresPerPath - Number of measures to generate per attack path (default: 5)
   * @returns {Promise<Object>} - Response with generated measures
   */
  generateMeasures: async (params) => {
    try {
      const response = await api.post('/ai/generate-ecosystem-measures', params);
      return response;
    } catch (error) {
      console.error('Error generating ecosystem measures with AI:', error);
      return {
        success: false,
        message: 'Erreur lors de la génération des mesures de sécurité avec l\'IA',
        error: error.message
      };
    }
  }
};

export default ecosystemMeasuresService;
