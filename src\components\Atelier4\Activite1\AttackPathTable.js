// src/components/Atelier4/Activite1/AttackPathTable.js
import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Edit, Trash2, Eye, Search, LogIn, MapPin, Target, AlertTriangle } from 'lucide-react';

const AttackPathTable = ({ attackPaths, onSelect, onEdit, onDelete, loading }) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedLikelihood, setSelectedLikelihood] = useState('all');

  // Likelihood levels
  const likelihoodLevels = {
    'V4': { label: 'Quasi-certain', color: 'bg-red-100 text-red-800', priority: 4 },
    'V3': { label: 'Très vraisemblable', color: 'bg-orange-100 text-orange-800', priority: 3 },
    'V2': { label: 'Vraisemblable', color: 'bg-yellow-100 text-yellow-800', priority: 2 },
    'V1': { label: 'Peu vraisemblable', color: 'bg-blue-100 text-blue-800', priority: 1 },
    'V0': { label: 'Invraisemblable', color: 'bg-gray-100 text-gray-800', priority: 0 }
  };

  // Phase icons
  const phaseIcons = {
    'connaitre': Search,
    'rentrer': LogIn,
    'trouver': MapPin,
    'exploiter': Target
  };

  // Filter attack paths
  const filteredPaths = attackPaths.filter(path => {
    const matchesSearch = (path.title || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
                         (path.description || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
                         (path.sourceRiskName || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
                         (path.objectifVise || '').toLowerCase().includes(searchTerm.toLowerCase());
    const matchesLikelihood = selectedLikelihood === 'all' || path.likelihood === selectedLikelihood;
    return matchesSearch && matchesLikelihood;
  });

  const handleDelete = (pathId, pathTitle) => {
    if (window.confirm(`Êtes-vous sûr de vouloir supprimer le scénario "${pathTitle}" ?`)) {
      onDelete(pathId);
    }
  };

  const getPhaseCount = (path) => {
    if (path.phases) {
      return path.phases.reduce((total, phase) => total + (phase.actions?.length || 0), 0) || 0;
    }
    if (path.steps) {
      return path.steps.length;
    }
    return 0;
  };

  const getPhaseData = (path) => {
    if (path.phases) {
      return path.phases;
    }
    if (path.steps) {
      // Group steps by phase
      const phaseGroups = {};
      path.steps.forEach(step => {
        if (!phaseGroups[step.phase]) {
          phaseGroups[step.phase] = [];
        }
        phaseGroups[step.phase].push(step);
      });

      return Object.entries(phaseGroups).map(([phaseId, steps]) => ({
        phase: phaseId,
        actions: steps
      }));
    }
    return [];
  };

  if (loading) {
    return (
      <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-100">
        <div className="animate-pulse space-y-4">
          <div className="h-4 bg-gray-200 rounded w-1/4"></div>
          <div className="space-y-3">
            {[1, 2, 3].map(i => (
              <div key={i} className="h-12 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-100">
      <div className="flex justify-between items-center mb-6">
        <h3 className="text-lg font-medium text-gray-800">Scénarios opérationnels</h3>
        <div className="flex space-x-4">
          {/* Search */}
          <div className="relative">
            <Search size={16} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <input
              type="text"
              placeholder="Rechercher un scénario..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>

          {/* Likelihood filter */}
          <select
            value={selectedLikelihood}
            onChange={(e) => setSelectedLikelihood(e.target.value)}
            className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="all">Toutes vraisemblances</option>
            {Object.entries(likelihoodLevels).map(([key, level]) => (
              <option key={key} value={key}>{key} - {level.label}</option>
            ))}
          </select>
        </div>
      </div>

      {filteredPaths.length === 0 ? (
        <div className="text-center py-12">
          <AlertTriangle size={48} className="mx-auto text-gray-400 mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">Aucun scénario trouvé</h3>
          <p className="text-gray-500 mb-4">
            {searchTerm || selectedLikelihood !== 'all'
              ? 'Aucun scénario ne correspond à vos critères de recherche.'
              : 'Commencez par créer votre premier scénario opérationnel.'
            }
          </p>
        </div>
      ) : (
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Scénario
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Phases d'attaque
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Vraisemblance
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredPaths.map((path, index) => (
                <motion.tr
                  key={path.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                  className="hover:bg-gray-50"
                >
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div>
                      <div className="text-sm font-medium text-gray-900">
                        {path.title || path.sourceRiskName || 'Chemin d\'attaque'}
                      </div>
                      <div className="text-sm text-gray-500">
                        {path.description || path.objectifVise || 'Aucune description'}
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="flex space-x-2">
                      {getPhaseData(path).map((phase, idx) => {
                        const IconComponent = phaseIcons[phase.phase];
                        return (
                          <div
                            key={idx}
                            className="flex items-center bg-gray-100 px-2 py-1 rounded text-xs"
                            title={phase.phase.toUpperCase()}
                          >
                            {IconComponent && <IconComponent size={12} className="mr-1" />}
                            {phase.actions?.length || 0}
                          </div>
                        );
                      })}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">
                      {getPhaseCount(path)} actions élémentaires
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                      likelihoodLevels[path.likelihood]?.color || 'bg-gray-100 text-gray-800'
                    }`}>
                      {path.likelihood} - {likelihoodLevels[path.likelihood]?.label || 'Non défini'}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <div className="flex space-x-2">
                      <button
                        onClick={() => onSelect(path)}
                        className="text-green-600 hover:text-green-900 p-1 rounded hover:bg-green-50"
                        title="Visualiser"
                      >
                        <Eye size={16} />
                      </button>
                      <button
                        onClick={() => onEdit(path)}
                        className="text-blue-600 hover:text-blue-900 p-1 rounded hover:bg-blue-50"
                        title="Modifier"
                      >
                        <Edit size={16} />
                      </button>
                      <button
                        onClick={() => handleDelete(path.id, path.title || path.sourceRiskName || 'ce chemin')}
                        className="text-red-600 hover:text-red-900 p-1 rounded hover:bg-red-50"
                        title="Supprimer"
                      >
                        <Trash2 size={16} />
                      </button>
                    </div>
                  </td>
                </motion.tr>
              ))}
            </tbody>
          </table>
        </div>
      )}
    </div>
  );
};

export default AttackPathTable;