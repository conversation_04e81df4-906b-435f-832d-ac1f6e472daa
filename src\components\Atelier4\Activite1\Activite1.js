// src/components/Atelier4/Activite1/Activite1.js
import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Plus, Search, LogIn, MapPin, Target, Eye, Info, Save, Download, Route, List } from 'lucide-react';
import AttackGraphVisualization from './AttackGraphVisualization';
import AttackSequenceForm from './AttackSequenceForm';
import AttackPathTable from './AttackPathTable';
import OperationalScenarios from './OperationalScenarios';
import ThreatIntelligencePanel from './ThreatIntelligencePanel';
import OperationalScenarioGenerator from './OperationalScenarioGenerator';
import GuideModal from './GuideModal';
import { useAnalysis } from '../../../context/AnalysisContext';
import { api } from '../../../api/apiClient';

const Activite1 = () => {
  const [attackPaths, setAttackPaths] = useState([]);
  const [selectedPath, setSelectedPath] = useState(null);
  const [showForm, setShowForm] = useState(false);
  const [showGuide, setShowGuide] = useState(false);
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('scenarios'); // 'graphs', 'scenarios', 'threats', 'generator'
  const [threatIntelligence, setThreatIntelligence] = useState(null);
  const [businessAssets, setBusinessAssets] = useState([]);
  const { currentAnalysis } = useAnalysis();

  // Attack sequence phases
  const attackPhases = [
    {
      id: 'connaitre',
      name: 'CONNAITRE',
      description: 'Reconnaissance et découverte externes',
      icon: Search,
      color: 'bg-blue-500',
      examples: [
        'Reconnaissance externe sources ouvertes',
        'Reconnaissance externe avancée',
        'Recrutement d\'une source'
      ]
    },
    {
      id: 'rentrer',
      name: 'RENTRER',
      description: 'Intrusion dans le système',
      icon: LogIn,
      color: 'bg-orange-500',
      examples: [
        'Intrusion via mail de hameçonnage',
        'Intrusion via un canal d\'accès préexistant',
        'Intrusion ou piège physique'
      ]
    },
    {
      id: 'trouver',
      name: 'TROUVER',
      description: 'Reconnaissance interne et latéralisation',
      icon: MapPin,
      color: 'bg-purple-500',
      examples: [
        'Reconnaissance interne réseaux',
        'Latéralisation vers réseaux LAN',
        'Élévation de privilèges'
      ]
    },
    {
      id: 'exploiter',
      name: 'EXPLOITER',
      description: 'Exploitation des données et biens supports',
      icon: Target,
      color: 'bg-red-500',
      examples: [
        'Exploitation maliciel de collecte',
        'Création canal d\'exfiltration',
        'Vol et exploitation de données'
      ]
    }
  ];

  // Load attack paths and business assets data
  useEffect(() => {
    const loadData = async () => {
      if (currentAnalysis?.id) {
        setLoading(true);
        try {
          // Load attack paths from API
          const attackPathsResponse = await api.get(`/analyses/${currentAnalysis.id}/attack-paths`);
          if (attackPathsResponse?.data?.attackPaths && Array.isArray(attackPathsResponse.data.attackPaths)) {
            setAttackPaths(attackPathsResponse.data.attackPaths);
          } else if (attackPathsResponse?.data && Array.isArray(attackPathsResponse.data)) {
            setAttackPaths(attackPathsResponse.data);
          } else {
            console.log('No attack paths found, using empty array');
            setAttackPaths([]);
          }

          // Load business assets from Workshop 1
          try {
            const businessValuesResponse = await api.get(`/analyses/${currentAnalysis.id}/business-values`);
            if (businessValuesResponse?.data?.businessValues && Array.isArray(businessValuesResponse.data.businessValues)) {
              setBusinessAssets(businessValuesResponse.data.businessValues);
            } else if (businessValuesResponse?.data && Array.isArray(businessValuesResponse.data)) {
              setBusinessAssets(businessValuesResponse.data);
            } else {
              console.log('No business assets found, using empty array');
              setBusinessAssets([]);
            }
          } catch (error) {
            console.error('Error loading business assets:', error);
            setBusinessAssets([]);
          }
        } catch (error) {
          console.error('Error loading attack paths:', error);
          // Use empty array if API fails
          setAttackPaths([]);
        } finally {
          setLoading(false);
        }
      }
    };

    loadData();
  }, [currentAnalysis?.id]);

  const handleAddPath = () => {
    setSelectedPath(null);
    setShowForm(true);
  };

  const handleEditPath = (path) => {
    setSelectedPath(path);
    setShowForm(true);
  };

  const handleSavePath = (pathData) => {
    if (selectedPath) {
      // Update existing path
      setAttackPaths(prev => prev.map(p =>
        p.id === selectedPath.id ? { ...pathData, id: selectedPath.id } : p
      ));
    } else {
      // Add new path
      const newPath = {
        ...pathData,
        id: Date.now(),
        status: 'active'
      };
      setAttackPaths(prev => [...prev, newPath]);
    }
    setShowForm(false);
    setSelectedPath(null);
  };

  const handleDeletePath = (pathId) => {
    setAttackPaths(prev => prev.filter(p => p.id !== pathId));
  };

  // Handle threat intelligence updates
  const handleThreatIntelligenceUpdate = (threatData) => {
    setThreatIntelligence(threatData);
  };

  return (
    <div className="space-y-6">
      {/* === MAIN HEADER === */}
      <div className="bg-white rounded-xl shadow-sm border border-slate-100 p-6">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          {/* Left Side: Breadcrumb & Title */}
          <div>
            <div className="flex items-center text-sm text-slate-500 mb-2">
              <span className="hover:text-blue-600 transition-colors">Atelier 4</span>
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mx-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
              <span className="hover:text-blue-600 transition-colors">Activité 1</span>
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mx-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
              <span className="text-blue-600 font-medium">Scénarios opérationnels</span>
            </div>
            <h1 className="text-2xl sm:text-3xl font-bold text-slate-800 flex items-center">
              <Target size={28} className="mr-3 text-blue-600" />
              Scénarios Opérationnels d'Attaque
            </h1>
          </div>

          {/* Right Side: Buttons */}
          <div className="flex items-center gap-2 flex-wrap">
            <button
              onClick={() => setShowGuide(true)}
              className="text-sm font-medium bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 flex items-center shadow-sm transition duration-200"
              title="Guide sur les scénarios opérationnels"
            >
              <Info size={16} className="mr-2" />
              Guide
            </button>
            {activeTab === 'graphs' && (
              <button
                onClick={handleAddPath}
                className="text-sm font-medium bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 flex items-center shadow-sm transition duration-200"
              >
                <Plus size={16} className="mr-2" />
                Nouveau scénario
              </button>
            )}
          </div>
        </div>
      </div>
      {/* === END MAIN HEADER === */}

      {/* Tab Navigation */}
      <div className="bg-white rounded-xl shadow-sm border border-slate-100 overflow-hidden">
        <div className="border-b border-gray-200 bg-gradient-to-r from-blue-50 to-indigo-50 px-6 py-4">
          <nav className="flex space-x-4 overflow-x-auto">
            <button
              onClick={() => setActiveTab('graphs')}
              className={`flex items-center py-3 px-4 border-b-2 font-medium text-sm transition-all duration-200 whitespace-nowrap ${
                activeTab === 'graphs'
                  ? 'border-blue-500 text-blue-600 bg-white rounded-t-lg shadow-sm'
                  : 'border-transparent text-gray-600 hover:text-blue-600 hover:border-blue-300 hover:bg-white/50 rounded-t-lg'
              }`}
            >
              <Route size={18} className="mr-2" />
              Graphes d'attaque
            </button>
            <button
              onClick={() => setActiveTab('scenarios')}
              className={`flex items-center py-3 px-4 border-b-2 font-medium text-sm transition-all duration-200 whitespace-nowrap ${
                activeTab === 'scenarios'
                  ? 'border-blue-500 text-blue-600 bg-white rounded-t-lg shadow-sm'
                  : 'border-transparent text-gray-600 hover:text-blue-600 hover:border-blue-300 hover:bg-white/50 rounded-t-lg'
              }`}
            >
              <List size={18} className="mr-2" />
              Scénarios opérationnels
            </button>
            <button
              onClick={() => setActiveTab('threats')}
              className={`flex items-center py-3 px-4 border-b-2 font-medium text-sm transition-all duration-200 whitespace-nowrap ${
                activeTab === 'threats'
                  ? 'border-blue-500 text-blue-600 bg-white rounded-t-lg shadow-sm'
                  : 'border-transparent text-gray-600 hover:text-blue-600 hover:border-blue-300 hover:bg-white/50 rounded-t-lg'
              }`}
            >
              <Search size={18} className="mr-2" />
              Intelligence des menaces
            </button>
            <button
              onClick={() => setActiveTab('generator')}
              className={`flex items-center py-3 px-4 border-b-2 font-medium text-sm transition-all duration-200 whitespace-nowrap ${
                activeTab === 'generator'
                  ? 'border-blue-500 text-blue-600 bg-white rounded-t-lg shadow-sm'
                  : 'border-transparent text-gray-600 hover:text-blue-600 hover:border-blue-300 hover:bg-white/50 rounded-t-lg'
              }`}
            >
              <Plus size={18} className="mr-2" />
              Générateur IA
            </button>
          </nav>
        </div>
      </div>

      {/* Tab Content */}
      {activeTab === 'graphs' ? (
        <>
          {/* Attack Sequence Model Overview */}
          <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-100">
            <h3 className="text-lg font-medium text-gray-800 mb-4">Modèle de séquence d'attaque</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {attackPhases.map((phase, index) => {
                const IconComponent = phase.icon;
                return (
                  <motion.div
                    key={phase.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.1 }}
                    className="bg-gray-50 p-4 rounded-lg border border-gray-200"
                  >
                    <div className="flex items-center mb-3">
                      <div className={`${phase.color} p-2 rounded-lg mr-3`}>
                        <IconComponent size={20} className="text-white" />
                      </div>
                      <h4 className="font-medium text-gray-800">{phase.name}</h4>
                    </div>
                    <p className="text-sm text-gray-600 mb-3">{phase.description}</p>
                    <div className="space-y-1">
                      {phase.examples.map((example, idx) => (
                        <div key={idx} className="text-xs text-gray-500 bg-white px-2 py-1 rounded">
                          {example}
                        </div>
                      ))}
                    </div>
                  </motion.div>
                );
              })}
            </div>
          </div>

          {/* Attack Paths Table */}
          <AttackPathTable
            attackPaths={attackPaths}
            onEdit={handleEditPath}
            onDelete={handleDeletePath}
            loading={loading}
          />

          {/* Attack Graph Visualization */}
          {selectedPath && (
            <AttackGraphVisualization
              attackPath={selectedPath}
              phases={attackPhases}
            />
          )}
        </>
      ) : activeTab === 'scenarios' ? (
        /* Operational Scenarios Tab */
        <OperationalScenarios attackPaths={attackPaths} />
      ) : activeTab === 'threats' ? (
        /* Threat Intelligence Tab */
        <ThreatIntelligencePanel
          selectedAttackPath={selectedPath}
          businessAssets={businessAssets}
          onThreatIntelligenceUpdate={handleThreatIntelligenceUpdate}
        />
      ) : activeTab === 'generator' ? (
        /* AI Scenario Generator Tab */
        <OperationalScenarioGenerator
          selectedAttackPath={selectedPath}
          threatIntelligence={threatIntelligence}
          businessAssets={businessAssets}
          onScenarioGenerated={(scenario) => {
            console.log('New scenario generated:', scenario);
            // Optionally switch to scenarios tab to show the new scenario
            setActiveTab('scenarios');
          }}
        />
      ) : null}

      {/* Form Modal */}
      {showForm && (
        <AttackSequenceForm
          path={selectedPath}
          phases={attackPhases}
          onSave={handleSavePath}
          onCancel={() => {
            setShowForm(false);
            setSelectedPath(null);
          }}
        />
      )}

      {/* Guide Modal */}
      {showGuide && (
        <GuideModal
          onClose={() => setShowGuide(false)}
        />
      )}
    </div>
  );
};

export default Activite1;