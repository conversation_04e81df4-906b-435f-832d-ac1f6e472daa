/**
 * Utility for token handling - AUTOMATIC REFRESH DISABLED
 * This module provides token utilities but automatic refresh has been disabled
 */

/**
 * Placeholder for token refresh monitoring - DISABLED
 * @returns {Object} - Object with dummy start and stop functions
 */
export const initTokenRefreshMonitor = () => {
  // Dummy functions that do nothing
  const noop = () => {};

  console.log('Token refresh monitoring is disabled');

  return {
    start: () => noop,
    stop: noop,
    checkNow: noop
  };
};

/**
 * Parse JWT token to get expiry time
 * @param {string} token - JWT token
 * @returns {number|null} - Expiry time in milliseconds or null if invalid
 */
export const getTokenExpiry = (token) => {
  if (!token) return null;

  try {
    // Get the payload part of the JWT
    const base64Url = token.split('.')[1];
    const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
    const jsonPayload = decodeURIComponent(
      atob(base64)
        .split('')
        .map(c => '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2))
        .join('')
    );

    const { exp } = JSON.parse(jsonPayload);
    return exp * 1000; // Convert to milliseconds
  } catch (error) {
    console.error('Error parsing token'); // Removed error object from log
    return null;
  }
};
