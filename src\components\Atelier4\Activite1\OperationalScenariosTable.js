// src/components/Atelier4/Activite1/OperationalScenariosTable.js
import React, { useState } from 'react';
import { Edit2, Trash2, ChevronDown, ChevronUp, Eye, Bot, Target, Clock, Shield } from 'lucide-react';

// Severity levels with consistent styling
const SEVERITY_LEVELS = {
  'Très faible': { label: 'Très faible', color: 'bg-green-100 text-green-800 border-green-200' },
  'Faible': { label: 'Faible', color: 'bg-green-100 text-green-800 border-green-200' },
  'Moyen': { label: 'Moyen', color: 'bg-yellow-100 text-yellow-800 border-yellow-200' },
  'Élevé': { label: 'Élevé', color: 'bg-orange-100 text-orange-800 border-orange-200' },
  'Très élevé': { label: 'Très élevé', color: 'bg-red-100 text-red-800 border-red-200' },
  // English fallbacks
  'low': { label: 'Faible', color: 'bg-green-100 text-green-800 border-green-200' },
  'medium': { label: 'Moyen', color: 'bg-yellow-100 text-yellow-800 border-yellow-200' },
  'high': { label: 'Élevé', color: 'bg-orange-100 text-orange-800 border-orange-200' },
  'critical': { label: 'Très élevé', color: 'bg-red-100 text-red-800 border-red-200' }
};

// Likelihood levels with consistent styling
const LIKELIHOOD_LEVELS = {
  'Très faible': { label: 'Très faible', color: 'bg-green-100 text-green-800 border-green-200' },
  'Faible': { label: 'Faible', color: 'bg-green-100 text-green-800 border-green-200' },
  'Moyen': { label: 'Moyen', color: 'bg-yellow-100 text-yellow-800 border-yellow-200' },
  'Élevé': { label: 'Élevé', color: 'bg-orange-100 text-orange-800 border-orange-200' },
  'Très élevé': { label: 'Très élevé', color: 'bg-red-100 text-red-800 border-red-200' },
  // English fallbacks
  'low': { label: 'Faible', color: 'bg-green-100 text-green-800 border-green-200' },
  'medium': { label: 'Moyen', color: 'bg-yellow-100 text-yellow-800 border-yellow-200' },
  'high': { label: 'Élevé', color: 'bg-orange-100 text-orange-800 border-orange-200' }
};

const OperationalScenariosTable = ({
  scenarios,
  attackPaths,
  onScenarioUpdate,
  onScenarioDelete,
  selectedScenarios,
  onSelectionChange
}) => {
  const [expandedScenario, setExpandedScenario] = useState(null);
  const [editingScenario, setEditingScenario] = useState(null);
  const [sortField, setSortField] = useState('name');
  const [sortDirection, setSortDirection] = useState('asc');
  const [selectedMeasures, setSelectedMeasures] = useState(new Set());
  const [selectAll, setSelectAll] = useState(false);

  // Toggle expansion for scenario details
  const toggleExpand = (scenarioId) => {
    if (expandedScenario === scenarioId) {
      setExpandedScenario(null);
    } else {
      setExpandedScenario(scenarioId);
    }
  };

  // Handle sorting
  const handleSort = (field) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  // Get sort icon
  const getSortIcon = (field) => {
    if (sortField !== field) return null;
    return sortDirection === 'asc' ? <ChevronUp size={16} /> : <ChevronDown size={16} />;
  };

  // Sort scenarios
  const sortedScenarios = React.useMemo(() => {
    return [...scenarios].sort((a, b) => {
      let aValue = a[sortField];
      let bValue = b[sortField];

      if (typeof aValue === 'string') {
        aValue = aValue.toLowerCase();
        bValue = bValue.toLowerCase();
      }

      if (aValue < bValue) {
        return sortDirection === 'asc' ? -1 : 1;
      }
      if (aValue > bValue) {
        return sortDirection === 'asc' ? 1 : -1;
      }
      return 0;
    });
  }, [scenarios, sortField, sortDirection]);

  // Handle selection
  const handleSelectScenario = (scenarioId) => {
    const newSelection = selectedScenarios.includes(scenarioId)
      ? selectedScenarios.filter(id => id !== scenarioId)
      : [...selectedScenarios, scenarioId];
    onSelectionChange(newSelection);
  };

  const handleSelectAll = () => {
    if (selectedScenarios.length === scenarios.length) {
      onSelectionChange([]);
      setSelectAll(false);
    } else {
      onSelectionChange(scenarios.map(s => s.id));
      setSelectAll(true);
    }
  };

  // Get attack path with reference code
  const getAttackPathInfo = (scenario) => {
    // First try to use the attack path context from the scenario (for AI-generated scenarios)
    if (scenario.attackPathContext) {
      return {
        name: scenario.attackPathContext.sourceRiskName || 'Chemin d\'attaque',
        referenceCode: scenario.attackPathContext.referenceCode || scenario.attackPathReference || 'CA???',
        targetObjective: scenario.attackPathContext.targetObjective
      };
    }

    // Fallback to finding the attack path by ID
    const path = attackPaths.find(p => p.id === scenario.attackPathId);
    if (!path) {
      return {
        name: `Chemin ${scenario.attackPathId}`,
        referenceCode: scenario.attackPathReference || 'CA???',
        targetObjective: 'Non spécifié'
      };
    }

    const referenceCode = path.referenceCode || scenario.attackPathReference || `CA${String(attackPaths.indexOf(path) + 1).padStart(2, '0')}`;
    const name = path.sourceRiskName || path.name || `Chemin ${scenario.attackPathId}`;
    const targetObjective = path.objectifVise || path.targetObjective || 'Non spécifié';

    return { name, referenceCode, targetObjective };
  };

  const handleEdit = (scenario) => {
    setEditingScenario({ ...scenario });
  };

  const handleSaveEdit = () => {
    if (editingScenario) {
      onScenarioUpdate(editingScenario.id, editingScenario);
      setEditingScenario(null);
    }
  };

  const handleCancelEdit = () => {
    setEditingScenario(null);
  };

  if (scenarios.length === 0) {
    return (
      <div className="text-center py-12">
        <Target size={48} className="mx-auto text-gray-400 mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">Aucun scénario opérationnel</h3>
        <p className="text-gray-500 mb-4">
          Commencez par générer des scénarios opérationnels à partir des chemins d'attaque.
        </p>
      </div>
    );
  }

  return (
    <div className="bg-white border border-gray-200 rounded-xl shadow-sm overflow-hidden">
      {/* Header with stats */}
      <div className="bg-gradient-to-r from-blue-50 to-indigo-50 px-6 py-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div>
            <div className="flex items-center space-x-4">
              <div>
                <h3 className="text-lg font-semibold text-gray-900">Scénarios opérationnels</h3>
                <p className="text-sm text-gray-600 mt-1">
                  {scenarios.length} scénario{scenarios.length > 1 ? 's' : ''} défini{scenarios.length > 1 ? 's' : ''}
                  {selectedScenarios.length > 0 && (
                    <span className="ml-2 text-blue-600 font-medium">
                      ({selectedScenarios.length} sélectionné{selectedScenarios.length > 1 ? 's' : ''})
                    </span>
                  )}
                </p>
              </div>
            </div>
          </div>

          {selectedScenarios.length > 0 && (
            <div className="flex items-center space-x-3">
              <button
                onClick={() => {
                  selectedScenarios.forEach(id => onScenarioDelete(id));
                  onSelectionChange([]);
                }}
                className="inline-flex items-center px-3 py-2 border border-red-300 shadow-sm text-sm leading-4 font-medium rounded-md text-red-700 bg-red-50 hover:bg-red-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-colors duration-200"
              >
                <Trash2 size={16} className="mr-2" />
                Supprimer ({selectedScenarios.length})
              </button>
            </div>
          )}
        </div>
      </div>

      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-16">
                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    checked={selectAll}
                    onChange={handleSelectAll}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <span className="sr-only">Sélectionner tout</span>
                </div>
              </th>
              <th
                className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100 transition-colors"
                onClick={() => handleSort('name')}
              >
                <div className="flex items-center space-x-1">
                  <span>Nom du scénario</span>
                  {getSortIcon('name')}
                </div>
              </th>
              <th
                className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100 transition-colors"
                onClick={() => handleSort('severity')}
              >
                <div className="flex items-center space-x-1">
                  <span>Gravité</span>
                  {getSortIcon('severity')}
                </div>
              </th>
              <th
                className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100 transition-colors"
                onClick={() => handleSort('likelihood')}
              >
                <div className="flex items-center space-x-1">
                  <span>Probabilité</span>
                  {getSortIcon('likelihood')}
                </div>
              </th>
              <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Chemin d'attaque
              </th>
              <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Compétences
              </th>
              <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Durée
              </th>
              <th className="px-6 py-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider w-24">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {sortedScenarios.map((scenario) => (
              <React.Fragment key={scenario.id}>
                <tr className="hover:bg-gray-50 transition-colors duration-150">
                  <td className="px-6 py-5 whitespace-nowrap">
                    <div className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        checked={selectedScenarios.includes(scenario.id)}
                        onChange={() => handleSelectScenario(scenario.id)}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                      <button
                        onClick={() => toggleExpand(scenario.id)}
                        className="text-gray-400 hover:text-gray-600 transition-colors"
                        title="Voir les détails"
                      >
                        {expandedScenario === scenario.id ? (
                          <ChevronDown size={16} />
                        ) : (
                          <ChevronUp size={16} />
                        )}
                      </button>
                    </div>
                  </td>
                  <td className="px-6 py-5">
                    <div className="flex items-center">
                      <div>
                        <div className="text-sm font-medium text-gray-900 flex items-center">
                          {scenario.name}
                          {scenario.source === 'ai_generated' && (
                            <span className="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-purple-100 text-purple-800 border border-purple-200">
                              <Bot size={12} className="mr-1" />
                              IA
                            </span>
                          )}
                        </div>
                        <div className="text-sm text-gray-500 mt-1 max-w-xs truncate">
                          {scenario.description}
                        </div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-5 whitespace-nowrap">
                    <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium shadow-sm ${SEVERITY_LEVELS[scenario.severity]?.color || 'bg-gray-100 text-gray-800'}`}>
                      {SEVERITY_LEVELS[scenario.severity]?.label || scenario.severity}
                    </span>
                  </td>
                  <td className="px-6 py-5 whitespace-nowrap">
                    <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium shadow-sm ${LIKELIHOOD_LEVELS[scenario.likelihood]?.color || 'bg-gray-100 text-gray-800'}`}>
                      {LIKELIHOOD_LEVELS[scenario.likelihood]?.label || scenario.likelihood}
                    </span>
                  </td>
                  <td className="px-6 py-5">
                    <div className="flex flex-wrap gap-2">
                      {(() => {
                        const pathInfo = getAttackPathInfo(scenario);
                        return (
                          <div className="flex flex-col space-y-1">
                            <div className="flex items-center space-x-2">
                              <span className="inline-flex items-center px-3 py-1.5 rounded-md text-sm font-bold bg-blue-100 text-blue-800 border border-blue-200 shadow-sm">
                                {pathInfo.referenceCode}
                              </span>
                              <span className="text-sm text-gray-600 max-w-xs truncate">
                                {pathInfo.name}
                              </span>
                            </div>
                            {pathInfo.targetObjective && pathInfo.targetObjective !== 'Non spécifié' && (
                              <div className="text-xs text-gray-500 ml-1">
                                → {pathInfo.targetObjective}
                              </div>
                            )}
                          </div>
                        );
                      })()}
                    </div>
                  </td>
                  <td className="px-6 py-5">
                    <span className="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-gray-100 text-gray-800">
                      <Shield size={12} className="mr-1" />
                      {scenario.requiredSkills || 'Non spécifié'}
                    </span>
                  </td>
                  <td className="px-6 py-5">
                    <span className="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-gray-100 text-gray-800">
                      <Clock size={12} className="mr-1" />
                      {scenario.timeline || 'Non spécifié'}
                    </span>
                  </td>
                  <td className="px-6 py-5 whitespace-nowrap text-right text-sm font-medium">
                    <div className="flex items-center justify-end space-x-2">
                      <button
                        onClick={() => handleEdit(scenario)}
                        className="text-blue-600 hover:text-blue-900 p-1 rounded hover:bg-blue-50 transition-colors"
                        title="Modifier"
                      >
                        <Edit2 size={16} />
                      </button>
                      <button
                        onClick={() => onScenarioDelete(scenario.id)}
                        className="text-red-600 hover:text-red-900 p-1 rounded hover:bg-red-50 transition-colors"
                        title="Supprimer"
                      >
                        <Trash2 size={16} />
                      </button>
                    </div>
                  </td>
                </tr>

                {expandedScenario === scenario.id && (
                  <tr className="bg-gray-50">
                    <td colSpan="8" className="px-6 py-6">
                      <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
                        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                          {/* Description Section */}
                          <div>
                            <h4 className="font-semibold text-gray-900 mb-3 flex items-center">
                              <div className="w-2 h-2 bg-blue-500 rounded-full mr-2"></div>
                              Description complète
                            </h4>
                            <p className="text-gray-700 text-sm leading-relaxed">
                              {scenario.description || 'Aucune description disponible'}
                            </p>
                          </div>

                          {/* Metadata Section */}
                          <div>
                            <h4 className="font-semibold text-gray-900 mb-3 flex items-center">
                              <div className="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                              Informations techniques
                            </h4>
                            <div className="space-y-2">
                              <div className="flex justify-between">
                                <span className="text-sm text-gray-600">Difficulté de détection:</span>
                                <span className="text-sm font-medium text-gray-900">
                                  {scenario.detectionDifficulty || 'Non spécifié'}
                                </span>
                              </div>
                              <div className="flex justify-between">
                                <span className="text-sm text-gray-600">Ressources nécessaires:</span>
                                <span className="text-sm font-medium text-gray-900">
                                  {scenario.resources || 'Non spécifié'}
                                </span>
                              </div>
                            </div>
                          </div>
                        </div>

                        {/* Steps Section */}
                        <div className="mt-6">
                          <h4 className="font-semibold text-gray-900 mb-4 flex items-center">
                            <div className="w-2 h-2 bg-purple-500 rounded-full mr-2"></div>
                            Étapes opérationnelles ({scenario.steps?.length || scenario.operationalSteps?.length || 0})
                          </h4>
                          {(scenario.steps || scenario.operationalSteps) && (scenario.steps?.length > 0 || scenario.operationalSteps?.length > 0) ? (
                            <div className="space-y-4">
                              {(scenario.steps || scenario.operationalSteps).map((step, index) => {
                                // Get phase color based on EBIOS RM phases
                                const getPhaseColor = (phase) => {
                                  const colors = {
                                    'CONNAITRE': 'from-green-50 to-emerald-50 border-green-200',
                                    'RENTRER': 'from-yellow-50 to-amber-50 border-yellow-200',
                                    'TROUVER': 'from-blue-50 to-cyan-50 border-blue-200',
                                    'EXPLOITER': 'from-red-50 to-rose-50 border-red-200'
                                  };
                                  return colors[phase] || 'from-gray-50 to-slate-50 border-gray-200';
                                };

                                const getPhaseIcon = (phase) => {
                                  const icons = {
                                    'CONNAITRE': '🔍',
                                    'RENTRER': '🚪',
                                    'TROUVER': '🎯',
                                    'EXPLOITER': '⚡'
                                  };
                                  return icons[phase] || '📋';
                                };

                                return (
                                  <div key={step.id || index} className={`bg-gradient-to-r ${getPhaseColor(step.phase)} rounded-lg p-4 border`}>
                                    <div className="flex items-center mb-3">
                                      <span className="inline-flex items-center justify-center w-6 h-6 rounded-full bg-blue-100 text-blue-800 text-xs font-bold mr-3">
                                        {index + 1}
                                      </span>
                                      {step.phase && (
                                        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-white border mr-3">
                                          {getPhaseIcon(step.phase)} {step.phase}
                                        </span>
                                      )}
                                      <h5 className="font-medium text-gray-900 flex-1">{step.name || step.technique || `Étape ${index + 1}`}</h5>
                                      <span className="text-xs text-gray-500 bg-white px-2 py-1 rounded">
                                        {step.duration || 'Durée non spécifiée'}
                                      </span>
                                    </div>

                                  {step.description && (
                                    <p className="text-sm text-gray-700 mb-3">{step.description}</p>
                                  )}

                                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    {step.techniques && step.techniques.length > 0 && (
                                      <div>
                                        <span className="text-xs font-medium text-gray-600 uppercase tracking-wide">Techniques:</span>
                                        <div className="flex flex-wrap gap-1 mt-1">
                                          {step.techniques.map((technique, i) => (
                                            <span key={`technique-${step.id || index}-${i}`} className="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-blue-100 text-blue-800">
                                              {technique}
                                            </span>
                                          ))}
                                        </div>
                                      </div>
                                    )}

                                    {step.indicators && step.indicators.length > 0 && (
                                      <div>
                                        <span className="text-xs font-medium text-gray-600 uppercase tracking-wide">Indicateurs:</span>
                                        <div className="flex flex-wrap gap-1 mt-1">
                                          {step.indicators.map((indicator, i) => (
                                            <span key={`indicator-${step.id || index}-${i}`} className="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-green-100 text-green-800">
                                              {indicator}
                                            </span>
                                          ))}
                                        </div>
                                      </div>
                                    )}
                                    </div>
                                  </div>
                                );
                              })}
                            </div>
                          ) : (
                            <div className="text-center py-8 text-gray-500">
                              <Target size={32} className="mx-auto mb-2 text-gray-400" />
                              <p className="text-sm">Aucune étape opérationnelle définie</p>
                            </div>
                          )}
                        </div>
                      </div>
                    </td>
                  </tr>
                )}
              </React.Fragment>
            ))}
          </tbody>
        </table>
      </div>

      {/* Edit Modal */}
      {editingScenario && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-medium text-gray-900">Modifier le scénario</h3>
              <button
                onClick={handleCancelEdit}
                className="text-gray-400 hover:text-gray-600 transition-colors"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Nom du scénario</label>
                <input
                  type="text"
                  value={editingScenario.name || ''}
                  onChange={(e) => setEditingScenario({
                    ...editingScenario,
                    name: e.target.value
                  })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Description</label>
                <textarea
                  value={editingScenario.description || ''}
                  onChange={(e) => setEditingScenario({
                    ...editingScenario,
                    description: e.target.value
                  })}
                  rows="4"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Gravité</label>
                  <select
                    value={editingScenario.severity || ''}
                    onChange={(e) => setEditingScenario({
                      ...editingScenario,
                      severity: e.target.value
                    })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    <option value="Très faible">Très faible</option>
                    <option value="Faible">Faible</option>
                    <option value="Moyen">Moyen</option>
                    <option value="Élevé">Élevé</option>
                    <option value="Très élevé">Très élevé</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Probabilité</label>
                  <select
                    value={editingScenario.likelihood || ''}
                    onChange={(e) => setEditingScenario({
                      ...editingScenario,
                      likelihood: e.target.value
                    })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    <option value="Très faible">Très faible</option>
                    <option value="Faible">Faible</option>
                    <option value="Moyen">Moyen</option>
                    <option value="Élevé">Élevé</option>
                    <option value="Très élevé">Très élevé</option>
                  </select>
                </div>
              </div>
            </div>

            <div className="flex items-center justify-end space-x-3 mt-6 pt-4 border-t border-gray-200">
              <button
                onClick={handleCancelEdit}
                className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
              >
                Annuler
              </button>
              <button
                onClick={handleSaveEdit}
                className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
              >
                Sauvegarder
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default OperationalScenariosTable;