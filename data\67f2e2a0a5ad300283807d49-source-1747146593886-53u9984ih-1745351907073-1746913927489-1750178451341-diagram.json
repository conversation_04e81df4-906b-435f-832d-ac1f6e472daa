{"nodes": [{"id": "source-risk", "type": "sourceRisk", "position": {"x": 50, "y": 150}, "data": {"label": "Competiteur cherchant à dénigrer", "description": "Source de risque identifiée dans l'analyse", "riskLevel": "medium", "sequence": 1}, "width": 256, "height": 110}, {"id": "objectif-vise", "type": "businessValue", "position": {"x": 650, "y": 150}, "data": {"label": "Dénigrement de la réputation", "description": "Objectif visé par la source de risque", "dreadedEvent": "Divulgation de données sensibles", "businessValue": "Brand Reputation & Image", "sequence": 3}, "width": 320, "height": 229}, {"id": "stakeholder-1746913927489", "type": "stakeholder", "position": {"x": 319.0593496019113, "y": 10.290265236075527}, "data": {"label": "PP2", "description": "Partie prenante concernée par l'événement redouté", "sequence": 2}, "width": 256, "height": 126, "selected": false, "positionAbsolute": {"x": 319.0593496019113, "y": 10.290265236075527}, "dragging": false}, {"id": "node-4", "type": "businessValueStandalone", "position": {"x": 220.61182560799296, "y": 348.59360680008695}, "width": 256, "height": 110, "selected": true, "positionAbsolute": {"x": 220.61182560799296, "y": 348.59360680008695}, "dragging": false}], "edges": [{"id": "e-direct-attack", "source": "source-risk", "target": "objectif-vise", "label": "Attaque directe", "labelStyle": {"fill": "#ff4500", "fontWeight": 600}, "style": {"stroke": "#ff4500", "strokeWidth": 1.5}, "zIndex": 5, "data": {"edgeType": "direct-attack"}, "type": "animated"}, {"id": "e-source-stakeholder-1746913927489", "source": "source-risk", "target": "stakeholder-1746913927489", "type": "animated", "label": "Cible", "labelStyle": {"fill": "#D8000C", "fontWeight": 500}, "style": {"stroke": "#D8000C", "strokeWidth": 2}, "zIndex": 1}, {"id": "e-stakeholder-1746913927489-objectif", "source": "stakeholder-1746913927489", "target": "objectif-vise", "type": "animated", "label": "Vise", "labelStyle": {"fill": "#00529B", "fontWeight": 500}, "style": {"stroke": "#00529B", "strokeWidth": 2}, "zIndex": 1}, {"source": "node-4", "sourceHandle": "source-handle", "target": "objectif-vise", "targetHandle": "target-handle", "id": "edge-4", "animated": true, "label": "Connexion", "style": {"stroke": "#666", "strokeWidth": 2}, "labelStyle": {"fill": "#666", "fontWeight": 500}, "type": "animated"}]}