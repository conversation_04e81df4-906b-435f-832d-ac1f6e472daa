// src/hooks/useNavigationWarning.js
import { useEffect, useCallback } from 'react';

/**
 * Custom hook to warn users about unsaved changes when navigating within the app
 * This extends the useUnsavedChangesWarning hook to handle internal navigation
 * 
 * @param {boolean} hasUnsavedChanges - Whether there are unsaved changes
 * @returns {Object} - Object containing navigation warning functions
 */
const useNavigationWarning = (hasUnsavedChanges) => {
  // Function to show a confirmation dialog
  const showConfirmationDialog = useCallback(() => {
    return window.confirm('Vous avez des modifications non enregistrées. Êtes-vous sûr de vouloir quitter cette page?');
  }, []);

  // Function to handle navigation attempts
  const handleNavigation = useCallback((e) => {
    if (hasUnsavedChanges) {
      // Show confirmation dialog
      const confirmed = showConfirmationDialog();
      if (!confirmed) {
        // If not confirmed, prevent navigation
        e.preventDefault();
        e.stopPropagation();
        return false;
      }
    }
    return true;
  }, [hasUnsavedChanges, showConfirmationDialog]);

  // Function to handle keyboard shortcuts (Ctrl+Arrow)
  const handleKeyboardNavigation = useCallback((e) => {
    // Check for Ctrl+ArrowLeft or Ctrl+ArrowRight
    if ((e.ctrlKey || e.metaKey) && (e.key === 'ArrowLeft' || e.key === 'ArrowRight')) {
      if (hasUnsavedChanges) {
        // Show confirmation dialog
        const confirmed = showConfirmationDialog();
        if (!confirmed) {
          // If not confirmed, prevent default behavior
          e.preventDefault();
          e.stopPropagation();
          return false;
        }
      }
    }
    return true;
  }, [hasUnsavedChanges, showConfirmationDialog]);

  // Set up event listeners
  useEffect(() => {
    // Add event listener for keyboard navigation
    document.addEventListener('keydown', handleKeyboardNavigation);

    // Clean up event listener
    return () => {
      document.removeEventListener('keydown', handleKeyboardNavigation);
    };
  }, [handleKeyboardNavigation]);

  return {
    handleNavigation,
    handleKeyboardNavigation,
    showConfirmationDialog
  };
};

export default useNavigationWarning;
