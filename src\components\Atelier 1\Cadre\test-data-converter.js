/**
 * Convertit les données JSON au format attendu par le composant RACIVisualization
 * 
 * @param {Object} jsonData - Les données JSON brutes
 * @returns {Object} Les données formatées pour le composant
 */
const convertTestDataToRaciFormat = (jsonData) => {
    // Initialiser les structures de données
    const workshops = [];
    const matrix = {};
    const participants = [];
    
    // 1. Extraire tous les ateliers uniques des workshopRoles
    const workshopNames = new Set();
    jsonData.participants.forEach(p => {
      if (p.workshopRoles) {
        Object.keys(p.workshopRoles).forEach(w => workshopNames.add(w));
      }
    });
    
    // 2. Créer les objets workshop avec ID et nom
    Array.from(workshopNames).forEach((name) => {
      workshops.push({ id: name, name });
    });
    
    // 3. Convertir les participants et remplir la matrice
    jsonData.participants.forEach(p => {
      // Créer l'objet participant
      const participant = {
        id: p.id,
        name: p.name,
        position: p.position || "",
        customPosition: p.customPosition || ""
      };
      participants.push(participant);
      
      // Remplir la matrice pour ce participant
      matrix[p.id] = {};
      if (p.workshopRoles) {
        Object.entries(p.workshopRoles).forEach(([workshop, role]) => {
          matrix[p.id][workshop] = role;
        });
      }
      
      // S'assurer que tous les workshops sont dans la matrice
      workshops.forEach(w => {
        if (!matrix[p.id][w.id]) {
          matrix[p.id][w.id] = "";
        }
      });
    });
    
    return {
      participants,
      workshops,
      matrix
    };
  };
  
  /**
   * Convertit les données des composants au format JSON pour export
   * 
   * @param {Array} participants - Liste des participants
   * @param {Array} workshops - Liste des ateliers
   * @param {Object} matrix - Matrice des rôles
   * @returns {Object} Les données au format JSON
   */
  const convertRaciDataToJsonFormat = (participants, workshops, matrix) => {
    const jsonData = {
      participants: []
    };
    
    participants.forEach(p => {
      const participant = {
        id: p.id,
        name: p.name,
        position: p.position,
        workshopRoles: {}
      };
      
      if (p.customPosition) {
        participant.customPosition = p.customPosition;
      }
      
      workshops.forEach(w => {
        if (matrix[p.id] && matrix[p.id][w.id]) {
          participant.workshopRoles[w.id] = matrix[p.id][w.id];
        }
      });
      
      jsonData.participants.push(participant);
    });
    
    return jsonData;
  };
  
  export { convertTestDataToRaciFormat, convertRaciDataToJsonFormat };