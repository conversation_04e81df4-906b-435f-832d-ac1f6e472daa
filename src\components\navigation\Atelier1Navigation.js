import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
    ChevronDown, ChevronRight,
    LayoutList,
    Users,
    Gem,
    AlertTriangle,
    Shield,
    ListChecks,
    ClipboardCheck
} from 'lucide-react';
import { getUnsavedChanges, setNavigationWarningDialogOpen } from '../../utils/navigationUtils';

const ATELIER1_TABS = {
  CONTEXT: 'context',
  BUSINESS_VALUES: 'business-values',
  EVENTS: 'events',
  SECURITY: 'security',
  SECURITY_CONTROLS: 'security-controls',
  RISK_TREATMENT: 'risk-treatment'
};

const Atelier1Navigation = ({
  activeTab,
  setActiveTab,
  expanded,
  toggleExpand,
  baseNavItemClass,
  workshopTitleClass,
  activeStyle,
  inactiveStyle,
  parentActiveStyle
}) => {
  const { t } = useTranslation();
  // Check if any tab within this workshop is active
  const isAtelierActive = Object.values(ATELIER1_TABS).includes(activeTab);

  // Sub-item class with added padding
  const subItemClass = `${baseNavItemClass} pl-8`; // Indent sub-items

  return (
    <li className="w-full">
      <button
        className={`${workshopTitleClass} ${isAtelierActive ? 'text-white' : 'text-gray-300 hover:bg-gray-700 hover:text-white'} group`}
        onClick={toggleExpand}
      >
        <div className="flex items-center w-full justify-between">
          <div className="flex items-center">
            <div className={`${isAtelierActive ? 'bg-blue-500' : 'bg-gray-700 group-hover:bg-gray-600'} p-1.5 rounded-md transition-colors duration-200 mr-3`}>
              <LayoutList size={16} className="flex-shrink-0 text-white" />
            </div>
            <span className="truncate">{t('navigation.workshop1')}</span>
          </div>
          <div className="text-gray-400 transition-transform duration-200">
            {expanded ? <ChevronDown size={16} /> : <ChevronRight size={16} />}
          </div>
        </div>
      </button>

      {expanded && (
        <ul className="mt-1 space-y-1 w-full">
          <li>
            <button
              className={`${subItemClass} ${activeTab === ATELIER1_TABS.CONTEXT ? activeStyle : inactiveStyle} group`}
              onClick={() => {
                // Check for unsaved changes before navigation
                if (getUnsavedChanges()) {
                  // Show the warning dialog
                  setNavigationWarningDialogOpen(true);
                  // Store the target tab for later use
                  localStorage.setItem('ebiosrm_pending_tab', ATELIER1_TABS.CONTEXT);
                  return;
                }
                // If no unsaved changes, navigate directly
                setActiveTab(ATELIER1_TABS.CONTEXT);
              }}
            >
              <div className="flex items-center">
                <div className={`${activeTab === ATELIER1_TABS.CONTEXT ? 'bg-blue-500' : 'bg-gray-700 group-hover:bg-gray-600'} p-1 rounded transition-colors duration-200 mr-2`}>
                  <Users size={14} className="flex-shrink-0 text-white" />
                </div>
                <span className="truncate">{t('navigation.context')}</span>
              </div>
            </button>
          </li>
          <li>
            <button
              className={`${subItemClass} ${activeTab === ATELIER1_TABS.BUSINESS_VALUES ? activeStyle : inactiveStyle} group`}
              onClick={() => {
                // Check for unsaved changes before navigation
                if (getUnsavedChanges()) {
                  // Show the warning dialog
                  setNavigationWarningDialogOpen(true);
                  // Store the target tab for later use
                  localStorage.setItem('ebiosrm_pending_tab', ATELIER1_TABS.BUSINESS_VALUES);
                  return;
                }
                // If no unsaved changes, navigate directly
                setActiveTab(ATELIER1_TABS.BUSINESS_VALUES);
              }}
            >
              <div className="flex items-center">
                <div className={`${activeTab === ATELIER1_TABS.BUSINESS_VALUES ? 'bg-blue-500' : 'bg-gray-700 group-hover:bg-gray-600'} p-1 rounded transition-colors duration-200 mr-2`}>
                  <Gem size={14} className="flex-shrink-0 text-white" />
                </div>
                <span className="truncate">{t('navigation.businessValues')}</span>
              </div>
            </button>
          </li>
          <li>
            <button
              className={`${subItemClass} ${activeTab === ATELIER1_TABS.EVENTS ? activeStyle : inactiveStyle} group`}
              onClick={() => {
                // Check for unsaved changes before navigation
                if (getUnsavedChanges()) {
                  // Show the warning dialog
                  setNavigationWarningDialogOpen(true);
                  // Store the target tab for later use
                  localStorage.setItem('ebiosrm_pending_tab', ATELIER1_TABS.EVENTS);
                  return;
                }
                // If no unsaved changes, navigate directly
                setActiveTab(ATELIER1_TABS.EVENTS);
              }}
            >
              <div className="flex items-center">
                <div className={`${activeTab === ATELIER1_TABS.EVENTS ? 'bg-blue-500' : 'bg-gray-700 group-hover:bg-gray-600'} p-1 rounded transition-colors duration-200 mr-2`}>
                  <AlertTriangle size={14} className="flex-shrink-0 text-white" />
                </div>
                <span className="truncate">{t('navigation.dreadedEvents')}</span>
              </div>
            </button>
          </li>

          {/* Renamed and directly linked Socle de Sécurité */}
          <li>
            <button
              className={`${subItemClass} ${activeTab === ATELIER1_TABS.SECURITY ? activeStyle : inactiveStyle} group`}
              onClick={() => {
                // Check for unsaved changes before navigation
                if (getUnsavedChanges()) {
                  // Show the warning dialog
                  setNavigationWarningDialogOpen(true);
                  // Store the target tab for later use
                  localStorage.setItem('ebiosrm_pending_tab', ATELIER1_TABS.SECURITY);
                  return;
                }
                // If no unsaved changes, navigate directly
                setActiveTab(ATELIER1_TABS.SECURITY);
              }}
            >
              <div className="flex items-center">
                <div className={`${activeTab === ATELIER1_TABS.SECURITY ? 'bg-blue-500' : 'bg-gray-700 group-hover:bg-gray-600'} p-1 rounded transition-colors duration-200 mr-2`}>
                  <Shield size={14} className="flex-shrink-0 text-white" />
                </div>
                <span className="truncate">{t('navigation.securityFoundation')}</span>
              </div>
            </button>
          </li>
          {/* Renamed and directly linked Mesures de Contrôle */}
          <li>
            <button
              className={`${subItemClass} ${activeTab === ATELIER1_TABS.SECURITY_CONTROLS ? activeStyle : inactiveStyle} group`}
              onClick={() => {
                // Check for unsaved changes before navigation
                if (getUnsavedChanges()) {
                  // Show the warning dialog
                  setNavigationWarningDialogOpen(true);
                  // Store the target tab for later use
                  localStorage.setItem('ebiosrm_pending_tab', ATELIER1_TABS.SECURITY_CONTROLS);
                  return;
                }
                // If no unsaved changes, navigate directly
                setActiveTab(ATELIER1_TABS.SECURITY_CONTROLS);
              }}
            >
              <div className="flex items-center">
                <div className={`${activeTab === ATELIER1_TABS.SECURITY_CONTROLS ? 'bg-blue-500' : 'bg-gray-700 group-hover:bg-gray-600'} p-1 rounded transition-colors duration-200 mr-2`}>
                  <ListChecks size={14} className="flex-shrink-0 text-white" />
                </div>
                <span className="truncate">{t('navigation.securityControls')}</span>
              </div>
            </button>
          </li>
          <li>
            <button
              className={`${subItemClass} ${activeTab === ATELIER1_TABS.RISK_TREATMENT ? activeStyle : inactiveStyle} group`}
              onClick={() => {
                // Check for unsaved changes before navigation
                if (getUnsavedChanges()) {
                  // Show the warning dialog
                  setNavigationWarningDialogOpen(true);
                  // Store the target tab for later use
                  localStorage.setItem('ebiosrm_pending_tab', ATELIER1_TABS.RISK_TREATMENT);
                  return;
                }
                // If no unsaved changes, navigate directly
                setActiveTab(ATELIER1_TABS.RISK_TREATMENT);
              }}
            >
              <div className="flex items-center">
                <div className={`${activeTab === ATELIER1_TABS.RISK_TREATMENT ? 'bg-blue-500' : 'bg-gray-700 group-hover:bg-gray-600'} p-1 rounded transition-colors duration-200 mr-2`}>
                  <ClipboardCheck size={14} className="flex-shrink-0 text-white" />
                </div>
                <span className="truncate">{t('navigation.riskTreatment')}</span>
              </div>
            </button>
          </li>
        </ul>
      )}
    </li>
  );
};

export default Atelier1Navigation;
