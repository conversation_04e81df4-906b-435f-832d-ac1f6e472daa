// src/components/Atelier4/Atelier4.js
import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { AlertCircle, Search, LogIn, MapPin, Target, BarChart3 } from 'lucide-react';
import Activite1 from './Activite1/Activite1';
import Activite2 from './Activite2/Activite2';
import WorkflowContainer from './WorkflowContainer';
import { useAnalysis } from '../../context/AnalysisContext';

const Atelier4 = () => {
  const [activeActivity, setActiveActivity] = useState('workflow');
  const { currentAnalysis } = useAnalysis();

  // Load data when component mounts
  useEffect(() => {
    const loadInitialData = async () => {
      if (currentAnalysis?.id) {
        try {
          console.log('Atelier4 - Component mounted, letting Activite1 handle data loading');
          // We'll let the Activite1 component handle the data loading directly
        } catch (error) {
          console.error('Error loading initial Atelier 4 data:', error);
        }
      }
    };

    loadInitialData();
  }, [currentAnalysis?.id]);

  // Helper function to get progress status for each activity
  const getProgressStatus = (activity) => {
    // This will be implemented later when we have progress tracking
    return 'pending';
  };

  // Helper function to get status icon based on progress
  const getStatusIcon = (status) => {
    switch (status) {
      case 'completed':
        return <span className="w-2 h-2 bg-green-500 rounded-full mr-2"></span>;
      case 'in-progress':
        return <span className="w-2 h-2 bg-yellow-500 rounded-full mr-2"></span>;
      case 'pending':
      default:
        return <span className="w-2 h-2 bg-gray-300 rounded-full mr-2"></span>;
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-100">
        <h1 className="text-2xl font-bold text-gray-800 mb-2">🚀 Atelier 4: Scénarios opérationnels - UPDATED!</h1>
        <p className="text-gray-600 mb-4">
          Élaborer des graphes d'attaque et évaluer la vraisemblance des scénarios opérationnels.
        </p>

        {/* Activity Tabs */}
        <div className="border-b border-gray-200">
          <div className="flex space-x-1">
            <button
              className={`flex items-center px-4 py-2 rounded-md ${
                activeActivity === 'workflow'
                  ? 'bg-indigo-50 text-indigo-700 font-medium'
                  : 'text-gray-600 hover:bg-gray-50'
              }`}
              onClick={() => setActiveActivity('workflow')}
            >
              {getStatusIcon(getProgressStatus('workflow'))}
              <div className="ml-2 flex items-center">
                <Search size={16} className="mr-1.5" />
                <span>Workflow CTI</span>
              </div>
            </button>
            <button
              className={`flex items-center px-4 py-2 rounded-md ${
                activeActivity === 'activite1'
                  ? 'bg-blue-50 text-blue-700 font-medium'
                  : 'text-gray-600 hover:bg-gray-50'
              }`}
              onClick={() => setActiveActivity('activite1')}
            >
              {getStatusIcon(getProgressStatus('activite1'))}
              <div className="ml-2 flex items-center">
                <Target size={16} className="mr-1.5" />
                <span>Graphes d'attaque</span>
              </div>
            </button>
            <button
              className={`flex items-center px-4 py-2 rounded-md ${
                activeActivity === 'activite2'
                  ? 'bg-blue-50 text-blue-700 font-medium'
                  : 'text-gray-600 hover:bg-gray-50'
              }`}
              onClick={() => setActiveActivity('activite2')}
            >
              {getStatusIcon(getProgressStatus('activite2'))}
              <div className="ml-2 flex items-center">
                <BarChart3 size={16} className="mr-1.5" />
                <span>Scénarios opérationnels</span>
              </div>
            </button>
          </div>
        </div>

        {/* Activity Content */}
        <div>
          {activeActivity === 'workflow' && <WorkflowContainer />}
          {activeActivity === 'activite1' && <Activite1 />}
          {activeActivity === 'activite2' && <Activite2 />}
        </div>
      </div>
    </div>
  );
};

export default Atelier4;