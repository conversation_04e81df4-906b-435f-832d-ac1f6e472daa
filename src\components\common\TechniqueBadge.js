// src/components/common/TechniqueBadge.js
// Reusable component for displaying MITRE ATT&CK technique badges with correct URLs

import React from 'react';
import { ExternalLink, Info } from 'lucide-react';
import { generateMitreUrl, getTechniqueColor, getTechniqueIcon, isValidTechniqueId } from '../../utils/mitreUtils';

const TechniqueBadge = ({ 
  technique, 
  size = 'sm', 
  showIcon = true, 
  showLink = true, 
  showTooltip = true,
  className = '',
  onClick = null 
}) => {
  // Handle different input formats
  const techniqueData = typeof technique === 'string' 
    ? { id: technique, name: technique }
    : technique;

  if (!techniqueData || !techniqueData.id) {
    return null;
  }

  const isValid = isValidTechniqueId(techniqueData.id);
  const techniqueUrl = generateMitreUrl(techniqueData.id);
  const techniqueColor = getTechniqueColor(techniqueData.tactic);
  const techniqueIcon = getTechniqueIcon(techniqueData.tactic);

  // Size variants
  const sizeClasses = {
    xs: 'px-1 py-0.5 text-xs',
    sm: 'px-2 py-1 text-xs',
    md: 'px-3 py-1.5 text-sm',
    lg: 'px-4 py-2 text-base'
  };

  const iconSizes = {
    xs: 'w-3 h-3',
    sm: 'w-3 h-3', 
    md: 'w-4 h-4',
    lg: 'w-5 h-5'
  };

  const baseClasses = `
    inline-flex items-center gap-1 rounded-full font-medium transition-all duration-200
    ${sizeClasses[size]}
    ${isValid ? 'cursor-pointer hover:shadow-md' : 'opacity-75'}
    ${className}
  `;

  const badgeStyle = {
    backgroundColor: `${techniqueColor}20`,
    borderColor: techniqueColor,
    color: techniqueColor,
    border: `1px solid ${techniqueColor}40`
  };

  const handleClick = (e) => {
    if (onClick) {
      e.preventDefault();
      onClick(techniqueData);
    } else if (showLink && isValid) {
      window.open(techniqueUrl, '_blank', 'noopener,noreferrer');
    }
  };

  const BadgeContent = () => (
    <>
      {showIcon && (
        <span className="flex-shrink-0" role="img" aria-label={techniqueData.tactic || 'technique'}>
          {techniqueIcon}
        </span>
      )}
      <span className="font-semibold">
        {techniqueData.id}
      </span>
      {techniqueData.name && techniqueData.name !== techniqueData.id && (
        <span className="hidden sm:inline truncate max-w-32">
          ({techniqueData.name})
        </span>
      )}
      {showLink && isValid && (
        <ExternalLink className={`flex-shrink-0 ${iconSizes[size]}`} />
      )}
    </>
  );

  if (showTooltip && techniqueData.description) {
    return (
      <div className="relative group">
        <div
          className={baseClasses}
          style={badgeStyle}
          onClick={handleClick}
          role={onClick || showLink ? "button" : "text"}
          tabIndex={onClick || showLink ? 0 : -1}
          onKeyDown={(e) => {
            if ((e.key === 'Enter' || e.key === ' ') && (onClick || showLink)) {
              e.preventDefault();
              handleClick(e);
            }
          }}
        >
          <BadgeContent />
        </div>
        
        {/* Tooltip */}
        <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 bg-gray-900 text-white text-xs rounded-lg shadow-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none z-50 max-w-xs">
          <div className="font-semibold mb-1">
            {techniqueData.id}: {techniqueData.name}
          </div>
          <div className="text-gray-300">
            {techniqueData.description}
          </div>
          {techniqueData.tactic && (
            <div className="text-gray-400 text-xs mt-1">
              Tactic: {techniqueData.tactic}
            </div>
          )}
          {/* Tooltip arrow */}
          <div className="absolute top-full left-1/2 transform -translate-x-1/2 border-4 border-transparent border-t-gray-900"></div>
        </div>
      </div>
    );
  }

  return (
    <div
      className={baseClasses}
      style={badgeStyle}
      onClick={handleClick}
      role={onClick || showLink ? "button" : "text"}
      tabIndex={onClick || showLink ? 0 : -1}
      onKeyDown={(e) => {
        if ((e.key === 'Enter' || e.key === ' ') && (onClick || showLink)) {
          e.preventDefault();
          handleClick(e);
        }
      }}
    >
      <BadgeContent />
    </div>
  );
};

// Component for displaying multiple technique badges
export const TechniqueBadgeList = ({ 
  techniques = [], 
  maxDisplay = 5, 
  size = 'sm',
  showMoreButton = true,
  className = '',
  onTechniqueClick = null
}) => {
  const [showAll, setShowAll] = React.useState(false);
  
  if (!techniques || techniques.length === 0) {
    return (
      <span className="text-gray-500 text-sm italic">
        No techniques available
      </span>
    );
  }

  const displayTechniques = showAll ? techniques : techniques.slice(0, maxDisplay);
  const hasMore = techniques.length > maxDisplay;

  return (
    <div className={`flex flex-wrap gap-1 ${className}`}>
      {displayTechniques.map((technique, index) => (
        <TechniqueBadge
          key={technique.id || index}
          technique={technique}
          size={size}
          onClick={onTechniqueClick}
        />
      ))}
      
      {hasMore && !showAll && showMoreButton && (
        <button
          onClick={() => setShowAll(true)}
          className="inline-flex items-center gap-1 px-2 py-1 text-xs bg-gray-100 hover:bg-gray-200 text-gray-600 rounded-full transition-colors duration-200"
        >
          <Info className="w-3 h-3" />
          +{techniques.length - maxDisplay} more
        </button>
      )}
      
      {showAll && hasMore && showMoreButton && (
        <button
          onClick={() => setShowAll(false)}
          className="inline-flex items-center gap-1 px-2 py-1 text-xs bg-gray-100 hover:bg-gray-200 text-gray-600 rounded-full transition-colors duration-200"
        >
          Show less
        </button>
      )}
    </div>
  );
};

// Component for technique statistics
export const TechniqueStats = ({ techniques = [] }) => {
  if (!techniques || techniques.length === 0) {
    return null;
  }

  // Group by tactic
  const tacticCounts = techniques.reduce((acc, technique) => {
    const tactic = technique.tactic || 'Unknown';
    acc[tactic] = (acc[tactic] || 0) + 1;
    return acc;
  }, {});

  const totalTechniques = techniques.length;
  const uniqueTactics = Object.keys(tacticCounts).length;

  return (
    <div className="bg-gray-50 rounded-lg p-3">
      <div className="flex items-center justify-between mb-2">
        <span className="text-sm font-medium text-gray-700">
          Technique Coverage
        </span>
        <span className="text-xs text-gray-500">
          {totalTechniques} techniques, {uniqueTactics} tactics
        </span>
      </div>
      
      <div className="grid grid-cols-2 gap-2 text-xs">
        {Object.entries(tacticCounts)
          .sort(([,a], [,b]) => b - a)
          .slice(0, 6)
          .map(([tactic, count]) => (
            <div key={tactic} className="flex justify-between">
              <span className="text-gray-600 truncate">{tactic}</span>
              <span className="font-medium text-gray-900">{count}</span>
            </div>
          ))}
      </div>
    </div>
  );
};

export default TechniqueBadge;
