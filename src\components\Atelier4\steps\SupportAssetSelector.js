// components/Atelier4/steps/SupportAssetSelector.js
import React, { useState, useEffect } from 'react';
import { useWorkflow } from '../../../contexts/WorkflowContext';
import { api } from '../../../api/apiClient';
import { Database, AlertTriangle, ArrowRight, CheckCircle, Server, Shield, Package } from 'lucide-react';

const SupportAssetSelector = () => {
  const { updateStep, selectedSupportAssets, currentSession, selectedAttackPath } = useWorkflow();
  const [availableAssets, setAvailableAssets] = useState([]);
  const [selectedAssets, setSelectedAssets] = useState(selectedSupportAssets || []);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [isUpdating, setIsUpdating] = useState(false);

  useEffect(() => {
    if (currentSession) {
      loadSupportAssets();
    }
  }, [currentSession]);

  useEffect(() => {
    if (selectedSupportAssets && selectedSupportAssets.length > 0) {
      setSelectedAssets(selectedSupportAssets);
    }
  }, [selectedSupportAssets]);

  const loadSupportAssets = async () => {
    try {
      setIsLoading(true);
      setError(null);

      console.log('[SupportAssetSelector] Loading support assets for session:', currentSession._id);

      const response = await api.get(`/workflow/sessions/${currentSession._id}/support-assets`);

      console.log('[SupportAssetSelector] API response:', response);

      if (response.data.success) {
        setAvailableAssets(response.data.data);

        // Si aucun actif n'est encore sélectionné, sélectionner tous par défaut
        if (selectedAssets.length === 0) {
          setSelectedAssets(response.data.data.map(asset => ({
            ...asset,
            selected: true
          })));
        }

        console.log('[SupportAssetSelector] Loaded support assets:', response.data.data.length);
      } else {
        console.error('[SupportAssetSelector] API returned error:', response.data.message);
        throw new Error(response.data.message || 'Erreur lors du chargement des biens supports');
      }
    } catch (error) {
      console.error('[SupportAssetSelector] Error loading support assets:', error);
      console.error('[SupportAssetSelector] Error details:', error.response?.data);
      setError(error.response?.data?.message || error.message || 'Erreur lors du chargement des biens supports');
    } finally {
      setIsLoading(false);
    }
  };

  const handleAssetToggle = (assetId) => {
    setSelectedAssets(prev =>
      prev.map(asset =>
        asset.assetId === assetId
          ? { ...asset, selected: !asset.selected }
          : asset
      )
    );
  };

  const handleSelectAll = () => {
    setSelectedAssets(prev =>
      prev.map(asset => ({ ...asset, selected: true }))
    );
  };

  const handleDeselectAll = () => {
    setSelectedAssets(prev =>
      prev.map(asset => ({ ...asset, selected: false }))
    );
  };

  const handleContinue = async () => {
    const selectedCount = selectedAssets.filter(asset => asset.selected).length;

    if (selectedCount === 0) {
      setError('Veuillez sélectionner au moins un bien support pour continuer.');
      return;
    }

    setIsUpdating(true);

    try {
      const assetsData = {
        selectedSupportAssets: selectedAssets
      };

      const success = await updateStep(3, assetsData);

      if (success) {
        console.log('[SupportAssetSelector] Successfully moved to step 3');
      }
    } catch (error) {
      console.error('[SupportAssetSelector] Error updating step:', error);
      setError('Erreur lors de la sauvegarde. Veuillez réessayer.');
    } finally {
      setIsUpdating(false);
    }
  };

  const getCriticalityColor = (criticality) => {
    switch (criticality?.toLowerCase()) {
      case 'critique':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'élevé':
        return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'moyen':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'faible':
        return 'bg-green-100 text-green-800 border-green-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getAssetTypeIcon = (type) => {
    switch (type?.toLowerCase()) {
      case 'equipements':
        return <Server className="h-5 w-5 text-purple-500" />;
      case 'logiciels':
        return <Package className="h-5 w-5 text-blue-500" />;
      case 'reseaux':
        return <Shield className="h-5 w-5 text-green-500" />;
      default:
        return <Database className="h-5 w-5 text-gray-500" />;
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Chargement des biens supports...</p>
        </div>
      </div>
    );
  }

  if (!selectedAttackPath) {
    return (
      <div className="text-center py-12">
        <AlertTriangle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">
          Chemin d'attaque requis
        </h3>
        <p className="text-gray-600">
          Veuillez d'abord sélectionner un chemin d'attaque à l'étape précédente.
        </p>
      </div>
    );
  }

  const selectedCount = selectedAssets.filter(asset => asset.selected).length;

  return (
    <div className="space-y-8">
      {/* En-tête */}
      <div className="text-center">
        <div className="flex items-center justify-center mb-4">
          <div className="p-3 bg-indigo-100 rounded-full">
            <Database className="h-8 w-8 text-indigo-600" />
          </div>
        </div>
        <h1 className="text-3xl font-bold text-gray-900 mb-2">
          Sélection des Biens Supports
        </h1>
        <p className="text-lg text-gray-600 max-w-2xl mx-auto">
          Choisissez les biens supports à analyser en détail. Ces actifs seront examinés
          pour identifier les vulnérabilités et techniques d'attaque spécifiques.
        </p>
      </div>

      {/* Résumé du chemin d'attaque sélectionné */}
      {selectedAttackPath && (
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">
            Chemin d'Attaque Sélectionné
          </h2>
          <div className="bg-indigo-50 border border-indigo-200 rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="font-medium text-indigo-900 mb-2">
                  {selectedAttackPath.pathName}
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                  {selectedAttackPath.sourceRisk && (
                    <div className="flex items-center">
                      <div className="w-2 h-2 bg-red-500 rounded-full mr-2"></div>
                      <span className="text-gray-700">Source: {selectedAttackPath.sourceRisk}</span>
                    </div>
                  )}
                  {selectedAttackPath.targetObjectives?.length > 0 && (
                    <div className="flex items-center">
                      <div className="w-2 h-2 bg-blue-500 rounded-full mr-2"></div>
                      <span className="text-gray-700">
                        {selectedAttackPath.targetObjectives.length} objectif(s)
                      </span>
                    </div>
                  )}
                  {selectedAttackPath.supportAssets?.length > 0 && (
                    <div className="flex items-center">
                      <div className="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                      <span className="text-gray-700">
                        {selectedAttackPath.supportAssets.length} bien(s) support
                      </span>
                    </div>
                  )}
                </div>
              </div>
              {selectedAttackPath.criticality && (
                <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 border border-yellow-200">
                  {selectedAttackPath.criticality}
                </span>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Contrôles de sélection */}
      <div className="bg-white rounded-lg shadow p-6">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-semibold text-gray-900">
            Biens Supports Disponibles ({availableAssets.length})
          </h2>
          <div className="flex space-x-2">
            <button
              onClick={handleSelectAll}
              className="px-3 py-1 text-sm bg-indigo-100 text-indigo-700 rounded-md hover:bg-indigo-200"
            >
              Tout sélectionner
            </button>
            <button
              onClick={handleDeselectAll}
              className="px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200"
            >
              Tout désélectionner
            </button>
          </div>
        </div>

        <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
          <div className="flex items-center justify-between">
            <span className="text-blue-900 font-medium">
              {selectedCount} bien(s) sélectionné(s) pour l'analyse détaillée
            </span>
            {selectedCount > 0 && (
              <CheckCircle className="h-5 w-5 text-blue-600" />
            )}
          </div>
        </div>
      </div>

      {/* Liste des biens supports */}
      {availableAssets.length === 0 ? (
        <div className="text-center py-12">
          <Database className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            Aucun bien support trouvé
          </h3>
          <p className="text-gray-600">
            Veuillez d'abord définir des biens supports dans les valeurs métier.
          </p>
        </div>
      ) : (
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {selectedAssets.map((asset) => (
            <div
              key={asset.assetId}
              className={`
                relative p-4 border-2 rounded-lg cursor-pointer transition-all duration-200
                ${asset.selected
                  ? 'border-indigo-500 bg-indigo-50 shadow-md'
                  : 'border-gray-200 bg-white hover:border-gray-300 hover:shadow-sm'
                }
              `}
              onClick={() => handleAssetToggle(asset.assetId)}
            >
              {/* Checkbox */}
              <div className="absolute top-3 right-3">
                {asset.selected ? (
                  <CheckCircle className="h-5 w-5 text-indigo-600" />
                ) : (
                  <div className="h-5 w-5 border-2 border-gray-300 rounded-full" />
                )}
              </div>

              {/* Contenu */}
              <div className="pr-8">
                <div className="flex items-start mb-3">
                  {getAssetTypeIcon(asset.assetType)}
                  <div className="ml-3 flex-1">
                    <h3 className="font-semibold text-gray-900 text-sm">
                      {asset.assetName}
                    </h3>
                    <p className="text-xs text-gray-600 mt-1">
                      {asset.assetType}
                    </p>
                  </div>
                </div>

                {/* Criticité */}
                <div className="mb-3">
                  <span className={`
                    inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border
                    ${getCriticalityColor(asset.criticality)}
                  `}>
                    {asset.criticality || 'Non définie'}
                  </span>
                </div>

                {/* Détails techniques */}
                {(asset.vendor || asset.product || asset.version) && (
                  <div className="text-xs text-gray-600 space-y-1">
                    {asset.vendor && (
                      <div><span className="font-medium">Vendeur:</span> {asset.vendor}</div>
                    )}
                    {asset.product && (
                      <div><span className="font-medium">Produit:</span> {asset.product}</div>
                    )}
                    {asset.version && (
                      <div><span className="font-medium">Version:</span> {asset.version}</div>
                    )}
                  </div>
                )}

                {/* Valeur métier associée */}
                {asset.businessValueName && (
                  <div className="mt-2 pt-2 border-t border-gray-200">
                    <div className="text-xs text-gray-600">
                      <span className="font-medium">Valeur métier:</span> {asset.businessValueName}
                    </div>
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Bouton de continuation */}
      {availableAssets.length > 0 && (
        <div className="flex justify-center pt-6">
          <button
            onClick={handleContinue}
            disabled={selectedCount === 0 || isUpdating}
            className={`
              flex items-center px-8 py-3 rounded-lg font-medium transition-all duration-200
              ${selectedCount > 0 && !isUpdating
                ? 'bg-indigo-600 text-white hover:bg-indigo-700 shadow-md hover:shadow-lg'
                : 'bg-gray-300 text-gray-500 cursor-not-allowed'
              }
            `}
          >
            {isUpdating ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Sauvegarde...
              </>
            ) : (
              <>
                Continuer vers l'analyse détaillée ({selectedCount} sélectionné{selectedCount > 1 ? 's' : ''})
                <ArrowRight className="ml-2 h-4 w-4" />
              </>
            )}
          </button>
        </div>
      )}

      {/* Erreur */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <div className="flex">
            <AlertTriangle className="h-5 w-5 text-red-400" />
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">Erreur</h3>
              <p className="mt-1 text-sm text-red-700">{error}</p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default SupportAssetSelector;
