// ../my-ebios-app/src/constants/manualControlsData.js

export const MANUAL_CONTROLS_DATA = [
  { description: "Encrypt sensitive data at rest using strong algorithms.", pillar: "Confidentialité", strategy: "Réduire le risque" },
  { description: "Implement strong role-based access control (RBAC) policies.", pillar: "Confidentialité", strategy: "Réduire le risque" },
  { description: "Enforce multi-factor authentication (MFA) for remote access.", pillar: "Confidentialité", strategy: "Réduire le risque" },
  { description: "Use TLS/SSL encryption for all data transmission over networks.", pillar: "Confidentialité", strategy: "Réduire le risque" },
  { description: "Deploy Data Loss Prevention (DLP) solutions on endpoints and network.", pillar: "Confidentialité", strategy: "Réduire le risque" },
  { description: "Regularly train employees on data handling and confidentiality policies.", pillar: "Confidentialité", strategy: "Rédu<PERSON> le risque" },
  { description: "Implement data masking or anonymization in non-production environments.", pillar: "Confidentialité", strategy: "Réduire le risque" },
  { description: "Conduct periodic security audits of access permissions.", pillar: "Confidentialité", strategy: "Réduire le risque" },
  { description: "Enforce strong password complexity and rotation policies.", pillar: "Confidentialité", strategy: "Réduire le risque" },
  { description: "Secure physical documents containing sensitive information.", pillar: "Confidentialité", strategy: "Réduire le risque" },
  { description: "Avoid collecting personally identifiable information (PII) unless strictly necessary.", pillar: "Confidentialité", strategy: "Éviter le risque" },
  { description: "Prohibit use of unencrypted portable storage devices (USB drives).", pillar: "Confidentialité", strategy: "Éviter le risque" },
  { description: "Do not store sensitive data on publicly accessible servers.", pillar: "Confidentialité", strategy: "Éviter le risque" },
  { description: "Restrict access to sensitive data repositories entirely for non-essential personnel.", pillar: "Confidentialité", strategy: "Éviter le risque" },
  { description: "Avoid transmitting sensitive data over unsecured public Wi-Fi networks.", pillar: "Confidentialité", strategy: "Éviter le risque" },
  { description: "Destroy sensitive data securely once its retention period expires.", pillar: "Confidentialité", strategy: "Éviter le risque" },
  { description: "Forbid sharing of account credentials among users.", pillar: "Confidentialité", strategy: "Éviter le risque" },
  { description: "Avoid using third-party services with inadequate security postures for sensitive data.", pillar: "Confidentialité", strategy: "Éviter le risque" },
  { description: "Disable unnecessary network services handling sensitive information.", pillar: "Confidentialité", strategy: "Éviter le risque" },
  { description: "Prohibit installation of unauthorized software on systems accessing sensitive data.", pillar: "Confidentialité", strategy: "Éviter le risque" },
  { description: "Purchase cyber insurance covering data breach incidents and liability.", pillar: "Confidentialité", strategy: "Transférer le risque" },
  { description: "Outsource secure data destruction services to a certified vendor.", pillar: "Confidentialité", strategy: "Transférer le risque" },
  { description: "Use a cloud provider with robust security controls and SLAs for confidentiality.", pillar: "Confidentialité", strategy: "Transférer le risque" },
  { description: "Employ a third-party Managed Security Service Provider (MSSP) for monitoring.", pillar: "Confidentialité", strategy: "Transférer le risque" },
  { description: "Utilize third-party penetration testing services to identify vulnerabilities.", pillar: "Confidentialité", strategy: "Transférer le risque" },
  { description: "Implement secure email gateway services from a specialized vendor.", pillar: "Confidentialité", strategy: "Transférer le risque" },
  { description: "Rely on vendor contracts for data protection responsibilities in SaaS applications.", pillar: "Confidentialité", strategy: "Transférer le risque" },
  { description: "Use a trusted third-party escrow service for sensitive key management.", pillar: "Confidentialité", strategy: "Transférer le risque" },
  { description: "Outsource background checks for personnel handling highly sensitive data.", pillar: "Confidentialité", strategy: "Transférer le risque" },
  { description: "Engage legal counsel specialized in data privacy regulations.", pillar: "Confidentialité", strategy: "Transférer le risque" },
  { description: "Formally accept risk of incidental data exposure in open office environments.", pillar: "Confidentialité", strategy: "Accepter le risque" },
  { description: "Document acceptance of residual risk after implementing encryption controls.", pillar: "Confidentialité", strategy: "Accepter le risque" },
  { description: "Accept risk for specific low-sensitivity data sets deemed non-critical.", pillar: "Confidentialité", strategy: "Accepter le risque" },
  { description: "Acknowledge risk associated with legacy systems where encryption is infeasible.", pillar: "Confidentialité", strategy: "Accepter le risque" },
  { description: "Accept the risk of using anonymized data for analysis (potential re-identification).", pillar: "Confidentialité", strategy: "Accepter le risque" },
  { description: "Decide not to implement costly controls for data with very low impact if compromised.", pillar: "Confidentialité", strategy: "Accepter le risque" },
  { description: "Accept risk of internal data misuse after providing adequate training.", pillar: "Confidentialité", strategy: "Accepter le risque" },
  { description: "Formally accept risk of data exposure during necessary system maintenance windows.", pillar: "Confidentialité", strategy: "Accepter le risque" },
  { description: "Acknowledge and accept limitations of DLP effectiveness against determined insiders.", pillar: "Confidentialité", strategy: "Accepter le risque" },
  { description: "Accept risk of metadata exposure even when content is encrypted.", pillar: "Confidentialité", strategy: "Accepter le risque" },
  { description: "Implement File Integrity Monitoring (FIM) on critical system files.", pillar: "Intégrité", strategy: "Réduire le risque" },
  { description: "Use digital signatures to verify software and document authenticity.", pillar: "Intégrité", strategy: "Réduire le risque" },
  { description: "Employ version control systems for configuration files and source code.", pillar: "Intégrité", strategy: "Réduire le risque" },
  { description: "Perform regular data backups and test restoration procedures frequently.", pillar: "Intégrité", strategy: "Réduire le risque" },
  { description: "Validate all user inputs to prevent injection attacks (SQLi, XSS).", pillar: "Intégrité", strategy: "Réduire le risque" },
  { description: "Apply security patches promptly to operating systems and applications.", pillar: "Intégrité", strategy: "Réduire le risque" },
  { description: "Use cryptographic hashes (checksums) to verify data integrity during transfer.", pillar: "Intégrité", strategy: "Réduire le risque" },
  { description: "Deploy Intrusion Detection/Prevention Systems (IDPS) to block malicious modifications.", pillar: "Intégrité", strategy: "Réduire le risque" },
  { description: "Enforce secure coding practices throughout the software development lifecycle.", pillar: "Intégrité", strategy: "Réduire le risque" },
  { description: "Implement change management processes with approval workflows.", pillar: "Intégrité", strategy: "Réduire le risque" },
  { description: "Restrict write access to critical configuration files and directories.", pillar: "Intégrité", strategy: "Éviter le risque" },
  { description: "Use immutable infrastructure deployments where possible.", pillar: "Intégrité", strategy: "Éviter le risque" },
  { description: "Prohibit direct database modification access for developers in production.", pillar: "Intégrité", strategy: "Éviter le risque" },
  { description: "Disable installation of unauthorized software or browser extensions.", pillar: "Intégrité", strategy: "Éviter le risque" },
  { description: "Avoid using software from untrusted or unverified sources.", pillar: "Intégrité", strategy: "Éviter le risque" },
  { description: "Use Write-Once-Read-Many (WORM) storage for critical logs and archives.", pillar: "Intégrité", strategy: "Éviter le risque" },
  { description: "Eliminate default passwords and configurations on all systems.", pillar: "Intégrité", strategy: "Éviter le risque" },
  { description: "Avoid running systems with known, unpatchable critical vulnerabilities.", pillar: "Intégrité", strategy: "Éviter le risque" },
  { description: "Prevent users from disabling endpoint security software.", pillar: "Intégrité", strategy: "Éviter le risque" },
  { description: "Do not allow code deployment without prior testing and approval.", pillar: "Intégrité", strategy: "Éviter le risque" },
  { description: "Use a trusted third-party Certificate Authority (CA) for SSL/TLS certificates.", pillar: "Intégrité", strategy: "Transférer le risque" },
  { description: "Host critical applications with a provider offering integrity guarantees and monitoring.", pillar: "Intégrité", strategy: "Transférer le risque" },
  { description: "Utilize a Content Delivery Network (CDN) with built-in integrity checks.", pillar: "Intégrité", strategy: "Transférer le risque" },
  { description: "Purchase insurance covering data corruption or malicious system alteration.", pillar: "Intégrité", strategy: "Transférer le risque" },
  { description: "Rely on SaaS vendors to maintain the integrity of their platforms.", pillar: "Intégrité", strategy: "Transférer le risque" },
  { description: "Outsource patch management to a specialized service provider.", pillar: "Intégrité", strategy: "Transférer le risque" },
  { description: "Use third-party code signing services to assure software integrity.", pillar: "Intégrité", strategy: "Transférer le risque" },
  { description: "Employ external security auditors to verify integrity controls.", pillar: "Intégrité", strategy: "Transférer le risque" },
  { description: "Use cloud provider's managed database services with integrity features.", pillar: "Intégrité", strategy: "Transférer le risque" },
  { description: "Transfer risk of malware infections through managed endpoint detection response (EDR).", pillar: "Intégrité", strategy: "Transférer le risque" },
  { description: "Accept minor data inconsistencies in non-critical internal reporting tools.", pillar: "Intégrité", strategy: "Accepter le risque" },
  { description: "Formally accept risk of configuration drift on development/testing environments.", pillar: "Intégrité", strategy: "Accepter le risque" },
  { description: "Accept potential for minor user data entry errors after providing training.", pillar: "Intégrité", strategy: "Accepter le risque" },
  { description: "Document acceptance of using legacy software where integrity verification is limited.", pillar: "Intégrité", strategy: "Accepter le risque" },
  { description: "Accept risk of undetected, low-impact changes in non-critical systems.", pillar: "Intégrité", strategy: "Accepter le risque" },
  { description: "Decide against implementing costly real-time FIM on low-risk assets.", pillar: "Intégrité", strategy: "Accepter le risque" },
  { description: "Accept the residual risk of zero-day exploits bypassing existing controls.", pillar: "Intégrité", strategy: "Accepter le risque" },
  { description: "Acknowledge potential integrity issues from third-party data feeds (after validation).", pillar: "Intégrité", strategy: "Accepter le risque" },
  { description: "Accept risk that backups might have minor inconsistencies between backup cycles.", pillar: "Intégrité", strategy: "Accepter le risque" },
  { description: "Formally accept risks associated with user-managed spreadsheet data integrity.", pillar: "Intégrité", strategy: "Accepter le risque" },
  { description: "Implement server load balancing across multiple systems.", pillar: "Disponibilité", strategy: "Réduire le risque" },
  { description: "Use redundant hardware components (power supplies, NICs, disks - RAID).", pillar: "Disponibilité", strategy: "Réduire le risque" },
  { description: "Maintain and regularly test a comprehensive Disaster Recovery (DR) plan.", pillar: "Disponibilité", strategy: "Réduire le risque" },
  { description: "Perform regular system backups and ensure recoverability.", pillar: "Disponibilité", strategy: "Réduire le risque" },
  { description: "Deploy DDoS mitigation solutions at the network edge.", pillar: "Disponibilité", strategy: "Réduire le risque" },
  { description: "Monitor system performance and resource utilization proactively.", pillar: "Disponibilité", strategy: "Réduire le risque" },
  { description: "Implement automated failover mechanisms for critical services.", pillar: "Disponibilité", strategy: "Réduire le risque" },
  { description: "Ensure adequate network bandwidth and redundancy (multiple ISPs).", pillar: "Disponibilité", strategy: "Réduire le risque" },
  { description: "Apply patches and updates during scheduled maintenance windows.", pillar: "Disponibilité", strategy: "Réduire le risque" },
  { description: "Maintain environmental controls (cooling, fire suppression) in data centers.", pillar: "Disponibilité", strategy: "Réduire le risque" },
  { description: "Design system architecture to eliminate single points of failure.", pillar: "Disponibilité", strategy: "Éviter le risque" },
  { description: "Avoid scheduling critical system maintenance during peak business hours.", pillar: "Disponibilité", strategy: "Éviter le risque" },
  { description: "Do not rely on a single geographic region for critical infrastructure.", pillar: "Disponibilité", strategy: "Éviter le risque" },
  { description: "Avoid overly complex system interdependencies that increase fragility.", pillar: "Disponibilité", strategy: "Éviter le risque" },
  { description: "Prohibit untested changes from being deployed to production environments.", pillar: "Disponibilité", strategy: "Éviter le risque" },
  { description: "Avoid deploying critical services on hardware nearing end-of-life.", pillar: "Disponibilité", strategy: "Éviter le risque" },
  { description: "Do not run critical production workloads on development or test infrastructure.", pillar: "Disponibilité", strategy: "Éviter le risque" },
  { description: "Eliminate reliance on unsupported software or operating systems.", pillar: "Disponibilité", strategy: "Éviter le risque" },
  { description: "Avoid resource exhaustion by implementing capacity planning.", pillar: "Disponibilité", strategy: "Éviter le risque" },
  { description: "Prevent accidental shutdowns by restricting physical access to critical hardware.", pillar: "Disponibilité", strategy: "Éviter le risque" },
  { description: "Utilize cloud hosting providers with high availability Service Level Agreements (SLAs).", pillar: "Disponibilité", strategy: "Transférer le risque" },
  { description: "Outsource DDoS protection services to a specialized third-party provider.", pillar: "Disponibilité", strategy: "Transférer le risque" },
  { description: "Purchase business interruption insurance to cover financial losses from downtime.", pillar: "Disponibilité", strategy: "Transférer le risque" },
  { description: "Use a Content Delivery Network (CDN) to improve global availability.", pillar: "Disponibilité", strategy: "Transférer le risque" },
  { description: "Contract with a third party for managed backup and disaster recovery services.", pillar: "Disponibilité", strategy: "Transférer le risque" },
  { description: "Outsource data center operations including power and cooling management.", pillar: "Disponibilité", strategy: "Transférer le risque" },
  { description: "Rely on SaaS vendors' commitments for application uptime.", pillar: "Disponibilité", strategy: "Transférer le risque" },
  { description: "Engage a third party for 24/7 infrastructure monitoring and alerting.", pillar: "Disponibilité", strategy: "Transférer le risque" },
  { description: "Transfer risk of hardware failure through comprehensive warranty/support contracts.", pillar: "Disponibilité", strategy: "Transférer le risque" },
  { description: "Use managed DNS services with global distribution and redundancy.", pillar: "Disponibilité", strategy: "Transférer le risque" },
  { description: "Accept occasional brief downtime for non-critical internal applications.", pillar: "Disponibilité", strategy: "Accepter le risque" },
  { description: "Define and accept lower availability targets for development/test environments.", pillar: "Disponibilité", strategy: "Accepter le risque" },
  { description: "Accept the risk of hardware failure for standard user workstations (replace on fail).", pillar: "Disponibilité", strategy: "Accepter le risque" },
  { description: "Formally accept longer Recovery Time Objectives (RTO) for archived data.", pillar: "Disponibilité", strategy: "Accepter le risque" },
  { description: "Accept risk of minor service degradation during peak load times.", pillar: "Disponibilité", strategy: "Accepter le risque" },
  { description: "Decide against costly active-active redundancy for services with low financial impact.", pillar: "Disponibilité", strategy: "Accepter le risque" },
  { description: "Accept the risk of relying on a single ISP for non-critical branch offices.", pillar: "Disponibilité", strategy: "Accepter le risque" },
  { description: "Acknowledge and accept potential downtime related to force majeure events.", pillar: "Disponibilité", strategy: "Accepter le risque" },
  { description: "Accept risk of scheduled downtime announced by third-party service providers.", pillar: "Disponibilité", strategy: "Accepter le risque" },
  { description: "Formally accept limitations in DR capabilities for non-essential legacy systems.", pillar: "Disponibilité", strategy: "Accepter le risque" },
  { description: "Enable detailed system, application, and security event logging.", pillar: "Traçabilité", strategy: "Réduire le risque" },
  { description: "Centralize logs from disparate systems into a SIEM solution.", pillar: "Traçabilité", strategy: "Réduire le risque" },
  { description: "Assign unique user IDs for all personnel accessing systems.", pillar: "Traçabilité", strategy: "Réduire le risque" },
  { description: "Implement accurate time synchronization across all systems (NTP).", pillar: "Traçabilité", strategy: "Réduire le risque" },
  { description: "Log all administrative actions and changes to critical systems.", pillar: "Traçabilité", strategy: "Réduire le risque" },
  { description: "Monitor and log network traffic flow patterns.", pillar: "Traçabilité", strategy: "Réduire le risque" },
  { description: "Implement user activity monitoring on sensitive systems where appropriate.", pillar: "Traçabilité", strategy: "Réduire le risque" },
  { description: "Retain logs according to regulatory requirements and internal policy.", pillar: "Traçabilité", strategy: "Réduire le risque" },
  { description: "Correlate security events across different log sources for context.", pillar: "Traçabilité", strategy: "Réduire le risque" },
  { description: "Log access to sensitive data files and databases.", pillar: "Traçabilité", strategy: "Réduire le risque" },
  { description: "Prohibit the use of shared or generic user accounts.", pillar: "Traçabilité", strategy: "Éviter le risque" },
  { description: "Avoid disabling essential operating system or application audit logs.", pillar: "Traçabilité", strategy: "Éviter le risque" },
  { description: "Do not permit anonymous access to systems requiring accountability.", pillar: "Traçabilité", strategy: "Éviter le risque" },
  { description: "Prevent unauthorized modification or deletion of log files.", pillar: "Traçabilité", strategy: "Éviter le risque" },
  { description: "Avoid using network address translation (NAT) excessively where source IP is critical.", pillar: "Traçabilité", strategy: "Éviter le risque" },
  { description: "Restrict direct administrative access to production systems whenever possible.", pillar: "Traçabilité", strategy: "Éviter le risque" },
  { description: "Avoid system configurations that obscure user actions (e.g., disabling command history).", pillar: "Traçabilité", strategy: "Éviter le risque" },
  { description: "Do not allow unmonitored remote access sessions to critical infrastructure.", pillar: "Traçabilité", strategy: "Éviter le risque" },
  { description: "Eliminate systems or applications that lack adequate logging capabilities.", pillar: "Traçabilité", strategy: "Éviter le risque" },
  { description: "Prevent changes outside of established change management (which includes logging).", pillar: "Traçabilité", strategy: "Éviter le risque" },
  { description: "Utilize a managed Security Information and Event Management (SIEM) service.", pillar: "Traçabilité", strategy: "Transférer le risque" },
  { description: "Outsource log storage and archiving to a specialized cloud provider.", pillar: "Traçabilité", strategy: "Transférer le risque" },
  { description: "Employ a third-party Security Operations Center (SOC) for log analysis.", pillar: "Traçabilité", strategy: "Transférer le risque" },
  { description: "Rely on cloud provider logging services (e.g., AWS CloudTrail, Azure Monitor logs).", pillar: "Traçabilité", strategy: "Transférer le risque" },
  { description: "Use SaaS applications where the vendor manages activity logging.", pillar: "Traçabilité", strategy: "Transférer le risque" },
  { description: "Contract with forensic investigators who rely on logs for evidence.", pillar: "Traçabilité", strategy: "Transférer le risque" },
  { description: "Utilize third-party identity providers (IdP) managing authentication logs.", pillar: "Traçabilité", strategy: "Transférer le risque" },
  { description: "Implement managed endpoint detection and response (EDR) with logging features.", pillar: "Traçabilité", strategy: "Transférer le risque" },
  { description: "Use external services for monitoring employee compliance with policies.", pillar: "Traçabilité", strategy: "Transférer le risque" },
  { description: "Rely on external auditors to validate the effectiveness of logging mechanisms.", pillar: "Traçabilité", strategy: "Transférer le risque" },
  { description: "Accept limited traceability on standard user workstations due to privacy concerns.", pillar: "Traçabilité", strategy: "Accepter le risque" },
  { description: "Accept lack of detailed logging for low-risk public information websites.", pillar: "Traçabilité", strategy: "Accepter le risque" },
  { description: "Accept reduced log retention periods for non-critical systems to manage costs.", pillar: "Traçabilité", strategy: "Accepter le risque" },
  { description: "Formally accept lack of traceability for specific end-of-life legacy systems.", pillar: "Traçabilité", strategy: "Accepter le risque" },
  { description: "Accept that logs may not capture user intent, only actions performed.", pillar: "Traçabilité", strategy: "Accepter le risque" },
  { description: "Decide against costly, high-volume network packet capture for routine traffic.", pillar: "Traçabilité", strategy: "Accepter le risque" },
  { description: "Accept limitations in correlating events across systems with unsynchronized clocks.", pillar: "Traçabilité", strategy: "Accepter le risque" },
  { description: "Acknowledge that determined attackers may attempt to evade or tamper with logs.", pillar: "Traçabilité", strategy: "Accepter le risque" },
  { description: "Accept gaps in tracing user activity across personal and corporate devices.", pillar: "Traçabilité", strategy: "Accepter le risque" },
  { description: "Formally accept that logs from certain third-party services may be insufficient.", pillar: "Traçabilité", strategy: "Accepter le risque" },
  { description: "Implement tamper-evident logging mechanisms (e.g., secure append-only, hashing).", pillar: "Preuve", strategy: "Réduire le risque" },
  { description: "Use digital signatures for verifying the origin and integrity of communications.", pillar: "Preuve", strategy: "Réduire le risque" },
  { description: "Maintain a strict chain of custody for all collected digital evidence.", pillar: "Preuve", strategy: "Réduire le risque" },
  { description: "Employ cryptographic hashing to ensure the integrity of stored evidence files.", pillar: "Preuve", strategy: "Réduire le risque" },
  { description: "Record all incident response actions, decisions, and findings meticulously.", pillar: "Preuve", strategy: "Réduire le risque" },
  { description: "Utilize secure timestamping services for critical log entries and transactions.", pillar: "Preuve", strategy: "Réduire le risque" },
  { description: "Implement non-repudiation controls for critical transactions.", pillar: "Preuve", strategy: "Réduire le risque" },
  { description: "Capture forensic images of systems involved in security incidents correctly.", pillar: "Preuve", strategy: "Réduire le risque" },
  { description: "Ensure log data includes sufficient detail for forensic analysis.", pillar: "Preuve", strategy: "Réduire le risque" },
  { description: "Corroborate digital evidence with other sources (physical logs, witness statements).", pillar: "Preuve", strategy: "Réduire le risque" },
  { description: "Avoid deleting critical logs before the mandated retention period expires.", pillar: "Preuve", strategy: "Éviter le risque" },
  { description: "Do not allow modification of original evidence logs (use WORM or secure copies).", pillar: "Preuve", strategy: "Éviter le risque" },
  { description: "Prevent actions that could contaminate evidence during incident response (e.g., premature reboot).", pillar: "Preuve", strategy: "Éviter le risque" },
  { description: "Avoid using systems or tools that cannot produce verifiable evidence trails.", pillar: "Preuve", strategy: "Éviter le risque" },
  { description: "Do not rely solely on volatile memory for critical evidence.", pillar: "Preuve", strategy: "Éviter le risque" },
  { description: "Prohibit undocumented or unauthorized changes to system configurations relevant to evidence.", pillar: "Preuve", strategy: "Éviter le risque" },
  { description: "Avoid commingling sensitive investigation data with normal operational data.", pillar: "Preuve", strategy: "Éviter le risque" },
  { description: "Do not store critical evidence on easily accessible or modifiable network shares.", pillar: "Preuve", strategy: "Éviter le risque" },
  { description: "Avoid interrupting legally required data preservation holds (litigation holds).", pillar: "Preuve", strategy: "Éviter le risque" },
  { description: "Prevent untrained personnel from handling potential digital evidence.", pillar: "Preuve", strategy: "Éviter le risque" },
  { description: "Use a trusted third-party timestamping authority for verifiable time records.", pillar: "Preuve", strategy: "Transférer le risque" },
  { description: "Outsource digital forensics investigations to specialized certified firms.", pillar: "Preuve", strategy: "Transférer le risque" },
  { description: "Rely on cloud provider logs and attestations as evidence (verify contractual terms).", pillar: "Preuve", strategy: "Transférer le risque" },
  { description: "Engage third-party e-discovery services for legal proceedings.", pillar: "Preuve", strategy: "Transférer le risque" },
  { description: "Store critical evidence backups or archives with a secure third-party custodian.", pillar: "Preuve", strategy: "Transférer le risque" },
  { description: "Use externally managed Public Key Infrastructure (PKI) for digital signatures.", pillar: "Preuve", strategy: "Transférer le risque" },
  { description: "Rely on independent auditors' reports as proof of control implementation.", pillar: "Preuve", strategy: "Transférer le risque" },
  { description: "Utilize third-party attestation services (e.g., SOC reports) as evidence of compliance.", pillar: "Preuve", strategy: "Transférer le risque" },
  { description: "Employ external legal counsel to validate evidence collection procedures.", pillar: "Preuve", strategy: "Transférer le risque" },
  { description: "Use blockchain-based services for immutable transaction proof where applicable.", pillar: "Preuve", strategy: "Transférer le risque" },
  { description: "Accept weaker non-repudiation for low-risk internal email communications.", pillar: "Preuve", strategy: "Accepter le risque" },
  { description: "Accept lack of definitive proof for minor policy violations handled informally.", pillar: "Preuve", strategy: "Accepter le risque" },
  { description: "Acknowledge that evidence retrieved from certain legacy systems may be less reliable.", pillar: "Preuve", strategy: "Accepter le risque" },
  { description: "Accept limitations in definitively proving user intent versus accidental action.", pillar: "Preuve", strategy: "Accepter le risque" },
  { description: "Accept that volatile data (RAM) might be lost if not captured immediately.", pillar: "Preuve", strategy: "Accepter le risque" },
  { description: "Decide against costly forensic tooling for all minor security events.", pillar: "Preuve", strategy: "Accepter le risque" },
  { description: "Accept that perfect chain of custody may be difficult for user-managed devices.", pillar: "Preuve", strategy: "Accepter le risque" },
  { description: "Formally accept that log timestamps might have minor drifts if NTP fails briefly.", pillar: "Preuve", strategy: "Accepter le risque" },
  { description: "Acknowledge that digitally signed documents might face legal challenges.", pillar: "Preuve", strategy: "Accepter le risque" },
  { description: "Accept risk that encrypted data may be unrecoverable without keys, limiting evidence.", pillar: "Preuve", strategy: "Accepter le risque" },
  { description: "Ensure audit logs are complete, accurate, and protected from tampering.", pillar: "Auditabilité", strategy: "Réduire le risque" },
  { description: "Implement specific, read-only access roles for internal and external auditors.", pillar: "Auditabilité", strategy: "Réduire le risque" },
  { description: "Maintain up-to-date documentation of system architecture, data flows, and policies.", pillar: "Auditabilité", strategy: "Réduire le risque" },
  { description: "Conduct regular internal security audits based on established frameworks (ISO 27001, NIST).", pillar: "Auditabilité", strategy: "Réduire le risque" },
  { description: "Facilitate periodic external audits by independent third parties.", pillar: "Auditabilité", strategy: "Réduire le risque" },
  { description: "Use Governance, Risk, and Compliance (GRC) tools to manage audit evidence.", pillar: "Auditabilité", strategy: "Réduire le risque" },
  { description: "Ensure audit trails capture sufficient detail (who, what, when, where, success/failure).", pillar: "Auditabilité", strategy: "Réduire le risque" },
  { description: "Implement processes for tracking and remediating audit findings promptly.", pillar: "Auditabilité", strategy: "Réduire le risque" },
  { description: "Standardize system configurations to simplify auditing processes.", pillar: "Auditabilité", strategy: "Réduire le risque" },
  { description: "Provide auditors with necessary tools and training for accessing audit data.", pillar: "Auditabilité", strategy: "Réduire le risque" },
  { description: "Avoid system designs that inherently lack audit capabilities or transparency.", pillar: "Auditabilité", strategy: "Éviter le risque" },
  { description: "Do not disable necessary audit trails or logging required for compliance.", pillar: "Auditabilité", strategy: "Éviter le risque" },
  { description: "Avoid using technologies or third-party services without audit rights or reports.", pillar: "Auditabilité", strategy: "Éviter le risque" },
  { description: "Prohibit undocumented exceptions to security policies that hinder audits.", pillar: "Auditabilité", strategy: "Éviter le risque" },
  { description: "Avoid granting excessive system permissions that bypass audit controls.", pillar: "Auditabilité", strategy: "Éviter le risque" },
  { description: "Do not allow destruction of audit records before the required retention period.", pillar: "Auditabilité", strategy: "Éviter le risque" },
  { description: "Avoid manual processes where automated, auditable workflows can be used.", pillar: "Auditabilité", strategy: "Éviter le risque" },
  { description: "Prevent deployment of systems without defined audit requirements and testing.", pillar: "Auditabilité", strategy: "Éviter le risque" },
  { description: "Eliminate shared administrative accounts that obscure individual accountability for audits.", pillar: "Auditabilité", strategy: "Éviter le risque" },
  { description: "Do not obstruct auditors' access to necessary personnel or documentation.", pillar: "Auditabilité", strategy: "Éviter le risque" },
  { description: "Hire independent external audit firms to perform periodic assessments.", pillar: "Auditabilité", strategy: "Transférer le risque" },
  { description: "Utilize cloud providers certified against recognized standards (e.g., SOC 2, ISO 27001).", pillar: "Auditabilité", strategy: "Transférer le risque" },
  { description: "Rely on audit reports and certifications provided by SaaS vendors.", pillar: "Auditabilité", strategy: "Transférer le risque" },
  { description: "Outsource the internal audit function (fully or partially) to a specialized firm.", pillar: "Auditabilité", strategy: "Transférer le risque" },
  { description: "Engage third-party consultants to assess compliance with specific regulations (PCI DSS, GDPR).", pillar: "Auditabilité", strategy: "Transférer le risque" },
  { description: "Use managed services where the provider assumes responsibility for auditable operations.", pillar: "Auditabilité", strategy: "Transférer le risque" },
  { description: "Rely on external legal counsel to interpret audit requirements in regulations.", pillar: "Auditabilité", strategy: "Transférer le risque" },
  { description: "Utilize third-party GRC platforms that facilitate audit evidence collection from vendors.", pillar: "Auditabilité", strategy: "Transférer le risque" },
  { description: "Contractually require service providers to grant audit rights.", pillar: "Auditabilité", strategy: "Transférer le risque" },
  { description: "Purchase cyber insurance that may require audits as a condition.", pillar: "Auditabilité", strategy: "Transférer le risque" },
  { description: "Accept reduced auditability for temporary development or sandbox environments.", pillar: "Auditabilité", strategy: "Accepter le risque" },
  { description: "Acknowledge limitations in auditing third-party \"black-box\" software components.", pillar: "Auditabilité", strategy: "Accepter le risque" },
  { description: "Accept longer lead times for retrieving audit data from offline archives.", pillar: "Auditabilité", strategy: "Accepter le risque" },
  { description: "Formally accept that the cost of full auditability is not justified for certain low-risk assets.", pillar: "Auditabilité", strategy: "Accepter le risque" },
  { description: "Accept limited visibility into the internal controls of certain upstream suppliers.", pillar: "Auditabilité", strategy: "Accepter le risque" },
  { description: "Decide against intrusive audits on systems where it could cause instability.", pillar: "Auditabilité", strategy: "Accepter le risque" },
  { description: "Accept that audit evidence for legacy systems may be incomplete or non-standard.", pillar: "Auditabilité", strategy: "Accepter le risque" },
  { description: "Acknowledge difficulty in auditing informal communication channels (e.g., hallway conversations).", pillar: "Auditabilité", strategy: "Accepter le risque" },
  { description: "Accept residual risk that audits may not detect all non-compliance or fraud.", pillar: "Auditabilité", strategy: "Accepter le risque" },
  { description: "Formally accept limitations on auditing employee adherence to acceptable use policies.", pillar: "Auditabilité", strategy: "Accepter le risque" }
];