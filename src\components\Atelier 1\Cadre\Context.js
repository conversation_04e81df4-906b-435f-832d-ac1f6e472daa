import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Save, Plus, Info, User, Calendar, Target, X, Download, BarChart2 } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import ParticipantsRACIMatrix from './ParticipantsRACIMatrix';
import RACIVisualization from './RACIVisualization';
import GuideModal from './GuideModal';
import { useAnalysis } from '../../../context/AnalysisContext'; // Import useAnalysis hook
import { POSITION_OPTIONS } from '../../../constants/index'; // Assuming ROLES is used internally in Matrix/Viz
import { showSuccessToast, showErrorToast, showLoadingToast, updateToast } from '../../../utils/toastUtils';

const cardVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: { opacity: 1, y: 0, transition: { duration: 0.5 } },
};

const Context = () => {
  const { t } = useTranslation();
  const {
    currentContextData,
    isWorkshopDataLoading,
    workshopDataError,
    saveCurrentContextData
  } = useAnalysis();

  // Local state for form fields
  const [organizationName, setOrganizationName] = useState('');
  const [missions, setMissions] = useState([]);
  const [newMission, setNewMission] = useState('');
  const [scope, setScope] = useState('');
  const [analysisDate, setAnalysisDate] = useState('');
  const [participants, setParticipants] = useState([]);
  const [matrix, setMatrix] = useState({});
  const [isGuideOpen, setIsGuideOpen] = useState(false);

  // Default workshops - These should ideally come from config or context if dynamic
  const [workshops, setWorkshops] = useState([
    { id: 'Atelier 1', name: 'Atelier 1' },
    { id: 'Atelier 2', name: 'Atelier 2' },
    { id: 'Atelier 3', name: 'Atelier 3' },
    { id: 'Atelier 4', name: 'Atelier 4' },
    { id: 'Atelier 5', name: 'Atelier 5' }
  ]);

  // State for UI

  // --- Effects ---

  // Sync local state with context data
  useEffect(() => {
    // Ensure workshops state is always an array before using it
    const currentWorkshops = Array.isArray(workshops) ? workshops : [];

    if (currentContextData) {
      setOrganizationName(currentContextData.organizationName || '');
      setMissions(Array.isArray(currentContextData.missions) ? currentContextData.missions : (currentContextData.mission ? [currentContextData.mission] : []));
      setScope(currentContextData.scope || '');
      setAnalysisDate(currentContextData.analysisDate || '');

       // Ensure participants from context is an array
      const contextParticipants = Array.isArray(currentContextData.participants) ? currentContextData.participants : [];

      if (contextParticipants.length > 0) {
        const participantsData = contextParticipants.map(p => ({
          id: p.id,
          name: p.name || '', // Ensure name is not undefined
          position: p.position || '', // Ensure position is not undefined
          customPosition: p.customPosition || ''
        }));
        setParticipants(participantsData);

        const matrixData = {};
        // Determine all workshops from defaults AND data
        const allWorkshopIdsInData = new Set();
        contextParticipants.forEach(p => {
            if (p && p.workshopRoles && typeof p.workshopRoles === 'object') {
                Object.keys(p.workshopRoles).forEach(wId => allWorkshopIdsInData.add(wId));
            }
        });
        const defaultWorkshopIds = new Set(currentWorkshops.map(w => w.id));
        const combinedWorkshopIds = new Set([...defaultWorkshopIds, ...allWorkshopIdsInData]);


        // Update state workshops if new ones found in data
        const currentWorkshopMap = new Map(currentWorkshops.map(w => [w.id, w]));
        const workshopsToAdd = [];
        combinedWorkshopIds.forEach(id => {
            if (!currentWorkshopMap.has(id)) {
                workshopsToAdd.push({ id, name: id }); // Simple name for new workshops
            }
        });
        // Use functional update to safely modify workshops state
        if (workshopsToAdd.length > 0) {
             setWorkshops(prev => Array.isArray(prev) ? [...prev, ...workshopsToAdd] : workshopsToAdd);
        }
         // Get the final list of workshop IDs after potential update for matrix generation
        const finalWorkshopIdsForMatrix = Array.from(combinedWorkshopIds);


        // Build matrix using participantsData and final workshop IDs
        participantsData.forEach(p => {
          matrixData[p.id] = {};
          const contextParticipant = contextParticipants.find(cp => cp.id === p.id);
          const roles = contextParticipant?.workshopRoles || {};

          finalWorkshopIdsForMatrix.forEach(workshopId => {
            matrixData[p.id][workshopId] = roles[workshopId] || ''; // Default to empty string
          });
        });
        setMatrix(matrixData);

      } else {
        // No participants in context data: Initialize with one empty participant
        const newId = Date.now(); // Use timestamp for a unique ID
         const defaultPosition = (POSITION_OPTIONS && POSITION_OPTIONS.length > 0) ? POSITION_OPTIONS[0].value : '';
        const initialParticipant = { id: newId, name: '', position: defaultPosition, customPosition: '' };
        setParticipants([initialParticipant]);

        const initialMatrix = { [newId]: {} };
        currentWorkshops.forEach(workshop => { // Use currentWorkshops which is guaranteed to be an array
          initialMatrix[newId][workshop.id] = '';
        });
        setMatrix(initialMatrix);
      }
    } else if (!currentContextData && !isWorkshopDataLoading && participants.length === 0) {
        // Initial load, no data, not loading, no participants yet, not in test mode
         const newId = Date.now() + 1; // Different ID
         const defaultPosition = (POSITION_OPTIONS && POSITION_OPTIONS.length > 0) ? POSITION_OPTIONS[0].value : '';
         const initialParticipant = { id: newId, name: '', position: defaultPosition, customPosition: '' };
         setParticipants([initialParticipant]);
         const initialMatrix = { [newId]: {} };
         // Use currentWorkshops which is guaranteed to be an array
         currentWorkshops.forEach(workshop => {
            initialMatrix[newId][workshop.id] = '';
         });
         setMatrix(initialMatrix);
    }
  }, [currentContextData, isWorkshopDataLoading, workshops]); // Rerun if context changes

  // --- Handlers ---

  const handleSaveData = async () => {

    // Ensure participants/workshops/matrix are in expected format before saving
    const currentParticipants = Array.isArray(participants) ? participants : [];
    const currentWorkshopsForSave = Array.isArray(workshops) ? workshops : [];
    const currentMatrix = typeof matrix === 'object' && matrix !== null ? matrix : {};

    // Filter out participants with no name before saving
    const validParticipants = currentParticipants.filter(p => p && p.name && p.name.trim() !== '');
    if (validParticipants.length !== currentParticipants.length) {
        if (!window.confirm(t('context.errors.invalidParticipants'))) {
            return;
        }
    }

    const contextData = {
      organizationName,
      missions,
      scope,
      analysisDate,
      participants: validParticipants.map(p => {
        const workshopRoles = {};
        // Use the current list of workshops from state
        currentWorkshopsForSave.forEach(workshop => {
          // Ensure participant ID exists in matrix and workshop ID exists for that participant
          workshopRoles[workshop.id] = currentMatrix[p.id]?.[workshop.id] || '';
        });

        const participantData = {
            id: p.id,
            name: p.name,
            position: p.position,
            workshopRoles
        };

        // Only include customPosition if position is 'OTHER' and it has a value
        if (p.position === "OTHER" && p.customPosition && p.customPosition.trim() !== '') {
            participantData.customPosition = p.customPosition;
        }

        return participantData;
      })
    };

    // Show loading toast
    const toastId = showLoadingToast(t('context.status.saving'));

    try {
      // Removed sensitive data logging for security
      const response = await saveCurrentContextData(contextData);
      if (response?.success) { // Check if response and success property exist
        // Update loading toast to success
        updateToast(toastId, t('context.status.saved'), 'success');
      } else {
        // Update loading toast to error
        updateToast(toastId, `${t('context.errors.saveError')}: ${response?.message || 'Réponse invalide ou erreur inconnue'}`, 'error');
      }
    } catch (error) {
      // Log without revealing sensitive data
      console.error('Error saving context data');
      // Update loading toast to error
      updateToast(toastId, `${t('context.errors.saveError')}: ${t('context.errors.serverError')}`, 'error');
    }
  };



  const exportCurrentData = () => {
    // Ensure data sources are in correct format
    const participantsToExport = Array.isArray(participants) ? participants : [];
    const workshopsToExport = Array.isArray(workshops) ? workshops : [];
    const matrixToExport = typeof matrix === 'object' && matrix !== null ? matrix : {};

    const dataToExport = {
      organizationName, // Use current state value (could be from test data)
      missions,
      scope,
      analysisDate,
      participants: participantsToExport
        .filter(p => p && p.name && p.name.trim() !== '') // Export only valid participants
        .map(p => {
          const participantData = {
            id: p.id,
            name: p.name,
            position: p.position,
            workshopRoles: workshopsToExport.reduce((acc, w) => {
                acc[w.id] = matrixToExport[p.id]?.[w.id] || '';
                return acc;
            }, {})
          };
          if (p.position === "OTHER" && p.customPosition && p.customPosition.trim() !== '') {
              participantData.customPosition = p.customPosition;
          }
          return participantData;
        })
    };

    try {
      const blob = new Blob([JSON.stringify(dataToExport, null, 2)], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `RACI_Contexte_${organizationName || 'export'}_${new Date().toISOString().slice(0, 10)}.json`;
      document.body.appendChild(a);
      a.click();
      setTimeout(() => {
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
      }, 0);

      showSuccessToast(t('context.status.exported'));
    } catch (error) {
      // Log without revealing sensitive data
      console.error('Error exporting data');
      showErrorToast(`${t('context.errors.exportError')}: ${t('context.errors.fileGenerationError')}`);
    }
  };

  const handleAddParticipant = () => {
        const newId = Date.now(); // Unique ID
        // Ensure POSITION_OPTIONS exists and has items before accessing [0]
        const defaultPosition = (Array.isArray(POSITION_OPTIONS) && POSITION_OPTIONS.length > 0) ? POSITION_OPTIONS[0].value : '';
        const newParticipant = { id: newId, name: "", position: defaultPosition, customPosition: "" };

        // Use functional updates for safety
        setParticipants(prev => [...(Array.isArray(prev) ? prev : []), newParticipant]);

        // Initialize matrix row for the new participant
        setMatrix(prevMatrix => {
            const newMatrix = { ...(typeof prevMatrix === 'object' && prevMatrix !== null ? prevMatrix : {}) };
            newMatrix[newId] = {};
            // Ensure workshops state is an array
            (Array.isArray(workshops) ? workshops : []).forEach(workshop => {
                newMatrix[newId][workshop.id] = ""; // Initialize with empty role
            });
            return newMatrix;
        });

        showSuccessToast(t('context.status.participantAdded'));
    };

  const handleAddMission = () => {
    if (newMission.trim() !== '') {
      setMissions(prev => [...prev, newMission.trim()]);
      setNewMission('');
    }
  };

  const handleRemoveMission = (index) => {
    setMissions(prev => prev.filter((_, i) => i !== index));
  };

  const handleKeyPress = (e) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      handleAddMission();
    }
  };

  // --- Render Logic ---

  // Show loading state if hook indicates loading AND we don't have data yet
  if (isWorkshopDataLoading && !currentContextData) {
    return (
      <div className="flex justify-center items-center h-64 bg-slate-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-slate-600">{t('context.status.loading')}</p>
        </div>
      </div>
    );
  }

  // Show error specific to context loading
  if (workshopDataError?.context) {
    return (
      <div className="p-6 bg-slate-50 rounded-lg">
        <div className="bg-red-100 border border-red-300 text-red-800 p-4 rounded-lg mb-4 shadow-sm">
          <h3 className="font-bold mb-2 text-lg flex items-center"><X size={20} className="mr-2"/>{t('context.errors.loadingError')}</h3>
          <p className="mb-3">{typeof workshopDataError.context === 'string' ? workshopDataError.context : JSON.stringify(workshopDataError.context)}</p>
          <button
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition duration-200 text-sm"
            onClick={() => window.location.reload()} // Simple retry strategy
          >
            {t('context.errors.retry')}
          </button>
        </div>
      </div>
    );
  }

  // Ensure defaults are arrays/objects if state is not yet initialized properly
  const displayParticipants = Array.isArray(participants) ? participants : [];
  const displayWorkshops = Array.isArray(workshops) ? workshops : [];
  const displayMatrix = typeof matrix === 'object' && matrix !== null ? matrix : {};
  const displayPositionOptions = Array.isArray(POSITION_OPTIONS) ? POSITION_OPTIONS : []; // Ensure it's an array


  return (
    <div className="space-y-8 p-4 sm:p-6 lg:p-8 bg-gradient-to-br from-slate-50 to-blue-50 min-h-screen">

      {/* === MERGED HEADER === */}
      <div className="bg-white rounded-xl shadow-sm border border-slate-100 p-4 mb-6">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          {/* Left Side: Breadcrumb & Title */}
          <div>
            <div className="flex items-center text-sm text-slate-500 mb-2">
              <span className="hover:text-blue-600 transition-colors">{t('context.breadcrumb.workshop1')}</span>
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mx-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
              <span className="text-blue-600 font-medium">{t('context.breadcrumb.framing')}</span>
            </div>
            <h1 className="text-2xl sm:text-3xl font-bold text-slate-800 flex items-center">
              <Target size={28} className="mr-3 text-blue-600" />
              {t('context.title')}
            </h1>
          </div>

          {/* Right Side: Buttons */}
          <div className="flex items-center gap-2 flex-wrap">

            {/* Export Button */}
            <button
               className="text-sm font-medium bg-white text-slate-700 px-4 py-2 rounded-md hover:bg-slate-100 transition duration-200 flex items-center shadow-sm border border-slate-300"
               onClick={exportCurrentData}
               title={t('context.tooltips.export')}
             >
               <Download size={16} className="mr-2" />
               {t('context.buttons.export')}
             </button>
            {/* Save Button */}
            <button
              className={`text-sm font-medium px-4 py-2 rounded-md flex items-center shadow-sm transition duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 ${
                isWorkshopDataLoading
                ? 'bg-slate-300 text-slate-500 cursor-not-allowed'
                : 'bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500'
              }`}
              onClick={handleSaveData}
              disabled={isWorkshopDataLoading}
              title={t('context.tooltips.save')}
            >
              <Save size={16} className="mr-2" />
              {t('context.buttons.save')}
            </button>

            {/* Help Button */}
            <button
              className="text-sm font-medium bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 flex items-center shadow-sm transition duration-200"
              onClick={() => setIsGuideOpen(true)}
            >
              <Info size={16} className="mr-2" />
              {t('context.buttons.help')}
            </button>
          </div>
        </div>
      </div>
      {/* === END MERGED HEADER === */}





      {/* Main Content - Sections stacked vertically */}

        {/* Section 1: Basic Info */}
        <motion.div
            variants={cardVariants}
            initial="hidden"
            animate="visible"
            className="space-y-6"
        >
          <div className="bg-white p-6 rounded-xl shadow-lg border border-slate-100 h-full overflow-hidden">
             <div className="flex items-center justify-between mb-6">
               <h2 className="text-xl font-semibold text-slate-800 flex items-center">
                   <Info size={20} className="mr-2 text-blue-600" />
                   {t('context.sections.generalInfo')}
               </h2>
               <div className="hidden sm:block px-3 py-1 bg-blue-50 text-blue-700 rounded-full text-xs font-medium">
                 {t('context.steps.step1')}
               </div>
             </div>
             <div className="space-y-8">
                 {/* Organization Name and Date side-by-side with enhanced layout */}
                 <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
                   {/* Organization Name */}
                   <div className="relative group transition-all duration-300 hover:shadow-md rounded-lg">
                      <div className="relative">
                        <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                          <User size={18} className="text-blue-500 transition-colors duration-200" />
                        </div>
                        <input
                            id="organizationName"
                            type="text"
                            className="w-full p-4 pl-11 border-2 border-slate-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-300 shadow-sm text-sm bg-white peer"
                            value={organizationName}
                            onChange={(e) => setOrganizationName(e.target.value)}
                            placeholder={t('context.fields.organizationNamePlaceholder')}
                            required
                            aria-label="Nom de l'organisation"
                        />
                        <label
                          htmlFor="organizationName"
                          className="absolute left-11 top-4 text-sm font-medium text-blue-600 transition-all duration-200 -translate-y-8 scale-75 transform origin-[0] bg-white px-1 peer-placeholder-shown:bg-transparent peer-placeholder-shown:text-slate-500 peer-placeholder-shown:scale-100 peer-placeholder-shown:translate-y-0 peer-focus:scale-75 peer-focus:-translate-y-8 peer-focus:bg-white peer-focus:text-blue-600 pointer-events-none"
                        >
                          {t('context.fields.organizationName')} <span className="text-red-500">{t('context.required')}</span>
                        </label>
                      </div>
                      <p className="text-xs text-slate-500 mt-1.5 ml-2">
                        {t('context.fields.organizationNameHelp')}
                      </p>
                   </div>

                   {/* Analysis Date with enhanced styling */}
                   <div className="relative group transition-all duration-300 hover:shadow-md rounded-lg">
                       <div className="relative">
                         <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                           <Calendar size={18} className="text-blue-500 transition-colors duration-200" />
                         </div>
                         <input
                             id="analysisDate"
                             type="date"
                             className="w-full p-4 pl-11 border-2 border-slate-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-300 shadow-sm text-sm bg-white peer"
                             value={analysisDate}
                             onChange={(e) => setAnalysisDate(e.target.value)}
                             required
                             aria-label="Date de l'analyse"
                         />
                         <label
                           htmlFor="analysisDate"
                           className="absolute left-11 top-4 text-sm font-medium text-blue-600 transition-all duration-200 -translate-y-8 scale-75 transform origin-[0] bg-white px-1 peer-placeholder-shown:bg-transparent peer-placeholder-shown:text-slate-500 peer-placeholder-shown:scale-100 peer-placeholder-shown:translate-y-0 peer-focus:scale-75 peer-focus:-translate-y-8 peer-focus:bg-white peer-focus:text-blue-600 pointer-events-none"
                         >
                           {t('context.fields.analysisDate')} <span className="text-red-500">{t('context.required')}</span>
                         </label>
                       </div>
                       <p className="text-xs text-slate-500 mt-1.5 ml-2">
                         {t('context.fields.analysisDateHelp')}
                       </p>
                   </div>
                 </div>

                 {/* Mission and Scope side-by-side with enhanced layout */}
                 <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
                   {/* Mission List */}
                   <div className="relative group transition-all duration-300 hover:shadow-md rounded-lg">
                       <div className="relative border-2 border-slate-200 rounded-lg p-4 bg-white">
                         <div className="flex items-center mb-3">
                           <Target size={18} className="text-blue-500 mr-2" />
                           <label className="text-sm font-medium text-blue-600">
                             {t('context.fields.missions')}
                           </label>
                         </div>

                         {/* Mission Input */}
                         <div className="flex gap-2 mb-3">
                           <input
                             type="text"
                             value={newMission}
                             onChange={(e) => setNewMission(e.target.value)}
                             onKeyPress={handleKeyPress}
                             placeholder={t('context.fields.missionPlaceholder')}
                             className="flex-1 p-2 border border-slate-300 rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm"
                           />
                           <button
                             type="button"
                             onClick={handleAddMission}
                             className="px-3 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors duration-200 flex items-center"
                             title={t('context.buttons.addMission')}
                           >
                             <Plus size={16} />
                           </button>
                         </div>

                         {/* Mission List */}
                         <div className="space-y-2 min-h-[60px]">
                           {missions.length === 0 ? (
                             <p className="text-slate-400 text-sm italic">{t('context.table.noMissions')}</p>
                           ) : (
                             missions.map((mission, index) => (
                               <div key={index} className="flex items-center justify-between bg-slate-50 p-2 rounded border">
                                 <span className="text-sm flex-1">{mission}</span>
                                 <button
                                   type="button"
                                   onClick={() => handleRemoveMission(index)}
                                   className="ml-2 p-1 text-red-500 hover:bg-red-100 rounded transition-colors duration-200"
                                 >
                                   <X size={14} />
                                 </button>
                               </div>
                             ))
                           )}
                         </div>
                       </div>
                   </div>

                   {/* Scope */}
                   <div className="relative group transition-all duration-300 hover:shadow-md rounded-lg">
                     <div className="relative">
                       <div className="absolute top-4 left-0 flex items-start pl-3 pointer-events-none">
                         <Target size={18} className="text-blue-500 transition-colors duration-200" />
                       </div>
                       <textarea
                           id="scope"
                           className="w-full p-4 pl-11 border-2 border-slate-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-300 shadow-sm text-sm bg-white peer min-h-[120px]"
                           rows="5"
                           value={scope}
                           onChange={(e) => setScope(e.target.value)}
                           placeholder={t('context.fields.scopePlaceholder')}
                           required
                           aria-label="Objectif ou Périmètre"
                        />
                        <label
                          htmlFor="scope"
                          className="absolute left-11 top-4 text-sm font-medium text-blue-600 transition-all duration-200 -translate-y-8 scale-75 transform origin-[0] bg-white px-1 peer-placeholder-shown:bg-transparent peer-placeholder-shown:text-slate-500 peer-placeholder-shown:scale-100 peer-placeholder-shown:translate-y-0 peer-focus:scale-75 peer-focus:-translate-y-8 peer-focus:bg-white peer-focus:text-blue-600 pointer-events-none"
                        >
                          {t('context.fields.scope')} <span className="text-red-500">{t('context.required')}</span>
                        </label>
                     </div>
                     <p className="text-xs text-slate-500 mt-1.5 ml-2">
                       {t('context.fields.scopeHelp')}
                     </p>
                   </div>
                 </div>

                 {/* Info section for both Mission and Scope */}
                 <div className="mt-2 p-3 bg-blue-50 rounded-lg border border-blue-100">
                     <p className="text-sm text-blue-700 flex items-start">
                         <Info size={16} className="mr-2 flex-shrink-0 mt-0.5" />
                         <span>
                           <strong>{t('context.advice.title')}</strong> {t('context.advice.missionScope')}
                         </span>
                     </p>
                 </div>
             </div>
          </div>
        </motion.div>

        {/* Section 2: RACI Matrix */}
        <motion.div
            variants={cardVariants}
            initial="hidden"
            animate="visible"
            transition={{ delay: 0.1 }}
            className="space-y-6"
        >
            <div className="bg-white p-6 rounded-xl shadow-lg border border-slate-100">
                <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-3 mb-6">
                    <h2 className="text-xl font-semibold text-slate-800 flex items-center">
                        <User size={20} className="mr-2 text-blue-600" />
                        {t('context.sections.participants')}
                         <span className="text-red-500 ml-1">{t('context.required')}</span>
                    </h2>
                    <div className="hidden sm:block px-3 py-1 bg-blue-50 text-blue-700 rounded-full text-xs font-medium">
                      {t('context.steps.step2')}
                    </div>
                    <button
                        type="button"
                        className="text-sm font-medium bg-gradient-to-r from-blue-500 to-blue-600 text-white px-4 py-2 rounded-lg hover:from-blue-600 hover:to-blue-700 transition-all duration-300 flex items-center focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed shadow-sm hover:shadow"
                        onClick={handleAddParticipant}

                        title={t('context.buttons.addParticipant')}
                    >
                        <Plus size={16} className="mr-1.5" />
                        {t('context.buttons.addParticipant')}
                    </button>
                </div>

                {/* Participant Matrix Component */}
                 {displayParticipants.length > 0 ? (
                      <ParticipantsRACIMatrix
                          key="live-matrix"
                          workshops={displayWorkshops} // Already validated as array
                          positionOptions={displayPositionOptions} // Already validated as array
                          participants={displayParticipants} // Already validated as array
                          setParticipants={setParticipants}
                          matrix={displayMatrix} // Already validated as object
                          setMatrix={setMatrix}
                       />
                 ) : (
                     <div className="text-center py-8 px-4 border border-dashed border-slate-300 rounded-lg bg-slate-50">
                        <Info size={24} className='mx-auto text-slate-400 mb-2' />
                        <p className="text-slate-600">{t('context.table.noParticipants')}</p>
                        <p className="text-sm text-slate-500 mt-1">{t('context.table.addParticipantHint')}</p>
                    </div>
                 )}


                {/* RACI Legend */}
                <div className="mt-6 border-t border-slate-200 pt-6">
                    <h3 className="text-sm font-semibold text-slate-600 mb-3 uppercase tracking-wider flex items-center">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1.5 text-blue-500" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                      </svg>
                      {t('context.raci.legend')}
                    </h3>
                    <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-3">
                         {[
                           { role: 'R', labelKey: 'responsible', descKey: 'responsibleDesc', color: 'green', bg: 'bg-green-100', text: 'text-green-800', border: 'border-green-200' },
                           { role: 'A', labelKey: 'accountable', descKey: 'accountableDesc', color: 'blue', bg: 'bg-blue-100', text: 'text-blue-800', border: 'border-blue-200' },
                           { role: 'C', labelKey: 'consulted', descKey: 'consultedDesc', color: 'yellow', bg: 'bg-yellow-100', text: 'text-yellow-800', border: 'border-yellow-200' },
                           { role: 'I', labelKey: 'informed', descKey: 'informedDesc', color: 'red', bg: 'bg-red-100', text: 'text-red-800', border: 'border-red-200' },
                           { role: '-', labelKey: 'notInvolved', descKey: 'notInvolvedDesc', color: 'gray', bg: 'bg-gray-100', text: 'text-gray-800', border: 'border-gray-200' },
                         ].map(item => (
                             <div key={item.role} className="flex items-start space-x-3 p-3 rounded-lg hover:bg-blue-50/30 transition-all duration-200 border border-transparent hover:border-blue-100">
                                 <span className={`flex-shrink-0 flex items-center justify-center w-8 h-8 rounded-full ${item.bg} ${item.text} font-bold text-sm shadow-sm border ${item.border}`}>
                                    {item.role}
                                 </span>
                                 <div>
                                    <p className="text-sm font-medium text-slate-800">{t(`context.raci.${item.labelKey}`)}</p>
                                    <p className="text-xs text-slate-500">{t(`context.raci.${item.descKey}`)}</p>
                                 </div>
                             </div>
                          ))}
                    </div>
                </div>
            </div>
        </motion.div>

        {/* Section 3: RACI Visualization */}
         <motion.div
             variants={cardVariants}
             initial="hidden"
             animate="visible"
             transition={{ delay: 0.2 }} // Stagger animation slightly
         >
             {(displayParticipants.length > 0 && displayWorkshops.length > 0) ? (
                <div className="bg-white p-6 rounded-xl shadow-lg border border-slate-100 overflow-hidden">


                  <RACIVisualization
                      key="live-viz"
                      participants={displayParticipants} // Already validated as array
                      workshops={displayWorkshops} // Already validated as array
                      matrix={displayMatrix} // Already validated as object

                   />

                </div>
              ) : (
                 <div className="bg-white p-6 rounded-xl shadow-lg border border-slate-100 overflow-hidden">
                     <div className="flex items-center justify-between mb-6">
                       <h2 className="text-xl font-semibold text-slate-800 flex items-center">
                           <BarChart2 size={20} className="mr-2 text-blue-600" />
                           {t('context.sections.visualization')}
                       </h2>

                       <div className="hidden sm:block px-3 py-1 bg-blue-50 text-blue-700 rounded-full text-xs font-medium">
                         {t('context.steps.step3')}
                       </div>
                     </div>
                     <div className="text-center py-12 px-4 border border-dashed border-slate-300 rounded-lg bg-slate-50">
                         <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-slate-100 text-slate-400 mb-4">
                           <BarChart2 size={28} />
                         </div>
                         <p className="text-slate-700 font-medium text-lg mb-2">{t('context.table.cannotDisplayVisualization')}</p>
                         <p className="text-sm text-slate-500 max-w-md mx-auto">{t('context.table.addParticipantsHint')}</p>
                     </div>
                 </div>
              )}
         </motion.div>

      {/* Guide Modal */}
      <GuideModal isOpen={isGuideOpen} onClose={() => setIsGuideOpen(false)} />
    </div> // End overall container
  );
};

export default Context;