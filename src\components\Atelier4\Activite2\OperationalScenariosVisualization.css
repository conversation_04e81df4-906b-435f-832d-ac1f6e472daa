/* src/components/Atelier4/Activite2/OperationalScenariosVisualization.css */

.visualization-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.empty-visualization {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400px;
  color: #6c757d;
  text-align: center;
}

.empty-visualization i {
  font-size: 3rem;
  margin-bottom: 20px;
  color: #dee2e6;
}

.empty-visualization h3 {
  margin: 0 0 10px 0;
  color: #495057;
}

.empty-visualization p {
  margin: 0;
  font-size: 1rem;
}

.visualization-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  background: #f8f9fa;
  border-bottom: 1px solid #dee2e6;
  flex-wrap: wrap;
  gap: 15px;
}

.view-mode-selector {
  display: flex;
  gap: 4px;
  background: white;
  padding: 4px;
  border-radius: 8px;
  border: 1px solid #dee2e6;
}

.mode-btn {
  padding: 8px 16px;
  border: none;
  background: transparent;
  color: #6c757d;
  cursor: pointer;
  border-radius: 6px;
  font-size: 0.9rem;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 6px;
  transition: all 0.2s ease;
}

.mode-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.mode-btn.active {
  background: #007bff;
  color: white;
}

.mode-btn:hover:not(.active):not(:disabled) {
  background: #f8f9fa;
  color: #495057;
}

.scenario-selector {
  display: flex;
  align-items: center;
  gap: 10px;
}

.scenario-selector label {
  font-weight: 500;
  color: #495057;
  white-space: nowrap;
}

.scenario-selector select {
  padding: 6px 12px;
  border: 1px solid #ced4da;
  border-radius: 6px;
  background: white;
  color: #495057;
  font-size: 0.9rem;
  min-width: 200px;
}

.visualization-stats {
  display: flex;
  gap: 20px;
  align-items: center;
}

.stat {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #6c757d;
  font-size: 0.9rem;
  font-weight: 500;
}

.stat i {
  color: #007bff;
}

.flow-container {
  flex: 1;
  height: 600px;
  background: #f8f9fa;
}

/* Custom Node Styles */
.scenario-node {
  background: white;
  border: 2px solid #007bff;
  border-radius: 12px;
  padding: 15px;
  min-width: 250px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
  position: relative;
}

.scenario-node.selected {
  border-color: #0056b3;
  box-shadow: 0 6px 20px rgba(0, 123, 255, 0.3);
  transform: translateY(-2px);
}

.scenario-node:hover {
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.15);
  transform: translateY(-1px);
}

.node-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
}

.node-title {
  font-weight: 600;
  color: #495057;
  font-size: 0.95rem;
  line-height: 1.3;
  flex: 1;
  margin-right: 10px;
}

.severity-indicator {
  color: white;
  padding: 3px 8px;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  white-space: nowrap;
}

.node-content {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.node-info {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #6c757d;
  font-size: 0.8rem;
}

.info-item i {
  width: 12px;
  color: #007bff;
}

.node-stats {
  display: flex;
  justify-content: space-between;
  gap: 10px;
}

.stat {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2px;
}

.stat-label {
  font-size: 0.7rem;
  color: #6c757d;
  text-transform: uppercase;
  font-weight: 500;
}

.stat-value {
  font-size: 0.9rem;
  font-weight: 600;
  color: #495057;
  text-transform: capitalize;
}

.ai-badge {
  position: absolute;
  top: -8px;
  right: -8px;
  background: #17a2b8;
  color: white;
  padding: 4px 6px;
  border-radius: 4px;
  font-size: 0.7rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 3px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* Step Node Styles */
.step-node {
  background: white;
  border: 2px solid #28a745;
  border-radius: 8px;
  padding: 12px;
  min-width: 200px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

.step-node.selected {
  border-color: #1e7e34;
  box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
}

.step-node:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transform: translateY(-1px);
}

.step-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.step-number {
  background: #28a745;
  color: white;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.75rem;
  font-weight: 600;
  flex-shrink: 0;
}

.step-name {
  font-weight: 500;
  color: #495057;
  font-size: 0.85rem;
  line-height: 1.2;
}

.step-content {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.step-duration {
  color: #6c757d;
  font-size: 0.75rem;
  font-weight: 500;
}

.techniques {
  display: flex;
  flex-wrap: wrap;
  gap: 3px;
}

.technique-tag {
  background: #e9ecef;
  color: #495057;
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 0.7rem;
  font-weight: 500;
}

.more-count {
  background: #6c757d;
  color: white;
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 0.7rem;
  font-weight: 500;
}

/* Attack Path Node Styles */
.attack-path-node {
  background: white;
  border: 2px solid #ffc107;
  border-radius: 10px;
  padding: 15px;
  min-width: 220px;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

.attack-path-node.selected {
  border-color: #e0a800;
  box-shadow: 0 5px 15px rgba(255, 193, 7, 0.3);
}

.attack-path-node:hover {
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
  transform: translateY(-1px);
}

.path-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.path-header i {
  color: #ffc107;
  font-size: 1.1rem;
}

.path-name {
  font-weight: 600;
  color: #495057;
  font-size: 0.9rem;
  line-height: 1.3;
}

.path-info {
  text-align: center;
}

.scenario-count {
  background: #f8f9fa;
  color: #6c757d;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.8rem;
  font-weight: 500;
}

/* Legend Styles */
.legend {
  background: white;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  padding: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  min-width: 150px;
}

.legend h4 {
  margin: 0 0 10px 0;
  font-size: 0.9rem;
  color: #495057;
  font-weight: 600;
}

.legend-items {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 2px;
}

.scenario-color {
  background: #007bff;
}

.step-color {
  background: #28a745;
}

.path-color {
  background: #ffc107;
}

.legend-item span {
  font-size: 0.8rem;
  color: #495057;
  font-weight: 500;
}

/* React Flow Overrides */
.react-flow__node {
  cursor: pointer;
}

.react-flow__edge {
  cursor: default;
}

.react-flow__controls {
  background: white;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.react-flow__controls-button {
  background: white;
  border: none;
  border-bottom: 1px solid #dee2e6;
  color: #495057;
  font-size: 1rem;
  transition: all 0.2s ease;
}

.react-flow__controls-button:hover {
  background: #f8f9fa;
  color: #007bff;
}

.react-flow__controls-button:last-child {
  border-bottom: none;
}

.react-flow__minimap {
  background: white;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Responsive Design */
@media (max-width: 768px) {
  .visualization-controls {
    flex-direction: column;
    align-items: stretch;
    gap: 15px;
  }

  .view-mode-selector {
    justify-content: center;
  }

  .visualization-stats {
    justify-content: center;
  }

  .scenario-selector {
    justify-content: center;
  }

  .scenario-selector select {
    min-width: 150px;
  }

  .flow-container {
    height: 500px;
  }

  .scenario-node,
  .step-node,
  .attack-path-node {
    min-width: 180px;
  }

  .node-title,
  .step-name,
  .path-name {
    font-size: 0.8rem;
  }

  .legend {
    min-width: 120px;
    padding: 10px;
  }
}

@media (max-width: 480px) {
  .visualization-controls {
    padding: 15px;
  }

  .view-mode-selector {
    flex-direction: column;
  }

  .mode-btn {
    justify-content: center;
  }

  .flow-container {
    height: 400px;
  }

  .scenario-node,
  .step-node,
  .attack-path-node {
    min-width: 150px;
    padding: 10px;
  }

  .node-stats {
    flex-direction: column;
    gap: 5px;
  }

  .legend {
    position: fixed;
    top: 10px;
    right: 10px;
    z-index: 1000;
  }
}