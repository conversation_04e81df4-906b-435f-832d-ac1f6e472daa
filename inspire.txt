import React, { useState, useEffect, useRef, createContext, useContext } from 'react';
import { AlertCircle, AlertTriangle, Target, Database, Eye, Zap, CheckCircle, Clock, Shield, Save, Bot, ChevronLeft, ChevronRight, ArrowRight } from 'lucide-react';

// --- MOCK IMPLEMENTATIONS ---
// To make this component self-contained and runnable, we'll mock the missing dependencies.

// 1. Mock AnalysisContext
const mockAnalysis = { id: 1, name: 'Cyber Risk Analysis Q3' };
const AnalysisContext = createContext({ currentAnalysis: mockAnalysis });
const useAnalysis = () => useContext(AnalysisContext);

// 2. Mock API Client
const mockApiData = {
    attackPaths: [
        { id: 101, businessValueId: 201, referenceCode: 'AP-001', name: 'Phishing Campaign to Steal Credentials', businessValueName: 'Customer Database Access', sourceRiskName: 'Malicious External Actor', dreadedEventName: 'Data Breach', objectifVise: 'Exfiltrate PII', stakeholders: [{ name: 'FIN7 Group' }] },
        { id: 102, businessValueId: 202, referenceCode: 'AP-002', name: 'Exploit Public-Facing Application', businessValueName: 'E-commerce Platform', sourceRiskName: 'Opportunistic Attacker', dreadedEventName: 'Service Disruption', objectifVise: 'Take down website', stakeholders: [{ name: 'Anonymous' }] },
        { id: 103, businessValueId: 201, referenceCode: 'AP-003', name: 'Insider Threat Data Exfiltration', businessValueName: 'Internal Financial Records', sourceRiskName: 'Disgruntled Employee', dreadedEventName: 'Fraud', objectifVise: 'Steal financial data', stakeholders: [{ name: 'Internal Threat' }] },
        { id: 104, businessValueId: 203, referenceCode: 'AP-004', name: 'Ransomware on Critical Server', businessValueName: 'File Storage Server', sourceRiskName: 'Ransomware Gang', dreadedEventName: 'Data Unavailability', objectifVise: 'Encrypt files for ransom', stakeholders: [{ name: 'LockBit' }] },
    ],
    businessValues: {
        data: {
            businessValues: [
                { id: 201, name: 'Customer Database Access', supportAssets: [{ id: 301, name: 'Primary DB Server', type: 'Server', vendor: 'Oracle', product: 'Database', version: '19c' }, { id: 302, name: 'DB Admin Workstation', type: 'Workstation' }] },
                { id: 202, name: 'E-commerce Platform', supportAssets: [{ id: 303, name: 'Web Server', type: 'Server', vendor: 'Apache' }, { id: 304, name: 'Payment Gateway API', type: 'API' }] },
                { id: 203, name: 'File Storage Server', supportAssets: [{ id: 305, name: 'NAS Storage', type: 'Storage' }] },
            ]
        }
    }
};

const api = {
    get: async (url) => {
        console.log(`Mock API GET: ${url}`);
        if (url.includes('attack-paths')) {
            return Promise.resolve({ data: { success: true, data: mockApiData.attackPaths } });
        }
        if (url.includes('business-values')) {
            return Promise.resolve({ data: mockApiData.businessValues });
        }
        return Promise.resolve({ data: {} });
    }
};

// 3. Mock Toast Utilities
const showToast = (message, type = 'info') => console.log(`[${type.toUpperCase()} TOAST] ${message}`);
const showSuccessToast = (message) => showToast(message, 'success');
const showErrorToast = (message) => showToast(message, 'error');
const showLoadingToast = (message) => {
    const toastId = Date.now();
    showToast(`${message} (ID: ${toastId})`, 'loading');
    return toastId;
};
const updateToast = (id, message, type) => showToast(`${message} (Updated ID: ${id})`, type);
const showSuccessToastOnce = (message) => console.log(`[SUCCESS TOAST ONCE] ${message}`);
const showErrorToastOnce = (message) => console.log(`[ERROR TOAST ONCE] ${message}`);


// 4. Mock CTI Services
const ctiService = {
    performCTIAnalysis: async (assets, analysisType) => {
        console.log(`[ctiService] Performing ${analysisType} analysis on:`, assets);
        await new Promise(resolve => setTimeout(resolve, 1500)); // Simulate network delay
        const newVulnerabilities = assets.map(asset => ({
            id: `CVE-2024-${Math.floor(Math.random() * 10000)}`,
            name: `Mock Vulnerability for ${asset.name}`,
            severity: 'High',
            assetId: asset.id,
        }));
        const newTechniques = assets.map(asset => ({
            id: `T${Math.floor(Math.random() * 1000) + 1000}`,
            name: `Mock Technique for ${asset.name}`,
            tactic: 'Execution',
            assetId: asset.id,
        }));

        const results = {
            totalVulnerabilities: analysisType !== 'mitre' ? newVulnerabilities.length : 0,
            totalAttackTechniques: analysisType !== 'nist' ? newTechniques.length : 0,
            overallRiskScore: Math.random() * 10,
            assets: assets.map(asset => ({
                ...asset,
                vulnerabilities: analysisType !== 'mitre' ? [{ id: `CVE-2024-${Math.floor(Math.random() * 10000)}`, name: `Mock CVE for ${asset.name}`, severity: 'High' }] : [],
                attackTechniques: analysisType !== 'nist' ? [{ id: `T${Math.floor(Math.random() * 1000) + 1000}`, name: `Mock TTP for ${asset.name}`, tactic: 'Initial Access' }] : [],
            })),
            analysisDate: new Date().toISOString(),
            dataSource: `Mock ${analysisType.toUpperCase()} Source`,
        };
        console.log("[ctiService] Analysis complete, returning:", results);
        return results;
    }
};

const ctiResultsService = {
    getCTIResults: async (analysisId) => {
        console.log(`[ctiResultsService] Getting CTI results for analysis ${analysisId}`);
        // Return a 404-like error to simulate no data initially
        return Promise.reject({ status: 404, message: 'No saved data found' });
    },
    saveCTIResults: async (analysisId, data) => {
        console.log(`[ctiResultsService] Saving CTI results for analysis ${analysisId}:`, data);
        await new Promise(resolve => setTimeout(resolve, 1000));
        return Promise.resolve({ success: true, data });
    },
    updateCTIResults: async (analysisId, data) => {
        console.log(`[ctiResultsService] Updating CTI results for analysis ${analysisId}:`, data);
        await new Promise(resolve => setTimeout(resolve, 1000));
        return Promise.resolve({ success: true, data });
    },
    deleteCTIResults: async (analysisId) => {
        console.log(`[ctiResultsService] Deleting CTI results for analysis ${analysisId}`);
        await new Promise(resolve => setTimeout(resolve, 1000));
        return Promise.resolve({ success: true });
    },
    formatCTIData: (attackPaths, vulnerabilities, techniques, metadata) => {
        console.log('[ctiResultsService] Formatting CTI data');
        return { attackPaths, vulnerabilities, techniques, metadata };
    }
};

// 5. Mock CTIDetailedResults Component
const CTIDetailedResults = ({ ctiResults, onUpdateAsset, onDeleteAsset }) => (
    <div className="border-t border-gray-200 mt-6 pt-6">
        <h3 className="text-lg font-semibold text-gray-800 mb-4">Mock Detailed Results</h3>
        <div className="bg-gray-50 p-4 rounded-lg">
            <p className="font-medium">CTI Analysis Summary:</p>
            <p className="text-sm">Total Vulnerabilities: {ctiResults.totalVulnerabilities}</p>
            <p className="text-sm">Total Attack Techniques: {ctiResults.totalAttackTechniques}</p>
            <p className="text-sm">Assets Analyzed: {ctiResults.assets.length}</p>
            <div className="mt-4 space-y-2">
                {ctiResults.assets.map(asset => (
                    <div key={asset.id} className="bg-white p-3 rounded shadow-sm border flex justify-between items-center">
                        <div>
                            <p className="font-semibold">{asset.name}</p>
                            <p className="text-xs text-gray-500">{asset.vulnerabilities?.length || 0} CVEs, {asset.attackTechniques?.length || 0} TTPs</p>
                        </div>
                        <div className="space-x-2">
                             <button onClick={() => onUpdateAsset(asset)} className="text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded hover:bg-blue-200">Update</button>
                             <button onClick={() => onDeleteAsset(asset)} className="text-xs bg-red-100 text-red-700 px-2 py-1 rounded hover:bg-red-200">Delete</button>
                        </div>
                    </div>
                ))}
            </div>
        </div>
    </div>
);


// --- MAIN COMPONENT ---
const ThreatIntelligence = () => {
  const { currentAnalysis } = useAnalysis();
  const [selectedAttackPath, setSelectedAttackPath] = useState(null);
  const [attackPaths, setAttackPaths] = useState([]);
  const [supportAssets, setSupportAssets] = useState([]);
  const [selectedAssets, setSelectedAssets] = useState([]);
  const [ctiResults, setCtiResults] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [vulnerabilityActions, setVulnerabilityActions] = useState({});
  const [techniqueActions, setTechniqueActions] = useState({});
  const [isSaving, setIsSaving] = useState(false);
  const [isLoadingSavedData, setIsLoadingSavedData] = useState(false);
  const [hasSavedData, setHasSavedData] = useState(false);
  const [currentAnalysisType, setCurrentAnalysisType] = useState(null);
  const [completedAnalysisTypes, setCompletedAnalysisTypes] = useState(new Set());

  // Carousel state and ref
  const carouselRef = useRef(null);
  const [canGoPrev, setCanGoPrev] = useState(false);
  const [canGoNext, setCanGoNext] = useState(true);

  // Carousel navigation functions
  const handleCarouselScroll = () => {
    const el = carouselRef.current;
    if (el) {
      const atStart = el.scrollLeft < 10;
      // Check if scrolled to the end (with a small tolerance)
      const atEnd = el.scrollLeft + el.clientWidth >= el.scrollWidth - 10;
      setCanGoPrev(!atStart);
      setCanGoNext(!atEnd);
    }
  };

  const scrollCarousel = (direction) => {
    const el = carouselRef.current;
    if (el) {
      const scrollAmount = el.clientWidth * 0.8; // Scroll by 80% of the visible width
      el.scrollBy({
        left: direction === 'next' ? scrollAmount : -scrollAmount,
        behavior: 'smooth',
      });
    }
  };
  
  // Attach scroll listener for carousel button states
  useEffect(() => {
    const el = carouselRef.current;
    if (el) {
      handleCarouselScroll(); // Initial check
      el.addEventListener('scroll', handleCarouselScroll);
      // Add resize listener to re-evaluate scroll buttons
      window.addEventListener('resize', handleCarouselScroll);
      return () => {
        el.removeEventListener('scroll', handleCarouselScroll);
        window.removeEventListener('resize', handleCarouselScroll);
      }
    }
  }, [attackPaths]); // Re-run when attack paths change


  // Load saved CTI data from database
  const loadSavedCTIData = async () => {
    if (!currentAnalysis) return;

    try {
      setIsLoadingSavedData(true);
      console.log('[ThreatIntelligence] Checking for saved CTI data...');

      const response = await ctiResultsService.getCTIResults(currentAnalysis.id);

      // Check different response structures
      let savedData = null;
      if (response?.data?.data) {
        savedData = response.data.data;
      } else if (response?.data) {
        savedData = response.data;
      } else if (response) {
        savedData = response;
      }

      if (savedData && (savedData.attackPaths || savedData.vulnerabilities || savedData.techniques)) {
        // Ensure arrays exist
        const attackPaths = savedData.attackPaths || [];
        const vulnerabilities = savedData.vulnerabilities || [];
        const techniques = savedData.techniques || [];

        // Check if there's meaningful data (at least some vulnerabilities or techniques)
        if (vulnerabilities.length === 0 && techniques.length === 0) {
          console.log('[loadSavedCTIData] No meaningful CTI data found, skipping load');
          setHasSavedData(false);
          return;
        }

        // Create assets from the saved data structure
        let assets = [];

        // First, try to use saved assets data if available
        if (savedData.metadata?.assetsData && Array.isArray(savedData.metadata.assetsData)) {
          assets = savedData.metadata.assetsData;
        } else if (attackPaths.length > 0) {
          // If we have attack paths, create assets from them
          assets = attackPaths.map(path => {
            const pathVulns = vulnerabilities.filter(v => v.attackPathId === path.id);
            const pathTechs = techniques.filter(t => t.attackPathId === path.id);

            return {
              id: path.id,
              name: path.businessValueName || path.name || 'Actif inconnu',
              type: path.type || 'Business Asset',
              vulnerabilities: pathVulns,
              attackTechniques: pathTechs,
              attackPathId: path.id,
              attackPathName: path.referenceCode || path.name,
              businessValueName: path.businessValueName
            };
          });
        } else {
          // If no attack paths, try to create assets from vulnerabilities/techniques
          const assetIds = new Set();

          vulnerabilities.forEach(v => {
            if (v.assetId) assetIds.add(v.assetId);
            if (v.attackPathId) assetIds.add(v.attackPathId);
            if (v.assetName && !v.assetId && !v.attackPathId) assetIds.add(v.assetName);
          });

          techniques.forEach(t => {
            if (t.assetId) assetIds.add(t.assetId);
            if (t.attackPathId) assetIds.add(t.attackPathId);
            if (t.assetName && !t.assetId && !t.attackPathId) assetIds.add(t.assetName);
          });

          if (assetIds.size === 0) {
            // If no asset IDs found, create a single asset with all data
            assets = [{
              id: 'combined-asset',
              name: 'Actifs Analysés',
              type: 'Combined Assets',
              vulnerabilities: vulnerabilities,
              attackTechniques: techniques
            }];
          } else {
            assets = Array.from(assetIds).map(assetId => {
              const assetVulns = vulnerabilities.filter(v =>
                v.assetId === assetId ||
                v.attackPathId === assetId ||
                v.assetName === assetId
              );
              const assetTechs = techniques.filter(t =>
                t.assetId === assetId ||
                t.attackPathId === assetId ||
                t.assetName === assetId
              );

              return {
                id: assetId,
                name: assetVulns[0]?.assetName || assetTechs[0]?.assetName || `Asset ${assetId}`,
                type: 'Support Asset',
                vulnerabilities: assetVulns,
                attackTechniques: assetTechs
              };
            });
          }
        }

        const restoredResults = {
          attackPaths: attackPaths,
          assets: assets,
          totalVulnerabilities: vulnerabilities.length,
          totalAttackTechniques: techniques.length,
          analysisDate: savedData.metadata?.analysisDate || savedData.savedAt || new Date().toISOString(),
          savedDate: savedData.updatedAt || savedData.createdAt || savedData.savedAt || new Date().toISOString(), // Database save timestamp
          overallRiskScore: savedData.metadata?.overallRiskScore || 'Medium',
          dataSource: 'Données sauvegardées'
        };


        setCtiResults(restoredResults);

        // Restore completed analysis types from saved data
        const savedAnalysisTypes = new Set();
        if (restoredResults.analysisType) {
          const analysisTypes = restoredResults.analysisType.split('+');
          analysisTypes.forEach(type => savedAnalysisTypes.add(type.trim()));
        }
        // Also check if assets have data for specific types
        if (restoredResults.assets) {
          restoredResults.assets.forEach(asset => {
            if (asset.vulnerabilities && asset.vulnerabilities.length > 0) {
              savedAnalysisTypes.add('nist');
            }
            if (asset.attackTechniques && asset.attackTechniques.length > 0) {
              if (restoredResults.analysisType?.includes('atlas')) {
                savedAnalysisTypes.add('atlas');
              } else {
                savedAnalysisTypes.add('mitre');
              }
            }
          });
        }
        setCompletedAnalysisTypes(savedAnalysisTypes);

        // Restore user actions
        if (savedData.metadata?.vulnerabilityActions) {
          console.log('[ThreatIntelligence] Restoring vulnerability actions:', savedData.metadata.vulnerabilityActions);
          setVulnerabilityActions(savedData.metadata.vulnerabilityActions);
        }
        if (savedData.metadata?.techniqueActions) {
          console.log('[ThreatIntelligence] Restoring technique actions:', savedData.metadata.techniqueActions);
          setTechniqueActions(savedData.metadata.techniqueActions);
        }

        setHasSavedData(true);
        showSuccessToastOnce(`Données CTI restaurées: ${restoredResults.totalVulnerabilities} vulnérabilités et ${restoredResults.totalAttackTechniques} techniques`);
      } else {
        setHasSavedData(false);
      }
    } catch (error) {
      setHasSavedData(false);
      // Handle 404 errors gracefully (expected when no CTI data exists)
      if (error.status === 404 || error.response?.status === 404 ||
        (error.data && error.data.message && error.data.message.includes("not found"))) {
        console.log('[ThreatIntelligence] No saved CTI data found (this is normal for new analyses or after deletion)');
      } else {
        // Only log and show actual errors (not 404s)
        console.error('[ThreatIntelligence] Error loading saved CTI data:', error);
        showErrorToast(`Erreur lors du chargement des données: ${error.message}`);
      }
    } finally {
      setIsLoadingSavedData(false);
    }
  };

  // Check for saved data availability when component mounts
  const checkSavedDataAvailability = async () => {
    if (!currentAnalysis) return;

    try {
      console.log('[ThreatIntelligence] Checking for saved CTI data...');
      const response = await ctiResultsService.getCTIResults(currentAnalysis.id);

      // Check if we have valid CTI data
      let hasValidData = false;
      if (response?.data?.data) {
        const data = response.data.data;
        hasValidData = (data.vulnerabilities?.length > 0 || data.techniques?.length > 0);
      } else if (response?.data) {
        hasValidData = (response.data.vulnerabilities?.length > 0 || response.data.techniques?.length > 0);
      }

      console.log('[ThreatIntelligence] Has saved data:', hasValidData);
      setHasSavedData(hasValidData);
    } catch (error) {
      // Handle 404 errors gracefully (expected when no CTI data exists)
      if (error.status === 404 || error.response?.status === 404) {
        console.log('[ThreatIntelligence] No saved data available (this is normal for new analyses)');
      } else {
        console.log('[ThreatIntelligence] Error checking saved data:', error);
      }
      setHasSavedData(false);
    }
  };

  // Load attack paths and check for saved CTI data when component mounts
  useEffect(() => {
    if (currentAnalysis) {
      loadAttackPaths();
      // Automatically load saved CTI data if available
      loadSavedCTIData();
    }
  }, [currentAnalysis]);

  // Load support assets when attack path is selected
  useEffect(() => {
    if (selectedAttackPath) {
      loadSupportAssets();
    }
  }, [selectedAttackPath]);

  // Check if attack path has been CTI analyzed
  const isAttackPathAnalyzed = (pathId) => {
    return ctiResults?.attackPaths?.some(ap => ap.pathId === pathId || ap.id === pathId) || false;
  };

  // Check if business asset has been CTI analyzed
  const isAssetAnalyzed = (assetId) => {
    return ctiResults?.assets?.some(asset => asset.id === assetId) || false;
  };

  const loadAttackPaths = async () => {
    try {
      setIsLoading(true);
      const response = await api.get(`/analyses/${currentAnalysis.id}/attack-paths`);

      if (response.data.success) {
        setAttackPaths(response.data.data);
      } else if (Array.isArray(response.data)) {
        setAttackPaths(response.data);
      } else {
        setAttackPaths([]);
      }
    } catch (error) {
      console.error('Error loading attack paths:', error);
      setError('Erreur lors du chargement des chemins d\'attaque');
    } finally {
      setIsLoading(false);
    }
  };

  const loadSupportAssets = async () => {
    try {
      setIsLoading(true);
      // Get all business values from the analysis
      const response = await api.get(`/analyses/${currentAnalysis.id}/business-values`);

      // Handle different response structures
      let businessValues = [];
      if (response.data.success) {
        businessValues = response.data.data.businessValues || [];
      } else if (response.data.data && response.data.data.businessValues) {
        businessValues = response.data.data.businessValues;
      } else if (response.data.businessValues) {
        businessValues = response.data.businessValues;
      }

      // Find the specific business value for this attack path
      const targetBusinessValue = businessValues.find(bv =>
        bv.id === selectedAttackPath.businessValueId ||
        bv.id === parseInt(selectedAttackPath.businessValueId)
      );

      const assets = [];

      if (targetBusinessValue && targetBusinessValue.supportAssets && Array.isArray(targetBusinessValue.supportAssets)) {
        targetBusinessValue.supportAssets.forEach(asset => {
          assets.push({
            id: asset.id,
            name: asset.name,
            type: asset.type,
            criticality: asset.criticality || 'moyen',
            vendor: asset.vendor,
            product: asset.product,
            version: asset.version,
            businessValue: targetBusinessValue.name
          });
        });
      }

      if (assets.length === 0) {
        setSupportAssets([]);
        setSelectedAssets([]);
      } else {
        setSupportAssets(assets);
        setSelectedAssets(assets.map(asset => ({ ...asset, selected: true })));
      }
    } catch (error) {
      console.error('Error loading support assets:', error);
      setError('Erreur lors du chargement des biens supports');
      setSupportAssets([]);
      setSelectedAssets([]);
    } finally {
      setIsLoading(false);
    }
  };

  const performCTIAnalysis = async (analysisType = 'combined') => {
    const selectedCount = selectedAssets.filter(asset => asset.selected).length;
    if (selectedCount === 0) {
      showErrorToast('Veuillez sélectionner au moins un bien support');
      return;
    }

    // Set current analysis type
    setCurrentAnalysisType(analysisType);

    // Show loading toast with analysis type
    const analysisTypeLabels = {
      'nist': 'Vulnérabilités NIST',
      'mitre': 'Techniques MITRE ATT&CK',
      'atlas': 'Menaces IA ATLAS',
      'combined': 'CTI'
    };
    const toastId = showLoadingToast(`Analyse ${analysisTypeLabels[analysisType]} en cours pour ${selectedCount} actif${selectedCount > 1 ? 's' : ''}...`);

    try {
      setIsLoading(true);
      setError(null);

      // Get only selected assets
      const assetsToAnalyze = selectedAssets.filter(asset => asset.selected);

      // Add attack path information to assets before analysis
      const assetsWithPathInfo = assetsToAnalyze.map(asset => ({
        ...asset,
        attackPathId: selectedAttackPath.id,
        attackPathName: selectedAttackPath.referenceCode || selectedAttackPath.name,
        businessValueName: selectedAttackPath.businessValueName
      }));

      // Perform real CTI analysis using specified analysis type
      const newResults = await ctiService.performCTIAnalysis(assetsWithPathInfo, analysisType);

      // Accumulate results instead of replacing them
      setCtiResults(prevResults => {
        if (!prevResults) {
          // First analysis - use new results directly
          return {
            ...newResults,
            analysisType: analysisType,
            attackPathInfo: {
              pathId: selectedAttackPath.id,
              pathName: selectedAttackPath.referenceCode || selectedAttackPath.name,
              businessValueName: selectedAttackPath.businessValueName
            }
          };
        }

        // Subsequent analyses - merge with existing results
        const existingAssetIds = new Set(prevResults.assets.map(asset => asset.id));
        const newAssets = newResults.assets.filter(asset => !existingAssetIds.has(asset.id));

        // Combine analysis types
        const combinedAnalysisTypes = prevResults.analysisType === analysisType
          ? analysisType
          : `${prevResults.analysisType}+${analysisType}`;

        const mergedResults = {
          totalVulnerabilities: prevResults.totalVulnerabilities + newResults.totalVulnerabilities,
          totalAttackTechniques: prevResults.totalAttackTechniques + newResults.totalAttackTechniques,
          overallRiskScore: Math.round(((prevResults.overallRiskScore + newResults.overallRiskScore) / 2) * 10) / 10,
          assets: [...prevResults.assets, ...newAssets],
          analysisDate: new Date().toISOString(),
          savedDate: prevResults.savedDate, // Keep existing save date until explicitly saved
          dataSource: newResults.dataSource,
          analysisType: combinedAnalysisTypes,
          attackPaths: [
            ...(prevResults.attackPaths || []),
            {
              pathId: selectedAttackPath.id,
              pathName: selectedAttackPath.referenceCode || selectedAttackPath.name,
              businessValueName: selectedAttackPath.businessValueName,
              assetsCount: newAssets.length,
              vulnerabilitiesCount: newResults.totalVulnerabilities,
              techniquesCount: newResults.totalAttackTechniques
            }
          ]
        };

        return mergedResults;
      });

      // Update toast to success
      updateToast(toastId, `Analyse ${analysisTypeLabels[analysisType]} terminée avec succès ! ${newResults.totalVulnerabilities} vulnérabilités et ${newResults.totalAttackTechniques} techniques trouvées.`, 'success');

      // Track completed analysis type
      setCompletedAnalysisTypes(prev => new Set([...prev, analysisType]));

    } catch (error) {
      console.error('[ThreatIntelligence] Error performing CTI analysis:', error);
      updateToast(toastId, `Erreur lors de l'analyse CTI: ${error.message}`, 'error');
      setError(`Erreur lors de l'analyse CTI: ${error.message}`);
    } finally {
      setIsLoading(false);
      setCurrentAnalysisType(null);
    }
  };

  const handleAssetToggle = (assetId) => {
    setSelectedAssets(prev => {
      const updated = prev.map(asset =>
        asset.id === assetId
          ? { ...asset, selected: !asset.selected }
          : asset
      );

      const toggledAsset = updated.find(asset => asset.id === assetId);
      const selectedCount = updated.filter(asset => asset.selected).length;

      if (toggledAsset?.selected) {
        showSuccessToastOnce(`Actif "${toggledAsset.name}" sélectionné (${selectedCount} actifs sélectionnés)`);
      } else {
        showSuccessToastOnce(`Actif "${toggledAsset.name}" désélectionné (${selectedCount} actifs sélectionnés)`);
      }

      return updated;
    });
  };

  // Handle vulnerability action updates
  const handleVulnerabilityAction = (vulnId, action) => {
    setVulnerabilityActions(prev => ({
      ...prev,
      [vulnId]: action
    }));
  };

  // Handle technique action updates
  const handleTechniqueAction = (techniqueId, action) => {
    setTechniqueActions(prev => ({
      ...prev,
      [techniqueId]: action
    }));
  };

  // Clear all CTI results
  const clearAllCTIResults = async () => {
    if (window.confirm('Êtes-vous sûr de vouloir effacer tous les résultats CTI ? Cette action est irréversible.')) {
      const toastId = showLoadingToast('Effacement des résultats CTI...');

      try {
        // Clear from database
        await ctiResultsService.deleteCTIResults(currentAnalysis.id);

        // Clear from state
        setCtiResults(null);
        setVulnerabilityActions({});
        setTechniqueActions({});
        setHasSavedData(false);

        updateToast(toastId, 'Tous les résultats CTI ont été effacés', 'success');
      } catch (error) {
        console.error('Error clearing CTI results:', error);

        // Clear from state anyway
        setCtiResults(null);
        setVulnerabilityActions({});
        setTechniqueActions({});
        setHasSavedData(false);

        updateToast(toastId, 'Résultats CTI effacés localement', 'success');
      }
    }
  };

  // Get action status for vulnerability
  const getVulnerabilityAction = (vulnId) => {
    return vulnerabilityActions[vulnId] || 'non-traité';
  };

  // Get action status for technique
  const getTechniqueAction = (techniqueId) => {
    return techniqueActions[techniqueId] || 'non-traité';
  };

  // Get CTI data freshness indicator
  const getCTIFreshness = (analysisDate) => {
    if (!analysisDate) return { level: 'unknown', color: 'gray', text: 'Date inconnue', icon: '❓' };

    const now = new Date();
    const analysisTime = new Date(analysisDate);
    const timeDiff = now - analysisTime;
    const minutesDiff = timeDiff / (1000 * 60);
    const hoursDiff = timeDiff / (1000 * 60 * 60);

    console.log('[getCTIFreshness] Analysis date:', analysisDate);
    console.log('[getCTIFreshness] Time diff (minutes):', minutesDiff);
    console.log('[getCTIFreshness] Time diff (hours):', hoursDiff);

    if (minutesDiff < 5) {
      return {
        level: 'fresh',
        color: 'green',
        text: `Maintenant`,
        icon: '🟢',
        bgColor: 'bg-green-50',
        borderColor: 'border-green-200',
        textColor: 'text-green-800'
      };
    } else if (hoursDiff < 1) {
      const mins = Math.round(minutesDiff);
      return {
        level: 'fresh',
        color: 'green',
        text: `${mins}min`,
        icon: '🟢',
        bgColor: 'bg-green-50',
        borderColor: 'border-green-200',
        textColor: 'text-green-800'
      };
    } else if (hoursDiff < 48) {
      const hours = Math.round(hoursDiff);
      return {
        level: 'fresh',
        color: 'green',
        text: `${hours}h`,
        icon: '🟢',
        bgColor: 'bg-green-50',
        borderColor: 'border-green-200',
        textColor: 'text-green-800'
      };
    } else if (hoursDiff < 168) { // 1 week
      const daysDiff = Math.round(hoursDiff / 24);
      return {
        level: 'recent',
        color: 'orange',
        text: `${daysDiff}j`,
        icon: '🟡',
        bgColor: 'bg-orange-50',
        borderColor: 'border-orange-200',
        textColor: 'text-orange-800'
      };
    } else if (hoursDiff < 720) { // 1 month
      const daysDiff = Math.round(hoursDiff / 24);
      return {
        level: 'aging',
        color: 'yellow',
        text: `${daysDiff}j`,
        icon: '🟠',
        bgColor: 'bg-yellow-50',
        borderColor: 'border-yellow-200',
        textColor: 'text-yellow-800'
      };
    } else {
      const daysDiff = Math.round(hoursDiff / 24);
      return {
        level: 'stale',
        color: 'red',
        text: `${daysDiff}j`,
        icon: '🔴',
        bgColor: 'bg-red-50',
        borderColor: 'border-red-200',
        textColor: 'text-red-800'
      };
    }
  };

  // Update individual asset CTI data
  const updateAssetCTI = async (asset) => {
    try {
      // Show loading toast
      const toastId = showLoadingToast(`Mise à jour CTI pour ${asset.name}...`);

      // Prepare asset for analysis
      const assetToAnalyze = {
        ...asset,
        selected: true,
        attackPathId: asset.attackPathId || asset.id,
        attackPathName: asset.attackPathName,
        businessValueName: asset.businessValueName
      };

      // Perform CTI analysis for this specific asset
      const newResults = await ctiService.performCTIAnalysis([assetToAnalyze], 'combined');

      // Update the asset in the current results
      setCtiResults(prevResults => {
        if (!prevResults) return prevResults;

        const updatedAssets = prevResults.assets.map(existingAsset => {
          if (existingAsset.id === asset.id) {
            const updatedAsset = newResults.assets[0];
            return {
              ...updatedAsset,
              lastUpdated: new Date().toISOString(),
              id: asset.id,
              name: asset.name,
              type: asset.type,
              attackPathId: asset.attackPathId,
              attackPathName: asset.attackPathName,
              businessValueName: asset.businessValueName
            };
          }
          return existingAsset;
        });

        // Recalculate totals
        const totalVulnerabilities = updatedAssets.reduce((sum, a) => sum + (a.vulnerabilities?.length || 0), 0);
        const totalAttackTechniques = updatedAssets.reduce((sum, a) => sum + (a.attackTechniques?.length || 0), 0);

        return {
          ...prevResults,
          assets: updatedAssets,
          totalVulnerabilities,
          totalAttackTechniques,
          analysisDate: new Date().toISOString()
        };
      });

      updateToast(toastId, `CTI mis à jour pour ${asset.name}`, 'success');
    } catch (error) {
      console.error('Error updating asset CTI:', error);
      showErrorToast(`Erreur lors de la mise à jour: ${error.message}`);
    }
  };

  // Delete individual asset CTI data
  const deleteAssetCTI = async (asset) => {
    try {
      // Remove the asset from current results
      setCtiResults(prevResults => {
        if (!prevResults) return prevResults;

        const filteredAssets = prevResults.assets.filter(existingAsset => existingAsset.id !== asset.id);

        // Recalculate totals
        const totalVulnerabilities = filteredAssets.reduce((sum, a) => sum + (a.vulnerabilities?.length || 0), 0);
        const totalAttackTechniques = filteredAssets.reduce((sum, a) => sum + (a.attackTechniques?.length || 0), 0);

        // If no assets left OR no meaningful data (no vulnerabilities and no techniques), clear completely
        if (filteredAssets.length === 0 || (totalVulnerabilities === 0 && totalAttackTechniques === 0)) {
          console.log('[deleteAssetCTI] No meaningful data left, clearing CTI results completely');
          return null;
        }

        return {
          ...prevResults,
          assets: filteredAssets,
          totalVulnerabilities,
          totalAttackTechniques,
          analysisDate: new Date().toISOString()
        };
      });

      // Remove any actions for this asset's vulnerabilities and techniques
      setVulnerabilityActions(prev => {
        const updated = { ...prev };
        asset.vulnerabilities?.forEach(vuln => {
          delete updated[vuln.id];
        });
        return updated;
      });

      setTechniqueActions(prev => {
        const updated = { ...prev };
        asset.attackTechniques?.forEach(tech => {
          delete updated[tech.id];
        });
        return updated;
      });

      // Check if we need to clear the database as well
      const hasRemainingData = ctiResults?.assets?.some(a =>
        a.id !== asset.id && ((a.vulnerabilities?.length || 0) > 0 || (a.attackTechniques?.length || 0) > 0)
      );

      if (!hasRemainingData) {
        // Clear from database completely
        console.log('[deleteAssetCTI] Clearing CTI data from database');
        await ctiResultsService.deleteCTIResults(currentAnalysis.id);
        setHasSavedData(false);
      } else {
        // Update database to reflect the deletion
        await saveCTIResults();
      }

      showSuccessToastOnce(`Actif "${asset.name}" supprimé avec succès`);
    } catch (error) {
      console.error('Error deleting asset CTI:', error);
      showErrorToast(`Erreur lors de la suppression: ${error.message}`);
    }
  };

  // Save CTI results to database
  const saveCTIResults = async () => {
    if (!ctiResults || !currentAnalysis) {
      showErrorToast('Aucun résultat CTI à sauvegarder');
      return;
    }

    // Show loading toast
    const toastId = showLoadingToast('Sauvegarde des résultats CTI...');

    try {
      setIsSaving(true);

      // Extract vulnerabilities and techniques from assets
      const allVulnerabilities = ctiResults.assets?.flatMap(asset =>
        (asset.vulnerabilities || []).map(vuln => ({
          ...vuln,
          assetId: asset.id,
          assetName: asset.name,
          attackPathId: asset.attackPathId || asset.id,
          attackPathName: asset.attackPathName,
          businessValueName: asset.businessValueName
        }))
      ) || [];

      const allTechniques = ctiResults.assets?.flatMap(asset =>
        (asset.attackTechniques || []).map(tech => ({
          ...tech,
          assetId: asset.id,
          assetName: asset.name,
          attackPathId: asset.attackPathId || asset.id,
          attackPathName: asset.attackPathName,
          businessValueName: asset.businessValueName
        }))
      ) || [];



      const ctiDataToSave = ctiResultsService.formatCTIData(
        ctiResults.attackPaths || [],
        allVulnerabilities,
        allTechniques,
        {
          analysisDate: ctiResults.analysisDate,
          overallRiskScore: ctiResults.overallRiskScore,
          totalVulnerabilities: ctiResults.totalVulnerabilities,
          totalAttackTechniques: ctiResults.totalAttackTechniques,
          dataSource: ctiResults.dataSource,
          vulnerabilityActions,
          techniqueActions,
          assetsData: ctiResults.assets // Save the full assets structure
        }
      );

      console.log('[ThreatIntelligence] Saving CTI data:', ctiDataToSave);

      // Save or update to database
      if (hasSavedData) {
        console.log('[ThreatIntelligence] Updating existing CTI data');
        await ctiResultsService.updateCTIResults(currentAnalysis.id, ctiDataToSave);
        updateToast(toastId, 'Résultats CTI mis à jour avec succès !', 'success');
      } else {
        console.log('[ThreatIntelligence] Saving new CTI data');
        await ctiResultsService.saveCTIResults(currentAnalysis.id, ctiDataToSave);
        updateToast(toastId, 'Résultats CTI sauvegardés avec succès !', 'success');
      }

      setHasSavedData(true);

      // Update the savedDate in the current results to reflect when it was saved to DB
      setCtiResults(prev => ({
        ...prev,
        savedDate: new Date().toISOString()
      }));

      console.log('[ThreatIntelligence] CTI data saved successfully');

    } catch (error) {
      console.error('[ThreatIntelligence] Error saving CTI results:', error);
      updateToast(toastId, `Erreur lors de la sauvegarde: ${error.message}`, 'error');
    } finally {
      setIsSaving(false);
    }
  };

  if (!currentAnalysis) {
    return (
      <div className="flex items-center justify-center py-12 bg-gray-50">
        <div className="text-center">
          <AlertCircle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-900 mb-2">
            Aucune analyse sélectionnée
          </h2>
          <p className="text-gray-600">
            Veuillez sélectionner une analyse pour commencer l'intelligence des menaces.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-gray-50/50 min-h-screen p-4 sm:p-6 lg:p-8">
    <div className="max-w-7xl mx-auto space-y-8">
      {/* === MAIN HEADER === */}
      <div className="bg-white rounded-xl shadow-sm border border-slate-100 p-6">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          {/* Left Side: Breadcrumb & Title */}
          <div>
            <div className="flex items-center text-sm text-slate-500 mb-2">
              <span className="hover:text-blue-600 transition-colors">Atelier 4</span>
              <ChevronRight className="h-4 w-4 mx-1 text-slate-400" />
              <span className="text-blue-600 font-medium">Intelligence des menaces</span>
            </div>
            <h1 className="text-2xl sm:text-3xl font-bold text-slate-800 flex items-center">
              <Target size={28} className="mr-3 text-blue-600" />
              Intelligence des Menaces
            </h1>
            <p className="text-slate-600 mt-1">
              Analyse: <span className="font-semibold">{currentAnalysis?.name}</span>
            </p>
          </div>

          {/* Right Side: Analysis Info */}
          <div className="flex items-center space-x-3">
            {ctiResults && (ctiResults.totalVulnerabilities > 0 || ctiResults.totalAttackTechniques > 0) && (
              <div className="text-right">
                <div className="text-sm font-medium text-slate-700">
                  {ctiResults.totalVulnerabilities} vulnérabilités • {ctiResults.totalAttackTechniques} techniques
                </div>
                <div className="text-xs text-slate-500">
                  Analysé le {new Date(ctiResults.analysisDate).toLocaleDateString('fr-FR')}
                </div>
              </div>
            )}

            {/* Global Freshness Indicator */}
            {ctiResults && (ctiResults.totalVulnerabilities > 0 || ctiResults.totalAttackTechniques > 0) && (() => {
              const freshness = getCTIFreshness(ctiResults.savedDate || ctiResults.analysisDate);
              return (
                <div className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium ${freshness.bgColor} ${freshness.borderColor} ${freshness.textColor} border`}>
                  <span className="mr-1.5">{freshness.icon}</span>
                  {freshness.text}
                </div>
              );
            })()}
          </div>
        </div>
      </div>

      {/* Loading Saved Data Indicator */}
      {isLoadingSavedData && (
        <div className="bg-blue-50 border border-blue-200 rounded-xl p-4">
          <div className="flex items-center">
            <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-600 mr-3"></div>
            <div>
              <h3 className="text-sm font-medium text-blue-800">Chargement des données</h3>
              <p className="mt-1 text-sm text-blue-700">Vérification des analyses précédentes...</p>
            </div>
          </div>
        </div>
      )}

      {/* Saved Data Status */}
      {!isLoadingSavedData && hasSavedData && ctiResults &&
       (ctiResults.totalVulnerabilities > 0 || ctiResults.totalAttackTechniques > 0) && (
        <div className="bg-green-50 border border-green-200 rounded-xl p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <CheckCircle className="h-5 w-5 text-green-500 mr-3" />
              <div>
                <h3 className="text-sm font-medium text-green-800">Analyse précédente chargée</h3>
                <p className="mt-1 text-sm text-green-700">
                  {ctiResults.totalVulnerabilities} vulnérabilités et {ctiResults.totalAttackTechniques} techniques identifiées
                </p>
              </div>
            </div>
            {(() => {
              const freshness = getCTIFreshness(ctiResults.savedDate || ctiResults.analysisDate);
              return (
                <div className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium ${freshness.bgColor} ${freshness.borderColor} ${freshness.textColor} border`}>
                  <span className="mr-1.5">{freshness.icon}</span>
                  {freshness.text}
                </div>
              );
            })()}
          </div>
        </div>
      )}

      {/* Error Display */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-xl p-4">
          <div className="flex">
            <AlertCircle className="h-5 w-5 text-red-400" />
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">Erreur</h3>
              <p className="mt-1 text-sm text-red-700">{error}</p>
              <button onClick={() => setError(null)} className="mt-2 text-sm text-red-600 hover:text-red-500 underline">Fermer</button>
            </div>
          </div>
        </div>
      )}

      {/* Step 1: Attack Path Selection */}
      <div className="bg-white rounded-xl shadow-lg p-6 sm:p-8">
        <div className="flex items-center justify-between mb-4">
            <div className="flex items-center">
                <div className="bg-indigo-100 p-2 rounded-full mr-4"><Target className="h-6 w-6 text-indigo-600" /></div>
                <h2 className="text-xl font-semibold text-gray-900">1. Sélection du Chemin d'Attaque</h2>
            </div>
            {attackPaths.length > 0 && (
              <div className="flex items-center space-x-2">
                <button onClick={() => scrollCarousel('prev')} disabled={!canGoPrev} className="p-2 rounded-full transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed enabled:hover:bg-indigo-100 enabled:hover:scale-110 text-gray-500 enabled:text-indigo-600"><ChevronLeft className="h-5 w-5" /></button>
                <button onClick={() => scrollCarousel('next')} disabled={!canGoNext} className="p-2 rounded-full transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed enabled:hover:bg-indigo-100 enabled:hover:scale-110 text-gray-500 enabled:text-indigo-600"><ChevronRight className="h-5 w-5" /></button>
              </div>
            )}
        </div>

        {isLoading && !selectedAttackPath ? (
          <div className="text-center py-8"><div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600 mx-auto mb-4"></div><p className="text-gray-600">Chargement des chemins d'attaque...</p></div>
        ) : attackPaths.length === 0 ? (
          <div className="text-center py-8"><Target className="h-12 w-12 text-gray-300 mx-auto mb-4" /><p className="text-gray-500">Aucun chemin d'attaque trouvé</p></div>
        ) : (
          <div ref={carouselRef} className="flex overflow-x-auto snap-x snap-mandatory scroll-smooth py-4 -mx-4 px-4 space-x-6" style={{ scrollbarWidth: 'none', '-ms-overflow-style': 'none' }}>
            {attackPaths.map((path) => (
              <div key={path.id} className="flex-shrink-0 w-full sm:w-[55%] md:w-[45%] lg:w-[31%] snap-start">
                <div
                  className={`h-full flex flex-col rounded-xl cursor-pointer transition-all duration-300 border ${selectedAttackPath?.id === path.id ? 'border-indigo-500 bg-indigo-50/50 shadow-2xl' : 'bg-slate-50/70 border-slate-200 hover:border-indigo-300 hover:shadow-xl'} p-6`}
                  onClick={() => setSelectedAttackPath(path)}
                >
                  <div className="flex-grow">
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex items-center flex-wrap gap-2">
                        {path.referenceCode && <span className="inline-flex items-center text-xs font-bold text-indigo-700 bg-indigo-100 px-2.5 py-1 rounded-full">{path.referenceCode}</span>}
                        {isAttackPathAnalyzed(path.id) && <span className="inline-flex items-center text-xs font-medium text-green-700 bg-green-100 px-2.5 py-1 rounded-full">🔍 Analysé</span>}
                      </div>
                      {selectedAttackPath?.id === path.id && <div className="p-1 bg-indigo-500 rounded-full text-white"><CheckCircle className="h-4 w-4" /></div>}
                    </div>
                    <h3 className="text-lg font-bold text-gray-800 mb-4 leading-tight">{path.name || path.businessValueName || `Chemin d'Attaque ${path.id}`}</h3>
                    <div className="space-y-3 text-sm">
                      {path.businessValueName && <div className="flex items-start"><Database className="h-4 w-4 text-blue-500 mr-3 mt-0.5 flex-shrink-0" /><p><span className="font-semibold text-gray-700">Valeur Métier:</span> {path.businessValueName}</p></div>}
                      {path.sourceRiskName && <div className="flex items-start"><AlertTriangle className="h-4 w-4 text-red-500 mr-3 mt-0.5 flex-shrink-0" /><p><span className="font-semibold text-gray-700">Source de Risque:</span> {path.sourceRiskName}</p></div>}
                    </div>
                  </div>
                  {path.stakeholders && path.stakeholders.length > 0 && (
                    <div className="flex-shrink-0 pt-4 border-t border-slate-200 mt-4">
                      <div className="flex items-center"><div className="w-8 h-8 bg-gradient-to-br from-gray-700 to-gray-900 rounded-full flex items-center justify-center mr-3 shadow-lg"><span className="text-sm font-bold text-white">{(typeof path.stakeholders[0] === 'object' ? path.stakeholders[0].name : path.stakeholders[0]).charAt(0).toUpperCase()}</span></div><div><p className="text-sm font-semibold text-gray-800">Attaquant</p><p className="text-xs text-gray-500">{typeof path.stakeholders[0] === 'object' ? path.stakeholders[0].name : path.stakeholders[0]}</p></div></div>
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Step 2 & 3 */}
      {selectedAttackPath && (
        <>
            <div className="bg-white rounded-xl shadow-lg p-6 sm:p-8">
                <div className="flex items-center mb-6">
                    <div className="bg-green-100 p-2 rounded-full mr-4"><Database className="h-6 w-6 text-green-600" /></div>
                    <h2 className="text-xl font-semibold text-gray-900">2. Biens Supports & Lancement de l'Analyse</h2>
                </div>
                {isLoading && supportAssets.length === 0 ? (
                    <div className="text-center py-8"><div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-600 mx-auto mb-4"></div><p className="text-gray-600">Chargement des biens supports...</p></div>
                ) : supportAssets.length === 0 ? (
                    <div className="text-center py-8 bg-slate-50 rounded-lg"><Database className="h-12 w-12 text-gray-300 mx-auto mb-4" /><h3 className="text-lg font-medium text-gray-800 mb-2">Aucun bien support trouvé</h3><p className="text-gray-500 mb-4 max-w-md mx-auto">La valeur métier "{selectedAttackPath.businessValueName}" n'a pas de biens supports définis.</p><p className="text-sm text-gray-400">Veuillez d'abord ajouter des biens supports à cette valeur métier dans l'Atelier 1.</p></div>
                ) : (
                    <>
                        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 mb-6">
                            {selectedAssets.map((asset) => (
                                <div key={asset.id} className={`relative p-4 border-2 rounded-lg cursor-pointer transition-all duration-200 ${asset.selected ? 'border-green-500 bg-green-50' : 'border-gray-200 bg-white hover:border-gray-300'}`} onClick={() => handleAssetToggle(asset.id)}>
                                    {isAssetAnalyzed(asset.id) && <div className="absolute top-2 right-2"><div className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 border border-blue-200"><span className="mr-1">🔍</span>CTI</div></div>}
                                    <h3 className="font-semibold text-gray-800 text-sm pr-6">{asset.name}</h3>
                                    <p className="text-xs text-gray-500">{asset.type}</p>
                                    {asset.selected && <div className="absolute bottom-2 right-2 p-0.5 bg-green-600 text-white rounded-full"><CheckCircle className="h-4 w-4" /></div>}
                                </div>
                            ))}
                        </div>
                        <div className="text-center pt-6 border-t border-gray-200">
                            <div className="flex flex-wrap justify-center gap-4">
                                <button onClick={() => performCTIAnalysis('nist')} disabled={selectedAssets.filter(a => a.selected).length === 0 || isLoading} className="inline-flex items-center justify-center px-5 py-2.5 rounded-lg text-sm font-medium transition-all duration-200 disabled:bg-gray-200 disabled:text-gray-500 disabled:cursor-not-allowed bg-blue-600 text-white hover:bg-blue-700 shadow-md hover:shadow-lg transform hover:-translate-y-0.5"><Shield className="mr-2 h-4 w-4" />{isLoading && currentAnalysisType === 'nist' ? 'Analyse...' : `Vulnérabilités NIST (${selectedAssets.filter(a => a.selected).length})`}</button>
                                <button onClick={() => performCTIAnalysis('mitre')} disabled={selectedAssets.filter(a => a.selected).length === 0 || isLoading} className="inline-flex items-center justify-center px-5 py-2.5 rounded-lg text-sm font-medium transition-all duration-200 disabled:bg-gray-200 disabled:text-gray-500 disabled:cursor-not-allowed bg-red-600 text-white hover:bg-red-700 shadow-md hover:shadow-lg transform hover:-translate-y-0.5"><Target className="mr-2 h-4 w-4" />{isLoading && currentAnalysisType === 'mitre' ? 'Analyse...' : `Techniques MITRE (${selectedAssets.filter(a => a.selected).length})`}</button>
                                <button onClick={() => performCTIAnalysis('atlas')} disabled={selectedAssets.filter(a => a.selected).length === 0 || isLoading} className="inline-flex items-center justify-center px-5 py-2.5 rounded-lg text-sm font-medium transition-all duration-200 disabled:bg-gray-200 disabled:text-gray-500 disabled:cursor-not-allowed bg-purple-600 text-white hover:bg-purple-700 shadow-md hover:shadow-lg transform hover:-translate-y-0.5"><Bot className="mr-2 h-4 w-4" />{isLoading && currentAnalysisType === 'atlas' ? 'Analyse...' : `Menaces IA ATLAS (${selectedAssets.filter(a => a.selected).length})`}</button>
                            </div>
                        </div>
                    </>
                )}
            </div>

            {(ctiResults || hasSavedData) && (
                <div className="bg-white rounded-xl shadow-lg p-6 sm:p-8">
                    <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between mb-4 gap-4">
                        <div className="flex items-center">
                            <div className="bg-purple-100 p-2 rounded-full mr-4"><Zap className="h-6 w-6 text-purple-600" /></div>
                            <div><h2 className="text-xl font-semibold text-gray-900">3. Résultats & Actions</h2>
                                {ctiResults && ctiResults.attackPaths && ctiResults.attackPaths.length > 0 && <p className="text-sm text-gray-600 mt-1">Analyse de {ctiResults.attackPaths.length} chemin{ctiResults.attackPaths.length > 1 ? 's' : ''} d'attaque</p>}
                                {!ctiResults && <p className="text-sm text-gray-600 mt-1">Toutes les analyses CTI ont été supprimées</p>}
                            </div>
                        </div>
                        <div className="flex items-center space-x-3 flex-shrink-0">
                            <button onClick={saveCTIResults} disabled={isSaving} className="inline-flex items-center px-5 py-2.5 rounded-lg font-medium transition-all duration-200 disabled:bg-gray-200 disabled:text-gray-500 disabled:cursor-not-allowed bg-green-600 text-white hover:bg-green-700 shadow-md hover:shadow-lg transform hover:-translate-y-0.5">{isSaving ? <><div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>Sauvegarde...</> : <><Save className="mr-2 h-4 w-4" />Sauvegarder l'Analyse</>}</button>
                        </div>
                    </div>
                    {ctiResults && <CTIDetailedResults ctiResults={ctiResults} onUpdateAsset={updateAssetCTI} onDeleteAsset={deleteAssetCTI} completedAnalysisTypes={completedAnalysisTypes} />}
                </div>
            )}
        </>
      )}
    </div>
    </div>
  );
};


// We need a provider for the mock context
const App = () => (
    <AnalysisContext.Provider value={{ currentAnalysis: mockAnalysis }}>
        <ThreatIntelligence />
    </AnalysisContext.Provider>
);


export default App;
