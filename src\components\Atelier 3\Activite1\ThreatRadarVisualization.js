// src/components/Atelier 3/Activite1/ThreatRadarVisualization.js
import React, { useState, useRef, useEffect } from 'react';
import { Users, Building, Shield, AlertCircle, Settings, Info } from 'lucide-react';

const ThreatRadarVisualization = ({ 
  stakeholders, 
  thresholds,
  onUpdateThresholds,
  onSelectStakeholder,
  isLoading
}) => {
  const [selectedStakeholder, setSelectedStakeholder] = useState(null);
  const [isThresholdEditing, setIsThresholdEditing] = useState(false);
  const [editedThresholds, setEditedThresholds] = useState({ ...thresholds });
  const [hoveredStakeholder, setHoveredStakeholder] = useState(null);
  const svgRef = useRef(null);
  
  // SVG dimensions
  const width = 800;
  const height = 800;
  const centerX = width / 2;
  const centerY = height / 2;
  const maxRadius = Math.min(width, height) / 2 - 50;
  
  // Calculate radius based on threat level
  const getRadius = (threatLevel) => {
    // Invert the scale: higher threat = closer to center
    const normalizedLevel = Math.min(threatLevel, 10); // Cap at 10 for visualization
    return maxRadius * (1 - normalizedLevel / 10);
  };
  
  // Get stakeholder position
  const getStakeholderPosition = (stakeholder) => {
    const threatLevel = stakeholder.threatLevel || 0;
    
    const radius = getRadius(threatLevel);
    // Random angle for initial positioning (could be improved to avoid overlaps)
    const angle = stakeholder.id.charCodeAt(0) % 360 * (Math.PI / 180);
    
    return {
      x: centerX + radius * Math.cos(angle),
      y: centerY + radius * Math.sin(angle),
      threatLevel
    };
  };
  
  // Get color based on stakeholder category
  const getCategoryColor = (category) => {
    switch(category) {
      case 'client': return '#3B82F6'; // blue
      case 'partner': return '#10B981'; // green
      case 'provider': return '#F59E0B'; // amber
      case 'technical': return '#8B5CF6'; // purple
      case 'business': return '#EC4899'; // pink
      case 'subsidiary': return '#6366F1'; // indigo
      default: return '#6B7280'; // gray
    }
  };
  
  // Get icon based on stakeholder category
  const getCategoryIcon = (category) => {
    switch(category) {
      case 'client': return <Users size={16} />;
      case 'partner': return <Building size={16} />;
      case 'provider': return <Shield size={16} />;
      case 'technical': return <AlertCircle size={16} />;
      case 'business': return <Building size={16} />;
      case 'subsidiary': return <Building size={16} />;
      default: return <Users size={16} />;
    }
  };
  
  // Handle threshold change
  const handleThresholdChange = (e) => {
    const { name, value } = e.target;
    setEditedThresholds(prev => ({
      ...prev,
      [name]: parseFloat(value)
    }));
  };
  
  // Save threshold changes
  const handleSaveThresholds = () => {
    onUpdateThresholds(editedThresholds);
    setIsThresholdEditing(false);
  };
  
  // Cancel threshold editing
  const handleCancelThresholds = () => {
    setEditedThresholds({ ...thresholds });
    setIsThresholdEditing(false);
  };
  
  // Handle stakeholder click
  const handleStakeholderClick = (stakeholder) => {
    setSelectedStakeholder(stakeholder);
    if (onSelectStakeholder) {
      onSelectStakeholder(stakeholder);
    }
  };
  
  // Render loading state
  if (isLoading) {
    return (
      <div className="p-8 text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto"></div>
        <p className="mt-4 text-gray-600">Chargement de la visualisation...</p>
      </div>
    );
  }
  
  // Render empty state
  if (stakeholders.length === 0) {
    return (
      <div className="p-8 text-center">
        <div className="bg-blue-50 p-4 rounded-full inline-flex items-center justify-center mb-4">
          <Users size={32} className="text-blue-500" />
        </div>
        <h3 className="text-lg font-medium text-gray-900 mb-2">Aucune partie prenante</h3>
        <p className="text-gray-600 mb-4">
          Ajoutez des parties prenantes pour visualiser la cartographie de l'écosystème.
        </p>
      </div>
    );
  }
  
  return (
    <div className="relative p-4">
      {/* Threshold settings button */}
      <div className="absolute top-4 right-4 z-10">
        <button
          onClick={() => setIsThresholdEditing(!isThresholdEditing)}
          className="p-2 bg-white border border-gray-200 rounded-md shadow-sm hover:bg-gray-50"
          title="Modifier les seuils"
        >
          <Settings size={20} className="text-gray-600" />
        </button>
      </div>
      
      {/* Threshold settings panel */}
      {isThresholdEditing && (
        <div className="absolute top-14 right-4 bg-white p-4 rounded-lg shadow-md border border-gray-200 z-20 w-64">
          <h3 className="text-sm font-medium text-gray-700 mb-3">Modifier les seuils</h3>
          
          <div className="space-y-3">
            <div>
              <label className="block text-xs text-gray-500 mb-1">Seuil de danger</label>
              <input
                type="number"
                name="danger"
                value={editedThresholds.danger}
                onChange={handleThresholdChange}
                step="0.1"
                min="0"
                className="w-full p-1.5 text-sm border border-gray-300 rounded"
              />
            </div>
            
            <div>
              <label className="block text-xs text-gray-500 mb-1">Seuil de contrôle</label>
              <input
                type="number"
                name="control"
                value={editedThresholds.control}
                onChange={handleThresholdChange}
                step="0.1"
                min="0"
                className="w-full p-1.5 text-sm border border-gray-300 rounded"
              />
            </div>
            
            <div>
              <label className="block text-xs text-gray-500 mb-1">Seuil de veille</label>
              <input
                type="number"
                name="watch"
                value={editedThresholds.watch}
                onChange={handleThresholdChange}
                step="0.1"
                min="0"
                className="w-full p-1.5 text-sm border border-gray-300 rounded"
              />
            </div>
          </div>
          
          <div className="flex justify-end mt-4 space-x-2">
            <button
              onClick={handleCancelThresholds}
              className="px-3 py-1.5 text-xs border border-gray-300 rounded hover:bg-gray-50"
            >
              Annuler
            </button>
            <button
              onClick={handleSaveThresholds}
              className="px-3 py-1.5 text-xs bg-blue-600 text-white rounded hover:bg-blue-700"
            >
              Appliquer
            </button>
          </div>
        </div>
      )}
      
      {/* Radar visualization */}
      <div className="overflow-auto">
        <svg 
          ref={svgRef}
          width={width} 
          height={height} 
          viewBox={`0 0 ${width} ${height}`}
          className="mx-auto"
        >
          {/* Background circles for threat zones */}
          <circle 
            cx={centerX} 
            cy={centerY} 
            r={getRadius(thresholds.watch)} 
            fill="#E5F7EB" 
            stroke="#10B981" 
            strokeWidth="1" 
          />
          <circle 
            cx={centerX} 
            cy={centerY} 
            r={getRadius(thresholds.control)} 
            fill="#FEF3C7" 
            stroke="#F59E0B" 
            strokeWidth="1" 
          />
          <circle 
            cx={centerX} 
            cy={centerY} 
            r={getRadius(thresholds.danger)} 
            fill="#FEE2E2" 
            stroke="#EF4444" 
            strokeWidth="1" 
          />
          
          {/* Center point representing the object of study */}
          <circle 
            cx={centerX} 
            cy={centerY} 
            r={10} 
            fill="#1F2937" 
          />
          <text 
            x={centerX} 
            y={centerY + 25} 
            textAnchor="middle" 
            fill="#1F2937" 
            fontSize="12"
          >
            Objet de l'étude
          </text>
          
          {/* Zone labels */}
          <text 
            x={centerX} 
            y={centerY - getRadius(thresholds.danger) - 10} 
            textAnchor="middle" 
            fill="#EF4444" 
            fontSize="12" 
            fontWeight="bold"
          >
            Zone de danger
          </text>
          <text 
            x={centerX} 
            y={centerY - getRadius(thresholds.control) - 10} 
            textAnchor="middle" 
            fill="#F59E0B" 
            fontSize="12" 
            fontWeight="bold"
          >
            Zone de contrôle
          </text>
          <text 
            x={centerX} 
            y={centerY - getRadius(thresholds.watch) - 10} 
            textAnchor="middle" 
            fill="#10B981" 
            fontSize="12" 
            fontWeight="bold"
          >
            Zone de veille
          </text>
          
          {/* Stakeholders */}
          {stakeholders.map(stakeholder => {
            const { x, y } = getStakeholderPosition(stakeholder);
            const color = getCategoryColor(stakeholder.category);
            const isInternal = stakeholder.type === 'internal';
            const isHovered = hoveredStakeholder?.id === stakeholder.id;
            
            return (
              <g 
                key={stakeholder.id} 
                onClick={() => handleStakeholderClick(stakeholder)}
                onMouseEnter={() => setHoveredStakeholder(stakeholder)}
                onMouseLeave={() => setHoveredStakeholder(null)}
                style={{ cursor: 'pointer' }}
              >
                {/* Highlight circle for hover feedback */}
                {isHovered && (
                  <circle
                    cx={x}
                    cy={y}
                    r={18}
                    fill="rgba(59, 130, 246, 0.3)"
                    stroke="#3b82f6"
                    strokeWidth={1}
                    strokeDasharray="3,3"
                  />
                )}
                
                {/* Use different shapes for internal vs external */}
                {isInternal ? (
                  <rect 
                    x={x-8} 
                    y={y-8} 
                    width={16} 
                    height={16} 
                    fill={color} 
                    transform={`rotate(45, ${x}, ${y})`} 
                  />
                ) : (
                  <circle 
                    cx={x} 
                    cy={y} 
                    r={8} 
                    fill={color} 
                  />
                )}
                
                {/* Stakeholder label */}
                <text 
                  x={x} 
                  y={y + 20} 
                  textAnchor="middle" 
                  fill="#4B5563" 
                  fontSize="10"
                >
                  {stakeholder.name}
                </text>
              </g>
            );
          })}
        </svg>
      </div>
      
      {/* Hover tooltip */}
      {hoveredStakeholder && (
        <div 
          className="absolute bg-white p-3 rounded-lg shadow-lg border border-gray-200 z-30 w-64"
          style={{ 
            left: getStakeholderPosition(hoveredStakeholder).x + 20, 
            top: getStakeholderPosition(hoveredStakeholder).y - 30 
          }}
        >
          <div className="flex items-center mb-2">
            <div className="p-1.5 rounded-full mr-2" style={{ backgroundColor: getCategoryColor(hoveredStakeholder.category) }}>
              {getCategoryIcon(hoveredStakeholder.category)}
            </div>
            <div>
              <h3 className="font-medium text-gray-900">{hoveredStakeholder.name}</h3>
              <p className="text-xs text-gray-500">{hoveredStakeholder.type === 'internal' ? 'Interne' : 'Externe'}</p>
            </div>
          </div>
          
          <div className="grid grid-cols-2 gap-2 text-xs">
            <div>
              <span className="text-gray-500">Dépendance:</span>
              <span className="ml-1 font-medium">{hoveredStakeholder.dependency}</span>
            </div>
            <div>
              <span className="text-gray-500">Pénétration:</span>
              <span className="ml-1 font-medium">{hoveredStakeholder.penetration}</span>
            </div>
            <div>
              <span className="text-gray-500">Maturité:</span>
              <span className="ml-1 font-medium">{hoveredStakeholder.cyberMaturity}</span>
            </div>
            <div>
              <span className="text-gray-500">Confiance:</span>
              <span className="ml-1 font-medium">{hoveredStakeholder.trust}</span>
            </div>
          </div>
          
          <div className="mt-2 pt-2 border-t border-gray-100">
            <div className="flex justify-between items-center">
              <span className="text-xs text-gray-500">Niveau de menace:</span>
              <span className="font-bold text-sm">{hoveredStakeholder.threatLevel?.toFixed(2) || '0.00'}</span>
            </div>
          </div>
        </div>
      )}
      
      {/* Legend */}
      <div className="absolute bottom-4 left-4 bg-white p-4 rounded-lg shadow border border-gray-200">
        <h4 className="text-sm font-medium mb-2">Légende</h4>
        
        {/* Zone legend */}
        <div className="mb-3">
          <div className="flex items-center mb-1">
            <div className="w-4 h-4 bg-red-100 border border-red-500 mr-2"></div>
            <span className="text-xs">Zone de danger</span>
          </div>
          <div className="flex items-center mb-1">
            <div className="w-4 h-4 bg-amber-100 border border-amber-500 mr-2"></div>
            <span className="text-xs">Zone de contrôle</span>
          </div>
          <div className="flex items-center">
            <div className="w-4 h-4 bg-green-100 border border-green-500 mr-2"></div>
            <span className="text-xs">Zone de veille</span>
          </div>
        </div>
        
        {/* Category legend */}
        <div className="mb-3">
          <div className="text-xs font-medium mb-1">Catégories</div>
          {['client', 'partner', 'provider', 'technical', 'business', 'subsidiary'].map(category => (
            <div key={category} className="flex items-center mb-1">
              <div className="w-4 h-4 rounded-full mr-2" style={{backgroundColor: getCategoryColor(category)}}></div>
              <span className="text-xs">{category === 'client' ? 'Client' : 
                                         category === 'partner' ? 'Partenaire' : 
                                         category === 'provider' ? 'Prestataire' : 
                                         category === 'technical' ? 'Service technique' : 
                                         category === 'business' ? 'Service métier' : 
                                         'Filiale'}</span>
            </div>
          ))}
        </div>
        
        {/* Type legend */}
        <div>
          <div className="text-xs font-medium mb-1">Types</div>
          <div className="flex items-center mb-1">
            <div className="w-4 h-4 bg-gray-500 rounded-full mr-2"></div>
            <span className="text-xs">Externe</span>
          </div>
          <div className="flex items-center">
            <div className="w-4 h-4 bg-gray-500 mr-2" style={{transform: 'rotate(45deg)'}}></div>
            <span className="text-xs">Interne</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ThreatRadarVisualization;
