// src/components/dashboard/DashboardCharts.jsx
import React from 'react';
import { 
  BarChart, Bar, 
  LineChart, Line, 
  PieChart, Pie, Cell,
  XAxis, YAxis, CartesianGrid, 
  <PERSON>lt<PERSON>, Legend, ResponsiveContainer 
} from 'recharts';

/**
 * Activity Chart component displaying activity metrics
 * 
 * @param {Object} props Component props
 * @param {Array} props.data Activity data
 * @returns {JSX.Element} ActivityChart component
 */
export const ActivityChart = ({ data = [] }) => {
  return (
    <div className="bg-white p-4 rounded-lg shadow-md">
      <h3 className="text-lg font-semibold mb-4">Activités par période</h3>
      <div className="h-80">
        <ResponsiveContainer width="100%" height="100%">
          <LineChart
            data={data}
            margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
          >
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="name" />
            <YAxis />
            <Tooltip />
            <Legend />
            <Line type="monotone" dataKey="login" stroke="#3B82F6" name="Connexions" />
            <Line type="monotone" dataKey="create" stroke="#10B981" name="Créations" />
            <Line type="monotone" dataKey="update" stroke="#F59E0B" name="Modifications" />
          </LineChart>
        </ResponsiveContainer>
      </div>
    </div>
  );
};

/**
 * User Distribution Chart component
 * 
 * @param {Object} props Component props
 * @param {Array} props.data User distribution data
 * @returns {JSX.Element} UserDistributionChart component
 */
export const UserDistributionChart = ({ data = [] }) => {
  const COLORS = ['#3B82F6', '#10B981', '#F59E0B', '#EF4444'];
  
  return (
    <div className="bg-white p-4 rounded-lg shadow-md">
      <h3 className="text-lg font-semibold mb-4">Répartition des utilisateurs</h3>
      <div className="h-72">
        <ResponsiveContainer width="100%" height="100%">
          <PieChart>
            <Pie
              data={data}
              cx="50%"
              cy="50%"
              labelLine={false}
              outerRadius={80}
              fill="#8884d8"
              dataKey="value"
              label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
            >
              {data.map((entry, index) => (
                <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
              ))}
            </Pie>
            <Tooltip formatter={(value) => [`${value} utilisateurs`, 'Nombre']} />
            <Legend />
          </PieChart>
        </ResponsiveContainer>
      </div>
    </div>
  );
};

/**
 * Company Status Chart component
 * 
 * @param {Object} props Component props
 * @param {Array} props.data Company status data
 * @returns {JSX.Element} CompanyStatusChart component
 */
export const CompanyStatusChart = ({ data = [] }) => {
  return (
    <div className="bg-white p-4 rounded-lg shadow-md">
      <h3 className="text-lg font-semibold mb-4">Statut des entreprises</h3>
      <div className="h-72">
        <ResponsiveContainer width="100%" height="100%">
          <BarChart
            data={data}
            margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
          >
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="name" />
            <YAxis />
            <Tooltip />
            <Legend />
            <Bar dataKey="count" fill="#3B82F6" name="Nombre d'entreprises" />
          </BarChart>
        </ResponsiveContainer>
      </div>
    </div>
  );
};

/**
 * Metrics Card component
 * 
 * @param {Object} props Component props
 * @param {string} props.title Card title
 * @param {number} props.value Metric value
 * @param {string} props.icon Card icon (SVG path)
 * @param {string} props.color Card color ('blue', 'green', 'red', 'yellow', 'purple')
 * @param {string} props.linkText Link text
 * @param {string} props.linkUrl Link URL
 * @returns {JSX.Element} MetricsCard component
 */
export const MetricsCard = ({ 
  title, 
  value, 
  icon, 
  color = 'blue',
  linkText,
  linkUrl
}) => {
  const colorClasses = {
    blue: {
      border: 'border-blue-500',
      bg: 'bg-blue-100',
      text: 'text-blue-600',
      hover: 'hover:text-blue-700'
    },
    green: {
      border: 'border-green-500',
      bg: 'bg-green-100',
      text: 'text-green-600',
      hover: 'hover:text-green-700'
    },
    red: {
      border: 'border-red-500',
      bg: 'bg-red-100',
      text: 'text-red-600',
      hover: 'hover:text-red-700'
    },
    yellow: {
      border: 'border-yellow-500',
      bg: 'bg-yellow-100',
      text: 'text-yellow-600',
      hover: 'hover:text-yellow-700'
    },
    purple: {
      border: 'border-purple-500',
      bg: 'bg-purple-100',
      text: 'text-purple-600',
      hover: 'hover:text-purple-700'
    }
  };
  
  const classes = colorClasses[color] || colorClasses.blue;
  
  return (
    <div className={`bg-white p-6 rounded-lg shadow-md border-l-4 ${classes.border} hover:shadow-lg transition-shadow`}>
      <div className="flex justify-between items-start">
        <div>
          <h2 className="text-lg font-semibold text-gray-700">{title}</h2>
          <p className={`text-3xl font-bold ${classes.text} mt-2`}>
            {typeof value === 'number' ? value.toLocaleString() : value}
          </p>
        </div>
        <div className={`rounded-full ${classes.bg} p-3`}>
          <svg 
            xmlns="http://www.w3.org/2000/svg" 
            className={`h-6 w-6 ${classes.text}`} 
            fill="none" 
            viewBox="0 0 24 24" 
            stroke="currentColor"
          >
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d={icon} />
          </svg>
        </div>
      </div>
      {linkText && linkUrl && (
        <a 
          href={linkUrl} 
          className={`${classes.text} hover:underline mt-4 inline-block ${classes.hover}`}
        >
          {linkText} →
        </a>
      )}
    </div>
  );
};