// src/components/Atelier 2/Activite1/ObjectifsVises.js
import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Save, Plus, RefreshCw, Target, Trash2, Edit, X, Check, Search, Filter, Download } from 'lucide-react';
import { useAnalysis } from '../../../context/AnalysisContext';
import { showSuccessToast, showErrorToast, showLoadingToast, updateToast } from '../../../utils/toastUtils';

const ObjectifsVises = () => {
  // State for objectifs visés
  const [objectifs, setObjectifs] = useState([]);
  const [newObjectif, setNewObjectif] = useState({
    name: '',
    description: '',
    type: 'confidentialite', // confidentialite, integrite, disponibilite, auditabilite, etc.
    impact: 'faible', // faible, moyen, fort
    businessValueId: '', // ID of related business value
  });
  const [editingObjectif, setEditingObjectif] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState('');
  const [isSaving, setIsSaving] = useState(false);

  // Get analysis context
  const { 
    currentAnalysis,
    saveObjectifsVises,
    loadObjectifsVises,
    currentBusinessValues,
  } = useAnalysis();

  // Load objectifs visés on component mount
  useEffect(() => {
    const fetchData = async () => {
      if (currentAnalysis?.id) {
        try {
          const data = await loadObjectifsVises(currentAnalysis.id);
          if (data) {
            setObjectifs(data);
          }
        } catch (error) {
          console.error('Error loading objectifs visés:', error);
          showErrorToast('Erreur lors du chargement des objectifs visés');
        }
      }
    };

    fetchData();
  }, [currentAnalysis?.id, loadObjectifsVises]);

  // Handle adding a new objectif visé
  const handleAddObjectif = () => {
    if (!newObjectif.name.trim()) {
      showErrorToast("Le nom de l'objectif visé est requis");
      return;
    }

    const objectifToAdd = {
      ...newObjectif,
      id: Date.now().toString(), // Temporary ID until saved to backend
      createdAt: new Date().toISOString(),
    };

    setObjectifs([...objectifs, objectifToAdd]);
    setNewObjectif({
      name: '',
      description: '',
      type: 'confidentialite',
      impact: 'faible',
      businessValueId: '',
    });

    showSuccessToast('Objectif visé ajouté');
  };

  // Handle editing an objectif visé
  const handleEditObjectif = (objectif) => {
    setEditingObjectif({
      ...objectif,
    });
  };

  // Handle saving edited objectif visé
  const handleSaveEdit = () => {
    if (!editingObjectif.name.trim()) {
      showErrorToast("Le nom de l'objectif visé est requis");
      return;
    }

    const updatedObjectifs = objectifs.map(objectif => 
      objectif.id === editingObjectif.id ? editingObjectif : objectif
    );

    setObjectifs(updatedObjectifs);
    setEditingObjectif(null);
    showSuccessToast('Objectif visé mis à jour');
  };

  // Handle canceling edit
  const handleCancelEdit = () => {
    setEditingObjectif(null);
  };

  // Handle deleting an objectif visé
  const handleDeleteObjectif = (id) => {
    if (window.confirm('Êtes-vous sûr de vouloir supprimer cet objectif visé ?')) {
      const updatedObjectifs = objectifs.filter(objectif => objectif.id !== id);
      setObjectifs(updatedObjectifs);
      showSuccessToast('Objectif visé supprimé');
    }
  };

  // Handle saving all objectifs visés
  const handleSaveAll = async () => {
    if (!currentAnalysis?.id) {
      showErrorToast('Aucune analyse sélectionnée');
      return;
    }

    setIsSaving(true);
    const toastId = showLoadingToast('Sauvegarde des objectifs visés...');

    try {
      await saveObjectifsVises(currentAnalysis.id, objectifs);
      updateToast(toastId, 'Objectifs visés sauvegardés avec succès', 'success');
    } catch (error) {
      console.error('Error saving objectifs visés:', error);
      updateToast(toastId, 'Erreur lors de la sauvegarde des objectifs visés', 'error');
    } finally {
      setIsSaving(false);
    }
  };

  // Filter objectifs based on search term and type filter
  const filteredObjectifs = objectifs.filter(objectif => {
    const matchesSearch = objectif.name.toLowerCase().includes(searchTerm.toLowerCase()) || 
                         objectif.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesType = filterType ? objectif.type === filterType : true;
    return matchesSearch && matchesType;
  });

  // Get color for impact
  const getImpactColor = (level) => {
    switch (level) {
      case 'faible': return 'bg-green-100 text-green-800';
      case 'moyen': return 'bg-yellow-100 text-yellow-800';
      case 'fort': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  // Get type badge color
  const getTypeBadgeColor = (type) => {
    switch (type) {
      case 'confidentialite': return 'bg-blue-100 text-blue-800';
      case 'integrite': return 'bg-purple-100 text-purple-800';
      case 'disponibilite': return 'bg-orange-100 text-orange-800';
      case 'auditabilite': return 'bg-red-100 text-red-800';
      case 'preuve': return 'bg-indigo-100 text-indigo-800';
      case 'controle': return 'bg-teal-100 text-teal-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  // Get business value name by ID
  const getBusinessValueName = (id) => {
    if (!id) return 'Non associé';
    const businessValue = currentBusinessValues?.find(bv => bv.id === id);
    return businessValue ? businessValue.name : `ID: ${id}`;
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="bg-white p-6 rounded-lg shadow-sm border border-gray-200"
    >
      {/* Header */}
      <div className="flex justify-between items-center mb-6">
        <div>
          <h2 className="text-2xl font-bold text-gray-800 flex items-center">
            <Target size={24} className="mr-2 text-blue-600" />
            Objectifs Visés
          </h2>
          <p className="text-sm text-gray-500 mt-1">
            Identifiez les objectifs visés par les sources de risque
          </p>
        </div>
        <button
          onClick={handleSaveAll}
          disabled={isSaving}
          className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 flex items-center disabled:opacity-50"
        >
          {isSaving ? (
            <RefreshCw size={16} className="mr-2 animate-spin" />
          ) : (
            <Save size={16} className="mr-2" />
          )}
          Enregistrer
        </button>
      </div>

      {/* Add new objectif form */}
      <div className="bg-gray-50 p-4 rounded-lg border border-gray-200 mb-6">
        <h3 className="text-lg font-semibold text-gray-700 mb-3">Ajouter un objectif visé</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Nom</label>
            <input
              type="text"
              value={newObjectif.name}
              onChange={(e) => setNewObjectif({ ...newObjectif, name: e.target.value })}
              className="w-full p-2 border border-gray-300 rounded-md"
              placeholder="Nom de l'objectif visé"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Type</label>
            <select
              value={newObjectif.type}
              onChange={(e) => setNewObjectif({ ...newObjectif, type: e.target.value })}
              className="w-full p-2 border border-gray-300 rounded-md"
            >
              <option value="confidentialite">Confidentialité</option>
              <option value="integrite">Intégrité</option>
              <option value="disponibilite">Disponibilité</option>
              <option value="auditabilite">Auditabilité</option>
              <option value="preuve">Preuve</option>
              <option value="controle">Contrôle</option>
            </select>
          </div>
        </div>
        <div className="mb-4">
          <label className="block text-sm font-medium text-gray-700 mb-1">Description</label>
          <textarea
            value={newObjectif.description}
            onChange={(e) => setNewObjectif({ ...newObjectif, description: e.target.value })}
            className="w-full p-2 border border-gray-300 rounded-md"
            rows="2"
            placeholder="Description de l'objectif visé"
          ></textarea>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Impact</label>
            <select
              value={newObjectif.impact}
              onChange={(e) => setNewObjectif({ ...newObjectif, impact: e.target.value })}
              className="w-full p-2 border border-gray-300 rounded-md"
            >
              <option value="faible">Faible</option>
              <option value="moyen">Moyen</option>
              <option value="fort">Fort</option>
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Valeur métier associée</label>
            <select
              value={newObjectif.businessValueId}
              onChange={(e) => setNewObjectif({ ...newObjectif, businessValueId: e.target.value })}
              className="w-full p-2 border border-gray-300 rounded-md"
            >
              <option value="">Aucune</option>
              {currentBusinessValues?.map(bv => (
                <option key={bv.id} value={bv.id}>{bv.name}</option>
              ))}
            </select>
          </div>
        </div>
        <div className="flex justify-end">
          <button
            onClick={handleAddObjectif}
            className="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 flex items-center"
          >
            <Plus size={16} className="mr-2" />
            Ajouter
          </button>
        </div>
      </div>

      {/* Search and filter */}
      <div className="flex flex-col md:flex-row gap-4 mb-6">
        <div className="flex-1">
          <div className="relative">
            <input
              type="text"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full p-2 pl-10 border border-gray-300 rounded-md"
              placeholder="Rechercher un objectif visé..."
            />
            <Search size={18} className="absolute left-3 top-2.5 text-gray-400" />
          </div>
        </div>
        <div className="w-full md:w-64">
          <select
            value={filterType}
            onChange={(e) => setFilterType(e.target.value)}
            className="w-full p-2 border border-gray-300 rounded-md"
          >
            <option value="">Tous les types</option>
            <option value="confidentialite">Confidentialité</option>
            <option value="integrite">Intégrité</option>
            <option value="disponibilite">Disponibilité</option>
            <option value="auditabilite">Auditabilité</option>
            <option value="preuve">Preuve</option>
            <option value="controle">Contrôle</option>
          </select>
        </div>
      </div>

      {/* Objectifs list */}
      {filteredObjectifs.length === 0 ? (
        <div className="text-center py-8 bg-gray-50 rounded-lg border border-gray-200">
          <Target size={48} className="mx-auto text-gray-400 mb-4" />
          <p className="text-gray-600 font-medium">Aucun objectif visé trouvé</p>
          <p className="text-gray-500 text-sm mt-1">
            {objectifs.length > 0 
              ? 'Essayez de modifier vos critères de recherche ou de filtre' 
              : 'Commencez par ajouter un objectif visé en utilisant le formulaire ci-dessus'}
          </p>
        </div>
      ) : (
        <div className="grid grid-cols-1 gap-4">
          {filteredObjectifs.map((objectif) => (
            <div 
              key={objectif.id} 
              className="bg-white p-4 rounded-lg border border-gray-200 shadow-sm hover:shadow-md transition-shadow"
            >
              {editingObjectif && editingObjectif.id === objectif.id ? (
                // Edit mode
                <div>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Nom</label>
                      <input
                        type="text"
                        value={editingObjectif.name}
                        onChange={(e) => setEditingObjectif({ ...editingObjectif, name: e.target.value })}
                        className="w-full p-2 border border-gray-300 rounded-md"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Type</label>
                      <select
                        value={editingObjectif.type}
                        onChange={(e) => setEditingObjectif({ ...editingObjectif, type: e.target.value })}
                        className="w-full p-2 border border-gray-300 rounded-md"
                      >
                        <option value="confidentialite">Confidentialité</option>
                        <option value="integrite">Intégrité</option>
                        <option value="disponibilite">Disponibilité</option>
                        <option value="auditabilite">Auditabilité</option>
                        <option value="preuve">Preuve</option>
                        <option value="controle">Contrôle</option>
                      </select>
                    </div>
                  </div>
                  <div className="mb-4">
                    <label className="block text-sm font-medium text-gray-700 mb-1">Description</label>
                    <textarea
                      value={editingObjectif.description}
                      onChange={(e) => setEditingObjectif({ ...editingObjectif, description: e.target.value })}
                      className="w-full p-2 border border-gray-300 rounded-md"
                      rows="2"
                    ></textarea>
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Impact</label>
                      <select
                        value={editingObjectif.impact}
                        onChange={(e) => setEditingObjectif({ ...editingObjectif, impact: e.target.value })}
                        className="w-full p-2 border border-gray-300 rounded-md"
                      >
                        <option value="faible">Faible</option>
                        <option value="moyen">Moyen</option>
                        <option value="fort">Fort</option>
                      </select>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Valeur métier associée</label>
                      <select
                        value={editingObjectif.businessValueId}
                        onChange={(e) => setEditingObjectif({ ...editingObjectif, businessValueId: e.target.value })}
                        className="w-full p-2 border border-gray-300 rounded-md"
                      >
                        <option value="">Aucune</option>
                        {currentBusinessValues?.map(bv => (
                          <option key={bv.id} value={bv.id}>{bv.name}</option>
                        ))}
                      </select>
                    </div>
                  </div>
                  <div className="flex justify-end space-x-2">
                    <button
                      onClick={handleCancelEdit}
                      className="bg-gray-200 text-gray-800 px-4 py-2 rounded-md hover:bg-gray-300 flex items-center"
                    >
                      <X size={16} className="mr-2" />
                      Annuler
                    </button>
                    <button
                      onClick={handleSaveEdit}
                      className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 flex items-center"
                    >
                      <Check size={16} className="mr-2" />
                      Enregistrer
                    </button>
                  </div>
                </div>
              ) : (
                // View mode
                <div>
                  <div className="flex justify-between items-start">
                    <div>
                      <div className="flex items-center">
                        <h3 className="text-lg font-semibold text-gray-800">{objectif.name}</h3>
                        <span className={`ml-2 px-2 py-0.5 rounded-full text-xs ${getTypeBadgeColor(objectif.type)}`}>
                          {objectif.type.charAt(0).toUpperCase() + objectif.type.slice(1)}
                        </span>
                      </div>
                      <p className="text-gray-600 mt-1">{objectif.description}</p>
                    </div>
                    <div className="flex space-x-1">
                      <button
                        onClick={() => handleEditObjectif(objectif)}
                        className="p-1.5 text-blue-600 hover:bg-blue-50 rounded-full"
                        title="Modifier"
                      >
                        <Edit size={16} />
                      </button>
                      <button
                        onClick={() => handleDeleteObjectif(objectif.id)}
                        className="p-1.5 text-red-600 hover:bg-red-50 rounded-full"
                        title="Supprimer"
                      >
                        <Trash2 size={16} />
                      </button>
                    </div>
                  </div>
                  <div className="mt-4 grid grid-cols-2 gap-4">
                    <div>
                      <p className="text-xs text-gray-500">Impact</p>
                      <span className={`inline-block px-2 py-0.5 rounded-full text-xs ${getImpactColor(objectif.impact)}`}>
                        {objectif.impact.charAt(0).toUpperCase() + objectif.impact.slice(1)}
                      </span>
                    </div>
                    <div>
                      <p className="text-xs text-gray-500">Valeur métier associée</p>
                      <p className="text-sm font-medium">{getBusinessValueName(objectif.businessValueId)}</p>
                    </div>
                  </div>
                </div>
              )}
            </div>
          ))}
        </div>
      )}
    </motion.div>
  );
};

export default ObjectifsVises;
