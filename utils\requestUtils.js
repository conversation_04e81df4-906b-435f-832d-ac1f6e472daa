// utils/requestUtils.js

/**
 * Get the IP address from the request
 * @param {Object} req - Express request object
 * @returns {String} - IP address
 */
const getIpAddress = (req) => {
  return req.ip || 
         req.headers['x-forwarded-for'] || 
         req.connection.remoteAddress || 
         req.socket.remoteAddress || 
         req.connection.socket.remoteAddress || 
         'unknown';
};

module.exports = {
  getIpAddress
};
