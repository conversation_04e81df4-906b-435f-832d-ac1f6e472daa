// src/components/Atelier 2/common/SliderSelector.js
import React, { useState, useEffect, useRef } from 'react';
import { motion } from 'framer-motion';

const SliderSelector = ({
  options,
  value,
  onChange,
  label,
  colorMap = {},
  disabled = false
}) => {
  const [activeIndex, setActiveIndex] = useState(0);
  const trackRef = useRef(null);

  // Find the index of the current value in options
  useEffect(() => {
    const index = options.findIndex(option => option.value === value);
    if (index !== -1 && index !== activeIndex) {
      setActiveIndex(index);
    }
  }, [value, options, activeIndex]);

  // Handle click on an option
  const handleOptionClick = (option, index) => {
    if (disabled) return;
    setActiveIndex(index);
    onChange(option.value);
  };

  // Get color for the button based on the value
  const getButtonColor = (optionValue) => {
    return colorMap[optionValue] || 'bg-blue-500';
  };

  // Get the currently selected option
  const selectedOption = options.find(option => option.value === value) || options[0];

  return (
    <div className="space-y-2">
      {label && (
        <div className="flex justify-between items-center">
          <label className="block text-sm font-medium text-gray-700">{label}</label>
          <div className={`text-sm font-medium px-2 py-0.5 rounded-full ${colorMap[value]?.replace('bg-', 'text-')} ${colorMap[value]?.replace('bg-', 'bg-').replace('-600', '-100')}`}>
            {selectedOption.label}
          </div>
        </div>
      )}

      <div
        ref={trackRef}
        className={`relative h-12 bg-gray-100 rounded-full p-1 ${disabled ? 'opacity-60 cursor-not-allowed' : ''}`}
      >
        {/* Track */}
        <div className="flex justify-between items-center h-full relative">
          {options.map((option, index) => (
            <button
              key={option.value}
              onClick={() => handleOptionClick(option, index)}
              className={`relative z-10 flex-1 h-full rounded-full flex items-center justify-center text-sm font-medium transition-colors ${
                activeIndex === index ? 'text-white' : 'text-gray-700 hover:text-gray-900'
              }`}
              disabled={disabled}
            >
              {option.label}
            </button>
          ))}

          {/* Sliding indicator */}
          <motion.div
            className={`absolute top-0 bottom-0 rounded-full ${getButtonColor(options[activeIndex]?.value)}`}
            initial={false}
            animate={{
              left: `${(activeIndex / options.length) * 100}%`,
              width: `${100 / options.length}%`
            }}
            transition={{ type: "spring", stiffness: 300, damping: 30 }}
          />
        </div>
      </div>
    </div>
  );
};

export default SliderSelector;
