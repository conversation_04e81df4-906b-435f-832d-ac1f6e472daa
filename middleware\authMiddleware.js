// backend/middleware/authMiddleware.js
const jwt = require('jsonwebtoken');
const User = require('../models/User');

/**
 * Middleware to protect routes - Verify JWT token
 */
exports.protect = async (req, res, next) => {
  try {
    let token;
    
    // Get token from Authorization header
    if (req.headers.authorization && req.headers.authorization.startsWith('Bearer')) {
      token = req.headers.authorization.split(' ')[1];
    }
    
    // Check if token exists
    if (!token) {
      return res.status(401).json({
        success: false,
        message: 'Accès non autorisé. Token manquant ou invalide'
      });
    }
    
    try {
      // Verify token
      const decoded = jwt.verify(token, process.env.JWT_SECRET || 'your_jwt_secret_key');
      
      // Check if it's an access token
      if (decoded.tokenType !== 'access') {
        return res.status(401).json({
          success: false,
          message: 'Type de token incorrect'
        });
      }
      
      // Get user from token
      const user = await User.findById(decoded.userId).select('-password');
      
      if (!user) {
        return res.status(401).json({
          success: false,
          message: 'Utilisateur non trouvé'
        });
      }
      
      // Check if user is active
      if (user.status !== 'active') {
        return res.status(401).json({
          success: false,
          message: 'Utilisateur inactif'
        });
      }
      
      // Add user to request
      req.user = user;
      next();
    } catch (error) {
      console.error('Token verification error:', error);
      
      if (error.name === 'TokenExpiredError') {
        return res.status(401).json({
          success: false,
          message: 'Token expiré'
        });
      }
      
      return res.status(401).json({
        success: false,
        message: 'Token invalide'
      });
    }
  } catch (error) {
    console.error('Auth middleware error:', error);
    return res.status(500).json({
      success: false,
      message: 'Erreur d\'authentification'
    });
  }
};

/**
 * Middleware to restrict routes to specific roles
 * @param {Array|String} roles - Allowed roles
 */
exports.restrictTo = (...roles) => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: 'Utilisateur non authentifié'
      });
    }
    
    // Check if user role is allowed
    if (!roles.includes(req.user.role)) {
      return res.status(403).json({
        success: false,
        message: 'Permission refusée. Rôle insuffisant'
      });
    }
    
    next();
  };
};

/**
 * Middleware to restrict access to own company resources
 */
exports.restrictToOwnCompany = (req, res, next) => {
  if (!req.user) {
    return res.status(401).json({
      success: false,
      message: 'Utilisateur non authentifié'
    });
  }
  
  // SuperAdmin can access all
  if (req.user.role === 'superadmin') {
    return next();
  }
  
  // Check if company ID in params matches user's company
  const companyId = req.params.companyId;
  
  if (!companyId || !req.user.companyId || req.user.companyId.toString() !== companyId) {
    return res.status(403).json({
      success: false,
      message: 'Permission refusée. Vous n\'avez pas accès à cette entreprise'
    });
  }
  
  next();
};

/**
 * Middleware to restrict access to own resources or as admin
 * @param {Function} getResourceOwnerId - Function to extract the owner ID from the request
 */
exports.restrictToOwnerOrAdmin = (getResourceOwnerId) => {
  return async (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: 'Utilisateur non authentifié'
      });
    }
    
    // SuperAdmin and Admin can access all
    if (['superadmin', 'admin'].includes(req.user.role)) {
      return next();
    }
    
    // For other users, check if they are the owner
    const ownerId = getResourceOwnerId(req);
    
    if (!ownerId || req.user.id !== ownerId.toString()) {
      return res.status(403).json({
        success: false,
        message: 'Permission refusée. Vous n\'êtes pas propriétaire de cette ressource'
      });
    }
    
    next();
  };
};