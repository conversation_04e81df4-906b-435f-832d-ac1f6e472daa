// src/utils/appStateManagerAPI.js
import contextService from '../services/contextService';
import businessValueService from '../services/businessValuesService';
import dreadedEventService from '../services/dreadedEventsService';
import securityFrameworkService from '../services/securityFrameworkService';
import securityControlsService from '../services/securityControlsService';
import riskTreatmentService from '../services/riskTreatmentService';

// Import the original functions at the top level to ensure they are available
import {
  handleSaveData,
  loadContextData,
  loadBusinessValuesData,
  loadDreadedEventsData,
  convertContextToRACIFormat,
  loadSecurityFrameworkData,
  loadDashboardPreferences
} from './appStateManager';

/**
 * Enhanced app state manager with API integration
 * Extends the functionality of appStateManager.js with API calls
 */

// Track pending requests to avoid duplicates
let pendingRequests = {};

// Current analysis ID - should be set when analysis is loaded or selected
let currentAnalysisId = null;

/**
 * Set the current analysis ID for API operations
 * @param {string} analysisId - The analysis ID to use for API calls
 */
export const setCurrentAnalysisId = (analysisId) => {
  if (currentAnalysisId !== analysisId) {
    console.log(`Setting current analysis ID: ${analysisId}`);
    currentAnalysisId = analysisId;
    localStorage.setItem('currentAnalysisId', analysisId);

    // Clear any pending requests for the previous analysis
    pendingRequests = {};
  }
};

/**
 * Get the current analysis ID
 * @returns {string|null} The current analysis ID or null if not set
 */
export const getCurrentAnalysisId = () => {
  if (!currentAnalysisId) {
    // Try to get from local storage if not in memory
    currentAnalysisId = localStorage.getItem('currentAnalysisId');
  }
  return currentAnalysisId;
};

/**
 * Save data to both API and local storage
 * @param {string} activeTab - The active tab/component
 * @param {Object} data - The data to save
 * @returns {Promise<Object>} The result of the save operation
 */
export const handleSaveDataWithAPI = async (activeTab, data) => {
  const analysisId = getCurrentAnalysisId();

  // If no analysis ID is set, fall back to local-only storage
  if (!analysisId) {
    console.warn('No analysis ID set, saving to local storage only');
    // Use the original handleSaveData function from appStateManager
    return handleSaveData(activeTab, data);
  }

  // Otherwise, save to API based on the active tab
  try {
    switch(activeTab) {
      case 'context':
        const contextData = {
          organizationName: data.organizationName,
          missions: data.missions,
          scope: data.scope,
          analysisDate: data.analysisDate,
          participants: data.participants.map(p => {
            const workshopRoles = {};
            if (data.WORKSHOPS) {
              data.WORKSHOPS.forEach(workshop => {
                workshopRoles[workshop.name] = data.matrix[p.id]?.[workshop.id] || "";
              });
            }
            return {
              id: p.id,
              name: p.name,
              position: p.position,
              customPosition: p.customPosition || "",
              workshopRoles
            };
          })
        };
        return await contextService.saveContext(analysisId, contextData);

      case 'business-values':
        return await businessValueService.saveBusinessValues(analysisId, data.businessValues);

      case 'events':
        return await dreadedEventService.saveDreadedEvents(analysisId, data.dreadedEvents);

      case 'security':
        // Handle saving security framework data
        const securityData = {
          frameworks: data.frameworks,
          selectedRules: data.selectedRules,
          ruleBusinessValues: data.ruleBusinessValues || {},
          summary: data.summary || [] // Make sure summary is included
        };
        return await securityFrameworkService.saveSecurityFramework(analysisId, securityData);

      case 'security-controls':
        // Handle saving security controls data
        return await securityControlsService.saveSecurityControls(analysisId, data.securityControls);

      case 'risk-treatment':
        // Handle saving risk treatment data
        return await riskTreatmentService.saveRiskTreatment(analysisId, data.riskTreatments);

      default:
        // Removed sensitive data logging for security
        return handleSaveData(activeTab, data);
    }
  } catch (error) {
    // Log without revealing sensitive data
    console.error(`Error saving ${activeTab} to API`);
    // Fall back to local storage in case of API error
    // Removed sensitive data logging for security
    return handleSaveData(activeTab, data);
  }
};

/**
 * Load data from API with local storage fallback and deduplication
 * @param {string} activeTab - The active tab/component
 * @returns {Promise<Object>} The requested data
 */
export const loadDataFromAPI = async (activeTab) => {
  const analysisId = getCurrentAnalysisId();

  // Create a unique request key
  const requestKey = `${analysisId}-${activeTab}`;

  // If this exact request is already in progress, return that promise
  if (pendingRequests[requestKey]) {
    // Removed sensitive data logging for security
    return pendingRequests[requestKey];
  }

  // If no analysis ID is set, fall back to local-only storage
  if (!analysisId) {
    // Removed sensitive data logging for security

    switch(activeTab) {
      case 'context':
        return loadContextData();
      case 'business-values':
        return loadBusinessValuesData();
      case 'events':
        return loadDreadedEventsData();
      case 'security':
        return loadSecurityFrameworkData().frameworks;
      case 'security-controls':
        return loadSecurityFrameworkData().securityControls;
      case 'risk-treatment':
        // Add logic to load risk treatment data from local storage if needed
        return [];
      default:
        // Removed sensitive data logging for security
        return null;
    }
  }

  // Create a new request promise and save it
  const requestPromise = (async () => {
    try {
      // Removed sensitive data logging for security

      // Otherwise, load from API based on the active tab
      switch(activeTab) {
        case 'context':
          const contextData = await contextService.getContext(analysisId);
          return contextService.formatContextData(contextData);

        case 'business-values':
          const businessValuesData = await businessValueService.getBusinessValues(analysisId);
          return businessValueService.formatBusinessValuesData(businessValuesData);

        case 'events':
          const dreadedEventsData = await dreadedEventService.getDreadedEvents(analysisId);
          return dreadedEventsData.data?.dreadedEvents || [];

        case 'security':
          const securityData = await securityFrameworkService.getSecurityFramework(analysisId);
          // Removed sensitive data logging for security
          return securityFrameworkService.formatSecurityFrameworkData(securityData);

        case 'security-controls':
          const controlsData = await securityControlsService.getSecurityControls(analysisId);
          return securityControlsService.formatSecurityControlsData(controlsData) || [];

        case 'risk-treatment':
          const riskData = await riskTreatmentService.getRiskTreatment(analysisId);
          return riskTreatmentService.formatRiskTreatmentData(riskData) || [];

        default:
          // Removed sensitive data logging for security
          return null;
      }
    } catch (error) {
      // Log without revealing sensitive data
      console.error(`Error loading data for ${activeTab} from API`);

      // Fallback to local storage
      // Removed sensitive data logging for security

      switch(activeTab) {
        case 'context':
          return loadContextData();
        case 'business-values':
          return loadBusinessValuesData();
        case 'events':
          return loadDreadedEventsData();
        case 'security':
          return loadSecurityFrameworkData().frameworks || [];
        case 'security-controls':
          return loadSecurityFrameworkData().securityControls || [];
        case 'risk-treatment':
          return [];
        default:
          // Removed sensitive data logging for security
          return null;
      }
    } finally {
      // Clean up the pending request reference when done
      delete pendingRequests[requestKey];
    }
  })();

  // Store the promise so we can reuse it for duplicate requests
  pendingRequests[requestKey] = requestPromise;

  return requestPromise;
};

// Since saveSecurityFrameworkData is not exported from appStateManager,
// we'll use handleSaveData for security framework data
const saveSecurityFrameworkData = (frameworks, selectedRules, securityControls) => {
  return handleSaveData('security', {
    frameworks,
    selectedRules,
    securityControls
  });
};

// Reset all pending requests (useful when changing analysis)
export const resetPendingRequests = () => {
  // Removed sensitive data logging for security
  pendingRequests = {};
};

// Export all functions from this file and the original appStateManager
export {
  handleSaveData,
  loadContextData,
  loadBusinessValuesData,
  loadDreadedEventsData,
  convertContextToRACIFormat,
  loadSecurityFrameworkData,
  loadDashboardPreferences,
  saveSecurityFrameworkData
};