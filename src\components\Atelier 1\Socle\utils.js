// Utility function to parse CSV content
export const parseCSV = (content) => {
    // Split by line and remove empty lines
    const lines = content.split(/\r\n|\n|\r/).filter(line => line.trim() !== '');
    
    // Parse each line
    return lines.map(line => {
      // If the line contains a comma, split by comma
      // Otherwise try with a semicolon (common format in French)
      const separator = line.includes(',') ? ',' : ';';
      const columns = line.split(separator).map(col => col.trim());
      
      // Return an object with the name and applicable rule
      return {
        name: columns[0] || '',
        rule: columns.length > 1 ? columns[1] : ''
      };
    });
  };