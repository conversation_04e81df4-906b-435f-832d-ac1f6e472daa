// src/components/Atelier3/Activite5/Activite5.jsx
import React, { useState, useEffect } from 'react';
import { TrendingUp, Info, AlertTriangle, Printer } from 'lucide-react';
import { useAnalysis } from '../../../context/AnalysisContext';
import { useTranslation } from 'react-i18next';
import EventProgressionTable from '../../EventProgression/EventProgressionTable';
import api from '../../../api/apiClient';
import { processEventProgressionData } from '../../EventProgression/dataProcessor';

const Activite5 = () => {
  const { t } = useTranslation();
  const {
    currentAnalysis,
    currentDreadedEvents,
    currentAnalysisControlPlan,
    isWorkshopDataLoading
  } = useAnalysis();

  const [ecosystemMeasures, setEcosystemMeasures] = useState([]);
  const [attackPaths, setAttackPaths] = useState([]);
  const [businessValuesData, setBusinessValuesData] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [progressionData, setProgressionData] = useState(null);
  const [isGuideOpen, setIsGuideOpen] = useState(false);

  // Function to handle printing
  const handlePrint = () => {
    // Create a new window for printing
    const printWindow = window.open('', '_blank');

    // Get the current page content
    const currentDate = new Date().toLocaleDateString('fr-FR', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });

    // Generate print content with Atelier 1 theme
    const printContent = `
      <!DOCTYPE html>
      <html>
        <head>
          <title>${t('workshop3.activity5.printTitle')} - ${currentAnalysis?.name || t('workshop3.activity5.defaultAnalysisName')}</title>
          <style>
            @media print {
              @page { size: A4; margin: 0.75in; }
              body {
                font-family: ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
                font-size: 9.5pt;
                line-height: 1.4;
                color: #333;
                -webkit-print-color-adjust: exact !important;
                print-color-adjust: exact !important;
                counter-reset: page;
                margin: 0;
                background: white !important;
              }
              @page {
                @bottom-right {
                  content: "Page " counter(page);
                  font-size: 8pt;
                  color: #666;
                }
              }
              h1 {
                font-size: 16pt;
                text-align: center;
                margin-bottom: 25px;
                color: #1e3a8a;
                font-weight: 600;
              }
              h2 {
                font-size: 13pt;
                margin-top: 20px;
                margin-bottom: 10px;
                border-bottom: 1px solid #ccc;
                padding-bottom: 4px;
                color: #1e40af;
                font-weight: 600;
                display: flex;
                align-items: center;
              }
              h2 svg {
                width: 18px;
                height: 18px;
                margin-right: 8px;
                color: #3b82f6;
              }
              h3 {
                font-size: 11pt;
                margin-top: 15px;
                margin-bottom: 5px;
                font-weight: 600;
                color: #1d4ed8;
              }
              p { margin-bottom: 6px; }
              strong { font-weight: 600; color: #1f2937; }
              ul { list-style: disc; margin-left: 25px; margin-bottom: 10px; }
              table {
                border-collapse: collapse;
                width: 100%;
                font-size: 8pt;
                margin-top: 8px;
                margin-bottom: 15px;
                table-layout: fixed;
                border: 1px solid #ddd;
              }
              th, td {
                border: 1px solid #e5e7eb;
                padding: 6px 8px;
                text-align: left;
                vertical-align: top;
                word-wrap: break-word;
                overflow-wrap: break-word;
              }
              /* Column width adjustments for print */
              th:nth-child(1), td:nth-child(1) { width: 25%; } /* Événement Redouté */
              th:nth-child(2), td:nth-child(2) { width: 20%; } /* Valeur Métier */
              th:nth-child(3), td:nth-child(3) { width: 20%; } /* Biens Supports */
              th:nth-child(4), td:nth-child(4) { width: 8%; text-align: center; } /* Atelier 1 - reduced */
              th:nth-child(5), td:nth-child(5) { width: 8%; text-align: center; } /* Atelier 3 - reduced */
              th:nth-child(6), td:nth-child(6) { width: 9%; text-align: center; } /* Progression */
              th:nth-child(7), td:nth-child(7) { width: 10%; text-align: center; } /* Statut - increased */
              th {
                background-color: #f9fafb !important;
                font-weight: 600;
                color: #1f2937 !important;
                font-size: 7.5pt;
                text-transform: uppercase;
                letter-spacing: 0.03em;
              }
              tbody tr:nth-child(odd) { background-color: #f9fafb !important; }
              .section-card {
                border: 1px solid #e5e7eb;
                border-radius: 0.375rem;
                padding: 12px 15px;
                margin-bottom: 18px;
                background-color: #fff !important;
                break-inside: avoid;
              }
              .no-print { display: none !important; }
            }

            /* Additional styles for status badges and progress */
            .status-badge {
              padding: 4px 8px;
              border-radius: 12px;
              font-size: 7pt;
              font-weight: 600;
              text-transform: uppercase;
              letter-spacing: 0.5px;
              display: inline-flex;
              align-items: center;
              gap: 2px;
            }
            .status-enriched {
              background-color: #d1fae5 !important;
              color: #065f46 !important;
            }
            .status-partiallytreated {
              background-color: #fef3c7 !important;
              color: #92400e !important;
            }
            .status-nottreated {
              background-color: #fee2e2 !important;
              color: #991b1b !important;
            }
            .progress-percentage {
              font-weight: 600;
              font-size: 7pt;
              margin-bottom: 2px;
            }
            .mini-progress {
              width: 40px;
              height: 4px;
              background: #e5e7eb;
              border-radius: 2px;
              overflow: hidden;
              margin: 0 auto;
            }
            .mini-progress-fill {
              height: 100%;
              border-radius: 2px;
            }
            .measure-count {
              background-color: #dbeafe !important;
              color: #1e40af !important;
              padding: 2px 6px;
              border-radius: 6px;
              font-weight: 600;
              font-size: 7pt;
              display: inline-block;
            }
            .event-name {
              font-weight: 600;
              color: #1f2937;
              margin-bottom: 2px;
              font-size: 8pt;
            }
            .event-details {
              font-size: 6pt;
              color: #6b7280;
            }
            .business-value {
              font-weight: 500;
              color: #374151;
              font-size: 7pt;
            }
            .business-id {
              font-size: 6pt;
              color: #9ca3af;
              background: #f3f4f6;
              padding: 1px 4px;
              border-radius: 3px;
              display: inline-block;
              margin-top: 1px;
            }

            /* Chart styles for print */
            .charts-container {
              display: grid;
              grid-template-columns: 1fr 1fr;
              gap: 20px;
              margin: 20px 0;
            }
            .chart-section {
              text-align: center;
            }
            .chart-title {
              font-size: 10pt;
              font-weight: 600;
              color: #1f2937;
              margin-bottom: 10px;
            }
            .pie-chart {
              width: 120px;
              height: 120px;
              border-radius: 50%;
              margin: 0 auto 10px;
              position: relative;
              background: conic-gradient(
                #10b981 0deg,
                #10b981 var(--enriched-angle, 0deg),
                #f59e0b var(--enriched-angle, 0deg),
                #f59e0b var(--partial-angle, 0deg),
                #ef4444 var(--partial-angle, 0deg),
                #ef4444 360deg
              );
            }
            .pie-chart::after {
              content: '';
              position: absolute;
              top: 50%;
              left: 50%;
              transform: translate(-50%, -50%);
              width: 60px;
              height: 60px;
              background: white;
              border-radius: 50%;
            }
            .chart-legend {
              font-size: 7pt;
              text-align: left;
              margin-top: 10px;
            }
            .legend-item {
              display: flex;
              align-items: center;
              margin-bottom: 3px;
            }
            .legend-color {
              width: 12px;
              height: 12px;
              border-radius: 2px;
              margin-right: 6px;
            }
            .legend-enriched { background-color: #10b981; }
            .legend-partial { background-color: #f59e0b; }
            .legend-nottreated { background-color: #ef4444; }
            .progress-bar-chart {
              width: 100%;
              max-width: 200px;
              margin: 0 auto;
            }
            .progress-bar-item {
              margin-bottom: 8px;
            }
            .progress-bar-label {
              font-size: 7pt;
              font-weight: 600;
              color: #374151;
              margin-bottom: 2px;
              display: flex;
              justify-content: space-between;
            }
            .progress-bar-bg {
              width: 100%;
              height: 12px;
              background-color: #e5e7eb;
              border-radius: 6px;
              overflow: hidden;
            }
            .progress-bar-fill {
              height: 100%;
              border-radius: 6px;
              transition: width 0.3s ease;
            }
            .progress-enrichment { background-color: #10b981; }
            .progress-treatment { background-color: #3b82f6; }

          </style>
        </head>
        <body>
          <h1>${t('workshop3.activity5.printTitle')}</h1>

          <div class="section-card">
            <h2>📊 ${t('workshop3.activity5.print.generalInfo')}</h2>
            <p><strong>${t('workshop3.activity5.print.analysis')} :</strong> ${currentAnalysis?.name || t('workshop3.activity5.defaultAnalysisName')}</p>
            <p><strong>${t('workshop3.activity5.print.generationDate')} :</strong> ${currentDate}</p>
            <p><strong>${t('workshop3.activity5.print.eventCount')} :</strong> ${progressionData ? progressionData.globalStats.totalEvents : 0}</p>
          </div>

          ${progressionData ? `
            <div class="section-card">
              <h2>📈 ${t('workshop3.activity5.print.globalStats')}</h2>
              <ul>
                <li><strong>${t('workshop3.activity5.print.totalEvents')} :</strong> ${progressionData.globalStats.totalEvents || 0}</li>
                <li><strong>${t('workshop3.activity5.print.enrichedEvents')} :</strong> ${progressionData.globalStats.enrichedEvents || 0} (${progressionData.globalStats.enrichmentRate || 0}%)</li>
                <li><strong>${t('workshop3.activity5.print.partialEvents')} :</strong> ${progressionData.globalStats.partiallyTreatedEvents || 0}</li>
                <li><strong>${t('workshop3.activity5.print.notTreatedEvents')} :</strong> ${progressionData.globalStats.notTreatedEvents || 0}</li>
                <li><strong>${t('workshop3.activity5.print.globalCoverageRate')} :</strong> ${progressionData.globalStats.treatmentRate || 0}%</li>
              </ul>
            </div>

            <div class="section-card">
              <h2>📊 ${t('workshop3.activity5.print.progressCharts')}</h2>
              <div class="charts-container">
                <div class="chart-section">
                  <div class="chart-title">${t('workshop3.activity5.print.eventDistribution')}</div>
                  <div class="pie-chart" style="
                    --enriched-angle: ${(progressionData.globalStats.enrichedEvents / progressionData.globalStats.totalEvents * 360) || 0}deg;
                    --partial-angle: ${((progressionData.globalStats.enrichedEvents + progressionData.globalStats.partiallyTreatedEvents) / progressionData.globalStats.totalEvents * 360) || 0}deg;
                  "></div>
                  <div class="chart-legend">
                    <div class="legend-item">
                      <div class="legend-color legend-enriched"></div>
                      <span>${t('workshop3.activity5.print.enriched')} (${progressionData.globalStats.enrichedEvents || 0})</span>
                    </div>
                    <div class="legend-item">
                      <div class="legend-color legend-partial"></div>
                      <span>${t('workshop3.activity5.print.partial')} (${progressionData.globalStats.partiallyTreatedEvents || 0})</span>
                    </div>
                    <div class="legend-item">
                      <div class="legend-color legend-nottreated"></div>
                      <span>${t('workshop3.activity5.print.notTreated')} (${progressionData.globalStats.notTreatedEvents || 0})</span>
                    </div>
                  </div>
                </div>

                <div class="chart-section">
                  <div class="chart-title">${t('workshop3.activity5.print.progressionRates')}</div>
                  <div class="progress-bar-chart">
                    <div class="progress-bar-item">
                      <div class="progress-bar-label">
                        <span>${t('workshop3.activity5.print.enrichment')}</span>
                        <span>${progressionData.globalStats.enrichmentRate || 0}%</span>
                      </div>
                      <div class="progress-bar-bg">
                        <div class="progress-bar-fill progress-enrichment" style="width: ${progressionData.globalStats.enrichmentRate || 0}%"></div>
                      </div>
                    </div>
                    <div class="progress-bar-item">
                      <div class="progress-bar-label">
                        <span>${t('workshop3.activity5.print.globalCoverage')}</span>
                        <span>${progressionData.globalStats.treatmentRate || 0}%</span>
                      </div>
                      <div class="progress-bar-bg">
                        <div class="progress-bar-fill progress-treatment" style="width: ${progressionData.globalStats.treatmentRate || 0}%"></div>
                      </div>
                    </div>
                  </div>

                  <div style="margin-top: 15px; font-size: 7pt; color: #6b7280;">
                    <div><strong>${t('workshop3.activity5.print.totalMeasures')} :</strong></div>
                    <div>• ${t('workshop3.activity5.print.workshop1')} : ${progressionData.globalStats.totalA1Measures || 0}</div>
                    <div>• ${t('workshop3.activity5.print.workshop3')} : ${progressionData.globalStats.totalA3Measures || 0}</div>
                    <div>• ${t('workshop3.activity5.print.averageProgression')} : ${progressionData.globalStats.averageProgressionRate || 0}%</div>
                  </div>
                </div>
              </div>
            </div>

            <div class="section-card">
              <h2>📋 ${t('workshop3.activity5.print.eventDetails')}</h2>
              <table>
                <thead>
                  <tr>
                    <th>${t('workshop3.activity5.print.dreadedEvent')}</th>
                    <th>${t('workshop3.activity5.print.businessValue')}</th>
                    <th>${t('workshop3.activity5.print.supportAssets')}</th>
                    <th style="text-align: center;">${t('workshop3.activity5.print.workshop1')}</th>
                    <th style="text-align: center;">${t('workshop3.activity5.print.workshop3')}</th>
                    <th style="text-align: center;">${t('workshop3.activity5.print.progression')}</th>
                    <th style="text-align: center;">${t('workshop3.activity5.print.status')}</th>
                  </tr>
                </thead>
                <tbody>
                  ${progressionData.eventProgression.map(event => `
                    <tr>
                      <td>
                        <div class="event-name">${event.name}</div>
                        <div class="event-details">${event.gravity} • ${event.securityPillar}</div>
                      </td>
                      <td>
                        <div class="business-value">${event.businessValueDetails?.name || t('workshop3.activity5.print.notDefined')}</div>
                        ${event.businessValueDetails?.shortId ? `<div class="business-id">${event.businessValueDetails.shortId}</div>` : ''}
                      </td>
                      <td style="max-width: 200px; word-wrap: break-word;">${event.supportAssetsFormatted || t('workshop3.activity5.print.none')}</td>
                      <td style="text-align: center;">
                        <span class="measure-count">${event.a1Measures.length} ${event.a1Measures.length > 1 ? t('workshop3.activity5.print.measures') : t('workshop3.activity5.print.measure')}</span>
                      </td>
                      <td style="text-align: center;">
                        <span class="measure-count">${event.a3Measures.length} ${event.a3Measures.length > 1 ? t('workshop3.activity5.print.measures') : t('workshop3.activity5.print.measure')}</span>
                      </td>
                      <td style="text-align: center;">
                        <div class="progress-percentage" style="color: ${
                          event.progressionRate >= 100 ? '#059669' :
                          event.progressionRate >= 50 ? '#d97706' :
                          event.progressionRate > 0 ? '#2563eb' : '#dc2626'
                        };">${event.progressionRate}%</div>
                        <div class="mini-progress">
                          <div class="mini-progress-fill" style="width: ${event.progressionRate}%; background: ${
                            event.progressionRate >= 100 ? 'linear-gradient(90deg, #10b981, #059669)' :
                            event.progressionRate >= 50 ? 'linear-gradient(90deg, #f59e0b, #d97706)' :
                            event.progressionRate > 0 ? 'linear-gradient(90deg, #3b82f6, #2563eb)' : 'linear-gradient(90deg, #ef4444, #dc2626)'
                          };"></div>
                        </div>
                      </td>
                      <td style="text-align: center;">
                        <span class="status-badge status-${event.status.replace('-', '')}">
                          ${event.status === 'enriched' ? '✅' : event.status === 'partially-treated' ? '⚠️' : '❌'}
                          ${event.statusLabel}
                        </span>
                      </td>
                    </tr>
                  `).join('')}
                </tbody>
              </table>
            </div>


          ` : `<p style="text-align: center; color: #6b7280; font-size: 18px; margin: 50px 0;">${t('workshop3.activity5.print.noDataAvailable')}</p>`}

          <div style="margin-top: 30px; text-align: center; padding: 20px; background-color: #f9fafb; border-radius: 8px;">
            <div style="font-weight: 600; color: #1f2937; margin-bottom: 5px;">🛡️ ${t('workshop3.activity5.print.toolName')}</div>
            <div style="color: #6b7280; font-size: 10pt;">
              ${t('workshop3.activity5.print.reportGenerated')} ${currentDate}<br>
              ${t('workshop3.activity5.print.methodology')}
            </div>
          </div>
        </body>
      </html>
    `;

    // Write content to print window
    printWindow.document.write(printContent);
    printWindow.document.close();

    // Wait for content to load then print
    printWindow.onload = () => {
      printWindow.print();
      printWindow.close();
    };
  };

  // Fetch ecosystem measures and attack paths
  useEffect(() => {
    const fetchData = async () => {
      if (!currentAnalysis?.id) return;

      setIsLoading(true);
      try {
        // Fetch ecosystem measures from Atelier 3
        const measuresResponse = await api.get(`/analyses/${currentAnalysis.id}/ecosystem-measures`);
        if (measuresResponse.success) {
          setEcosystemMeasures(measuresResponse.data || []);
        }

        // Fetch attack paths
        const pathsResponse = await api.get(`/analyses/${currentAnalysis.id}/attack-paths`);
        if (pathsResponse.success) {
          setAttackPaths(pathsResponse.data || []);
        }

        // Fetch business values data
        const businessValuesResponse = await api.get(`/analyses/${currentAnalysis.id}/business-values`);
        if (businessValuesResponse.success) {
          console.log('Business values API response:', businessValuesResponse.data);
          setBusinessValuesData(businessValuesResponse.data || null);
        }

        setIsLoading(false);
      } catch (error) {
        console.error('Error fetching data for event progression:', error);
        setIsLoading(false);
      }
    };

    fetchData();
  }, [currentAnalysis]);

  // Process data to calculate progression metrics
  useEffect(() => {
    if (isLoading || isWorkshopDataLoading) return;

    // Process the data to calculate progression metrics
    // The processEventProgressionData function will use demo data if real data is missing
    const processedData = processEventProgressionData(
      currentDreadedEvents,
      currentAnalysisControlPlan,
      ecosystemMeasures,
      attackPaths,
      businessValuesData
    );

    setProgressionData(processedData);
  }, [
    isLoading,
    isWorkshopDataLoading,
    currentDreadedEvents,
    currentAnalysisControlPlan,
    ecosystemMeasures,
    attackPaths,
    businessValuesData
  ]);

  if (isLoading || isWorkshopDataLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
        <span className="ml-3 text-gray-600">{t('workshop3.activity5.loading')}</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Enhanced Header - Matching Scénarios Stratégiques style */}
      <div className="bg-white rounded-xl shadow-sm border border-slate-100 p-4 mb-6">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          {/* Left Side: Breadcrumb & Title */}
          <div>
            <div className="flex items-center text-sm text-slate-500 mb-2">
              <span className="hover:text-blue-600 transition-colors">{t('navigation.workshop3')}</span>
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mx-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
              <span className="text-blue-600 font-medium">{t('workshop3.activity5.breadcrumb')}</span>
            </div>
            <h1 className="text-2xl sm:text-3xl font-bold text-slate-800 flex items-center">
              <TrendingUp size={28} className="mr-3 text-blue-600" />
{t('workshop3.activity5.title')}
            </h1>
          </div>

          {/* Right Side: Action Buttons */}
          <div className="flex items-center space-x-3">
            {/* Print Button */}
            {progressionData && (
              <button
                className="text-sm font-medium bg-green-100 text-green-700 px-4 py-2 rounded-lg hover:bg-green-200 flex items-center shadow-sm transition duration-200"
                onClick={handlePrint}
              >
                <Printer size={16} className="mr-2" />
{t('common.print')}
              </button>
            )}

            {/* Help Button */}
            <button
              className="text-sm font-medium bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 flex items-center shadow-sm transition duration-200"
              onClick={() => setIsGuideOpen(true)}
            >
              <Info size={16} className="mr-2" />
{t('common.help')}
            </button>
          </div>
        </div>

        {/* Description */}
        <div className="mt-4 p-4 bg-blue-50 rounded-lg border border-blue-100">
          <p className="text-sm text-blue-700">
            {t('workshop3.activity5.description')}
          </p>
        </div>
      </div>

      {/* Note about demo data if no real data is available */}
      {(!currentDreadedEvents?.length || !Object.keys(currentAnalysisControlPlan || {}).length) && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
          <div className="flex items-center">
            <AlertTriangle className="h-5 w-5 text-blue-400 mr-3" />
            <div>
              <p className="text-blue-700 font-medium">{t('workshop3.activity5.demoMode')}</p>
              <p className="text-blue-600 text-sm mt-1">
                {t('workshop3.activity5.demoModeDescription')}
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Main Content */}
      <EventProgressionTable data={progressionData} />

      {/* Guide Modal */}
      {isGuideOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-xl shadow-xl p-6 max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl font-bold text-slate-800 flex items-center">
                <Info size={20} className="mr-2 text-blue-600" />
{t('workshop3.activity5.guide.title')}
              </h2>
              <button
                onClick={() => setIsGuideOpen(false)}
                className="text-slate-500 hover:text-slate-700 focus:outline-none"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            <div className="space-y-4 text-slate-700">
              <div className="bg-slate-50 p-4 rounded-lg border border-slate-200 mb-4">
                <h3 className="font-semibold text-slate-800 mb-2">{t('workshop3.activity5.guide.objective')}</h3>
                <p className="text-slate-600">
                  {t('workshop3.activity5.guide.objectiveDescription')}
                </p>
              </div>

              <div className="bg-blue-50 p-4 rounded-lg border border-blue-100">
                <h3 className="font-semibold text-blue-800 mb-2">{t('workshop3.activity5.guide.progressionLogic')}</h3>
                <ul className="list-disc pl-5 space-y-2">
                  <li><strong>🟢 {t('workshop3.activity5.guide.enriched')}:</strong> {t('workshop3.activity5.guide.enrichedDescription')}</li>
                  <li><strong>🟡 {t('workshop3.activity5.guide.partiallyTreated')}:</strong> {t('workshop3.activity5.guide.partiallyTreatedDescription')}</li>
                  <li><strong>🔴 {t('workshop3.activity5.guide.notTreated')}:</strong> {t('workshop3.activity5.guide.notTreatedDescription')}</li>
                  <li>{t('workshop3.activity5.guide.generateReport')}</li>
                </ul>
              </div>

              <div className="bg-green-50 p-4 rounded-lg border border-green-100">
                <h3 className="font-semibold text-green-800 mb-2">{t('workshop3.activity5.guide.tableUsage')}</h3>
                <p className="text-green-700 text-sm">
                  {t('workshop3.activity5.guide.tableUsageDescription')}
                </p>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Activite5;
