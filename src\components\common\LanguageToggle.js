import React from 'react';
import { useLanguage } from '../../context/LanguageContext';

const LanguageToggle = ({ className = "" }) => {
  const { changeLanguage, currentLanguage } = useLanguage();

  return (
    <div className={`flex items-center bg-slate-100 rounded-lg p-1 ${className}`}>
      <button
        onClick={() => changeLanguage('fr')}
        className={`px-3 py-1 rounded text-sm font-medium transition duration-200 ${
          currentLanguage === 'fr' 
            ? 'bg-blue-600 text-white shadow-sm' 
            : 'text-slate-600 hover:bg-slate-200'
        }`}
        title="Français"
      >
        🇫🇷 FR
      </button>
      <button
        onClick={() => changeLanguage('en')}
        className={`px-3 py-1 rounded text-sm font-medium transition duration-200 ${
          currentLanguage === 'en' 
            ? 'bg-blue-600 text-white shadow-sm' 
            : 'text-slate-600 hover:bg-slate-200'
        }`}
        title="English"
      >
        🇬🇧 EN
      </button>
    </div>
  );
};

export default LanguageToggle;
