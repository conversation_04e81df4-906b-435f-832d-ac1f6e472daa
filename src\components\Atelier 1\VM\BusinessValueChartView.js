// BusinessValueChartView.js
import React from 'react';
import { useTranslation } from 'react-i18next';
import ChartSidePanel from './ChartSidePanel';
import ChartVisualization from './ChartVisualization';

const BusinessValueChartView = ({
  businessValues,
  selectedValueForChart,
  setSelectedValueForChart,
  handleRemoveSupportAsset,
  useShortIds,
  setUseShortIds,
  handleToggleSecurityPillar,
  setExpandedValue,
  setViewMode,
  zoomLevel,
  setZoomLevel,
  viewportPosition,
  setViewportPosition,
  isDragging,
  setIsDragging,
  dragStart,
  setDragStart,
  chartContainerRef
}) => {
  const { t } = useTranslation();
  if (businessValues.length === 0) {
    return (
      <div className="bg-white p-4 rounded-md border shadow-md">
        <div className="text-center py-8">
          <p className="text-gray-500">{t('businessValues.empty.addValuesForChart')}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white p-4 rounded-md border shadow-md">
      <div className="flex flex-col lg:flex-row gap-6">
        <ChartSidePanel
          businessValues={businessValues}
          selectedValueForChart={selectedValueForChart}
          setSelectedValueForChart={setSelectedValueForChart}
          useShortIds={useShortIds}
          setUseShortIds={setUseShortIds}
          handleToggleSecurityPillar={handleToggleSecurityPillar}
          setExpandedValue={setExpandedValue}
          handleRemoveSupportAsset={handleRemoveSupportAsset}

          setViewMode={setViewMode}
        />

        <ChartVisualization
          businessValues={businessValues}
          selectedValueForChart={selectedValueForChart}
          setSelectedValueForChart={setSelectedValueForChart}
          zoomLevel={zoomLevel}
          setZoomLevel={setZoomLevel}
          viewportPosition={viewportPosition}
          setViewportPosition={setViewportPosition}
          isDragging={isDragging}
          setIsDragging={setIsDragging}
          dragStart={dragStart}
          setDragStart={setDragStart}
          useShortIds={useShortIds}
          chartContainerRef={chartContainerRef}
        />
      </div>
    </div>
  );
};

export default BusinessValueChartView;