// src/components/Atelier 2/Activite1/ThreatCategories.js
import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { AlertCircle, Check, ChevronDown, ChevronUp } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { useAnalysis } from '../../../context/AnalysisContext';
// No need for toast utils as we're not saving categories

// Define threat categories structure (without translations)
const THREAT_CATEGORIES_BASE = [
  {
    id: 'etatique',
    color: 'bg-red-600 border-red-700 text-red-50',
    icon: '🏛️'
  },
  {
    id: 'criminel',
    color: 'bg-purple-600 border-purple-700 text-purple-50',
    icon: '🔪'
  },
  {
    id: 'terroriste',
    color: 'bg-orange-600 border-orange-700 text-orange-50',
    icon: '💣'
  },
  {
    id: 'activiste',
    color: 'bg-green-600 border-green-700 text-green-50',
    icon: '✊'
  },
  {
    id: 'officine',
    color: 'bg-blue-600 border-blue-700 text-blue-50',
    icon: '🕵️'
  },
  {
    id: 'amateur',
    color: 'bg-yellow-600 border-yellow-700 text-yellow-50',
    icon: '🎮'
  },
  {
    id: 'vengeur',
    color: 'bg-pink-600 border-pink-700 text-pink-50',
    icon: '😡'
  },
  {
    id: 'malveillant',
    color: 'bg-indigo-600 border-indigo-700 text-indigo-50',
    icon: '🦹'
  }
];

// Function to get translated threat categories
export const getThreatCategories = (t) => {
  return THREAT_CATEGORIES_BASE.map(category => ({
    ...category,
    name: t(`workshop2.activity1.categories.${category.id}.name`),
    description: t(`workshop2.activity1.categories.${category.id}.description`),
    details: t(`workshop2.activity1.categories.${category.id}.details`)
  }));
};

// Export for backward compatibility (will be translated in component)
export const THREAT_CATEGORIES = THREAT_CATEGORIES_BASE;

const ThreatCategories = ({ externalSelectedCategories, setExternalSelectedCategories }) => {
  const { t } = useTranslation();
  const [selectedCategories, setSelectedCategories] = useState([]);
  const [expandedCategory, setExpandedCategory] = useState(null);
  const [hoverTimer, setHoverTimer] = useState(null);
  const { currentAnalysis, loadThreatCategories } = useAnalysis();

  // Get translated threat categories
  const translatedCategories = getThreatCategories(t);

  // Use external state if provided, otherwise use internal state
  const actualSelectedCategories = externalSelectedCategories || selectedCategories;
  const actualSetSelectedCategories = setExternalSelectedCategories || setSelectedCategories;

  // Load selected threat categories on component mount
  useEffect(() => {
    // If external categories are provided, use them
    if (!externalSelectedCategories || !Array.isArray(externalSelectedCategories)) {
      // Otherwise load from API
      const fetchData = async () => {
        if (currentAnalysis?.id) {
          try {
            const data = await loadThreatCategories(currentAnalysis.id);
            if (data && Array.isArray(data)) {
              setSelectedCategories(data);
            }
          } catch (error) {
            console.error('Error loading threat categories:', error);
          }
        }
      };

      fetchData();
    }
  }, [currentAnalysis?.id, loadThreatCategories, externalSelectedCategories]);

  // Toggle selection of a category
  const toggleCategorySelection = (categoryId) => {
    if (actualSelectedCategories.includes(categoryId)) {
      actualSetSelectedCategories(actualSelectedCategories.filter(id => id !== categoryId));
    } else {
      actualSetSelectedCategories([...actualSelectedCategories, categoryId]);
    }
  };

  // Handle hover start with delay
  const handleHoverStart = (categoryId) => {
    // Clear any existing timer
    if (hoverTimer) {
      clearTimeout(hoverTimer);
    }

    // Set a new timer that will expand the category after 1 second
    const timer = setTimeout(() => {
      setExpandedCategory(categoryId);
    }, 1000); // 1 second delay

    setHoverTimer(timer);
  };

  // Handle hover end
  const handleHoverEnd = () => {
    // Clear the timer if it exists
    if (hoverTimer) {
      clearTimeout(hoverTimer);
      setHoverTimer(null);
    }

    // Collapse the category
    setExpandedCategory(null);
  };

  // Clean up timer on unmount
  useEffect(() => {
    return () => {
      if (hoverTimer) {
        clearTimeout(hoverTimer);
      }
    };
  }, [hoverTimer]);

  // Helper function to get color name based on category ID
  const getColorName = (category) => {
    // Direct mapping for problematic categories
    const colorMap = {
      'etatique': 'red',
      'criminel': 'purple',
      'terroriste': 'amber', // Changed from orange to amber for better distinction from red
      'activiste': 'green',
      'officine': 'blue',
      'amateur': 'yellow',
      'vengeur': 'pink', // Using standard pink with enhanced border styling
      'malveillant': 'indigo'
    };

    return colorMap[category.id] || 'gray';
  };

  // No need to save categories separately as they are derived from sources de risque

  return (
    <div className="space-y-6">

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {translatedCategories.map(category => {
          const isSelected = actualSelectedCategories.includes(category.id);
          const isExpanded = expandedCategory === category.id;

          return (
            <motion.div
              key={category.id}
              whileHover={{ y: -5 }}
              onHoverStart={() => handleHoverStart(category.id)}
              onHoverEnd={handleHoverEnd}
              className={`rounded-xl overflow-hidden transition-all duration-300 cursor-pointer ${isSelected ?
                (category.id === 'vengeur' ? 'border-4 border-pink-500 ring-2 ring-pink-300 ring-offset-2 shadow-lg shadow-pink-200' :
                `border-4 border-${getColorName(category)}-500 ring-2 ring-${getColorName(category)}-300 ring-offset-1`)
                : 'border border-gray-200'}`}
              style={{
                boxShadow: isSelected ? '0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)' : '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
              }}
              onClick={(e) => {
                e.preventDefault();
                toggleCategorySelection(category.id);
              }}
            >
              <div className="bg-white h-full">
                <div className="flex justify-between items-start p-5">
                  <div className="flex items-center">
                    <div className={`text-3xl p-2 rounded-full ${isSelected ?
                      (category.id === 'vengeur' ? 'bg-pink-200 border border-pink-400' : `bg-${getColorName(category)}-100`)
                      : 'bg-gray-100'} mr-3`}>
                      {category.icon}
                    </div>
                    <div>
                      <h3 className={`font-bold text-lg ${isSelected ?
                        (category.id === 'vengeur' ? 'text-pink-600 underline decoration-pink-500' : `text-${getColorName(category)}-700`)
                        : 'text-gray-800'}`}>{category.name}</h3>
                      <p className={`text-sm ${isSelected ? 'text-gray-700' : 'text-gray-600'}`}>
                        {category.description}
                      </p>
                    </div>
                  </div>
                  <div>
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        e.preventDefault();
                        toggleCategorySelection(category.id);
                      }}
                      onMouseEnter={(e) => e.stopPropagation()}
                      onMouseLeave={(e) => e.stopPropagation()}
                      className={`p-2 rounded-full transition-colors duration-200 ${isSelected ?
                        (category.id === 'vengeur' ? 'bg-pink-200 text-pink-700 hover:bg-pink-300 ring-1 ring-pink-400' :
                        `bg-${getColorName(category)}-100 text-${getColorName(category)}-700 hover:bg-${getColorName(category)}-200`)
                        : 'bg-gray-100 text-gray-500 hover:bg-gray-200'}`}
                      aria-label={isSelected ? t('workshop2.activity1.threatCategories.deselectButton') : t('workshop2.activity1.threatCategories.selectButton')}
                    >
                      <Check size={20} />
                    </button>
                  </div>
                </div>

                {isExpanded && (
                  <motion.div
                    initial={{ opacity: 0, height: 0 }}
                    animate={{ opacity: 1, height: 'auto' }}
                    exit={{ opacity: 0, height: 0 }}
                    transition={{ duration: 0.3 }}
                    className="px-5 pb-5 text-gray-600"
                    onClick={(e) => {
                      e.stopPropagation();
                      e.preventDefault();
                    }}
                    onMouseEnter={(e) => e.stopPropagation()}
                  >
                    <div className="pt-4 border-t border-gray-200">
                      <p>{category.details}</p>
                    </div>
                  </motion.div>
                )}
              </div>
            </motion.div>
          );
        })}
      </div>

      <div className="flex justify-between items-center pt-4 border-t border-gray-200">
        <div className="text-gray-600">
          {t('workshop2.activity1.threatCategories.selectedCount', { count: actualSelectedCategories.length, total: translatedCategories.length })}
        </div>
      </div>
    </div>
  );
};

export default ThreatCategories;
