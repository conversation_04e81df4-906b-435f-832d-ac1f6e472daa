// src/AuthWrapper.js - Updated with AnalysisProvider and Session Management
import React, { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import LoginForm from './components/auth/LoginForm';
import { useAuth } from './context/AuthContext';
import { AnalysisProvider } from './context/AnalysisContext';
import LoadingSpinner from './components/common/LoadingSpinner';
import useSessionManager from './hooks/useSessionManager';

// Composant d'authentification qui protège l'accès à l'application
const AuthWrapper = ({ children }) => {
  const { isLoading, isAuthenticated, user, logout } = useAuth();
  const navigate = useNavigate();
  const { initializeSession } = useSessionManager();

  useEffect(() => {
    // Vérifier si l'utilisateur a le rôle requis pour accéder à l'application
    if (user && !['simpleuser', 'admin', 'superadmin'].includes(user.role)) {
      // Si l'utilisateur n'a pas le bon rôle, déconnexion et redirection
      logout();
      navigate('/login', { replace: true });
    }
  }, [user, logout, navigate]);

  // Initialize session when user is authenticated
  useEffect(() => {
    if (isAuthenticated && user) {
      initializeSession();
    }
  }, [isAuthenticated, user, initializeSession]);

  // Afficher un indicateur de chargement pendant la vérification
  if (isLoading) {
    return (
      <div className="flex justify-center items-center min-h-screen bg-gray-100">
        <div className="text-center">
          <LoadingSpinner size="large" color="blue" text="Vérification de l'authentification..." />
        </div>
      </div>
    );
  }

  // Si l'utilisateur n'est pas connecté, afficher la page de connexion
  if (!isAuthenticated) {
    return <LoginForm />;
  }

  // Injecter l'utilisateur et la fonction de déconnexion aux composants enfants
  // Wrap with AnalysisProvider to provide analysis context
  return (
    <AnalysisProvider>
      {React.cloneElement(children, {
        user,
        handleLogout: logout
      })}
    </AnalysisProvider>
  );
};

export default AuthWrapper;