import React, { useState, useCallback } from 'react';
import ReactFlow, {
  MiniMap,
  Controls,
  Background,
  addEdge,
  applyNodeChanges,
  applyEdgeChanges,
  // Handle, // Uncomment if you create custom nodes that need explicit handles
  // Position, // Utility, not directly used in this App component's rendering
} from 'reactflow';
import 'reactflow/dist/style.css'; // Main React Flow styles

// --- Initial Elements ---
// These are the nodes and edges that will be displayed when the diagram first loads.
// Inspired by your "chemin d'attaque" example.

const initialNodes = [
  {
    id: 'source-risk',
    type: 'input', // Input nodes typically have only a source handle
    data: { label: 'Source de Risque: Concurrent' },
    position: { x: 50, y: 150 },
    style: {
      background: '#FFCACA', // Light red
      color: '#D8000C',
      border: '1px solid #D8000C',
      borderRadius: '8px',
      width: 180,
      textAlign: 'center',
      padding: '10px',
    },
  },
  {
    id: 'ecosystem-info',
    data: { label: 'Écosystème: Informations de R&D (Partielles)' },
    position: { x: 350, y: 50 },
    style: {
      background: '#D9E8FF', // Light blue
      color: '#00529B',
      border: '1px solid #00529B',
      borderRadius: '8px',
      width: 220,
      textAlign: 'center',
      padding: '10px',
    },
  },
  {
    id: 'ecosystem-lab',
    data: { label: 'Écosystème: Laboratoire (P3)' },
    position: { x: 350, y: 150 },
    style: {
      background: '#D9E8FF', // Light blue
      color: '#00529B',
      border: '1px solid #00529B',
      borderRadius: '8px',
      width: 180,
      textAlign: 'center',
      padding: '10px',
    },
  },
  {
    id: 'ecosystem-provider',
    data: { label: 'Écosystème: Prestataire Informatique (F3)' },
    position: { x: 350, y: 250 },
    style: {
      background: '#D9E8FF', // Light blue
      color: '#00529B',
      border: '1px solid #00529B',
      borderRadius: '8px',
      width: 220,
      textAlign: 'center',
      padding: '10px',
    },
  },
  {
    id: 'target-biotech',
    type: 'output', // Output nodes typically have only a target handle
    data: { label: 'Société de Biotechnologie: Informations de R&D' },
    position: { x: 650, y: 150 },
    style: {
      background: '#DFF2BF', // Light green
      color: '#4F8A10',
      border: '1px solid #4F8A10',
      borderRadius: '8px',
      width: 200,
      textAlign: 'center',
      padding: '10px',
    },
  },
];

const initialEdges = [
  // Path 1
  { id: 'e-source-ecosysteminfo', source: 'source-risk', target: 'ecosystem-info', animated: true, label: 'Attaque 1' },
  { id: 'e-ecosysteminfo-target', source: 'ecosystem-info', target: 'target-biotech', animated: true },

  // Path 2
  { id: 'e-source-ecosystemlab', source: 'source-risk', target: 'ecosystem-lab', animated: true, label: 'Attaque 2' },
  { id: 'e-ecosystemlab-target', source: 'ecosystem-lab', target: 'target-biotech', animated: true },

  // Path 3
  { id: 'e-source-ecosystemprovider', source: 'source-risk', target: 'ecosystem-provider', animated: true, label: 'Attaque 3' },
  { id: 'e-ecosystemprovider-target', source: 'ecosystem-provider', target: 'target-biotech', animated: true },
];

// A counter for generating unique IDs for new nodes
let nodeIdCounter = initialNodes.length;

function App() {
  // State for nodes and edges
  const [nodes, setNodes] = useState(initialNodes);
  const [edges, setEdges] = useState(initialEdges);

  // Handlers for node and edge changes (drag, selection, removal)
  const onNodesChange = useCallback(
    (changes) => setNodes((nds) => applyNodeChanges(changes, nds)),
    [setNodes]
  );
  const onEdgesChange = useCallback(
    (changes) => setEdges((eds) => applyEdgeChanges(changes, eds)),
    [setEdges]
  );

  // Handler for connecting nodes
  const onConnect = useCallback(
    (connection) => setEdges((eds) => addEdge(connection, eds)),
    [setEdges]
  );

  // Function to add a new node
  const addNode = useCallback(() => {
    nodeIdCounter++;
    const newNodeId = `new-node-${nodeIdCounter}`;
    const newNode = {
      id: newNodeId,
      data: { label: `Nouveau Nœud ${nodeIdCounter}` },
      position: {
        // Position new nodes somewhat randomly or in a specific area
        x: Math.random() * 400 + 50,
        y: Math.random() * 300 + 50,
      },
      style: { // Basic style for new nodes
          background: '#FFF3CD', // Light yellow
          color: '#856404',
          border: '1px solid #856404',
          borderRadius: '8px',
          width: 150,
          textAlign: 'center',
          padding: '10px',
      }
    };
    setNodes((nds) => nds.concat(newNode));
  }, []);

  // --- Tailwind CSS for overall page layout ---
  // React Flow will take over the styling of its canvas.
  // We use Tailwind for the button and page container.

  return (
    <div className="font-sans">
      <header className="bg-gray-800 text-white p-4 text-center">
        <h1 className="text-2xl">Créateur de Diagrammes - Chemin d'Attaque</h1>
      </header>

      <div className="p-4">
        <button
          onClick={addNode}
          className="mb-4 bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg shadow-md transition duration-150 ease-in-out"
        >
          Ajouter un Nœud
        </button>
      </div>

      {/* React Flow Canvas */}
      {/* Set a fixed height for the ReactFlow component */}
      <div style={{ height: '70vh' }} className="border border-gray-300 rounded-lg shadow-lg mx-4">
        <ReactFlow
          nodes={nodes}
          edges={edges}
          onNodesChange={onNodesChange}
          onEdgesChange={onEdgesChange}
          onConnect={onConnect}
          fitView // Zooms/pans to fit all nodes on initial render
          attributionPosition="bottom-left" // Hides the "React Flow" attribution slightly
        >
          {/* Helper components */}
          <MiniMap
            nodeColor={(n) => { // Color minimap nodes based on their style
                if (n.style && n.style.background) return n.style.background;
                if (n.type === 'input') return '#FFCACA';
                if (n.type === 'output') return '#DFF2BF';
                return '#D9E8FF';
            }}
            nodeStrokeWidth={3}
            pannable // Allows panning the minimap
            zoomable // Allows zooming the minimap
          />
          <Controls /> {/* Adds zoom/fitView controls */}
          <Background color="#ccc" variant="dots" gap={16} size={1} /> {/* Adds a background pattern */}
        </ReactFlow>
      </div>

      <footer className="text-center p-4 mt-8 text-gray-600 text-sm">
        <p>Utilisez React Flow pour créer des diagrammes interactifs. Ceci est un exemple de base.</p>
        <p>
          Ce code suppose que <code>reactflow</code> est installé dans votre projet (<code>npm install reactflow</code>).
        </p>
      </footer>
    </div>
  );
}

export default App;

/**
 * To run this React application in a local development environment:
 * 1. Ensure you have Node.js and npm (or yarn) installed.
 * 2. Create a new React project (if you haven't already): `npx create-react-app my-diagram-app`
 * 3. Navigate into your project: `cd my-diagram-app`
 * 4. Install React Flow: `npm install reactflow` (or `yarn add reactflow`)
 * 5. Replace the content of `src/App.js` with the code above.
 * 6. Ensure Tailwind CSS is set up in your project.
 * - Install Tailwind: `npm install -D tailwindcss postcss autoprefixer`
 * - Initialize Tailwind: `npx tailwindcss init -p`
 * - Configure `tailwind.config.js`'s `content` array to include your source files:
 * `content: ["./src/**\/*.{js,jsx,ts,tsx}"],`
 * - Add Tailwind directives to your main CSS file (e.g., `src/index.css`):
 * ```css
 * @tailwind base;
 * @tailwind components;
 * @tailwind utilities;
 * ```
 * 7. Start the development server: `npm start` or `yarn start`
 */
