// src/services/securityFrameworkService.js
import api from '../api/apiClient';

const securityFrameworkService = {
  getSecurityFramework: async (analysisId) => {
    try {
      return await api.get(`/analyses/${analysisId}/security-framework`);
    } catch (error) {
      console.error('Error fetching security framework:', error);
      throw error;
    }
  },

  saveSecurityFramework: async (analysisId, data) => {
    try {
      console.log("Sending security framework data to API:", data);
      return await api.put(`/analyses/${analysisId}/security-framework`, {
        data: {
          frameworks: data.frameworks,
          selectedRules: data.selectedRules,
          ruleBusinessValues: data.ruleBusinessValues || {},
          ruleDreadedEvents: data.ruleDreadedEvents || {}, // Add this line
          summary: data.summary || [] ,// Make sure summary is included here

        }
      });
    } catch (error) {
      console.error('Error saving security framework:', error);
      throw error;
    }
  },

  formatSecurityFrameworkData: (apiData) => {
    if (!apiData || !apiData.data) {
      return {
        frameworks: [],
        selectedRules: {},
        ruleBusinessValues: {},
        ruleDreadedEvents: {},
        summary: []
      };
    }
  
    // Check if data is nested inside data property
    const data = apiData.data.data || apiData.data;
    
    return {
      frameworks: data.frameworks || [],
      selectedRules: data.selectedRules || {},
      ruleBusinessValues: data.ruleBusinessValues || {},
      ruleDreadedEvents: data.ruleDreadedEvents || {},
      summary: data.summary || []
    };
  }
};

export default securityFrameworkService;