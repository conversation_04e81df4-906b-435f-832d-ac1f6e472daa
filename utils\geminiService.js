const { GoogleGenerativeAI, HarmCategory, HarmBlockThreshold } = require("@google/generative-ai");
const dotenv = require('dotenv');

dotenv.config(); // Load environment variables from .env file

const apiKey = process.env.GEMINI_API_KEY;

if (!apiKey) {
  console.error("CRITICAL ERROR: GEMINI_API_KEY is not set in the environment variables. AI features will not work.");
  // Consider throwing an error during startup if the key is absolutely essential
  // throw new Error("Missing GEMINI_API_KEY in environment variables."); 
}

// Initialize the Gemini client only if the API key exists
let genAI;
let model;
if (apiKey) {
    genAI = new GoogleGenerativeAI(apiKey);
    // Choose a model (e.g., gemini-1.5-flash or gemini-pro)
    // Use a newer model if possible, like gemini-1.5-flash
    model = genAI.getGenerativeModel({ 
        model: "gemini-1.5-flash", // Or "gemini-pro"
        // Optional: Configure safety settings if needed
        // safetySettings: [
        //     { category: HarmCategory.HARM_CATEGORY_HARASSMENT, threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE },
        //     { category: HarmCategory.HARM_CATEGORY_HATE_SPEECH, threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE },
        //     { category: HarmCategory.HARM_CATEGORY_SEXUALLY_EXPLICIT, threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE },
        //     { category: HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT, threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE },
        // ],
        // Optional: Specify JSON output mode (requires specific prompting)
        // generationConfig: {
        //   responseMimeType: "application/json",
        // },
    });
    console.log("Gemini Service Initialized with model: gemini-1.5-flash");
} else {
    console.warn("Gemini Service NOT initialized due to missing API key.");
}


/**
 * Generates content using the Gemini API.
 * Ensures the output is valid JSON if the prompt requested it.
 * @param {string} prompt - The text prompt to send to the model.
 * @returns {Promise<string>} The generated text content (expected to be a JSON string based on our prompt).
 * @throws {Error} If the API call fails, returns an error, or the response isn't valid JSON.
 */
const generateJsonContent = async (prompt) => {
  if (!model) {
      throw new Error("Gemini API client is not initialized (missing API key?). Cannot generate content.");
  }
  try {
    console.log("Sending prompt to Gemini API...");
    const result = await model.generateContent(prompt);
    const response = await result.response;

    if (!response) {
         console.error("Gemini API returned no response object.");
         throw new Error("Gemini API returned no response object.");
    }

    // Check for blocked content due to safety or other reasons
    const candidate = response?.candidates?.[0];
    if (!candidate || (candidate.finishReason && candidate.finishReason !== 'STOP')) {
         const reason = candidate?.finishReason || 'Unknown';
         const safetyRatings = candidate?.safetyRatings || 'N/A';
         console.error(`Gemini generation stopped. Reason: ${reason}. Safety Ratings: ${JSON.stringify(safetyRatings)}`);
         throw new Error(`Gemini generation stopped due to ${reason}.`);
     }

    // Extract the text, cleaning potential markdown ```json ... ``` markers
    let text = response.text();
    if (text.startsWith("```json")) {
        text = text.substring(7, text.length - 3).trim();
    } else if (text.startsWith("```")) {
         text = text.substring(3, text.length - 3).trim();
    }
    
    // NEW: Explicitly remove a potential trailing backtick
    if (text.endsWith('`')) {
        console.log("Detected and removing trailing backtick from Gemini response.");
        text = text.slice(0, -1).trim(); // Remove last character and trim again
    }

    // Validate if the response is valid JSON
    try {
      JSON.parse(text); // Try parsing to check validity
      console.log("Received valid JSON response from Gemini API.");
      return text; // Return the JSON string
    } catch (parseError) {
       console.error("Gemini API response is not valid JSON:", parseError);
       console.error("Invalid JSON received:", text); // Log the invalid text
       throw new Error("Gemini API response was not valid JSON.");
    }

  } catch (error) {
    console.error("Error calling Gemini API:", error);
    // Re-throw a more specific error or handle it as needed
    throw new Error(`Gemini API request failed: ${error.message || error}`);
  }
};

module.exports = {
  generateJsonContent,
};
