// src/services/dreadedEventsService.js
import api from '../api/apiClient';

/**
 * Service for handling dreaded events operations with version support
 * API-first approach without localStorage fallback for loading
 */
const dreadedEventService = {
  /**
   * Get dreaded events for a specific analysis
   * @param {string} analysisId - Analysis ID
   * @returns {Promise<Object>} Dreaded events data
   */
  getDreadedEvents: async (analysisId) => {
    try {
      // Try to get from API
      const response = await api.get(`/analyses/${analysisId}/dreaded-events`);

      if (response.success) {
        return {
          success: true,
          data: response.data,
          source: 'api'
        };
      }

      throw new Error(response.message || 'Failed to fetch dreaded events data');
    } catch (error) {
      console.error('Error fetching dreaded events data:', error);
      // No localStorage fallback - API is the source of truth
      throw error;
    }
  },

  /**
   * Save dreaded events for a specific analysis
   * @param {string} analysisId - Analysis ID
   * @param {Array} dreadedEvents - Dreaded events data to save
   * @returns {Promise<Object>} Saved dreaded events data
   */
  saveDreadedEvents: async (analysisId, dreadedEvents) => {
    // Format data for API
    const formattedData = {
      data: {
        dreadedEvents: dreadedEvents
      }
    };

    try {
      // Save to API
      const response = await api.post(`/analyses/${analysisId}/dreaded-events`, formattedData);

      // Also save to local storage as backup
      localStorage.setItem('dreadedEventsData', JSON.stringify(dreadedEvents));

      return {
        success: true,
        data: response.data,
        source: 'api'
      };
    } catch (error) {
      console.error('Error saving dreaded events data:', error);

      // If API fails, at least try to save locally
      localStorage.setItem('dreadedEventsData', JSON.stringify(dreadedEvents));

      // Return a success response with a local flag
      return {
        success: true,
        data: dreadedEvents,
        savedLocally: true,
        source: 'local',
        message: 'Data saved locally only. Will sync when connection is restored.'
      };
    }
  },

  /**
   * NEW: Get AI-powered dreaded event suggestions
   * @param {Object} contextPayload - Contextual information for the AI
   * @returns {Promise<Object>} Suggested dreaded events
   */
  getAiDreadedEventSuggestions: async (contextPayload) => {
    try {
      // Make POST request to the new backend endpoint
      // Replace '/ai/suggest-dreaded-events' with your actual endpoint
      const response = await api.post('/ai/suggest-dreaded-events', contextPayload);

      if (response.success) {
        return {
          success: true,
          // Assuming suggestions are in response.data.suggestions
          // Adjust based on your backend response structure
          data: response.data,
          source: 'api'
        };
      }

      // Handle backend indicating failure (e.g., validation error, AI error)
      throw new Error(response.message || 'Failed to get AI suggestions from backend');
    } catch (error) {
      console.error('Error fetching AI dreaded event suggestions:', error);
      // Rethrow or return a specific error structure
      // For consistency, rethrowing might be better here as we expect API interaction
      throw error;
      /* Or return an error object:
      return {
        success: false,
        message: error.message || 'An unexpected error occurred while fetching AI suggestions.',
        source: 'error' // Or determine source based on error type
      };
      */
    }
  },

  /**
   * Get the event history for the dreaded events component
   * @param {string} analysisId - Analysis ID
   * @returns {Promise<Object>} Component history
   */
  getEventHistory: async (analysisId) => {
    try {
      const response = await api.get(`/analyses/${analysisId}/components/dreaded-events/history`);

      if (response.success) {
        return {
          success: true,
          data: response.data,
          source: 'api'
        };
      }

      throw new Error(response.message || 'Failed to fetch event history');
    } catch (error) {
      console.error('Error fetching event history:', error);
      throw error;
    }
  },

  /**
   * Get a specific version of the dreaded events component
   * @param {string} analysisId - Analysis ID
   * @param {number} version - Version number
   * @returns {Promise<Object>} Component version
   */
  getEventVersion: async (analysisId, version) => {
    try {
      const response = await api.get(`/analyses/${analysisId}/components/dreaded-events/versions/${version}`);

      if (response.success) {
        return {
          success: true,
          data: response.data,
          source: 'api'
        };
      }

      throw new Error(response.message || 'Failed to fetch event version');
    } catch (error) {
      console.error('Error fetching event version:', error);
      throw error;
    }
  },

  /**
   * Get event suggestions from the database
   * @param {string} securityPillar - Security pillar to filter by
   * @returns {Promise<Object>} Event suggestions
   */
  getEventSuggestions: async (securityPillar) => {
    try {
      // Build query parameters
      const params = {};
      if (securityPillar) {
        params.securityPillar = securityPillar;
      }

      // Make GET request to the event suggestions endpoint
      const response = await api.get('/event-suggestions', params);

      if (response.success) {
        return {
          success: true,
          data: response.data,
          source: 'api'
        };
      }

      throw new Error(response.message || 'Failed to fetch event suggestions');
    } catch (error) {
      console.error('Error fetching event suggestions:', error);
      throw error;
    }
  }
};

export default dreadedEventService;