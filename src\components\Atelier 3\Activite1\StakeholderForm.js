// src/components/Atelier 3/Activite1/StakeholderForm.js
import React, { useState, useEffect } from 'react';
import { X, Save, AlertCircle } from 'lucide-react';

const StakeholderForm = ({ stakeholder, onSubmit, onCancel }) => {
  const [formData, setFormData] = useState({
    name: '',
    category: 'client',
    type: 'external',
    description: '',
    rank: 1,
    dependency: 1,
    penetration: 1,
    cyberMaturity: 1,
    trust: 1,
    notes: ''
  });
  
  const [threatLevel, setThreatLevel] = useState(0);
  
  // Initialize form with stakeholder data if editing
  useEffect(() => {
    if (stakeholder) {
      setFormData({
        name: stakeholder.name || '',
        category: stakeholder.category || 'client',
        type: stakeholder.type || 'external',
        description: stakeholder.description || '',
        rank: stakeholder.rank || 1,
        dependency: stakeholder.dependency || 1,
        penetration: stakeholder.penetration || 1,
        cyberMaturity: stakeholder.cyberMaturity || 1,
        trust: stakeholder.trust || 1,
        notes: stakeholder.notes || ''
      });
    }
  }, [stakeholder]);
  
  // Calculate threat level when rating values change
  useEffect(() => {
    const { dependency, penetration, cyberMaturity, trust } = formData;
    
    // Prevent division by zero
    if (cyberMaturity === 0 || trust === 0) {
      setThreatLevel(Infinity);
      return;
    }
    
    // Formula: (Dependency × Penetration) / (Cyber Maturity × Trust)
    const calculatedThreatLevel = (dependency * penetration) / (cyberMaturity * trust);
    setThreatLevel(calculatedThreatLevel);
  }, [formData.dependency, formData.penetration, formData.cyberMaturity, formData.trust]);
  
  // Handle form input changes
  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: name === 'rank' || name === 'dependency' || name === 'penetration' || 
              name === 'cyberMaturity' || name === 'trust' 
                ? parseInt(value, 10) 
                : value
    }));
  };
  
  // Handle form submission
  const handleSubmit = (e) => {
    e.preventDefault();
    
    // Calculate threat level
    const calculatedThreatLevel = (formData.dependency * formData.penetration) / 
                                 (formData.cyberMaturity * formData.trust);
    
    // Submit form data with calculated threat level
    onSubmit({
      ...formData,
      threatLevel: calculatedThreatLevel
    });
  };
  
  // Helper function to render rating options with descriptions
  const renderRatingOptions = (field, descriptions) => (
    <div className="mb-4">
      <label className="block text-sm font-medium text-gray-700 mb-1">
        {field === 'dependency' ? 'Dépendance' : 
         field === 'penetration' ? 'Pénétration' : 
         field === 'cyberMaturity' ? 'Maturité cyber' : 
         field === 'trust' ? 'Confiance' : field}
      </label>
      <div className="grid grid-cols-4 gap-2">
        {[1, 2, 3, 4].map(value => (
          <div 
            key={value}
            className={`p-3 border rounded-lg cursor-pointer transition-colors
              ${formData[field] === value ? 'bg-blue-100 border-blue-500' : 'border-gray-300 hover:bg-gray-50'}`}
            onClick={() => setFormData(prev => ({ ...prev, [field]: value }))}
          >
            <div className="font-medium text-center mb-1">{value}</div>
            <div className="text-xs text-gray-600">{descriptions[value-1]}</div>
          </div>
        ))}
      </div>
    </div>
  );
  
  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-xl font-bold text-gray-800">
          {stakeholder ? 'Modifier la partie prenante' : 'Ajouter une partie prenante'}
        </h2>
        <button
          onClick={onCancel}
          className="p-2 rounded-full hover:bg-gray-100"
        >
          <X size={20} className="text-gray-500" />
        </button>
      </div>
      
      <form onSubmit={handleSubmit}>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Left column - Basic information */}
          <div className="space-y-4">
            {/* Name */}
            <div>
              <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
                Nom *
              </label>
              <input
                type="text"
                id="name"
                name="name"
                value={formData.name}
                onChange={handleChange}
                required
                className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
            
            {/* Category */}
            <div>
              <label htmlFor="category" className="block text-sm font-medium text-gray-700 mb-1">
                Catégorie *
              </label>
              <select
                id="category"
                name="category"
                value={formData.category}
                onChange={handleChange}
                required
                className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="client">Client</option>
                <option value="partner">Partenaire</option>
                <option value="provider">Prestataire</option>
                <option value="technical">Service technique</option>
                <option value="business">Service métier</option>
                <option value="subsidiary">Filiale</option>
              </select>
            </div>
            
            {/* Type */}
            <div>
              <label htmlFor="type" className="block text-sm font-medium text-gray-700 mb-1">
                Type *
              </label>
              <div className="flex space-x-4">
                <label className="flex items-center">
                  <input
                    type="radio"
                    name="type"
                    value="internal"
                    checked={formData.type === 'internal'}
                    onChange={handleChange}
                    className="mr-2"
                  />
                  <span>Interne</span>
                </label>
                <label className="flex items-center">
                  <input
                    type="radio"
                    name="type"
                    value="external"
                    checked={formData.type === 'external'}
                    onChange={handleChange}
                    className="mr-2"
                  />
                  <span>Externe</span>
                </label>
              </div>
            </div>
            
            {/* Rank */}
            <div>
              <label htmlFor="rank" className="block text-sm font-medium text-gray-700 mb-1">
                Rang *
              </label>
              <select
                id="rank"
                name="rank"
                value={formData.rank}
                onChange={handleChange}
                required
                className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value={1}>Rang 1 (interaction directe)</option>
                <option value={2}>Rang 2 (interaction indirecte)</option>
                <option value={3}>Rang 3 (interaction distante)</option>
              </select>
            </div>
            
            {/* Description */}
            <div>
              <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-1">
                Description
              </label>
              <textarea
                id="description"
                name="description"
                value={formData.description}
                onChange={handleChange}
                rows={3}
                className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
            
            {/* Notes */}
            <div>
              <label htmlFor="notes" className="block text-sm font-medium text-gray-700 mb-1">
                Notes
              </label>
              <textarea
                id="notes"
                name="notes"
                value={formData.notes}
                onChange={handleChange}
                rows={3}
                className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
          </div>
          
          {/* Right column - Ratings */}
          <div className="space-y-4">
            {/* Dependency Rating */}
            {renderRatingOptions('dependency', [
              "Relation non nécessaire aux fonctions stratégiques",
              "Relation utile aux fonctions stratégiques",
              "Relation indispensable mais non exclusive",
              "Relation indispensable et unique (pas de substitution possible à court terme)"
            ])}
            
            {/* Penetration Rating */}
            {renderRatingOptions('penetration', [
              "Pas d'accès ou accès avec privilèges de type utilisateur à des terminaux utilisateurs",
              "Accès avec privilèges de type administrateur à des terminaux utilisateurs ou accès physique aux sites",
              "Accès avec privilèges de type administrateur à des serveurs « métier »",
              "Accès avec privilèges de type administrateur à des équipements d'infrastructure ou accès physique aux salles serveurs"
            ])}
            
            {/* Cyber Maturity Rating */}
            {renderRatingOptions('cyberMaturity', [
              "Des règles d'hygiène informatique sont appliquées ponctuellement et non formalisées",
              "Les règles d'hygiène et la réglementation sont prises en compte, sans intégration dans une politique globale",
              "Une politique globale est appliquée en matière de sécurité numérique",
              "La partie prenante met en œuvre une politique de management du risque"
            ])}
            
            {/* Trust Rating */}
            {renderRatingOptions('trust', [
              "Les intentions de la partie prenante ne peuvent être évaluées",
              "Les intentions de la partie prenante sont considérées comme neutres",
              "Les intentions de la partie prenante sont connues et probablement positives",
              "Les intentions de la partie prenante sont parfaitement connues et pleinement compatibles"
            ])}
            
            {/* Calculated Threat Level */}
            <div className="mt-6 p-4 bg-gray-50 rounded-lg border border-gray-200">
              <div className="flex justify-between items-center">
                <span className="text-sm font-medium text-gray-700">Niveau de menace calculé:</span>
                <span className="text-lg font-bold">
                  {threatLevel.toFixed(2)}
                </span>
              </div>
              <div className="mt-2">
                <div className="text-sm text-gray-500">
                  Formule: (Dépendance × Pénétration) / (Maturité cyber × Confiance)
                </div>
              </div>
            </div>
          </div>
        </div>
        
        {/* Form actions */}
        <div className="mt-8 flex justify-end space-x-3">
          <button
            type="button"
            onClick={onCancel}
            className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
          >
            Annuler
          </button>
          <button
            type="submit"
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center"
          >
            <Save size={18} className="mr-1.5" />
            {stakeholder ? 'Mettre à jour' : 'Ajouter'}
          </button>
        </div>
      </form>
    </div>
  );
};

export default StakeholderForm;
