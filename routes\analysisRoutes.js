// routes/analysisRoutes.js
const express = require('express');
const { 
  getAnalyses, 
  getAnalysisById, 
  createAnalysis, 
  updateAnalysis, 
  deleteAnalysis,
  getCompanyAnalyses
} = require('../controllers/analysisController');
const { 
  getAnalysisControls,
  updateAnalysisControls
} = require('../controllers/analysisControlController');

const { protect, restrictTo } = require('../middleware/authMiddleware');

const router = express.Router();

// All routes need authentication
router.use(protect);

// Analysis routes
router.route('/analyses')
  .get(protect, getAnalyses)
  .post(protect, createAnalysis);

// Individual analysis routes
router.route('/analyses/:id')
  .get(protect, getAnalysisById)
  .put(protect, updateAnalysis)
  .delete(protect, restrictTo('superadmin', 'admin'), deleteAnalysis);

// Company-related analysis routes
router.route('/companies/:companyId/analyses')
  .get(protect, getCompanyAnalyses);

// Analysis Control Plan Routes
router.route('/analyses/:analysisId/controls')
  .get(protect, getAnalysisControls)
  .put(protect, updateAnalysisControls);

module.exports = router;