// --- Imports ---
import React, { useState, useMemo, useEffect, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { MANUAL_CONTROLS_DATA } from '../../../constants/manualControlsData';
import { motion } from 'framer-motion';
// Ensure all used icons are imported
import { Save, CheckSquare, Info, ShieldCheck, X, Edit3, Printer, PlusCircle, RefreshCw, BrainCircuit } from 'lucide-react';
import { useAnalysis } from '../../../context/AnalysisContext';
import securityControlsService from '../../../services/securityControlsService';
import { showSuccessToast, showErrorToast, showLoadingToast, updateToast, showInfoToast } from '../../../utils/toastUtils';

// --- Constants and Helpers ---
const getSeverityLevelInfo = (severity, t) => {
    const lowerSeverity = severity?.toLowerCase() || '';
    switch (lowerSeverity) {
        case 'catastrophic':
            return { level: 'G5', label: t('securityControls.severity.catastrophic'), textClass: 'text-white', bgClass: 'bg-black' };
        case 'critical':
            return { level: 'G4', label: t('securityControls.severity.critical'), textClass: 'text-white', bgClass: 'bg-red-600' };
        case 'major': case 'majeure':
            return { level: 'G3', label: t('securityControls.severity.major'), textClass: 'text-black', bgClass: 'bg-yellow-500' };
        case 'moderate': case 'significative':
            return { level: 'G2', label: t('securityControls.severity.moderate'), textClass: 'text-black', bgClass: 'bg-yellow-200' };
        case 'minor': case 'mineure':
            return { level: 'G1', label: t('securityControls.severity.minor'), textClass: 'text-black', bgClass: 'bg-cyan-300' };
        default:
            // console.warn(`Unknown severity: '${severity}'`);
            return { level: 'N/A', label: t('securityControls.severity.undefined'), textClass: 'text-gray-800', bgClass: 'bg-gray-200' };
    }
};
// Helper functions for translated constants
const getDecisionOptions = (t) => [
    { value: 'implemented', label: t('securityControls.decision.implemented') },
    { value: 'partial', label: t('securityControls.decision.partial') },
    { value: 'planned', label: t('securityControls.decision.planned') },
    { value: 'apply_before', label: t('securityControls.decision.apply_before') },
    { value: 'abandoned', label: t('securityControls.decision.abandoned') },
];

const getTreatmentOptions = (t) => [
    { value: 'reduce', label: t('securityControls.treatment.reduce') },
    { value: 'transfer', label: t('securityControls.treatment.transfer') },
    { value: 'avoid', label: t('securityControls.treatment.avoid') },
    { value: 'accept', label: t('securityControls.treatment.accept') },
];

// Function to get badge color and style for measure types
const getMeasureTypeBadge = (measureType) => {
    if (!measureType) return null;

    const typeConfig = {
        'gouvernance et anticipation': {
            color: 'bg-blue-100 text-blue-800 border-blue-200',
            label: 'Gouvernance'
        },
        'protection': {
            color: 'bg-green-100 text-green-800 border-green-200',
            label: 'Protection'
        },
        'défense': {
            color: 'bg-orange-100 text-orange-800 border-orange-200',
            label: 'Défense'
        },
        'résilience': {
            color: 'bg-purple-100 text-purple-800 border-purple-200',
            label: 'Résilience'
        }
    };

    return typeConfig[measureType] || null;
};

const getSeverityOptions = (t) => [
    { value: 'minor', label: `${t('securityControls.severity.minor')} (G1)` },
    { value: 'moderate', label: `${t('securityControls.severity.moderate')} (G2)` },
    { value: 'major', label: `${t('securityControls.severity.major')} (G3)` },
    { value: 'critical', label: `${t('securityControls.severity.critical')} (G4)` },
    { value: 'catastrophic', label: `${t('securityControls.severity.catastrophic')} (G5)` },
];

const SEVERITY_LEVEL_MAP = { 'minor': 1, 'moderate': 2, 'major': 3, 'critical': 4, 'catastrophic': 5, };

// --- Modals ---
// Updated ControlSelectionModal to handle loading/error states and use fetched controls
const ControlSelectionModal = ({ isOpen, onClose, modalAvailableControls, initialSelectedIds, onSave, isLoading, error }) => {
    const { t } = useTranslation();
    const [currentSelection, setCurrentSelection] = useState(new Set(initialSelectedIds || []));
    const [filterInfo, setFilterInfo] = useState(null);

    useEffect(() => {
        // Reset selection when initialSelectedIds change (modal reopens for different context)
        setCurrentSelection(new Set(initialSelectedIds || []));
    }, [initialSelectedIds]);

    // Extract filter information from the context when controls are loaded
    useEffect(() => {
        if (!isLoading && modalAvailableControls && modalAvailableControls.length > 0) {
            // Get unique pillars and treatment strategies from the available controls
            const pillars = [...new Set(modalAvailableControls.map(c => c.pillar).filter(Boolean))];
            const strategies = [...new Set(modalAvailableControls.map(c => c.risk_treatment_strategy).filter(Boolean))];

            setFilterInfo({ pillars, strategies });
        }
    }, [isLoading, modalAvailableControls]);

    const handleToggle = (controlId) => {
        setCurrentSelection(prev => {
            const ns = new Set(prev);
            if (ns.has(controlId)) {
                ns.delete(controlId);
            } else {
                ns.add(controlId);
            }
            return ns;
        });
    };

    const handleSave = () => {
        onSave(Array.from(currentSelection));
        onClose(); // Close modal after saving
    };

    if (!isOpen) return null;

    return (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4 print:hidden">
            <div className="bg-white rounded-lg shadow-xl p-6 w-full max-w-lg relative"> {/* Increased max-width */}
                <button onClick={onClose} className="absolute top-2 right-2 text-gray-500 hover:text-gray-700"><X size={20} /></button>
                <h3 className="text-lg font-medium text-gray-900 mb-2">{t('securityControls.modal.addControls')}</h3>

                {/* Filter information display */}
                {filterInfo && (
                    <div className="mb-4 text-xs bg-blue-50 p-2 rounded border border-blue-100">
                        <p className="font-medium text-blue-700 mb-1">{t('securityControls.status.controlsFiltered')}</p>
                        {filterInfo.pillars.length > 0 && (
                            <p className="text-blue-600">
                                <span className="font-medium">{t('securityControls.status.pillarOfEvent')}</span> {filterInfo.pillars[0]}
                            </p>
                        )}
                        {filterInfo.strategies.length > 0 && (
                            <p className="text-blue-600">
                                <span className="font-medium">{t('securityControls.status.treatmentStrategy')}</span> {filterInfo.strategies[0]}
                            </p>
                        )}
                    </div>
                )}

                <div className="space-y-3 max-h-80 overflow-y-auto pr-2 mb-6 border border-gray-200 rounded p-3 bg-gray-50 min-h-[100px]"> {/* Increased max-height */}
                    {isLoading && (
                        <div className="flex items-center justify-center h-full text-gray-500">
                            <RefreshCw size={18} className="animate-spin mr-2" />
                            <span>{t('securityControls.status.loadingControls')}</span>
                        </div>
                    )}
                    {error && (
                        <div className="p-3 bg-red-50 border border-red-200 rounded-md text-sm text-red-700 flex items-center">
                            <X size={16} className="text-red-500 mr-2 flex-shrink-0" />
                            <span>{t('securityControls.modal.error')} {error}</span>
                        </div>
                    )}
                    {!isLoading && !error && modalAvailableControls && modalAvailableControls.length === 0 && (
                         <p className="text-sm text-gray-400 text-center py-4 italic">{t('securityControls.status.noControlsFound')}</p>
                    )}
                    {!isLoading && !error && modalAvailableControls && modalAvailableControls.length > 0 && (
                        modalAvailableControls.map(control => (
                            <div key={control.id || control._id} className="flex items-center p-2 bg-white rounded border border-gray-200 hover:border-blue-300 hover:bg-blue-50 transition-colors">
                                <input
                                    type="checkbox"
                                    id={`modal-ctrl-${control.id || control._id}`}
                                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded mr-3 shrink-0"
                                    checked={currentSelection.has(control.id || control._id)}
                                    onChange={() => handleToggle(control.id || control._id)}
                                />
                                <label htmlFor={`modal-ctrl-${control.id || control._id}`} className="text-sm text-gray-700 cursor-pointer flex flex-col">
                                    <div className="flex items-center space-x-2 mb-1">
                                        <span className="font-medium">{control.measure_description || control.name}</span>
                                        {control.measure_type && getMeasureTypeBadge(control.measure_type) && (
                                            <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium border ${getMeasureTypeBadge(control.measure_type).color}`}>
                                                {getMeasureTypeBadge(control.measure_type).label}
                                            </span>
                                        )}
                                    </div>
                                    <span className="text-xs text-gray-500">Pilier: {control.pillar || 'Non spécifié'} | Traitement: {control.risk_treatment_strategy || 'Non spécifié'}</span>
                                </label>
                            </div>
                        ))
                    )}
                </div>

                <div className="flex justify-end space-x-3">
                    <button onClick={onClose} className="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 text-sm">{t('securityControls.buttons.cancel')}</button>
                    <button onClick={handleSave} className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 text-sm">{t('securityControls.buttons.addSelection')}</button>
                </div>
            </div>
        </div>
    );
};

 const AiSuggestionModal = ({ isOpen, onClose, context, onGenerate, suggestions, isLoading, error, onSelectSuggestion, selectedSuggestions, onAddSelected }) => {
     const { t } = useTranslation();
     if (!isOpen || !context) return null;
     const { rule, de } = context;
     return (
         <div className="fixed inset-0 bg-black bg-opacity-60 flex items-center justify-center z-[60] p-4 print:hidden">
             <motion.div initial={{ opacity: 0, y: -20 }} animate={{ opacity: 1, y: 0 }} exit={{ opacity: 0, y: -20 }} className="bg-white rounded-lg shadow-xl p-6 w-full max-w-lg relative">
                 <button onClick={onClose} className="absolute top-3 right-3 text-gray-400 hover:text-gray-600 p-1 rounded-full hover:bg-gray-100 transition-colors" aria-label="Close modal">
                     <X size={22} />
                 </button>
                 <h3 className="text-xl font-semibold text-gray-800 mb-1">{t('securityControls.modal.aiSuggestions')}</h3>
                 <p className="text-sm text-gray-500 mb-4">
                     {t('securityControls.modal.forRule')} <span className="font-medium text-gray-700">{rule?.name}</span><br/>
                     {t('securityControls.modal.linkedToEvent')} <span className="font-medium text-gray-700">{de?.name}</span> ({t('securityControls.sections.severity')}: {getSeverityLevelInfo(de?.severity, t).label})
                 </p>
                 <div className="mb-5">
                     <button onClick={onGenerate} disabled={isLoading} className="w-full flex items-center justify-center px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors text-sm font-medium shadow-sm">
                         {isLoading ? (
                             <><RefreshCw size={16} className="animate-spin mr-2" /> {t('securityControls.status.generating')}</>
                         ) : (
                             <><BrainCircuit size={16} className="mr-1.5"/> {t('securityControls.modal.generateSuggestions')}</>
                         )}
                     </button>
                     {error && (
                         <div className="mt-2 p-3 bg-red-50 border border-red-200 rounded-md text-sm text-red-700 flex items-center">
                             <X size={16} className="text-red-500 mr-2 flex-shrink-0" />
                             <span>{error}</span>
                         </div>
                     )}
                 </div>
                 <div className="mb-5 min-h-[150px] max-h-[300px] overflow-y-auto border border-gray-200 rounded-md p-3 bg-gray-50 shadow-inner">
                     <h4 className="text-sm font-medium text-gray-600 mb-2">{t('securityControls.status.suggestionsGenerated')}</h4>
                     {isLoading && !suggestions.length && (
                         <div className="flex items-center justify-center h-full text-gray-500"><RefreshCw size={18} className="animate-spin mr-2" /><span>{t('securityControls.buttons.loading')}</span></div>
                     )}
                     {!isLoading && !suggestions.length && !error && (
                         <p className="text-sm text-gray-400 text-center py-4 italic">{t('securityControls.status.noSuggestions')}</p>
                     )}
                     {!isLoading && suggestions.length > 0 && (
                         <div className="space-y-2">
                             {suggestions.map((suggestion, index) => {
                                 // Handle both old format (strings) and new format (objects)
                                 const suggestionName = typeof suggestion === 'string' ? suggestion : suggestion.name;
                                 const measureType = typeof suggestion === 'object' ? suggestion.measure_type : null;
                                 const isSelected = selectedSuggestions.some(selected => {
                                     const selectedKey = typeof selected === 'string' ? selected : selected.name;
                                     return selectedKey === suggestionName;
                                 });

                                 return (
                                     <label key={index} htmlFor={`ai-ctrl-${index}`} className="flex items-start p-2.5 bg-white rounded border border-gray-200 hover:border-indigo-300 hover:bg-indigo-50 transition-colors cursor-pointer shadow-sm">
                                         <input
                                             type="checkbox"
                                             id={`ai-ctrl-${index}`}
                                             className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded mr-3 shrink-0 mt-0.5"
                                             checked={isSelected}
                                             onChange={() => onSelectSuggestion(suggestion)}
                                         />
                                         <div className="flex-1">
                                             <div className="flex flex-col space-y-1">
                                                 <span className="text-sm text-gray-800">{suggestionName}</span>
                                                 {measureType && getMeasureTypeBadge(measureType) && (
                                                     <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium border w-fit ${getMeasureTypeBadge(measureType).color}`}>
                                                         {getMeasureTypeBadge(measureType).label}
                                                     </span>
                                                 )}
                                             </div>
                                         </div>
                                     </label>
                                 );
                             })}
                         </div>
                     )}
                 </div>
                 <div className="flex justify-end space-x-3 pt-3 border-t border-gray-200">
                     <button onClick={onClose} className="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 text-sm">{t('securityControls.buttons.cancel')}</button>
                     <button onClick={onAddSelected} disabled={selectedSuggestions.length === 0} className="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 text-sm disabled:opacity-50">
                         {t('securityControls.buttons.addSelection')}
                     </button>
                 </div>
             </motion.div>
         </div>
     );
 };


// --- Main Component ---
const SecurityControls = ({ securityControls, setSecurityControls, handleSaveData }) => {
    const { t } = useTranslation();
    // --- State Hooks ---
  const {
    currentAnalysisControlPlan,
    saveCurrentAnalysisControlPlan,
    allControls,
    currentSecurityFramework,
    currentBusinessValues,
    currentDreadedEvents,
    currentAnalysis
  } = useAnalysis();

  // Removed sensitive data logging for security

    // Ref to prevent useEffect reset immediately after save
    const justSaved = useRef(false);

    // Local state for UI interactions
    const [selectedControlsForRuleDE, setSelectedControlsForRuleDE] = useState({}); // { [ruleId_deId]: [controlObject1, controlObject2,...] } <- Store objects
    const [residualSeverities, setResidualSeverities] = useState({}); // { [deId]: 'severity' }
    const [responsiblePersons, setResponsiblePersons] = useState({}); // { [ruleId_deId]: { [controlId]: 'name' } }
    const [implementationDecisions, setImplementationDecisions] = useState({}); // { [ruleId_deId]: { [controlId]: { decision: 'value', date: 'YYYY-MM-DD' } } }
    const [treatmentOptions, setTreatmentOptions] = useState({}); // { [ruleId_deId]: 'treatmentValue' }

  const [isControlModalOpen, setIsControlModalOpen] = useState(false);
    const [modalContext, setModalContext] = useState(null); // { ruleId, deId }

    // State for Control Selection Modal Data
    const [modalAvailableControls, setModalAvailableControls] = useState([]);
    const [isModalLoading, setIsModalLoading] = useState(false);
    const [modalError, setModalError] = useState(null);


    // State for AI Suggestions Modal
    const [isAiSuggestionModalOpen, setIsAiSuggestionModalOpen] = useState(false);
    const [aiModalContext, setAiModalContext] = useState(null); // { rule, de }
    const [aiSuggestionsList, setAiSuggestionsList] = useState([]); // Array of suggestion strings
    const [isLoadingAiSuggestions, setIsLoadingAiSuggestions] = useState(false);
    const [aiSuggestionError, setAiSuggestionError] = useState(null);
    const [selectedAiSuggestionNames, setSelectedAiSuggestionNames] = useState([]); // Array of selected suggestion strings

    // Loading state for the main save button
    const [isLoadingSave, setIsLoadingSave] = useState(false);

    const printRef = useRef(); // Ref for print target

    // --- Data Derivation --- (useMemo to optimize)
    // REMOVED: const rules = useMemo(() => {
    //     // Get rules from the 'definitions' property if framework exists
    //     // return currentSecurityFramework?.definitions || [];
    // }, [currentSecurityFramework]);

    // ---> NEW: Calculate linked rule/event pairs based on SecurityFramework links <---
    console.log('SecurityControls: Using data from AnalysisContext:', {
        currentSecurityFramework,
        currentBusinessValues,
        currentDreadedEvents
    });

    // Extract plan data from securityControls and AnalysisContext
    const linkedPairs = useMemo(() => {
        const pairs = [];

        // If we have plan data, use that to create pairs
        const planObject = currentAnalysisControlPlan?.planData?.plan;
        if (planObject && typeof planObject === 'object' && Object.keys(planObject).length > 0) {
            console.log("SC: Using currentAnalysisControlPlan data to create pairs");

            // Extract pairs from plan keys (ruleId_dreadedEventId)
            Object.keys(planObject).forEach(key => {
                const [ruleId, deIdStr] = key.split('_');
                if (ruleId && deIdStr) {
                    const deId = Number(deIdStr);
                    console.log(`SC: Found pair in plan: Rule ${ruleId} - Event ${deId}`);

                    // Try to find the actual rule and event objects first
                    let rule = null;
                    let de = null;

                    // Find the rule in the security framework
                    if (currentSecurityFramework?.selectedRules) {
                        Object.values(currentSecurityFramework.selectedRules).forEach(rulesInFramework => {
                            if (Array.isArray(rulesInFramework)) {
                                const foundRule = rulesInFramework.find(r => r.id === ruleId);
                                if (foundRule) rule = foundRule;
                            }
                        });
                    }

                    // Find the dreaded event
                    if (currentDreadedEvents) {
                        de = currentDreadedEvents.find(event => String(event.id) === String(deId));
                    }

                    // Use actual objects if found, otherwise create dummy objects
                    if (!rule) {
                        rule = { id: ruleId, name: `Règle ${ruleId}` };
                    }
                    if (!de) {
                        de = { id: deId, name: `Événement ${deId}` };
                    }

                    pairs.push({ rule, de });
                }
            });

            console.log("SC: Created pairs from currentAnalysisControlPlan data:", pairs);
            return pairs;
        }

        // Fallback to using ruleDreadedEvents if no plan data
        const selectedRuleMap = new Map(); // Map rule.id (string) to rule object
        const ruleLinks = currentSecurityFramework?.ruleDreadedEvents || {}; // { ruleId: [deId1, deId2], ... }
        const allEventsMap = new Map((currentDreadedEvents || []).map(de => [String(de.id), de])); // Map de.id (string) to de object

        // Populate selectedRuleMap from selectedRules (which is grouped by frameworkId)
        // Ensure we handle both _id (from DB) and id (temporary) for rules within selectedRules
        if (currentSecurityFramework?.selectedRules) {
            Object.values(currentSecurityFramework.selectedRules).forEach(rulesInFramework => {
                if (Array.isArray(rulesInFramework)) {
                    rulesInFramework.forEach(rule => {
                        const ruleId = rule?.id; // Use the consistent 'id' property established in SecurityFramework state
                        if (rule && ruleId) {
                             selectedRuleMap.set(String(ruleId), rule); // Use string ID for consistency
                        }
                    });
                }
            });
        }
        console.log("SC: Built selectedRuleMap:", selectedRuleMap);
        console.log("SC: Using ruleLinks:", ruleLinks);
        console.log("SC: Using allEventsMap:", allEventsMap);
        console.log("SC: Using currentDreadedEvents:", currentDreadedEvents);
        console.log("SC: Using currentSecurityFramework:", currentSecurityFramework);

        // Iterate through the links stored in ruleDreadedEvents
        for (const ruleIdStr in ruleLinks) {
            const linkedDeIds = ruleLinks[ruleIdStr];
            const rule = selectedRuleMap.get(ruleIdStr); // Find the corresponding *selected* rule object using its ID

            // Only proceed if the rule was found in the selected rules map AND it has linked DEs
            if (rule && Array.isArray(linkedDeIds) && linkedDeIds.length > 0) {
                 linkedDeIds.forEach(deId => {
                    const de = allEventsMap.get(String(deId)); // Find the corresponding DE object using its ID
                    if (de) {
                        pairs.push({ rule, de }); // Add the valid pair
                    } else {
                        console.warn(`SecurityControls: Linked DE with ID ${deId} for rule ${rule.name} (ID: ${ruleIdStr}) not found in currentDreadedEvents.`);
                    }
                });
            } else if (!rule && Array.isArray(linkedDeIds) && linkedDeIds.length > 0) {
                 // This might happen if ruleDreadedEvents contains links for rules
                 // that are no longer selected/present in selectedRules state. Log it.
                 console.warn(`SecurityControls: Rule with ID ${ruleIdStr} found in ruleDreadedEvents but not in selectedRules map. Skipping.`);
            }
        }

        // console.log("SC: Calculated linkedPairs:", pairs); // Debug log
        // ---> ADD Sorting by Rule Name/ID <---
        pairs.sort((a, b) => {
            const ruleNameA = a.rule?.name?.toLowerCase() || String(a.rule?.id);
            const ruleNameB = b.rule?.name?.toLowerCase() || String(b.rule?.id);
            if (ruleNameA < ruleNameB) return -1;
            if (ruleNameA > ruleNameB) return 1;
            // Optional: secondary sort by DE name if rules are the same
            const deNameA = a.de?.name?.toLowerCase() || '';
            const deNameB = b.de?.name?.toLowerCase() || '';
            return deNameA.localeCompare(deNameB);
        });

        console.log("SC: Sorted linkedPairs:", pairs); // Debug log after sorting

        return pairs;

    }, [currentAnalysisControlPlan, currentSecurityFramework?.selectedRules, currentSecurityFramework?.ruleDreadedEvents, currentDreadedEvents]); // Dependencies


    // --- Effect to initialize local state from loaded plan ---
    useEffect(() => {
        // Only run the reset logic if we didn't just save from this component
        if (justSaved.current) {
            console.log("SC Effect: Skipping state reset because save was just initiated.");
            // Reset the flag *after* skipping, so subsequent loads work
            // justSaved.current = false; // Let the finally block in save handle this
            return;
        }

        console.log("SC Effect: Running with received plan object:", currentAnalysisControlPlan);
        // ---> ADD Log for the actual plan object being processed <---
        const planObject = currentAnalysisControlPlan?.planData?.plan;
        console.log("SC Effect: Extracted planData.plan:", planObject);

        // Ensure planObject exists, is an object, and allControls are loaded
        if (planObject && typeof planObject === 'object' && allControls) {

            // Use the nested plan directly
            const plan = planObject;

            const newSelectedControls = {};
            const newResidualSeverities = {};
            const newResponsiblePersons = {};
            const newImplementationDecisions = {};
            const newTreatmentOptions = {};

            Object.entries(plan).forEach(([key, entry]) => {
                // Treatment Option
        if (entry.treatmentOption !== undefined && entry.treatmentOption !== null) {
                    newTreatmentOptions[key] = entry.treatmentOption;
                }
                // Residual Severity
                if (entry.residualSeverity !== undefined && entry.residualSeverity !== null) {
                    newResidualSeverities[key] = entry.residualSeverity;
                }

                // Controls, Responsible, Decision
        if (entry.controls && entry.controls.length > 0) {
                    // Map saved controls to full control objects
                    newSelectedControls[key] = entry.controls.map(savedControl => {
                       // Basic check for controlId existence before proceeding
                       if (!savedControl.controlId) {
                           console.warn("Saved control entry missing controlId:", savedControl, "for key:", key);
                           return null; // Skip this invalid entry
                       }
                       const fullControl = allControls.find(c => c.id === savedControl.controlId);
                       if (!fullControl && !savedControl.isNewSuggestion) {
                           console.warn(`Control definition not found in allControls for ID: ${savedControl.controlId}`);
                           // Still return a basic object so responsible/decision might be linked
                       }
                       return {
                          id: savedControl.controlId,
                          name: savedControl.name || fullControl?.name || `Contrôle ${savedControl.controlId.substring(0, 6)}...`,
                          measure_type: savedControl.measure_type || fullControl?.measure_type || null, // Restore measure type
                          isNewSuggestion: savedControl.isNewSuggestion || false,
                          description: fullControl?.description || '',
                          type: fullControl?.type || (savedControl.isNewSuggestion ? 'AI' : 'Inconnu'),
                       };
                    }).filter(Boolean); // Filter out nulls from invalid entries

                    // Extract responsible/decision info
          const responsiblesForThisKey = {};
          const decisionsForThisKey = {};
          entry.controls.forEach(control => {
                        // Ensure control.controlId exists before using it as a key
                        if (control.controlId) {
            if (control.responsiblePerson) {
              responsiblesForThisKey[control.controlId] = control.responsiblePerson;
            }
            if (control.decision) {
              decisionsForThisKey[control.controlId] = {
                decision: control.decision,
                date: control.decisionDate || null
              };
                            }
            }
          });
          if (Object.keys(responsiblesForThisKey).length > 0) {
                        newResponsiblePersons[key] = responsiblesForThisKey;
          }
          if (Object.keys(decisionsForThisKey).length > 0) {
                        newImplementationDecisions[key] = decisionsForThisKey;
          }
        }
      });

            // Set all states at once
            console.log("SC Effect: Setting derived states:", { newTreatmentOptions, newResidualSeverities, newResponsiblePersons, newImplementationDecisions, newSelectedControls });
            setTreatmentOptions(newTreatmentOptions);
            setResidualSeverities(newResidualSeverities);
            setResponsiblePersons(newResponsiblePersons);
            setImplementationDecisions(newImplementationDecisions);
            setSelectedControlsForRuleDE(newSelectedControls); // This holds the control list for rendering

    } else {
            console.log("SC Effect: Conditions not met, resetting states.", {
                planObjectExists: !!planObject,
                isPlanObject: typeof planObject === 'object',
                allControlsExists: !!allControls
            });
            // Reset states if conditions aren't met
            setTreatmentOptions({});
       setResidualSeverities({});
       setResponsiblePersons({});
       setImplementationDecisions({});
            setSelectedControlsForRuleDE({});
        }
    }, [currentAnalysisControlPlan, allControls]); // Depend on allControls as well


    // --- Handler Functions ---

    // Print Handler (Revised to print only the table)
    const handlePrint = () => {
        const printArea = printRef.current;
        if (!printArea) {
            console.error("Print area ref not found.");
            showErrorToast("Erreur: Impossible de trouver la zone d'impression.");
            return;
        }

        const tableToPrint = printArea.querySelector('#security-controls-table');
        if (!tableToPrint) {
            console.error("Table with ID 'security-controls-table' not found within print area.");
            showErrorToast("Erreur: Tableau non trouvé pour l'impression.");
            return;
        }

        // Clone the table to avoid modifying the original
        const tableClone = tableToPrint.cloneNode(true);

        const printWindow = window.open('', '_blank');
        printWindow.document.write('<html><head><title>Plan de Traitement des Risques</title>');

        // Inject print-specific styles directly
        printWindow.document.write(`
            <style>
                /* Basic setup */
                body { font-family: sans-serif; margin: 1cm; -webkit-print-color-adjust: exact !important; color-adjust: exact !important; }
                /* Table styling */
                table { width: 100%; border-collapse: collapse !important; font-size: 8pt !important; table-layout: fixed; }
                th, td {
                    border: 1px solid #ccc !important;
                    padding: 3px 0 !important; /* Vertical padding only */
                    vertical-align: top !important;
                    word-wrap: break-word;
                    text-align: left;
                    box-sizing: border-box !important; /* Contain background */
                }
                th { background-color: #e2e8f0 !important; font-weight: 600 !important; color: #2d3748 !important; text-align: center; }
                thead { display: table-header-group; } /* Repeat header */
                tr { page-break-inside: avoid !important; }

                /* Severity cell colors */
                .bg-black { background-color: black !important; color: white !important; }
                .bg-red-600 { background-color: #DC2626 !important; color: white !important; }
                .bg-yellow-500 { background-color: #F59E0B !important; color: black !important; }
                .bg-yellow-200 { background-color: #FDE68A !important; color: black !important; }
                .bg-cyan-300 { background-color: #67E8F9 !important; color: black !important; }
                .bg-gray-200 { background-color: #E5E7EB !important; color: #1F2937 !important; }
                .bg-gray-100 { background-color: #F3F4F6 !important; }

                /* Text colors needed for contrast */
                .text-white { color: white !important; }
                .text-black { color: black !important; }
                .text-gray-800 { color: #1F2937 !important; }
                .text-gray-500 { color: #6B7280 !important; }
                .text-blue-600 { color: #2563EB !important; }
                .text-indigo-600 { color: #4F46E5 !important; }

                /* Input/Select styling for print */
                input[type="text"],
                select,
                input[type="date"] {
                     border: none !important; background-color: transparent !important;
                     padding: 0 !important; margin: 0 !important; box-shadow: none !important;
                     width: 95%; font-size: 8pt !important; color: #000 !important;
                     -webkit-appearance: none; -moz-appearance: none; appearance: none;
                }
                select { width: auto; }

                /* Hide elements marked with .print-internal-hidden inside the table if necessary */
                .print-internal-hidden { display: none !important; }

                 /* Specific overrides for nested table */
                 td table { font-size: inherit !important; }
                 td table td, td table th { padding: 1px 2px !important; border-top: 1px solid #eee !important; border-bottom: 0 !important; border-left: 0 !important; border-right: 0 !important; }
                 td table thead { display: none; } /* Hide nested thead */

                /* Input/Select styling for print - HIDE THEM */
                input[type="text"],
                select,
                input[type="date"] {
                     display: none !important; /* Hide inputs/selects */
                }
                /* Ensure print-only spans are visible */
                .print\\:inline { display: inline !important; }
                .print\\:block { display: block !important; }

                /* Hide elements marked with .print-internal-hidden inside the table if necessary */
                .print-internal-hidden { display: none !important; }
            </style>
        `);
        printWindow.document.write('</head><body>');

        // Optional: Add a title within the printed page
        printWindow.document.write('<h3>Plan de Traitement des Risques - Détails</h3>');

        // Append the cloned table HTML
        printWindow.document.write(tableClone.outerHTML);

        printWindow.document.write('</body></html>');
        printWindow.document.close(); // Necessary for some browsers

        // Use timeout to ensure content is loaded before printing
        setTimeout(() => {
            printWindow.focus(); // Focus the new window
            printWindow.print();
            // Show success toast
            showSuccessToast('Impression du plan de traitement des risques en cours...');
            // Keep the window open for preview or manual closing, or close automatically:
            // printWindow.close();
        }, 500); // Adjust delay if needed
    };

    // Control Selection Modal Handlers - Combined fetch and open logic
    const fetchAndOpenControlModal = async (ruleId, deId) => {
        if (!ruleId || !deId) {
            console.error("Cannot open control modal: Missing ruleId or deId");
            showErrorToast("Erreur: Contexte manquant pour ouvrir le modal.");
            return;
        }

        // Find the current rule and dreaded event objects
        const currentRule = linkedPairs.find(pair => pair.rule?.id === ruleId)?.rule;
        const currentDE = linkedPairs.find(pair => pair.de?.id === String(deId) || pair.de?.id === Number(deId))?.de;

        if (!currentRule || !currentDE) {
            console.error("Cannot find rule or dreaded event objects", { ruleId, deId });
            showErrorToast("Erreur: Impossible de trouver les informations de contexte.");
            return;
        }

        // Get the treatment option for this rule-DE pair
        const key = `${ruleId}_${deId}`;
        const currentTreatmentOption = treatmentOptions[key];

        // Get the security pillar directly from the dreaded event
        // Note: DreadedEvent has a single securityPillar property, not pillars (plural)
        const securityPillar = currentDE?.securityPillar || '';

        // Convert to array for consistent processing
        const securityPillars = securityPillar ? [securityPillar] : [];

        console.log('Context for control filtering:', {
            rule: currentRule.name,
            dreadedEvent: currentDE.name,
            treatmentOption: currentTreatmentOption,
            securityPillar,
            securityPillars
        });

        // Note: Security controls are filtered by the pillar of the dreaded event, not the business value

        setModalContext({ ruleId, deId });
        setIsModalLoading(true);
        setModalError(null);
        setModalAvailableControls([]); // Clear previous controls
        setIsControlModalOpen(true); // Open modal immediately to show loading state

        try {
            // Get controls (either from context or API)
            let availableControls = [];

            if (allControls && Array.isArray(allControls) && allControls.length > 0) {
                console.log('Using allControls from context:', allControls.length, 'controls available');
                availableControls = allControls;
            } else {
                // If allControls is empty, fetch directly from the API
                console.log('allControls is empty, fetching from API');
                const controls = await securityControlsService.getAvailableSecurityControls();

                if (controls && Array.isArray(controls) && controls.length > 0) {
                    console.log('Fetched', controls.length, 'controls from API');
                    availableControls = controls;
                } else {
                    console.warn('No controls available from API');
                    throw new Error("Aucun contrôle disponible. Veuillez contacter l'administrateur.");
                }
            }

            // Filter controls based on security pillars and treatment option
            let filteredControls = availableControls;

            // Map treatment option values to risk_treatment_strategy values
            const treatmentOptionMap = {
                'reduce': 'Réduire le risque',
                'transfer': 'Transférer le risque',
                'avoid': 'Éviter le risque',
                'accept': 'Accepter le risque'
            };

            // Filter by treatment option if specified
            if (currentTreatmentOption && treatmentOptionMap[currentTreatmentOption]) {
                const treatmentStrategy = treatmentOptionMap[currentTreatmentOption];
                filteredControls = filteredControls.filter(control =>
                    control.risk_treatment_strategy === treatmentStrategy
                );
                console.log(`Filtered by treatment option '${currentTreatmentOption}':`, filteredControls.length, 'controls remaining');
            }

            // Filter by security pillar from the dreaded event (not the business value)
            if (securityPillar) {
                // Map from the dreaded event's securityPillar format to the control's pillar format
                const pillarMap = {
                    // Map from DreadedEvent.securityPillar to SecurityControl.pillar
                    'confidentialite': 'Confidentialité',
                    'integrite': 'Intégrité',
                    'disponibilite': 'Disponibilité',
                    'tracabilite': 'Traçabilité',
                    'preuve': 'Preuve',
                    'auditabilite': 'Auditabilité',
                    'Auditabilite': 'Auditabilité',
                    // Also include direct mappings in case the format is already correct
                    'Confidentialité': 'Confidentialité',
                    'Intégrité': 'Intégrité',
                    'Disponibilité': 'Disponibilité',
                    'Traçabilité': 'Traçabilité',
                    'Preuve': 'Preuve',
                    'Auditabilité': 'Auditabilité'
                };

                // Get the French pillar name
                const frenchPillar = pillarMap[securityPillar] || securityPillar;

                // Filter controls that match the specified pillar
                filteredControls = filteredControls.filter(control =>
                    control.pillar === frenchPillar
                );
                console.log(`Filtered by pillar '${frenchPillar}':`, filteredControls.length, 'controls remaining');
            }

            if (filteredControls.length === 0) {
                console.warn('No controls match the filtering criteria, showing all controls instead');
                // If no controls match the criteria, show all controls as a fallback
                setModalAvailableControls(availableControls);
                showInfoToast("Aucun contrôle ne correspond exactement aux critères. Tous les contrôles sont affichés.");
            } else {
                setModalAvailableControls(filteredControls);
            }
        } catch (error) {
            console.error("Error fetching controls for modal:", error);
            setModalError(error.message || "Impossible de charger les contrôles.");
            // Keep modal open to show error
        } finally {
            setIsModalLoading(false);
        }
    };


    // SAVES MANUAL SELECTIONS - Merges with existing AI suggestions
    // This function now uses modalAvailableControls to find the full object
    const handleSaveControlSelection = (newlySelectedManualIds) => {
      if (!modalContext) return;
      const { ruleId, deId } = modalContext;
      const key = `${ruleId}_${deId}`;

        // Get full control objects for the newly selected manual IDs FROM THE MODAL'S DATA SOURCE
        const newlySelectedManualControls = newlySelectedManualIds
            .map(id => modalAvailableControls.find(c => c.id === id || c._id === id)) // Use modalAvailableControls, check both id and _id
            .filter(Boolean)
            // Map the fetched control structure to the structure expected by setSelectedControlsForRuleDE
            .map(fetchedControl => {
                // Log the control structure to debug
                console.log('Processing control:', fetchedControl);

                return {
                    id: fetchedControl.id || fetchedControl._id, // Use id or _id
                    name: fetchedControl.measure_description || fetchedControl.name || 'Contrôle sans nom', // Try different field names
                    isNewSuggestion: false, // Mark as not an AI suggestion
                    description: `Pilier: ${fetchedControl.pillar || 'Non spécifié'}, Traitement: ${fetchedControl.risk_treatment_strategy || 'Non spécifié'}`, // Combine info for description
                    type: 'Database', // Indicate source
                };
            });

      setSelectedControlsForRuleDE(prev => {
          const newState = { ...prev };
            const existingControls = prev[key] || [];

            // Keep existing AI suggestions
            const existingAiSuggestions = existingControls.filter(c => c.isNewSuggestion);

            // Combine existing AI suggestions with the *newly selected* manual controls
            // Use a Map to handle potential duplicates if a manual control was somehow already added
            const combinedMap = new Map();
            existingAiSuggestions.forEach(c => combinedMap.set(c.id, c));
            newlySelectedManualControls.forEach(c => combinedMap.set(c.id, c)); // Overwrites if manual ID matches AI ID (unlikely but safe)

            newState[key] = Array.from(combinedMap.values());

            // Cleanup if empty
            if (newState[key].length === 0) {
                 delete newState[key];
            }

            console.log(`Updated controls for ${key} after manual save:`, newState[key]);
          return newState;
      });
      setModalContext(null);
        setIsControlModalOpen(false);
  };

    // Table Cell Input Handlers (Ensure these are defined)
    const handleResidualSeverityChange = (key, newSeverity) => {
        // Extract deId from the key
        const deId = key.split('_').pop(); // Assumes key format 'ruleId_deId'

        // Find the corresponding dreaded event
        const dreadedEvent = currentDreadedEvents.find(event => String(event.id) === String(deId));

        if (!dreadedEvent) {
            console.warn(`Could not find dreaded event with ID ${deId} for key ${key}`);
            // Optionally allow update anyway or handle error
            setResidualSeverities(prev => ({ ...prev, [key]: newSeverity }));
            return;
        }

        const initialSeverity = dreadedEvent.severity;

        // Get numerical values for comparison
        const initialSeverityValue = SEVERITY_LEVEL_MAP[initialSeverity?.toLowerCase()] || 0;
        const newSeverityValue = SEVERITY_LEVEL_MAP[newSeverity?.toLowerCase()] || 0;

        // Validate: New severity must be <= initial severity
        if (newSeverityValue <= initialSeverityValue) {
             setResidualSeverities(prev => ({ ...prev, [key]: newSeverity }));
        } else {
            console.warn(`Validation failed: Residual severity (${newSeverity} / ${newSeverityValue}) cannot be higher than initial severity (${initialSeverity} / ${initialSeverityValue}) for key ${key}.`);
             // Optionally, add user feedback here (e.g., alert, toast)
             // alert(`La sévérité résiduelle ne peut pas être supérieure à la sévérité initiale (${initialSeverity}).`);
        }
    };

    const handleResponsibleChange = (key, controlId, value) => {
    setResponsiblePersons(prev => ({
        ...prev,
        [key]: {
            ...(prev[key] || {}),
            [controlId]: value
        }
    }));
  };

    const handleDecisionChange = (key, controlId, decision, date = null) => {
        setImplementationDecisions(prev => ({
            ...prev,
            [key]: {
                ...(prev[key] || {}),
                [controlId]: {
                    decision,
                    date: decision === 'apply_before' ? date : null
                }
            }
        }));
    };

    const handleTreatmentOptionChange = (key, optionValue) => {
        setTreatmentOptions(prev => ({ ...prev, [key]: optionValue }));
    };

    // AI Suggestion Modal Handlers
    const handleOpenAiSuggestionModal = (rule, de) => {
        if (!rule || !de) { console.error("Missing context for AI modal."); return; }
        setAiModalContext({ rule, de });
        setIsAiSuggestionModalOpen(true);
        setAiSuggestionsList([]);
        setSelectedAiSuggestionNames([]);
        setIsLoadingAiSuggestions(false);
        setAiSuggestionError(null);
    };

    const handleCloseAiSuggestionModal = () => {
        setIsAiSuggestionModalOpen(false); // Other resets happen in handleAdd or handleOpen
    };

    const handleGenerateAiSuggestions = async () => {
        if (!aiModalContext) return;
        setIsLoadingAiSuggestions(true);
        setAiSuggestionsList([]);
        setSelectedAiSuggestionNames([]);
        setAiSuggestionError(null);

        const { rule, de } = aiModalContext;
        const key = `${rule.id}_${de.id}`;

        const treatment = treatmentOptions[key] || null;
         // Find the full business value object using the ID stored in the dreaded event
        const bv = currentBusinessValues?.find(b => String(b.id) === String(de.businessValue)); // Ensure comparison is robust
        const supportAssets = bv?.supportAssets || [];
        const existingControlsForKey = selectedControlsForRuleDE[key] || [];
        const existingControlNames = existingControlsForKey.map(c => c.name);

        const apiContext = {
          ruleName: rule.name,
            ruleDescription: rule.description,
          dreadedEventName: de.name,
            dreadedEventSeverity: de.severity,
            treatmentOption: treatment,
            businessValueName: bv?.name || null,
            businessValueDescription: bv?.description || null,
            supportAssetNames: supportAssets.map(sa => sa.name),
            existingControlNames: existingControlNames,
            language: 'fr'
        };

        // console.log("Sending ENHANCED context to AI service:", apiContext);

        try {
            const suggestions = await securityControlsService.getAiControlSuggestions(apiContext);
            // console.log("Received AI suggestions:", suggestions);
            // Filter out suggestions that *exactly* match existing control names (case-insensitive)
            const uniqueSuggestions = suggestions.filter(suggestion => {
                // Handle both old format (strings) and new format (objects)
                const suggestionName = typeof suggestion === 'string' ? suggestion : suggestion.name;
                return !existingControlNames.some(existingName =>
                    existingName.trim().toLowerCase() === suggestionName.trim().toLowerCase()
                );
            });
            setAiSuggestionsList(uniqueSuggestions || []);
            // if (uniqueSuggestions.length < suggestions.length) { console.log("Filtered out duplicate AI suggestions."); }
    } catch (error) {
            console.error("Error fetching AI suggestions:", error);
            setAiSuggestionError(error.message || "Erreur lors de la récupération des suggestions IA.");
            setAiSuggestionsList([]);
        } finally {
            setIsLoadingAiSuggestions(false);
        }
    };

    const handleSelectAiSuggestion = (suggestion) => {
        // Handle both old format (strings) and new format (objects)
        const suggestionKey = typeof suggestion === 'string' ? suggestion : suggestion.name;

        setSelectedAiSuggestionNames(prev => {
            // Check if this suggestion is already selected (by name for compatibility)
            const isAlreadySelected = prev.some(selected => {
                const selectedKey = typeof selected === 'string' ? selected : selected.name;
                return selectedKey === suggestionKey;
            });

            if (isAlreadySelected) {
                // Remove the suggestion
                return prev.filter(selected => {
                    const selectedKey = typeof selected === 'string' ? selected : selected.name;
                    return selectedKey !== suggestionKey;
                });
            } else {
                // Add the suggestion (keep original format)
                return [...prev, suggestion];
            }
        });
    };

    // ADDS SELECTED AI SUGGESTIONS - Merges with existing manual selections
    const handleAddSelectedAiControls = () => {
        if (!aiModalContext || selectedAiSuggestionNames.length === 0) return;

        const { rule, de } = aiModalContext;
        // Ensure the key uses rule.id to match the rendering logic and save logic
        const ruleIdForKey = rule?.id;
        const deIdForKey = de?.id;

        // Check if IDs are valid before creating the key
        if (!ruleIdForKey || !deIdForKey) {
            console.error("Cannot add AI controls: Missing rule or DE ID.", { rule, de });
            handleCloseAiSuggestionModal();
            return;
        }

        const key = `${ruleIdForKey}_${deIdForKey}`;
        console.log(`SC Add AI: Targeting key: ${key}`); // Debug log

        // Create new control *objects* for the AI suggestions
        // Handle both old format (strings) and new format (objects with name and measure_type)
        const newAiControlsToAdd = selectedAiSuggestionNames.map(suggestion => {
            // Check if suggestion is a string (old format) or object (new format)
            const suggestionName = typeof suggestion === 'string' ? suggestion : suggestion.name;
            const measureType = typeof suggestion === 'object' ? suggestion.measure_type : null;

            return {
                id: `ai_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`, // Unique temporary ID
                name: suggestionName,
                measure_type: measureType, // Add measure type if available
                isNewSuggestion: true,
                // Add default properties if needed for display/logic
                description: 'Suggestion générée par IA.',
                type: 'AI',
            };
        });
        console.log("SC Add AI: New AI controls to add:", newAiControlsToAdd); // Debug log

        setSelectedControlsForRuleDE(prev => {
            const newState = { ...prev };
            const existingControls = prev[key] || [];
            console.log(`SC Add AI: Existing controls for key ${key}:`, existingControls); // Debug log

            // Keep ALL existing controls (both manual and AI)
            // Use a Map to prevent adding duplicate AI suggestions (based on name) if button clicked multiple times
            const combinedMap = new Map();

            // First add all existing controls (both manual and AI)
            existingControls.forEach(c => combinedMap.set(c.id, c));

            // Add new AI suggestions, checking against existing names (case-insensitive)
            const existingNamesLower = new Set(existingControls.map(c => c.name.trim().toLowerCase()));
            newAiControlsToAdd.forEach(aiControl => {
                if (!existingNamesLower.has(aiControl.name.trim().toLowerCase())) {
                    combinedMap.set(aiControl.id, aiControl);
                } else {
                     console.log(`Skipping duplicate AI suggestion: ${aiControl.name}`);
                }
            });

            newState[key] = Array.from(combinedMap.values());

            // Cleanup if empty (shouldn't happen here as we check selectedAiSuggestionNames.length)
            // if (newState[key].length === 0) { delete newState[key]; }

            console.log(`SC Add AI: Updated controls for key ${key}:`, newState[key]); // Debug log
            return newState;
        });

        handleCloseAiSuggestionModal(); // Close modal after adding
    };


    // --- Global Save Handler ---
    const handleGlobalSave = async () => {
        setIsLoadingSave(true);
        justSaved.current = true; // Flag that a save was just initiated
        // console.log("Attempting to save control plan...");

        const planToSave = {};
         // ---> Iterate over the LINKED PAIRS instead of all rules/events <---
        linkedPairs.forEach(({ rule, de }) => {
            // Use rule.id (established in SecurityFramework) and de.id for consistency
            const ruleIdForKey = rule?.id;
            const deIdForKey = de?.id;

            // Skip if IDs are invalid (shouldn't happen based on linkedPairs calculation, but good practice)
            if (!ruleIdForKey || !deIdForKey) {
                 // Removed sensitive data logging for security
                 return; // Skip this pair
            }

            const key = `${ruleIdForKey}_${deIdForKey}`;

            const controlsForKey = selectedControlsForRuleDE[key]; // Array of objects
            const treatment = treatmentOptions[key];
            const residualSev = residualSeverities[key];

            // Save entry if there are controls, a treatment option, OR a residual severity defined
            if (controlsForKey?.length > 0 || treatment || residualSev) {
                planToSave[key] = {
                    ruleId: ruleIdForKey, // Use the consistent ID
                    dreadedEventId: deIdForKey,
                    treatmentOption: treatment || null,
                    residualSeverity: residualSev || null,
                    // Map control objects back to the structure expected by API/storage
                    controls: controlsForKey ? controlsForKey.map(control => ({
                        controlId: control.id, // Use the actual control ID (manual or temporary AI)
                        name: control.name,    // Save name for context
                        measure_type: control.measure_type || null, // Save measure type
                        isNewSuggestion: control.isNewSuggestion || false, // Persist AI suggestion flag
                        responsiblePerson: responsiblePersons?.[key]?.[control.id] || null,
                        decision: implementationDecisions?.[key]?.[control.id]?.decision || null,
                        decisionDate: implementationDecisions?.[key]?.[control.id]?.date || null,
                    })) : []
                };
            }
        });

        // Show loading toast
        const toastId = showLoadingToast(t('securityControls.buttons.saving'));

        try {
            // Removed sensitive data logging for security
            await saveCurrentAnalysisControlPlan({ plan: planToSave });
            // Removed sensitive data logging for security

            // Update loading toast to success
            updateToast(toastId, t('securityControls.status.planSaved'), 'success');
        } catch (error) {
            // Log without revealing sensitive data
            console.error("Error saving control plan");
            justSaved.current = false; // Reset flag on error

            // Update loading toast to error
            updateToast(toastId, t('securityControls.status.saveError'), 'error');
        } finally {
            setIsLoadingSave(false);
            // Reset the flag after a short delay to allow context to potentially update
            // and prevent unnecessary blocking if the user interacts quickly after save.
            setTimeout(() => { justSaved.current = false; }, 150);
        }
    };


    // --- Main Render ---\n
  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      className="bg-white p-6 rounded-lg shadow-sm border border-gray-200"
    >
      {/* === MERGED HEADER === */}
      <div className="bg-white rounded-xl shadow-sm border border-slate-100 p-4 mb-6 print:hidden">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          {/* Left Side: Breadcrumb & Title */}
          <div>
            <div className="flex items-center text-sm text-slate-500 mb-2">
              <span className="hover:text-blue-600 transition-colors">{t('securityControls.breadcrumb.workshop1')}</span>
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mx-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
              <span className="text-blue-600 font-medium">{t('securityControls.breadcrumb.securityControls')}</span>
            </div>
            <h1 className="text-2xl sm:text-3xl font-bold text-slate-800 flex items-center">
              <ShieldCheck size={28} className="mr-3 text-blue-600" />
              {t('securityControls.sections.riskTreatmentPlan')}
            </h1>
          </div>

          {/* Right Side: Action Buttons */}
          <div className="flex items-center space-x-3">
            {/* Print Button */}
            <button
              onClick={handlePrint}
              className="text-sm font-medium bg-slate-100 text-slate-700 px-4 py-2 rounded-lg hover:bg-slate-200 flex items-center shadow-sm transition duration-200"
            >
              <Printer size={16} className="mr-2" />
              {t('securityControls.buttons.print')}
            </button>

            {/* Save Button */}
            <button
              onClick={handleGlobalSave}
              disabled={isLoadingSave}
              className="text-sm font-medium bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 disabled:opacity-50 flex items-center shadow-sm transition duration-200"
            >
              {isLoadingSave ? (
                <RefreshCw size={16} className="animate-spin mr-2" />
              ) : (
                <Save size={16} className="mr-2" />
              )}
              {isLoadingSave ? t('securityControls.buttons.saving') : t('securityControls.buttons.save')}
            </button>
          </div>
        </div>
      </div>
      {/* === END MERGED HEADER === */}

      {/* Info Boxes - Hidden on Print */}
             <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6 print:hidden">
                 <div className="bg-blue-50 p-4 rounded-lg border border-blue-100 shadow-sm">
                     <div className="flex items-center text-blue-700"> <Info size={18} className="mr-2" /> <h3 className="font-medium">{t('securityControls.sections.securityRules')}</h3> </div>
                     <p className="text-blue-600 text-sm mt-1"> {linkedPairs && linkedPairs.length > 0 ? [...new Set(linkedPairs.map(pair => pair.rule?.id))].length : 0} {t('securityControls.status.rulesDefined')} </p>
                 </div>
                 <div className="bg-amber-50 p-4 rounded-lg border border-amber-100 shadow-sm">
                     <div className="flex items-center text-amber-700"> <Info size={18} className="mr-2" /> <h3 className="font-medium">{t('securityControls.sections.dreadedEvents')}</h3> </div>
                     <p className="text-amber-600 text-sm mt-1"> {linkedPairs && linkedPairs.length > 0 ? [...new Set(linkedPairs.map(pair => pair.de?.id))].length : 0} {t('securityControls.status.eventsIdentified')} </p>
                 </div>
                 <div className="bg-emerald-50 p-4 rounded-lg border border-emerald-100 shadow-sm">
                     <div className="flex items-center text-emerald-700"> <Info size={18} className="mr-2" /> <h3 className="font-medium">{t('securityControls.sections.identifiedControls')}</h3> </div>
                     <p className="text-emerald-600 text-sm mt-1"> {Object.values(selectedControlsForRuleDE).flat().length || 0} {t('securityControls.status.controlsIdentified')} </p>
                 </div>
        </div>

            {/* Main Table Container - Apply print styles here */}
            <div className="overflow-x-auto print-target" ref={printRef}>
                {/* Print-specific styles */}
                 <style>{`
                    @media print {
                        /* Basic setup */
                        body { -webkit-print-color-adjust: exact !important; color-adjust: exact !important; margin: 1cm; }
                        .print\\:hidden { display: none !important; }
                        .print-target { width: 100%; overflow: visible !important; }

                        /* Table styling */
                        #security-controls-table { width: 100%; border-collapse: collapse !important; font-size: 8pt !important; table-layout: fixed; }
                        #security-controls-table th, #security-controls-table td { border: 1px solid #ccc !important; padding: 3px 0 !important; vertical-align: top !important; word-wrap: break-word; text-align: left; }
                        #security-controls-table th { background-color: #e2e8f0 !important; font-weight: 600 !important; color: #2d3748 !important; text-align: center; }
                        #security-controls-table thead { display: table-header-group; } /* Repeat header */
                        #security-controls-table tr { page-break-inside: avoid !important; }
                        .print-page-break-before { page-break-before: always !important; }

                        /* Severity cell colors - Map Tailwind class names used in JS to actual colors */
                        /* Map background classes */
                        #security-controls-table .print\\:bg-black { background-color: black !important; }
                        #security-controls-table .print\\:bg-red-600 { background-color: #DC2626 !important; }
                        #security-controls-table .print\\:bg-yellow-500 { background-color: #F59E0B !important; }
                        #security-controls-table .print\\:bg-yellow-200 { background-color: #FDE68A !important; }
                        #security-controls-table .print\\:bg-cyan-300 { background-color: #67E8F9 !important; }
                        #security-controls-table .print\\:bg-gray-200 { background-color: #E5E7EB !important; }
                        #security-controls-table .print\\:bg-gray-100 { background-color: #F3F4F6 !important; }

                        /* Map text classes (important for contrast) */
                        #security-controls-table .print\\:text-white { color: white !important; }
                        #security-controls-table .print\\:text-black { color: black !important; }
                        #security-controls-table .print\\:text-gray-800 { color: #1F2937 !important; }


                        /* Input/Select styling for print - HIDE THEM */
                        input[type="text"],
                        select,
                        input[type="date"] {
                             display: none !important; /* Hide inputs/selects */
                        }
                        /* Ensure print-only spans are visible */
                        .print\\:inline { display: inline !important; }
                        .print\\:block { display: block !important; }

                        /* Hide elements marked with .print-internal-hidden inside the table if necessary */
                        .print-internal-hidden { display: none !important; }
                    }
                `}</style>

                <table id="security-controls-table" className="min-w-full border-collapse border border-gray-300">
                     <thead className="bg-gray-100">
                        <tr>
                            <th className="border px-3 py-2 text-left text-xs font-medium text-gray-600 uppercase tracking-wider print:py-1 print:px-1.5 w-[20%]">{t('securityControls.table.headers.rule')}</th>
                            <th className="border px-3 py-2 text-left text-xs font-medium text-gray-600 uppercase tracking-wider print:py-1 print:px-1.5 w-[15%]">{t('securityControls.table.headers.dreadedEvent')}</th>
                            <th className="border px-3 py-2 text-center text-xs font-medium text-gray-600 uppercase tracking-wider print:py-1 print:px-1.5 w-[10%]">{t('securityControls.table.headers.severity')}</th>
                            <th className="border px-3 py-2 text-center text-xs font-medium text-gray-600 uppercase tracking-wider print:py-1 print:px-1.5 w-[10%]">{t('securityControls.table.headers.treatment')}</th>
                            {/* Combined Column Header */}
                            <th className="border px-3 py-2 text-left text-xs font-medium text-gray-600 uppercase tracking-wider print:py-1 print:px-1.5 w-[35%]" colSpan="3">{t('securityControls.table.headers.controls')} ({t('securityControls.sections.controls')} / {t('securityControls.sections.responsible')} / {t('securityControls.sections.decision')})</th>
                            <th className="border px-3 py-2 text-center text-xs font-medium text-gray-600 uppercase tracking-wider print:py-1 print:px-1.5 w-[10%]">{t('securityControls.table.headers.residualSeverity')}</th>
                              </tr>
                        </thead>
                         <tbody className="bg-white divide-y divide-gray-200">
                        {/* ---> NEW: Iterate over linkedPairs <--- */}
                        {/* Map with index to check previous row */}
                        {linkedPairs && linkedPairs.length > 0 ? (
                            linkedPairs.map(({ rule, de }, index) => {

                                const ruleIdForKey = rule?.id || `rule-${index}`;
                                const deIdForKey = de?.id || `de-${index}`;
                                const key = `${ruleIdForKey}_${deIdForKey}`;

                                // ---> Check if it's the start of a new rule group <---
                                const isNewRuleGroup = index === 0 || (rule?.id && linkedPairs[index - 1]?.rule?.id !== rule.id);

                                // ---> Calculate rowSpan if it's a new group <---
                                let ruleRowSpan = 1;
                                if (isNewRuleGroup && rule?.id) {
                                    for (let i = index + 1; i < linkedPairs.length; i++) {
                                        if (linkedPairs[i]?.rule?.id === rule.id) {
                                            ruleRowSpan++;
                                        } else {
                                            break; // Stop counting when rule ID changes
                                        }
                                    }
                                }

                                const severityInfo = getSeverityLevelInfo(de.severity, t);
                                const residualSeverity = residualSeverities[key];
                                const residualSeverityInfo = residualSeverity ? getSeverityLevelInfo(residualSeverity, t) : { level: '', label: '', textClass: 'text-gray-500', bgClass: 'bg-gray-100' };
                                const selectedControls = selectedControlsForRuleDE[key] || []; // Array of objects
                                const currentTreatmentOption = treatmentOptions[key];

                                // Get initial severity value for filtering residual options
                                const initialSeverityValue = SEVERITY_LEVEL_MAP[de.severity?.toLowerCase()] || 0;
                                const severityOptions = getSeverityOptions(t);
                                const filteredSeverityOptions = severityOptions.filter(opt =>
                                    (SEVERITY_LEVEL_MAP[opt.value?.toLowerCase()] || 0) <= initialSeverityValue
                                );

                                // Map Tailwind class to a print-specific class
                                const printSeverityBgClass = `print:${severityInfo.bgClass}`;
                                const printSeverityTextClass = `print:${severityInfo.textClass}`;
                                const printResidualBgClass = `print:${residualSeverityInfo.bgClass}`;
                                const printResidualTextClass = `print:${residualSeverityInfo.textClass}`;

                                // Determine row background based on severity change
                                let rowBgClass = 'bg-white';
                                const initialSevMapVal = SEVERITY_LEVEL_MAP[de.severity?.toLowerCase()];
                                const residualSevMapVal = SEVERITY_LEVEL_MAP[residualSeverity?.toLowerCase()];

                                if (initialSevMapVal && residualSevMapVal) { // Only apply color if both are defined
                                    if (residualSevMapVal < initialSevMapVal) rowBgClass = 'bg-green-50 hover:bg-green-100';
                                    else if (residualSevMapVal > initialSevMapVal) rowBgClass = 'bg-red-50 hover:bg-red-100'; // Should be prevented by validation, but good visual cue
                                    else if (residualSevMapVal === initialSevMapVal) rowBgClass = 'bg-yellow-50 hover:bg-yellow-100';
                                } else {
                                    rowBgClass = 'bg-white hover:bg-gray-50'; // Default hover for rows without comparison
                                }
                                rowBgClass += ' transition-colors duration-150'; // Add transition

                                // Find linked Business Value Name (using de.businessValue ID)
                                const linkedBv = Array.isArray(currentBusinessValues)
                                    ? currentBusinessValues.find(bv => String(bv.id) === String(de.businessValue))
                                    : null;
                                const linkedBvName = linkedBv?.name;

                                return (
                                    // Each row is now a direct rule-event pair
                                    <tr key={key} className={`${rowBgClass}`}>
                                        {/* Conditionally render Rule cell with rowSpan */}
                                        {isNewRuleGroup && (
                                            <td className="border px-3 py-3 align-top print:py-1 print:px-1.5" rowSpan={ruleRowSpan}>
                                                <span className="font-medium text-sm text-gray-800" title={rule?.description}>{rule?.name || t('securityControls.status.unknownRule')}</span>
                                            </td>
                                        )}
                                        {/* Dreaded Event */}
                                        <td className="border px-3 py-3 align-top print:py-1 print:px-1.5">
                                            <span className="text-sm text-gray-700">{de.name}</span>
                                            {/* Display linked Business Value name if available */}
                                            {linkedBvName && (
                                                <p className="text-xs text-blue-600 mt-1 print:hidden">
                                                    ({t('securityControls.sections.value')}: {linkedBvName})
                                                </p>
                                            )}
                                        </td>
                                        {/* Initial Severity */}
                                        <td className={`border px-3 py-3 text-center align-middle print:py-1 print:px-1.5 ${severityInfo.bgClass} ${printSeverityBgClass}`}>
                                            <span className={`text-xs font-medium ${severityInfo.textClass} ${printSeverityTextClass}`}> {severityInfo.label} ({severityInfo.level}) </span>
                                        </td>
                                        {/* Treatment Option */}
                                        <td className="border px-3 py-3 align-top print:py-1 print:px-1.5">
                                            {/* Select for Screen */}
                                            <select
                                                value={currentTreatmentOption || ''}
                                                onChange={(e) => handleTreatmentOptionChange(key, e.target.value)}
                                                className="w-full p-1 border border-gray-300 rounded text-xs print:hidden"
                                            >
                                                <option value="" disabled>{t('securityControls.placeholders.selectTreatment')}</option>
                                                {getTreatmentOptions(t).map(opt => (<option key={opt.value} value={opt.value}>{opt.label}</option>))}
                                            </select>
                                            {/* Span for Print */}
                                            <span className="hidden print:inline text-xs">
                                                {getTreatmentOptions(t).find(opt => opt.value === currentTreatmentOption)?.label || t('securityControls.severity.undefined')}
                                            </span>
                                        </td>
                                        {/* --- Combined Controls/Responsible/Decision Column --- */}
                                        <td className="border px-1 py-1 align-top print:py-0.5 print:px-0.5" colSpan="3">
                                            <div className="flex items-center space-x-1 mb-1 print:hidden print-internal-hidden">
                                                {/* Pass rule.id and de.id directly - Updated onClick */}
                                                <button onClick={() => fetchAndOpenControlModal(rule?.id, de?.id)} className="p-1 bg-blue-100 text-blue-600 rounded hover:bg-blue-200" title={t('securityControls.buttons.addControlsFromDatabase')}> <PlusCircle size={14} /> </button>
                                                <button onClick={() => handleOpenAiSuggestionModal(rule, de)} className="p-1 bg-indigo-100 text-indigo-600 rounded hover:bg-indigo-200" title={t('securityControls.buttons.suggestControlsWithAI')}> <BrainCircuit size={14} /> </button>
                                            </div>
                                            {/* Render nested table ONLY if there are controls */}
                                            {selectedControls.length > 0 ? (
                                                <table className="w-full text-xs table-fixed">
                                                    {/* Header for nested table - REMOVED sr-only */}
                                                     <thead className=""><tr><th className="w-[40%] text-left font-medium text-gray-500 uppercase text-xs pb-1">{t('securityControls.sections.control')}</th><th className="w-[30%] text-left font-medium text-gray-500 uppercase text-xs pb-1">{t('securityControls.sections.responsible')}</th><th className="w-[30%] text-left font-medium text-gray-500 uppercase text-xs pb-1">{t('securityControls.sections.decision')}</th></tr></thead>
                                                    <tbody>
                                                        {selectedControls.map((control) => ( // control is now an object
                                                            <tr key={`${key}-${control.id}`} className="border-t border-gray-100 first:border-t-0 print:border-t-gray-300">
                                                                {/* Control Name */}
                                                                <td className="py-1 pr-1 align-top print:py-0.5">
                                                                    <div className="flex flex-col space-y-1">
                                                                        <span className={`text-xs ${control.isNewSuggestion ? 'italic text-indigo-600' : 'text-gray-700'}`}>{control.name}</span>
                                                                        {control.measure_type && getMeasureTypeBadge(control.measure_type) && (
                                                                            <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium border ${getMeasureTypeBadge(control.measure_type).color}`}>
                                                                                {getMeasureTypeBadge(control.measure_type).label}
                                                                            </span>
                                                                        )}
                                                                    </div>
                                                                </td>
                                                                {/* Responsible */}
                                                                <td className="py-1 px-1 align-top print:py-0.5">
                                                                    {/* Added print-only span */}
                                                                     <span className="hidden print:inline text-xs">{responsiblePersons?.[key]?.[control.id] || '-'}</span>
                                                                    <input type="text" value={responsiblePersons?.[key]?.[control.id] || ''} onChange={(e) => handleResponsibleChange(key, control.id, e.target.value)} placeholder={t('securityControls.placeholders.who')} className="w-full p-0.5 border border-gray-300 rounded text-xs print:hidden" />
                                                                </td>
                                                                {/* Decision */}
                                                                <td className="py-1 pl-1 align-top print:py-0.5">
                                                                    <div className="flex flex-col space-y-0.5">
                                                                        {/* Select for Screen */}
                                                                        <select
                                                                            value={implementationDecisions?.[key]?.[control.id]?.decision || ''}
                                                                            onChange={(e) => handleDecisionChange(key, control.id, e.target.value, implementationDecisions?.[key]?.[control.id]?.date)}
                                                                            className="w-full p-0.5 border border-gray-300 rounded text-xs print:hidden"
                                                                        >
                                                                            <option value="" disabled>{t('securityControls.placeholders.selectDecision')}</option>
                                                                            {getDecisionOptions(t).map(opt => (<option key={opt.value} value={opt.value}>{opt.label}</option>))}
                                                                        </select>
                                                                        {/* Span for Print */}
                                                                        <span className="hidden print:inline text-xs">
                                                                            {getDecisionOptions(t).find(opt => opt.value === implementationDecisions?.[key]?.[control.id]?.decision)?.label || t('securityControls.severity.undefined')}
                                                                            {implementationDecisions?.[key]?.[control.id]?.decision === 'apply_before' && implementationDecisions?.[key]?.[control.id]?.date ?
                                                                                ` (${implementationDecisions?.[key]?.[control.id]?.date})` : ''}
                                                                        </span>

                                                                        {/* Date Input for Screen (Conditional) */}
                                                                        {implementationDecisions?.[key]?.[control.id]?.decision === 'apply_before' && (
                                                                            <input
                                                                                type="date"
                                                                                value={implementationDecisions?.[key]?.[control.id]?.date || ''}
                                                                                onChange={(e) => handleDecisionChange(key, control.id, 'apply_before', e.target.value)}
                                                                                className="w-full p-0.5 border border-gray-300 rounded text-xs mt-0.5 print:hidden"
                                                                            />
                                                                        )}
                                                                    </div>
                                                                </td>
                                                            </tr>
                                                        ))}
                                                    </tbody>
                                                </table>
                                            ) : (
                                                 <div className="text-center text-xs text-gray-500 italic py-1 print:hidden print-internal-hidden"> {t('securityControls.status.noControlsSelected')} </div>
                                            )}
                                        </td>
                                        {/* Residual Severity */}
                                        <td className={`border px-3 py-3 text-center align-middle print:py-1 print:px-1.5 ${residualSeverityInfo.bgClass} ${printResidualBgClass}`}>
                                            {/* Select for Screen */}
                                            <select
                                                value={residualSeverity || ''}
                                                onChange={(e) => handleResidualSeverityChange(key, e.target.value)}
                                                className={`w-full p-1 border rounded text-xs print:hidden font-medium ${residualSeverityInfo.textClass} ${residualSeverityInfo.bgClass}`}
                                                // Use explicit background color for the select itself to override row color
                                                style={{ backgroundColor: residualSeverityInfo.bgClass.startsWith('bg-') ? undefined : '#ffffff' }} // Use white bg unless specific color needed
                                            >
                                                <option value="" disabled className="bg-white text-black">{t('securityControls.placeholders.selectSeverity')}</option>
                                                {filteredSeverityOptions.map(opt => (
                                                    <option key={opt.value} value={opt.value} className="bg-white text-black">{opt.label}</option>
                                                ))}
                                            </select>
                                            {/* Span for Print */}
                                            <span className={`hidden print:inline text-xs font-medium ${residualSeverityInfo.textClass} ${printResidualTextClass}`}>
                                                {residualSeverity ? `${residualSeverityInfo.label} (${residualSeverityInfo.level})` : t('securityControls.severity.undefined')}
                                            </span>
                                        </td>
                                    </tr>
                                );
                            })
                        ) : (
                             <tr className="print:hidden">
                                 <td colSpan="8" className="text-center py-10 text-gray-500">
                                      {/* Updated Empty State Message */}
                                      {t('securityControls.status.noLinkedRules')}
                                      <br /> {t('securityControls.status.linkRulesFirst')}
                                 </td>
                             </tr>
                        )}
                    </tbody>
                </table>
            </div> {/* End overflow-x-auto / print-target */}

             {/* Modals - Hidden on Print */}
            <div className="print:hidden">
                {/* Updated ControlSelectionModal props */}
                <ControlSelectionModal
                    isOpen={isControlModalOpen}
                    onClose={() => setIsControlModalOpen(false)}
                    modalAvailableControls={modalAvailableControls} // Pass fetched controls
                    isLoading={isModalLoading} // Pass loading state
                    error={modalError} // Pass error state
                    initialSelectedIds={(modalContext && selectedControlsForRuleDE[`${modalContext.ruleId}_${modalContext.deId}`])
                                        ? selectedControlsForRuleDE[`${modalContext.ruleId}_${modalContext.deId}`]
                                            .filter(c => !c.isNewSuggestion && c.type !== 'AI') // Pass only IDs of *manual/database* controls
                                            .map(c => c.id)
                                        : []}
                    onSave={handleSaveControlSelection}
                />
                 <AiSuggestionModal
                    isOpen={isAiSuggestionModalOpen}
                    onClose={handleCloseAiSuggestionModal}
                    context={aiModalContext}
                    onGenerate={handleGenerateAiSuggestions}
                    suggestions={aiSuggestionsList}
                    isLoading={isLoadingAiSuggestions}
                    error={aiSuggestionError}
                    onSelectSuggestion={handleSelectAiSuggestion}
                    selectedSuggestions={selectedAiSuggestionNames}
                    onAddSelected={handleAddSelectedAiControls}
                />
                         </div>

        </motion.div> // End main container
    );

}; // End SecurityControls Component

export default SecurityControls;