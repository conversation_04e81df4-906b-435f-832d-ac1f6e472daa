﻿import React, { useMemo, useCallback, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Printer, FileText, Briefcase, AlertTriangle, <PERSON><PERSON>heck, CheckSquare } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { useAnalysis } from '../../../context/AnalysisContext';
import { DREADED_EVENT_IMPACTS } from '../../../constants';
import { getTranslatedDreadedEventImpacts } from '../../../utils/translatedConstants';



// --- Helper Function to get Severity Info (Copied from SecurityControls) ---
const getSeverityLevelInfo = (severity) => {
    const lowerSeverity = severity?.toLowerCase() || '';
    switch (lowerSeverity) {
        case 'catastrophic':
            return { level: 'G5', label: 'Catastrophique', textClass: 'text-white', bgClass: 'bg-black', value: 5 };
        case 'critical':
            return { level: 'G4', label: 'Critique', textClass: 'text-white', bgClass: 'bg-red-600', value: 4 };
        case 'high':
        case 'major':
            return { level: 'G3', label: 'Majeure', textClass: 'text-black', bgClass: 'bg-yellow-500', value: 3 };
        case 'moderate':
        case 'significative':
            return { level: 'G2', label: 'Significative', textClass: 'text-black', bgClass: 'bg-yellow-200', value: 2 };
        case 'minor':
        case 'mineure':
            return { level: 'G1', label: 'Mineure', textClass: 'text-black', bgClass: 'bg-cyan-300', value: 1 };
        default:
            // Don't warn here, expected for unselected values
            return { level: 'N/A', label: 'Non défini', textClass: 'text-gray-800', bgClass: 'bg-gray-200', value: 0 };
    }
};

// Removed unused getEffectivenessLabel function

// --- UPDATED: Helper Function for Treatment Option Labels ---
const getTreatmentOptionLabel = (optionValue) => {
    switch (optionValue) {
        case 'reduce': return 'Réduire';
        case 'transfer': return 'Transférer';
        case 'avoid': return 'Éviter';
        case 'accept': return 'Accepter';
        default: return '-';
    }
};

// --- UPDATED: Helper Function for Decision Labels (including rationale) ---
const getDecisionLabel = (decisionData) => {
    if (!decisionData || !decisionData.decision) return '-';
    switch (decisionData.decision) {
        case 'planned': return 'À planifier';
        case 'apply_before': return `Appliquer avant le ${decisionData.date || '[Date?]'}`;
        case 'abandoned': return 'Abandonner';
        // NEW: Add rationale for acceptance
        case 'accepted':
            const rationale = decisionData.acceptanceRationale ? ` (Raison: ${decisionData.acceptanceRationale})` : '';
            return `Accepter ${rationale}`;
        default: return '-';
    }
};

// --- Main Synthesis Component ---
const RiskTreatment = () => {
    const {
        currentAnalysis,
        currentContextData,
        currentBusinessValues,
        currentDreadedEvents,
        currentSecurityFramework,
        currentAnalysisControlPlan,
        isWorkshopDataLoading,
        workshopDataError,
        allControls,
    } = useAnalysis();

    // Use i18next translation hook
    const { t } = useTranslation();

    // --- NEW: Debugging useEffect ---
    useEffect(() => {
        if (!isWorkshopDataLoading) { // Only log after loading attempt
            console.log('[RiskTreatment] currentAnalysisControlPlan:', currentAnalysisControlPlan);
            console.log('[RiskTreatment] currentAnalysisControlPlan?.planData:', currentAnalysisControlPlan?.planData);
        }
    }, [currentAnalysisControlPlan, isWorkshopDataLoading]);

    // --- Derived Data for the Report ---
    const analysisOverview = useMemo(() => ({
        name: currentAnalysis?.name || 'Analyse sans nom',
        date: currentAnalysis?.createdAt ? new Date(currentAnalysis.createdAt).toLocaleDateString() : 'Date inconnue',
        orgName: currentContextData?.organizationName || 'N/A',
        scope: currentContextData?.scope || 'N/A',
        missions: currentContextData?.missions || []
    }), [currentAnalysis, currentContextData]);

    const participantsList = useMemo(() => currentContextData?.participants || [], [currentContextData]);

    // --- UPDATED: Detailed Business Values and Assets ---
    const detailedBusinessValues = useMemo(() => {
        return (currentBusinessValues || []).map(bv => ({
            id: bv.id,
            name: bv.name,
            description: bv.description,
            assets: (bv.supportAssets || []).map(sa => sa) || [],
        }));
    }, [currentBusinessValues]);

    // --- UPDATED: Detailed Dreaded Events (adding impact field) ---
    const detailedDreadedEvents = useMemo(() => {
        return (currentDreadedEvents || []).map(de => {
            // ---> Find linked Business Value <--- (Keep for name)
            const linkedBv = (currentBusinessValues || []).find(bv => String(bv.id) === String(de.businessValue));
            const linkedBvName = linkedBv?.name || 'N/A';
            // ---> REMOVED Pillar Formatting <---
            /*
            const pillars = linkedBv?.pillars || [];
            const pillarLabels = {
                confidentiality: 'C',
                integrity: 'I',
                availability: 'D',
                authenticity: 'A',
                traceability: 'T'
            };
            const formattedPillars = pillars.map(p => pillarLabels[p] || p).join(', ') || 'N/A';
            */

            // Get impact labels if available (support both old impact field and new impacts array)
            const impactArray = de.impacts || (de.impact ? [de.impact] : []);
            const impactLabels = impactArray.length > 0 ?
                impactArray.map(impactValue => {
                    const translatedImpacts = getTranslatedDreadedEventImpacts();
                    const impact = translatedImpacts.find(imp => imp.value === impactValue);
                    return impact ? impact.label : impactValue;
                }) :
                ['Non spécifié'];

            return {
                id: de.id,
                name: de.name || 'Événement sans nom',
                description: de.description || 'Pas de description',
                severityInfo: getSeverityLevelInfo(de.severity),
                businessValueName: linkedBvName, // Keep BV Name
                impacts: impactArray, // Add impact values array
                impactLabels: impactLabels // Add formatted impact labels
                // businessValuePillars: formattedPillars, // REMOVED Pillars
            };
        });
    }, [currentDreadedEvents, currentBusinessValues]); // Dependency remains

    // --- UPDATED: Detailed Security Rules (adding linked DEs) ---
    const detailedSecurityRules = useMemo(() => {
        const rules = Object.values(currentSecurityFramework?.selectedRules || {}).flat();
        const ruleLinks = currentSecurityFramework?.ruleDreadedEvents || {}; // { ruleId: [deId1, deId2], ... }
        const allEventsMap = new Map((currentDreadedEvents || []).map(de => [String(de.id), de.name])); // Map de.id (string) to de.name

        return rules.map(rule => {
            const ruleId = String(rule.id || rule.rule_id);
            const linkedDeIds = ruleLinks[ruleId] || [];
            const linkedDeNames = linkedDeIds
                .map(deId => allEventsMap.get(String(deId)) || `ID:${deId}?`)
                .filter(Boolean); // Get names, fallback to ID, filter out nulls if any

            return {
                id: ruleId || `rule_${Math.random()}`,
                name: rule.name || rule.title || 'Règle sans nom',
                // reference: rule.reference || 'N/A', // Already removed
                description: rule.description || 'Pas de description',
                status: rule.status || 'unknown',
                // effectiveness: getEffectivenessLabel(rule.effectiveness), // Already removed
                statusLabel: (() => {
            switch (rule.status) {
                        case 'applied': return 'Appliqué';
                        case 'partially-applied': return 'Partiellement';
                        case 'not-applied': return 'Non appliqué';
                        default: return 'Inconnu';
                    }
                })(),
                linkedDreadedEvents: linkedDeNames, // ADDED Linked DE Names array
            };
        });
    }, [currentSecurityFramework, currentDreadedEvents]); // ADDED currentDreadedEvents dependency

    // --- Existing Detailed Treatment Plan (ensure control details are rendered) ---
    const detailedTreatmentPlan = useMemo(() => {
        // Access the nested 'plan' object
        const actualPlanObject = currentAnalysisControlPlan?.planData?.plan || {};
        const allRules = Object.values(currentSecurityFramework?.selectedRules || {}).flat();
        // ---> CREATE Map for efficient control lookup <---
        const allControlsMap = new Map((allControls || []).map(c => [String(c.id), c]));
        // console.log("[RiskTreatment] Controls Map:", allControlsMap);

        const processedPlan = Object.values(actualPlanObject).map((entry, index) => {
            // Find the corresponding rule by ID
            const rule = allRules.find(r => r.id === entry.ruleId || r.rule_id === entry.ruleId);
            const ruleName = rule?.name || rule?.title || 'Règle inconnue';

            // Find the corresponding dreaded event by ID
            const dreadedEvent = (currentDreadedEvents || []).find(de => de.id === entry.dreadedEventId);
            const deName = dreadedEvent?.name || 'Événement inconnu';
            // Get initial severity from the found dreaded event
            const initialSeverity = getSeverityLevelInfo(dreadedEvent?.severity);

            // Get impact information (support both old impact field and new impacts array)
            const impactArray = dreadedEvent?.impacts || (dreadedEvent?.impact ? [dreadedEvent.impact] : []);
            const impactLabels = impactArray.length > 0 ?
                impactArray.map(impactValue => {
                    const translatedImpacts = getTranslatedDreadedEventImpacts();
                    const impact = translatedImpacts.find(imp => imp.value === impactValue);
                    return impact ? impact.label : impactValue;
                }) :
                ['Non spécifié'];

            return {
                id: `${entry.ruleId}_${entry.dreadedEventId}_${index}`, // Create a unique key for mapping
                // ---> ADD Original IDs and Value <---
                ruleId: entry.ruleId,
                dreadedEventId: entry.dreadedEventId,
                treatmentOptionValue: entry.treatmentOption, // Keep the raw value
                // ---> Existing display properties <---
                ruleName: ruleName,
                deName: deName,
                initialSeverity: initialSeverity,
                impacts: impactArray, // Add impact values array
                impactLabels: impactLabels, // Add formatted impact labels
                treatmentOption: getTreatmentOptionLabel(entry.treatmentOption), // Keep label for display table
                controls: (entry.controls || []).map(ctrl => { // Ensure we map control details
                    // ---> LOOKUP Control Name <---
                    const foundControl = allControlsMap.get(String(ctrl.controlId));
                    const controlName = foundControl?.name || ctrl.name || 'Contrôle inconnu'; // Use found name, fallback to saved name, then unknown
                    const controlDescription = foundControl?.description || 'Pas de description'; // Use description from master list if available
                    // console.log(`[RiskTreatment] Control Lookup: ID=${ctrl.controlId}, Found=${!!foundControl}, Name=${controlName}`); // Debug log
                    // ---> ADD DETAILED LOGGING <--- (Also update to check ctrl.name)
                    console.log(`[RiskTreatment] Processing Control:`, {
                        controlFromPlan: ctrl, // Log the raw object from planData
                        foundInMasterList: foundControl, // Log the result of map lookup
                        nameFromPlan: ctrl.name, // Explicitly log the name from plan (using .name)
                        finalName: controlName, // Log the final calculated name
                    });

                    return {
                        controlId: ctrl.controlId,
                        controlName: controlName, // Use the looked-up or fallback name
                        description: controlDescription, // Use the looked-up description
                        responsiblePerson: ctrl.responsiblePerson || '-',
                        decision: ctrl.decision,
                        decisionDate: ctrl.decisionDate,
                    };
                }),
                residualSeverity: getSeverityLevelInfo(entry.residualSeverity),
            };
        }).filter(Boolean); // Filter out entries where rule/DE couldn't be found

        // ---> ADD Sorting by Rule Name/ID <---
        processedPlan.sort((a, b) => {
            const ruleNameA = a.ruleName?.toLowerCase() || String(a.ruleId); // Use calculated name or ID
            const ruleNameB = b.ruleName?.toLowerCase() || String(b.ruleId);
            if (ruleNameA < ruleNameB) return -1;
            if (ruleNameA > ruleNameB) return 1;
            // Optional: secondary sort by DE name
            const deNameA = a.deName?.toLowerCase() || '';
            const deNameB = b.deName?.toLowerCase() || '';
            return deNameA.localeCompare(deNameB);
        });

        console.log("RT: Calculated and sorted detailedTreatmentPlan:", processedPlan);

        return processedPlan;
    }, [currentAnalysisControlPlan, currentSecurityFramework, currentDreadedEvents, allControls]);

    // --- NEW: Risk Distribution Calculations ---
    const initialRiskDistribution = useMemo(() => {
        const distribution = { 'G5': 0, 'G4': 0, 'G3': 0, 'G2': 0, 'G1': 0, 'N/A': 0 };
        detailedDreadedEvents.forEach(de => {
            const level = de.severityInfo.level;
            if (distribution.hasOwnProperty(level)) {
                distribution[level]++;
            } else {
                distribution['N/A']++; // Catch any unexpected levels
            }
        });
        return distribution;
    }, [detailedDreadedEvents]);

    const residualRiskDistribution = useMemo(() => {
        const distribution = { 'G5': 0, 'G4': 0, 'G3': 0, 'G2': 0, 'G1': 0, 'N/A': 0 };
        detailedTreatmentPlan.forEach(item => {
            const level = item.residualSeverity.level;
            if (distribution.hasOwnProperty(level)) {
                distribution[level]++;
            } else {
                distribution['N/A']++;
            }
        });
        return distribution;
    }, [detailedTreatmentPlan]);

    // --- NEW: Treatment Strategy Summary Calculation ---
    const treatmentStrategySummary = useMemo(() => {
        const summary = { reduce: 0, accept: 0, transfer: 0, avoid: 0, undefined: 0 };
        detailedTreatmentPlan.forEach(item => {
            // Use the direct value stored in the detailed plan item
            const actualOption = item.treatmentOptionValue || 'undefined';

            if (summary.hasOwnProperty(actualOption)) {
                summary[actualOption]++;
            } else {
                 summary.undefined++;
            }
        });
        return summary;
    }, [detailedTreatmentPlan]);

    // --- NEW: Accepted Risks Calculation ---
    const acceptedRisks = useMemo(() => {
        const actualPlanObject = currentAnalysisControlPlan?.planData?.plan || {};
        return detailedTreatmentPlan
            .filter(item => {
                const planEntry = Object.values(actualPlanObject).find(entry => entry.ruleId === item.ruleId && entry.dreadedEventId === item.dreadedEventId);
                return planEntry?.treatmentOption === 'accept';
            });
    }, [detailedTreatmentPlan, currentAnalysisControlPlan]); // Added dependency

    // --- Print Handler ---
    const handlePrintReport = useCallback(() => {
        const printArea = document.getElementById('risk-treatment-report-print-area');
        if (!printArea) {
            console.error("Print area '#risk-treatment-report-print-area' not found.");
            alert("Erreur: Impossible de trouver la zone d'impression.");
            return;
        }

        const printWindow = window.open('', '_blank');
        printWindow.document.write(`<html><head><title>${t('riskTreatment.title')}</title>`);
        printWindow.document.write(`
          <style>
            @media print {
              @page { size: A4; margin: 0.75in; }
              body {
                font-family: ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
                font-size: 9.5pt; /* Slightly smaller base */
                line-height: 1.4;
                color: #333;
                -webkit-print-color-adjust: exact !important;
                print-color-adjust: exact !important;
                counter-reset: page;
              }
              @page {
                @bottom-right {
                  content: "Page " counter(page);
                  font-size: 8pt;
                  color: #666;
                }
              }
              h1 { font-size: 16pt; text-align: center; margin-bottom: 25px; color: #1e3a8a; font-weight: 600; }
              h2 { font-size: 13pt; margin-top: 20px; margin-bottom: 10px; border-bottom: 1px solid #ccc; padding-bottom: 4px; color: #1e40af; font-weight: 600; display: flex; align-items: center; }
              h2 svg { width: 18px; height: 18px; margin-right: 8px; color: #3b82f6; }
              h3 { font-size: 11pt; margin-top: 15px; margin-bottom: 5px; font-weight: 600; color: #1d4ed8; }
              p { margin-bottom: 6px; }
              strong { font-weight: 600; color: #1f2937; }
              ul { list-style: disc; margin-left: 25px; margin-bottom: 10px; }
              table { border-collapse: collapse; width: 100%; font-size: 8pt; margin-top: 8px; margin-bottom: 15px; table-layout: fixed; border: 1px solid #ddd; }
              th, td { border: 1px solid #e5e7eb; padding: 6px 8px; text-align: left; vertical-align: top; word-wrap: break-word; overflow-wrap: break-word; }
              th { background-color: #f9fafb !important; font-weight: 600; color: #1f2937 !important; font-size: 7.5pt; text-transform: uppercase; letter-spacing: 0.03em; }
              tbody tr:nth-child(odd) { background-color: #f9fafb !important; } /* Zebra striping */
              .section-card { border: 1px solid #e5e7eb; border-radius: 0.375rem; padding: 12px 15px; margin-bottom: 18px; background-color: #fff !important; break-inside: avoid; }
              .detail-table { font-size: 8pt; margin-top: 10px; border: 1px solid #e5e7eb; width: 100%; table-layout: fixed; } /* Ensure full width */
              .detail-table th { background-color: #f3f4f6 !important; font-size: 7pt; padding: 5px 7px; }
              .detail-table td { padding: 5px 7px; font-size: 7.5pt; }
              .bv-asset-list { list-style: none; padding-left: 15px; margin-top: 5px; }
              .bv-asset-list li { margin-bottom: 3px; font-size: 7.5pt; }
              .bv-asset-list strong { color: #4b5563; }
              .dreaded-event-table th:nth-child(1) { width: 20%; } /* Name */
              .dreaded-event-table th:nth-child(2) { width: 30%; } /* Description */
              .dreaded-event-table th:nth-child(3) { width: 20%; } /* Business Value */
              .dreaded-event-table th:nth-child(4) { width: 15%; } /* Impact Principal */
              .dreaded-event-table th:nth-child(5) { width: 15%; text-align: center; } /* Severity */
              .security-rule-table th:nth-child(1) { width: 25%; } /* Name */
              .security-rule-table th:nth-child(2) { width: 30%; } /* Description */
              .security-rule-table th:nth-child(3) { width: 30%; } /* Linked DEs */
              .security-rule-table th:nth-child(4) { width: 25%; } /* Status */
              .severity-box-print { display: inline-flex; flex-direction: column; align-items: center; justify-content: center; padding: 0.1rem 0.15rem; border-radius: 0.25rem; font-size: 0.55rem; font-weight: 600; text-align: center; border: 1px solid #ccc; width: 3.5rem; height: 2.2rem; box-sizing: border-box; vertical-align: middle; }
              .severity-box-print span { display: block !important; line-height: 1 !important; }
              .severity-box-print .label-print { font-size: 0.5rem !important; line-height: 1 !important; margin-top: 1px; }
              /* Severity Colors */
              .bg-black-print { background-color: #000 !important; color: #fff !important; }
              .text-white-print { color: #fff !important; }
              .bg-red-600-print { background-color: #DC2626 !important; color: #fff !important; }
              .bg-yellow-500-print { background-color: #F59E0B !important; color: #000 !important; }
              .text-black-print { color: #000 !important; }
              .bg-yellow-200-print { background-color: #FEF08A !important; color: #000 !important; }
              .bg-cyan-300-print { background-color: #67E8F9 !important; color: #000 !important; }
              .bg-gray-200-print { background-color: #E5E7EB !important; color: #374151 !important; }
              /* --- Treatment Plan Table Specifics (Print) --- */
              .treatment-plan-table { font-size: 6.5pt; }
              .treatment-plan-table th, .treatment-plan-table td { padding: 2px 3px; } /* Minimal padding */
              .treatment-plan-table th:nth-child(1) { width: 10%; } /* Règle */
              .treatment-plan-table th:nth-child(2) { width: 10%; } /* Événement */
              .treatment-plan-table th:nth-child(3) { width: 12%; } /* Impact */
              .treatment-plan-table th:nth-child(4) { width: 11%; }  /* Grav. Init. */
              .treatment-plan-table th:nth-child(5) { width: 10%; }  /* Option */
              .treatment-plan-table th:nth-child(6) { width: 16%; } /* Contrôles */
              .treatment-plan-table th:nth-child(7) { width: 11%; }  /* Grav. Résid. */
              .treatment-plan-table th:nth-child(8) { width: 8%; }  /* Réduction % */
              .treatment-plan-table th:nth-child(9) { width: 8%; } /* Responsable/Décision */
              .control-list-item { margin-bottom: 4px; line-height: 1.3; }
              .control-list-item strong { font-weight: 600; margin-right: 4px; color: #4b5563; }
              .control-list-item .label { display: inline-block; width: 60px; } /* Align Resp/Decision labels */
              .control-list-item .rationale { display: block; font-style: italic; color: #6b7280; padding-left: 65px; font-size: 0.9em; }
              .no-print { display: none !important; } /* Hide print button in print */
            }
          </style>
        `);
        printWindow.document.write('</head><body>');
        printWindow.document.write(`<h1>${t('riskTreatment.title')}</h1>`); // Updated Title

        const contentClone = printArea.cloneNode(true);

        // Apply print classes to severity boxes
        contentClone.querySelectorAll('.severity-box').forEach(box => {
            box.classList.add('severity-box-print');
            const bgColorClass = Array.from(box.classList).find(cls => cls.startsWith('bg-'));
            if (bgColorClass) box.classList.add(`${bgColorClass}-print`);
            const textColorClass = Array.from(box.classList).find(cls => cls.startsWith('text-'));
            if (textColorClass) box.classList.add(`${textColorClass}-print`);
            const labelSpan = box.querySelector('.label');
            if(labelSpan) labelSpan.classList.add('label-print');
        });

        // Add SVG icons to H2 headers in the clone (using simple path data for print)
        const iconMap = {
            'Informations Générales': '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" width="18" height="18" style="margin-right: 8px; vertical-align: middle;"><path d="M9.25 13.25a.75.75 0 001.5 0V4.75l.97 1.029a.75.75 0 001.091-1.029l-2.25-2.368a.75.75 0 00-1.062 0l-2.25 2.368a.75.75 0 101.091 1.029L9.25 4.75v8.5z"/><path d="M3.5 12.75a.75.75 0 00-1.5 0v2.5A2.75 2.75 0 004.75 18h10.5A2.75 2.75 0 0018 15.25v-2.5a.75.75 0 00-1.5 0v2.5c0 .69-.56 1.25-1.25 1.25H4.75c-.69 0-1.25-.56-1.25-1.25v-2.5z"/></svg>',
            'Contexte Métier': '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" width="18" height="18" style="margin-right: 8px; vertical-align: middle;"><path d="M2.879 7.121A3 3 0 017.5 6.001h5a3 3 0 014.621 1.12l1.112 2.224A4.5 4.5 0 0117.5 13.5V15a3 3 0 01-3 3h-9a3 3 0 01-3-3v-1.5a4.5 4.5 0 01-.732-4.155l1.111-2.224zM17 13.5V15a1.5 1.5 0 01-1.5 1.5h-9A1.5 1.5 0 015 15v-1.5a3 3 0 01-.18-5.996l-1.11-2.223a1.5 1.5 0 012.31-1.152l.318.159A3 3 0 017.5 7.501h5a3 3 0 012.682 1.62l.318-.16a1.5 1.5 0 012.31 1.153l-1.11 2.223A3 3 0 0117 13.5z"/></svg>',
            'Identification des Risques': '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" width="18" height="18" style="margin-right: 8px; vertical-align: middle;"><path fill-rule="evenodd" d="M8.485 2.47a.75.75 0 011.03 0l6.25 6.25a.75.75 0 01-.023 1.053l-6.25 6.25a.75.75 0 01-1.03 0l-6.25-6.25a.75.75 0 01.023-1.053l6.25-6.25zm.76 8.78a.75.75 0 10-1.5 0v2a.75.75 0 001.5 0v-2zm-.75-3.5a.75.75 0 00-.75.75v.75c0 .414.336.75.75.75h.01a.75.75 0 00.75-.75v-.75a.75.75 0 00-.75-.75H9.245z" clip-rule="evenodd"/></svg>',
            'Socle de Sécurité': '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" width="18" height="18" style="margin-right: 8px; vertical-align: middle;"><path fill-rule="evenodd" d="M10 1a4.5 4.5 0 00-4.5 4.5V9H5a2 2 0 00-2 2v6a2 2 0 002 2h10a2 2 0 002-2v-6a2 2 0 00-2-2h-.5V5.5A4.5 4.5 0 0010 1zm3 8V5.5a3 3 0 10-6 0V9h6z" clip-rule="evenodd"/></svg>',
            'Menaces Identifiées': '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" width="18" height="18" style="margin-right: 8px; vertical-align: middle;"><path fill-rule="evenodd" d="M11.06 2.148a.75.75 0 00-1.06-.04l-7.5 7.5a.75.75 0 101.06 1.06L10 4.268l6.44 6.44a.75.75 0 101.06-1.06l-7.5-7.5a.75.75 0 00-.94-.04zM10 12.75a.75.75 0 00-.75.75v4a.75.75 0 001.5 0v-4a.75.75 0 00-.75-.75z" clip-rule="evenodd" /></svg>',
            'Vulnérabilités Identifiées': '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" width="18" height="18" style="margin-right: 8px; vertical-align: middle;"><path fill-rule="evenodd" d="M10.313 4.042a3.5 3.5 0 10-5.626 4.007l-.687.687a.75.75 0 000 1.06l1.414 1.414a.75.75 0 001.06 0l.687-.687a3.502 3.502 0 004.006-5.626L10.313 4.042zm4.95 4.95a3.5 3.5 0 10-5.627-4.007l.687-.687a.75.75 0 00-1.06-1.06L7.85 4.654a.75.75 0 000 1.06l.687.687A3.5 3.5 0 0012.542 12l.854.854a.75.75 0 001.06 0l1.414-1.414a.75.75 0 000-1.06l-.853-.854-.001-.001zM8.464 11.536l-1.414 1.414a.75.75 0 000 1.06l2.121 2.121a.75.75 0 001.06 0L12.35 14.01a3.51 3.51 0 00-3.886-2.474z" clip-rule="evenodd" /></svg>',
            'Scénarios de Risque': '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" width="18" height="18" style="margin-right: 8px; vertical-align: middle;"><path fill-rule="evenodd" d="M7.84 1.82a.75.75 0 01.84.2l5.25 6.5a.75.75 0 01-.114 1.09l-5.25 4.2a.75.75 0 01-1.166-.71V2.63a.75.75 0 01.49-.71zm-.23 1.88v12.1a.75.75 0 001.165.71l5.25-4.2a.75.75 0 00.115-1.09l-5.25-6.5a.75.75 0 00-1.315-.93l.035.03v-.05zM12.25 7a.75.75 0 000 1.5h3a.75.75 0 000-1.5h-3z" clip-rule="evenodd" /></svg>',
            'Distribution Initiale des Risques': '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" width="18" height="18" style="margin-right: 8px; vertical-align: middle;"><path d="M15.5 2.75a.75.75 0 00-1.5 0v14.5a.75.75 0 001.5 0V2.75zM10.5 10a.75.75 0 00-1.5 0v7.25a.75.75 0 001.5 0V10zM5.5 5.75a.75.75 0 00-1.5 0v11.5a.75.75 0 001.5 0V5.75z" /></svg>',
            'Plan de Traitement Détaillé': '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" width="18" height="18" style="margin-right: 8px; vertical-align: middle;"><path fill-rule="evenodd" d="M16.403 3.903a.75.75 0 01.07 1.057l-4.75 8.25a.75.75 0 01-1.14.17L7.87 10.63a.75.75 0 01-1.118 1.002l-3.75-2.25a.75.75 0 01.368-1.384l3.588 1.196 2.064-2.89a.75.75 0 011.14-.17l4.75 8.25a.75.75 0 011.14-.17l4.25-7.5a.75.75 0 011.118 1.002l-5 3a.75.75 0 11-.736-1.224L16.403 3.903zM4.75 16.75a.75.75 0 01.75-.75h9.5a.75.75 0 010 1.5h-9.5a.75.75 0 01-.75-.75z" clip-rule="evenodd"/></svg>',
        };
        contentClone.querySelectorAll('h2').forEach(h2 => {
            const title = h2.textContent.trim();
            if(iconMap[title]) {
                h2.innerHTML = iconMap[title] + h2.innerHTML; // Prepend SVG string
            }
        });

        printWindow.document.write(contentClone.innerHTML);
        printWindow.document.write('</body></html>');
        printWindow.document.close();
        setTimeout(() => {
            printWindow.focus();
            printWindow.print();
        }, 500);
    }, [detailedTreatmentPlan, detailedBusinessValues, detailedDreadedEvents, detailedSecurityRules, analysisOverview, participantsList, t]);

    // --- Render Logic ---
    if (isWorkshopDataLoading) {
        return <div className="p-6 text-center">Chargement des données pour la synthèse...</div>;
    }

    if (workshopDataError) {
        return <div className="p-6 bg-red-100 border border-red-400 text-red-700 rounded-lg">
            <p className="font-semibold">Erreur de chargement des données de synthèse:</p>
            <pre className="mt-2 text-xs whitespace-pre-wrap">{JSON.stringify(workshopDataError, null, 2)}</pre>
        </div>;
    }

    if (!currentAnalysis) {
         return <div className="p-6 bg-yellow-100 border border-yellow-400 text-yellow-700 rounded-lg">Sélectionnez une analyse pour voir la synthèse.</div>;
    }

    return (
        <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.4 }}
            className="p-6 bg-white rounded-lg shadow-sm border border-gray-200"
        >
            {/* === MERGED HEADER === */}
            <div className="bg-white rounded-xl shadow-sm border border-slate-100 p-4 mb-6">
                <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
                    {/* Left Side: Breadcrumb & Title */}
                    <div>
                        <div className="flex items-center text-sm text-slate-500 mb-2">
                            <span className="hover:text-blue-600 transition-colors">Atelier 1</span>
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mx-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                            </svg>
                            <span className="text-blue-600 font-medium">Synthèse</span>
                        </div>
                        <h1 className="text-2xl sm:text-3xl font-bold text-slate-800 flex items-center">
                            <FileText size={28} className="mr-3 text-blue-600" />
                            {t('riskTreatment.title')}
                        </h1>
                    </div>

                    {/* Right Side: Action Buttons */}
                    <div className="flex items-center space-x-3">
                        {/* Print Button */}
                        <button
                            onClick={handlePrintReport}
                            className="text-sm font-medium bg-slate-100 text-slate-700 px-4 py-2 rounded-lg hover:bg-slate-200 flex items-center shadow-sm transition duration-200 no-print"
                        >
                            <Printer size={16} className="mr-2" />
                            {t('common.print')}
                        </button>
                    </div>
                </div>
            </div>
            {/* === END MERGED HEADER === */}

            {/* Report Content Area for Printing */}
            <div id="risk-treatment-report-print-area">

                {/* --- NEW: Executive Summary --- */}
                <div className="section-card bg-indigo-50 p-6 rounded-lg shadow-sm mb-6 border border-indigo-200">
                    <h2 className="text-xl font-semibold text-indigo-800 border-b border-indigo-300 pb-2 mb-4 flex items-center">
                        <Briefcase size={20} className="mr-2 text-indigo-600" />{t('riskTreatment.executiveSummaryTitle')}
                    </h2>
                    <p className="text-sm text-indigo-900 mb-3">
                         {t('riskTreatment.executiveSummary.intro', {
                             analysisName: analysisOverview.name,
                             scope: analysisOverview.scope
                         })}
                     </p>
                     <p className="text-sm text-indigo-900 mb-3">
                         {t('riskTreatment.executiveSummary.businessValues', {
                             businessValuesCount: detailedBusinessValues.length,
                             dreadedEventsCount: detailedDreadedEvents.length,
                             criticalEventsCount: initialRiskDistribution['G5'] + initialRiskDistribution['G4']
                         })}
                     </p>
                     <p className="text-sm text-indigo-900 mb-1">
                         {t('riskTreatment.executiveSummary.securityRules', {
                             rulesCount: detailedSecurityRules.length,
                             reduceCount: treatmentStrategySummary.reduce,
                             acceptCount: treatmentStrategySummary.accept,
                             transferCount: treatmentStrategySummary.transfer,
                             avoidCount: treatmentStrategySummary.avoid
                         })}
                         {treatmentStrategySummary.undefined > 0 && <span className="ml-2 text-gray-500">({t('riskTreatment.executiveSummary.undefined', { count: treatmentStrategySummary.undefined })})</span>}
                     </p>
                     <p className="text-sm text-indigo-900">
                         {t('riskTreatment.executiveSummary.objective')}
                     </p>
                </div>

                {/* Section: Analysis Overview */}
                <div className="section-card bg-white p-6 rounded-lg shadow-md mb-6">
                    {/* Updated H2 style */}
                    <h2 className="text-xl font-semibold text-blue-700 border-b border-gray-300 pb-2 mb-4 flex items-center">
                        <FileText size={20} className="mr-2 text-blue-600" />{t('riskTreatment.generalInfo')}
                    </h2>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-3 text-sm text-gray-700">
                        <p><strong>{t('riskTreatment.analysisName')}</strong> {analysisOverview.name}</p>
                        <p><strong>{t('riskTreatment.creationDate')}</strong> {analysisOverview.date}</p>
                        <p><strong>{t('riskTreatment.organization')}</strong> {analysisOverview.orgName}</p>
                        <p className="md:col-span-2"><strong>{t('riskTreatment.scope')}</strong> {analysisOverview.scope}</p>
                        {analysisOverview.missions.length > 0 && (
                            <div className="md:col-span-2">
                                <p><strong>{t('riskTreatment.missions')}</strong></p>
                                <ul className="list-disc pl-6 mt-1 space-y-1">
                                    {analysisOverview.missions.map((mission, index) => (
                                        <li key={index} className="text-gray-600">{mission}</li>
                                    ))}
                                </ul>
                            </div>
                        )}
                    </div>
                    {participantsList.length > 0 && (
                        <>
                            {/* Updated H3 style */}
                            <h3 className="text-lg font-medium text-blue-600 mt-5 mb-2">{t('riskTreatment.participants')}</h3>
                            <ul className="list-disc pl-6 text-sm text-gray-600 space-y-1">
                                {participantsList.map(p => (
                                    <li key={p.id}>{p.name} <span className="text-gray-500">({p.position || p.customPosition || 'N/A'})</span></li>
                                ))}
                            </ul>
                        </>
                    )}
                </div>

                {/* --- NEW: Initial Risk Distribution --- */}
                <div className="section-card bg-white p-6 rounded-lg shadow-md mb-6">
                    <h2 className="text-xl font-semibold text-orange-700 border-b border-gray-300 pb-2 mb-4 flex items-center">
                         <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" width="18" height="18" className="mr-2 text-orange-600"><path d="M15.5 2.75a.75.75 0 00-1.5 0v14.5a.75.75 0 001.5 0V2.75zM10.5 10a.75.75 0 00-1.5 0v7.25a.75.75 0 001.5 0V10zM5.5 5.75a.75.75 0 00-1.5 0v11.5a.75.75 0 001.5 0V5.75z" /></svg>
                         {t('riskTreatment.initialDistribution')}
                    </h2>
                    <p className="text-sm mb-4 text-gray-700">{t('riskTreatment.initialDistribution.description', { count: detailedDreadedEvents.length })}</p>
                    <div className="overflow-x-auto -mx-6 px-6">
                         <table className="min-w-full detail-table text-xs border border-gray-200 w-3/4 mx-auto"> {/* Centered & Smaller Table */}
                             <thead className="bg-gray-100">
                                 <tr>
                                     <th className="px-3 py-2 font-medium text-gray-600 w-1/2">{t('riskTreatment.table.headers.severityLevel')}</th>
                                     <th className="px-3 py-2 font-medium text-gray-600 w-1/2 text-center">{t('riskTreatment.table.headers.eventCount')}</th>
                                 </tr>
                             </thead>
                             <tbody className="divide-y divide-gray-100">
                                 {Object.entries(initialRiskDistribution)
                                     // Show levels with count > 0, always show N/A if present
                                     .filter(([level, count]) => count > 0 || level === 'N/A')
                                     .map(([level, count]) => (
                                     <tr key={level} className="hover:bg-gray-50">
                                         <td className="px-4 py-2 text-gray-700">
                                             {(() => {
                                                 // Use the reliable hardcoded map
                                                 const levelLabelMap = {
                                                      'G5': t('riskTreatment.severity.catastrophic'), 'G4': t('riskTreatment.severity.critical'), 'G3': t('riskTreatment.severity.major'),
                                                      'G2': t('riskTreatment.severity.moderate'), 'G1': t('riskTreatment.severity.minor'), 'N/A': t('riskTreatment.severity.undefined')
                                                 };
                                                 const label = levelLabelMap[level] || level; // Fallback to level code if not found
                                                 return `${label} (${level})`;
                                             })()}
                                         </td>
                                         <td className="px-4 py-2 text-gray-700 text-center font-semibold">{count}</td>
                                     </tr>
                                 ))}
                             </tbody>
                         </table>
                    </div>
                </div>

                {/* Section: Business Context */}
                <div className="section-card bg-white p-6 rounded-lg shadow-md mb-6">
                    <h2 className="text-xl font-semibold text-green-700 border-b border-gray-300 pb-2 mb-4 flex items-center">
                        <Briefcase size={20} className="mr-2 text-green-600" />{t('riskTreatment.businessContext')}
                    </h2>
                    {detailedBusinessValues.length > 0 ? (
                        <div className="space-y-5"> {/* Increased spacing */}
                            {detailedBusinessValues.map(bv => (
                                <div key={bv.id} className="pb-3 border-b border-gray-100 last:border-b-0">
                                    <h3 className="text-base font-semibold text-gray-800 mb-1">{bv.name}</h3>
                                    <p className="text-sm text-gray-600 italic ml-2 mb-2">{bv.description || t('common.noDescription')}</p>
                                    {bv.assets.length > 0 ? (
                                        <>
                                            <p className="text-xs mt-1 ml-2 mb-1"><strong className="text-gray-500">{t('riskTreatment.businessContext.supportAssets', { count: bv.assets.length })}</strong></p>
                                            {/* UPDATED: Display asset details */}
                                            <ul className="list-none pl-4 text-xs text-gray-700 space-y-1.5 bv-asset-list"> {/* Increased spacing */}
                                                {bv.assets.map(asset => (
                                                    <li key={asset.id}>
                                                        <strong>{asset.name || `Asset ${asset.id}`}</strong>
                                                        <span className="block pl-3 text-gray-600">
                                                            {asset.type && asset.type !== 'N/A' && <span>{t('riskTreatment.businessContext.type')}: {asset.type}</span>}
                                                            {asset.location && <span className="ml-3">{t('riskTreatment.businessContext.location')}: {asset.location}</span>}
                                                            {asset.description && asset.description !== t('common.noDescription') && <span className="block mt-0.5 italic">{t('riskTreatment.businessContext.description')}: {asset.description}</span>}
                                                            {/* Add more fields here if needed, e.g., asset.owner, asset.value */}
                                                        </span>
                                                    </li>
                                                ))}
                                            </ul>
                                        </>
                                    ) : (
                                        <p className="text-xs mt-1 ml-2 text-gray-500 italic">{t('riskTreatment.businessContext.noSupportAssets')}</p>
                                    )}
                                </div>
                            ))}
                        </div>
                    ) : (
                        <p className="text-sm text-gray-500 italic text-center py-4">{t('riskTreatment.businessContext.noBusinessValues')}</p>
                    )}
                </div>

                {/* Section: Risk Identification -> Detailed Dreaded Events */}
                <div className="section-card bg-white p-6 rounded-lg shadow-sm border border-gray-200 mb-6">
                    <h2 className="text-xl font-semibold text-red-700 border-b border-gray-300 pb-2 mb-4 flex items-center">
                        <AlertTriangle size={20} className="mr-2 text-red-600" />{t('riskTreatment.riskIdentification')}
                    </h2>
                    <p className="text-sm mb-4 text-gray-700">{t('riskTreatment.riskIdentification.totalEvents', { count: detailedDreadedEvents.length })}</p>
                    {detailedDreadedEvents.length > 0 ? (
                        // UPDATED: Display detailed table instead of summary
                        <div className="overflow-x-auto -mx-6 px-6">
                            <table className="min-w-full detail-table dreaded-event-table text-xs border border-gray-200">
                                <thead className="bg-gray-100">
                                    <tr>
                                        <th className="px-3 py-2 font-medium text-gray-600">{t('riskTreatment.table.headers.eventName')}</th>
                                        <th className="px-3 py-2 font-medium text-gray-600">{t('common.description')}</th>
                                        <th className="px-3 py-2 font-medium text-gray-600">{t('riskTreatment.table.headers.associatedBusinessValue')}</th>
                                        <th className="px-3 py-2 font-medium text-gray-600">{t('riskTreatment.table.headers.primaryImpact')}</th>
                                        <th className="px-3 py-2 font-medium text-gray-600 text-center">{t('riskTreatment.table.headers.initialSeverity')}</th>
                                    </tr>
                                </thead>
                                <tbody className="divide-y divide-gray-200">
                                    {detailedDreadedEvents.map((de) => (
                                        <tr key={de.id} className="hover:bg-gray-50 align-top">
                                            <td className="px-3 py-2 text-gray-800 font-medium">{de.name}</td>
                                            <td className="px-3 py-2 text-gray-700">{de.description}</td>
                                            <td className="px-3 py-2 text-gray-700">{de.businessValueName}</td>
                                            <td className="px-3 py-2 text-gray-700">
                                                {de.impactLabels && de.impactLabels.length > 0 && de.impactLabels[0] !== 'Non spécifié' ? (
                                                    <div className="flex flex-wrap gap-1">
                                                        {de.impactLabels.map((label, idx) => (
                                                            <span
                                                                key={idx}
                                                                className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-50 text-blue-800 border border-blue-200"
                                                            >
                                                                {label}
                                                            </span>
                                                        ))}
                                                    </div>
                                                ) : (
                                                    <span className="text-gray-400 text-sm">Non spécifié</span>
                                                )}
                                            </td>
                                            <td className="px-3 py-2 text-center">
                                                <div className={`severity-box inline-flex flex-col items-center justify-center w-14 h-9 p-0.5 rounded text-[0.6rem] font-semibold border border-gray-200 ${de.severityInfo.bgClass} ${de.severityInfo.textClass}`}>
                                                    <span>{de.severityInfo.level}</span>
                                                    <span className="label text-[0.55rem] leading-tight mt-0.5">{de.severityInfo.label}</span>
                                                </div>
                                            </td>
                                        </tr>
                                    ))}
                                </tbody>
                            </table>
                        </div>
                    ) : (
                         <p className="text-sm text-gray-500 italic text-center py-4">Aucun événement redouté défini.</p>
                    )}
                </div>

                {/* Section: Security Framework -> Detailed Rules */}
                <div className="section-card bg-white p-6 rounded-lg shadow-md mb-6">
                    <h2 className="text-xl font-semibold text-yellow-700 border-b border-gray-300 pb-2 mb-4 flex items-center">
                        <ShieldCheck size={20} className="mr-2 text-yellow-600" />{t('riskTreatment.securityFramework')}
                    </h2>
                    <p className="text-sm mb-4 text-gray-700">Nombre total de règles de sécurité sélectionnées : <strong className="text-lg">{detailedSecurityRules.length}</strong></p>
                    {detailedSecurityRules.length > 0 ? (
                        // UPDATED: Display detailed table instead of summary
                        <div className="overflow-x-auto -mx-6 px-6">
                            <table className="min-w-full detail-table security-rule-table text-xs border border-gray-200">
                                <thead className="bg-gray-100">
                                    <tr>
                                        <th className="px-3 py-2 font-medium text-gray-600">Nom Règle</th>
                                        <th className="px-3 py-2 font-medium text-gray-600">Description</th>
                                        <th className="px-3 py-2 font-medium text-gray-600">Événements Redoutés Associés</th>
                                        <th className="px-3 py-2 font-medium text-gray-600">Statut</th>
                                    </tr>
                                </thead>
                                <tbody className="divide-y divide-gray-200">
                                     {detailedSecurityRules.map((rule) => (
                                        <tr key={rule.id} className="hover:bg-gray-50 align-top">
                                            <td className="px-3 py-2 text-gray-800 font-medium">{rule.name}</td>
                                            <td className="px-3 py-2 text-gray-700">{rule.description}</td>
                                            <td className="px-3 py-2 text-gray-700">
                                                {rule.linkedDreadedEvents && rule.linkedDreadedEvents.length > 0 ? (
                                                    <ul className="list-disc list-inside pl-1 m-0 space-y-0.5">
                                                        {rule.linkedDreadedEvents.map((deName, idx) => (
                                                            <li key={idx}>{deName}</li>
                                                        ))}
                                                    </ul>
                                                ) : (
                                                    <span className="text-gray-400 italic">Aucun</span>
                                                )}
                                            </td>
                                            <td className="px-3 py-2 text-gray-700">{rule.statusLabel}</td>
                                        </tr>
                                    ))}
                                </tbody>
                            </table>
                        </div>
                    ) : (
                         <p className="text-sm text-gray-500 italic text-center py-4">Aucune règle de sécurité sélectionnée.</p>
                    )}
                </div>

                {/* --- NEW: Treatment Strategy Overview --- */}
                <div className="section-card bg-white p-6 rounded-lg shadow-sm border border-gray-200 mb-6">
                    <h2 className="text-xl font-semibold text-cyan-700 border-b border-gray-300 pb-2 mb-4 flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" width="18" height="18" className="mr-2 text-cyan-600"><path fillRule="evenodd" d="M15.312 11.424a5.5 5.5 0 01-9.201-2.466l-.74 1.063a.75.75 0 11-1.214-.882l1.06-1.518a.75.75 0 011.06 0l1.518 1.06a.75.75 0 11-.882 1.214l-1.062-.74A4.002 4.002 0 0010 14.5a4 4 0 003.536-1.97l.832.594a.75.75 0 00.956-1.343l-.595-.833a5.48 5.48 0 01-1.784-1.555l.987.493a.75.75 0 10.67-1.342l-.987-.493c.23-.304.44-.627.632-.962l.987.493a.75.75 0 10.67-1.342l-.987-.493a5.5 5.5 0 01-1.314-3.618l1.06-.74a.75.75 0 00-.882-1.214l-1.06 1.518a.75.75 0 101.214.882l.74-1.063a5.5 5.5 0 019.201 2.466 5.5 5.5 0 01-4.688 4.01l-.001.001z" clipRule="evenodd" /></svg>
                        {t('riskTreatment.treatmentStrategies')}
                    </h2>
                    <p className="text-sm mb-4 text-gray-700">Répartition des {detailedTreatmentPlan.length} couples Règle/Événement traités par stratégie :</p>
                    <ul className="list-disc pl-6 text-sm text-gray-800 space-y-1">
                        <li><strong>Réduire le risque :</strong> {treatmentStrategySummary.reduce} occurrences</li>
                        <li><strong>Accepter le risque :</strong> {treatmentStrategySummary.accept} occurrences</li>
                        <li><strong>Transférer le risque :</strong> {treatmentStrategySummary.transfer} occurrences</li>
                        <li><strong>Éviter le risque :</strong> {treatmentStrategySummary.avoid} occurrences</li>
                        {treatmentStrategySummary.undefined > 0 && (
                            <li className="text-gray-500">Non défini : {treatmentStrategySummary.undefined} occurrences</li>
                        )}
                    </ul>
                </div>

                {/* Section: Detailed Treatment Plan */}
                <div className="section-card bg-white p-6 rounded-lg shadow-sm border border-gray-200 mb-6">
                    <h2 className="text-xl font-semibold text-purple-700 border-b border-gray-300 pb-2 mb-4 flex items-center">
                        <CheckSquare size={20} className="mr-2 text-purple-600" />{t('riskTreatment.detailedPlan')}
                    </h2>
                    {detailedTreatmentPlan.length > 0 ? (
                        <div className="overflow-x-auto -mx-6 px-6"> {/* Allow horizontal scroll if needed */}
                            {/* Improved Table Style */}
                            <table id="treatment-plan-table" className="w-full border-collapse border border-gray-300 table-fixed treatment-plan-table text-[13px]">
                                {/* Explicit Column Widths */}
                                <colgroup>
                                    <col style={{ width: '12%' }} />
                                    <col style={{ width: '12%' }} />
                                    <col style={{ width: '10%' }} />
                                    <col style={{ width: '7%' }} />
                                    <col style={{ width: '8%' }} />
                                    <col style={{ width: '18%' }} />
                                    <col style={{ width: '7%' }} />
                                    <col style={{ width: '6%' }} />
                                    <col style={{ width: '14%' }} />
                                </colgroup>
                                <thead className="bg-gray-100">
                                    <tr>
                                        {/* Adjusted Padding and Font */}
                                        <th className="px-3 py-2 font-medium text-gray-600">Règle de Sécurité</th>
                                        <th className="px-3 py-2 font-medium text-gray-600">Événement Redouté</th>
                                        <th className="px-3 py-2 font-medium text-gray-600">Impact Principal</th>
                                        <th className="px-3 py-2 font-medium text-gray-600 text-center">Grav. Init.</th>
                                        <th className="px-3 py-2 font-medium text-gray-600">Option Traitement</th>
                                        <th className="px-3 py-2 font-medium text-gray-600">Contrôles Associés</th>
                                        <th className="px-3 py-2 font-medium text-gray-600 text-center">Grav. Résid.</th>
                                        <th className="px-3 py-2 font-medium text-gray-600 text-center">Réduction (%)</th>
                                        <th className="px-3 py-2 font-medium text-gray-600">Responsable / Décision</th>
                                    </tr>
                                </thead>
                                <tbody className="divide-y divide-gray-200">
                                    {detailedTreatmentPlan.map((item, index) => {
                                        // Calculate reduction
                                        const initialValue = item.initialSeverity?.value || 0;
                                        const residualValue = item.residualSeverity?.value || 0;
                                        let reductionPercent = 'N/A';
                                        if (initialValue > 0 && residualValue < initialValue) {
                                            reductionPercent = Math.round(((initialValue - residualValue) / initialValue) * 100) + '%';
                                        } else if (initialValue > 0 && residualValue >= initialValue) {
                                            reductionPercent = '0%'; // No reduction or increase
                                        }

                                        // ---> Check if it's the start of a new rule group <---
                                        const isNewRuleGroup = index === 0 || (item.ruleId && detailedTreatmentPlan[index - 1]?.ruleId !== item.ruleId);

                                        // ---> Calculate rowSpan if it's a new group <---
                                        let ruleRowSpan = 1;
                                        if (isNewRuleGroup && item.ruleId) {
                                            for (let i = index + 1; i < detailedTreatmentPlan.length; i++) {
                                                if (detailedTreatmentPlan[i]?.ruleId === item.ruleId) {
                                                    ruleRowSpan++;
                                                } else {
                                                    break; // Stop counting when rule ID changes
                                                }
                                            }
                                        }

                                        return (
                                        <tr key={index} className="hover:bg-gray-50 align-top">
                                                {/* Conditionally render Rule cell with rowSpan */}
                                                {isNewRuleGroup && (
                                                    <td className="px-3 py-2 text-gray-800 font-medium align-top" rowSpan={ruleRowSpan}>{item.ruleName}</td>
                                                )}
                                            <td className="px-3 py-2 text-gray-700">{item.deName}</td>
                                            <td className="px-3 py-2 text-gray-700">
                                                {item.impactLabels && item.impactLabels.length > 0 && item.impactLabels[0] !== 'Non spécifié' ? (
                                                    <div className="flex flex-wrap gap-1">
                                                        {item.impactLabels.map((label, idx) => (
                                                            <span
                                                                key={idx}
                                                                className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-blue-50 text-blue-800 border border-blue-200"
                                                            >
                                                                {label}
                                                            </span>
                                                        ))}
                                                    </div>
                                                ) : (
                                                    <span className="text-gray-400 text-sm">Non spécifié</span>
                                                )}
                                            </td>
                                            <td className="px-3 py-2 text-center">
                                                {/* Smaller Severity Box */}
                                                <div className={`severity-box inline-flex flex-col items-center justify-center w-14 h-9 p-0.5 rounded text-[0.6rem] font-semibold border border-gray-200 ${item.initialSeverity.bgClass} ${item.initialSeverity.textClass}`}>
                                                    <span>{item.initialSeverity.level}</span>
                                                    <span className="label text-[0.55rem] leading-tight mt-0.5">{item.initialSeverity.label}</span>
                                                </div>
                                            </td>
                                            <td className="px-3 py-2 text-gray-700 font-medium">{item.treatmentOption}</td>
                                            <td className="px-3 py-2 text-gray-700">
                                                {item.controls.length > 0 ? (
                                                        <div className="space-y-1">
                                                            {item.controls.map((mappedCtrl, cIdx) => (
                                                                <div key={mappedCtrl.controlId || cIdx} className="text-xs">
                                                                    • <strong>{mappedCtrl.controlName || '???'}</strong>
                                                                    {mappedCtrl.description !== 'Pas de description' && <span className="block text-gray-600 text-[0.7rem] italic pl-1">↳ {mappedCtrl.description}</span>}
                                                                </div>
                                                            ))}
                                                        </div>
                                                ) : (
                                                    <span className="text-xs italic text-gray-500">Aucun</span>
                                                )}
                                            </td>
                                            <td className="px-3 py-2 text-center">
                                                <div className={`severity-box inline-flex flex-col items-center justify-center w-14 h-9 p-0.5 rounded text-[0.6rem] font-semibold border border-gray-200 ${item.residualSeverity.bgClass} ${item.residualSeverity.textClass}`}>
                                                    <span>{item.residualSeverity.level}</span>
                                                    <span className="label text-[0.55rem] leading-tight mt-0.5">{item.residualSeverity.label}</span>
                                                </div>
                                            </td>
                                                <td className="px-3 py-2 text-center font-semibold text-gray-700">
                                                    {reductionPercent}
                                                </td>
                                            <td className="px-3 py-2 text-gray-700">
                                                {item.controls.length > 0 ? (
                                                        <div className="space-y-1.5">
                                                        {item.controls.map((ctrl, cIdx) => (
                                                                <div key={ctrl.controlId || cIdx} className="text-xs leading-snug">
                                                                    <div><strong className="text-gray-500">Resp:</strong> {ctrl.responsiblePerson || '-'}</div>
                                                                    <div><strong className="text-gray-500">Décision:</strong> {getDecisionLabel({ decision: ctrl.decision, date: ctrl.decisionDate })}</div>
                                                                    {ctrl.decision === 'accepted' && ctrl.acceptanceRationale && (
                                                                        <div className="text-xs italic text-gray-500 pl-1">Raison: {ctrl.acceptanceRationale}</div>
                                                                    )}
                                                                </div>
                                                            ))}
                                                        </div>
                                                ) : (
                                                    <span className="text-xs italic text-gray-500">N/A</span>
                                                )}
                                            </td>
                                        </tr>
                                        );
                                    })}
                                </tbody>
                            </table>
                        </div>
                    ) : (
                        <p className="text-sm text-gray-500 italic text-center py-6">Aucun plan de traitement détaillé disponible (vérifiez l'étape 'Analyse et Association des Contrôles').</p>
                    )}
                </div>

                {/* --- NEW: Accepted Risks Summary --- */}
                {acceptedRisks.length > 0 && (
                    <div className="section-card bg-yellow-50 p-6 rounded-lg shadow-sm mb-6 border border-yellow-200">
                        <h2 className="text-xl font-semibold text-yellow-800 border-b border-yellow-300 pb-2 mb-4 flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" width="18" height="18" className="mr-2 text-yellow-600"><path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.483 4.79-1.88-1.88a.75.75 0 10-1.06 1.061l2.5 2.5a.75.75 0 001.137-.089l4-5.5z" clipRule="evenodd" /></svg>
                            {t('riskTreatment.acceptedRisks')}
                        </h2>
                        <p className="text-sm mb-4 text-yellow-900">Liste des risques pour lesquels la stratégie "Accepter" a été choisie :</p>
                        <div className="overflow-x-auto -mx-6 px-6">
                             <table className="min-w-full detail-table text-xs border border-yellow-300">
                                 <thead className="bg-yellow-100">
                                     <tr>
                                         <th className="px-3 py-2 font-medium text-yellow-800 w-[25%]">Règle de Sécurité</th>
                                         <th className="px-3 py-2 font-medium text-yellow-800 w-[25%]">Événement Redouté</th>
                                         <th className="px-3 py-2 font-medium text-yellow-800 w-[20%]">Impact Principal</th>
                                         <th className="px-3 py-2 font-medium text-yellow-800 w-[15%] text-center">Gravité Initiale</th>
                                         <th className="px-3 py-2 font-medium text-yellow-800 w-[15%] text-center">Gravité Résiduelle</th>
                                     </tr>
                                 </thead>
                                 <tbody className="divide-y divide-yellow-200">
                                     {acceptedRisks.map((item) => (
                                         <tr key={item.id} className="hover:bg-yellow-50 align-top">
                                             <td className="px-3 py-2 text-gray-800 font-medium">{item.ruleName}</td>
                                             <td className="px-3 py-2 text-gray-700">{item.deName}</td>
                                             <td className="px-3 py-2 text-gray-700">
                                                {item.impactLabels && item.impactLabels.length > 0 && item.impactLabels[0] !== 'Non spécifié' ? (
                                                    <div className="flex flex-wrap gap-1">
                                                        {item.impactLabels.map((label, idx) => (
                                                            <span
                                                                key={idx}
                                                                className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-yellow-50 text-yellow-800 border border-yellow-200"
                                                            >
                                                                {label}
                                                            </span>
                                                        ))}
                                                    </div>
                                                ) : (
                                                    <span className="text-gray-400 text-sm">Non spécifié</span>
                                                )}
                                             </td>
                                             <td className="px-3 py-2 text-center">
                                                 {/* Simplified severity display */}
                                                 <span className={`font-semibold ${item.initialSeverity.textClass} ${item.initialSeverity.bgClass} px-1.5 py-0.5 rounded text-[0.65rem]`}>{item.initialSeverity.level}</span>
                                             </td>
                                             <td className="px-3 py-2 text-center">
                                                 <span className={`font-semibold ${item.residualSeverity.textClass} ${item.residualSeverity.bgClass} px-1.5 py-0.5 rounded text-[0.65rem]`}>{item.residualSeverity.level}</span>
                                             </td>
                                         </tr>
                                     ))}
                                 </tbody>
                             </table>
                        </div>
                    </div>
                )}

                {/* --- NEW: Residual Risk Distribution --- */}
                <div className="section-card bg-white p-6 rounded-lg shadow-sm border border-gray-200 mb-6">
                    <h2 className="text-xl font-semibold text-green-700 border-b border-gray-300 pb-2 mb-4 flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" width="18" height="18" className="mr-2 text-green-600"><path d="M15.5 2.75a.75.75 0 00-1.5 0v14.5a.75.75 0 001.5 0V2.75zM10.5 10a.75.75 0 00-1.5 0v7.25a.75.75 0 001.5 0V10zM5.5 5.75a.75.75 0 00-1.5 0v11.5a.75.75 0 001.5 0V5.75z" /></svg>
                         {t('riskTreatment.residualDistribution')}
                    </h2>
                    <p className="text-sm mb-4 text-gray-700">Répartition des {detailedTreatmentPlan.length} couples Règle/Événement par niveau de gravité résiduel estimé :</p>
                     <div className="overflow-x-auto -mx-6 px-6">
                         <table className="min-w-full detail-table text-xs border border-gray-200 w-3/4 mx-auto"> {/* Centered & Smaller Table */}
                             <thead className="bg-gray-100">
                                 <tr>
                                     <th className="px-3 py-2 font-medium text-gray-600 w-1/2">Niveau de Gravité Résiduel</th>
                                     <th className="px-3 py-2 font-medium text-gray-600 w-1/2 text-center">Nombre d'Occurrences</th>
                                 </tr>
                             </thead>
                             <tbody className="divide-y divide-gray-100">
                                 {Object.entries(residualRiskDistribution)
                                     // Show levels with count > 0, always show N/A if present
                                     .filter(([level, count]) => count > 0 || level === 'N/A')
                                     .map(([level, count]) => (
                                     <tr key={level} className="hover:bg-gray-50">
                                         <td className="px-4 py-2 text-gray-700">
                                              {(() => {
                                                 // Find the label corresponding to the level code (G1, G2 etc.)
                                                 // Note: This assumes getSeverityLevelInfo returns an object with a 'level' property matching the key
                                                 // A better approach might be a dedicated map if levels are complex
                                                 const levelLabelMap = {
                                                      'G5': 'Catastrophique', 'G4': 'Critique', 'G3': 'Majeure',
                                                      'G2': 'Significative', 'G1': 'Mineure', 'N/A': 'Non défini'
                                                 };
                                                 const label = levelLabelMap[level] || level; // Fallback to level code if not found
                                                 return `${label} (${level})`;
                                             })()}
                                         </td>
                                         <td className="px-4 py-2 text-gray-700 text-center font-semibold">{count}</td>
                                     </tr>
                                 ))}
                             </tbody>
                         </table>
                     </div>
                </div>

            </div> {/* End Print Area */}
        </motion.div>
    );
};

export default RiskTreatment;