const express = require('express');
const {
    createFrameworkDefinition,
    getFrameworkDefinitions,
    // Import update/delete controllers later if needed
} = require('../controllers/frameworkDefinitionController');

const { protect } = require('../middleware/authMiddleware'); // Assuming your auth middleware is here

const router = express.Router();

// Apply protect middleware to all routes in this file
router.use(protect);

// Route to get definitions (predefined + custom for a specific analysis via query param)
// Route to create a new custom definition
router.route('/')
    .get(getFrameworkDefinitions)
    .post(createFrameworkDefinition);

// Add routes for update/delete later
// router.route('/:id')
//     .put(updateFrameworkDefinition)
//     .delete(deleteFrameworkDefinition);

module.exports = router;