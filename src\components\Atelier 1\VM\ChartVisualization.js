// ChartVisualization.js
import React from 'react';
import { useTranslation } from 'react-i18next';
import { SECURITY_PILLARS } from '../../../constants';

const ChartVisualization = ({
  businessValues,
  selectedValueForChart,
  setSelectedValueForChart,
  zoomLevel,
  setZoomLevel,
  viewportPosition,
  setViewportPosition,
  isDragging,
  setIsDragging,
  dragStart,
  setDragStart,
  useShortIds,
  chartContainerRef
}) => {
  const { t } = useTranslation();
  // Fonction pour gérer le zoom
  const handleZoomIn = () => setZoomLevel(prev => Math.min(prev + 0.1, 2));
  const handleZoomOut = () => setZoomLevel(prev => Math.max(prev - 0.1, 0.5));
  const handleZoomReset = () => {
    setZoomLevel(0.75); // Reset to 1.5x zoom out (0.75 zoom level)
    setViewportPosition({ x: 0, y: 0 });
  };

  // Gestion du déplacement dans la vue
  const handleMouseDown = (e) => {
    if (e.button === 0) { // Click gauche uniquement
      setIsDragging(true);
      setDragStart({ x: e.clientX, y: e.clientY });
    }
  };

  const handleMouseMove = (e) => {
    if (isDragging) {
      const dx = e.clientX - dragStart.x;
      const dy = e.clientY - dragStart.y;
      setViewportPosition(prev => ({ x: prev.x + dx, y: prev.y + dy }));
      setDragStart({ x: e.clientX, y: e.clientY });
    }
  };

  const handleMouseUp = () => {
    setIsDragging(false);
  };

  // Rechercher la valeur métier sélectionnée
  const selectedBusinessValue = businessValues.find(value => value.id === selectedValueForChart);

  return (
    <div className="lg:w-2/3 border rounded-lg p-4 bg-gray-50 overflow-hidden">
      <div className="flex justify-between items-center mb-4">
        <h3 className="font-semibold text-gray-700">{t('businessValues.chart.globalMapping')}</h3>

        {/* Contrôles de zoom */}
        <div className="flex space-x-2">
          <button
            className="bg-white border rounded-md p-1 text-gray-600 hover:bg-gray-100"
            onClick={handleZoomIn}
            title={t('businessValues.chart.zoomIn')}
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
          </button>
          <button
            className="bg-white border rounded-md p-1 text-gray-600 hover:bg-gray-100"
            onClick={handleZoomOut}
            title={t('businessValues.chart.zoomOut')}
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M18 12H6" />
            </svg>
          </button>
          <button
            className="bg-white border rounded-md p-1 text-gray-600 hover:bg-gray-100"
            onClick={handleZoomReset}
            title={t('businessValues.chart.resetView')}
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
            </svg>
          </button>
        </div>
      </div>

      <div
        ref={chartContainerRef}
        className="relative overflow-hidden cursor-move"
        style={{ height: '600px' }} // Increased height to accommodate more content
        onMouseDown={handleMouseDown}
        onMouseMove={handleMouseMove}
        onMouseUp={handleMouseUp}
        onMouseLeave={handleMouseUp}
      >
        {/* Info-bulle pour la valeur sélectionnée - placée en dehors du périmètre */}
        {selectedBusinessValue && (
          <div className="absolute top-2 right-2 bg-white p-2 rounded-md border shadow-sm z-30">
            <div className="flex items-center">
              <span className="text-sm font-semibold text-blue-600 mr-1">{selectedBusinessValue.shortId}</span>
              <span className="text-sm">{selectedBusinessValue.name}</span>
            </div>
            <div className="text-xs text-gray-500 mt-1">
              {selectedBusinessValue.supportAssets?.length || 0} {t('businessValues.chart.supportAssetCount')} •
              {selectedBusinessValue.securityPillars?.length || 0} {t('businessValues.chart.pillarCount')}
            </div>
          </div>
        )}

        {/* Périmètre d'étude */}
        <div
          className="absolute border-[3px] border-gray-400 border-dashed rounded-lg"
          style={{
            width: `800px`,
            height: `600px`,
            left: `calc(50% - 400px + ${viewportPosition.x}px)`,
            top: `calc(50% - 300px + ${viewportPosition.y}px)`,
            transform: `scale(${zoomLevel})`,
            transformOrigin: 'center',
            zIndex: 0
          }}
        >
          <div className="absolute -top-3 left-1/2 transform -translate-x-1/2 bg-gray-50 px-2">
            <span className="font-medium text-gray-500">{t('businessValues.chart.studyPerimeter')}</span>
          </div>

          {/* Info-bulle déplacée à l'extérieur du périmètre */}

          {/* Éléments internes qui seront transformés ensemble */}
          <div className="absolute inset-0">
            {/* Cercle central pour chaque valeur métier */}
            {businessValues.map((value, index) => {
              const angle = (Math.PI * 2 * index) / businessValues.length;
              const radius = 200; // Distance from center
              const x = 400 + radius * Math.cos(angle);
              const y = 300 + radius * Math.sin(angle);
              const isSelected = value.id === selectedValueForChart;

              return (
                <div key={value.id}>
                  {/* Ligne de connection au centre */}
                  <svg className="absolute top-0 left-0 w-full h-full" style={{ zIndex: 0 }}>
                    <line
                      x1="400"
                      y1="300"
                      x2={x}
                      y2={y}
                      stroke={isSelected ? "#3182CE" : "#A0AEC0"}
                      strokeWidth={isSelected ? "3" : "2"}
                    />
                  </svg>

                  {/* Cercle de la valeur métier */}
                  <div
                    className={`absolute flex items-center justify-center rounded-full
                      border-[3px] bg-white cursor-pointer transform transition-all duration-200
                      ${isSelected ? 'border-blue-500 shadow-md' : 'border-gray-400'}`}
                    style={{
                      width: isSelected ? '110px' : '90px',
                      height: isSelected ? '110px' : '90px',
                      left: x - (isSelected ? 55 : 45),
                      top: y - (isSelected ? 55 : 45),
                      zIndex: isSelected ? 10 : 1
                    }}
                    onClick={() => setSelectedValueForChart(value.id)}
                  >
                    <div className="text-center p-1">
                      <div className={`font-medium text-sm ${isSelected ? 'text-blue-600' : 'text-gray-800'}`}>
                        {useShortIds ? value.shortId : (
                          value.name.length > 15 ? `${value.name.substring(0, 12)}...` : value.name
                        )}
                      </div>
                      <div className="text-sm text-gray-600">
                        {!useShortIds && <span className="font-semibold">{value.shortId}</span>}
                        {(value.supportAssets || []).length > 0 &&
                          <span>{!useShortIds && ' • '}{(value.supportAssets || []).length} {t('businessValues.chart.assets')}</span>
                        }
                      </div>
                    </div>
                  </div>

                  {/* Petits cercles pour les piliers de sécurité */}
                  {(value.securityPillars || []).map((pillarId, pIndex) => {
                    const pillar = SECURITY_PILLARS.find(p => p.id === pillarId);
                    if (!pillar) return null;

                    const pillarAngle = (Math.PI * 2 * pIndex) / (value.securityPillars || []).length;
                    const pillarRadius = 70;
                    const pX = x + pillarRadius * Math.cos(pillarAngle);
                    const pY = y + pillarRadius * Math.sin(pillarAngle);

                    return (
                      <div
                        key={`${value.id}-${pillarId}`}
                        className="absolute rounded-full flex items-center justify-center text-white text-sm shadow-sm font-medium"
                        style={{
                          backgroundColor: pillar.color,
                          width: '36px',
                          height: '36px',
                          left: pX - 18,
                          top: pY - 18,
                          zIndex: 5
                        }}
                        title={pillar.name}
                      >
                        {pillar.name.substring(0, 1)}
                      </div>
                    );
                  })}

                  {/* Afficher les biens support */}
                  {(value.supportAssets || []).map((asset, assetIndex) => {
                    const totalAssets = value.supportAssets.length;
                    const assetAngleOffset = (Math.PI / 3) * (assetIndex / Math.max(totalAssets, 1));
                    const assetAngle = angle + Math.PI/6 + assetAngleOffset;

                    const assetRadius = 130;
                    const aX = x + assetRadius * Math.cos(assetAngle);
                    const aY = y + assetRadius * Math.sin(assetAngle);

                    return (
                      <div key={`asset-${asset.id}`}>
                        {/* Ligne pointillée vers le bien support */}
                        <svg className="absolute top-0 left-0 w-full h-full" style={{ zIndex: 0 }}>
                          <line x1={x} y1={y} x2={aX} y2={aY}
                                stroke={isSelected ? "#4299E1" : "#A0AEC0"}
                                strokeWidth={isSelected ? "2.5" : "1.5"}
                                strokeDasharray="5 3" />
                        </svg>

                        {/* Bien support */}
                        <div
                          className={`absolute flex items-center justify-center rounded border-2 text-sm shadow-sm font-medium
                            ${isSelected ? 'bg-blue-50 border-blue-400 text-blue-800' : 'bg-gray-100 border-gray-400 text-gray-800'}`}
                          style={{
                            width: '80px',
                            height: '36px',
                            left: aX - 40,
                            top: aY - 18,
                            zIndex: 4
                          }}
                          title={`${asset.shortId} - ${asset.name}`}
                        >
                          {useShortIds ?
                            <span className="font-semibold">{asset.shortId}</span> :
                            <span className="truncate px-1">{asset.name}</span>
                          }
                        </div>
                      </div>
                    );
                  })}
                </div>
              );
            })}

            {/* Cercle central */}
            <div
              className="absolute rounded-full bg-gray-200 flex items-center justify-center shadow-sm"
              style={{
                width: '60px',
                height: '60px',
                left: '370px',
                top: '270px',
                zIndex: 2
              }}
            >
              <span className="text-xs text-gray-600 font-medium">SI</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ChartVisualization;