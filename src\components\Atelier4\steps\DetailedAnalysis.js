// components/Atelier4/steps/DetailedAnalysis.js
import React, { useState, useEffect } from 'react';
import { useWorkflow } from '../../../contexts/WorkflowContext';
import { api } from '../../../api/apiClient';
import { Eye, AlertTriangle, ArrowRight, CheckCircle, X, Edit3, Save, Database } from 'lucide-react';

const DetailedAnalysis = () => {
  const { updateStep, selectedSupportAssets, currentSession, updateAssetAnalysis } = useWorkflow();
  const [assetAnalyses, setAssetAnalyses] = useState({});
  const [selectedAssetId, setSelectedAssetId] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [error, setError] = useState(null);
  const [isUpdating, setIsUpdating] = useState(false);
  const [editingItem, setEditingItem] = useState(null);

  useEffect(() => {
    if (currentSession && selectedSupportAssets && selectedSupportAssets.length > 0) {
      performDetailedAnalysis();
    }
  }, [currentSession, selectedSupportAssets]);

  const performDetailedAnalysis = async () => {
    const selectedAssets = selectedSupportAssets.filter(asset => asset.selected);

    if (selectedAssets.length === 0) return;

    setIsAnalyzing(true);
    setError(null);

    try {
      const response = await api.post(`/workflow/sessions/${currentSession._id}/detailed-analysis`, {
        assetIds: selectedAssets.map(asset => asset.assetId)
      });

      if (response.data.success) {
        const analysesMap = {};
        response.data.data.forEach(analysis => {
          analysesMap[analysis.assetId] = analysis;
        });
        setAssetAnalyses(analysesMap);

        // Sélectionner le premier actif par défaut
        if (selectedAssets.length > 0 && !selectedAssetId) {
          setSelectedAssetId(selectedAssets[0].assetId);
        }

        console.log('[DetailedAnalysis] Analysis completed for', response.data.data.length, 'assets');
      } else {
        throw new Error(response.data.message);
      }
    } catch (error) {
      console.error('[DetailedAnalysis] Error performing detailed analysis:', error);
      setError(error.message);
    } finally {
      setIsAnalyzing(false);
      setIsLoading(false);
    }
  };

  const handleStatusUpdate = async (itemType, itemId, statusData) => {
    if (!selectedAssetId) return;

    try {
      const success = await updateAssetAnalysis(selectedAssetId, itemType, itemId, statusData);

      if (success) {
        // Recharger l'analyse de l'actif
        const response = await api.get(`/workflow/sessions/${currentSession._id}/assets/${selectedAssetId}/analysis`);
        if (response.data.success) {
          setAssetAnalyses(prev => ({
            ...prev,
            [selectedAssetId]: response.data.data
          }));
        }
        setEditingItem(null);
      }
    } catch (error) {
      console.error('[DetailedAnalysis] Error updating status:', error);
      setError('Erreur lors de la mise à jour du statut.');
    }
  };

  const handleContinue = async () => {
    const completedAnalyses = Object.values(assetAnalyses);

    if (completedAnalyses.length === 0) {
      setError('Aucune analyse complétée. Veuillez d\'abord analyser les biens supports.');
      return;
    }

    setIsUpdating(true);

    try {
      const analysisData = {
        detailedAnalysis: {
          assetAnalyses: completedAnalyses.map(analysis => ({
            assetId: analysis.assetId,
            assetName: analysis.assetName,
            vulnerabilities: analysis.vulnerabilities.map(v => ({
              id: v.id,
              description: v.description,
              cvss: v.cvss,
              severity: v.severity,
              status: v.userStatus?.status || 'non_traité',
              priority: v.userStatus?.priority,
              selectedForScenario: v.userStatus?.selectedForScenario || false,
              notes: v.userStatus?.notes
            })),
            attackTechniques: analysis.attackTechniques.map(t => ({
              id: t.id,
              name: t.name,
              tactic: t.tactic,
              description: t.description,
              status: t.userStatus?.status || 'non_traité',
              selectedForScenario: t.userStatus?.selectedForScenario || false,
              notes: t.userStatus?.notes
            })),
            completionPercentage: analysis.metrics?.completionPercentage || 0
          }))
        }
      };

      const success = await updateStep(4, analysisData);

      if (success) {
        console.log('[DetailedAnalysis] Successfully moved to step 4');
      }
    } catch (error) {
      console.error('[DetailedAnalysis] Error updating step:', error);
      setError('Erreur lors de la sauvegarde. Veuillez réessayer.');
    } finally {
      setIsUpdating(false);
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'corrigé':
      case 'mitigé':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'planifié':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'accepté':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'non_traité':
      default:
        return 'bg-red-100 text-red-800 border-red-200';
    }
  };

  const getSeverityColor = (severity) => {
    switch (severity?.toUpperCase()) {
      case 'CRITICAL':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'HIGH':
        return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'MEDIUM':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'LOW':
        return 'bg-green-100 text-green-800 border-green-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  if (!selectedSupportAssets || selectedSupportAssets.filter(a => a.selected).length === 0) {
    return (
      <div className="text-center py-12">
        <AlertTriangle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">
          Aucun bien support sélectionné
        </h3>
        <p className="text-gray-600">
          Veuillez d'abord sélectionner des biens supports à l'étape précédente.
        </p>
      </div>
    );
  }

  if (isLoading || isAnalyzing) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600 mx-auto mb-4"></div>
          <p className="text-gray-600">
            {isAnalyzing ? 'Analyse des vulnérabilités en cours...' : 'Chargement...'}
          </p>
        </div>
      </div>
    );
  }

  const selectedAssets = selectedSupportAssets.filter(asset => asset.selected);
  const currentAnalysis = selectedAssetId ? assetAnalyses[selectedAssetId] : null;

  return (
    <div className="space-y-8">
      {/* En-tête */}
      <div className="text-center">
        <div className="flex items-center justify-center mb-4">
          <div className="p-3 bg-indigo-100 rounded-full">
            <Eye className="h-8 w-8 text-indigo-600" />
          </div>
        </div>
        <h1 className="text-3xl font-bold text-gray-900 mb-2">
          Analyse Détaillée des Vulnérabilités
        </h1>
        <p className="text-lg text-gray-600 max-w-2xl mx-auto">
          Examinez et gérez le statut de chaque vulnérabilité et technique d'attaque
          identifiée pour vos biens supports.
        </p>
      </div>

      {/* Sélecteur d'actif */}
      <div className="bg-white rounded-lg shadow p-6">
        <h2 className="text-lg font-semibold text-gray-900 mb-4">
          Biens Supports Analysés ({selectedAssets.length})
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {selectedAssets.map((asset) => {
            const analysis = assetAnalyses[asset.assetId];
            const isSelected = selectedAssetId === asset.assetId;

            return (
              <div
                key={asset.assetId}
                className={`
                  p-4 border-2 rounded-lg cursor-pointer transition-all duration-200
                  ${isSelected
                    ? 'border-indigo-500 bg-indigo-50 shadow-md'
                    : 'border-gray-200 bg-white hover:border-gray-300 hover:shadow-sm'
                  }
                `}
                onClick={() => setSelectedAssetId(asset.assetId)}
              >
                <div className="flex items-start justify-between mb-2">
                  <h3 className="font-semibold text-gray-900 text-sm">
                    {asset.assetName}
                  </h3>
                  {isSelected && (
                    <CheckCircle className="h-5 w-5 text-indigo-600" />
                  )}
                </div>

                {analysis && (
                  <div className="space-y-2 text-xs">
                    <div className="flex justify-between">
                      <span className="text-gray-600">Vulnérabilités:</span>
                      <span className="font-medium">{analysis.vulnerabilities?.length || 0}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Techniques:</span>
                      <span className="font-medium">{analysis.attackTechniques?.length || 0}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Progression:</span>
                      <span className="font-medium">{analysis.metrics?.completionPercentage || 0}%</span>
                    </div>
                  </div>
                )}
              </div>
            );
          })}
        </div>
      </div>

      {/* Analyse détaillée de l'actif sélectionné */}
      {currentAnalysis && (
        <div className="bg-white rounded-lg shadow">
          <div className="p-6 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <h2 className="text-xl font-semibold text-gray-900">
                {currentAnalysis.assetName}
              </h2>
              <div className="flex items-center space-x-4">
                <div className="text-sm text-gray-600">
                  Progression: {currentAnalysis.metrics?.completionPercentage || 0}%
                </div>
                <div className="w-24 bg-gray-200 rounded-full h-2">
                  <div
                    className="bg-indigo-600 h-2 rounded-full transition-all duration-300"
                    style={{ width: `${currentAnalysis.metrics?.completionPercentage || 0}%` }}
                  />
                </div>
              </div>
            </div>
          </div>

          {/* Onglets */}
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex">
              <button className="py-4 px-6 border-b-2 border-indigo-500 text-indigo-600 font-medium">
                Vulnérabilités ({currentAnalysis.vulnerabilities?.length || 0})
              </button>
              <button className="py-4 px-6 border-b-2 border-transparent text-gray-500 hover:text-gray-700">
                Techniques d'Attaque ({currentAnalysis.attackTechniques?.length || 0})
              </button>
            </nav>
          </div>

          {/* Liste des vulnérabilités */}
          <div className="p-6">
            <div className="space-y-4">
              {currentAnalysis.vulnerabilities?.map((vuln) => (
                <div key={vuln.id} className="border border-gray-200 rounded-lg p-4">
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex-1">
                      <div className="flex items-center space-x-3 mb-2">
                        <h4 className="font-semibold text-gray-900">{vuln.id}</h4>
                        <span className={`
                          inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border
                          ${getSeverityColor(vuln.severity)}
                        `}>
                          {vuln.severity}
                        </span>
                        <span className="text-sm text-gray-600">
                          CVSS: {vuln.cvss?.toFixed(1) || 'N/A'}
                        </span>
                      </div>
                      <p className="text-sm text-gray-600 mb-3">
                        {vuln.description}
                      </p>
                    </div>
                  </div>

                  {/* Statut et actions */}
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <span className={`
                        inline-flex items-center px-3 py-1 rounded-full text-xs font-medium border
                        ${getStatusColor(vuln.userStatus?.status)}
                      `}>
                        {vuln.userStatus?.status === 'corrigé' ? 'Corrigé' :
                         vuln.userStatus?.status === 'planifié' ? 'Planifié' :
                         vuln.userStatus?.status === 'accepté' ? 'Accepté' :
                         'Non traité'}
                      </span>

                      <label className="flex items-center space-x-2 text-sm">
                        <input
                          type="checkbox"
                          checked={vuln.userStatus?.selectedForScenario || false}
                          onChange={(e) => handleStatusUpdate('vulnerability', vuln._id, {
                            ...vuln.userStatus,
                            selectedForScenario: e.target.checked
                          })}
                          className="text-indigo-600 focus:ring-indigo-500 rounded"
                        />
                        <span className="text-gray-700">Inclure dans les scénarios</span>
                      </label>
                    </div>

                    <button
                      onClick={() => setEditingItem({ type: 'vulnerability', id: vuln._id, data: vuln })}
                      className="text-indigo-600 hover:text-indigo-700"
                    >
                      <Edit3 className="h-4 w-4" />
                    </button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Bouton de continuation */}
      <div className="flex justify-center pt-6">
        <button
          onClick={handleContinue}
          disabled={Object.keys(assetAnalyses).length === 0 || isUpdating}
          className={`
            flex items-center px-8 py-3 rounded-lg font-medium transition-all duration-200
            ${Object.keys(assetAnalyses).length > 0 && !isUpdating
              ? 'bg-indigo-600 text-white hover:bg-indigo-700 shadow-md hover:shadow-lg'
              : 'bg-gray-300 text-gray-500 cursor-not-allowed'
            }
          `}
        >
          {isUpdating ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              Sauvegarde...
            </>
          ) : (
            <>
              Continuer vers la génération de scénarios
              <ArrowRight className="ml-2 h-4 w-4" />
            </>
          )}
        </button>
      </div>

      {/* Erreur */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <div className="flex">
            <AlertTriangle className="h-5 w-5 text-red-400" />
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">Erreur</h3>
              <p className="mt-1 text-sm text-red-700">{error}</p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default DetailedAnalysis;
