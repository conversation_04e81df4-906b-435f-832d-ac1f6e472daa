// Test EUVD search endpoint with exact documentation parameters
const axios = require('axios');

async function testEUVDSearch() {
    console.log('🧪 Testing EUVD Search Endpoint with Documentation Parameters...\n');

    const EUVD_BASE_URL = 'https://euvdservices.enisa.europa.eu/api';

    // Test 1: Search with Oracle vendor (your use case)
    console.log('🔍 Test 1: Search for Oracle vulnerabilities');
    console.log('='.repeat(50));
    try {
        const params = new URLSearchParams({
            vendor: 'Oracle',
            fromScore: '4.0',
            toScore: '10.0',
            size: '10',
            page: '0'
        });

        console.log(`URL: ${EUVD_BASE_URL}/search?${params}`);

        const response = await axios.get(`${EUVD_BASE_URL}/search`, {
            params: {
                vendor: 'Oracle',
                fromScore: '4.0',
                toScore: '10.0',
                size: '10',
                page: '0'
            },
            headers: {
                'Accept': 'application/json',
                'User-Agent': 'EBIOS-RM-CTI-Test/1.0'
            },
            timeout: 15000
        });

        console.log(`✅ Status: ${response.status}`);
        console.log(`📊 Response type: ${typeof response.data}`);
        console.log(`📊 Is array: ${Array.isArray(response.data)}`);
        console.log(`📊 Length: ${response.data?.length || 'N/A'}`);
        console.log(`📊 Keys: ${Object.keys(response.data || {})}`);

        if (Array.isArray(response.data) && response.data.length > 0) {
            console.log(`🔍 First Oracle vulnerability:`, {
                id: response.data[0].cveId || response.data[0].id || 'No ID',
                description: (response.data[0].description || response.data[0].summary || 'No description').substring(0, 100) + '...',
                score: response.data[0].cvssScore || response.data[0].score || 'No score',
                vendor: response.data[0].vendor || 'No vendor',
                product: response.data[0].product || 'No product'
            });
        }

    } catch (error) {
        console.error(`❌ Error: ${error.message}`);
        if (error.response) {
            console.error(`   Status: ${error.response.status} ${error.response.statusText}`);
            console.error(`   Data: ${JSON.stringify(error.response.data)}`);
        }
    }

    console.log('\n');

    // Test 2: Search with MySQL product
    console.log('🔍 Test 2: Search for MySQL vulnerabilities');
    console.log('='.repeat(50));
    try {
        const response = await axios.get(`${EUVD_BASE_URL}/search`, {
            params: {
                product: 'MySQL',
                fromScore: '3.0',
                toScore: '10.0',
                size: '5',
                page: '0'
            },
            headers: {
                'Accept': 'application/json',
                'User-Agent': 'EBIOS-RM-CTI-Test/1.0'
            },
            timeout: 15000
        });

        console.log(`✅ Status: ${response.status}`);
        console.log(`📊 Results: ${response.data?.length || 'Unknown'}`);

        if (Array.isArray(response.data) && response.data.length > 0) {
            console.log(`🔍 First MySQL vulnerability:`, {
                id: response.data[0].cveId || response.data[0].id || 'No ID',
                description: (response.data[0].description || response.data[0].summary || 'No description').substring(0, 100) + '...',
                score: response.data[0].cvssScore || response.data[0].score || 'No score'
            });
        }

    } catch (error) {
        console.error(`❌ Error: ${error.message}`);
        if (error.response) {
            console.error(`   Status: ${error.response.status} ${error.response.statusText}`);
        }
    }

    console.log('\n');

    // Test 3: Search with text keywords
    console.log('🔍 Test 3: Search with text keywords "database"');
    console.log('='.repeat(50));
    try {
        const response = await axios.get(`${EUVD_BASE_URL}/search`, {
            params: {
                text: 'database',
                fromScore: '5.0',
                toScore: '10.0',
                size: '5',
                page: '0'
            },
            headers: {
                'Accept': 'application/json',
                'User-Agent': 'EBIOS-RM-CTI-Test/1.0'
            },
            timeout: 15000
        });

        console.log(`✅ Status: ${response.status}`);
        console.log(`📊 Results: ${response.data?.length || 'Unknown'}`);

        if (Array.isArray(response.data) && response.data.length > 0) {
            console.log(`🔍 First database vulnerability:`, {
                id: response.data[0].cveId || response.data[0].id || 'No ID',
                description: (response.data[0].description || response.data[0].summary || 'No description').substring(0, 100) + '...',
                score: response.data[0].cvssScore || response.data[0].score || 'No score'
            });
        }

    } catch (error) {
        console.error(`❌ Error: ${error.message}`);
        if (error.response) {
            console.error(`   Status: ${error.response.status} ${error.response.statusText}`);
        }
    }

    console.log('\n');

    // Test 4: Simple search with minimal parameters
    console.log('🔍 Test 4: Simple search with minimal parameters');
    console.log('='.repeat(50));
    try {
        const response = await axios.get(`${EUVD_BASE_URL}/search`, {
            params: {
                fromScore: '7.0',
                toScore: '10.0',
                size: '3'
            },
            headers: {
                'Accept': 'application/json',
                'User-Agent': 'EBIOS-RM-CTI-Test/1.0'
            },
            timeout: 15000
        });

        console.log(`✅ Status: ${response.status}`);
        console.log(`📊 Results: ${response.data?.length || 'Unknown'}`);

        if (Array.isArray(response.data) && response.data.length > 0) {
            console.log(`🔍 First high-severity vulnerability:`, {
                id: response.data[0].cveId || response.data[0].id || 'No ID',
                description: (response.data[0].description || response.data[0].summary || 'No description').substring(0, 100) + '...',
                score: response.data[0].cvssScore || response.data[0].score || 'No score'
            });
        }

    } catch (error) {
        console.error(`❌ Error: ${error.message}`);
        if (error.response) {
            console.error(`   Status: ${error.response.status} ${error.response.statusText}`);
        }
    }

    console.log('\n🎯 Test Summary:');
    console.log('- If any tests work, the search endpoint is functional');
    console.log('- If all tests fail with 403, there might be IP/region restrictions');
    console.log('- If tests work but return empty arrays, the search terms need adjustment');
}

testEUVDSearch().catch(console.error);
