// src/pages/provider/ProviderDashboard.jsx
import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { useAuth } from '../../context/AuthContext';
import { companyService, userService, logService } from '../../services/apiServices';
import LoadingSpinner from '../../components/common/LoadingSpinner';

const ProviderDashboard = () => {
  // Get current user from auth context
  const { user, companyId, companyName } = useAuth();
  
  // State for dashboard data
  const [companyInfo, setCompanyInfo] = useState({
    name: companyName || 'Entreprise',
    domain: '',
    userCount: 0,
    analysesCount: 0
  });
  
  const [recentActivities, setRecentActivities] = useState([]);
  
  // Loading and error states
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);

  // Fetch dashboard data on component mount
  useEffect(() => {
    const fetchDashboardData = async () => {
      if (!companyId) {
        setError('ID d\'entreprise non trouvé. Veuillez vous reconnecter.');
        setIsLoading(false);
        return;
      }
      
      setIsLoading(true);
      setError(null);
      
      try {
        // Fetch company details
        const companyResponse = await companyService.getCompanyById(companyId);
        
        // Fetch company users
        const usersResponse = await companyService.getCompanyUsers(companyId);
        
        // Get company logs
        const logsResponse = await companyService.getCompanyLogs(companyId, {
          limit: 5,
          sort: 'timestamp:desc'
        });
        
        // Update company info state
        setCompanyInfo({
          name: companyResponse.data?.company?.name || companyName || 'Entreprise',
          domain: companyResponse.data?.company?.domain || '',
          userCount: usersResponse.data?.users?.length || 0,
          analysesCount: companyResponse.data?.company?.analysesCount || 0
        });
        
        // Update activities state
        setRecentActivities(logsResponse.data?.logs || []);
      } catch (err) {
        console.error('Error fetching provider dashboard data:', err);
        setError('Failed to load dashboard data. Please try again later.');
        
        // Set fallback data
        setRecentActivities([
          { id: 1, userName: 'John Doe', actionType: 'USER_LOGIN', timestamp: '2025-03-19T08:30:00' },
          { id: 2, userName: 'Jane Smith', actionType: 'ANALYSIS_CREATE', timestamp: '2025-03-18T15:45:00' },
          { id: 3, userName: 'John Doe', actionType: 'ANALYSIS_UPDATE', timestamp: '2025-03-18T10:20:00' }
        ]);
      } finally {
        setIsLoading(false);
      }
    };

    fetchDashboardData();
  }, [companyId, companyName]);

  // Format timestamp for display
  const formatDate = (timestamp) => {
    return new Date(timestamp).toLocaleString();
  };

  // Determine action badge color
  const getActionBadgeClass = (actionType) => {
    if (actionType.includes('CREATE')) return 'bg-green-100 text-green-800';
    if (actionType.includes('UPDATE')) return 'bg-yellow-100 text-yellow-800';
    if (actionType.includes('DELETE')) return 'bg-red-100 text-red-800';
    if (actionType.includes('LOGIN')) return 'bg-blue-100 text-blue-800';
    return 'bg-gray-100 text-gray-800';
  };

  // If loading, show spinner
  if (isLoading) {
    return (
      <div className="p-6 flex justify-center items-center h-64">
        <LoadingSpinner size="large" color="blue" text="Chargement des données..." />
      </div>
    );
  }

  return (
    <div className="p-6">
      <h1 className="text-2xl font-bold mb-2">Dashboard Administrateur Entreprise</h1>
      <p className="text-gray-600 mb-6">
        {companyInfo.name} {companyInfo.domain ? `(${companyInfo.domain})` : ''}
      </p>

      {/* Display error if any */}
      {error && (
        <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-md text-red-700">
          {error}
        </div>
      )}

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
        <div className="bg-white p-6 rounded-lg shadow-md">
          <h2 className="text-lg font-semibold text-gray-700">Utilisateurs</h2>
          <p className="text-3xl font-bold text-blue-600">{companyInfo.userCount}</p>
          <Link to="/admin/users" className="text-blue-500 hover:underline mt-2 inline-block">
            Gérer les utilisateurs →
          </Link>
        </div>
        
        <div className="bg-white p-6 rounded-lg shadow-md">
          <h2 className="text-lg font-semibold text-gray-700">Analyses actives</h2>
          <p className="text-3xl font-bold text-green-600">{companyInfo.analysesCount}</p>
          <Link to="/admin/analyses" className="text-blue-500 hover:underline mt-2 inline-block">
            Voir les analyses →
          </Link>
        </div>
      </div>

      <div className="bg-white p-6 rounded-lg shadow-md">
        <h2 className="text-lg font-semibold mb-4">Activités récentes de l'entreprise</h2>
        {recentActivities.length === 0 ? (
          <div className="text-center py-6 text-gray-500">
            Aucune activité récente à afficher
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Utilisateur</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Action</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {recentActivities.map((activity) => (
                  <tr key={activity.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">{activity.userName}</td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${getActionBadgeClass(activity.actionType)}`}>
                        {activity.actionType}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">{formatDate(activity.timestamp)}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
        <Link to="/admin/activities" className="text-blue-500 hover:underline mt-4 inline-block">
          Voir tous les logs d'activité →
        </Link>
      </div>
      
      {/* User Quick Info */}
      <div className="mt-6 bg-white p-6 rounded-lg shadow-md">
        <h2 className="text-lg font-semibold mb-4">Informations utilisateur</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <p className="text-sm text-gray-600">Nom</p>
            <p className="font-medium">{user?.name || 'Non disponible'}</p>
          </div>
          <div>
            <p className="text-sm text-gray-600">Email</p>
            <p className="font-medium">{user?.email || 'Non disponible'}</p>
          </div>
          <div>
            <p className="text-sm text-gray-600">Rôle</p>
            <p className="font-medium capitalize">{user?.role || 'Non disponible'}</p>
          </div>
          <div>
            <p className="text-sm text-gray-600">Dernière connexion</p>
            <p className="font-medium">{user?.lastLogin ? formatDate(user.lastLogin) : 'Non disponible'}</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProviderDashboard;