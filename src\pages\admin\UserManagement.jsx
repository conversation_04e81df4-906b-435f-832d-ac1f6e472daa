// src/pages/admin/UserManagement.jsx
import React, { useState, useEffect } from 'react';
import { userService, companyService } from '../../services/apiServices';
import LoadingSpinner from '../../components/common/LoadingSpinner';

const UserManagement = () => {
  // State for users and companies
  const [users, setUsers] = useState([]);
  const [companies, setCompanies] = useState([]);
  
  // Loading and error states
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState(null);
  
  // Form data state
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    password: '', // Added password field for creating new users
    role: 'admin',
    companyId: '',
    status: 'active'
  });
  
  // Password change state
  const [passwordData, setPasswordData] = useState({
    currentPassword: '',
    newPassword: ''
  });
  const [showPasswordModal, setShowPasswordModal] = useState(false);
  
  // Editing state
  const [isEditing, setIsEditing] = useState(false);
  const [currentUserId, setCurrentUserId] = useState(null);

  // Pagination state
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
    total: 0,
    pages: 0
  });

  // Fetch users and companies on component mount
  useEffect(() => {
    fetchData();
  }, [pagination.page, pagination.limit]);

  // Function to fetch users and companies from API
  const fetchData = async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      // Fetch users with pagination
      const usersResponse = await userService.getUsers({
        page: pagination.page,
        limit: pagination.limit
      });
      
      const companiesResponse = await companyService.getCompanies();
      
      if (usersResponse.success) {
        setUsers(usersResponse.data || []);
        // Update pagination information from response
        if (usersResponse.pagination) {
          setPagination(usersResponse.pagination);
        }
      } else {
        throw new Error(usersResponse.message || 'Failed to fetch users');
      }
      
      if (companiesResponse.success) {
        setCompanies(companiesResponse.data || []);
      } else {
        throw new Error(companiesResponse.message || 'Failed to fetch companies');
      }
    } catch (error) {
      console.error('Error fetching data:', error);
      setError('Erreur lors du chargement des données. Veuillez réessayer plus tard.');
      
      // Use mock data in development for testing
      if (process.env.NODE_ENV === 'development') {
        setUsers([
          { _id: '1', name: 'John Doe', email: '<EMAIL>', role: 'admin', companyName: 'Entreprise A', companyId: '1', status: 'active', lastLogin: '2025-03-15T09:30:00' },
          { _id: '2', name: 'Jane Smith', email: '<EMAIL>', role: 'admin', companyName: 'Entreprise B', companyId: '2', status: 'active', lastLogin: '2025-03-17T14:45:00' },
          { _id: '3', name: 'Bob Johnson', email: '<EMAIL>', role: 'admin', companyName: 'Entreprise C', companyId: '3', status: 'inactive', lastLogin: null }
        ]);
        
        setCompanies([
          { _id: '1', name: 'Entreprise A' },
          { _id: '2', name: 'Entreprise B' },
          { _id: '3', name: 'Entreprise C' }
        ]);
        
        setPagination({
          page: 1,
          limit: 10,
          total: 3,
          pages: 1
        });
      }
    } finally {
      setIsLoading(false);
    }
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value
    });
  };

  const handlePasswordChange = (e) => {
    const { name, value } = e.target;
    setPasswordData({
      ...passwordData,
      [name]: value
    });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);
    setError(null);
    
    try {
      // Prepare data for API
      const userData = { ...formData };
      
      // Don't send empty password when updating
      if (isEditing && !userData.password) {
        delete userData.password;
      }
      
      if (isEditing) {
        // Update existing user
        const response = await userService.updateUser(currentUserId, userData);
        
        if (response.success) {
          // Update the user in the local state
          setUsers(
            users.map((user) =>
              user._id === currentUserId 
                ? { ...user, ...response.data } 
                : user
            )
          );
          
          resetForm();
        } else {
          throw new Error(response.message || 'Failed to update user');
        }
      } else {
        // Create new user
        const response = await userService.createUser(userData);
        
        if (response.success) {
          // Add the new user to the local state or refresh the list
          fetchData();
          resetForm();
        } else {
          throw new Error(response.message || 'Failed to create user');
        }
      }
    } catch (error) {
      console.error(isEditing ? 'Update user error:' : 'Create user error:', error);
      setError(`Erreur lors de ${isEditing ? 'la mise à jour' : 'la création'} de l'utilisateur. ${error.message || 'Veuillez réessayer.'}`);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handlePasswordSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);
    setError(null);
    
    try {
      const response = await userService.updatePassword(currentUserId, passwordData);
      
      if (response.success) {
        setShowPasswordModal(false);
        setPasswordData({
          currentPassword: '',
          newPassword: ''
        });
        alert('Mot de passe mis à jour avec succès');
      } else {
        throw new Error(response.message || 'Failed to update password');
      }
    } catch (error) {
      console.error('Update password error:', error);
      setError(`Erreur lors de la mise à jour du mot de passe. ${error.message || 'Veuillez réessayer.'}`);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleEdit = (user) => {
    if (!user || typeof user !== 'object') {
      console.error('Invalid user object:', user);
      return;
    }
    
    setFormData({
      name: user.name || '',
      email: user.email || '',
      password: '', // Clear password field when editing
      role: user.role || 'admin',
      companyId: user.companyId || '',
      status: user.status || 'active'
    });
    
    setCurrentUserId(user._id);
    setIsEditing(true);
  };

  const handlePasswordModalOpen = (user) => {
    setCurrentUserId(user._id);
    setShowPasswordModal(true);
  };

  const handleStatusToggle = async (user) => {
    if (!window.confirm(`Êtes-vous sûr de vouloir ${user.status === 'active' ? 'désactiver' : 'activer'} cet utilisateur?`)) {
      return;
    }
    
    setIsLoading(true);
    
    try {
      const updatedStatus = user.status === 'active' ? 'inactive' : 'active';
      
      const response = await userService.updateUser(user._id, { status: updatedStatus });
      
      if (response.success) {
        setUsers(
          users.map((u) =>
            u._id === user._id
              ? { ...u, status: updatedStatus }
              : u
          )
        );
      } else {
        throw new Error(response.message || 'Failed to update user status');
      }
    } catch (error) {
      console.error('Update user status error:', error);
      setError('Erreur lors de la mise à jour du statut. Veuillez réessayer.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleDeleteUser = async (user) => {
    if (!window.confirm(`Êtes-vous sûr de vouloir supprimer l'utilisateur ${user.name}? Cette action est irréversible.`)) {
      return;
    }
    
    setIsLoading(true);
    
    try {
      const response = await userService.deleteUser(user._id);
      
      if (response.success) {
        // Remove the user from the local state
        setUsers(users.filter(u => u._id !== user._id));
      } else {
        throw new Error(response.message || 'Failed to delete user');
      }
    } catch (error) {
      console.error('Delete user error:', error);
      setError('Erreur lors de la suppression de l\'utilisateur. Veuillez réessayer.');
    } finally {
      setIsLoading(false);
    }
  };

  const resetForm = () => {
    setFormData({
      name: '',
      email: '',
      password: '',
      role: 'admin',
      companyId: '',
      status: 'active'
    });
    setIsEditing(false);
    setCurrentUserId(null);
    setError(null);
  };

  // Helper to get readable role name
  const getRoleName = (role) => {
    switch (role) {
      case 'superadmin': return 'SuperAdmin';
      case 'admin': return 'Provider';
      case 'simpleuser': return 'Analyste';
      default: return role;
    }
  };

  // Pagination controls
  const handlePageChange = (newPage) => {
    if (newPage > 0 && newPage <= pagination.pages) {
      setPagination({ ...pagination, page: newPage });
    }
  };

  return (
    <div className="p-6">
      <h1 className="text-2xl font-bold mb-6">Gestion des Utilisateurs</h1>
      
      {/* Error message */}
      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg mb-6">
          <p>{error}</p>
        </div>
      )}
      
      <div className="bg-white p-6 rounded-lg shadow-md mb-8">
        <h2 className="text-lg font-semibold mb-4">
          {isEditing ? 'Modifier un utilisateur' : 'Ajouter un utilisateur'}
        </h2>
        <form onSubmit={handleSubmit}>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Nom complet
              </label>
              <input
                type="text"
                name="name"
                value={formData.name}
                onChange={handleInputChange}
                required
                className="w-full px-3 py-2 border border-gray-300 rounded-md"
                disabled={isSubmitting}
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Email
              </label>
              <input
                type="email"
                name="email"
                value={formData.email}
                onChange={handleInputChange}
                required
                className="w-full px-3 py-2 border border-gray-300 rounded-md"
                disabled={isSubmitting}
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                {isEditing ? 'Nouveau mot de passe (laisser vide pour ne pas changer)' : 'Mot de passe'}
              </label>
              <input
                type="password"
                name="password"
                value={formData.password}
                onChange={handleInputChange}
                required={!isEditing}
                className="w-full px-3 py-2 border border-gray-300 rounded-md"
                disabled={isSubmitting}
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Rôle
              </label>
              <select
                name="role"
                value={formData.role}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md"
                disabled={isSubmitting}
              >
                <option value="admin">Provider (Admin Entreprise)</option>
                <option value="simpleuser">Analyste</option>
              </select>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Entreprise
              </label>
              <select
                name="companyId"
                value={formData.companyId}
                onChange={handleInputChange}
                required
                className="w-full px-3 py-2 border border-gray-300 rounded-md"
                disabled={isSubmitting}
              >
                <option value="">-- Sélectionner une entreprise --</option>
                {companies.map((company) => (
                  <option key={company._id} value={company._id}>
                    {company.name}
                  </option>
                ))}
              </select>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Statut
              </label>
              <select
                name="status"
                value={formData.status}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md"
                disabled={isSubmitting}
              >
                <option value="active">Actif</option>
                <option value="inactive">Inactif</option>
              </select>
            </div>
          </div>
          
          <div className="mt-4 flex space-x-3">
            <button
              type="submit"
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:bg-blue-300"
              disabled={isSubmitting}
            >
              {isSubmitting ? 'Chargement...' : isEditing ? 'Mettre à jour' : 'Ajouter'}
            </button>
            
            {isEditing && (
              <button
                type="button"
                onClick={resetForm}
                className="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 disabled:bg-gray-200"
                disabled={isSubmitting}
              >
                Annuler
              </button>
            )}
          </div>
        </form>
      </div>
      
      {/* Password Change Modal */}
      {showPasswordModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white p-6 rounded-lg shadow-lg w-full max-w-md">
            <h2 className="text-lg font-semibold mb-4">Changer le mot de passe</h2>
            
            {error && (
              <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg mb-4">
                <p>{error}</p>
              </div>
            )}
            
            <form onSubmit={handlePasswordSubmit}>
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Mot de passe actuel
                </label>
                <input
                  type="password"
                  name="currentPassword"
                  value={passwordData.currentPassword}
                  onChange={handlePasswordChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md"
                  disabled={isSubmitting}
                />
                <p className="text-xs text-gray-500 mt-1">
                  Requis uniquement si vous modifiez votre propre mot de passe
                </p>
              </div>
              
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Nouveau mot de passe
                </label>
                <input
                  type="password"
                  name="newPassword"
                  value={passwordData.newPassword}
                  onChange={handlePasswordChange}
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-md"
                  disabled={isSubmitting}
                />
              </div>
              
              <div className="flex justify-end space-x-3">
                <button
                  type="button"
                  onClick={() => setShowPasswordModal(false)}
                  className="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 disabled:bg-gray-200"
                  disabled={isSubmitting}
                >
                  Annuler
                </button>
                <button
                  type="submit"
                  className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:bg-blue-300"
                  disabled={isSubmitting}
                >
                  {isSubmitting ? 'Chargement...' : 'Mettre à jour'}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
      
      {/* Users list */}
      {isLoading && !users.length ? (
        <div className="flex justify-center my-12">
          <LoadingSpinner />
        </div>
      ) : (
        <div className="bg-white rounded-lg shadow-md overflow-hidden">
          {users.length === 0 ? (
            <div className="p-6 text-center text-gray-500">
              Aucun utilisateur trouvé. Ajoutez votre premier utilisateur ci-dessus.
            </div>
          ) : (
            <>
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Nom
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Email
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Rôle
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Entreprise
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Statut
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Dernière connexion
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {users.map((user) => (
                    <tr key={user._id}>
                      <td className="px-6 py-4 whitespace-nowrap">{user.name || '-'}</td>
                      <td className="px-6 py-4 whitespace-nowrap">{user.email || '-'}</td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span
                          className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                            user.role === 'superadmin'
                              ? 'bg-purple-100 text-purple-800'
                              : user.role === 'admin'
                              ? 'bg-blue-100 text-blue-800'
                              : 'bg-green-100 text-green-800'
                          }`}
                        >
                          {getRoleName(user.role)}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">{user.companyName || '-'}</td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span
                          className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                            user.status === 'active'
                              ? 'bg-green-100 text-green-800'
                              : 'bg-red-100 text-red-800'
                          }`}
                        >
                          {user.status === 'active' ? 'Actif' : 'Inactif'}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        {user.lastLogin
                          ? new Date(user.lastLogin).toLocaleString()
                          : 'Jamais'}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex space-x-3">
                          <button
                            onClick={() => handleEdit(user)}
                            className="text-indigo-600 hover:text-indigo-900"
                            disabled={isLoading || user.role === 'superadmin'}
                          >
                            Modifier
                          </button>
                          
                          <button
                            onClick={() => handlePasswordModalOpen(user)}
                            className="text-blue-600 hover:text-blue-900"
                            disabled={isLoading || user.role === 'superadmin'}
                          >
                            Mot de passe
                          </button>
                          
                          <button
                            onClick={() => handleStatusToggle(user)}
                            className={
                              user.status === 'active' 
                                ? 'text-red-600 hover:text-red-900' 
                                : 'text-green-600 hover:text-green-900'
                            }
                            disabled={isLoading || user.role === 'superadmin'}
                          >
                            {user.status === 'active' ? 'Désactiver' : 'Activer'}
                          </button>
                          
                          <button
                            onClick={() => handleDeleteUser(user)}
                            className="text-red-600 hover:text-red-900"
                            disabled={isLoading || user.role === 'superadmin'}
                          >
                            Supprimer
                          </button>
                          
                          <a
                            href={`/admin/users/${user._id}/logs`}
                            className="text-blue-600 hover:text-blue-900"
                          >
                            Activités
                          </a>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
              
              {/* Pagination */}
              {pagination.pages > 1 && (
                <div className="px-6 py-4 flex items-center justify-between border-t border-gray-200">
                  <div className="flex-1 flex justify-between items-center">
                    <p className="text-sm text-gray-700">
                      Affichage de{' '}
                      <span className="font-medium">{(pagination.page - 1) * pagination.limit + 1}</span>
                      {' '}à{' '}
                      <span className="font-medium">
                        {Math.min(pagination.page * pagination.limit, pagination.total)}
                      </span>
                      {' '}sur{' '}
                      <span className="font-medium">{pagination.total}</span>
                      {' '}résultats
                    </p>
                    <div>
                      <button
                        onClick={() => handlePageChange(pagination.page - 1)}
                        disabled={pagination.page === 1}
                        className="px-4 py-2 border border-gray-300 rounded-md mr-2 disabled:opacity-50"
                      >
                        Précédent
                      </button>
                      <button
                        onClick={() => handlePageChange(pagination.page + 1)}
                        disabled={pagination.page === pagination.pages}
                        className="px-4 py-2 border border-gray-300 rounded-md disabled:opacity-50"
                      >
                        Suivant
                      </button>
                    </div>
                  </div>
                </div>
              )}
            </>
          )}
        </div>
      )}
    </div>
  );
};

export default UserManagement;