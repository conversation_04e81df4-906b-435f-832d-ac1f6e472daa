// WorkshopNavigation.jsx
import React from 'react';
import {
    Home, Settings, ChevronDown, ChevronRight, LayoutList, Briefcase, AlertTriangle,
    ShieldCheck, FileText, Target, Search
} from 'lucide-react';
import Atelier1Navigation from './navigation/Atelier1Navigation';
import Atelier2Navigation from './navigation/Atelier2Navigation';
import Atelier3Navigation from './navigation/Atelier3Navigation';
import Atelier4Navigation from './navigation/Atelier4Navigation';
import { getUnsavedChanges, setNavigationWarningDialogOpen } from '../utils/navigationUtils';

const WorkshopNavigation = ({
  activeTab,
  setActiveTab,
  expandedWorkshops,
  toggleWorkshop,
  isSidebarOpen
}) => {
  // Define modern navigation styles
  const navItemClass = "w-full flex items-center px-4 py-3 rounded-xl transition-all duration-200 text-sm group";
  const activeStyle = "bg-gradient-to-r from-blue-500 to-indigo-600 text-white font-medium shadow-lg transform scale-[1.02]";
  const inactiveStyle = "text-gray-300 hover:bg-white/10 hover:text-white hover:transform hover:scale-[1.01]";
  const workshopTitleClass = "w-full flex items-center px-4 py-3 rounded-xl transition-all duration-200 text-sm font-medium group";

  return (
    <aside
      className={`fixed inset-y-0 left-0 z-20 w-64 modern-backdrop-blur border-r border-white/10 p-4 overflow-y-auto transition-transform duration-300 ease-in-out ${isSidebarOpen ? 'translate-x-0' : '-translate-x-full'}`}
      style={{
        minWidth: '16rem',
        maxWidth: '16rem',
        flexShrink: 0,
        background: 'linear-gradient(135deg, rgba(30, 41, 59, 0.95) 0%, rgba(51, 65, 85, 0.95) 100%)'
      }}
    >
      {/* Modern Sidebar Header */}
      <div className="mb-8">
        {/* Logo Section */}
        <div className="flex items-center modern-glass-effect p-4 rounded-2xl mb-6 shadow-xl border border-white/10">
          {/* ACG Logo */}
          <div className="overflow-hidden rounded-xl shadow-lg bg-white p-2 transition-transform duration-300 hover:scale-105 mr-3">
            <img
              src="/acg-logo.png"
              alt="ACG Logo"
              className="h-8 w-auto object-contain"
            />
          </div>
          <div className="flex-1">
            <div className="flex items-center">
              <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-lg flex items-center justify-center mr-2 shadow-md">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-white" viewBox="0 0 20 20" fill="currentColor">
                  <path d="M5 3a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2V5a2 2 0 00-2-2H5zM5 11a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2v-2a2 2 0 00-2-2H5zM11 5a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V5zM11 13a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z" />
                </svg>
              </div>
              <div>
                <h3 className="text-sm font-bold text-white">Navigation</h3>
                <p className="text-xs text-gray-300">EBIOS RM</p>
              </div>
            </div>
          </div>
        </div>
        {/* Modern Separator */}
        <div className="relative h-px bg-gradient-to-r from-transparent via-blue-400/50 to-transparent mb-6">
          <div className="absolute inset-0 bg-blue-400/20 blur-sm"></div>
        </div>
      </div>
      <nav className="w-full">
        <ul className="space-y-1 w-full">
          {/* Modern Dashboard */}
          <li className="w-full mb-4">
            <button
              className={`${navItemClass} ${activeTab === 'dashboard' ? activeStyle : inactiveStyle}`}
              onClick={() => {
                // Check for unsaved changes before navigation
                if (getUnsavedChanges()) {
                  // Store the target tab in a data attribute
                  setNavigationWarningDialogOpen(true);
                  // The actual navigation will be handled by the dialog
                  return;
                }
                // If no unsaved changes, navigate directly
                setActiveTab('dashboard');
              }}
            >
              <div className="flex items-center w-full">
                <div className={`p-2 rounded-lg transition-all duration-200 mr-3 ${
                  activeTab === 'dashboard'
                    ? 'bg-white/20 shadow-lg'
                    : 'bg-white/10 group-hover:bg-white/20'
                }`}>
                  <Home size={18} className={`flex-shrink-0 transition-colors ${
                    activeTab === 'dashboard' ? 'text-white' : 'text-gray-300 group-hover:text-white'
                  }`} />
                </div>
                <div className="flex-1">
                  <span className="font-medium">Tableau de bord</span>
                  <div className="text-xs text-gray-400 group-hover:text-gray-300">Vue d'ensemble</div>
                </div>
              </div>
            </button>
          </li>

          {/* Modern Separator */}
          <li className="my-6 relative">
            <div className="absolute inset-0 flex items-center">
              <div className="w-full border-t border-gradient-to-r from-transparent via-white/20 to-transparent"></div>
            </div>
            <div className="relative flex justify-center">
              <span className="modern-glass-effect px-4 py-1 text-xs text-gray-300 uppercase font-semibold rounded-full border border-white/10">
                Ateliers EBIOS RM
              </span>
            </div>
          </li>

          {/* Atelier 1 */}
          <Atelier1Navigation
            activeTab={activeTab}
            setActiveTab={setActiveTab}
            expanded={expandedWorkshops['workshop1']}
            toggleExpand={() => toggleWorkshop('workshop1')}
            baseNavItemClass={navItemClass}
            workshopTitleClass={workshopTitleClass}
            activeStyle={activeStyle}
            inactiveStyle={inactiveStyle}
          />

          {/* Atelier 2 */}
          <Atelier2Navigation
            activeTab={activeTab}
            setActiveTab={setActiveTab}
            expanded={expandedWorkshops['workshop2']}
            toggleExpand={() => toggleWorkshop('workshop2')}
            baseNavItemClass={navItemClass}
            workshopTitleClass={workshopTitleClass}
            activeStyle={activeStyle}
            inactiveStyle={inactiveStyle}
          />

          {/* Atelier 3 */}
          <Atelier3Navigation
            activeTab={activeTab}
            setActiveTab={setActiveTab}
            expanded={expandedWorkshops['workshop3']}
            toggleExpand={() => toggleWorkshop('workshop3')}
            baseNavItemClass={navItemClass}
            workshopTitleClass={workshopTitleClass}
            activeStyle={activeStyle}
            inactiveStyle={inactiveStyle}
          />

          {/* Atelier 4 */}
          <Atelier4Navigation
            activeTab={activeTab}
            setActiveTab={setActiveTab}
            expanded={expandedWorkshops['workshop4']}
            toggleExpand={() => toggleWorkshop('workshop4')}
            baseNavItemClass={navItemClass}
            workshopTitleClass={workshopTitleClass}
            activeStyle={activeStyle}
            inactiveStyle={inactiveStyle}
          />

          {/* Modern Coming Soon Section */}
          <li className="w-full mt-8">
            <div className="relative mb-4">
              <div className="absolute inset-0 flex items-center">
                <div className="w-full border-t border-gradient-to-r from-transparent via-white/10 to-transparent"></div>
              </div>
              <div className="relative flex justify-center">
                <span className="modern-glass-effect px-4 py-1 text-xs text-gray-400 uppercase font-semibold rounded-full border border-white/10">
                  Prochainement
                </span>
              </div>
            </div>
            <div className="modern-glass-effect p-4 rounded-xl border border-white/10 opacity-60">
              <div className="flex items-center">
                <div className="p-2 bg-white/10 rounded-lg mr-3">
                  <Settings size={16} className="flex-shrink-0 text-gray-400" />
                </div>
                <div className="flex-1">
                  <span className="text-gray-400 font-medium">Atelier 5</span>
                  <div className="text-xs text-gray-500">Fonctionnalités avancées</div>
                </div>
                <div className="text-xs bg-orange-500/20 text-orange-300 px-2 py-1 rounded-full">
                  Bientôt
                </div>
              </div>
            </div>
          </li>
        </ul>
      </nav>
    </aside>
  );
};

export default WorkshopNavigation;