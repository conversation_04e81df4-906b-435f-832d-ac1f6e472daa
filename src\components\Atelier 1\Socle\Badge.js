import React from 'react';
import { SIMPLIFIED_STATUS_OPTIONS } from '../../../constants';

// Badge component for displaying status
const Badge = ({ status }) => {
  const styles = {
    'applied': 'bg-emerald-50 text-emerald-700 border-emerald-200',
    'partially-applied': 'bg-amber-50 text-amber-700 border-amber-200',
    'not-applied': 'bg-slate-50 text-slate-700 border-slate-200'
  };

  const label = SIMPLIFIED_STATUS_OPTIONS.find(opt => opt.value === status)?.label || 'Non défini';
  
  return (
    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border ${styles[status] || 'bg-gray-100 text-gray-800 border-gray-200'}`}>
      {status === 'applied' && (
        <svg className="w-3 h-3 mr-1 text-emerald-500" fill="currentColor" viewBox="0 0 8 8">
          <circle cx="4" cy="4" r="3" />
        </svg>
      )}
      {status === 'partially-applied' && (
        <svg className="w-3 h-3 mr-1 text-amber-500" fill="currentColor" viewBox="0 0 8 8">
          <circle cx="4" cy="4" r="3" />
        </svg>
      )}
      {status === 'not-applied' && (
        <svg className="w-3 h-3 mr-1 text-slate-400" fill="currentColor" viewBox="0 0 8 8">
          <circle cx="4" cy="4" r="3" />
        </svg>
      )}
      {label}
    </span>
  );
};

export default Badge;