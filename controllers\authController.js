// backend/controllers/authController.js
const jwt = require('jsonwebtoken');
const User = require('../models/User');
const ActivityLog = require('../models/ActivityLog');

// Get IP address from request
const getIpAddress = (req) => {
  return req.ip ||
         req.headers['x-forwarded-for'] ||
         req.connection.remoteAddress ||
         'unknown';
};

/**
 * @desc    Login user
 * @route   POST /api/auth/login
 * @access  Public
 */
exports.login = async (req, res) => {
  try {
    const { email, password } = req.body;

    // Validation
    if (!email || !password) {
      return res.status(400).json({
        success: false,
        message: 'Email et mot de passe requis'
      });
    }

    // Find user by email and include password for verification
    const user = await User.findOne({ email }).select('+password');

    if (!user) {
      return res.status(401).json({
        success: false,
        message: 'Identifiants invalides'
      });
    }

    // Check if user is active
    if (user.status === 'inactive') {
      return res.status(403).json({
        success: false,
        message: 'Compte désactivé. Contactez votre administrateur'
      });
    }

    // Check if password matches
    const isMatch = await user.comparePassword(password);

    if (!isMatch) {
      return res.status(401).json({
        success: false,
        message: 'Identifiants invalides'
      });
    }

    // Update last login
    user.lastLogin = Date.now();
    await user.save();

    // Generate tokens
    const jwtData = user.getJwtData();

    const accessToken = jwt.sign(
      { userId: user._id, role: user.role, tokenType: 'access', ...jwtData },
      process.env.JWT_SECRET || 'your_jwt_secret_key',
      { expiresIn: process.env.JWT_EXPIRY || '8h' }
    );

    const refreshToken = jwt.sign(
      { userId: user._id, tokenType: 'refresh' },
      process.env.JWT_REFRESH_SECRET || 'your_jwt_refresh_secret_key',
      { expiresIn: process.env.JWT_REFRESH_EXPIRY || '7d' }
    );

    // Log user login activity
    await ActivityLog.create({
      userId: user._id,
      userName: user.name,
      companyId: user.companyId,
      companyName: user.companyName,
      actionType: 'USER_LOGIN',
      resourceType: 'user',
      resourceId: user._id,
      ipAddress: getIpAddress(req),
      userAgent: req.headers['user-agent'] || '',
      details: {
        browser: req.headers['user-agent'] || '',
        timestamp: new Date()
      }
    });

    // Send response with user data and tokens
    res.status(200).json({
      success: true,
      data: {
        user: {
          id: user._id,
          name: user.name,
          email: user.email,
          role: user.role,
          companyId: user.companyId,
          companyName: user.companyName
        },
        tokens: {
          accessToken,
          refreshToken
        }
      }
    });
  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la connexion',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * @desc    Refresh access token
 * @route   POST /api/auth/refresh
 * @access  Public (with refresh token)
 */
exports.refreshToken = async (req, res) => {
  try {
    const { refreshToken } = req.body;

    if (!refreshToken) {
      return res.status(400).json({
        success: false,
        message: 'Refresh token requis'
      });
    }

    // Verify refresh token
    const decoded = jwt.verify(
      refreshToken,
      process.env.JWT_REFRESH_SECRET || 'your_jwt_refresh_secret_key'
    );

    // Ensure it's a refresh token
    if (decoded.tokenType !== 'refresh') {
      return res.status(400).json({
        success: false,
        message: 'Token invalid'
      });
    }

    // Get user
    const user = await User.findById(decoded.userId);

    if (!user || user.status !== 'active') {
      return res.status(401).json({
        success: false,
        message: 'User not found or inactive'
      });
    }

    // Generate new access token
    const jwtData = user.getJwtData();

    const accessToken = jwt.sign(
      { userId: user._id, role: user.role, tokenType: 'access', ...jwtData },
      process.env.JWT_SECRET || 'your_jwt_secret_key',
      { expiresIn: process.env.JWT_EXPIRY || '8h' }
    );

    // Send new access token
    res.status(200).json({
      success: true,
      data: {
        accessToken
      }
    });
  } catch (error) {
    console.error('Refresh token error:', error);

    // Specific error for token expiration
    if (error.name === 'TokenExpiredError') {
      return res.status(401).json({
        success: false,
        message: 'Refresh token expired. Please log in again'
      });
    }

    res.status(500).json({
      success: false,
      message: 'Error refreshing token',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * @desc    Logout user
 * @route   POST /api/auth/logout
 * @access  Private
 */
exports.logout = async (req, res) => {
  try {
    // Log the logout activity
    if (req.user) {
      await ActivityLog.create({
        userId: req.user.id,
        userName: req.user.name,
        companyId: req.user.companyId,
        companyName: req.user.companyName,
        actionType: 'USER_LOGOUT',
        resourceType: 'user',
        resourceId: req.user.id,
        ipAddress: getIpAddress(req),
        userAgent: req.headers['user-agent'] || '',
        details: {
          browser: req.headers['user-agent'] || '',
          timestamp: new Date()
        }
      });
    }

    // In a real application, you would add the refresh token to a blacklist
    // or remove it from a whitelist in the database

    res.status(200).json({
      success: true,
      message: 'Déconnexion réussie'
    });
  } catch (error) {
    console.error('Logout error:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la déconnexion'
    });
  }
};

/**
 * @desc    Get current user information
 * @route   GET /api/auth/me
 * @access  Private
 */
exports.getMe = async (req, res) => {
  try {
    const user = await User.findById(req.user.id);

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    res.status(200).json({
      success: true,
      data: {
        user: {
          id: user._id,
          name: user.name,
          email: user.email,
          role: user.role,
          companyId: user.companyId,
          companyName: user.companyName
        }
      }
    });
  } catch (error) {
    console.error('Get me error:', error);
    res.status(500).json({
      success: false,
      message: 'Erreur lors de la récupération des informations utilisateur'
    });
  }
};