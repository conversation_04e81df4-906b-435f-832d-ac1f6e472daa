// --- START OF FILE FrameworkComponents.js ---

import React, { useState, useEffect, useMemo } from 'react'; // Added useEffect for modal sync and useMemo for grouping
import { useTranslation } from 'react-i18next';
import Badge from './Badge';
import { Card, Input, Select, TextArea } from './UIComponents';

// --- MOCK DATA (For Modal Example - Normally comes via Props) ---
const MOCK_ALL_DREADED_EVENTS = [
    { id: 'de-1', name: "Ransomware Attack", shortId: "DE001", description: "System files encrypted, ransom demanded." },
    { id: 'de-2', name: "Major Data Breach", shortId: "DE002", description: "Sensitive customer data stolen." },
    { id: 'de-3', name: "Denial of Service (DoS)", shortId: "DE003", description: "Website or critical service becomes unavailable." },
];
// --- (Keep MOCK_ALL_BUSINESS_VALUES if needed for BV modal) ---


// --- Helper ---
/* REMOVED ensureShortId
const ensureShortId = (item, prefix = '') => {
    if (item.shortId) return item.shortId;
    const idStr = String(item.id);
    const randomPart = Math.random().toString(36).substring(2, 5);
    return `${prefix}${idStr.substring(idStr.length - 4, idStr.length)}-${randomPart}`.toUpperCase();
};
*/

// --- FrameworkListItem (No changes needed) ---
export const FrameworkListItem = ({ framework, isSelected, onSelect, selectedRuleCount }) => {
    const { t } = useTranslation();
    // ... (same as before) ...
     const ruleCount = selectedRuleCount || 0;
    const hasRules = ruleCount > 0;

    return (
        <Card
            isSelected={isSelected}
            onClick={onSelect}
            className="p-4 cursor-pointer hover:shadow-md"
        >
            <div className="flex justify-between items-start">
                <div className="flex-1 pr-2">
                    <h4 className="font-medium text-gray-900 truncate">{framework.name}</h4>
                </div>
            </div>
            {hasRules && (
                <div className="mt-2 pt-2 border-t border-gray-100">
                    <div className="text-xs text-blue-600 font-semibold">
                        {t('securityFoundation.status.rulesSelected', { count: ruleCount })}
                    </div>
                </div>
            )}
        </Card>
    );
};

// --- Available Rule Item (UPDATED for Modal Trigger) ---
export const RuleItem = ({ rule, isSelected, onToggle, disabled, onShowDescription }) => ( // Added onShowDescription prop
    <div
        className={`flex items-center justify-between px-4 py-3 transition-colors ${disabled ? 'opacity-50 cursor-not-allowed' : isSelected ? 'bg-blue-50' : 'hover:bg-gray-50'} ${isSelected ? '' : 'cursor-pointer'}`}
        onClick={!disabled ? onToggle : undefined}
    >
        <div className="flex items-center">
                    <input
                        type="checkbox"
                        checked={isSelected}
                onChange={onToggle} // Let parent handle state via onClick on div
                onClick={(e) => e.stopPropagation()} // Prevent click bubbling to div if clicking checkbox itself
                        disabled={disabled}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded mr-3 disabled:cursor-not-allowed"
            />
            <label className={`text-sm ${isSelected ? 'font-semibold text-blue-800' : 'text-gray-700'} ${disabled ? 'cursor-not-allowed' : 'cursor-pointer'}`}>
                        {rule.name}
                    </label>
             {/* UPDATED: Info Icon triggers modal */}
             {rule.description && (
                 <span
                     // title={rule.description} // Removed title attribute
                     className="ml-2 text-gray-400 hover:text-blue-600 cursor-pointer"
                     onClick={(e) => {
                         e.stopPropagation(); // Still prevent toggling rule
                         if (onShowDescription) onShowDescription(rule); // Call handler passed from parent
                     }}
                 >
                     ⓘ
                 </span>
             )}
        </div>
        {/* Optionally add a visual cue like a checkmark if selected */}
        {isSelected && <span className="text-blue-500 font-bold">✓</span>}
        </div>
    );


// --- Selected Rule Card (UPDATED for Justification Position) ---
export const SelectedRuleCard = ({
    rule,
    onRemove,
    onStatusChange,
    onJustificationChange,
    // BV Linking Props - Keep callback, but remove UI trigger props
    allBusinessValues = [], // Keep for potential future use or context, but not for direct linking UI
    // linkedBusinessValueIds = [], // No longer needed directly from props for manual linking display
    onUpdateRuleBusinessValues,
    // DE Linking Props (NEW)
    allDreadedEvents = [], // Expects format: [{ id, name, linkedBusinessValueIds: [...] }, ...]
    linkedDreadedEventIds = [],
    onUpdateRuleDreadedEvents, // Callback: (ruleId, arrayOfDeIds) => void
    // disabled
}) => {
    const { t } = useTranslation();
    // const [isBvModalOpen, setIsBvModalOpen] = useState(false); // Remove BV modal state
    const [isDeModalOpen, setIsDeModalOpen] = useState(false); // State for DE modal

    // BV Modal Handlers (Remove)
    // const handleOpenBvModal = () => setIsBvModalOpen(true);
    // const handleCloseBvModal = () => setIsBvModalOpen(false);
    // const handleSaveBvModal = (selectedIds) => {
    //     if (onUpdateRuleBusinessValues) {
    //          onUpdateRuleBusinessValues(rule.id, selectedIds);
    //     }
    //      handleCloseBvModal();
    // };

     // DE Modal Handlers (UPDATED to include BV linking)
    const handleOpenDeModal = () => setIsDeModalOpen(true);
    const handleCloseDeModal = () => setIsDeModalOpen(false);
    const handleSaveDeModal = (selectedDeIds) => {
        // 1. Update Dreaded Events
        if (onUpdateRuleDreadedEvents) {
             onUpdateRuleDreadedEvents(rule.id, selectedDeIds);
        }

        // 2. Automatically derive and update Business Values (using event.businessValue)
        if (onUpdateRuleBusinessValues && allDreadedEvents?.length > 0) {
            const derivedBvIds = selectedDeIds.reduce((acc, deId) => {
                const event = allDreadedEvents.find(e => e.id === deId);
                if (event && event.businessValue) {
                    acc.add(event.businessValue);
                }
                return acc;
            }, new Set());

            const uniqueBvIds = Array.from(derivedBvIds);
            console.log(`Derived BV IDs for rule ${rule.id}:`, uniqueBvIds);
            onUpdateRuleBusinessValues(rule.id, uniqueBvIds);
        } else if (!allDreadedEvents?.length > 0) {
             console.warn("Cannot derive Business Values: allDreadedEvents prop is missing or empty.");
             if (selectedDeIds.length === 0 && onUpdateRuleBusinessValues) {
                console.log(`Clearing BV IDs for rule ${rule.id} as no DEs selected.`);
                onUpdateRuleBusinessValues(rule.id, []);
             }
        }


         handleCloseDeModal();
    };

    const controlsDisabled = false; // Simplified

    const statusOptions = [
        { value: 'applied', label: t('securityFoundation.status.applied') },
        { value: 'partially-applied', label: t('securityFoundation.status.partially') },
        { value: 'not-applied', label: t('securityFoundation.status.notApplied') }
    ];

    return (
        <>
            <div className="bg-white border border-gray-200 rounded-lg shadow-sm hover:shadow transition-shadow overflow-hidden group">
                 {/* Card Header (same) */}
                <div className="p-3 flex justify-between items-start border-b border-gray-100 bg-gray-50">
                    <div className="flex-1 pr-2">
                         <h5 className="font-medium text-gray-800 text-sm truncate" title={rule.name}>{rule.name}</h5>
                    </div>
                    {!controlsDisabled && (
                         <button type="button" className="text-gray-400 hover:text-red-600 p-1 rounded-full hover:bg-red-50 -mr-1 -mt-1" onClick={onRemove} title={t('securityFoundation.buttons.remove')}>
                             <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2}><path strokeLinecap="round" strokeLinejoin="round" d="M6 18L18 6M6 6l12 12" /></svg>
                         </button>
                    )}
                </div>

                 {/* Card Body (UPDATED) */}
                <div className="p-3 space-y-4">
                    {/* Status Radio Group */}
                    <div >
                        <label className="block text-xs font-medium text-gray-600 mb-1">{t('securityFoundation.sections.status')}</label>
                        <div className="flex items-center space-x-3 pt-1">
                            {statusOptions.map(option => (
                                <div key={option.value} className="flex items-center">
                                    <input
                                        id={`status-${rule.id}-${option.value}`}
                                        name={`status-${rule.id}`}
                                        type="radio"
                                        value={option.value}
                                        checked={rule.status === option.value}
                                        onChange={onStatusChange}
                                        disabled={controlsDisabled}
                                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                                    />
                                    <label htmlFor={`status-${rule.id}-${option.value}`} className="ml-2 block text-sm text-gray-700">
                                        {option.label}
                                    </label>
                                </div>
                            ))}
                        </div>
                    </div>

                    {/* Justification Input (Now below Status) */}
                    <div >
                        <label htmlFor={`justification-${rule.id}`} className="block text-xs font-medium text-gray-600 mb-1">{t('securityFoundation.sections.justification')}</label>
                        <Input id={`justification-${rule.id}`} type="text" value={rule.justification || ''} onChange={onJustificationChange} placeholder={t('securityFoundation.placeholders.optional')} disabled={controlsDisabled}
                            className={`block w-full text-sm ${controlsDisabled ? 'bg-gray-100 cursor-not-allowed' : ''}`}
                        />
                    </div>

                    {/* Dreaded Event Linking */}
                    <div>
                         <label className="block text-xs font-medium text-gray-600 mb-1">{t('securityFoundation.sections.linkedDreadedEvents')}</label>
                         <button onClick={handleOpenDeModal} disabled={controlsDisabled}
                            className={`w-full text-left px-3 py-2 border rounded-md text-sm transition-colors ${ controlsDisabled ? 'bg-gray-100 text-gray-500 cursor-not-allowed' : 'bg-white text-purple-600 border-gray-300 hover:border-purple-400 hover:bg-purple-50' }`} >
                            {linkedDreadedEventIds.length > 0 ? t('securityFoundation.status.linkedCount', { count: linkedDreadedEventIds.length }) : t('securityFoundation.buttons.link')}
                        </button>
                    </div>
                </div>
            </div>

            {/* Render BV Modal (REMOVED) */}
            {/*
            <BusinessValueSelectorModal
                isOpen={isBvModalOpen}
                onClose={handleCloseBvModal}
                onSave={handleSaveBvModal}
                allBusinessValues={allBusinessValues}
                initialSelectedValueIds={linkedBusinessValueIds}
                currentRuleName={rule.name}
            />
            */}

             {/* Render DE Modal (NEW) */}
             <DreadedEventSelectorModal
                 isOpen={isDeModalOpen}
                 onClose={handleCloseDeModal}
                 onSave={handleSaveDeModal} // Use DE save handler
                 allDreadedEvents={allDreadedEvents} // Pass DE list
                 initialSelectedValueIds={linkedDreadedEventIds} // Pass DE linked IDs
                 currentRuleName={rule.name}
             />
        </>
    );
};


// --- BusinessValueSelectorModal (No changes needed) ---
const BusinessValueSelectorModal = ({ isOpen, onClose, onSave, allBusinessValues = [], initialSelectedValueIds = [], currentRuleName }) => {
     // ... (same as before, uses state internally) ...
    const [tempSelectedValues, setTempSelectedValues] = useState(initialSelectedValueIds);

    useEffect(() => { if (isOpen) { setTempSelectedValues(initialSelectedValueIds); } }, [initialSelectedValueIds, isOpen]);

    const toggleValue = (valueId) => { setTempSelectedValues(prev => prev.includes(valueId) ? prev.filter(id => id !== valueId) : [...prev, valueId]); };
    const handleSaveClick = () => { onSave(tempSelectedValues); };

     if (!isOpen) return null;
     return ( <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50"> <div className="bg-white rounded-lg shadow-xl max-w-md w-full"> {/* Header */} <div className="p-5 border-b border-gray-200"><h3 className="text-lg font-medium text-gray-900">Lier Valeurs Métier</h3><p className="text-sm text-gray-500 mt-1">Règle: <span className="font-semibold">{currentRuleName}</span></p></div> {/* Body */} <div className="p-6 max-h-96 overflow-y-auto">{ allBusinessValues.length > 0 ? (<div className="space-y-3">{ allBusinessValues.map(value => (<div key={value.id} className="flex items-start"><div className="flex items-center h-5"><input id={`modal-bv-${value.id}`} type="checkbox" className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded" checked={tempSelectedValues.includes(value.id)} onChange={() => toggleValue(value.id)} /></div><div className="ml-3 text-sm"><label htmlFor={`modal-bv-${value.id}`} className="font-medium text-gray-700">{/* <span className="text-blue-600 mr-1">{ensureShortId(value, 'BV')}</span> */} {value.name}</label>{(value.supportAssets || []).length > 0 && (<div className="mt-1 text-xs text-gray-500">Biens: {(value.supportAssets).map(a => a.name || `Asset ${a.id}`).join(', ')}</div>)}</div></div>))}</div>) : (<p className="text-gray-500 text-center py-4">Aucune valeur métier.</p>)}</div> {/* Footer */} <div className="px-6 py-4 bg-gray-50 border-t border-gray-200 flex justify-end space-x-3"><button className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50" onClick={onClose}>Annuler</button><button className="px-4 py-2 rounded-md text-sm font-medium text-white bg-blue-600 hover:bg-blue-700" onClick={handleSaveClick}>Confirmer</button></div> </div> </div> );
};


// --- DreadedEventSelectorModal (NEW COMPONENT) ---
// Very similar structure to BV Modal
const DreadedEventSelectorModal = ({
    isOpen,
    onClose,
    onSave,
    allDreadedEvents = [], // Use DE list
    initialSelectedValueIds = [], // Changed from initialSelectedValueIds to initialSelectedEventIds for clarity
    currentRuleName
}) => {
    const { t } = useTranslation();
    const [tempSelectedEvents, setTempSelectedEvents] = useState(initialSelectedValueIds);

    // Re-sync state if modal reopens with different initial values
    useEffect(() => {
        if (isOpen) {
             setTempSelectedEvents(initialSelectedValueIds); // Use initialSelectedEventIds
        }
    }, [initialSelectedValueIds, isOpen]); // Use initialSelectedEventIds

    const toggleEvent = (eventId) => { // Renamed from toggleValue
        setTempSelectedEvents(prev =>
            prev.includes(eventId)
                ? prev.filter(id => id !== eventId)
                : [...prev, eventId]
        );
    };

    const handleSaveClick = () => {
        onSave(tempSelectedEvents); // Pass selected event IDs
    };

    if (!isOpen) return null;

    return (
         <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
            <div className="bg-white rounded-lg shadow-xl max-w-md w-full">
                {/* Modal Header */}
                <div className="p-5 border-b border-gray-200">
                    {/* Changed Title */}
                    <h3 className="text-lg font-medium text-gray-900">{t('securityFoundation.modal.linkDreadedEvents')}</h3>
                    <p className="text-sm text-gray-500 mt-1">{t('securityFoundation.modal.rule')} <span className="font-semibold">{currentRuleName}</span></p>
                </div>
                {/* Modal Body */}
                <div className="p-6 max-h-96 overflow-y-auto">
                    {allDreadedEvents.length > 0 ? ( // Use DE list
                        <div className="space-y-3">
                             {allDreadedEvents.map(value => ( // Iterate DE list
                                <div key={value.id} className="flex items-start">
                                    <div className="flex items-center h-5">
                                         <input
                                            id={`modal-de-${value.id}`} // Unique ID prefix
                                            type="checkbox"
                                            className="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded" // Changed color
                                            checked={tempSelectedEvents.includes(value.id)}
                                            onChange={() => toggleEvent(value.id)}
                                        />
                                    </div>
                                     <div className="ml-3 text-sm">
                                        <label htmlFor={`modal-de-${value.id}`} className="font-medium text-gray-700 cursor-pointer">
                                            {/* REMOVED Short ID Span */}
                                            {value.name}
                                        </label>
                                        {/* Optional: Show DE description */}
                                        {value.description && (<p className="text-xs text-gray-500">{value.description}</p>)}
                                    </div>
                                </div>
                            ))}
                        </div>
                    ) : (
                        // Changed message
                        <p className="text-gray-500 text-center py-4">{t('securityFoundation.status.noDreadedEventsAvailable')}</p>
                    )}
                </div>
                {/* Modal Footer */}
                 <div className="px-6 py-4 bg-gray-50 border-t border-gray-200 flex justify-end space-x-3">
                    <button className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50" onClick={onClose}>{t('securityFoundation.buttons.cancel')}</button>
                    <button
                        className="px-4 py-2 rounded-md text-sm font-medium text-white bg-purple-600 hover:bg-purple-700" // Changed color
                        onClick={handleSaveClick}
                    >
                        {t('securityFoundation.buttons.confirm')}
                    </button>
                </div>
            </div>
        </div>
    );
};


// --- NEW: Rule Description Modal Component ---
export const RuleDescriptionModal = ({ isOpen, onClose, ruleName, ruleDescription }) => {
    const { t } = useTranslation();
    if (!isOpen) return null;

    return (
        <div className="fixed inset-0 bg-black bg-opacity-60 flex items-center justify-center p-4 z-[60]"> {/* Higher z-index than other modals */}
            <div className="bg-white rounded-lg shadow-xl max-w-xl w-full relative animate-fade-in-scale">
                 {/* Close Button */}
                <button
                    onClick={onClose}
                    className="absolute top-3 right-3 text-gray-400 hover:text-gray-700 hover:bg-gray-100 rounded-full p-1 transition-colors"
                    title={t('securityFoundation.buttons.close')}
                >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2}>
                        <path strokeLinecap="round" strokeLinejoin="round" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>

                {/* Header */}
                <div className="p-5 border-b border-gray-200">
                    <h3 className="text-lg font-medium text-gray-900">{t('securityFoundation.modal.ruleDescription')}</h3>
                    <p className="text-sm text-gray-600 mt-1 font-semibold truncate" title={ruleName}>{ruleName || t('securityFoundation.sections.rule')}</p>
                </div>

                {/* Body */}
                <div className="p-6 max-h-[60vh] overflow-y-auto">
                    <p className="text-sm text-gray-700 whitespace-pre-wrap">{ruleDescription || t('securityFoundation.status.noDescriptionAvailable')}</p>
                </div>

                {/* Optional Footer (if needed for actions) */}
                 {/* <div className="px-6 py-4 bg-gray-50 border-t border-gray-200 flex justify-end"> */}
                 {/*     <button className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50" onClick={onClose}>Fermer</button> */}
                 {/* </div> */}
            </div>
        </div>
    );
};


// --- Helper Functions ---

// Map severity strings to G-levels and Tailwind classes based on the image
const getSeverityLevelInfo = (severity, t) => {
    const lowerSeverity = severity?.toLowerCase() || '';
    // UPDATED: Check against actual data values first, then potentially French fallbacks if needed
    switch (lowerSeverity) {
        case 'catastrophic': // From your data
            return { level: 'G5', label: t('severity.catastrophic'), textClass: 'text-white', bgClass: 'bg-black' };
        case 'critical':     // From your data
            return { level: 'G4', label: t('severity.critical'), textClass: 'text-white', bgClass: 'bg-red-600' };
        // Assuming 'high' severity might map to G3 - Grave
        case 'major':
        case 'majeure':        // Keep French as fallback/alternative
            return { level: 'G3', label: t('severity.major'), textClass: 'text-black', bgClass: 'bg-yellow-500' };
        case 'moderate':     // From your data
        case 'significative': // Keep French as fallback/alternative
            return { level: 'G2', label: t('severity.moderate'), textClass: 'text-black', bgClass: 'bg-yellow-200' };
        // Assuming 'low' severity might map to G1 - Mineure
        case 'minor':
        case 'mineure':      // Keep French as fallback/alternative
            return { level: 'G1', label: t('severity.minor'), textClass: 'text-black', bgClass: 'bg-cyan-300' };
        default:
            console.warn(`Unknown severity value encountered: '${severity}'`); // Added warning
            return { level: 'N/A', label: t('securityFoundation.status.undefined'), textClass: 'text-gray-800', bgClass: 'bg-gray-200' };
    }
};

// Helper to get status label (RE-ADDED)
const getStatusLabel = (status, t) => {
    if (status === 'applied') return t('securityFoundation.status.applied');
    if (status === 'partially-applied') return t('securityFoundation.status.partially');
    if (status === 'not-applied') return t('securityFoundation.status.notApplied');
    return t('securityFoundation.status.unknown'); // Default/fallback label
};

// --- Summary Table Component (UPDATED for Merged Rows) ---
export const SecurityFrameworkSummaryTable = ({
    rulesToDisplay = [],
    onModifyRule,
    onDeleteRule,
}) => {
    const { t } = useTranslation();
    const exportToJson = () => {
         const jsonData = rulesToDisplay.map(rule => ({
             frameworkId: rule.frameworkId, frameworkName: rule.frameworkName, ruleId: rule.id, ruleName: rule.name, status: rule.status, statusLabel: getStatusLabel(rule.status, t), justification: rule.justification || "N/A",
             businessValueIds: rule.businessValues?.map(bv => bv.id) || [],
            dreadedEventIds: rule.dreadedEvents?.map(de => de.id) || [],
         }));
         const blob = new Blob([JSON.stringify(jsonData, null, 2)], { type: 'application/json' });
         const url = URL.createObjectURL(blob); const a = document.createElement('a'); a.href = url; a.download = 'summary.json'; a.click(); URL.revokeObjectURL(url);
    };

    // Pre-process rules to group by frameworkName
    const groupedRules = useMemo(() => {
        const groups = new Map();
        rulesToDisplay.forEach(rule => {
            if (!groups.has(rule.frameworkName)) {
                groups.set(rule.frameworkName, []);
            }
            groups.get(rule.frameworkName).push(rule);
        });
        // Convert map to array for easier iteration
        return Array.from(groups.entries()).map(([frameworkName, rules]) => ({ frameworkName, rules }));
    }, [rulesToDisplay]);

    return (
        <div className="mt-8 bg-white rounded-lg border border-gray-200 shadow overflow-hidden">
            {/* UPDATED Header Div - Removed Buttons */}
            <div className="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                <h3 className="text-lg font-medium leading-6 text-gray-900">{t('securityFoundation.sections.summary')}</h3>
      </div>

            {/* Keep class for printing trigger from parent */}
          <div className="overflow-x-auto summary-table-print-area">
                <table className="min-w-full divide-y divide-gray-200 border-collapse">
                  <thead className="bg-gray-50">
                        <tr>
                            {/* UPDATED Column Order & Added Règle Header */}
                            <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b-2 border-gray-200">{t('securityFoundation.table.headers.framework')}</th>
                            <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b-2 border-gray-200">{t('securityFoundation.table.headers.rule')}</th>
                            <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b-2 border-gray-200">{t('securityFoundation.table.headers.status')}</th>
                            <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b-2 border-gray-200">{t('securityFoundation.table.headers.justification')}</th>
                            {/* Combined ER/VM/BS column */}
                            <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b-2 border-gray-200">{t('securityFoundation.table.headers.eventsValuesAssets')}</th>
                            <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b-2 border-gray-200">{t('securityFoundation.table.headers.actions')}</th>
                      </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                        {groupedRules.length === 0 ? (
                            <tr>
                                {/* colspan should still be 6 based on current headers */}
                                <td colSpan="6" className="px-6 py-10 text-center text-sm text-gray-500">
                                    {t('securityFoundation.status.noRuleSelectedSummary')}
                                </td>
                            </tr>
                        ) : (
                            groupedRules.map(({ frameworkName, rules }) => (
                                // Render rows for each group
                                rules.map((item, ruleIndexInGroup) => (
                                    <tr key={`${item.frameworkId}-${item.id}`} className="hover:bg-gray-50">
                                        {/* 1. Framework Name (Render only for the first row of the group with rowspan) */}
                                        {ruleIndexInGroup === 0 && (
                                            <td
                                                rowSpan={rules.length} // Span across all rows in this group
                                                className="px-4 py-4 align-top text-sm font-medium text-gray-700 whitespace-nowrap border-r border-l border-b border-gray-200 w-48 bg-gray-50"
                                            >
                                                {frameworkName}
                                     </td>
                                 )}

                                        {/* 2. Rule Name */}
                                        <td className="px-4 py-4 align-top text-sm font-medium text-gray-800 border-r border-b border-gray-200 max-w-xs break-words">
                                            {item.name}
                                        </td>

                                        {/* 3. Status */}
                                        <td className="px-4 py-4 align-top text-sm text-gray-500 border-r border-b border-gray-200 whitespace-nowrap">
                                            <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${ item.status === 'applied' ? 'bg-green-100 text-green-800' : item.status === 'partially-applied' ? 'bg-yellow-100 text-yellow-800' : 'bg-gray-100 text-gray-800' }`}>
                                                {getStatusLabel(item.status, t)}
                                     </span>
                                 </td>

                                        {/* 4. Justification */}
                                        <td className="px-4 py-4 align-top text-sm text-gray-500 border-r border-b border-gray-200 max-w-xs break-words">
                                            {item.justification || <span className="text-gray-400 italic">{t('securityFoundation.status.none')}</span>}
                                 </td>

                                        {/* 5. Combined DE/BV/SA Column (UPDATED with VM:/BS: prefixes) */}
                                        <td className="px-4 py-4 align-top text-sm text-gray-900 border-r border-b border-gray-200">
                                            <div className="space-y-4">
                                                {(item.dreadedEvents || []).map((de, deIndex) => {
                                                    const severityInfo = getSeverityLevelInfo(de.severity, t);
                                                    return (
                                                        <div key={`${de.id}-details-${deIndex}`} className={deIndex > 0 ? "pt-4 border-t border-dashed border-gray-200" : ""}>
                                                            <div className="flex items-start space-x-3">
                                                                {/* Severity Square (Fixed Width) */}
                                                                <div
                                                                    className={`flex-shrink-0 p-2 rounded text-xs font-semibold flex flex-col items-center justify-center text-center w-24 ${severityInfo.bgClass} ${severityInfo.textClass}`}
                                                                    title={severityInfo.label}
                                                                >
                                                                    <span>{severityInfo.level}</span>
                                                                    <span>{severityInfo.label}</span>
                                                                </div>

                                                                {/* Details Block */}
                                                                <div className="flex-grow">
                                                                    {/* Event Name (with ER: Prefix) */}
                                                                    <div className="font-semibold text-gray-800 mb-1">
                                                                        ER: {de.name}
                                                                    </div>
                                                                    {/* Linked Business Value & Support Assets (with Prefixes) */}
                                                                    {de.linkedBusinessValue ? (
                                                                        <div className="pl-0 text-xs space-y-1">
                                                                            <div className="text-gray-600">
                                                                                <span className="font-medium text-gray-500">VM:</span> {de.linkedBusinessValue.name}
                                                                            </div>
                                                                            {(de.linkedBusinessValue.supportAssets || []).length > 0 && (
                                                                                <div className="text-gray-500">
                                                                                    <span className="font-medium">BS:</span> {
                                                                                        (de.linkedBusinessValue.supportAssets).map(sa => sa.name || `Asset ${sa.id}`).join(', ')
                                                                                    }
                                                 </div>
                                                                            )}
                                         </div>
                                     ) : (
                                                                        <div className="pl-0 text-xs text-gray-400 italic">{t('securityFoundation.status.noLinkedBusinessValue')}</div>
                                                                    )}
                                                                </div>
                                                 </div>
                                         </div>
                                                    );
                                                })}
                                                {(item.dreadedEvents || []).length === 0 && (
                                                    <div className="text-xs text-gray-400 italic">{t('securityFoundation.status.noDreadedEventsLinked')}</div>
                                                )}
                                            </div>
                                        </td>

                                        {/* 6. Actions (with Icons) */}
                                        <td className="px-4 py-4 align-top text-sm whitespace-nowrap space-x-3 border-b border-r border-gray-200">
                                            <button
                                                onClick={() => onModifyRule(item.id)}
                                                className="text-indigo-600 hover:text-indigo-900 p-1 rounded hover:bg-indigo-100 transition duration-150 ease-in-out"
                                                title={t('securityFoundation.buttons.modifyRule')}
                                            >
                                                {/* Pencil Icon SVG */}
                                                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2}>
                                                    <path strokeLinecap="round" strokeLinejoin="round" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
                                                </svg>
                                            </button>
                                            <button
                                                onClick={() => onDeleteRule(item.id)}
                                                className="text-red-600 hover:text-red-900 p-1 rounded hover:bg-red-100 transition duration-150 ease-in-out"
                                                title={t('securityFoundation.buttons.deleteRule')}
                                            >
                                                {/* Trash Icon SVG */}
                                                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2}>
                                                    <path strokeLinecap="round" strokeLinejoin="round" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                                </svg>
                                            </button>
                                 </td>
                             </tr>
                                ))
                         ))
                     )}
                  </tbody>
              </table>
      </div>
  </div>
    );
};

// --- END OF FILE FrameworkComponents.js ---