// src/context/AnalysisContext.js
import React, { createContext, useContext, useState, useEffect, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from './AuthContext';
import api from '../api/apiClient';
import { setCurrentAnalysisId, getCurrentAnalysisId } from '../utils/appStateManagerAPI';

// Import all workshop-related services
import contextService from '../services/contextService';
import businessValueService from '../services/businessValuesService';
import dreadedEventService from '../services/dreadedEventsService';
import securityFrameworkService from '../services/securityFrameworkService';
import securityControlsService from '../services/securityControlsService';
import riskTreatmentService from '../services/riskTreatmentService';
import frameworkDefinitionService from '../services/frameworkDefinitionService';
import atelier2Service from '../services/atelier2Service';
import stakeholderService from '../services/stakeholderService';

// Create Analysis Context
const AnalysisContext = createContext(null);

/**
 * Analysis Provider Component for managing the current analysis
 * and related operations
 */
export const AnalysisProvider = ({ children }) => {
  // Analysis state
  const [currentAnalysis, setCurrentAnalysis] = useState(null);
  const [analyses, setAnalyses] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);

  // Workshop data states
  const [currentContextData, setCurrentContextData] = useState(null);
  const [currentBusinessValues, setCurrentBusinessValues] = useState([]);
  const [currentDreadedEvents, setCurrentDreadedEvents] = useState([]);
  const [currentSecurityFramework, setCurrentSecurityFramework] = useState(null);
  const [currentAnalysisControlPlan, setCurrentAnalysisControlPlan] = useState(null);
  const [currentRiskTreatments, setCurrentRiskTreatments] = useState([]);

  // ---> ADDED: State for the master list of all controls <---
  const [allControls, setAllControls] = useState([]);

  // Atelier 3 states
  const [currentStakeholders, setCurrentStakeholders] = useState([]);
  const [stakeholderThresholds, setStakeholderThresholds] = useState({
    danger: 3,
    control: 1.5,
    watch: 0.5
  });

  // Workshop data loading states
  const [isWorkshopDataLoading, setIsWorkshopDataLoading] = useState(false);
  const [workshopDataError, setWorkshopDataError] = useState(null);

  // Auth context for user info
  const { user, isAuthenticated } = useAuth();
  const navigate = useNavigate();

  // Load analyses list
  const loadAnalyses = async () => {
    if (!isAuthenticated || !user) return;

    setIsLoading(true);
    setError(null);

    try {
      let response;

      // If superadmin, get all analyses
      if (user.role === 'superadmin') {
        response = await api.get('/analyses');
      }
      // If admin, get analyses for their company
      else if (user.role === 'admin' && user.companyId) {
        response = await api.get(`/companies/${user.companyId}/analyses`);
      }
      // Otherwise, get analyses (API will filter based on user permissions)
      else {
        response = await api.get('/analyses');
      }

      if (response.success) {
        setAnalyses(response.data || []);
      } else {
        setError(response.message || 'Failed to load analyses');
      }
    } catch (error) {
      console.error('Error loading analyses:', error);
      setError('Error loading analyses. Please try again later.');

      // For development, provide some mock data
      if (process.env.NODE_ENV === 'development') {
        console.warn('Using mock analyses data for development');
        setAnalyses([
          {
            id: 'mock-analysis-1',
            name: 'Risk Analysis - Main Project',
            description: 'Main cybersecurity risk analysis',
            status: 'in-progress',
            companyId: user.companyId,
            companyName: user.companyName,
            createdAt: '2025-01-15T08:30:00.000Z',
            updatedAt: '2025-03-10T14:22:00.000Z'
          },
          {
            id: 'mock-analysis-2',
            name: 'GDPR Compliance Analysis',
            description: 'Analysis for GDPR requirements',
            status: 'draft',
            companyId: user.companyId,
            companyName: user.companyName,
            createdAt: '2025-02-20T09:15:00.000Z',
            updatedAt: '2025-02-20T09:15:00.000Z'
          }
        ]);
      }
    } finally {
      setIsLoading(false);
    }
  };

  // Load all workshop data for an analysis
  const loadWorkshopData = async (analysisId) => {
    if (!analysisId) return;

    setIsWorkshopDataLoading(true);
    setWorkshopDataError(null);
    // Removed sensitive data logging for security

    try {
      // ---> ADDED: Overall try...catch for Promise.allSettled and processing <---
      try {
        // Determine which data to load based on the current route/tab
        const currentPath = window.location.pathname;
        const isRiskTreatmentNeeded = currentPath.includes('risk-treatment') || currentPath.includes('dashboard');

        // Create an array of promises to fetch data
        const dataPromises = [
          contextService.getContext(analysisId),
          businessValueService.getBusinessValues(analysisId),
          dreadedEventService.getDreadedEvents(analysisId),
          securityFrameworkService.getSecurityFramework(analysisId),
          securityControlsService.getSecurityControls(analysisId),
          // Only load risk treatment data if needed
          isRiskTreatmentNeeded ? riskTreatmentService.getRiskTreatment(analysisId) : Promise.resolve({ data: { riskTreatments: [] } }),
          frameworkDefinitionService.getFrameworkDefinitions(analysisId),
          securityControlsService.getAllControlDefinitions(),
          stakeholderService.getStakeholders(analysisId),
          stakeholderService.getThresholds(analysisId)
        ];

        const results = await Promise.allSettled(dataPromises);

        // Removed sensitive data logging for security

        const [contextRes, bvRes, deRes, sfRes, scPlanRes, rtRes, defRes, allControlsRes, stakeholdersRes, thresholdsRes] = results;
        const errors = {};

        // --- Process Context ---
        if (contextRes.status === 'fulfilled' && contextRes.value?.data) {
            // Removed sensitive data logging for security
            setCurrentContextData(contextService.formatContextData(contextRes.value));
        } else if (contextRes.status === 'rejected' && contextRes.reason?.status === 404 ||
                   contextRes.status === 'fulfilled' && !contextRes.value?.data) {
            // Removed sensitive data logging for security
            setCurrentContextData(null);
        } else {
            const errorMsg = contextRes.reason?.message || contextRes.value?.message || 'Failed to load context data';
            errors.context = errorMsg;
            // Log without revealing sensitive data
            console.error('Error loading context data');
        }

        // --- Process Business Values ---
        if (bvRes.status === 'fulfilled' && bvRes.value?.data?.businessValues?.length > 0) {
            // Removed sensitive data logging for security
            setCurrentBusinessValues(businessValueService.formatBusinessValuesData(bvRes.value));
        } else if (bvRes.status === 'rejected' && bvRes.reason?.status === 404 ||
                   bvRes.status === 'fulfilled' && (!bvRes.value?.data || !bvRes.value?.data?.businessValues?.length)) {
            // Removed sensitive data logging for security
            setCurrentBusinessValues([]);
        } else {
            const errorMsg = bvRes.reason?.message || bvRes.value?.message || 'Failed to load business values';
            errors.businessValues = errorMsg;
            // Log without revealing sensitive data
            console.error('Error loading business values');
        }

        // --- Process Dreaded Events ---
         if (deRes.status === 'fulfilled' && deRes.value?.data?.data?.dreadedEvents?.length > 0) {
             // Removed sensitive data logging for security
             const actualDreadedEvents = deRes.value.data.data.dreadedEvents;
             setCurrentDreadedEvents(actualDreadedEvents);
         } else if (deRes.status === 'rejected' && deRes.reason?.status === 404 ||
                    deRes.status === 'fulfilled' && (!deRes.value?.data?.data?.dreadedEvents?.length)) {
             // Removed sensitive data logging for security
             setCurrentDreadedEvents([]);
         } else {
             const errorMsg = deRes.reason?.message || deRes.value?.message || 'Failed to load dreaded events';
             errors.dreadedEvents = errorMsg;
             // Log without revealing sensitive data
             console.error('Error loading dreaded events');
         }

        // --- Process Security Framework ---
        let loadedDefinitions = [];
        let loadedSelections = null;

        // Get Definitions first
        if (defRes.status === 'fulfilled' && defRes.value?.success) {
            loadedDefinitions = defRes.value.data || [];
            // Removed sensitive data logging for security
        } else {
            const errorMsg = defRes.reason?.message || defRes.value?.message || 'Failed to load framework definitions';
            errors.frameworkDefinitions = errorMsg;
            // Log without revealing sensitive data
            console.error('Error loading framework definitions');
            // Continue without definitions if fetch failed
        }

        // Get Selections
        if (sfRes.status === 'fulfilled' && sfRes.value?.data) {
            // Removed sensitive data logging for security
            const formattedData = securityFrameworkService.formatSecurityFrameworkData(sfRes.value);
            if (formattedData && (Object.keys(formattedData.selectedRules || {}).length > 0 || (formattedData.ruleBusinessValues && Object.keys(formattedData.ruleBusinessValues).length > 0))) {
                 loadedSelections = formattedData;
             } else {
                  // Removed sensitive data logging for security
                  loadedSelections = {}; // Use empty object if selections exist but are empty
             }
        } else if (sfRes.status === 'rejected' && sfRes.reason?.status === 404 ||
                   sfRes.status === 'fulfilled' && !sfRes.value?.data) {
            // Removed sensitive data logging for security
            loadedSelections = {}; // Use empty object if not found
        } else {
            const errorMsg = sfRes.reason?.message || sfRes.value?.message || 'Failed to load security framework selections';
            errors.securityFramework = errorMsg; // Add to overall errors
            // Log without revealing sensitive data
            console.error('Error loading security framework selections');
            // Continue, but selections will be null/empty
        }

        // Combine definitions and selections
        const combinedFrameworkData = {
            ...(loadedSelections || {}), // Spread selections (or empty object)
            definitions: loadedDefinitions // Add definitions array
        };
        // Removed sensitive data logging for security
        setCurrentSecurityFramework(combinedFrameworkData);

        // --- Process Analysis Control Plan ---
        if (scPlanRes.status === 'fulfilled' && scPlanRes.value) {
            // Removed sensitive data logging for security
            setCurrentAnalysisControlPlan(scPlanRes.value);
        } else if (scPlanRes.status === 'rejected' && scPlanRes.reason?.status === 404) {
            // Removed sensitive data logging for security
            setCurrentAnalysisControlPlan({ planData: {} });
        } else {
            const errorMsg = scPlanRes.reason?.message || scPlanRes.value?.message || 'Failed to load analysis control plan';
            errors.securityControls = errorMsg;
            // Log without revealing sensitive data
            console.error('Error loading analysis control plan');
            setCurrentAnalysisControlPlan(null);
        }

        // --- Process Risk Treatments ---
        // Use the same isRiskTreatmentNeeded variable from earlier
        if (!isRiskTreatmentNeeded) {
            // Skip processing risk treatment data if not needed
            console.log('Skipping risk treatment data processing - not needed for current view');
            setCurrentRiskTreatments([]);
        } else if (rtRes.status === 'fulfilled' && rtRes.value?.data?.riskTreatments?.length > 0) {
            // Process risk treatment data if available
            setCurrentRiskTreatments(riskTreatmentService.formatRiskTreatmentData(rtRes.value));
        } else if (rtRes.status === 'rejected' && rtRes.reason?.status === 404 ||
                   rtRes.status === 'fulfilled' && (!rtRes.value?.data || !rtRes.value?.data?.riskTreatments?.length)) {
            // Set empty array if no data found
            setCurrentRiskTreatments([]);
        } else {
            // Handle errors
            const errorMsg = rtRes.reason?.message || rtRes.value?.message || 'Failed to load risk treatments';
            // Only add error if we actually need the risk treatment data
            if (isRiskTreatmentNeeded) {
                errors.riskTreatment = errorMsg;
                console.error('Error loading risk treatments');
            } else {
                console.log('Risk treatment data error ignored - not needed for current view');
            }
        }

        // --- Process Master Control List ---
        if (allControlsRes.status === 'fulfilled' && allControlsRes.value?.success && Array.isArray(allControlsRes.value.data)) {
            // Removed sensitive data logging for security
            setAllControls(allControlsRes.value.data);
        } else {
            // Log error but don't necessarily block loading if master list fails
            const errorMsg = allControlsRes.reason?.message || allControlsRes.value?.message || 'Failed to load master control list';
            errors.allControls = errorMsg;
            // Log without revealing sensitive data
            console.error('Error loading master control list');
            setAllControls([]); // Set empty array on failure
        }

        // --- Process Stakeholders ---
        if (stakeholdersRes.status === 'fulfilled' && stakeholdersRes.value?.data) {
            setCurrentStakeholders(stakeholdersRes.value.data);
        } else if (stakeholdersRes.status === 'rejected' && stakeholdersRes.reason?.status === 404 ||
                   stakeholdersRes.status === 'fulfilled' && !stakeholdersRes.value?.data) {
            setCurrentStakeholders([]);
        } else {
            const errorMsg = stakeholdersRes.reason?.message || stakeholdersRes.value?.message || 'Failed to load stakeholders';
            errors.stakeholders = errorMsg;
            console.error('Error loading stakeholders');
        }

        // --- Process Stakeholder Thresholds ---
        if (thresholdsRes.status === 'fulfilled' && thresholdsRes.value?.data) {
            setStakeholderThresholds(thresholdsRes.value.data);
        } else if (thresholdsRes.status === 'rejected' && thresholdsRes.reason?.status === 404 ||
                   thresholdsRes.status === 'fulfilled' && !thresholdsRes.value?.data) {
            // Keep default thresholds
        } else {
            const errorMsg = thresholdsRes.reason?.message || thresholdsRes.value?.message || 'Failed to load stakeholder thresholds';
            errors.stakeholderThresholds = errorMsg;
            console.error('Error loading stakeholder thresholds');
        }

        // Set combined errors
        if (Object.keys(errors).length > 0) {
            // Log without revealing sensitive data
            console.log("CONTEXT: Workshop data loading encountered errors");
            setWorkshopDataError(errors);
        } else {
             // Removed sensitive data logging for security
             // Ensure error state is cleared if all promises succeed
             setWorkshopDataError(null);
        }

      } catch (processingError) {
          console.error('CONTEXT: Critical error processing workshop data results:', processingError);
          setWorkshopDataError({ general: 'Failed to process loaded workshop data.', details: processingError.message });
          // Reset states to prevent partial data issues
          setCurrentContextData(null);
          setCurrentBusinessValues([]);
          setCurrentDreadedEvents([]);
          setCurrentSecurityFramework(null);
          setCurrentAnalysisControlPlan(null);
          setCurrentRiskTreatments([]);
          setAllControls([]);
      }

    } catch (error) {
      console.error('CONTEXT: Unexpected error during workshop data fetch:', error);
      setWorkshopDataError({ general: 'Failed to load workshop data due to an unexpected error.' });
    } finally {
      setIsWorkshopDataLoading(false);
    }
  };

  // Load a specific analysis by ID and all its associated workshop data
  const loadAnalysis = async (analysisId) => {
    // Don't do anything if it's already the selected analysis
    if (currentAnalysis && currentAnalysis.id === analysisId) {
      console.log("Analysis already loaded:", analysisId);
      return currentAnalysis;
    }

    setIsLoading(true);
    setError(null);

    try {
      const response = await api.get(`/analyses/${analysisId}`);

      if (response.success) {
        // Important: set the stored ID first, then update the object
        setCurrentAnalysisId(analysisId);
        setCurrentAnalysis(response.data);

        // After loading the analysis, load all workshop data
        await loadWorkshopData(analysisId);

        return response.data;
      } else {
        setError(response.message || 'Failed to load analysis');
        return null;
      }
    } catch (error) {
      console.error('Error loading analysis:', error);
      setError('Error loading analysis. Please try again later.');

      // For development, provide mock data
      if (process.env.NODE_ENV === 'development') {
        console.warn('Using mock analysis data for development');
        const mockAnalysis = {
          id: analysisId,
          name: 'Mock Analysis',
          description: 'Mock analysis for development',
          status: 'in-progress',
          companyId: user?.companyId || 'mock-company',
          companyName: user?.companyName || 'Mock Company',
          createdAt: '2025-01-15T08:30:00.000Z',
          updatedAt: '2025-03-10T14:22:00.000Z'
        };

        setCurrentAnalysis(mockAnalysis);
        setCurrentAnalysisId(analysisId);

        // Load mock workshop data for development
        await mockLoadWorkshopData(analysisId);

        return mockAnalysis;
      }

      return null;
    } finally {
      setIsLoading(false);
    }
  };

  // Mock function to load demo workshop data in development
  const mockLoadWorkshopData = async (_analysisId) => {
    setIsWorkshopDataLoading(true);

    try {
      // Initialize with empty data instead of mock data
      setCurrentContextData({
        organizationName: '',
        missions: [],
        scope: '',
        analysisDate: '',
        participants: []
      });

      setCurrentBusinessValues([
        { id: 1, name: 'Customer Data', description: 'All customer information and records', pillars: ['confidentiality', 'integrity'] },
        { id: 2, name: 'Financial Operations', description: 'Core financial processes and systems', pillars: ['availability', 'integrity'] }
      ]);

      setCurrentDreadedEvents([
        { id: 1, name: 'Data Breach', description: 'Unauthorized access to customer data', businessValue: '1' },
        { id: 2, name: 'Service Interruption', description: 'Critical service downtime', businessValue: '2' }
      ]);

      // Mock combined framework data
      setCurrentSecurityFramework({
        // Mock Selections
        selectedRules: {
          'iso27001-a5': true,
          'iso27001-a8': true,
          'nist-id': true
        },
        ruleBusinessValues: {},
        summary: [],
        // Mock Definitions
        definitions: [
          { id: 'iso27001', name: 'ISO 27001 (Mock)', description: 'Mock Description', is_predefined: true, rules: [] },
          { id: 'nist', name: 'NIST CSF (Mock)', description: 'Mock Description', is_predefined: true, rules: [] }
        ]
      });

      setCurrentAnalysisControlPlan({
        planData: {
          plan: {
            'rule1_1': {
              controls: [
                {
                  id: 'control1',
                  name: 'Chiffrement des données sensibles',
                  description: 'Mise en place du chiffrement pour toutes les données sensibles',
                  type: 'Technique',
                  decision: 'implemented',
                  responsiblePerson: 'John Doe'
                },
                {
                  id: 'control2',
                  name: 'Authentification multi-facteurs',
                  description: 'Mise en place de l\'authentification à deux facteurs pour tous les accès',
                  type: 'Technique',
                  decision: 'planned',
                  responsiblePerson: 'Jane Smith'
                },
                {
                  id: 'control4',
                  name: 'Sauvegarde des données',
                  description: 'Mise en place d\'un système de sauvegarde automatique des données',
                  type: 'Technique',
                  decision: 'apply_before',
                  responsiblePerson: 'John Doe'
                },
                {
                  id: 'control5',
                  name: 'Antivirus périmétrique',
                  description: 'Installation d\'un antivirus sur le périmètre réseau',
                  type: 'Technique',
                  decision: 'abandoned',
                  responsiblePerson: 'Jane Smith'
                }
              ]
            },
            'rule2_2': {
              controls: [
                {
                  id: 'control3',
                  name: 'Formation de sensibilisation',
                  description: 'Formation annuelle de sensibilisation à la sécurité',
                  type: 'Organisationnel',
                  decision: 'partial',
                  responsiblePerson: 'John Doe'
                }
              ]
            }
          }
        }
      });

      setCurrentRiskTreatments([
        { id: 1, name: 'Data Encryption Implementation', status: 'in-progress', relatedEvents: [1] },
        { id: 2, name: 'Redundancy Planning', status: 'planned', relatedEvents: [2] }
      ]);

      // Mock stakeholders data
      setCurrentStakeholders([
        {
          id: 'mock-stakeholder-1',
          name: 'InfoTech Services',
          category: 'provider',
          type: 'external',
          description: 'Prestataire informatique principal gérant l\'infrastructure réseau et les serveurs de l\'entreprise.',
          rank: 1,
          dependency: 4,
          penetration: 4,
          cyberMaturity: 3,
          trust: 3,
          threatLevel: 1.78,
          createdAt: new Date(),
          updatedAt: new Date()
        },
        {
          id: 'mock-stakeholder-2',
          name: 'Service Financier',
          category: 'business',
          type: 'internal',
          description: 'Département financier gérant les opérations comptables et les paiements.',
          rank: 1,
          dependency: 3,
          penetration: 2,
          cyberMaturity: 2,
          trust: 4,
          threatLevel: 0.75,
          createdAt: new Date(),
          updatedAt: new Date()
        }
      ]);
    } catch (error) {
      console.error('Error loading mock workshop data:', error);
    } finally {
      setIsWorkshopDataLoading(false);
    }
  };

  // Save functions for each workshop component

  // Save context data
  const saveCurrentContextData = async (contextData) => {
    if (!currentAnalysis?.id) {
      console.error('No analysis selected');
      return { success: false, message: 'No analysis selected' };
    }

    try {
      const response = await contextService.saveContext(currentAnalysis.id, contextData);
      if (response.success) {
        setCurrentContextData(contextService.formatContextData(response.data));
      }
      return response;
    } catch (error) {
      console.error('Error saving context data:', error);
      return { success: false, message: error.message || 'Failed to save context data' };
    }
  };

  // Save business values
  const saveCurrentBusinessValues = async (businessValuesData) => {
    if (!currentAnalysis?.id) {
      console.error('No analysis selected');
      return { success: false, message: 'No analysis selected' };
    }

    try {
      const response = await businessValueService.saveBusinessValues(currentAnalysis.id, businessValuesData);
      if (response.success) {
        setCurrentBusinessValues(businessValueService.formatBusinessValuesData(response.data));
      }
      return response;
    } catch (error) {
      console.error('Error saving business values:', error);
      return { success: false, message: error.message || 'Failed to save business values' };
    }
  };

  // Save dreaded events
  const saveCurrentDreadedEvents = async (dreadedEventsData) => {
    if (!currentAnalysis?.id) {
      console.error('No analysis selected');
      return { success: false, message: 'No analysis selected' };
    }

    try {
      const response = await dreadedEventService.saveDreadedEvents(currentAnalysis.id, dreadedEventsData);
      if (response.success) {
        setCurrentDreadedEvents(response.data?.data?.dreadedEvents || []);
      }
      return response;
    } catch (error) {
      console.error('Error saving dreaded events:', error);
      return { success: false, message: error.message || 'Failed to save dreaded events' };
    }
  };

  // Save security framework selections
  const saveCurrentSecurityFramework = async (securityFrameworkData, updatedDefinitionsList = null) => {
    if (!currentAnalysis?.id) {
      console.error('No analysis selected');
      return { success: false, message: 'No analysis selected' };
    }

    try {
      // Use the provided updatedDefinitionsList if available, otherwise use current state
      const definitionsToUse = updatedDefinitionsList !== null ? updatedDefinitionsList : (currentSecurityFramework?.definitions || []);

      // Removed sensitive data logging for security
      const response = await securityFrameworkService.saveSecurityFramework(currentAnalysis.id, securityFrameworkData);

      if (response.success) {
        // Combine the saved selections with the potentially updated definitions
        const combinedData = {
          ...(securityFrameworkService.formatSecurityFrameworkData(response) || {}),
          definitions: definitionsToUse
        };
        console.log("CONTEXT: Updating security framework state after successful save."); // Log success, not data
        setCurrentSecurityFramework(combinedData);

        return { success: true, data: response.data };
      } else {
        // Return the error response from the service
        return response;
      }
    } catch (error) {
      console.error('Error saving security framework selections:', error);
      return { success: false, message: error.message || 'Failed to save security framework selections' };
    }
  };

  // Save analysis control plan
  const saveCurrentAnalysisControlPlan = async (planDataToSave) => {
    if (!currentAnalysis?.id) {
      console.error('No analysis selected for saving control plan');
      return { success: false, message: 'No analysis selected' };
    }
    console.log("CONTEXT: Saving Analysis Control Plan..."); // Log intent, not data
    try {
      const response = await securityControlsService.saveSecurityControls(currentAnalysis.id, planDataToSave);

      if (response.success) {
        console.log("CONTEXT: Control Plan save successful, updating state."); // Log success, not data
        setCurrentAnalysisControlPlan(response.data);
        return { success: true, data: response.data };
      } else {
        // Return the error response from the service
        return response;
      }
    } catch (error) {
      console.error('Error saving analysis control plan:', error);
      return { success: false, message: error.message || 'Failed to save analysis control plan' };
    }
  };

  // Save risk treatments
  const saveCurrentRiskTreatments = async (riskTreatmentsData) => {
    if (!currentAnalysis?.id) {
      console.error('No analysis selected');
      return { success: false, message: 'No analysis selected' };
    }

    try {
      const response = await riskTreatmentService.saveRiskTreatment(currentAnalysis.id, riskTreatmentsData);
      if (response.success) {
        setCurrentRiskTreatments(riskTreatmentService.formatRiskTreatmentData(response.data));
      }
      return response;
    } catch (error) {
      console.error('Error saving risk treatments:', error);
      return { success: false, message: error.message || 'Failed to save risk treatments' };
    }
  };

  // CRUD operations for analyses

  // Create a new analysis
  const createAnalysis = async (analysisData) => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await api.post('/analyses', analysisData);

      if (response.success) {
        setAnalyses([...analyses, response.data]);
        // Optionally select the new analysis
        // await selectAnalysis(response.data.id);
        return { success: true, data: response.data };
      } else {
        setError(response.message || 'Failed to create analysis');
        return { success: false, message: response.message };
      }
    } catch (error) {
      console.error('Error creating analysis:', error);
      setError('Error creating analysis. Please try again later.');
      return { success: false, message: error.message || 'Error creating analysis' };
    } finally {
      setIsLoading(false);
    }
  };

  // Update an existing analysis
  const updateAnalysis = async (analysisId, analysisData) => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await api.put(`/analyses/${analysisId}`, analysisData);

      if (response.success) {
        const updatedAnalyses = analyses.map(analysis =>
          analysis.id === analysisId ? response.data : analysis
        );
        setAnalyses(updatedAnalyses);

        // If the updated analysis is the current one, update it
        if (currentAnalysis && currentAnalysis.id === analysisId) {
          setCurrentAnalysis(response.data);
        }
        return { success: true, data: response.data };
      } else {
        setError(response.message || 'Failed to update analysis');
        return { success: false, message: response.message };
      }
    } catch (error) {
      console.error('Error updating analysis:', error);
      setError('Error updating analysis. Please try again later.');
      return { success: false, message: error.message || 'Error updating analysis' };
    } finally {
      setIsLoading(false);
    }
  };

  // Delete an analysis
  const deleteAnalysis = async (analysisId) => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await api.delete(`/analyses/${analysisId}`);

      if (response.success) {
        const updatedAnalyses = analyses.filter(analysis => analysis.id !== analysisId);
        setAnalyses(updatedAnalyses);

        // If the deleted analysis was the current one, clear it
        if (currentAnalysis && currentAnalysis.id === analysisId) {
          setCurrentAnalysis(null);
          setCurrentAnalysisId(null); // Clear stored ID
          // Reset all workshop data
          setCurrentContextData(null);
          setCurrentBusinessValues([]);
          setCurrentDreadedEvents([]);
          setCurrentSecurityFramework(null);
          setCurrentAnalysisControlPlan(null);
          setCurrentRiskTreatments([]);
          setAllControls([]);
        }
        return { success: true };
      } else {
        setError(response.message || 'Failed to delete analysis');
        return { success: false, message: response.message };
      }
    } catch (error) {
      console.error('Error deleting analysis:', error);
      setError('Error deleting analysis. Please try again later.');
      return { success: false, message: error.message || 'Error deleting analysis' };
    } finally {
      setIsLoading(false);
    }
  };

  // Effect to load analyses when user is authenticated
  useEffect(() => {
    if (isAuthenticated && user) {
      loadAnalyses();
    } else {
      // Clear state if user logs out
      setAnalyses([]);
      setCurrentAnalysis(null);
      setCurrentAnalysisId(null);
      // Reset all workshop data
      setCurrentContextData(null);
      setCurrentBusinessValues([]);
      setCurrentDreadedEvents([]);
      setCurrentSecurityFramework(null);
      setCurrentAnalysisControlPlan(null);
      setCurrentRiskTreatments([]);
      setAllControls([]);
    }
  }, [isAuthenticated, user]); // Dependency on user ensures reload if user info changes

  // Effect to load the last selected analysis on mount
  useEffect(() => {
    const lastAnalysisId = getCurrentAnalysisId();
    if (lastAnalysisId && isAuthenticated && user) {
      // Check if the last analysis ID is still valid within the loaded analyses list
      const isValidLastAnalysis = analyses.some(a => a.id === lastAnalysisId);
      if (isValidLastAnalysis) {
        selectAnalysis(lastAnalysisId);
      } else if (analyses.length > 0) {
        // If last ID is invalid, select the first available analysis
        selectAnalysis(analyses[0].id);
      }
    }
    // Run only once on mount after analyses are potentially loaded
  }, [analyses, isAuthenticated, user]); // Depend on analyses list

  // Atelier 2 specific functions (proxied through context)
  const getThreatCategories = async (analysisId) => {
    try {
      return await atelier2Service.getThreatCategories(analysisId);
    } catch (error) {
      // Log without revealing sensitive data
      console.error('Error loading threat categories');
      return [];
    }
  };

  const saveThreatCategories = async (analysisId, data) => {
    try {
      return await atelier2Service.saveThreatCategories(analysisId, data);
    } catch (error) {
      console.error('Error saving threat categories:', error);
      return { success: false, message: error.message };
    }
  };

  const getSourcesDeRisque = useCallback(async (analysisId) => {
    try {
      console.log("AnalysisContext - getSourcesDeRisque called for analysis:", analysisId);

      // Check if the analysis ID is valid
      if (!analysisId) {
        console.error('Invalid analysis ID provided to getSourcesDeRisque');
        return [];
      }

      // Call the service function
      const result = await atelier2Service.getSourcesRisque(analysisId);
      console.log("AnalysisContext - getSourcesDeRisque result:", result);

      // Ensure we always return an array
      if (!result || !Array.isArray(result)) {
        console.warn("AnalysisContext - getSourcesDeRisque returned non-array result, returning empty array");
        return [];
      }

      return result;
    } catch (error) {
      // Log error details without sensitive data
      console.error('Error loading sources de risque:', error.message);

      // Always return an empty array on error
      return [];
    }
  }, []);

  const saveSourcesDeRisque = async (analysisId, data) => {
    try {
      console.log("AnalysisContext - saveSourcesDeRisque called for analysis:", analysisId);

      // Check if the analysis ID is valid
      if (!analysisId) {
        console.error('Invalid analysis ID provided to saveSourcesDeRisque');
        return { success: false, message: 'Invalid analysis ID' };
      }

      // Call the service function
      const result = await atelier2Service.saveSourcesRisque(analysisId, data);
      console.log("AnalysisContext - saveSourcesDeRisque result:", result);

      return result;
    } catch (error) {
      // Log error details without sensitive data
      console.error('Error saving sources de risque:', error.message);
      return { success: false, message: error.message };
    }
  };
  const getObjectifsVises = async (analysisId) => {
    try {
      return await atelier2Service.getObjectifsVises(analysisId);
    } catch (error) {
      // Log without revealing sensitive data
      console.error('Error loading objectifs visés');
      return [];
    }
  };

  const saveObjectifsVises = async (analysisId, data) => {
    try {
      return await atelier2Service.saveObjectifsVises(analysisId, data);
    } catch (error) {
      // Log without revealing sensitive data
      console.error('Error saving objectifs visés');
      return { success: false, message: error.message };
    }
  };

  const getCouplesSROV = async (analysisId) => {
    try {
      return await atelier2Service.getCouplesSROV(analysisId);
    } catch (error) {
      // Log without revealing sensitive data
      console.error('Error loading couples SR/OV');
      return [];
    }
  };

  const saveCouplesSROV = async (analysisId, data) => {
    try {
      return await atelier2Service.saveCouplesSROV(analysisId, data);
    } catch (error) {
      // Log without revealing sensitive data
      console.error('Error saving couples SR/OV');
      return { success: false, message: error.message };
    }
  };

  const getSelectedCouplesSROV = useCallback(async (analysisId) => {
    try {
      return await atelier2Service.getSelectedCouplesSROV(analysisId);
    } catch (error) {
      // Log without revealing sensitive data
      console.error('Error loading selected couples SR/OV');
      return [];
    }
  }, []);

  const saveSelectedCouplesSROV = async (analysisId, data) => {
    try {
      return await atelier2Service.saveSelectedCouplesSROV(analysisId, data);
    } catch (error) {
      // Log without revealing sensitive data
      console.error('Error saving selected couples SR/OV');
      return { success: false, message: error.message };
    }
  };

  // Atelier 3 functions

  // Get stakeholders
  const getStakeholders = useCallback(async (analysisId) => {
    try {
      // Check if we already have stakeholders in state to avoid unnecessary API calls
      if (currentStakeholders && currentStakeholders.length > 0) {
        return { success: true, data: currentStakeholders };
      }

      const response = await stakeholderService.getStakeholders(analysisId);
      if (response.success) {
        setCurrentStakeholders(response.data);
        return response;
      }

      // If API call fails, return empty array
      return { success: true, data: [] };
    } catch (error) {
      console.error('Error getting stakeholders:', error);
      return { success: false, data: [] };
    }
  }, [currentStakeholders]);

  // Save stakeholders
  const saveStakeholders = async (analysisId, stakeholders) => {
    try {
      // Update local state immediately for better UX
      setCurrentStakeholders(stakeholders);

      // Then send to server
      const response = await stakeholderService.saveStakeholders(analysisId, stakeholders);

      // If server update fails, we keep the local state updated anyway
      // This prevents UI inconsistencies
      return response.success
        ? response
        : {
            success: true,
            message: 'Stakeholders updated locally only',
            data: stakeholders
          };
    } catch (error) {
      console.error('Error saving stakeholders:', error);
      // Even if API call fails, we keep the local state updated
      return {
        success: true,
        message: 'Stakeholders updated locally only',
        data: stakeholders
      };
    }
  };

  // Get stakeholder thresholds
  const getStakeholderThresholds = async (analysisId) => {
    try {
      // Check if we already have thresholds in state to avoid unnecessary API calls
      if (stakeholderThresholds) {
        return { success: true, data: stakeholderThresholds };
      }

      const response = await stakeholderService.getThresholds(analysisId);
      if (response.success) {
        setStakeholderThresholds(response.data);
        return response;
      }

      // If API call fails, return default thresholds
      const defaultThresholds = {
        danger: 3,
        control: 1.5,
        watch: 0.5
      };
      setStakeholderThresholds(defaultThresholds);
      return { success: true, data: defaultThresholds };
    } catch (error) {
      console.error('Error getting stakeholder thresholds:', error);
      // Return default thresholds on error
      const defaultThresholds = {
        danger: 3,
        control: 1.5,
        watch: 0.5
      };
      return { success: true, data: defaultThresholds };
    }
  };

  // Update stakeholder thresholds
  const updateStakeholderThresholds = async (analysisId, thresholds) => {
    try {
      // Update local state immediately for better UX
      setStakeholderThresholds(thresholds);

      // Then send to server
      const response = await stakeholderService.updateThresholds(analysisId, thresholds);

      // If server update fails, we keep the local state updated anyway
      // This prevents UI inconsistencies
      return response.success
        ? response
        : {
            success: true,
            message: 'Thresholds updated locally only',
            data: thresholds
          };
    } catch (error) {
      console.error('Error updating stakeholder thresholds:', error);
      // Even if API call fails, we keep the local state updated
      return {
        success: true,
        message: 'Thresholds updated locally only',
        data: thresholds
      };
    }
  };

  // Function to select an analysis and load its data
  const selectAnalysis = async (analysisId) => {
    if (!analysisId) {
      // Clear current analysis and related data if null ID is passed
      setCurrentAnalysis(null);
      setCurrentAnalysisId(null);
      setCurrentContextData(null);
      setCurrentBusinessValues([]);
      setCurrentDreadedEvents([]);
      setCurrentSecurityFramework(null);
      setCurrentAnalysisControlPlan(null);
      setCurrentRiskTreatments([]);
      setAllControls([]);
      setCurrentStakeholders([]);
      setStakeholderThresholds({
        danger: 3,
        control: 1.5,
        watch: 0.5
      });
      setWorkshopDataError(null);
      return;
    }

    // Avoid reloading if already selected and not loading
    if (currentAnalysis && currentAnalysis.id === analysisId && !isLoading && !isWorkshopDataLoading) {
      console.log("Analysis already selected:", analysisId);
      return;
    }

    // Avoid concurrent loading
    if (isLoading || isWorkshopDataLoading) {
      console.log("Already loading an analysis, ignoring request");
      return;
    }

    // Set loading state immediately
    setIsLoading(true);
    try {
      console.log("Loading analysis:", analysisId);
      const result = await loadAnalysis(analysisId);
      if (result) {
        // Navigate to dashboard after successful load
        // navigate('/dashboard'); // Consider if navigation is always desired
      }
    } catch (error) {
      console.error("Error selecting analysis:", error);
    } finally {
      setIsLoading(false); // Ensure loading state is reset
    }
  };

  // Context value
  const contextValue = {
    // Analysis state and functions
    currentAnalysis,
    analyses,
    isLoading,
    error,
    loadAnalyses,
    selectAnalysis,
    createAnalysis,
    updateAnalysis,
    deleteAnalysis,

    // Workshop data state
    currentContextData,
    currentBusinessValues,
    currentDreadedEvents,
    currentSecurityFramework,
    currentAnalysisControlPlan,
    currentRiskTreatments,
    allControls, // ---> ADDED: Expose allControls <---

    // Workshop loading state
    isWorkshopDataLoading,
    workshopDataError,

    // Workshop save functions
    saveCurrentContextData,
    saveCurrentBusinessValues,
    saveCurrentDreadedEvents,
    saveCurrentSecurityFramework,
    saveCurrentAnalysisControlPlan,
    saveCurrentRiskTreatments,

    // Atelier 2 functions
    getThreatCategories,
    saveThreatCategories,
    getSourcesDeRisque,
    saveSourcesDeRisque,
    getObjectifsVises,
    saveObjectifsVises,
    getCouplesSROV,
    saveCouplesSROV,
    getSelectedCouplesSROV,
    saveSelectedCouplesSROV,

    // Atelier 3 functions
    currentStakeholders,
    stakeholderThresholds,
    getStakeholders,
    saveStakeholders,
    getStakeholderThresholds,
    updateStakeholderThresholds,
  };

  return (
    <AnalysisContext.Provider value={contextValue}>
      {children}
    </AnalysisContext.Provider>
  );
};

// Custom hook to use the Analysis Context
export const useAnalysis = () => {
  const context = useContext(AnalysisContext);
  if (!context) {
    throw new Error('useAnalysis must be used within an AnalysisProvider');
  }
  return context;
};