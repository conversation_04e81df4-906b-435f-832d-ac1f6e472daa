// test-enhanced-scenarios.js
// Test script to verify enhanced operational scenarios generation

const axios = require('axios');

// Test data with comprehensive business context and CTI information
const testData = {
  analysisId: "test-analysis-enterprise-123",
  attackPaths: [
    {
      id: "test-path-enterprise-1",
      referenceCode: "CA01",
      sourceRiskName: "Groupe APT ciblant le secteur financier",
      objectifVise: "Compromission des systèmes de trading haute fréquence",
      dreadedEventName: "Interruption des services de trading critiques",
      businessValueName: "Plateforme de trading haute fréquence",
      businessValueId: "bv-trading-001",
      dreadedEventId: "de-trading-001",
      description: "Chemin d'attaque sophistiqué ciblant l'infrastructure critique de trading via l'écosystème de confiance",
      difficulty: "Élevé",
      impact: "Critique",
      businessContinuityImpact: "Arrêt complet des opérations de trading pendant plusieurs heures",
      financialImpact: "Pertes estimées à 50M€ par heure d'arrêt",
      regulatoryImpact: "Violation MiFID II, risque d'amendes ACPR",
      reputationalImpact: "Perte de confiance des clients institutionnels",
      stakeholders: [
        {
          name: "Fournisseur de données de marché Bloomberg",
          type: "Fournisseur de données critiques",
          cyberMaturity: "Élevé",
          trust: "Confiance maximale - Accès direct aux systèmes de trading",
          securitySLA: "SLA sécurité niveau 1 - Monitoring 24/7",
          certifications: "ISO 27001, SOC 2 Type II",
          dataAccess: "Flux de données de marché en temps réel",
          securityControls: "Authentification multi-facteurs, chiffrement bout-en-bout"
        }
      ],
      vulnerabilities: [
        {
          cveId: "CVE-2023-44487",
          severity: "HIGH",
          score: 7.5,
          description: "Vulnérabilité HTTP/2 Rapid Reset dans les serveurs de données de marché permettant des attaques DDoS sophistiquées",
          publishedDate: "2023-10-10",
          configurations: ["nginx 1.20.x", "Apache HTTP Server 2.4.x", "Systèmes de flux de données Bloomberg"],
          weaknesses: [
            { id: "CWE-400", description: "Uncontrolled Resource Consumption" },
            { id: "CWE-770", description: "Allocation of Resources Without Limits" }
          ],
          references: ["https://www.cve.org/CVERecord?id=CVE-2023-44487"]
        }
      ],
      attackTechniques: [
        {
          id: "T1190",
          name: "Exploit Public-Facing Application",
          description: "Adversaries may attempt to take advantage of a weakness in an Internet-facing computer or program using software, data, or commands in order to cause unintended or unanticipated behavior.",
          platforms: ["Linux", "Windows", "Network"],
          tactics: ["Initial Access"],
          kill_chain_phases: ["exploitation"],
          x_mitre_detection: "Monitor network traffic for HTTP/2 rapid reset patterns, unusual connection rates to trading data feeds",
          x_mitre_mitigation: "Implement rate limiting, update HTTP/2 implementations, deploy DDoS protection"
        },
        {
          id: "T1498",
          name: "Network Denial of Service",
          description: "Adversaries may perform Network Denial of Service (DoS) attacks to degrade or block the availability of targeted resources to users.",
          platforms: ["Linux", "Windows", "Network"],
          tactics: ["Impact"],
          kill_chain_phases: ["actions-on-objectives"],
          x_mitre_detection: "Monitor for unusual network traffic patterns, connection exhaustion, bandwidth consumption",
          x_mitre_mitigation: "Implement network segmentation, traffic shaping, and DDoS mitigation services"
        },
        {
          id: "T1565.001",
          name: "Data Manipulation: Stored Data Manipulation",
          description: "Adversaries may insert, delete, or manipulate data in order to influence external outcomes or hide activity.",
          platforms: ["Linux", "Windows", "macOS"],
          tactics: ["Impact"],
          kill_chain_phases: ["actions-on-objectives"],
          x_mitre_detection: "Monitor for unexpected changes to trading data, price feed anomalies, transaction irregularities",
          x_mitre_mitigation: "Implement data integrity checks, transaction monitoring, and backup verification"
        }
      ]
    }
  ],
  existingScenarios: []
};

async function testEnhancedScenarios() {
  try {
    console.log('🧪 Testing Enhanced Operational Scenarios Generation...\n');

    const response = await axios.post('http://localhost:5000/api/ai/generate-operational-scenarios', testData, {
      headers: {
        'Content-Type': 'application/json'
      },
      timeout: 60000 // 60 seconds timeout
    });

    if (response.data.success) {
      console.log('✅ Scenarios generated successfully!');
      console.log(`📊 CTI Data Fidelity Score: ${response.data.data.validation.ctiDataFidelity}%`);
      console.log(`🔍 CVE Data Used: ${response.data.data.validation.ctiDataUsed.vulnerabilities}`);
      console.log(`🎯 MITRE Techniques Used: ${response.data.data.validation.ctiDataUsed.techniques}`);

      if (response.data.data.validation.warnings.length > 0) {
        console.log('\n⚠️ Validation Warnings:');
        response.data.data.validation.warnings.forEach(warning => {
          console.log(`  - ${warning}`);
        });
      } else {
        console.log('\n✅ No validation warnings - All CTI data properly used!');
      }

      console.log('\n📋 Generated Business-Focused Scenarios:');
      response.data.data.scenarios.forEach((scenario, index) => {
        console.log(`\n${index + 1}. ${scenario.name}`);
        console.log(`   Description: ${scenario.description.substring(0, 150)}...`);
        console.log(`   Severity: ${scenario.severity}, Likelihood: ${scenario.likelihood}`);
        console.log(`   Steps: ${scenario.steps.length} phases`);

        // Check if CVE is mentioned
        const cveMatches = scenario.description.match(/CVE-\d{4}-\d{4,}/g) || [];
        if (cveMatches.length > 0) {
          console.log(`   ✅ CVE References: ${cveMatches.join(', ')}`);
        }

        // Check if MITRE techniques are mentioned
        const mitreMatches = scenario.description.match(/T\d{4}(?:\.\d{3})?/g) || [];
        if (mitreMatches.length > 0) {
          console.log(`   ✅ MITRE Techniques: ${mitreMatches.join(', ')}`);
        }

        // Check for business context integration
        const businessTerms = ['trading', 'financier', 'Bloomberg', 'MiFID', 'ACPR', 'institutionnels'];
        const foundBusinessTerms = businessTerms.filter(term =>
          scenario.description.toLowerCase().includes(term.toLowerCase())
        );
        if (foundBusinessTerms.length > 0) {
          console.log(`   🏢 Business Context: ${foundBusinessTerms.join(', ')}`);
        }

        // Check for professional impact mentions
        const impactTerms = ['€', 'pertes', 'conformité', 'réglementaire', 'réputation', 'continuité'];
        const foundImpactTerms = impactTerms.filter(term =>
          scenario.description.toLowerCase().includes(term.toLowerCase())
        );
        if (foundImpactTerms.length > 0) {
          console.log(`   💼 Professional Impact: ${foundImpactTerms.join(', ')}`);
        }

        // Check steps for business integration
        if (scenario.steps && scenario.steps.length > 0) {
          const businessSteps = scenario.steps.filter(step =>
            step.description.toLowerCase().includes('trading') ||
            step.description.toLowerCase().includes('bloomberg') ||
            step.description.toLowerCase().includes('financier')
          );
          if (businessSteps.length > 0) {
            console.log(`   🎯 Business-Integrated Steps: ${businessSteps.length}/${scenario.steps.length}`);
          }
        }
      });

    } else {
      console.error('❌ Failed to generate scenarios:', response.data.message);
    }

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', error.response.data);
    }
  }
}

// Run the test
if (require.main === module) {
  testEnhancedScenarios();
}

module.exports = { testEnhancedScenarios };
