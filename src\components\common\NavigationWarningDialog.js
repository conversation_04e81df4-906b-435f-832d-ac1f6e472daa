// src/components/common/NavigationWarningDialog.js
import React from 'react';
import { AlertTriangle, Save, X } from 'lucide-react';

/**
 * Modal dialog to warn users about unsaved changes when navigating
 * 
 * @param {Object} props
 * @param {boolean} props.isOpen - Whether the dialog is open
 * @param {Function} props.onClose - Function to call when dialog is closed
 * @param {Function} props.onContinue - Function to call when user chooses to continue navigation
 * @param {Function} props.onSave - Function to call when user chooses to save changes
 */
const NavigationWarningDialog = ({ isOpen, onClose, onContinue, onSave }) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
      <div className="bg-white rounded-lg shadow-xl max-w-md w-full p-6 mx-4">
        <div className="flex items-start mb-4">
          <div className="flex-shrink-0 bg-amber-100 rounded-full p-2 mr-3">
            <AlertTriangle className="h-6 w-6 text-amber-600" />
          </div>
          <div className="flex-1">
            <h3 className="text-lg font-medium text-gray-900">Modifications non enregistrées</h3>
            <p className="mt-2 text-sm text-gray-500">
              Vous avez des modifications non enregistrées. Si vous quittez cette page, vos modifications seront perdues.
            </p>
          </div>
        </div>
        
        <div className="mt-6 flex flex-col sm:flex-row-reverse gap-2">
          <button
            type="button"
            className="w-full sm:w-auto px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center justify-center"
            onClick={onSave}
          >
            <Save size={16} className="mr-2" />
            Enregistrer et continuer
          </button>
          <button
            type="button"
            className="w-full sm:w-auto px-4 py-2 bg-amber-600 text-white rounded-md hover:bg-amber-700 flex items-center justify-center"
            onClick={onContinue}
          >
            Continuer sans enregistrer
          </button>
          <button
            type="button"
            className="w-full sm:w-auto px-4 py-2 border border-gray-300 bg-white text-gray-700 rounded-md hover:bg-gray-50 flex items-center justify-center"
            onClick={onClose}
          >
            <X size={16} className="mr-2" />
            Annuler
          </button>
        </div>
      </div>
    </div>
  );
};

export default NavigationWarningDialog;
