const SecurityControl = require('../models/SecurityControl');
const asyncHandler = require('../middleware/asyncHandler'); // Assuming you have an asyncHandler utility

/**
 * @desc    Get all security controls
 * @route   GET /api/security-controls
 * @access  Private (adjust middleware as needed)
 */
exports.getAllSecurityControls = asyncHandler(async (req, res, next) => {
  // Add any filtering or pagination logic here if needed in the future
  const controls = await SecurityControl.find({});

  res.status(200).json({
    success: true,
    count: controls.length,
    data: controls,
  });
});

/**
 * @desc    Add multiple security controls in bulk
 * @route   POST /api/security-controls/bulk
 * @access  Private/Admin (adjust middleware as needed)
 */
exports.addSecurityControlsBulk = asyncHandler(async (req, res, next) => {
  const { controls } = req.body; // Expecting an array { controls: [...] }

  if (!controls || !Array.isArray(controls) || controls.length === 0) {
    return res.status(400).json({ success: false, message: 'No controls data provided or data is not an array.' });
    // Or use your error handling middleware:
    // return next(new ErrorResponse('No controls data provided or data is not an array.', 400));
  }

  // Optional: Add validation for each control object within the array if needed

  try {
    const insertedControls = await SecurityControl.insertMany(controls, { ordered: false }); // ordered: false allows inserting valid ones even if some fail
    res.status(201).json({
      success: true,
      message: `${insertedControls.length} controls added successfully.`,
      data: insertedControls, // Optionally return the inserted documents
    });
  } catch (error) {
    console.error('Bulk insert error:', error);
    // Handle potential duplicate key errors or validation errors during insertMany
    if (error.code === 11000) {
       return res.status(400).json({ success: false, message: 'Duplicate control detected during bulk insert.' });
       // return next(new ErrorResponse('Duplicate control detected during bulk insert.', 400));
    }
    if (error.name === 'ValidationError') {
       return res.status(400).json({ success: false, message: `Validation Error: ${error.message}` });
       // return next(new ErrorResponse(`Validation Error: ${error.message}`, 400));
    }
    // General error
    res.status(500).json({ success: false, message: 'Server error during bulk insert.' });
    // next(error); // Pass to generic error handler
  }
});