// test-cti-delete.js
// Test script to verify CTI delete functionality

const axios = require('axios');

const BASE_URL = 'http://localhost:5000/api/cti';

async function testCTIDeleteFunctionality() {
  console.log('🧪 Testing CTI Delete Functionality...\n');

  // Test 1: Create some test CTI data first
  try {
    console.log('1. Creating test CTI data...');
    const testData = {
      results: {
        totalVulnerabilities: 5,
        totalAttackTechniques: 3,
        assets: [
          {
            id: 'test-asset-1',
            name: 'Test Server',
            vulnerabilities: [
              { id: 'CVE-2023-12345', severity: 'HIGH', description: 'Test vulnerability' }
            ],
            techniques: [
              { id: 'T1190', name: 'Exploit Public-Facing Application' }
            ]
          }
        ]
      },
      vulnActions: { 'CVE-2023-12345': 'to_fix' },
      techniqueActions: { 'T1190': 'accepted' },
      attackPath: { id: 'test-path-1', name: 'Test Attack Path' },
      selectedAssets: ['test-asset-1']
    };

    const saveResponse = await axios.post(`${BASE_URL}/save-results`, testData);
    console.log('✅ Test CTI data created:', saveResponse.data.message);
    
    const reportId = saveResponse.data.data.id;
    console.log('📋 Report ID:', reportId);

  } catch (error) {
    console.error('❌ Failed to create test data:', error.response?.data?.message || error.message);
    return;
  }

  // Test 2: List CTI results
  try {
    console.log('\n2. Listing CTI results...');
    const listResponse = await axios.get(`${BASE_URL}/list-results`);
    console.log('✅ CTI results listed:', listResponse.data.count, 'results found');
    
    if (listResponse.data.count > 0) {
      console.log('📋 First result:', {
        id: listResponse.data.data[0].id,
        totalVulnerabilities: listResponse.data.data[0].totalVulnerabilities,
        totalAssets: listResponse.data.data[0].totalAssets
      });
    }
  } catch (error) {
    console.error('❌ Failed to list CTI results:', error.response?.data?.message || error.message);
  }

  // Test 3: Load CTI results
  try {
    console.log('\n3. Loading CTI results...');
    const loadResponse = await axios.get(`${BASE_URL}/load-results`);
    console.log('✅ CTI results loaded:', {
      totalVulnerabilities: loadResponse.data.data.results.totalVulnerabilities,
      totalAssets: loadResponse.data.data.results.assets.length,
      reportId: loadResponse.data.data.reportId
    });
  } catch (error) {
    console.error('❌ Failed to load CTI results:', error.response?.data?.message || error.message);
  }

  // Test 4: Delete CTI results (general)
  try {
    console.log('\n4. Deleting general CTI results...');
    const deleteResponse = await axios.delete(`${BASE_URL}/delete-results`);
    console.log('✅ CTI results deleted:', deleteResponse.data.message);
    console.log('📊 Deleted count:', deleteResponse.data.data.deletedCount);
  } catch (error) {
    console.error('❌ Failed to delete CTI results:', error.response?.data?.message || error.message);
  }

  // Test 5: Verify deletion
  try {
    console.log('\n5. Verifying deletion...');
    const verifyResponse = await axios.get(`${BASE_URL}/load-results`);
    console.log('❌ CTI results still exist after deletion (unexpected)');
  } catch (error) {
    if (error.response?.status === 404) {
      console.log('✅ CTI results properly deleted (404 as expected)');
    } else {
      console.error('❌ Unexpected error during verification:', error.message);
    }
  }

  // Test 6: Create test data for specific analysis
  try {
    console.log('\n6. Creating test data for specific analysis...');
    const analysisTestData = {
      results: {
        totalVulnerabilities: 2,
        totalAttackTechniques: 1,
        assets: [
          {
            id: 'analysis-asset-1',
            name: 'Analysis Test Server',
            vulnerabilities: [
              { id: 'CVE-2023-67890', severity: 'MEDIUM', description: 'Analysis test vulnerability' }
            ]
          }
        ]
      },
      attackPath: { id: 'analysis-path-1', name: 'Analysis Test Path' },
      selectedAssets: ['analysis-asset-1']
    };

    // Save with analysis ID in the data (simulating frontend behavior)
    analysisTestData.analysisId = 'test-analysis-123';
    
    const analysisResponse = await axios.post(`${BASE_URL}/save-results`, analysisTestData);
    console.log('✅ Analysis-specific CTI data created:', analysisResponse.data.message);
  } catch (error) {
    console.error('❌ Failed to create analysis-specific data:', error.response?.data?.message || error.message);
  }

  // Test 7: Delete by analysis ID
  try {
    console.log('\n7. Deleting CTI results by analysis ID...');
    const deleteAnalysisResponse = await axios.delete(`${BASE_URL}/delete-results?analysisId=test-analysis-123`);
    console.log('✅ Analysis CTI results deleted:', deleteAnalysisResponse.data.message);
    console.log('📊 Deleted count:', deleteAnalysisResponse.data.data.deletedCount);
  } catch (error) {
    console.error('❌ Failed to delete analysis CTI results:', error.response?.data?.message || error.message);
  }

  // Test 8: Test delete non-existent data
  try {
    console.log('\n8. Testing delete of non-existent data...');
    const deleteNonExistentResponse = await axios.delete(`${BASE_URL}/delete-results?analysisId=non-existent-analysis`);
    
    if (deleteNonExistentResponse.data.data.deletedCount === 0) {
      console.log('✅ Non-existent data deletion handled properly (0 deleted)');
    } else {
      console.log('❌ Unexpected deletion count for non-existent data');
    }
  } catch (error) {
    if (error.response?.status === 404) {
      console.log('✅ Non-existent data deletion handled properly (404)');
    } else {
      console.error('❌ Unexpected error for non-existent data:', error.message);
    }
  }

  console.log('\n🏁 CTI Delete functionality testing completed!');
}

// Run the test
if (require.main === module) {
  testCTIDeleteFunctionality().catch(console.error);
}

module.exports = { testCTIDeleteFunctionality };
