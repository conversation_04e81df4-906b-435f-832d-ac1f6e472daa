﻿import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { WORKSHOPS, POSITION_OPTIONS } from './constants/index';
import WorkshopNavigation from './components/WorkshopNavigation';
import Dashboard from './components/Dashboard';
import './styles/navbar-animation.css'; // Import the navbar animation CSS
import './styles/modernDesignSystem.css'; // Import the modern design system
import Context from './components/Atelier 1/Cadre/Context';
import BusinessValues from './components/Atelier 1/VM/BusinessValues';
import DreadedEvents from './components/Atelier 1/ER/DreadedEvents';
import SecurityFramework from './components/Atelier 1/Socle/SecurityFramework';
import SecurityControls from './components/Atelier 1/Socle/SecurityControls';
import RiskTreatment from './components/Atelier 1/Socle/RiskTreatment';

import KeyboardShortcutHelp from './components/KeyboardShortcutHelp';
// Import Atelier 2 components
import Activite1 from './components/Atelier 2/Activite1/Activite1';
import Activite2 from './components/Atelier 2/Activite2/Activite2';
import Activite3 from './components/Atelier 2/Activite3/Activite3';
import Activite1Atelier3 from './components/Atelier3/Activite1/Activite1';
import Activite2Atelier3 from './components/Atelier3/Activite2/Activite2';
import Activite3Atelier3 from './components/Atelier3/Activite3/Activite3';
import Activite4Atelier3 from './components/Atelier3/Activite4/Activite4';
import Activite5Atelier3 from './components/Atelier3/Activite5/Activite5';
import Activite1Atelier4 from './components/Atelier4/Activite1/Activite1';
import ThreatIntelligence from './components/Atelier4/ThreatIntelligence';
import OperationalScenariosAI from './components/Atelier4/OperationalScenariosAI';
import Synthesis from './components/Atelier4/Synthesis';
import { RefreshCw, Menu, X } from 'lucide-react';
import { Toaster } from 'react-hot-toast';

// Import des utilitaires de navigation
import {
  getPreviousTab,
  getNextTab,
  isFirstTab,
  isLastTab,
  getActiveWorkshop
} from './utils/navigationUtils';

// Import du gestionnaire d'état avec API
import {
  handleSaveData,
  handleSaveDataWithAPI,
  loadDataFromAPI,
  loadSecurityFrameworkData,
  loadContextData,
  loadBusinessValuesData,
  loadDreadedEventsData,
  loadDashboardPreferences,
  convertContextToRACIFormat,
  getCurrentAnalysisId
} from './utils/appStateManagerAPI';

// Import analysis context
import { useAnalysis } from './context/AnalysisContext';

// Note: user et handleLogout seront injectés par AuthWrapper
const EbiosRMApp = ({ user, handleLogout }) => {
  const { i18n, t } = useTranslation(); // Add i18n hook for language switching
  const [activeTab, setActiveTab] = useState('dashboard'); // Défini sur 'dashboard' par défaut
  const [expandedWorkshops, setExpandedWorkshops] = useState({ 'workshop1': true, 'workshop2': true,'workshop3': true, 'workshop4': true });
  const [isLoading, setIsLoading] = useState(false);
  const [currentLocalAnalysisId, setLocalAnalysisId] = useState(null); // Pour suivre l'ID chargé
  const [dataLoaded, setDataLoaded] = useState(false); // Pour éviter les chargements multiples
  const [isSidebarOpen, setSidebarOpen] = useState(true); // State for sidebar visibility

  // Set sidebar state based on screen size
  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth < 768) { // 768px is the md breakpoint in Tailwind
        setSidebarOpen(false);
      } else {
        setSidebarOpen(true);
      }
    };

    // Set initial state
    handleResize();

    // Add event listener
    window.addEventListener('resize', handleResize);

    // Cleanup
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Listen for tab switching events from Dashboard component
  useEffect(() => {
    const handleTabSwitch = () => {
      const tabName = localStorage.getItem('ebiosrm_switch_to_tab');
      if (tabName) {
        setActiveTab(tabName);
        localStorage.removeItem('ebiosrm_switch_to_tab'); // Clean up
      }
    };

    // Add event listener
    window.addEventListener('ebiosrm_switch_tab', handleTabSwitch);

    // Cleanup
    return () => window.removeEventListener('ebiosrm_switch_tab', handleTabSwitch);
  }, []);

  // Get analysis context
  const {
    currentAnalysis,
    loadAnalysis
  } = useAnalysis();

  // State for Context component
  const [organizationName, setOrganizationName] = useState('');
  const [scope, setScope] = useState('');
  const [analysisDate, setAnalysisDate] = useState('');
  const [participants, setParticipants] = useState([]);
  const [matrix, setMatrix] = useState([]);

  // State for BusinessValues component
  const [businessValues, setBusinessValues] = useState([]);
  const [newBusinessValue, setNewBusinessValue] = useState({
    id: '',
    name: '',
    description: '',
    criticality: 'medium',
    supportAssets: []
  });

  // State for DreadedEvents component
  const [dreadedEvents, setDreadedEvents] = useState([]);
  const [newEvent, setNewEvent] = useState({
    id: '',
    name: '',
    description: '',
    impacts: [],
    businessValues: []
  });

  // State for SecurityFramework component
  const [frameworks, setFrameworks] = useState([]);
  const [selectedRules, setSelectedRules] = useState([]);
  const [ruleDreadedEvents, setRuleDreadedEvents] = useState({});

  // State for SecurityControls component
  const [currentSecurityControls, setCurrentSecurityControls] = useState([]);

  // State for RiskTreatment component
  const [currentRiskTreatments, setCurrentRiskTreatments] = useState([]);

  // State for Dashboard preferences
  const [dashboardPreferences, setDashboardPreferences] = useState({
    showBusinessValues: true,
    showDreadedEvents: true,
    showSecurityFramework: true,
    showRiskTreatment: true
  });

  // Load data when component mounts
  useEffect(() => {
    loadAllData();
  }, []);

  // Load data when analysis changes
  useEffect(() => {
    if (currentAnalysis && currentAnalysis.id !== currentLocalAnalysisId) {
      setLocalAnalysisId(currentAnalysis.id);
      loadAllData();
    }
  }, [loadAnalysis]);

  // Function to load all data
  const loadAllData = async () => {
    if (dataLoaded) return;

    setIsLoading(true);
    try {
      // Load data from API or local storage
      const analysisId = getCurrentAnalysisId();

      if (analysisId) {
        // Load from API
        const contextData = await loadContextData(analysisId);
        if (contextData) {
          setOrganizationName(contextData.organizationName || '');
          setScope(contextData.scope || '');
          setAnalysisDate(contextData.analysisDate || '');
          setParticipants(contextData.participants || []);
          setMatrix(contextData.matrix || []);
        }

        const businessValuesData = await loadBusinessValuesData(analysisId);
        if (businessValuesData) {
          setBusinessValues(businessValuesData.businessValues || []);
        }

        const dreadedEventsData = await loadDreadedEventsData(analysisId);
        // Removed sensitive data logging for security
        if (dreadedEventsData) {
          setDreadedEvents(dreadedEventsData.dreadedEvents || []);
          // Removed sensitive data logging for security
        }

        const securityFrameworkData = await loadSecurityFrameworkData(analysisId);
        // Removed sensitive data logging for security
        if (securityFrameworkData) {
          setFrameworks(securityFrameworkData.frameworks || []);
          // Removed sensitive data logging for security

          setSelectedRules(securityFrameworkData.selectedRules || {});
          // Removed sensitive data logging for security

          setRuleDreadedEvents(securityFrameworkData.ruleDreadedEvents || {});
          // Removed sensitive data logging for security

          setCurrentSecurityControls(securityFrameworkData.securityControls || []);
          setCurrentRiskTreatments(securityFrameworkData.riskTreatments || []);
        }

        const dashboardPrefs = await loadDashboardPreferences(analysisId);
        if (dashboardPrefs) {
          setDashboardPreferences(dashboardPrefs);
        }
      } else {
        // Load from local storage
        const data = await loadDataFromAPI();
        if (data) {
          setOrganizationName(data.organizationName || '');
          setScope(data.scope || '');
          setAnalysisDate(data.analysisDate || '');
          setParticipants(data.participants || []);
          setMatrix(data.matrix || []);
          setBusinessValues(data.businessValues || []);
          setDreadedEvents(data.dreadedEvents || []);
          setFrameworks(data.frameworks || []);
          setSelectedRules(data.selectedRules || []);
          setCurrentSecurityControls(data.currentSecurityControls || []);
          setCurrentRiskTreatments(data.currentRiskTreatments || []);
          setDashboardPreferences(data.dashboardPreferences || {
            showBusinessValues: true,
            showDreadedEvents: true,
            showSecurityFramework: true,
            showRiskTreatment: true
          });
        }
      }

      setDataLoaded(true);
    } catch (error) {
      // Log without revealing sensitive data
      console.error('Error loading data');
    } finally {
      setIsLoading(false);
    }
  };

  // Function to save data based on the active tab
  const saveData = async (tabToSave, customData = null) => {
    // Check if we have an active analysis
    const analysisId = getCurrentAnalysisId();

    // If using an analysis, use the API version
    if (analysisId) {
      const dataToSave = customData || {
        organizationName,
        scope,
        analysisDate,
        participants,
        matrix,
        WORKSHOPS,
        businessValues,
        dreadedEvents,
        frameworks,
        selectedRules,
        ruleDreadedEvents,
        dashboardPreferences,
        currentSecurityControls,
        currentRiskTreatments
      };

      return await handleSaveDataWithAPI(tabToSave, dataToSave);
    }

    // Otherwise use the local storage version
    if (customData) {
      return handleSaveData(tabToSave, customData);
    }

    // Otherwise, use the current states
    const dataToSave = {
      organizationName,
      scope,
      analysisDate,
      participants,
      matrix,
      WORKSHOPS,
      businessValues,
      dreadedEvents,
      frameworks,
      selectedRules,
      ruleDreadedEvents,
      dashboardPreferences,
      currentSecurityControls,
      currentRiskTreatments
    };

    return handleSaveData(tabToSave, dataToSave);
  };

  // Function to navigate to the previous tab
  const handlePrevious = () => {
    if (activeTab === 'dashboard') {
      // If on dashboard, do nothing as it's the first screen
      return;
    }

    if (!isFirstTab(activeTab)) {
      const currentWorkshop = getActiveWorkshop(activeTab);
      const { nextTab } = getPreviousTab(activeTab, currentWorkshop);
      setActiveTab(nextTab);
    }
  };

  // Function to navigate to the next tab
  const handleNext = () => {
    if (activeTab === 'dashboard') {
      // If on dashboard, go to the first tab of the normal flow
      setActiveTab('context');
      return;
    }

    if (!isLastTab(activeTab)) {
      const currentWorkshop = getActiveWorkshop(activeTab);
      const { nextTab } = getNextTab(activeTab, currentWorkshop);
      setActiveTab(nextTab);
    }
  };

  // Handle workshop tree structure
  const toggleWorkshop = (workshop) => {
    setExpandedWorkshops({
      ...expandedWorkshops,
      [workshop]: !expandedWorkshops[workshop]
    });
  };

  // Save dashboard preferences
  const saveDashboardPreferences = (preferences) => {
    setDashboardPreferences(preferences);
    saveData('dashboard', {
      dashboardPreferences: preferences
    });
  };

  // Handle keyboard shortcuts
  useEffect(() => {
    // Function to handle keyboard shortcuts
    const handleKeyDown = (event) => {
      // Check for Ctrl + Right Arrow (next tab)
      if (event.ctrlKey && event.key === 'ArrowRight') {
        event.preventDefault(); // Prevent default browser behavior
        if (!isLastTab(activeTab)) {
          handleNext();
        }
      }

      // Check for Ctrl + Left Arrow (previous tab)
      if (event.ctrlKey && event.key === 'ArrowLeft') {
        event.preventDefault(); // Prevent default browser behavior
        if (activeTab !== 'dashboard' && !isFirstTab(activeTab)) {
          handlePrevious();
        }
      }
    };

    // Add event listener
    window.addEventListener('keydown', handleKeyDown);

    // Clean up event listener on component unmount
    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, [activeTab]); // Re-establish the event listener when activeTab changes



  return (
    <div className="flex flex-col min-h-screen bg-gradient-to-br from-gray-50 via-blue-50 to-indigo-50">
      {/* Modern Toast Container */}
      <Toaster
        position="top-right"
        toastOptions={{
          duration: 4000,
          style: {
            background: 'rgba(255, 255, 255, 0.95)',
            color: '#374151',
            borderRadius: '12px',
            padding: '16px',
            backdropFilter: 'blur(10px)',
            border: '1px solid rgba(255, 255, 255, 0.2)',
            boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
          },
          success: {
            style: {
              background: 'linear-gradient(135deg, #10B981 0%, #059669 100%)',
              color: '#fff',
            },
            iconTheme: {
              primary: '#ffffff',
              secondary: '#10B981',
            },
          },
          error: {
            style: {
              background: 'linear-gradient(135deg, #EF4444 0%, #DC2626 100%)',
              color: '#fff',
            },
            iconTheme: {
              primary: '#ffffff',
              secondary: '#EF4444',
            },
          },
        }}
      />
      {/* Sidebar with Tree Structure - Positioned absolutely */}
      <WorkshopNavigation
        activeTab={activeTab}
        setActiveTab={setActiveTab}
        expandedWorkshops={expandedWorkshops}
        toggleWorkshop={toggleWorkshop}
        isSidebarOpen={isSidebarOpen}
      />

      {/* Overlay for mobile when sidebar is open */}
      {isSidebarOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-10 md:hidden"
          onClick={() => setSidebarOpen(false)}
          aria-hidden="true"
        ></div>
      )}

      {/* Sidebar Toggle Button - Mobile */}
      <button
        onClick={() => setSidebarOpen(!isSidebarOpen)}
        className="fixed left-0 top-20 z-30 bg-blue-600 text-white p-2 rounded-r-lg shadow-md hover:bg-blue-700 transition-all duration-300"
        aria-label={isSidebarOpen ? "Fermer le menu" : "Ouvrir le menu"}
      >
        {isSidebarOpen ? <X size={20} /> : <Menu size={20} />}
      </button>

      {/* Content that shifts with sidebar */}
      <div
        className={`flex-grow flex flex-col transition-all duration-300 ${isSidebarOpen ? 'ml-64' : 'ml-0'}`}
        style={{ maxWidth: isSidebarOpen ? 'calc(100% - 16rem)' : '100%' }}
      >
        {/* Main header with French flag-inspired gradient */}
        <header className="text-white p-3 shadow-lg flex items-center justify-between relative overflow-hidden french-flag-navbar"
               style={{
                 background: 'linear-gradient(90deg, rgba(0,85,164,0.95) 0%, rgba(255,255,255,0.95) 50%, rgba(237,41,57,0.95) 100%)',
               }}
        >
          {/* Subtle overlay effects for the French flag gradient */}
          <div className="absolute inset-0 opacity-20 french-flag-glow">
          </div>

          {/* Logo with rounded corners */}
          <div className="overflow-hidden rounded-lg shadow-md bg-white p-1 transition-transform duration-300 hover:scale-105">
            <img
              src="/ebios-rm-logo.png"
              alt="EBIOS Risk Manager Logo"
              className="h-9 object-contain"
            />
          </div>
          {/* Title - Centered and Enhanced with blurry edges and gradient transparency */}
          <h1 className="text-xl font-bold tracking-tight backdrop-blur-sm px-8 py-3 rounded-full shadow-lg text-center flex items-center mx-auto z-10 relative overflow-hidden"
              style={{
                filter: 'drop-shadow(0 0 10px rgba(0,0,0,0.3))',
                backdropFilter: 'blur(8px)',
                WebkitBackdropFilter: 'blur(8px)'
              }}
          >
            {/* Gradient background that fades at edges */}
            <div className="absolute inset-0 rounded-full title-gradient"
                 style={{
                   background: 'radial-gradient(ellipse at center, rgba(17, 24, 39, 0.7) 0%, rgba(17, 24, 39, 0.5) 70%, rgba(17, 24, 39, 0) 100%)',
                   zIndex: -1
                 }}>
            </div>
            <span className="mr-2 text-blue-300 opacity-80">✦</span>
            <span className="text-white text-opacity-90">EBIOS RM - Analyse des risques</span>
            <span className="ml-2 text-red-300 opacity-80">✦</span>
          </h1>
          {/* ANSSI Logo */}
          <div className="overflow-hidden rounded-lg shadow-md bg-white p-1 transition-transform duration-300 hover:scale-105">
            <img
              src="/anssi-logo.png"
              alt="ANSSI Logo"
              className="h-9 object-contain"
            />
          </div>
        </header>

        {/* Modern navigation bar with logout button and Heroicons */}
        {user && (
          <nav className="bg-gray-900 text-white px-6 py-2 flex justify-between items-center shadow-md transition-all duration-300 w-full relative overflow-hidden"
          >
            <div className="flex items-center space-x-5">
              {/* User icon with name (Heroicons - User) */}
              <div className="flex items-center bg-gray-800 px-3 py-1.5 rounded-full transition-colors duration-300 hover:bg-gray-700">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="w-5 h-5 mr-2 text-blue-400">
                  <path fillRule="evenodd" d="M7.5 6a4.5 4.5 0 119 0 4.5 4.5 0 01-9 0zM3.751 20.105a8.25 8.25 0 0116.498 0 .75.75 0 01-.437.695A18.683 18.683 0 0112 22.5c-2.786 0-5.433-.608-7.812-1.7a.75.75 0 01-.437-.695z" clipRule="evenodd" />
                </svg>
                <span className="text-sm font-medium">{user.name}</span>
              </div>

              {/* Modern separator */}
              <span className="border-l border-gray-700 h-5 mx-1"></span>

              {/* Role icon (Heroicons - ShieldCheck) */}
              <div className="flex items-center bg-gray-800 px-3 py-1.5 rounded-full transition-colors duration-300 hover:bg-gray-700">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="w-5 h-5 mr-2 text-green-400">
                  <path fillRule="evenodd" d="M12.516 2.17a.75.75 0 00-1.032 0 11.209 11.209 0 01-7.877 ********** 0 00-.722.515A12.74 12.74 0 002.25 9.75c0 5.942 4.064 10.933 9.563 12.348a.75.75 0 00.674 0c5.499-1.415 9.563-6.406 9.563-12.348 0-1.39-.223-2.73-.635-3.985a.75.75 0 00-.722-.516l-.143.001c-2.996 0-5.717-1.17-7.734-3.08zm3.094 8.016a.75.75 0 10-1.22-.872l-3.236 4.53L9.53 12.22a.75.75 0 00-1.06 1.06l2.25 2.25a.75.75 0 001.14-.094l3.75-5.25z" clipRule="evenodd" />
                </svg>
                <span className="text-sm">{user.role}</span>
              </div>

              {/* Show company name if available */}
              {user.companyName && (
                <>
                  <span className="border-l border-gray-700 h-5 mx-1"></span>
                  <div className="flex items-center bg-gray-800 px-3 py-1.5 rounded-full transition-colors duration-300 hover:bg-gray-700">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="w-5 h-5 mr-2 text-yellow-400">
                      <path fillRule="evenodd" d="M4.5 2.25a.75.75 0 000 1.5v16.5h-.75a.75.75 0 000 1.5h16.5a.75.75 0 000-1.5h-.75V3.75a.75.75 0 000-1.5h-15zM9 6a.75.75 0 000 1.5h1.5a.75.75 0 000-1.5H9zm-.75 3.75A.75.75 0 019 9h1.5a.75.75 0 010 1.5H9a.75.75 0 01-.75-.75zM9 12a.75.75 0 000 1.5h1.5a.75.75 0 000-1.5H9zm3.75-5.25A.75.75 0 0113.5 6H15a.75.75 0 010 1.5h-1.5a.75.75 0 01-.75-.75zM13.5 9a.75.75 0 000 1.5H15A.75.75 0 0015 9h-1.5zm-.75 3.75a.75.75 0 01.75-.75H15a.75.75 0 010 1.5h-1.5a.75.75 0 01-.75-.75z" clipRule="evenodd" />
                    </svg>
                    <span className="text-sm">{user.companyName}</span>
                  </div>
                </>
              )}

              {/* Show current analysis if available */}
              {currentAnalysis && (
                <>
                  <span className="border-l border-gray-700 h-5 mx-1"></span>
                  <div className="flex items-center bg-gray-800 px-3 py-1.5 rounded-full transition-colors duration-300 hover:bg-gray-700">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="w-5 h-5 mr-2 text-blue-400">
                      <path d="M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0016.5 9h-1.875a1.875 1.875 0 01-1.875-1.875V5.25A3.75 3.75 0 009 1.5H5.625z" />
                      <path d="M12.971 1.816A5.23 5.23 0 0114.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 013.434 1.279 9.768 9.768 0 00-6.963-6.963z" />
                    </svg>
                    <span className="text-sm">{currentAnalysis.name}</span>
                  </div>
                </>
              )}
            </div>

            <div className="flex items-center gap-4">
              {/* Language Toggle Button with Flags */}
              <div className="flex items-center bg-gray-800 rounded-full p-1 transition-all duration-300 shadow-sm hover:shadow">
                <button
                  onClick={() => i18n.changeLanguage('fr')}
                  className={`flex items-center px-3 py-1.5 text-sm rounded-full transition-all duration-300 ${
                    i18n.language === 'fr'
                      ? 'bg-blue-600 text-white shadow-sm'
                      : 'text-gray-300 hover:text-white hover:bg-gray-700'
                  }`}
                  title="Français"
                >
                  <span className="fi fi-fr mr-1" style={{fontSize: '1.25rem'}}></span>
                  <span className="font-medium">FR</span>
                </button>
                <button
                  onClick={() => i18n.changeLanguage('en')}
                  className={`flex items-center px-3 py-1.5 text-sm rounded-full transition-all duration-300 ${
                    i18n.language === 'en'
                      ? 'bg-blue-600 text-white shadow-sm'
                      : 'text-gray-300 hover:text-white hover:bg-gray-700'
                  }`}
                  title="English"
                >
                  <span className="fi fi-gb mr-1" style={{fontSize: '1.25rem'}}></span>
                  <span className="font-medium">EN</span>
                </button>
              </div>

              {/* Logout button with icon at the far right (Heroicons - ArrowRightOnRectangle) */}
              <button
                onClick={handleLogout}
                className="flex items-center px-4 py-2 text-sm bg-red-600 hover:bg-red-700 rounded-full transition-all duration-300 shadow-sm hover:shadow-md focus:ring-2 focus:ring-red-500 focus:ring-opacity-50 focus:outline-none"
              >
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="w-5 h-5 mr-2">
                  <path fillRule="evenodd" d="M7.5 3.75A1.5 1.5 0 006 5.25v13.5a1.5 1.5 0 001.5 1.5h6a1.5 1.5 0 001.5-1.5V15a.75.75 0 011.5 0v3.75a3 3 0 01-3 3h-6a3 3 0 01-3-3V5.25a3 3 0 013-3h6a3 3 0 013 3V9a.75.75 0 01-1.5 0V5.25a1.5 1.5 0 00-1.5-1.5h-6zm10.72 4.72a.75.75 0 011.06 0l3 3a.75.75 0 010 1.06l-3 3a.75.75 0 11-1.06-1.06l1.72-1.72H9a.75.75 0 010-1.5h10.94l-1.72-1.72a.75.75 0 010-1.06z" clipRule="evenodd" />
                </svg>
                {t('common.logout')}
              </button>
            </div>
          </nav>
        )}

        {/* Loading indicator */}
        {isLoading && (
          <div className="fixed inset-0 bg-black bg-opacity-30 flex items-center justify-center z-50">
            <div className="bg-white p-4 rounded-lg shadow-lg flex items-center">
              <RefreshCw size={24} className="text-blue-500 mr-3 animate-spin" />
              <span className="text-gray-800">{t('common.loading')}</span>
            </div>
          </div>
        )}

        {/* Modern Main Content */}
        <div className="flex-grow p-6">
          <div className="modern-card modern-fade-in">
            {/* Dashboard Tab */}
            {activeTab === 'dashboard' && (
              <Dashboard
                organizationName={organizationName}
                analysisDate={analysisDate}
                participants={participants}
                matrix={matrix}
                businessValues={businessValues}
                dreadedEvents={dreadedEvents}
                frameworks={frameworks}
                selectedRules={selectedRules}
                securityControls={currentSecurityControls}
                riskTreatments={currentRiskTreatments}
                preferences={dashboardPreferences}
                setPreferences={saveDashboardPreferences}
              />
            )}

            {/* Context Tab */}
            {activeTab === 'context' && (
              <Context
                organizationName={organizationName}
                setOrganizationName={setOrganizationName}
                scope={scope}
                setScope={setScope}
                analysisDate={analysisDate}
                setAnalysisDate={setAnalysisDate}
                workshops={WORKSHOPS}
                positionOptions={POSITION_OPTIONS}
                participants={participants}
                setParticipants={setParticipants}
                matrix={matrix}
                setMatrix={setMatrix}
                handleSaveData={() => saveData('context')}
              />
            )}

            {/* Business Values Tab */}
            {activeTab === 'business-values' && (
              <BusinessValues
                businessValues={businessValues}
                setBusinessValues={setBusinessValues}
                newBusinessValue={newBusinessValue}
                setNewBusinessValue={setNewBusinessValue}
                handleSaveData={() => saveData('business-values')}
              />
            )}

            {/* Events Tab */}
            {activeTab === 'events' && (
              <DreadedEvents
                businessValues={businessValues}
                dreadedEvents={dreadedEvents}
                setDreadedEvents={setDreadedEvents}
                newEvent={newEvent}
                setNewEvent={setNewEvent}
                handleSaveData={() => saveData('events')}
              />
            )}

            {/* Security Framework Tab */}
            {activeTab === 'security' && (
              <SecurityFramework
                frameworks={frameworks}
                setFrameworks={setFrameworks}
                selectedRules={selectedRules}
                setSelectedRules={setSelectedRules}
                handleSaveData={(data) => saveData('security', data)}
              />
            )}

            {/* Security Controls Tab */}
            {activeTab === 'security-controls' && (
              <SecurityControls
                securityControls={currentSecurityControls}
                setSecurityControls={setCurrentSecurityControls}
                handleSaveData={() => saveData('security-controls')}
              />
            )}

            {/* Risk Treatment Tab */}
            {activeTab === 'risk-treatment' && (
              <RiskTreatment
                riskTreatments={currentRiskTreatments}
                setRiskTreatments={setCurrentRiskTreatments}
                handleSaveData={() => saveData('risk-treatment')}
              />
            )}

            {/* Atelier 2 - Activité 1 */}
            {activeTab === 'atelier2-activite1' && (
              <Activite1 />
            )}

            {/* Atelier 2 - Activité 2 */}
            {activeTab === 'atelier2-activite2' && (
              <Activite2 />
            )}

            {/* Atelier 2 - Activité 3 */}
            {activeTab === 'atelier2-activite3' && (
              <Activite3 />
            )}
            {/* Atelier 3 - Activité 1 */}
{activeTab === 'atelier3-activite1' && (
  <Activite1Atelier3 />
)}
{/* Atelier 3 - Activité 2 */}
{activeTab === 'atelier3-activite2' && (
  <Activite2Atelier3 />
)}
{/* Atelier 3 - Activité 3 */}
{activeTab === 'atelier3-activite3' && (
  <Activite3Atelier3 />
)}
{/* Atelier 3 - Activité 4 */}
{activeTab === 'atelier3-activite4' && (
  <Activite4Atelier3 />
)}
{/* Atelier 3 - Activité 5 */}
{activeTab === 'atelier3-activite5' && (
  <Activite5Atelier3 />
)}
{/* Atelier 4 - Phase 1: Threat Intelligence */}
{activeTab === 'atelier4-threat-intelligence' && (
  <ThreatIntelligence />
)}

{/* Atelier 4 - Phase 2: CTI-based Operational Scenarios AI */}
{activeTab === 'atelier4-cti-scenarios' && (
  <OperationalScenariosAI />
)}

{/* Atelier 4 - Phase 3: Synthesis */}
{activeTab === 'atelier4-synthesis' && (
  <Synthesis />
)}

{/* Atelier 4 - Legacy: Activité 1 (for backward compatibility) */}
{activeTab === 'atelier4-activite1' && (
  <Activite1Atelier4 />
)}
          </div>

          {/* Modern Navigation buttons */}
          <div className="flex justify-between items-center mt-6">
            <button
              className={`modern-btn ${
                activeTab === 'dashboard' || isFirstTab(activeTab)
                  ? 'opacity-50 cursor-not-allowed bg-gray-200 text-gray-500'
                  : 'modern-btn-secondary'
              }`}
              onClick={handlePrevious}
              disabled={activeTab === 'dashboard' || isFirstTab(activeTab)}
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
              {t('common.previous')}
            </button>

            <div className="flex items-center space-x-2 text-sm text-gray-500">
              <span>Ctrl + ←/→ pour naviguer</span>
            </div>

            <button
              className={`modern-btn ${
                isLastTab(activeTab)
                  ? 'opacity-50 cursor-not-allowed bg-gray-200 text-gray-500'
                  : 'modern-btn-primary'
              }`}
              onClick={handleNext}
              disabled={isLastTab(activeTab)}
            >
              {activeTab === 'dashboard' ? t('common.startAnalysis') : t('common.next')}
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </button>
          </div>
        </div>

        {/* Modern Footer */}
        <footer className="modern-backdrop-blur border-t border-white/20 p-4 text-center text-gray-600 mt-auto">
          <div className="flex items-center justify-center space-x-2">
            <div className="w-6 h-6 bg-gradient-to-br from-blue-600 to-indigo-700 rounded-lg flex items-center justify-center">
              <span className="text-white font-bold text-xs">E</span>
            </div>
            <span className="font-medium">{t('footer.copyright')}</span>
          </div>
        </footer>
        <KeyboardShortcutHelp />
      </div>
    </div>
  );
};

export default EbiosRMApp;
