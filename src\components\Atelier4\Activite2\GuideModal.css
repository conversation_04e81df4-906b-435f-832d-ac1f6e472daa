/* src/components/Atelier4/Activite2/GuideModal.css */

.guide-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
}

.guide-modal {
  background: white;
  border-radius: 16px;
  width: 100%;
  max-width: 1000px;
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

.guide-header {
  padding: 25px;
  background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
  color: white;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.guide-header h2 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 12px;
}

.close-btn {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  color: white;
  font-size: 1.2rem;
  cursor: pointer;
  padding: 8px;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.close-btn:hover {
  background: rgba(255, 255, 255, 0.3);
}

.guide-content {
  padding: 30px;
  overflow-y: auto;
  flex: 1;
}

.guide-section {
  margin-bottom: 40px;
}

.guide-section:last-child {
  margin-bottom: 20px;
}

.guide-section h3 {
  color: #495057;
  font-size: 1.3rem;
  font-weight: 600;
  margin: 0 0 20px 0;
  display: flex;
  align-items: center;
  gap: 10px;
  padding-bottom: 10px;
  border-bottom: 2px solid #e9ecef;
}

.guide-section h3 i {
  color: #17a2b8;
  font-size: 1.2rem;
}

.guide-section p {
  color: #6c757d;
  line-height: 1.6;
  margin-bottom: 15px;
}

.guide-section strong {
  color: #495057;
  font-weight: 600;
}

/* Feature Grid */
.feature-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-top: 20px;
}

.feature-card {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 12px;
  padding: 20px;
  text-align: center;
  transition: all 0.2s ease;
}

.feature-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-color: #17a2b8;
}

.feature-icon {
  width: 50px;
  height: 50px;
  background: linear-gradient(135deg, #17a2b8, #138496);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 15px;
  color: white;
  font-size: 1.2rem;
}

.feature-card h4 {
  margin: 0 0 10px 0;
  color: #495057;
  font-size: 1.1rem;
  font-weight: 600;
}

.feature-card p {
  margin: 0;
  color: #6c757d;
  font-size: 0.9rem;
  line-height: 1.5;
}

/* Steps Container */
.steps-container {
  margin-top: 20px;
}

.step {
  display: flex;
  gap: 20px;
  margin-bottom: 30px;
  align-items: flex-start;
}

.step-number {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #17a2b8, #138496);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 1.1rem;
  flex-shrink: 0;
}

.step-content {
  flex: 1;
}

.step-content h4 {
  margin: 0 0 10px 0;
  color: #495057;
  font-size: 1.1rem;
  font-weight: 600;
}

.step-content p {
  margin: 0 0 10px 0;
  color: #6c757d;
  line-height: 1.5;
}

.step-content ul {
  margin: 0;
  padding-left: 20px;
  color: #6c757d;
}

.step-content li {
  margin-bottom: 5px;
  line-height: 1.4;
}

/* Tips Grid */
.tips-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  margin-top: 20px;
}

.tip-card {
  border-radius: 12px;
  padding: 20px;
  border: 1px solid;
}

.tip-card.success {
  background: #d4edda;
  border-color: #c3e6cb;
  color: #155724;
}

.tip-card.warning {
  background: #fff3cd;
  border-color: #ffeaa7;
  color: #856404;
}

.tip-card i {
  font-size: 1.2rem;
  margin-bottom: 10px;
}

.tip-card h4 {
  margin: 0 0 15px 0;
  font-size: 1.1rem;
  font-weight: 600;
}

.tip-card ul {
  margin: 0;
  padding-left: 20px;
}

.tip-card li {
  margin-bottom: 8px;
  line-height: 1.4;
}

/* Info Cards */
.info-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  margin-top: 20px;
}

.info-card {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 12px;
  padding: 20px;
}

.info-card h4 {
  margin: 0 0 15px 0;
  color: #495057;
  font-size: 1.1rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
}

.info-card h4::before {
  content: "📋";
  font-size: 1rem;
}

.info-card ul {
  margin: 0;
  padding-left: 20px;
  color: #6c757d;
}

.info-card li {
  margin-bottom: 8px;
  line-height: 1.4;
}

.info-card strong {
  color: #495057;
}

/* FAQ Container */
.faq-container {
  margin-top: 20px;
}

.faq-item {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 15px;
}

.faq-item h4 {
  margin: 0 0 10px 0;
  color: #495057;
  font-size: 1rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
}

.faq-item h4::before {
  content: "❓";
  font-size: 0.9rem;
}

.faq-item p {
  margin: 0;
  color: #6c757d;
  line-height: 1.5;
}

/* Guide Footer */
.guide-footer {
  padding: 20px 30px;
  background: #f8f9fa;
  border-top: 1px solid #dee2e6;
  display: flex;
  justify-content: center;
}

.btn {
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 1rem;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.2s ease;
  text-decoration: none;
}

.btn-primary {
  background: linear-gradient(135deg, #17a2b8, #138496);
  color: white;
}

.btn-primary:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(23, 162, 184, 0.3);
}

/* Responsive Design */
@media (max-width: 768px) {
  .guide-modal-overlay {
    padding: 10px;
  }

  .guide-modal {
    max-height: 95vh;
  }

  .guide-header {
    padding: 20px;
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }

  .guide-header h2 {
    font-size: 1.3rem;
  }

  .close-btn {
    align-self: flex-end;
  }

  .guide-content {
    padding: 20px;
  }

  .guide-section h3 {
    font-size: 1.2rem;
  }

  .feature-grid {
    grid-template-columns: 1fr;
  }

  .step {
    flex-direction: column;
    gap: 15px;
  }

  .step-number {
    align-self: flex-start;
  }

  .tips-grid {
    grid-template-columns: 1fr;
  }

  .info-cards {
    grid-template-columns: 1fr;
  }

  .guide-footer {
    padding: 15px 20px;
  }
}

@media (max-width: 480px) {
  .guide-header {
    padding: 15px;
  }

  .guide-header h2 {
    font-size: 1.1rem;
  }

  .guide-content {
    padding: 15px;
  }

  .guide-section {
    margin-bottom: 30px;
  }

  .feature-card,
  .info-card,
  .faq-item {
    padding: 15px;
  }

  .step-content h4 {
    font-size: 1rem;
  }

  .btn {
    padding: 10px 20px;
    font-size: 0.9rem;
  }
}