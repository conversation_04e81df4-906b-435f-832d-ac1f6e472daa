// backend/utils/seeder.js
const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
const dotenv = require('dotenv');
const User = require('../models/User');
const Company = require('../models/Company');
const connectDB = require('../config/database');

// Load environment variables
dotenv.config();

// Connect to database
connectDB();

// Sample data
const companies = [
  {
    name: 'Entreprise A',
    domain: 'entreprisea.com',
    status: 'active',
    createdAt: new Date('2025-01-15T12:00:00Z')
  },
  {
    name: 'Entreprise B',
    domain: 'entrepriseb.com',
    status: 'active',
    createdAt: new Date('2025-02-20T09:30:00Z')
  }
];

const users = [
  {
    name: 'Admin System',
    email: '<EMAIL>',
    password: 'admin123',
    role: 'superadmin',
    status: 'active',
    companyId: null,
    companyName: null,
    lastLogin: null
  },
  {
    name: 'Provider A',
    email: '<EMAIL>',
    password: 'provider123',
    role: 'admin',
    status: 'active',
    lastLogin: null,
    // CompanyId will be set after creating companies
  },
  {
    name: 'John Analyst',
    email: '<EMAIL>',
    password: 'analyst123',
    role: 'simpleuser',
    status: 'active',
    lastLogin: null,
    // CompanyId will be set after creating companies
  }
];

// utils/seeder.js - Modified version
const importData = async () => {
    try {
      // Clear existing data
      await User.deleteMany();
      await Company.deleteMany();
      
      console.log('Previous data deleted');
      
      // Create companies
      const createdCompanies = await Company.create(companies);
      console.log(`${createdCompanies.length} companies created`);
      
      // Update users with company IDs
      users[1].companyId = createdCompanies[0]._id;
      users[1].companyName = createdCompanies[0].name;
      users[2].companyId = createdCompanies[0]._id;
      users[2].companyName = createdCompanies[0].name;
      
      // Create users with plaintext passwords 
      // (User model pre-save middleware will hash them)
      const createdUsers = await User.create(users);
      console.log(`${createdUsers.length} users created`);
      
      console.log('Data imported successfully');
      process.exit();
    } catch (error) {
      console.error(`Error: ${error.message}`);
      process.exit(1);
    }
  };

// Delete all data from DB
const destroyData = async () => {
  try {
    await User.deleteMany();
    await Company.deleteMany();
    
    console.log('All data destroyed');
    process.exit();
  } catch (error) {
    console.error(`Error: ${error.message}`);
    process.exit(1);
  }
};

// Execute based on command line argument
if (process.argv[2] === '-d') {
  destroyData();
} else {
  importData();
}